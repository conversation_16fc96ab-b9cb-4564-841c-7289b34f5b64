const fs = require('node:fs');
const path = require('node:path');
const os = require('os');
const Oss = require('ali-oss');
const JSZip = require('jszip');
const { defaultLogger } = require('../utils/logger');
const wsServer = require('./wsServer');
const configStore = require('./configStore');

/**
 * 文件监控服务
 *
 * 此服务监控指定目录中的文件变化，特别是处理网络共享路径上的临时文件。
 * 由于网络路径上的文件可能被其他进程（如WPS Office）锁定，
 * 所有文件操作都实现了重试机制来处理EBUSY错误。
 */

// OSS配置信息
const ossInfo = {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe',
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
    bucket: 'sigma-temp',
};

// 全局OSS客户端实例
let ossClient;
try {
    ossClient = new Oss({
        region: ossInfo.region,
        accessKeyId: ossInfo.accessKeyId,
        accessKeySecret: ossInfo.accessKeySecret,
        bucket: ossInfo.bucket,
        timeout: '120s'
    });
    defaultLogger.info(`OSS客户端初始化成功，bucket: ${ossInfo.bucket}`);
} catch (initError) {
    defaultLogger.error('初始化OSS客户端失败，请检查OSS配置和网络', initError);
    process.exit(1);
}

const sleep = (t = 1000) => {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve();
        }, t);
    });
};

/**
 * 带重试的文件读取函数
 * @param {string} filePath - 文件路径
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 * @returns {Promise<Buffer>} 文件缓冲区
 */
const readFileWithRetry = async (filePath, maxRetries = 3, retryDelay = 500) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return fs.readFileSync(filePath);
        } catch (error) {
            if (error.code === 'EBUSY' && attempt < maxRetries) {
                defaultLogger.warn(`文件被占用，第${attempt}次重试失败，${retryDelay}ms后重试: ${filePath}`);
                await sleep(retryDelay);
                // 增加延迟时间，指数退避
                retryDelay *= 1.5;
                continue;
            }
            throw error;
        }
    }
};

/**
 * 带重试的文件复制函数
 * @param {string} srcPath - 源文件路径
 * @param {string} destPath - 目标文件路径
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 * @returns {Promise<void>}
 */
const copyFileWithRetry = async (srcPath, destPath, maxRetries = 3, retryDelay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            fs.copyFileSync(srcPath, destPath);
            return;
        } catch (error) {
            if (error.code === 'EBUSY' && attempt < maxRetries) {
                defaultLogger.warn(`文件复制失败，第${attempt}次重试失败，${retryDelay}ms后重试: ${srcPath} -> ${destPath}`);
                await sleep(retryDelay);
                // 增加延迟时间，指数退避
                retryDelay *= 1.5;
                continue;
            }
            throw error;
        }
    }
};

/**
 * 带重试的文件存在检查函数
 * @param {string} filePath - 文件路径
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试延迟（毫秒）
 * @returns {Promise<boolean>} 文件是否存在
 */
const existsWithRetry = async (filePath, maxRetries = 3, retryDelay = 500) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return fs.existsSync(filePath);
        } catch (error) {
            if (error.code === 'EBUSY' && attempt < maxRetries) {
                defaultLogger.warn(`文件存在检查失败，第${attempt}次重试失败，${retryDelay}ms后重试: ${filePath}`);
                await sleep(retryDelay);
                retryDelay *= 1.5;
                continue;
            }
            // existsSync通常不应该抛出异常，如果抛出了，可能是网络问题
            defaultLogger.warn(`文件存在检查出现异常: ${filePath}`, error.message);
            return false; // 保守处理，假设文件不存在
        }
    }
    return false;
};

/**
 * 检测docx文件是否加密（从Buffer检测）
 * @param {Buffer} fileBuffer - 文件缓冲区
 * @param {string} filePath - 文件路径（仅用于日志）
 * @returns {Promise<boolean>} 是否加密
 */
const isDocxEncryptedFromBuffer = async (fileBuffer, filePath = '') => {
    try {
        console.log('开始解析文件');
        // 尝试使用JSZip解析文件
        const zip = await JSZip.loadAsync(fileBuffer);

        // 检查是否存在加密相关的文件或标志
        // 如果文件被密码保护，JSZip.loadAsync会抛出异常
        // 或者检查特定的加密标志文件

        // 尝试读取核心文档文件，如果无法读取可能是加密的
        const documentXml = zip.file('word/document.xml');
        if (!documentXml) {
            // 缺少核心文档文件，可能是损坏或加密
            return true;
        }

        // 尝试读取文档内容
        const content = await documentXml.async('text');
        if (!content || content.length === 0) {
            return true;
        }

        return false;
    } catch (error) {
        // 如果JSZip无法解析文件，很可能是加密的
        defaultLogger.warn(`检测文件加密状态时出错，可能是加密文件: ${filePath}`, error.message);
        return true;
    }
};

/**
 * 检测docx文件是否加密
 * @param {string} filePath - 文件路径
 * @returns {Promise<{isEncrypted: boolean, skipCheck: boolean}>} 加密检测结果
 */
const isDocxEncrypted = async (filePath) => {
    try {
        // 使用带重试的文件读取
        const fileBuffer = await readFileWithRetry(filePath);
        const isEncrypted = await isDocxEncryptedFromBuffer(fileBuffer, filePath);
        return { isEncrypted, skipCheck: false };
    } catch (error) {
        // 如果是EBUSY错误，说明文件被锁定，可能正在被使用
        if (error.code === 'EBUSY' || error.message.includes('EBUSY')) {
            defaultLogger.warn(`文件被占用或锁定，无法检测加密状态，跳过加密检查继续上传: ${filePath}`);
            return { isEncrypted: false, skipCheck: true }; // 跳过加密检查，允许上传
        }

        // 如果JSZip无法解析文件，很可能是加密的
        defaultLogger.warn(`检测文件加密状态时出错，可能是加密文件: ${filePath}`, error.message);
        return { isEncrypted: true, skipCheck: false };
    }
};

/**
 * 从OSS下载文件并检测是否加密
 * @param {string} ossFileName - OSS上的文件名
 * @param {string} originalFileName - 原始文件名（用于日志）
 * @returns {Promise<boolean>} 是否加密
 */
const checkEncryptionFromOSS = async (ossFileName, originalFileName) => {
    let tempFilePath = null;
    try {
        defaultLogger.info(`从OSS下载文件进行加密检测: ${ossFileName}`);

        // 下载文件到临时位置
        const result = await ossClient.get(`temp_docx/${ossFileName}`);
        if (!result || !result.content) {
            defaultLogger.error(`从OSS下载文件失败: ${ossFileName}`);
            return true; // 下载失败，保守处理
        }

        // 检测加密状态
        const isEncrypted = await isDocxEncryptedFromBuffer(result.content, originalFileName);
        defaultLogger.info(`OSS文件加密检测完成: ${ossFileName}, 加密状态: ${isEncrypted}`);

        return isEncrypted;
    } catch (error) {
        defaultLogger.error(`从OSS检测文件加密状态时出错: ${ossFileName}`, error.message);
        return true; // 出错时保守处理
    }
};

const uploadFile = async (fileAbsolutePath, filenameToUpload) => {
    defaultLogger.info(`尝试上传: "${fileAbsolutePath}" 作为 "temp_docx/${filenameToUpload}"`);

    try {
        const fileExists = await existsWithRetry(fileAbsolutePath);
        if (!fileExists) {
            defaultLogger.error(`找不到要上传的文件: ${fileAbsolutePath}`);
            return { success: false, error: 'fileNotFound' };
        }

        // 检查文件是否加密
        const encryptionResult = await isDocxEncrypted(fileAbsolutePath);
        let shouldUpload = true;
        let needPostUploadCheck = false;

        if (encryptionResult.isEncrypted && !encryptionResult.skipCheck) {
            defaultLogger.warn(`检测到加密文件，拒绝上传: ${filenameToUpload}`);
            return { success: false, error: 'encryptedFile', fileName: filenameToUpload };
        }

        if (encryptionResult.skipCheck) {
            defaultLogger.info(`文件被占用，跳过预检查，先上传后检测: ${filenameToUpload}`);
            needPostUploadCheck = true; // 标记需要上传后检测
        }

        // 使用带重试的文件读取（用于上传）
        let fileBuffer;
        if (needPostUploadCheck) {
            // 如果需要上传后检测，可能是文件被占用，尝试多次读取
            try {
                fileBuffer = await readFileWithRetry(fileAbsolutePath, 5, 2000); // 更多重试次数和更长延迟
            } catch (error) {
                if (error.code === 'EBUSY') {
                    defaultLogger.error(`文件持续被占用，无法读取进行上传: ${filenameToUpload}`);
                    return { success: false, error: 'fileBusy', fileName: filenameToUpload };
                }
                throw error;
            }
        } else {
            fileBuffer = await readFileWithRetry(fileAbsolutePath);
        }
        defaultLogger.info(`文件读取完成(大小: ${fileBuffer.length} bytes)。开始上传到 "temp_docx/${filenameToUpload}"`);

        const result = await ossClient.put(`temp_docx/${filenameToUpload}`, fileBuffer);
        if (result && result.res && result.res.status >= 200 && result.res.status < 300) {
            defaultLogger.info(`上传成功: "${filenameToUpload}". 状态: ${result.res.status} ${result.res.statusMessage}`);

            // 如果需要上传后检测，现在进行检测
            if (needPostUploadCheck) {
                defaultLogger.info(`开始上传后加密检测: ${filenameToUpload}`);
                const isEncryptedFromOSS = await checkEncryptionFromOSS(filenameToUpload, fileAbsolutePath);

                if (isEncryptedFromOSS) {
                    defaultLogger.warn(`上传后检测发现加密文件，删除已上传文件: ${filenameToUpload}`);
                    try {
                        await ossClient.delete(`temp_docx/${filenameToUpload}`);
                        defaultLogger.info(`已删除加密文件: ${filenameToUpload}`);
                    } catch (deleteError) {
                        defaultLogger.error(`删除加密文件失败: ${filenameToUpload}`, deleteError.message);
                    }
                    return { success: false, error: 'encryptedFile', fileName: filenameToUpload };
                } else {
                    defaultLogger.info(`上传后检测确认文件未加密: ${filenameToUpload}`);
                }
            }

            return { success: true };
        } else {
            const status = result && result.res ? `${result.res.status} ${result.res.statusMessage}` : '未知状态';
            defaultLogger.error(`上传失败: "${filenameToUpload}". OSS响应: ${status}`, result);
            return { success: false, error: 'uploadFailed' };
        }
    } catch (e) {
        console.log(e);
        // 特殊处理EBUSY错误
        if (e.code === 'EBUSY') {
            defaultLogger.error(`文件被占用或锁定，无法上传: "${filenameToUpload}"`, e.message);
            return { success: false, error: 'fileBusy', fileName: filenameToUpload };
        }

        defaultLogger.error(`上传过程中出现异常: "${filenameToUpload}"`, e);
        return { success: false, error: 'uploadException' };
    }
};

class FileWatcher {
    constructor() {
        // 获取监控目录并确保它是字符串
        const configDir = configStore.getWatchDir();

        this.dirToWatch = typeof configDir === 'string' ? configDir : 'c:\\Temp';

        // 设置中转目录路径
        this.tempDir = this.getTempDir();

        this.uploadedFilesMap = {};
        this.isRunning = false;
        this.status = 'stopped';
        this.lastError = null;
        this.startTime = null;

        // 添加客户端任务关联映射
        this.fileClientMap = new Map(); // 存储文件与客户端的关联关系
    }

    // 获取中转目录路径
    getTempDir() {
        if (process.platform === 'win32') {
            // Windows 平台
            const appData = process.env.APPDATA || path.join(os.homedir(), 'AppData', 'Roaming');
            return path.join(appData, 'wps-addon-server', 'temp_docx');
        } else {
            // 其他平台
            return path.join(os.homedir(), '.wps-addon-server', 'temp_docx');
        }
    }

    // 确保中转目录存在
    ensureTempDirExists() {
        try {
            if (!fs.existsSync(this.tempDir)) {
                fs.mkdirSync(this.tempDir, { recursive: true });
                defaultLogger.info(`创建中转目录: ${this.tempDir}`);
                return true;
            }
            return true;
        } catch (error) {
            defaultLogger.error(`创建中转目录失败: ${error.message}`);
            return false;
        }
    }

    // 处理中转目录中的文件
    async processTempFiles() {
        try {
            // 确保中转目录存在
            if (!this.ensureTempDirExists()) {
                return; // 无法创建目录，跳过处理
            }

            const filesInTempDir = fs.readdirSync(this.tempDir);
            const docxFileRegex = /^[a-zA-Z0-9]{8}\.docx$/;
            const tempDocxFiles = filesInTempDir.filter(f => docxFileRegex.test(f));

            if (tempDocxFiles.length > 0) {
                defaultLogger.info(`中转目录发现${tempDocxFiles.length}个文件需要转移: ${tempDocxFiles.join(', ')}`);

                for (const file of tempDocxFiles) {
                    const tempFilePath = path.join(this.tempDir, file);
                    const targetFilePath = path.join(this.dirToWatch, file);

                    try {
                        // 确保目标目录存在
                        if (!fs.existsSync(this.dirToWatch)) {
                            fs.mkdirSync(this.dirToWatch, { recursive: true });
                            defaultLogger.info(`创建监控目录: ${this.dirToWatch}`);
                        }

                        // 复制文件到网络目录
                        await copyFileWithRetry(tempFilePath, targetFilePath);
                        defaultLogger.info(`文件已从中转目录复制到监控目录: ${file}`);
                        await sleep(2500);

                        // 复制成功后删除中转文件
                        try {
                            fs.unlinkSync(tempFilePath);
                            defaultLogger.info(`已删除中转文件: ${file}`);
                        } catch (deleteError) {
                            defaultLogger.warn(`删除中转文件失败，但复制已成功: ${file}, 错误: ${deleteError.message}`);
                            // 删除失败不影响主流程，只记录警告
                        }

                        // 检查文件是否有关联的客户端
                        const clientId = this.getClientForFile(file);
                        if (clientId) {
                            // 向特定客户端发送文件传输通知
                            this.sendWatcherEvent('fileTransferred', {
                                fileName: file,
                                fromPath: this.tempDir,
                                toPath: this.dirToWatch,
                                transferType: 'copy'
                            }, file);
                        }

                    } catch (transferError) {
                        defaultLogger.error(`传输文件 ${file} 失败: ${transferError.message}`);

                        // 发送错误事件
                        this.sendWatcherEvent('error', {
                            error: 'fileTransferError',
                            message: `传输文件失败: ${transferError.message}`,
                            fileName: file
                        });
                    }
                }
            }
        } catch (error) {
            defaultLogger.error(`处理中转目录时出错: ${error.message}`);
        }
    }

    /**
     * 将文件与客户端ID关联起来
     * @param {string} fileName - 文件名
     * @param {string} clientId - 客户端ID
     */
    associateFileWithClient(fileName, clientId) {
        if (!fileName || !clientId) return;

        defaultLogger.info(`关联文件 ${fileName} 与客户端 ${clientId}`);
        this.fileClientMap.set(fileName, clientId);
    }

    /**
     * 获取与文件关联的客户端ID
     * @param {string} fileName - 文件名
     * @returns {string|null} 客户端ID或null
     */
    getClientForFile(fileName) {
        return this.fileClientMap.get(fileName) || null;
    }

    /**
     * 发送事件给特定客户端或广播
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     * @param {string} [fileName] - 相关文件名，用于查找关联的客户端
     */
    sendWatcherEvent(eventType, data, fileName = null) {
        // 检查文件是否有关联的客户端
        const clientId = fileName ? this.getClientForFile(fileName) : null;
        if (clientId) {
            // 向特定客户端发送事件
            defaultLogger.info(`向客户端 ${clientId} 发送 ${eventType} 事件，文件: ${fileName || 'N/A'}`);
            wsServer.sendWatcherEventToClient(eventType, data, clientId);
        } else {
            // 无关联客户端或通用事件，使用广播
            defaultLogger.info(`广播 ${eventType} 事件，文件: ${fileName || 'N/A'}`);
            wsServer.sendWatcherEvent(eventType, data);
        }
    }

    // 获取监控目录
    getWatchDir() {
        return this.dirToWatch;
    }

    // 设置监控目录
    setWatchDir(dir) {
        if (this.isRunning) {
            defaultLogger.warn('无法在服务运行时更改监控目录');
            return false;
        }

        const success = configStore.setWatchDir(dir);
        if (success) {
            this.dirToWatch = dir;
            defaultLogger.info(`监控目录已更新为: ${dir}`);
            return true;
        }
        return false;
    }

    async start() {
        if (this.isRunning) {
            defaultLogger.warn('文件监控服务已经在运行中');
            return;
        }

        // 启动时确保中转目录存在
        this.ensureTempDirExists();

        this.isRunning = true;
        this.status = 'running';
        this.startTime = new Date();
        defaultLogger.info('文件监控服务启动');

        // 发送服务启动事件
        this.sendWatcherEvent('start', {
            startTime: this.startTime,
            watchDir: this.dirToWatch,
            tempDir: this.tempDir
        });

        this.run();
    }

    stop() {
        this.isRunning = false;
        this.status = 'stopped';
        defaultLogger.info('文件监控服务停止');

        // 发送服务停止事件
        this.sendWatcherEvent('stop', {
            stopTime: new Date(),
            processedFiles: Object.keys(this.uploadedFilesMap).length
        });
    }

    getStatus() {
        return {
            status: this.status,
            startTime: this.startTime,
            watchDir: this.dirToWatch,
            tempDir: this.tempDir,
            lastError: this.lastError,
            processedFiles: Object.keys(this.uploadedFilesMap).length
        };
    }

    async run() {
        defaultLogger.info('开始文件监控循环。');
        // 确保 dirToWatch 是字符串
        if (typeof this.dirToWatch !== 'string') {
            this.dirToWatch = 'c:\\Temp';
            defaultLogger.warn(`监控目录无效，使用默认目录: ${this.dirToWatch}`);
        }
        defaultLogger.info(`监控目标目录: "${this.dirToWatch}"`);

        while (this.isRunning) {
            try {
                // 首先处理中转目录中的文件
                await this.processTempFiles();

                if (!fs.existsSync(this.dirToWatch)) {
                    defaultLogger.warn(`监控目录"${this.dirToWatch}"不存在。尝试创建。`);
                    try {
                        fs.mkdirSync(this.dirToWatch, { recursive: true });
                        defaultLogger.info(`监控目录"${this.dirToWatch}"创建成功。`);

                        // 发送目录创建事件
                        this.sendWatcherEvent('dirCreated', {
                            dir: this.dirToWatch
                        });
                    } catch (mkdirError) {
                        defaultLogger.error(`创建监控目录"${this.dirToWatch}"失败。休眠后重试。`, mkdirError);
                        this.lastError = mkdirError;
                        // 发送错误事件
                        this.sendWatcherEvent('error', {
                            error: 'dirCreateError',
                            message: mkdirError.message
                        });
                        await sleep(15000);
                        continue;
                    }
                }
                let filesInDir;
                try {
                    filesInDir = fs.readdirSync(this.dirToWatch);
                } catch (readdirError) {
                    defaultLogger.error(`读取目录"${this.dirToWatch}"失败。权限问题？休眠后重试。`, readdirError);
                    this.lastError = readdirError;
                    // 发送错误事件
                    this.sendWatcherEvent('error', {
                        error: 'readDirError',
                        message: readdirError.message
                    });
                    await sleep(15000);
                    continue;
                }
                const docxFileRegex = /^[a-zA-Z0-9]{8}\.docx$/;
                const docxFiles = filesInDir.filter(f => docxFileRegex.test(f));
                const newFilesToUpload = docxFiles.filter(f => !this.uploadedFilesMap[f]);

                if (newFilesToUpload.length > 0) {
                    defaultLogger.info(`发现${newFilesToUpload.length}个新文件需要上传: ${newFilesToUpload.join(', ')}`);

                    for (const file of newFilesToUpload) {
                        // 检查文件是否有关联的客户端
                        const clientId = this.getClientForFile(file);
                        if (clientId) {
                            // 单独为每个文件发送通知给关联的客户端
                            defaultLogger.info(`向客户端 ${clientId} 发送文件发现通知: ${file}`);
                            this.sendWatcherEvent('filesFound', {
                                count: 1,
                                files: [file]
                            }, file);
                        } else {
                            // 文件没有关联的客户端，记录日志但不发送通知
                            defaultLogger.info(`文件 ${file} 没有关联的客户端，跳过发送通知`);
                        }
                        defaultLogger.info(`正在处理文件准备上传: ${file}`);
                        const filePath = path.join(this.dirToWatch, file);
                        // 发送开始上传事件 - 特定于文件，发送给关联客户端
                        this.sendWatcherEvent('uploadStart', {
                            file: file
                        }, file);

                        const uploadResult = await uploadFile(filePath, file);
                        if (uploadResult.success) {
                            try {
                                defaultLogger.info(`成功删除已上传的文件: "${file}"`);
                                this.uploadedFilesMap[file] = true;

                                // 发送上传成功事件 - 特定于文件，发送给关联客户端
                                this.sendWatcherEvent('uploadSuccess', {
                                    file: file,
                                    totalProcessed: Object.keys(this.uploadedFilesMap).length
                                }, file);
                                fs.unlinkSync(filePath);
                            } catch (deleteError) {
                                defaultLogger.warn(`无法删除已上传的文件: "${file}"`, deleteError);

                                // 发送删除失败事件 - 特定于文件，发送给关联客户端
                                this.sendWatcherEvent('deleteError', {
                                    file: file,
                                    error: deleteError.message
                                }, file);
                            }
                        } else {
                            // 根据不同的错误类型发送不同的事件
                            if (uploadResult.error === 'encryptedFile') {
                                defaultLogger.warn(`文件"${file}"已加密，拒绝上传。`);

                                // 发送加密文件错误事件 - 特定于文件，发送给关联客户端
                                this.sendWatcherEvent('encryptedFileError', {
                                    file: file,
                                    error: '文件已加密，无法处理',
                                    message: '检测到Word文档被加密。请使用未加密的文档，或更新"万唯AI编辑WPS插件服务"至最新版本。'
                                }, file);

                                // 删除加密文件，避免重复检测
                                try {
                                    fs.unlinkSync(filePath);
                                    defaultLogger.info(`已删除加密文件: "${file}"`);
                                } catch (deleteError) {
                                    defaultLogger.warn(`删除加密文件失败: "${file}"`, deleteError);
                                }
                            } else {
                                defaultLogger.warn(`上传"${file}"失败。将在未来循环中重试。`);

                                // 发送上传失败事件 - 特定于文件，发送给关联客户端
                                this.sendWatcherEvent('uploadError', {
                                    file: file,
                                    error: uploadResult.error || 'unknown'
                                }, file);
                            }
                        }
                    }
                    defaultLogger.info(`本次已完成处理${newFilesToUpload.length}个新文件。`);
                }
            } catch (loopError) {
                defaultLogger.error('主循环中的严重错误！', loopError);
                this.lastError = loopError;

                // 发送循环错误事件 - 通用错误，可以广播
                this.sendWatcherEvent('error', {
                    error: 'loopError',
                    message: loopError.message
                });

                await sleep(3000);
            }
            await sleep(3000);
        }
    }

    /**
     * 清理与断开连接的客户端相关的资源
     * @param {string} clientId - 断开连接的客户端ID
     */
    cleanupDisconnectedClient(clientId) {
        if (!clientId) return;

        defaultLogger.info(`清理客户端 ${clientId} 的文件关联`);

        // 找出所有与该客户端关联的文件
        const associatedFiles = [];
        this.fileClientMap.forEach((cid, fileName) => {
            if (cid === clientId) {
                associatedFiles.push(fileName);
            }
        });

        // 移除关联
        associatedFiles.forEach(fileName => {
            this.fileClientMap.delete(fileName);
            defaultLogger.info(`已解除客户端 ${clientId} 与文件 ${fileName} 的关联`);
        });

        defaultLogger.info(`已清理客户端 ${clientId} 的 ${associatedFiles.length} 个文件关联`);
    }
}

module.exports = new FileWatcher();
