const fs = require('node:fs');
const path = require('node:path');
const os = require('os');
const axios = require('axios');
const Oss = require('ali-oss');
const { defaultLogger } = require('../utils/logger');
const wsServer = require('./wsServer');
const configStore = require('./configStore');

/**
 * 图片分割文件监控服务
 * 
 * 专门监控图片分割功能产生的图片文件，并上传到OSS
 * 使用WebSocket与前端进行实时通信
 */

// OSS配置信息
const ossInfo = {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe',
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
    bucket: 'sigma-temp',
};

// 全局OSS客户端实例
let ossClient;
try {
    ossClient = new Oss({
        region: ossInfo.region,
        accessKeyId: ossInfo.accessKeyId,
        accessKeySecret: ossInfo.accessKeySecret,
        bucket: ossInfo.bucket,
        timeout: '120s'
    });
    defaultLogger.info(`图片分割OSS客户端初始化成功，bucket: ${ossInfo.bucket}`);
} catch (initError) {
    defaultLogger.error('初始化图片分割OSS客户端失败，请检查OSS配置和网络', initError);
}

const sleep = (t = 1000) => {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve();
        }, t);
    });
};

/**
 * 带重试的文件读取函数
 */
const readFileWithRetry = async (filePath, maxRetries = 3, retryDelay = 500) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return fs.readFileSync(filePath);
        } catch (error) {
            if (error.code === 'EBUSY' && attempt < maxRetries) {
                defaultLogger.warn(`图片文件被占用，第${attempt}次重试失败，${retryDelay}ms后重试: ${filePath}`);
                await sleep(retryDelay);
                retryDelay *= 1.5;
                continue;
            }
            throw error;
        }
    }
};

/**
 * 带重试的文件存在检查函数
 */
const existsWithRetry = async (filePath, maxRetries = 3, retryDelay = 500) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return fs.existsSync(filePath);
        } catch (error) {
            if (error.code === 'EBUSY' && attempt < maxRetries) {
                defaultLogger.warn(`图片文件存在检查失败，第${attempt}次重试失败，${retryDelay}ms后重试: ${filePath}`);
                await sleep(retryDelay);
                retryDelay *= 1.5;
                continue;
            }
            defaultLogger.warn(`图片文件存在检查出现异常: ${filePath}`, error.message);
            return false;
        }
    }
    return false;
};

/**
 * 上传图片文件到OSS
 */
const uploadImageFile = async (fileAbsolutePath, filenameToUpload, clientId) => {
    defaultLogger.info(`尝试上传图片: "${fileAbsolutePath}" 作为 "image_split/${filenameToUpload}"`);

    try {
        const fileExists = await existsWithRetry(fileAbsolutePath);
        if (!fileExists) {
            defaultLogger.error(`找不到要上传的图片文件: ${fileAbsolutePath}`);
            return { success: false, error: 'fileNotFound' };
        }

        // 读取图片文件
        const fileBuffer = await readFileWithRetry(fileAbsolutePath);
        defaultLogger.info(`图片文件读取完成(大小: ${fileBuffer.length} bytes)。开始上传到 "image_split/${filenameToUpload}"`);

        // 上传到OSS的image_split目录
        const result = await ossClient.put(`image_split/${filenameToUpload}`, fileBuffer);
        
        if (result && result.res && result.res.status >= 200 && result.res.status < 300) {
            defaultLogger.info(`图片上传成功: "${filenameToUpload}". 状态: ${result.res.status} ${result.res.statusMessage}`);
            
            // 生成OSS访问URL
            const ossUrl = `https://${ossInfo.bucket}.${ossInfo.region}.aliyuncs.com/image_split/${filenameToUpload}`;
            
            return { 
                success: true, 
                ossUrl: ossUrl,
                fileName: filenameToUpload,
                fileSize: fileBuffer.length
            };
        } else {
            const status = result && result.res ? `${result.res.status} ${result.res.statusMessage}` : '未知状态';
            defaultLogger.error(`图片上传失败: "${filenameToUpload}". OSS响应: ${status}`, result);
            return { success: false, error: 'uploadFailed' };
        }
    } catch (e) {
        console.log(e);
        if (e.code === 'EBUSY') {
            defaultLogger.error(`图片文件被占用或锁定，无法上传: "${filenameToUpload}"`, e.message);
            return { success: false, error: 'fileBusy', fileName: filenameToUpload };
        }

        defaultLogger.error(`图片上传过程中出现异常: "${filenameToUpload}"`, e);
        return { success: false, error: 'uploadException' };
    }
};

class ImageSplitWatcher {
    constructor() {
        // 获取监控目录
        const configDir = configStore.getWatchDir();
        this.baseDir = typeof configDir === 'string' ? configDir : 'c:\\Temp';
        
        // 图片分割专用目录
        this.dirToWatch = path.join(this.baseDir, 'ImageSplit');
        
        this.uploadedFilesMap = {};
        this.isRunning = false;
        this.status = 'stopped';
        this.lastError = null;
        this.startTime = null;

        // 客户端文件关联映射
        this.fileClientMap = new Map();
        
        // 支持的图片文件格式
        this.supportedFormats = ['.png', '.jpg', '.jpeg', '.gif', '.bmp'];
    }

    /**
     * 获取图片分割监控目录
     */
    getImageSplitDir() {
        return this.dirToWatch;
    }

    /**
     * 确保监控目录存在
     */
    ensureWatchDirExists() {
        try {
            if (!fs.existsSync(this.dirToWatch)) {
                fs.mkdirSync(this.dirToWatch, { recursive: true });
                defaultLogger.info(`创建图片分割监控目录: ${this.dirToWatch}`);
                return true;
            }
            return true;
        } catch (error) {
            defaultLogger.error(`创建图片分割监控目录失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 将文件与客户端ID关联
     */
    associateFileWithClient(fileName, clientId) {
        if (!fileName || !clientId) return;
        
        defaultLogger.info(`关联图片文件 ${fileName} 与客户端 ${clientId}`);
        this.fileClientMap.set(fileName, clientId);
    }

    /**
     * 获取与文件关联的客户端ID
     */
    getClientForFile(fileName) {
        return this.fileClientMap.get(fileName) || null;
    }

    /**
     * 发送事件给特定客户端或广播
     */
    sendImageSplitEvent(eventType, data, fileName = null) {
        const clientId = fileName ? this.getClientForFile(fileName) : null;

        // 添加详细的调试日志
        defaultLogger.info(`[DEBUG] sendImageSplitEvent: eventType=${eventType}, fileName=${fileName}, clientId=${clientId}`);
        defaultLogger.info(`[DEBUG] 当前文件客户端映射:`, Array.from(this.fileClientMap.entries()));

        if (clientId) {
            defaultLogger.info(`向客户端 ${clientId} 发送图片分割 ${eventType} 事件，文件: ${fileName || 'N/A'}`);
            wsServer.sendImageSplitEventToClient(eventType, data, clientId);
        } else {
            defaultLogger.info(`广播图片分割 ${eventType} 事件，文件: ${fileName || 'N/A'} (原因: 未找到关联的客户端)`);
            // 使用imageSplit类型广播事件
            wsServer.broadcast({
                type: 'imageSplit',
                eventType,
                data,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 检查文件是否为支持的图片格式
     */
    isSupportedImageFile(fileName) {
        const ext = path.extname(fileName).toLowerCase();
        return this.supportedFormats.includes(ext);
    }

    /**
     * 检查文件是否为图片分割相关文件
     */
    isImageSplitFile(fileName) {
        // 匹配图片分割文件命名模式
        const patterns = [
            /^image_split_\d+\.(png|jpg|jpeg|gif|bmp)$/i,           // 原图
            /^split_rect_\d+_\d+\.(png|jpg|jpeg|gif|bmp)$/i,       // 矩形分割
            /^split_h_(top|bottom)_\d+_\d+\.(png|jpg|jpeg|gif|bmp)$/i, // 横向分割
            /^split_v_(left|right)_\d+_\d+\.(png|jpg|jpeg|gif|bmp)$/i  // 竖向分割
        ];
        
        return patterns.some(pattern => pattern.test(fileName)) && this.isSupportedImageFile(fileName);
    }

    async start() {
        if (this.isRunning) {
            defaultLogger.warn('图片分割文件监控服务已经在运行中');
            return;
        }

        // 确保监控目录存在
        this.ensureWatchDirExists();

        this.isRunning = true;
        this.status = 'running';
        this.startTime = new Date();
        defaultLogger.info('图片分割文件监控服务启动');

        // 发送服务启动事件
        this.sendImageSplitEvent('imageSplitWatcherStart', {
            startTime: this.startTime,
            watchDir: this.dirToWatch
        });

        this.run();
    }

    stop() {
        this.isRunning = false;
        this.status = 'stopped';
        defaultLogger.info('图片分割文件监控服务停止');

        // 发送服务停止事件
        this.sendImageSplitEvent('imageSplitWatcherStop', {
            stopTime: new Date(),
            processedFiles: Object.keys(this.uploadedFilesMap).length
        });
    }

    getStatus() {
        return {
            status: this.status,
            startTime: this.startTime,
            watchDir: this.dirToWatch,
            lastError: this.lastError,
            processedFiles: Object.keys(this.uploadedFilesMap).length
        };
    }

    async run() {
        defaultLogger.info('开始图片分割文件监控循环');
        defaultLogger.info(`监控图片分割目录: "${this.dirToWatch}"`);

        while (this.isRunning) {
            try {
                if (!fs.existsSync(this.dirToWatch)) {
                    defaultLogger.warn(`图片分割监控目录"${this.dirToWatch}"不存在。尝试创建。`);
                    try {
                        fs.mkdirSync(this.dirToWatch, { recursive: true });
                        defaultLogger.info(`图片分割监控目录"${this.dirToWatch}"创建成功。`);

                        this.sendImageSplitEvent('imageSplitDirCreated', {
                            dir: this.dirToWatch
                        });
                    } catch (mkdirError) {
                        defaultLogger.error(`创建图片分割监控目录"${this.dirToWatch}"失败。休眠后重试。`, mkdirError);
                        this.lastError = mkdirError;

                        this.sendImageSplitEvent('imageSplitError', {
                            error: 'dirCreateError',
                            message: mkdirError.message
                        });
                        await sleep(15000);
                        continue;
                    }
                }

                let filesInDir;
                try {
                    filesInDir = fs.readdirSync(this.dirToWatch);
                } catch (readdirError) {
                    defaultLogger.error(`读取图片分割目录"${this.dirToWatch}"失败。权限问题？休眠后重试。`, readdirError);
                    this.lastError = readdirError;

                    this.sendImageSplitEvent('imageSplitError', {
                        error: 'readDirError',
                        message: readdirError.message
                    });
                    await sleep(15000);
                    continue;
                }

                // 筛选图片分割相关文件
                const imageSplitFiles = filesInDir.filter(f => this.isImageSplitFile(f));
                const newFilesToUpload = imageSplitFiles.filter(f => !this.uploadedFilesMap[f]);

                if (newFilesToUpload.length > 0) {
                    defaultLogger.info(`发现${newFilesToUpload.length}个新的图片分割文件需要上传: ${newFilesToUpload.join(', ')}`);

                    for (const file of newFilesToUpload) {
                        const clientId = this.getClientForFile(file);
                        if (clientId) {
                            defaultLogger.info(`向客户端 ${clientId} 发送图片文件发现通知: ${file}`);
                            this.sendImageSplitEvent('imageSplitFilesFound', {
                                count: 1,
                                files: [file]
                            }, file);
                        } else {
                            defaultLogger.info(`图片文件 ${file} 没有关联的客户端，跳过发送通知`);
                        }

                        defaultLogger.info(`正在处理图片文件准备上传: ${file}`);
                        const filePath = path.join(this.dirToWatch, file);

                        // 发送开始上传事件
                        this.sendImageSplitEvent('imageSplitUploadStart', {
                            file: file
                        }, file);

                        const uploadResult = await uploadImageFile(filePath, file, clientId);

                        if (uploadResult.success) {
                            try {
                                defaultLogger.info(`成功上传图片文件: "${file}"`);
                                this.uploadedFilesMap[file] = {
                                    uploaded: true,
                                    ossUrl: uploadResult.ossUrl,
                                    fileSize: uploadResult.fileSize,
                                    uploadTime: new Date()
                                };

                                // 发送上传成功事件
                                this.sendImageSplitEvent('imageSplitUploadSuccess', {
                                    file: file,
                                    ossUrl: uploadResult.ossUrl,
                                    fileSize: uploadResult.fileSize,
                                    totalProcessed: Object.keys(this.uploadedFilesMap).length
                                }, file);

                                // 删除本地文件
                                fs.unlinkSync(filePath);
                                defaultLogger.info(`已删除本地图片文件: "${file}"`);

                            } catch (deleteError) {
                                defaultLogger.warn(`无法删除已上传的图片文件: "${file}"`, deleteError);

                                this.sendImageSplitEvent('imageSplitDeleteError', {
                                    file: file,
                                    error: deleteError.message
                                }, file);
                            }
                        } else {
                            defaultLogger.warn(`上传图片文件"${file}"失败。将在未来循环中重试。`);

                            this.sendImageSplitEvent('imageSplitUploadError', {
                                file: file,
                                error: uploadResult.error || 'unknown'
                            }, file);
                        }
                    }
                    defaultLogger.info(`本次已完成处理${newFilesToUpload.length}个新的图片分割文件。`);
                }
            } catch (loopError) {
                defaultLogger.error('图片分割监控主循环中的严重错误！', loopError);
                this.lastError = loopError;

                this.sendImageSplitEvent('imageSplitError', {
                    error: 'loopError',
                    message: loopError.message
                });

                await sleep(3000);
            }
            await sleep(3000);
        }
    }

    /**
     * 清理与断开连接的客户端相关的资源
     */
    cleanupDisconnectedClient(clientId) {
        if (!clientId) return;

        defaultLogger.info(`清理客户端 ${clientId} 的图片文件关联`);

        const associatedFiles = [];
        this.fileClientMap.forEach((cid, fileName) => {
            if (cid === clientId) {
                associatedFiles.push(fileName);
            }
        });

        associatedFiles.forEach(fileName => {
            this.fileClientMap.delete(fileName);
            defaultLogger.info(`已解除客户端 ${clientId} 与图片文件 ${fileName} 的关联`);
        });

        defaultLogger.info(`已清理客户端 ${clientId} 的 ${associatedFiles.length} 个图片文件关联`);
    }

    /**
     * 手动触发文件上传（用于WebSocket消息处理）
     */
    async triggerFileUpload(fileName, clientId) {
        if (!fileName) {
            return { success: false, error: 'fileName is required' };
        }

        const filePath = path.join(this.dirToWatch, fileName);

        // 检查文件是否存在
        const fileExists = await existsWithRetry(filePath);
        if (!fileExists) {
            return { success: false, error: 'fileNotFound' };
        }

        // 检查是否为支持的图片分割文件
        if (!this.isImageSplitFile(fileName)) {
            return { success: false, error: 'unsupportedFileType' };
        }

        // 关联文件与客户端
        if (clientId) {
            this.associateFileWithClient(fileName, clientId);
        }

        // 执行上传
        const uploadResult = await uploadImageFile(filePath, fileName, clientId);

        if (uploadResult.success) {
            this.uploadedFilesMap[fileName] = {
                uploaded: true,
                ossUrl: uploadResult.ossUrl,
                fileSize: uploadResult.fileSize,
                uploadTime: new Date()
            };

            // 删除本地文件
            try {
                fs.unlinkSync(filePath);
            } catch (deleteError) {
                defaultLogger.warn(`删除本地图片文件失败: ${fileName}`, deleteError);
            }
        }

        return uploadResult;
    }

    /**
     * 下载OSS分割图片到缓存目录
     */
    async downloadSplitImage(ossUrl, fileName) {
        try {
            if (!ossUrl || !fileName) {
                return { success: false, error: 'ossUrl and fileName are required' };
            }

            // 确保缓存目录存在
            const cacheDir = path.join(this.dirToWatch, 'cache');
            if (!fs.existsSync(cacheDir)) {
                fs.mkdirSync(cacheDir, { recursive: true });
                defaultLogger.info(`创建分割图片缓存目录: ${cacheDir}`);
            }

            const localPath = path.join(cacheDir, fileName);

            defaultLogger.info(`开始下载分割图片: ${ossUrl} -> ${localPath}`);

            // 使用axios下载图片
            const response = await axios.get(ossUrl, {
                responseType: 'arraybuffer',
                timeout: 30000 // 30秒超时
            });

            const buffer = Buffer.from(response.data);

            // 保存到本地文件
            fs.writeFileSync(localPath, buffer);

            defaultLogger.info(`分割图片下载成功: ${localPath} (${buffer.length} bytes)`);

            return {
                success: true,
                localPath: localPath,
                fileSize: buffer.length
            };

        } catch (error) {
            defaultLogger.error(`下载分割图片失败: ${ossUrl}`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = new ImageSplitWatcher();
