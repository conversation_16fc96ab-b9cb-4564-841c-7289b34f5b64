const axios = require('axios');
const { defaultLogger } = require('../utils/logger');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const configStore = require('./configStore');

// Target URL for the UC API
const UC_API_URL = 'http://uc.hexinedu.com/api';

// 加密密钥 (应该放在环境变量或安全配置中)
const ENCRYPTION_KEY = 'hexinedu-secure-key-for-auth-data-2025';
const ENCRYPTION_IV = Buffer.from('1234567890123456'); // 16 bytes IV

// 创建固定长度的加密密钥 (32字节/256位)
const getDerivedKey = () => {
  return crypto.createHash('sha256').update(ENCRYPTION_KEY).digest();
};

// 加密数据
const encryptData = (data) => {
  try {
    const key = getDerivedKey();
    const cipher = crypto.createCipheriv('aes-256-cbc', key, ENCRYPTION_IV);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  } catch (error) {
    defaultLogger.error('加密数据失败:', error);
    return null;
  }
};

// 解密数据
const decryptData = (encryptedData) => {
  try {
    const key = getDerivedKey();
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, ENCRYPTION_IV);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
  } catch (error) {
    defaultLogger.error('解密数据失败:', error);
    return null;
  }
};

// 获取认证存储路径
const getAuthStorePath = () => {
  // 从配置中获取根目录
  const configPath = configStore.getAddonConfigPath() || path.join(__dirname, '../data');
  return path.join(configPath, '.auth');
};

// 确保存储目录存在
const ensureAuthStoreDir = () => {
  const authStorePath = getAuthStorePath();
  const dir = path.dirname(authStorePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  return authStorePath;
};

// 初始化认证存储
const initAuthStore = () => {
  try {
    const authStorePath = ensureAuthStoreDir();
    if (!fs.existsSync(authStorePath)) {
      const initialData = {
        cookies: {},
        timestamp: new Date().toISOString()
      };
      const encryptedData = encryptData(initialData);

      // 确保加密数据有效
      if (encryptedData) {
        fs.writeFileSync(authStorePath, encryptedData);
        defaultLogger.info('初始化认证存储: ' + authStorePath);
      } else {
        defaultLogger.error('初始化认证存储失败: 加密失败');
      }
    }
    return authStorePath;
  } catch (error) {
    defaultLogger.error('初始化认证存储失败:', error);
    return null;
  }
};

// 保存认证数据
const saveAuthData = (authData) => {
  try {
    const authStorePath = ensureAuthStoreDir();
    const encryptedData = encryptData(authData);
    if (!encryptedData) {
      return false;
    }
    fs.writeFileSync(authStorePath, encryptedData);
    return true;
  } catch (error) {
    defaultLogger.error('保存认证数据失败:', error);
    return false;
  }
};

// 获取认证数据
const getAuthData = () => {
  try {
    const authStorePath = getAuthStorePath();
    if (!fs.existsSync(authStorePath)) {
      return null;
    }

    const encryptedData = fs.readFileSync(authStorePath, 'utf8');
    return decryptData(encryptedData);
  } catch (error) {
    defaultLogger.error('获取认证数据失败:', error);
    return null;
  }
};

/**
 * 从Set-Cookie头提取cookie信息，同时忽略domain限制
 * @param {Array} setCookieHeaders - Set-Cookie头数组
 * @returns {Object} 提取的cookies
 */
const extractCookiesFromHeaders = (setCookieHeaders) => {
  let cookies = {};

  if (setCookieHeaders) {
    setCookieHeaders.forEach(cookie => {
      // 分离cookie主体和其属性
      const parts = cookie.split(';');
      const cookiePart = parts[0];
      const [name, value] = cookiePart.split('=');

      if (name && value) {
        cookies[name] = value;
      }
    });
  }

  return cookies;
};

/**
 * 代理登录请求到UC API
 * @param {Object} credentials - 登录凭证 (username, password)
 * @returns {Promise<Object>} 登录响应结果
 */
async function proxyLogin(credentials) {
  try {
    defaultLogger.info(`尝试登录: 用户名=${credentials.username}`);

    // 发起登录请求
    const response = await axios.post(`${UC_API_URL}/login`, credentials);
    // 提取响应中的cookies，忽略domain限制
    const hasCookies = Array.isArray(response.headers['set-cookie']) && response.headers['set-cookie'].length > 0;
    defaultLogger.debug(`响应包含cookies: ${hasCookies ? '是' : '否'}, 数量=${hasCookies ? response.headers['set-cookie'].length : 0}`);

    const cookies = extractCookiesFromHeaders(response.headers['set-cookie']);
    const cookiesCount = Object.keys(cookies).length;

    if (response?.data?.status === 0) {
      // 保存认证数据
      defaultLogger.info('登录成功, 保存认证数据');
      saveAuthData({
        cookies: cookies,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: response?.data?.data || '',
        cookies: cookies,
        message: response?.data?.message || ''
      };
    }

    defaultLogger.warn(`登录失败: ${response?.data?.statusInfo || '未知错误'}, 状态码=${response?.data?.status}`);
    return {
      success: false,
      message: response?.data?.statusInfo || '登录失败'
    };
  } catch (error) {
    if (error.response) {
      defaultLogger.error(`登录代理错误: HTTP状态=${error.response.status}`, {
        data: error.response.data,
        headers: error.response.headers
      });
    } else if (error.request) {
      defaultLogger.error('登录代理错误: 无响应', {
        request: {
          method: error.request.method,
          path: error.request.path,
          host: error.request.host
        }
      });
    } else {
      defaultLogger.error(`登录代理错误: ${error.message}`, error);
    }

    return {
      success: false,
      message: error.response?.data?.message || '登录失败，请稍后重试',
      error: error.message
    };
  }
}

/**
 * 代理登出请求到UC API
 * @returns {Promise<Object>} 登出响应结果
 */
async function proxyLogout() {
  try {
    // 获取认证数据
    const authData = getAuthData();
    let config = {};

    // 如果有认证数据，设置请求cookie
    if (authData && authData.cookies) {
      config.headers = {
        Cookie: Object.entries(authData.cookies)
          .map(([name, value]) => `${name}=${value}`)
          .join('; ')
      };
    }

    // 发起登出请求
    const response = await axios.post(`${UC_API_URL}/quitLogin`, {}, config);

    // 清除认证数据
    saveAuthData({
      cookies: {},
      timestamp: new Date().toISOString()
    });

    return {
      success: true,
      message: '退出登录成功'
    };
  } catch (error) {
    defaultLogger.error('登出代理错误:', error);
    // 尝试清除认证数据，即使API请求失败
    saveAuthData({
      cookies: {},
      timestamp: new Date().toISOString()
    });

    return {
      success: false,
      message: error.response?.data?.message || '退出登录失败，请稍后重试',
      error: error.message
    };
  }
}

/**
 * 代理获取用户信息请求
 * @returns {Promise<Object>} 用户信息响应结果
 */
async function proxyGetOwn() {
  try {
    // 获取认证数据
    const authData = getAuthData();

    // 如果没有认证数据，返回未登录
    if (!authData || !authData.cookies || Object.keys(authData.cookies).length === 0) {
      defaultLogger.info('无有效认证数据，用户未登录');
      return {
        success: false,
        message: '未登录或会话已过期'
      };
    }

    // 设置请求配置，包含cookie
    const config = {
      headers: {
        Cookie: Object.entries(authData.cookies)
          .map(([name, value]) => `${name}=${value}`)
          .join('; ')
      },
      // 添加请求超时设置，防止无限等待
      timeout: 5000
    };
    // 发起获取用户信息请求
    const response = await axios.get(`${UC_API_URL}/getOwn`, config); // 如果请求成功，更新认证数据
    const userInfo = response.data?.data;
    if (userInfo && userInfo.userId) {
      // 提取新的cookies
      let cookies = { ...authData.cookies };

      // 更新cookies，如果有新的set-cookie头
      if (response.headers['set-cookie']) {
        const newCookies = extractCookiesFromHeaders(response.headers['set-cookie']);
        cookies = { ...cookies, ...newCookies };
      }

      try {
        // 获取 appkey
        const orgId = userInfo.orgs?.[0]?.orgId;
        const ucRes = await axios.post(`${UC_API_URL}/orgAppl/getAllByOrg`,
          { orgIds: [orgId] },
          {
            params: {
              appKey: '5c72836fa066b75bb3d0bd63',
              appSecret: '45230d9315079c776b7b06f6674d37f24f0121fa'
            }
          },
          config
        );
        let appKey = ''
        let appSecret = ''
        if (ucRes.data.status === 0 && ucRes.data?.data?.length > 0) {
          const appInfo = ucRes.data.data[0];
          appKey = appInfo.appKey;
          appSecret = appInfo.appSecret;
          response.data.data.appKey = appKey;
          response.data.data.appSecret = appSecret;
          if (orgId === 2) {
            response.data.data.appKey = 'fc7539b21810cd4f0f0fb620';
            response.data.data.appSecret = '0ba32f787780b05ea971d511069d911c61d5b493';
          }
        }
      } catch (error) {
        console.log(error)
      }

      try {
        // 获取用户学科信息和权限信息
        const userOrgRes = await axios.get(`${UC_API_URL}/businessOrg/getUserAndOrgList?cache=0`, config);
        if (userOrgRes.data.status === 0 && userOrgRes.data?.data?.length > 0) {
          const userOrgInfo = userOrgRes.data.data[0];
          // 将学科信息和权限信息添加到响应数据中
          response.data.data.subject = userOrgInfo.subject;
          response.data.data.isOwner = userOrgInfo.isOwner;
          response.data.data.isAdmin = userOrgInfo.isAdmin;
          defaultLogger.info('成功获取用户学科和权限信息:', {
            userId: response.data.data.userId,
            subject: userOrgInfo.subject,
            isOwner: userOrgInfo.isOwner,
            isAdmin: userOrgInfo.isAdmin
          });
        }
      } catch (error) {
        defaultLogger.warn('获取用户学科和权限信息失败:', error.message);
        // 即使获取学科信息失败，也不影响主流程，只记录警告日志
      }
      // 检查企业是否开启了 AI 编辑功能
      const enableAiEdit = response.data.data?.orgs?.[0]?.enableAiEdit;
      if (enableAiEdit !== 1) {
        defaultLogger.warn('企业未开启AI编辑功能:', { userId: response.data.data.userId, enableAiEdit });
        return {
          success: false,
          message: '该企业尚未开启 AI 编辑功能，请联系您的企业管理员。',
          code: 'AI_EDIT_DISABLED',
          data: { ...response.data.data, cookies }
        };
      }

      // 更新认证数据
      saveAuthData({
        cookies: cookies,
        timestamp: new Date().toISOString()
      });

      defaultLogger.info('成功获取用户信息:', { userId: response.data.data.userId });
      return {
        success: true,
        data: { ...response.data.data, cookies }
      };
    }

    defaultLogger.warn('getOwn响应中没有用户ID:', response.data);
    return {
      success: false,
      message: '获取用户信息失败'
    };
  } catch (error) {
    console.trace(error)
    defaultLogger.error('获取用户信息错误:', JSON.stringify(error));
    // 强制显示错误详情
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      defaultLogger.error(`无法连接到UC API服务器 (${UC_API_URL}): 连接被拒绝或服务器不可用`);
      return {
        success: false,
        message: '无法连接到认证服务器，请检查网络连接',
        error: `连接错误: ${error.code}`
      };
    } else if (error.code === 'ETIMEDOUT' || error.code === 'TIMEOUT') {
      defaultLogger.error(`连接UC API服务器超时 (${UC_API_URL})`);
      return {
        success: false,
        message: '连接认证服务器超时，请稍后重试',
        error: `超时错误: ${error.code}`
      };
    }

    return {
      success: false,
      message: error.response?.data?.message || '获取用户信息失败',
      error: error.message
    };
  }
}

/**
 * 刷新会话，重新获取用户信息
 * @returns {Promise<Object>} 刷新结果
 */
async function refreshSession() {
  return await proxyGetOwn();
}

// 初始化认证存储
initAuthStore();

module.exports = {
  proxyLogin,
  proxyLogout,
  proxyGetOwn,
  refreshSession,
  getAuthStorePath,
  getAuthData
};
