import axios from 'axios';

// Constants
const AUTH_FILE_NAME = 'auth_data.json';

// API Constants
function getServerUrl() {
  if (import.meta.env.MODE === 'development') {
    return 'http://127.0.0.1:3000';
  } else {
    return 'http://127.0.0.1:3000'; // 生产环境可能需要调整此URL
  }
}

class StorageService {
  constructor() {
    this.storageFolder = 'HexinWpsAddon'; // Default value until API response is received
    this.isInitialized = false;
    this.initPromise = null;
  }

  /**
   * 初始化存储服务
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    // 如果已经初始化过或正在初始化，返回初始化Promise
    if (this.isInitialized) {
      return true;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    // 创建初始化Promise
    this.initPromise = this._fetchStorageFolder()
      .then(success => {
        this.isInitialized = success;
        return success;
      })
      .catch(error => {
        console.error('初始化存储服务失败:', error);
        return false;
      });

    return this.initPromise;
  }

  /**
   * 从API获取存储文件夹路径
   * @returns {Promise<boolean>} 是否成功获取存储文件夹
   * @private
   */
  async _fetchStorageFolder() {
    try {
      const response = await axios.get(`${getServerUrl()}/api/config/addon-config-path`);
      const data = response.data;

      if (data.success && data.addonConfigPath) {
        this.storageFolder = data.addonConfigPath;
        console.log('存储文件夹设置为:', this.storageFolder);
        return true;
      }
      return false;
    } catch (error) {
      console.error('从API获取存储文件夹失败:', error);
      return false;
    }
  }

  /**
   * 确保存储目录存在
   * @returns {boolean} 存储目录是否存在或创建成功
   */
  ensureStorageDirectory() {
    try {
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取当前存储文件夹路径
   * @returns {string} 存储文件夹路径
   */
  getStorageFolder() {
    return this.storageFolder;
  }

  /**
   * 手动设置存储文件夹路径
   * @param {string} path 新的存储文件夹路径
   * @returns {boolean} 是否成功设置
   */
  setStorageFolder(path) {
    if (path) {
      this.storageFolder = path;
      return true;
    }
    return false;
  }

  /**
   * 保存数据到文件
   * @param {string} fileName 文件名
   * @param {any} data 要保存的数据
   * @returns {boolean} 是否成功保存
   */
  saveToFile(fileName, data) {
    try {
      if (!this.ensureStorageDirectory()) {
        return false;
      }
      
      const fs = window.Application.FileSystem;
      const filePath = `${this.storageFolder}\\${fileName}`;
      
      const jsonData = JSON.stringify(data);
      fs.WriteFile(filePath, jsonData);
      
      return true;
    } catch (error) {
      console.error(`保存文件 ${fileName} 时出错:`, error);
      return false;
    }
  }

  /**
   * 从文件加载数据
   * @param {string} fileName 文件名
   * @returns {any|null} 加载的数据或null（如果加载失败）
   */
  loadFromFile(fileName) {
    try {
      const fs = window.Application.FileSystem;
      const filePath = `${this.storageFolder}\\${fileName}`;
      
      if (!fs.Exists(filePath)) {
        return null;
      }
      
      const jsonData = fs.ReadFile(filePath);
      
      return JSON.parse(jsonData);
    } catch (error) {
      console.error(`从文件 ${fileName} 加载数据时出错:`, error);
      return null;
    }
  }

  /**
   * 保存认证数据
   * @param {any} authData 认证数据
   * @returns {boolean} 是否成功保存
   */
  saveAuthData(authData) {
    return this.saveToFile(AUTH_FILE_NAME, authData);
  }

  /**
   * 加载认证数据
   * @returns {any|null} 认证数据或null（如果加载失败）
   */
  loadAuthData() {
    return this.loadFromFile(AUTH_FILE_NAME);
  }

  /**
   * 清除认证数据
   * @returns {boolean} 是否成功清除
   */
  clearAuthData() {
    try {
      const fs = window.Application.FileSystem;
      const filePath = `${this.storageFolder}\\${AUTH_FILE_NAME}`;
      
      if (fs.Exists(filePath)) {
        fs.DeleteFile(filePath);
      }
      
      return true;
    } catch (error) {
      console.error('清除认证数据时出错:', error);
      return false;
    }
  }
}

// 创建单例实例
const storageService = new StorageService();

// 导出服务函数
export async function initializeStorage() {
  return storageService.initialize();
}

export function saveAuthData(authData) {
  return storageService.saveAuthData(authData);
}

export function loadAuthData() {
  return storageService.loadAuthData();
}

export function clearAuthData() {
  return storageService.clearAuthData();
}

export function getStorageFolder() {
  return storageService.getStorageFolder();
}

export function setStorageFolder(path) {
  return storageService.setStorageFolder(path);
}

// 导出服务实例（高级用法）
export { storageService };
