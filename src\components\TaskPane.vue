<template>
  <div class="task-pane">
    <!-- Loading overlay -->
    <div class="loading-overlay" v-if="isLoading">
      <div class="loading-spinner"></div>
      <div class="loading-text">处理中...</div>
    </div>

    <!-- 文档格式错误遮罩 -->
    <div class="format-error-overlay" v-if="isPageLocked">
      <div class="format-error-content">
        <div class="format-error-icon">⚠️</div>
        <div class="format-error-title">文档格式不支持</div>
        <div class="format-error-message">{{ documentFormatMessage }}</div>
        <div class="format-error-actions">
          <button class="retry-check-btn" @click="performDocumentFormatCheck()">重新检查</button>
        </div>
      </div>
    </div>

    <!-- 文件名常驻顶部 -->
    <div class="doc-header">
      <div class="doc-title">
        {{ docName || '未选择文档' }}
        <!-- <span v-if="Object.keys(map).length > 0" class="status-badge">
          <span v-if="getRunningTasksCount() > 0" class="badge running">{{getRunningTasksCount()}}个进行中</span>
          <span v-if="getCompletedTasksCount() > 0" class="badge completed">{{getCompletedTasksCount()}}个完成</span>
          <span v-if="getErrorTasksCount() > 0" class="badge error">{{getErrorTasksCount()}}个异常</span>
        </span> -->
      </div>
      <div class="header-controls">
        <!-- 批量测试模式控制 -->
        <div v-if="userInfo?.orgs[0]?.orgId === 20000000" class="batch-test-header-control">
          <label class="batch-toggle-label-compact">
            <input
              type="checkbox"
              v-model="isBatchTestMode"
              class="batch-toggle-input"
              :disabled="isPageLocked"
            />
            <span class="batch-toggle-slider-compact"></span>
            <span class="batch-toggle-text-compact">批量测试</span>
          </label>

          <button
            v-if="isBatchTestMode && getBatchWaitingTasksCount() > 0"
            class="batch-insert-btn-compact"
            @click="batchInsertAllTasks()"
            :disabled="isPageLocked"
            :title="`批量插入 ${getBatchWaitingTasksCount()} 个任务`"
          >
            插入({{ getBatchWaitingTasksCount() }})
          </button>
        </div>

        <button class="settings-btn" @click="showFileWatcher = true">
          <i class="icon-settings"></i>
        </button>
      </div>
    </div>

    <!-- 操作区域 -->
    <div class="action-area">
      <!-- 添加学科和年级选择框 -->
      <div class="select-container">
        <div class="select-group">
          <label for="stage-select">年级:</label>
          <select id="stage-select" v-model="stage" class="select-input" :disabled="isPageLocked">
            <option v-for="option in stageOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
        <div class="select-group">
          <label for="subject-select">学科:</label>
          <select id="subject-select" v-model="subject" class="select-input" :disabled="isPageLocked">
            <option v-for="option in filteredSubjectOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
          <span
            v-if="userInfo && !userInfo.isAdmin && !userInfo.isOwner && userInfo.subject"
            class="subject-hint"
            :title="`当前用户只能使用 ${filteredSubjectOptions.find(opt => opt.value === userInfo.subject)?.label || userInfo.subject} 学科`"
          >
            🔒
          </span>
        </div>
      </div>
      <div v-if="0 && isScienceSubject" class="science-warning">
        理科可使用增强模式以获取更精准的解析
      </div>
      <div class="action-buttons">
        <button
          class="action-btn primary"
          @click="checkSubjectAndRun1('wps-analysis')"
          :disabled="isAnalyzeLoading || isPageLocked"
        >
          <div class="btn-content">
            <span v-if="isAnalyzeLoading" class="button-loader"></span>
            <span class="btn-text">解析</span>
          </div>
        </button>
        <button
          v-if="0 && isScienceSubject"
          class="action-btn enhance"
          @click="checkSubjectAndRun1('wps-enhance_analysis')"
          :disabled="isEnhanceLoading || isPageLocked"
        >
          <div class="btn-content">
            <span v-if="isEnhanceLoading" class="button-loader"></span>
            <span class="btn-text">增强解析</span>
          </div>
        </button>
        <button
          class="action-btn secondary"
          @click="checkSubjectAndRunCheck()"
          :disabled="isCheckLoading || isPageLocked"
          v-if="userInfo?.orgs[0]?.orgId === 2"
        >
          <div class="btn-content">
            <span v-if="isCheckLoading" class="button-loader"></span>
            <span class="btn-text">校对</span>
          </div>
        </button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- <div class="selection-info" @click="showSelectionModal = true">
        <div class="info-label">当前选中内容:</div>
        <div class="info-content">{{ selected ? '点击查看选中内容' : '未选中内容' }}</div>
      </div> -->

      <!-- 选中内容弹窗 -->
      <div class="modal-overlay" v-if="showSelectionModal" @click="showSelectionModal = false">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <div class="modal-title">选中内容</div>
            <button class="modal-close" @click="showSelectionModal = false">×</button>
          </div>
          <div class="modal-body">
            <pre class="selection-content">{{ selected || '未选中内容' }}</pre>
          </div>
        </div>
      </div>

      <!-- 提示弹窗 -->
      <div class="modal-overlay" v-if="showAlertModal" @click="showAlertModal = false">
        <div class="modal-content alert-modal" @click.stop>
          <div class="modal-header">
            <div class="modal-title">提示</div>
            <button class="modal-close" @click="showAlertModal = false">×</button>
          </div>
          <div class="modal-body">
            <div class="alert-message">{{ alertMessage }}</div>
            <div class="alert-actions">
              <button class="action-btn primary" @click="showAlertModal = false">确定</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 确认弹窗 -->
      <div class="modal-overlay" v-if="confirmDialog.show">
        <div class="modal-content confirm-modal" @click.stop>
          <div class="modal-header">
            <div class="modal-title">确认</div>
            <button class="modal-close" @click="handleConfirm(false)">×</button>
          </div>
          <div class="modal-body">
            <div class="confirm-message">{{ confirmDialog.message }}</div>
            <div class="confirm-actions">
              <button class="action-btn secondary" @click="handleConfirm(false)">取消</button>
              <button class="action-btn primary" @click="handleConfirm(true)">确定</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误弹窗 -->
      <div class="modal-overlay" v-if="errorDialog.show" @click="hideErrorDialog()">
        <div class="modal-content alert-modal" @click.stop>
          <div class="modal-header">
            <div class="modal-title">{{ errorDialog.title }}</div>
            <button class="modal-close" @click="hideErrorDialog()">×</button>
          </div>
          <div class="modal-body">
            <div class="alert-message">{{ errorDialog.message }}</div>
            <div class="alert-actions">
              <button class="action-btn primary" @click="hideErrorDialog()">确定</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务队列区域 -->
      <div class="task-queue">
        <div class="queue-header">
          <div class="queue-title">任务队列</div>
          <div class="queue-status-filter">
            <select
              id="status-filter-select"
              v-model="selectedStatusFilter"
              class="status-filter-select-input"
            >
              <option
                v-for="option in statusFilterOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
          </div>
          <div class="queue-actions">
            <button
              class="release-all-btn"
              @click="releaseAllTasks"
              :disabled="getReleasableTasksCount() === 0"
              :title="getReleasableTasksCount() === 0 ? '无可释放任务' : `释放所有${getReleasableTasksCount()}个可释放任务`"
            >
              一键释放
            </button>
            <!-- <button
              class="collapse-toggle-btn"
              @click="toggleAllGroups"
              :title="hasCollapsedGroups ? '展开所有折叠组' : '折叠所有已释放任务组'"
              v-if="hasReleasedGroups"
            >
              {{ hasCollapsedGroups ? '全部展开' : '全部折叠' }}
            </button> -->
          </div>
          <div class="task-count">{{ Object.keys(displayedTasks).length }}个任务</div>
        </div>
        <div class="queue-table-container" v-if="Object.keys(displayedTasks).length > 0">
          <table class="queue-table" :class="{ 'narrow-view': isNarrowView, 'ultra-narrow-view': isUltraNarrowView }">
            <thead>
              <tr>
                <th class="col-id">
                  <div class="id-header">
                    <span>任务ID</span>
                    <span class="help-icon" @mouseenter="showEnhanceTooltip($event)">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#666"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                      </svg>
                    </span>
                  </div>
                </th>
                <th class="col-subject" v-if="!isNarrowView">
                  <div class="subject-header">
                    <span>题目内容</span>
                    <label class="switch">
                      <input type="checkbox" v-model="showSubjectTooltip" />
                      <span
                        class="slider round"
                        :title="showSubjectTooltip ? '关闭题目预览' : '开启题目预览'"
                      ></span>
                    </label>
                  </div>
                </th>
                <th class="col-status" v-if="!isUltraNarrowView">状态</th>
                <th class="col-actions">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="item in processedTasks" :key="item.type === 'group' ? item.groupId : item.tid">
                <!-- 折叠组显示 -->
                <tr v-if="item.type === 'group'" class="group-row" @click="toggleGroupCollapse(item.groupId)">
                  <td class="col-id">
                    <div class="id-cell group-cell">
                      <span class="group-toggle-icon">
                        {{ item.isCollapsed ? '▶' : '▼' }}
                      </span>
                      <span class="group-label">已释放任务组 ({{ item.count }}个)</span>
                    </div>
                  </td>
                  <td class="col-subject" v-if="!isNarrowView">
                    <div class="subject-cell group-subject">
                      {{ item.isCollapsed ? '点击展开查看详情' : '点击折叠隐藏详情' }}
                    </div>
                  </td>
                  <td class="col-status" v-if="!isUltraNarrowView">
                    <div class="status-cell">
                      <span class="task-tag status-released">已释放</span>
                    </div>
                  </td>
                  <td class="col-actions">
                    <div class="task-actions">
                      <span class="group-action-text">{{ item.isCollapsed ? '展开' : '折叠' }}</span>
                    </div>
                  </td>
                </tr>

                <!-- 折叠组内的任务 -->
                <template v-if="item.type === 'group' && !item.isCollapsed">
                  <tr
                    v-for="(task, index) in item.tasks"
                    :key="task.tid"
                    class="task-row group-task-row"
                    :class="[
                      getTaskStatusClass(task.status),
                      { 'selected-task-row': task.tid === selectedTaskId }
                    ]"
                    @click="navigateToTask(task.tid, task)"
                  >
                    <td class="col-id">
                      <div class="id-cell" :class="{ 'id-with-status': isUltraNarrowView }">
                        <div class="id-content">
                          <span class="group-indent">└─</span>
                          <span class="task-id">{{ task.tid.substring(0, 8) }}</span>
                          <span
                            v-if="task.wordType === 'wps-analysis'"
                            class="analysis-task-icon"
                            title="解析任务"
                          >
                            解
                          </span>
                          <span
                            v-if="task.wordType === 'wps-enhance_analysis' || task.isEnhanced"
                            class="enhance-task-icon"
                            title="增强解析任务"
                          >
                            解
                          </span>
                          <span
                            v-if="task.wordType === 'wps-check' || task.isCheckTask"
                            class="check-task-icon"
                            title="校对任务"
                          >
                            校
                          </span>
                          <span
                            v-if="hasVerticalTab(task)"
                            class="soft-break-warning-icon"
                            @mouseenter="showSoftBreakTooltip($event)"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="#ff9800"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <title>提示</title>
                              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                              <line x1="12" y1="9" x2="12" y2="13"></line>
                              <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                          </span>
                        </div>
                        <div v-if="isUltraNarrowView" class="status-in-id">
                          <span
                            class="task-tag compact"
                            :class="getTaskStatusClass(task.status)"
                            @mouseenter="task.status === -1 && task.errorMessage ? showErrorTooltip($event, task) : null"
                            :style="{ cursor: task.status === -1 && task.errorMessage ? 'help' : 'default' }"
                          >{{
                            getTaskStatusText(task.status)
                          }}</span>
                        </div>
                      </div>
                    </td>
                    <td class="col-subject" v-if="!isNarrowView">
                      <div class="subject-cell" @mouseenter="showTooltip($event, task)">
                        {{ getSubjectPreview(task) }}
                      </div>
                    </td>
                    <td class="col-status" v-if="!isUltraNarrowView">
                      <div class="status-cell">
                        <span
                          class="task-tag"
                          :class="getTaskStatusClass(task.status)"
                          @mouseenter="task.status === -1 && task.errorMessage ? showErrorTooltip($event, task) : null"
                          :style="{ cursor: task.status === -1 && task.errorMessage ? 'help' : 'default' }"
                        >{{
                          getTaskStatusText(task.status)
                        }}</span>
                      </div>
                    </td>
                    <td class="col-actions">
                      <div class="task-actions">
                        <!-- 已释放任务没有操作按钮 -->
                        <span class="no-action-icon" title="无可用操作">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="#9e9e9e"
                            stroke-width="1.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                          </svg>
                        </span>
                      </div>
                    </td>
                  </tr>
                </template>

                <!-- 普通任务行 -->
                <tr
                  v-if="item.type === 'task'"
                  class="task-row"
                  :class="[
                    getTaskStatusClass(item.status),
                    { 'selected-task-row': item.tid === selectedTaskId }
                  ]"
                  @click="navigateToTask(item.tid, item)"
                >
                  <td class="col-id">
                    <div class="id-cell" :class="{ 'id-with-status': isUltraNarrowView }">
                      <div class="id-content">
                        <span class="task-id">{{ item.tid.substring(0, 8) }}</span>
                        <span
                          v-if="!item.isEnhanced && !item.isCheckTask"
                          class="analysis-task-icon"
                          title="解析任务"
                        >
                          解
                        </span>
                        <span
                          v-if="item.isEnhanced"
                          class="enhance-task-icon"
                          title="增强解析任务"
                        >
                          解
                        </span>
                        <span
                          v-if="item.isCheckTask"
                          class="check-task-icon"
                          title="校对任务"
                        >
                          校
                        </span>
                        <span
                          v-if="hasVerticalTab(item)"
                          class="soft-break-warning-icon"
                          @mouseenter="showSoftBreakTooltip($event)"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="#ff9800"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <title>提示</title>
                            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                            <line x1="12" y1="9" x2="12" y2="13"></line>
                            <line x1="12" y1="17" x2="12.01" y2="17"></line>
                          </svg>
                        </span>
                      </div>
                      <div v-if="isUltraNarrowView" class="status-in-id">
                        <span
                          class="task-tag compact"
                          :class="getTaskStatusClass(item.status)"
                          @mouseenter="item.status === -1 && item.errorMessage ? showErrorTooltip($event, item) : null"
                          :style="{ cursor: item.status === -1 && item.errorMessage ? 'help' : 'default' }"
                        >{{
                          getTaskStatusText(item.status)
                        }}</span>
                      </div>
                    </div>
                  </td>
                  <td class="col-subject" v-if="!isNarrowView">
                    <div class="subject-cell" @mouseenter="showTooltip($event, item)">
                      {{ getSubjectPreview(item) }}
                    </div>
                  </td>
                  <td class="col-status" v-if="!isUltraNarrowView">
                    <div class="status-cell">
                      <span
                        class="task-tag"
                        :class="getTaskStatusClass(item.status)"
                        @mouseenter="item.status === -1 && item.errorMessage ? showErrorTooltip($event, item) : null"
                        :style="{ cursor: item.status === -1 && item.errorMessage ? 'help' : 'default' }"
                      >{{
                        getTaskStatusText(item.status)
                      }}</span>
                    </div>
                  </td>
                  <td class="col-actions">
                    <div class="task-actions">
                      <button
                        v-if="item.status === 1"
                        @click.stop="terminateTask(item.tid)"
                        class="terminate-btn"
                      >
                        终止
                      </button>
                      <button
                        v-if="item.status === 2 || item.status === 4"
                        @click.stop="deleteTaskControl(item.tid)"
                        class="release-btn"
                        title="释放任务控件"
                      >
                        释放
                      </button>
                      <!-- 当没有任何操作按钮时显示的图标 -->
                      <span
                        v-if="
                          item.status !== 1 && item.status !== 2 && item.status !== 4
                        "
                        class="no-action-icon"
                        title="无可用操作"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="#9e9e9e"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="12"></line>
                          <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                      </span>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
        <div class="empty-queue" v-else>
          <div class="empty-text">暂无任务</div>
        </div>
      </div>

      <!-- 日志展示区域 -->
      <div class="log-container" v-if="userInfo?.orgs[0]?.orgId === 2">
        <div class="log-header" @click="toggleLogContent">
          <div class="log-title">执行日志</div>
          <div class="log-actions">
            <button class="clear-btn" @click.stop="clearLog()">清空日志</button>
            <span class="toggle-icon">{{ showLogContent ? '▼' : '▶' }}</span>
          </div>
        </div>
        <div class="log-content" v-if="showLogContent" v-html="logger"></div>
      </div>
    </div>

    <!-- 文件监控弹窗 -->
    <div v-if="showFileWatcher" class="modal-overlay" @click="showFileWatcher = false">
      <div class="modal-content" @click.stop>
        <ServerSetting @close="showFileWatcher = false" />
      </div>
    </div>

    <!-- Tippy.js 会在 JS 中动态创建 tooltip -->
  </div>
</template>

<script setup>
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { useTaskPane } from './js/useTaskPane.js'
import ServerSetting from './ServerSetting.vue'
import tippy from 'tippy.js'
import 'tippy.js/dist/tippy.css'
import 'tippy.js/themes/light.css'

const showSelectionModal = ref(false)
const showAlertModal = ref(false)
const showFileWatcher = ref(false)
const alertMessage = ref('')
const showLogContent = ref(false)
const isAnalyzeLoading = ref(false)
const isEnhanceLoading = ref(false)
const isCheckLoading = ref(false)

// 文档格式检查相关状态
const isDocumentFormatValid = ref(true)
const documentFormatMessage = ref('')
const isPageLocked = ref(false)

const windowWidth = ref(window.innerWidth);
const isNarrowView = computed(() => windowWidth.value < 750);
const isUltraNarrowView = computed(() => windowWidth.value < 380);

const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

const selectedTaskId = ref(null)
const showOnlySelected = ref(false)
const selectedStatusFilter = ref('')

// 折叠功能相关状态
const collapsedGroups = ref(new Set()) // 存储折叠的组ID
const showCollapsedTasks = ref(false) // 是否显示折叠的任务

// Tippy 实例存储
const tippyInstances = {
  subjects: new Map(), // 存储题目提示的 tippy 实例
  enhance: new Map(), // 存储增强模式提示的 tippy 实例
  switch: null, // 存储开关提示的 tippy 实例
  softBreak: new Map(), // 存储软换行警告提示的 tippy 实例
  error: new Map() // 存储错误信息提示的 tippy 实例
}

// 题目预览提示开关
const showSubjectTooltip = ref(false)

const statusFilterOptions = ref([
  { value: '', label: '所有状态' },
  { value: 1, label: '进行中' },
  { value: 2, label: '完成' },
  { value: 4, label: '已停止' },
  { value: 5, label: '等待批量插入' }
  // { value: 3, label: '已释放' },
  // { value: -1, label: '异常' }
])

const {
  docName,
  selected,
  logger,
  map,
  subject,
  stage,
  subjectOptions,
  stageOptions,
  appConfig, // 添加应用配置
  clearLog,
  checkDocumentFormat,
  getTaskStatusClass,
  getTaskStatusText,
  terminateTask,
  run1,
  runCheck, // 添加校对功能
  setupLifecycle,
  navigateToTaskControl,
  isLoading,
  tryRemoveTaskPlaceholderWithLoading,
  confirmDialog,
  handleConfirm,
  errorDialog, // 添加错误弹窗状态
  hideErrorDialog, // 添加隐藏错误弹窗方法
  getCompletedTasksCount,
  getReleasableTasksCount,
  showConfirm,
  // 批量测试模式相关
  isBatchTestMode,
  batchInsertAllTasks,
  getBatchWaitingTasksCount
} = useTaskPane()
const userInfo = ref(null)

// 加载用户信息的函数
const loadUserInfo = () => {
  try {
    if (window.Application?.PluginStorage) {
      const userInfoStr = window.Application.PluginStorage.getItem('user_info')
      if (userInfoStr) {
        userInfo.value = JSON.parse(userInfoStr)
        console.log('用户信息已加载:', userInfo.value)
      } else {
        console.log('未找到用户信息')
      }
    }
  } catch (error) {
    console.error('解析用户信息时出错:', error)
  }
}

// 初始加载用户信息
loadUserInfo()

// 当用户信息加载后，检查校对功能可见性
watch(userInfo, (newUserInfo) => {
  if (newUserInfo && newUserInfo.orgs && newUserInfo.orgs[0]) {
    // 这里可以添加额外的校对功能检查逻辑
    console.log(`用户企业ID: ${newUserInfo.orgs[0].orgId}, 校对功能${newUserInfo.orgs[0].orgId === 2 ? '可用' : '不可用'}`)
  }
}, { immediate: true })

// 根据用户权限过滤学科选项
const filteredSubjectOptions = computed(() => {
  if (!userInfo.value) {
    return subjectOptions
  }

  // 如果是管理员或拥有者，显示全部学科
  if (userInfo.value.isAdmin || userInfo.value.isOwner) {
    return subjectOptions
  }

  // 如果用户有特定学科权限，只显示该学科
  if (userInfo.value.subject) {
    return subjectOptions.filter(option => option.value === userInfo.value.subject)
  }

  // 默认显示全部学科
  return subjectOptions
})

// 自动根据用户信息设置学科
const initializeSubjectFromUserInfo = () => {
  if (!userInfo.value) return

  // 如果用户不是管理员且有特定学科，自动设置
  if (!userInfo.value.isAdmin && !userInfo.value.isOwner && userInfo.value.subject) {
    subject.value = userInfo.value.subject
  }
}

const isScienceSubject = computed(() => {
  const scienceSubjects = ['physics', 'chemistry', 'biology', 'math']
  return scienceSubjects.includes(subject.value)
})

// 监听 userInfo 变化，自动重新初始化学科选择
// watch(userInfo, (newUserInfo) => {
//   if (newUserInfo) {
//     initializeSubjectFromUserInfo()
//   }
// }, { deep: true })

// 监听应用配置变化
watch(appConfig, (newConfig) => {
  if (newConfig) {
    console.log('TaskPane组件收到应用配置更新:', newConfig);
    console.log('当前版本类型:', newConfig.EDITION);
    console.log('当前年级选项:', stageOptions.value);
  }
}, { deep: true, immediate: true });

// 检查文档格式的函数
const performDocumentFormatCheck = () => {
  try {
    const formatCheck = checkDocumentFormat()
    isDocumentFormatValid.value = formatCheck.isValid
    documentFormatMessage.value = formatCheck.message
    isPageLocked.value = !formatCheck.isValid

    if (!formatCheck.isValid) {
      console.warn('文档格式检查失败:', formatCheck.message)
    }
  } catch (error) {
    console.error('执行文档格式检查时出错:', error)
    isDocumentFormatValid.value = false
    documentFormatMessage.value = '检查文档格式时出错，请确保当前文档已保存为 .docx 格式。'
    isPageLocked.value = true
  }
}

const displayedTasks = computed(() => {
  let filteredByStatus = {}
  const sourceTasks = map

  if (selectedStatusFilter.value === '') {
    filteredByStatus = { ...sourceTasks }
  } else {
    for (const tid in sourceTasks) {
      if (Object.prototype.hasOwnProperty.call(sourceTasks, tid)) {
        const task = sourceTasks[tid]
        if (task.status === selectedStatusFilter.value) {
          filteredByStatus[tid] = task
        }
      }
    }
  }

  if (showOnlySelected.value && selectedTaskId.value) {
    if (filteredByStatus[selectedTaskId.value]) {
      return { [selectedTaskId.value]: filteredByStatus[selectedTaskId.value] }
    } else {
      return {}
    }
  }

  return filteredByStatus
})

// 按时间排序的任务列表，新任务在最上面
const sortedDisplayedTasks = computed(() => {
  const tasks = displayedTasks.value
  const taskArray = Object.entries(tasks).map(([tid, task]) => ({
    tid,
    ...task
  }))

  // 按开始时间降序排列（新任务在前）
  return taskArray
    .sort((a, b) => {
      const timeA = a.startTime || 0
      const timeB = b.startTime || 0
      return timeB - timeA
    })
    .reduce((acc, task) => {
      const { tid, ...taskData } = task
      acc[tid] = taskData
      return acc
    }, {})
})

// 处理任务分组和折叠的计算属性
const processedTasks = computed(() => {
  const tasks = sortedDisplayedTasks.value
  const taskArray = Object.entries(tasks).map(([tid, task]) => ({
    tid,
    ...task
  }))

  // 分离已释放任务和其他任务
  const releasedTasks = taskArray.filter(task => task.status === 3)
  const otherTasks = taskArray.filter(task => task.status !== 3)

  const result = []

  // 先添加非已释放任务
  otherTasks.forEach(task => {
    result.push({
      type: 'task',
      ...task
    })
  })

  // 如果有已释放任务且数量>=2，创建折叠组
  if (releasedTasks.length >= 2) {
    const groupId = 'released-group-all'
    const isCollapsed = collapsedGroups.value.has(groupId)

    result.push({
      type: 'group',
      groupId: groupId,
      isCollapsed: isCollapsed,
      tasks: releasedTasks,
      count: releasedTasks.length
    })
  } else {
    // 少于2个已释放任务，直接添加到结果中
    releasedTasks.forEach(releasedTask => {
      result.push({
        type: 'task',
        ...releasedTask
      })
    })
  }

  return result
})

// 切换折叠状态
const toggleGroupCollapse = (groupId) => {
  if (collapsedGroups.value.has(groupId)) {
    collapsedGroups.value.delete(groupId)
  } else {
    collapsedGroups.value.add(groupId)
  }
}

const checkSubjectAndRun1 = (wordType = 'wps-analysis') => {
  if (!subject.value) {
    alertMessage.value = '请选择学科'
    showAlertModal.value = true
  } else if (
    !window.Application ||
    !window.Application.Selection ||
    !window.Application.Selection.Text.trim()
  ) {
    alertMessage.value = '未选中内容'
    showAlertModal.value = true
  } else {
    if (wordType === 'wps-analysis') {
      isAnalyzeLoading.value = true
    } else if (wordType === 'wps-enhance_analysis') {
      isEnhanceLoading.value = true
    }

    // 创建取消loading的回调函数
    const cancelLoadingCallback = () => {
      if (wordType === 'wps-analysis') {
        isAnalyzeLoading.value = false
      } else if (wordType === 'wps-enhance_analysis') {
        isEnhanceLoading.value = false
      }
    }

    run1(wordType, cancelLoadingCallback)
      .catch((error) => {
        console.log(error)
        if (error.message.includes('重叠')) {
          alertMessage.value = `当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`
          showAlertModal.value = true
        } else {
          console.error('操作失败:', error)
          alertMessage.value = error.message;
          showAlertModal.value = true
        }
        // 发生错误时取消loading
        cancelLoadingCallback()
      })
  }
}

const checkSubjectAndRunCheck = () => {
  if (!subject.value) {
    alertMessage.value = '请选择学科'
    showAlertModal.value = true
  } else if (
    !window.Application ||
    !window.Application.Selection ||
    !window.Application.Selection.Text.trim()
  ) {
    alertMessage.value = '未选中内容'
    showAlertModal.value = true
  } else {
    isCheckLoading.value = true

    // 创建取消校对loading的回调函数
    const cancelCheckLoadingCallback = () => {
      isCheckLoading.value = false
    }

    runCheck(cancelCheckLoadingCallback)
      .catch((error) => {
        console.log(error)
        if (error.message.includes('重叠')) {
          alertMessage.value = `当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`
          showAlertModal.value = true
        } else {
          console.error('校对操作失败:', error)
          alertMessage.value = error.message;
          showAlertModal.value = true
        }
        // 发生错误时取消loading
        cancelCheckLoadingCallback()
      })
  }
}

const navigateToTask = (tid, task) => {
  selectedTaskId.value = tid
  navigateToTaskControl(tid)
}

const deleteTaskControl = (tid) => {
  if (map[tid]) {
    map[tid].status = 3
  }
  if (selectedTaskId.value === tid) {
    selectedTaskId.value = null
  }
  tryRemoveTaskPlaceholderWithLoading(tid, true)
}

// 释放所有已完成和已停止的任务
const releaseAllTasks = async () => {
  const releasableTasks = Object.entries(map).filter(([tid, task]) => task.status === 2 || task.status === 4);

  if (releasableTasks.length === 0) {
    alertMessage.value = '没有可释放的任务'
    showAlertModal.value = true
    return
  }

  try {
    // 显示确认对话框
    const confirmed = await showConfirm(`确定要释放所有 ${releasableTasks.length} 个可释放的任务吗？\n此操作不可撤销。`)

    if (confirmed) {
      // 执行批量释放
      let releasedCount = 0
      releasableTasks.forEach(([tid, task]) => {
        if (map[tid]) {
          map[tid].status = 3
          if (selectedTaskId.value === tid) {
            selectedTaskId.value = null
          }
          tryRemoveTaskPlaceholderWithLoading(tid, true)
          releasedCount++
        }
      })

      // 显示释放结果
      alertMessage.value = `已成功释放 ${releasedCount} 个任务`
      showAlertModal.value = true
    }
  } catch (error) {
    console.error('释放任务时出错:', error)
    alertMessage.value = '释放任务时出现错误'
    showAlertModal.value = true
  }
}

const toggleLogContent = () => {
  showLogContent.value = !showLogContent.value
}

// 过滤特殊字符的工具函数
const filterSpecialCharacters = (text) => {
  if (!text) return ''
  return (
    text
      .toString()
      // 移除换行符、制表符等控制字符
      .replace(/[\r\n\t\f\v]/g, ' ')
      // 移除其他控制字符
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
      // 移除多余的空格
      .replace(/\s+/g, ' ')
      // 移除首尾空格
      .trim()
  )
}

// 获取题目内容预览（自适应长度）
const getSubjectPreview = (task) => {
  const content = getFullSubjectContent(task)
  if (!content) return '无题目内容'
  const cleanContent = filterSpecialCharacters(content)
  // 不再硬编码字符数量限制，让CSS控制显示
  return cleanContent
}

// 获取题目完整内容（用于tooltip）
const getFullSubjectContent = (task) => {
  if (!task.selectedText) return ''

  // 先按\r分割内容，保留段落结构（在清理特殊字符前）
  const paragraphs = task.selectedText.split('\r').filter((p) => p.trim())

  // 如果没有\r分割，尝试\n分割
  if (paragraphs.length === 1) {
    const nlParagraphs = task.selectedText.split('\n').filter((p) => p.trim())
    if (nlParagraphs.length > 1) {
      paragraphs.splice(0, 1, ...nlParagraphs)
    }
  }

  // 格式化段落，每段最多显示200个字符
  const formattedParagraphs = paragraphs.map((paragraph, index) => {
    const trimmedParagraph = paragraph.trim()
    if (trimmedParagraph.length > 200) {
      // return `${trimmedParagraph.substring(0, 200)}...`;
      return trimmedParagraph
    }
    return trimmedParagraph
  })

  // 如果只有一段，不添加序号
  if (formattedParagraphs.length === 1) {
    // return singleParagraph.length > 300 ? singleParagraph.substring(0, 300) + '...' : singleParagraph;
    return paragraphs[0].trim()
  }

  return formattedParagraphs.join('\n')
}

// 创建并显示题目内容提示
const showTooltip = (event, task) => {
  // 如果提示开关关闭，不显示提示
  if (!showSubjectTooltip.value) return

  const element = event.target
  const content = getFullSubjectContent(task).toString()

  if (!content || content.trim() === '') {
    console.log('题目内容为空，不显示tooltip')
    return // 如果内容为空，直接返回
  }

  // 格式化内容为HTML
  const htmlContent = `
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${content.replace(/(\S{40})(?=\S)/g, '$1<wbr>')}</div>
    </div>
  `

  // 记录鼠标位置
  const mouseX = event.clientX
  const mouseY = event.clientY

  // 如果已经创建过该元素的tippy实例，则直接使用并显示
  if (tippyInstances.subjects.has(element)) {
    const instance = tippyInstances.subjects.get(element)
    instance.setContent(htmlContent)
    // 更新位置后显示
    instance.setProps({
      getReferenceClientRect: () => ({
        width: 0,
        height: 0,
        top: mouseY,
        bottom: mouseY,
        left: mouseX,
        right: mouseX
      })
    })
    instance.show() // 强制显示
    return
  }

  // 创建新的tippy实例
  const instance = tippy(element, {
    content: htmlContent,
    allowHTML: true,
    placement: 'right',
    theme: 'light',
    interactive: true,
    appendTo: document.body,
    maxWidth: 280,
    animation: 'scale',
    duration: [200, 0], // 显示动画200ms，隐藏动画0ms（立即隐藏）
    trigger: 'manual', // 改为手动触发，以便我们可以控制显示时机
    hideOnClick: false, // 点击时不隐藏
    interactiveBorder: 30, // 增加交互边界，使鼠标移动到tooltip内部更容易
    popperOptions: {
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            boundary: document.body,
            padding: 10
          }
        }
      ]
    },
    // 使用鼠标位置作为参考点
    getReferenceClientRect: () => ({
      width: 0,
      height: 0,
      top: mouseY,
      bottom: mouseY,
      left: mouseX,
      right: mouseX
    }),
    // 隐藏时清理
    onHidden: () => {
      instance.setProps({ getReferenceClientRect: null })
    }
  })

  // 存储实例引用
  tippyInstances.subjects.set(element, instance)

  // 手动显示 tooltip
  instance.show()
}

// 显示任务ID说明tooltip
const showEnhanceTooltip = (event) => {
  const element = event.currentTarget
  const content = `
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `

  // 记录鼠标位置
  const mouseX = event.clientX
  const mouseY = event.clientY

  // 如果已经创建过该元素的tippy实例，则直接使用并显示
  if (tippyInstances.enhance.has(element)) {
    const instance = tippyInstances.enhance.get(element)
    // 更新位置后显示
    instance.setProps({
      getReferenceClientRect: () => ({
        width: 0,
        height: 0,
        top: mouseY,
        bottom: mouseY,
        left: mouseX,
        right: mouseX
      })
    })
    instance.show() // 强制显示
    return
  }

  // 创建新的tippy实例
  const instance = tippy(element, {
    content: content,
    allowHTML: true,
    placement: 'bottom',
    theme: 'light',
    animation: 'scale',
    duration: [200, 0],
    trigger: 'manual',
    interactive: false,
    hideOnClick: true,
    maxWidth: 200
  })

  // 存储实例引用
  tippyInstances.enhance.set(element, instance)

  // 手动显示 tooltip
  instance.show()
}

// 显示错误信息tooltip
const showErrorTooltip = (event, task) => {
  const element = event.currentTarget
  const errorMessage = task.errorMessage || '未知错误'

  const content = `
    <div class="error-tooltip">
      <div class="error-tooltip-title">错误详情</div>
      <div class="error-tooltip-content">${errorMessage}</div>
    </div>
  `

  // 记录鼠标位置
  const mouseX = event.clientX
  const mouseY = event.clientY

  // 如果已经创建过该元素的tippy实例，则直接使用并显示
  if (tippyInstances.error.has(element)) {
    const instance = tippyInstances.error.get(element)
    instance.setContent(content)
    // 更新位置后显示
    instance.setProps({
      getReferenceClientRect: () => ({
        width: 0,
        height: 0,
        top: mouseY,
        bottom: mouseY,
        left: mouseX,
        right: mouseX
      })
    })
    instance.show() // 强制显示
    return
  }

  // 创建新的tippy实例
  const instance = tippy(element, {
    content: content,
    allowHTML: true,
    placement: 'top',
    theme: 'light',
    animation: 'scale',
    duration: [200, 0],
    trigger: 'manual',
    interactive: true,
    hideOnClick: true,
    maxWidth: 300,
    appendTo: document.body,
    interactiveBorder: 30,
    popperOptions: {
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            boundary: document.body,
            padding: 10
          }
        }
      ]
    },
    // 使用鼠标位置作为参考点
    getReferenceClientRect: () => ({
      width: 0,
      height: 0,
      top: mouseY,
      bottom: mouseY,
      left: mouseX,
      right: mouseX
    }),
    // 隐藏时清理
    onHidden: () => {
      instance.setProps({ getReferenceClientRect: null })
    }
  })

  // 存储实例引用
  tippyInstances.error.set(element, instance)

  // 手动显示 tooltip
  instance.show()
}

// 显示开关按钮提示
const showSwitchTooltip = (event) => {
  const element = event.target
  const content = `
    <div class="switch-tooltip">
      <div class="switch-tooltip-content">${showSubjectTooltip.value ? '关闭题目预览提示' : '开启题目预览提示'}</div>
    </div>
  `

  // 记录鼠标位置
  const mouseX = event.clientX
  const mouseY = event.clientY

  // 如果已经创建过该元素的tippy实例，则更新内容并显示
  if (tippyInstances.switch) {
    tippyInstances.switch.setContent(content)
    tippyInstances.switch.show()
    return
  }

  // 创建新的tippy实例
  const instance = tippy(element, {
    content: content,
    allowHTML: true,
    placement: 'bottom',
    theme: 'light',
    animation: 'scale',
    duration: [200, 0],
    trigger: 'manual',
    interactive: false,
    hideOnClick: true,
    maxWidth: 120
  })

  // 存储实例引用
  tippyInstances.switch = instance

  // 手动显示 tooltip
  instance.show()
}

// 清理 tippy 实例，在组件销毁前调用
const cleanupTippyInstances = () => {
  // 销毁所有题目提示实例
  tippyInstances.subjects.forEach((instance) => {
    instance.destroy()
  })
  tippyInstances.subjects.clear()

  // 销毁所有增强模式提示实例
  tippyInstances.enhance.forEach((instance) => {
    instance.destroy()
  })
  tippyInstances.enhance.clear()

  // 销毁开关提示实例
  if (tippyInstances.switch) {
    tippyInstances.switch.destroy()
    tippyInstances.switch = null
  }

  // 销毁软换行警告提示实例
  tippyInstances.softBreak.forEach((instance) => {
    instance.destroy()
  })
  tippyInstances.softBreak.clear()

  // 移除全局事件监听器
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('mousemove', handleGlobalMouseMove)
}

// 全局事件处理函数
const handleGlobalClick = (e) => {
  const tippyBox = document.querySelector('.tippy-box')
  if (tippyBox && !tippyBox.contains(e.target)) {
    // 关闭所有tippy实例
    tippyInstances.subjects.forEach((instance) => instance.hide())
    tippyInstances.enhance.forEach((instance) => instance.hide())
    if (tippyInstances.switch) {
      tippyInstances.switch.hide()
    }
    tippyInstances.softBreak.forEach((instance) => instance.hide())
  }
}

// 鼠标移动处理函数
let lastMoveTime = 0
const handleGlobalMouseMove = (e) => {
  const now = Date.now()
  // 限制处理频率，每100ms处理一次
  if (now - lastMoveTime < 100) return
  lastMoveTime = now

  const tippyBox = document.querySelector('.tippy-box')
  if (!tippyBox) return

  // 获取tippyBox位置
  const rect = tippyBox.getBoundingClientRect()
  // 检查鼠标是否在tippyBox附近
  const isNearTippyBox =
    e.clientX >= rect.left - 20 &&
    e.clientX <= rect.right + 20 &&
    e.clientY >= rect.top - 20 &&
    e.clientY <= rect.bottom + 20

  if (!isNearTippyBox && !tippyBox.matches(':hover')) {
    // 关闭所有tippy实例
    tippyInstances.subjects.forEach((instance) => instance.hide())
    tippyInstances.enhance.forEach((instance) => instance.hide())
    if (tippyInstances.switch) {
      tippyInstances.switch.hide()
    }
    tippyInstances.softBreak.forEach((instance) => instance.hide())
  }
}

// 全局点击和移动事件处理，用于关闭tooltip
const setupGlobalEvents = () => {
  // 添加全局事件监听
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('mousemove', handleGlobalMouseMove)
}

// 在组件挂载后添加全局样式和事件
onMounted(() => {
  window.addEventListener('resize', handleResize);
  // 添加全局事件处理
  setupGlobalEvents()

  // 根据用户信息初始化学科选择
  initializeSubjectFromUserInfo()

  // 检查文档格式
  setTimeout(() => {
    performDocumentFormatCheck()
  }, 500) // 延迟检查，确保WPS应用完全加载

  // 添加自定义样式到文档头
  const styleEl = document.createElement('style')
  styleEl.id = 'tippy-custom-styles'
  styleEl.textContent = `
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 错误信息提示样式 */
    .error-tooltip {
      padding: 12px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 300px;
      box-sizing: border-box;
    }

    .error-tooltip-title {
      font-weight: 600;
      color: #d32f2f;
      margin-bottom: 8px;
      font-size: 14px;
      display: flex;
      align-items: center;
    }

    .error-tooltip-title::before {
      content: '⚠';
      margin-right: 6px;
      font-size: 16px;
      color: #ff9800;
    }

    .error-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-all;
      text-align: left;
      background-color: #fafafa;
      padding: 8px;
      border-radius: 4px;
      border-left: 3px solid #d32f2f;
      max-height: 200px;
      overflow-y: auto;
      overflow-x: hidden;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `
  document.head.appendChild(styleEl)
})

// 组件销毁前清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 清理 tippy 实例
  cleanupTippyInstances()

  // 移除自定义样式
  const styleEl = document.getElementById('tippy-custom-styles')
  if (styleEl) {
    styleEl.remove()
  }
})

setupLifecycle()

// 检测是否包含垂直制表符
const hasVerticalTab = (task) => {
  if (!task.selectedText) return false
  // 检测垂直制表符 \v (\u000b)
  return task.selectedText.includes('\v') || task.selectedText.includes('\u000b')
}

// 显示软换行警告tooltip
const showSoftBreakTooltip = (event) => {
  const element = event.currentTarget
  const content = `
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `

  // 记录鼠标位置
  const mouseX = event.clientX
  const mouseY = event.clientY

  // 如果已经创建过该元素的tippy实例，则直接使用并显示
  if (tippyInstances.softBreak.has(element)) {
    const instance = tippyInstances.softBreak.get(element)
    // 更新位置后显示
    instance.setProps({
      getReferenceClientRect: () => ({
        width: 0,
        height: 0,
        top: mouseY,
        bottom: mouseY,
        left: mouseX,
        right: mouseX
      })
    })
    instance.show() // 强制显示
    return
  }

  // 创建新的tippy实例
  const instance = tippy(element, {
    content: content,
    allowHTML: true,
    placement: 'bottom-start',
    theme: 'light',
    animation: 'scale',
    duration: [200, 0],
    trigger: 'manual',
    interactive: true,
    hideOnClick: true,
    maxWidth: 280
  })

  // 存储实例引用
  tippyInstances.softBreak.set(element, instance)

  // 手动显示 tooltip
  instance.show()
}

// 全局折叠/展开所有组
const toggleAllGroups = () => {
  const allGroups = processedTasks.value.filter(item => item.type === 'group')

  if (hasCollapsedGroups.value) {
    // 如果有折叠的组，展开所有组
    collapsedGroups.value.clear()
  } else {
    // 如果没有折叠的组，折叠所有组
    allGroups.forEach(group => {
      collapsedGroups.value.add(group.groupId)
    })
  }
}

// 检查是否有已释放任务组
const hasReleasedGroups = computed(() => {
  return processedTasks.value.some(item => item.type === 'group')
})

// 检查是否有折叠的组
const hasCollapsedGroups = computed(() => {
  return collapsedGroups.value.size > 0
})
</script>

<style scoped>
.task-pane {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  color: #333;
  background-color: #f8f9fa;
}

.doc-header {
  background: linear-gradient(135deg, #229a52, #229a52);
  color: white;
  padding: 12px 15px;
  text-align: center;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  position: sticky;
  top: 0;
  z-index: 100;
  margin: 0 8px 8px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 紧凑版批量测试模式样式 */
.batch-test-header-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.batch-toggle-label-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.batch-toggle-input {
  display: none;
}

.batch-toggle-slider-compact {
  position: relative;
  width: 32px;
  height: 18px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 18px;
  transition: background-color 0.3s ease;
}

.batch-toggle-slider-compact::before {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.batch-toggle-input:checked + .batch-toggle-slider-compact {
  background-color: rgba(255, 255, 255, 0.8);
}

.batch-toggle-input:checked + .batch-toggle-slider-compact::before {
  transform: translateX(14px);
}

.batch-toggle-text-compact {
  color: white;
  user-select: none;
  white-space: nowrap;
}

.batch-insert-btn-compact {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.batch-insert-btn-compact:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.batch-insert-btn-compact:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
}

.doc-title {
  flex: 1;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.4;
  padding: 0 10px;
  word-wrap: break-word;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.settings-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: white;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.settings-btn:hover {
  opacity: 1;
}

.icon-settings {
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="white" d="M12 15.5A3.5 3.5 0 0 1 8.5 12 3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.65.07-.97 0-.32-.03-.65-.07-.97l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65c-.04-.24-.25-.42-.5-.42h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.63c-.04.32-.07.65-.07.97 0 .32.03.65.07.97l-2.11 1.63c-.19.15-.24.42-.12-.64l2 3.46c.12.22.39.31.61.22l2.49-1c.52.39 1.06.73 1.69.98l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.25 1.17-.59 1.69-.98l2.49 1c.22.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.63z"/></svg>')
    no-repeat center;
}

.action-area {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 10px;
  align-items: stretch;
}

.select-container {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
}

.select-group {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

.select-group label {
  font-size: 13px;
  margin-right: 8px;
  color: #555;
  min-width: 40px;
}

.subject-hint {
  margin-left: 6px;
  font-size: 14px;
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.subject-hint:hover {
  opacity: 1;
}

.select-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  color: #333;
  background-color: white;
}

.select-input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.action-btn {
  padding: 10px 0;
  min-width: 110px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.btn-text {
  margin: 0 5px;
}

.button-loader {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

.action-btn:disabled {
  position: relative;
  cursor: not-allowed;
  opacity: 0.8;
}

.action-btn:disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  pointer-events: none;
}

.action-btn.primary {
  background-color: #4285f4;
  color: white;
}

.action-btn.primary:hover {
  background-color: #3b78e7;
}

.action-btn.enhance {
  background-color: #9c27b0;
  color: white;
}

.action-btn.enhance:hover {
  background-color: #7b1fa2;
}

.action-btn.secondary {
  background-color: #34a853;
  color: white;
}

.action-btn.secondary:hover {
  background-color: #2d9346;
}

.content-area {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.selection-info {
  background-color: white;
  padding: 12px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.selection-info:hover {
  background-color: #f5f5f5;
}

.info-label {
  font-weight: bold;
  color: #555;
  margin-bottom: 4px;
  font-size: 13px;
}

.info-content {
  font-size: 12px;
  color: #666;
  word-break: break-word;
  max-height: 80px;
  overflow-y: auto;
  padding: 5px;
  background-color: #f5f5f5;
  border-radius: 3px;
  line-height: 1.5;
}

.task-queue {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f1f3f4;
  border-bottom: 1px solid #e0e0e0;
}

.queue-title {
  font-weight: bold;
  font-size: 13px;
  margin-right: auto;
}

.task-count {
  font-size: 12px;
  color: #666;
  margin-left: 10px;
}

.queue-actions {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.release-all-btn {
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.release-all-btn:hover:not(:disabled) {
  background-color: #e55a2e;
}

.release-all-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.queue-table-container {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.queue-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.queue-table thead {
  background-color: #f1f3f4;
  position: sticky;
  top: 0;
  z-index: 10;
}

.queue-table th {
  padding: 8px 10px;
  text-align: center;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
}

.queue-table th.col-id {
  text-align: left;
  padding-left: 22px;
}

.id-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: help;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.help-icon:hover {
  opacity: 1;
}

.queue-table .col-id {
  width: 110px;
}

.queue-table .col-subject {
  width: auto;
  min-width: 120px;
}

.queue-table .col-status {
  width: 100px;
}

.queue-table .col-actions {
  width: 80px;
}

.task-row {
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.task-row:hover {
  background-color: #f0f4f8;
}

.task-row td {
  padding: 8px 6px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: center;
}

.selected-task-row {
  background: linear-gradient(to right, rgba(24, 144, 255, 0.2), rgba(24, 144, 255, 0.05));
  border-left: 3px solid #1890ff !important;
}

.task-row.status-preparing {
  border-left: 3px solid #bdbdbd;
}

.task-row.status-running {
  border-left: 3px solid #fbbc05;
}

.task-row.status-completed {
  border-left: 3px solid #34a853;
  background-color: #f8f9fa;
}

.task-row.status-error {
  border-left: 3px solid #ea4335;
}

.task-row.status-released {
  border-left: 3px solid #5f6368;
  background-color: #f8f9fa;
}

.task-row.status-stopped {
  border-left: 3px solid #4285f4;
  background-color: #f8f9fa;
}

.task-row.status-batch-waiting {
  border-left: 3px solid #ff9800;
  background-color: #fff3e0;
}

.task-id {
  font-family: monospace;
  font-size: 11px;
  color: #333;
  font-weight: 500;
  text-align: left;
}

.subject-cell {
  min-width: 100px;
  max-width: 300px;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  color: #333;
  line-height: 1.4;
  padding: 4px 8px;
  text-align: left;
}

.status-cell {
  display: flex;
  min-width: 100px;
  align-items: center;
  justify-content: center;
  gap: 4px;
  flex-wrap: wrap;
}

.task-actions {
  min-width: 100px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.terminate-btn {
  background-color: #ea4335;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.terminate-btn:hover {
  background-color: #d33426;
}

.release-btn {
  background-color: #000000;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.release-btn:hover {
  background-color: #827e70;
}

.delete-btn {
  background-color: transparent;
  color: #888;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  margin-left: 5px;
}

.delete-btn:hover {
  background-color: #f0f0f0;
  color: #ea4335;
}

.delete-icon {
  display: inline-block;
  line-height: 1;
}

.empty-queue {
  padding: 20px;
  text-align: center;
  color: #999;
}

.empty-text {
  font-size: 13px;
}

.toggle-icon {
  margin-left: 5px;
  font-size: 12px;
}

.log-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  margin-top: 16px;
  border: 1px solid #e0e0e0;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f1f3f4;
  padding: 10px 16px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.log-header:hover {
  background-color: #e6e8ea;
}

.log-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.log-actions {
  display: flex;
  align-items: center;
}

.clear-btn {
  background-color: transparent;
  border: none;
  color: #4285f4;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 10px;
  border-radius: 4px;
  transition: all 0.2s;
}

.clear-btn:hover {
  background-color: rgba(66, 133, 244, 0.1);
  color: #3367d6;
}

.log-content {
  padding: 12px 16px;
  font-size: 13px;
  color: #555;
  max-height: 350px;
  overflow-y: auto;
  line-height: 1.6;
  background-color: #fafafa;
  font-family: 'Consolas', 'Monaco', monospace;
}

.log-item {
  display: inline-block;
  margin-bottom: 6px;
  line-height: 1.6;
  padding: 2px 0;
  font-weight: 500;
  border-radius: 3px;
}

.loading-icon {
  display: inline-block;
  animation: spin 1s infinite linear;
  margin-right: 4px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.doc-header.status-running {
  background: linear-gradient(135deg, #fbbc05, #ffd466);
}

.doc-header.status-completed {
  background: linear-gradient(135deg, #34a853, #4fc571);
}

.doc-header.status-error {
  background: linear-gradient(135deg, #ea4335, #ff7167);
}

.status-badge {
  display: inline-block;
  margin-left: 10px;
  font-size: 12px;
}

.badge {
  padding: 1px 6px;
  border-radius: 10px;
  margin-right: 5px;
  color: white;
  font-weight: normal;
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.modal-title {
  font-weight: bold;
  font-size: 14px;
  color: #333;
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  line-height: 1;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.selection-content {
  white-space: pre-wrap;
  word-break: break-word;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  max-height: 50vh;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.alert-modal {
  max-width: 400px;
}

.alert-message {
  text-align: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.alert-actions {
  display: flex;
  justify-content: center;
}

.task-tag {
  margin-left: 10px;
  display: inline-block;
  padding: 2px 8px;
  border-radius: 5px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  background-color: #fbbc05;
}

.task-tag.status-preparing {
  background-color: #bdbdbd;
}

.task-tag.status-running {
  background-color: #fbbc05;
}

.task-tag.status-completed {
  background-color: #34a853;
}

.task-tag.status-error {
  background-color: #ea4335;
}

.task-tag.status-released {
  background-color: #5f6368;
}

.task-tag.status-stopped {
  background-color: #4285f4;
}

.task-tag.status-batch-waiting {
  background-color: #ff9800;
}

.enhance-tag {
  background-color: #9c27b0;
  color: white;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  display: inline-block;
}

.enhance-id-tag {
  background-color: #9c27b0;
  color: white;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  margin-left: 5px;
  display: inline-block;
}



.soft-break-warning-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  cursor: help;
}

.soft-break-warning-icon svg {
  filter: drop-shadow(0 0 1px rgba(255, 152, 0, 0.3));
  transition: all 0.2s ease;
}

.soft-break-warning-icon:hover svg {
  stroke: #f57c00;
  filter: drop-shadow(0 0 2px rgba(255, 152, 0, 0.5));
}

.id-cell {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 12px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.format-error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(2px);
}

.format-error-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 500px;
  width: 90%;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 2px solid #ff9800;
}

.format-error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.format-error-title {
  font-size: 20px;
  font-weight: bold;
  color: #d32f2f;
  margin-bottom: 16px;
}

.format-error-message {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 24px;
  white-space: pre-line;
  text-align: left;
  background-color: #fff3e0;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.format-error-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.retry-check-btn {
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-check-btn:hover {
  background-color: #f57c00;
  transform: translateY(-1px);
}

.science-warning {
  background-color: #fff3e0;
  color: #e65100;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 13px;
  text-align: center;
  border: 1px solid #ffe0b2;
}

.action-btn.primary:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.queue-filter {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #555;
  margin-left: 10px;
}

.queue-filter input[type='checkbox'] {
  margin-right: 5px;
  cursor: pointer;
}

.queue-filter label {
  cursor: pointer;
}

.queue-status-filter {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #555;
  margin-left: 10px;
}

.status-filter-select-input {
  padding: 4px 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
  background-color: white;
}

.confirm-modal {
  max-width: 400px;
}

.confirm-message {
  text-align: center;
  margin: 15px 0;
  font-size: 14px;
  white-space: pre-line;
}

.confirm-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.confirm-actions .action-btn {
  min-width: 80px;
  padding: 8px 15px;
}

.confirm-actions .action-btn.secondary {
  background-color: #e0e0e0;
  color: #333;
}

.confirm-actions .action-btn.secondary:hover {
  background-color: #d0d0d0;
}



/* Tippy.js 相关样式已通过 JS 动态添加 */
</style>

<style>
.log-item.success {
  color: #0d904f;
}

.log-item.error {
  color: #d93025;
}

.log-item.info {
  color: #1a73e8;
}

.log-item.warning {
  color: #e37400;
}

.log-item.progress {
  color: #e37400;
}
</style>

<style scoped>
.subject-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 28px;
  height: 16px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 12px;
  width: 12px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
}

input:checked + .slider {
  background-color: #4285f4;
}

input:focus + .slider {
  box-shadow: 0 0 1px #4285f4;
}

input:checked + .slider:before {
  transform: translateX(12px);
}

.slider.round {
  border-radius: 16px;
}

.slider.round:before {
  border-radius: 50%;
}

.no-action-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  opacity: 0.6;
  cursor: help;
}

.no-action-icon:hover {
  opacity: 0.8;
}

.no-action-icon svg {
  transition: all 0.2s ease;
}

/* Styles for narrow view table */
.queue-table.narrow-view .col-id,
.queue-table.narrow-view .col-status,
.queue-table.narrow-view .col-actions {
  width: auto; /* Allow columns to adapt */
}

.queue-table.narrow-view .col-id {
  min-width: 70px; /* Adjusted for ID + icon */
}
.queue-table.narrow-view .col-status {
  min-width: 70px; /* Adjusted for status text */
}
.queue-table.narrow-view .col-actions {
  min-width: 60px; /* Adjusted for action buttons */
}

/* General cell adjustments for the narrow view table */
.queue-table.narrow-view th,
.queue-table.narrow-view td {
  padding: 8px 5px; /* Reduced padding */
  font-size: 11px; /* Reduced font size */
}

/* Styles for ultra narrow view table (< 380px) */
.queue-table.ultra-narrow-view .col-id {
  min-width: 120px; /* Increased width to accommodate ID + status */
  width: 60%; /* Take up most of the space */
}

.queue-table.ultra-narrow-view .col-actions {
  min-width: 70px; /* Increased action column width */
  width: 40%; /* Remaining space for actions */
}

.queue-table.ultra-narrow-view th,
.queue-table.ultra-narrow-view td {
  padding: 6px 4px; /* Further reduced padding */
  font-size: 11px;
}

/* ID cell with status layout */
.id-cell.id-with-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  padding: 6px 8px;
}

.id-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-in-id {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.task-tag.compact {
  padding: 1px 6px;
  font-size: 10px;
  border-radius: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Ultra narrow view task ID styling */
.queue-table.ultra-narrow-view .task-id {
  font-size: 10px;
  font-weight: 600;
}

.queue-table.ultra-narrow-view .soft-break-warning-icon svg {
  width: 12px;
  height: 12px;
}

.queue-table.ultra-narrow-view .analysis-task-icon,
.queue-table.ultra-narrow-view .enhance-task-icon,
.queue-table.ultra-narrow-view .check-task-icon {
  width: 14px;
  height: 14px;
  font-size: 9px;
  margin-left: 4px;
}

/* Enhanced visual separation for ultra narrow view */
.queue-table.ultra-narrow-view .task-row {
  border-bottom: 2px solid #f0f0f0;
}

.queue-table.ultra-narrow-view .task-row:last-child {
  border-bottom: 1px solid #f0f0f0;
}

/* Adjust action buttons for ultra narrow view */
.queue-table.ultra-narrow-view .task-actions {
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.queue-table.ultra-narrow-view .terminate-btn,
.queue-table.ultra-narrow-view .release-btn {
  padding: 3px 6px;
  font-size: 10px;
  min-width: 45px;
  border-radius: 4px;
}

.queue-table.ultra-narrow-view .no-action-icon {
  padding: 2px;
}

.queue-table.ultra-narrow-view .no-action-icon svg {
  width: 14px;
  height: 14px;
}

.analysis-task-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  cursor: help;
  background-color: #4285f4;
  color: white;
  font-size: 10px;
  font-weight: 600;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(66, 133, 244, 0.3);
  transition: all 0.2s ease;
}

.analysis-task-icon:hover {
  background-color: #3b78e7;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(66, 133, 244, 0.4);
}

.enhance-task-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  cursor: help;
  background-color: #9c27b0;
  color: white;
  font-size: 10px;
  font-weight: 600;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(156, 39, 176, 0.3);
  transition: all 0.2s ease;
}

.enhance-task-icon:hover {
  background-color: #7b1fa2;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(156, 39, 176, 0.4);
}

.check-task-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  cursor: help;
  background-color: #4caf50;
  color: white;
  font-size: 10px;
  font-weight: 600;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(76, 175, 80, 0.3);
  transition: all 0.2s ease;
}

.check-task-icon:hover {
  background-color: #43a047;
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(76, 175, 80, 0.4);
}

/* 折叠组相关样式 */
.group-row {
  cursor: pointer;
  background-color: #f8f9fa;
  border-left: 3px solid #9e9e9e;
  transition: background-color 0.2s ease;
}

.group-row:hover {
  background-color: #e9ecef;
}

.group-cell {
  display: flex;
  align-items: center;
  padding-left: 12px;
}

.group-toggle-icon {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
  transition: transform 0.2s ease;
  font-family: monospace;
}

.group-label {
  font-size: 12px;
  font-weight: 600;
  color: #555;
}

.group-subject {
  font-size: 12px;
  color: #777;
  font-style: italic;
}

.group-action-text {
  font-size: 11px;
  color: #666;
  padding: 4px 8px;
  border-radius: 3px;
  background-color: #e9ecef;
  transition: background-color 0.2s ease;
}

.group-row:hover .group-action-text {
  background-color: #dee2e6;
}

.group-task-row {
  background-color: #fafafa;
  border-left: 3px solid #e0e0e0;
}

.group-task-row:hover {
  background-color: #f0f4f8;
}

.group-indent {
  font-family: monospace;
  color: #999;
  margin-right: 6px;
  font-size: 12px;
}

/* 响应式调整 */
.queue-table.narrow-view .group-label {
  font-size: 11px;
}

.queue-table.ultra-narrow-view .group-toggle-icon {
  font-size: 10px;
  margin-right: 4px;
}

.queue-table.ultra-narrow-view .group-label {
  font-size: 10px;
}

.queue-table.ultra-narrow-view .group-indent {
  font-size: 10px;
  margin-right: 4px;
}

.collapse-toggle-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  margin-left: 8px;
}

.collapse-toggle-btn:hover {
  background-color: #5a6268;
}



/* 批量模式相关样式 */

/* 按钮组样式 */
.btn-group {
  display: flex;
  align-items: stretch;
  position: relative;
  height: auto;
}

.main-btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  min-width: 90px;
  flex: 1;
}

.dropdown-trigger {
  position: relative;
  display: flex;
  align-items: stretch;
}

.dropdown-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  min-width: 40px;
  padding: 10px 12px;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.2s ease;
  line-height: 1;
}

.dropdown-trigger:hover .dropdown-arrow {
  transform: translateY(1px);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 140px;
  margin-top: 4px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.dropdown-item:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.dropdown-item:disabled {
  color: #999;
  cursor: not-allowed;
}

/* 批量模式蒙层 */
.batch-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.batch-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: linear-gradient(135deg, #4285f4, #3b78e7);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.batch-title {
  font-size: 18px;
  font-weight: 600;
}

.batch-task-count {
  font-size: 14px;
  opacity: 0.9;
}

.batch-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.batch-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.batch-content {
  flex: 1;
  padding: 20px;
  background-color: #f8f9fa;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 批量操作按钮 */
.batch-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
}

.batch-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
  justify-content: center;
}

.batch-action-btn.add-btn {
  background-color: #34a853;
  color: white;
}

.batch-action-btn.add-btn:hover:not(:disabled) {
  background-color: #2d9346;
  transform: translateY(-1px);
}

.batch-action-btn.start-btn {
  background-color: #4285f4;
  color: white;
}

.batch-action-btn.start-btn:hover:not(:disabled) {
  background-color: #3b78e7;
  transform: translateY(-1px);
}

.batch-action-btn:disabled {
  background-color: #e0e0e0;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

.btn-icon {
  font-size: 16px;
  font-weight: bold;
}

/* 批量任务列表 */
.batch-task-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.batch-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f1f3f4;
  border-bottom: 1px solid #e0e0e0;
}

.batch-list-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.batch-clear-btn {
  background-color: #ea4335;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.batch-clear-btn:hover {
  background-color: #d33426;
}

.batch-task-items {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.batch-task-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.batch-task-item:hover {
  background-color: #f8f9fa;
}

.batch-task-item.selected {
  background-color: #e3f2fd;
  border-left: 4px solid #4285f4;
}

.batch-task-item:last-child {
  border-bottom: none;
}

.task-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.task-item-index {
  background-color: #4285f4;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.task-item-id {
  font-family: monospace;
  font-size: 13px;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.task-item-type {
  margin-left: auto;
}

.task-type-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
  color: white;
}

.task-type-tag.normal {
  background-color: #4285f4;
}

.task-type-tag.enhance {
  background-color: #9c27b0;
}

.task-type-tag.check {
  background-color: #34a853;
}

.task-item-remove {
  background: none;
  border: none;
  color: #ea4335;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.task-item-remove:hover {
  background-color: rgba(234, 67, 53, 0.1);
}

.task-item-content {
  color: #555;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 6px;
  word-break: break-word;
}

.task-item-time {
  color: #999;
  font-size: 12px;
}

/* 空状态 */
.batch-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  max-width: 300px;
}

/* 提高触摸体验 */
@media (hover: none) and (pointer: coarse) {
  .dropdown-btn {
    min-width: 50px;
    padding: 12px 15px;
  }

  .dropdown-arrow {
    font-size: 14px;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-buttons {
    gap: 12px;
  }

  .dropdown-btn {
    min-width: 45px;
    padding: 10px 14px;
  }
}

@media (max-width: 480px) {
  .batch-header {
    padding: 12px 15px;
  }

  .batch-title {
    font-size: 16px;
  }

  .batch-task-count {
    font-size: 13px;
  }

  .batch-content {
    padding: 15px;
    gap: 15px;
  }

  .batch-actions {
    flex-direction: column;
    gap: 10px;
  }

  .batch-action-btn {
    width: 100%;
    min-width: unset;
  }

  .batch-list-header {
    padding: 12px 15px;
  }

  .batch-task-item {
    padding: 12px 15px;
  }

  .task-item-header {
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .btn-group {
    width: 100%;
  }

  .main-btn {
    min-width: unset;
  }

  .dropdown-btn {
    min-width: 50px;
  }
}
</style>
