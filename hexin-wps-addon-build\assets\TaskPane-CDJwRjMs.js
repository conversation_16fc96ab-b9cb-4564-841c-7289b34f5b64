import{U as on,r as fe,h as vt,v as Ke,i as Q,j as Rt,k as Ls,m as Jt,_ as js,n as ln,o as L,c as j,a as c,p as cn,t as ae,f as z,q as $e,w as Ye,s as Vt,e as Zt,F as mt,u as _t,x as ct,y as un,z as de,A as Qt,B as vs,C as ht,D as dn,E as pn}from"./index-BPovJarb.js";function fn(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=on.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const hn={onbuttonclick:fn};var gn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function vn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function mn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var l=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,l.get?l:{enumerable:!0,get:function(){return e[a]}})}),t}var Ws={exports:{}};const bn={},wn=Object.freeze(Object.defineProperty({__proto__:null,default:bn},Symbol.toStringTag,{value:"Module"})),ms=mn(wn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",l=a?window:{};l.JS_SHA1_NO_WINDOW&&(a=!1);var v=!a&&typeof self=="object",T=!l.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;T?l=gn:v&&(l=self);var D=!l.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,w=!l.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",C="0123456789abcdef".split(""),O=[-**********,8388608,32768,128],F=[24,16,8,0],V=["hex","array","digest","arrayBuffer"],I=[],B=Array.isArray;(l.JS_SHA1_NO_NODE_JS||!B)&&(B=function(g){return Object.prototype.toString.call(g)==="[object Array]"});var Y=ArrayBuffer.isView;w&&(l.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!Y)&&(Y=function(g){return typeof g=="object"&&g.buffer&&g.buffer.constructor===ArrayBuffer});var N=function(g){var b=typeof g;if(b==="string")return[g,!0];if(b!=="object"||g===null)throw new Error(s);if(w&&g.constructor===ArrayBuffer)return[new Uint8Array(g),!1];if(!B(g)&&!Y(g))throw new Error(s);return[g,!1]},se=function(g){return function(b){return new P(!0).update(b)[g]()}},ne=function(){var g=se("hex");T&&(g=oe(g)),g.create=function(){return new P},g.update=function(k){return g.create().update(k)};for(var b=0;b<V.length;++b){var x=V[b];g[x]=se(x)}return g},oe=function(g){var b=ms,x=ms.Buffer,k;x.from&&!l.JS_SHA1_NO_BUFFER_FROM?k=x.from:k=function(m){return new x(m)};var S=function(m){if(typeof m=="string")return b.createHash("sha1").update(m,"utf8").digest("hex");if(m==null)throw new Error(s);return m.constructor===ArrayBuffer&&(m=new Uint8Array(m)),B(m)||Y(m)||m.constructor===x?b.createHash("sha1").update(k(m)).digest("hex"):g(m)};return S},d=function(g){return function(b,x){return new le(b,!0).update(x)[g]()}},ee=function(){var g=d("hex");g.create=function(k){return new le(k)},g.update=function(k,S){return g.create(k).update(S)};for(var b=0;b<V.length;++b){var x=V[b];g[x]=d(x)}return g};function P(g){g?(I[0]=I[16]=I[1]=I[2]=I[3]=I[4]=I[5]=I[6]=I[7]=I[8]=I[9]=I[10]=I[11]=I[12]=I[13]=I[14]=I[15]=0,this.blocks=I):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}P.prototype.update=function(g){if(this.finalized)throw new Error(t);var b=N(g);g=b[0];for(var x=b[1],k,S=0,m,U=g.length||0,M=this.blocks;S<U;){if(this.hashed&&(this.hashed=!1,M[0]=this.block,this.block=M[16]=M[1]=M[2]=M[3]=M[4]=M[5]=M[6]=M[7]=M[8]=M[9]=M[10]=M[11]=M[12]=M[13]=M[14]=M[15]=0),x)for(m=this.start;S<U&&m<64;++S)k=g.charCodeAt(S),k<128?M[m>>>2]|=k<<F[m++&3]:k<2048?(M[m>>>2]|=(192|k>>>6)<<F[m++&3],M[m>>>2]|=(128|k&63)<<F[m++&3]):k<55296||k>=57344?(M[m>>>2]|=(224|k>>>12)<<F[m++&3],M[m>>>2]|=(128|k>>>6&63)<<F[m++&3],M[m>>>2]|=(128|k&63)<<F[m++&3]):(k=65536+((k&1023)<<10|g.charCodeAt(++S)&1023),M[m>>>2]|=(240|k>>>18)<<F[m++&3],M[m>>>2]|=(128|k>>>12&63)<<F[m++&3],M[m>>>2]|=(128|k>>>6&63)<<F[m++&3],M[m>>>2]|=(128|k&63)<<F[m++&3]);else for(m=this.start;S<U&&m<64;++S)M[m>>>2]|=g[S]<<F[m++&3];this.lastByteIndex=m,this.bytes+=m-this.start,m>=64?(this.block=M[16],this.start=m-64,this.hash(),this.hashed=!0):this.start=m}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},P.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var g=this.blocks,b=this.lastByteIndex;g[16]=this.block,g[b>>>2]|=O[b&3],this.block=g[16],b>=56&&(this.hashed||this.hash(),g[0]=this.block,g[16]=g[1]=g[2]=g[3]=g[4]=g[5]=g[6]=g[7]=g[8]=g[9]=g[10]=g[11]=g[12]=g[13]=g[14]=g[15]=0),g[14]=this.hBytes<<3|this.bytes>>>29,g[15]=this.bytes<<3,this.hash()}},P.prototype.hash=function(){var g=this.h0,b=this.h1,x=this.h2,k=this.h3,S=this.h4,m,U,M,K=this.blocks;for(U=16;U<80;++U)M=K[U-3]^K[U-8]^K[U-14]^K[U-16],K[U]=M<<1|M>>>31;for(U=0;U<20;U+=5)m=b&x|~b&k,M=g<<5|g>>>27,S=M+m+S+1518500249+K[U]<<0,b=b<<30|b>>>2,m=g&b|~g&x,M=S<<5|S>>>27,k=M+m+k+1518500249+K[U+1]<<0,g=g<<30|g>>>2,m=S&g|~S&b,M=k<<5|k>>>27,x=M+m+x+1518500249+K[U+2]<<0,S=S<<30|S>>>2,m=k&S|~k&g,M=x<<5|x>>>27,b=M+m+b+1518500249+K[U+3]<<0,k=k<<30|k>>>2,m=x&k|~x&S,M=b<<5|b>>>27,g=M+m+g+1518500249+K[U+4]<<0,x=x<<30|x>>>2;for(;U<40;U+=5)m=b^x^k,M=g<<5|g>>>27,S=M+m+S+1859775393+K[U]<<0,b=b<<30|b>>>2,m=g^b^x,M=S<<5|S>>>27,k=M+m+k+1859775393+K[U+1]<<0,g=g<<30|g>>>2,m=S^g^b,M=k<<5|k>>>27,x=M+m+x+1859775393+K[U+2]<<0,S=S<<30|S>>>2,m=k^S^g,M=x<<5|x>>>27,b=M+m+b+1859775393+K[U+3]<<0,k=k<<30|k>>>2,m=x^k^S,M=b<<5|b>>>27,g=M+m+g+1859775393+K[U+4]<<0,x=x<<30|x>>>2;for(;U<60;U+=5)m=b&x|b&k|x&k,M=g<<5|g>>>27,S=M+m+S-1894007588+K[U]<<0,b=b<<30|b>>>2,m=g&b|g&x|b&x,M=S<<5|S>>>27,k=M+m+k-1894007588+K[U+1]<<0,g=g<<30|g>>>2,m=S&g|S&b|g&b,M=k<<5|k>>>27,x=M+m+x-1894007588+K[U+2]<<0,S=S<<30|S>>>2,m=k&S|k&g|S&g,M=x<<5|x>>>27,b=M+m+b-1894007588+K[U+3]<<0,k=k<<30|k>>>2,m=x&k|x&S|k&S,M=b<<5|b>>>27,g=M+m+g-1894007588+K[U+4]<<0,x=x<<30|x>>>2;for(;U<80;U+=5)m=b^x^k,M=g<<5|g>>>27,S=M+m+S-899497514+K[U]<<0,b=b<<30|b>>>2,m=g^b^x,M=S<<5|S>>>27,k=M+m+k-899497514+K[U+1]<<0,g=g<<30|g>>>2,m=S^g^b,M=k<<5|k>>>27,x=M+m+x-899497514+K[U+2]<<0,S=S<<30|S>>>2,m=k^S^g,M=x<<5|x>>>27,b=M+m+b-899497514+K[U+3]<<0,k=k<<30|k>>>2,m=x^k^S,M=b<<5|b>>>27,g=M+m+g-899497514+K[U+4]<<0,x=x<<30|x>>>2;this.h0=this.h0+g<<0,this.h1=this.h1+b<<0,this.h2=this.h2+x<<0,this.h3=this.h3+k<<0,this.h4=this.h4+S<<0},P.prototype.hex=function(){this.finalize();var g=this.h0,b=this.h1,x=this.h2,k=this.h3,S=this.h4;return C[g>>>28&15]+C[g>>>24&15]+C[g>>>20&15]+C[g>>>16&15]+C[g>>>12&15]+C[g>>>8&15]+C[g>>>4&15]+C[g&15]+C[b>>>28&15]+C[b>>>24&15]+C[b>>>20&15]+C[b>>>16&15]+C[b>>>12&15]+C[b>>>8&15]+C[b>>>4&15]+C[b&15]+C[x>>>28&15]+C[x>>>24&15]+C[x>>>20&15]+C[x>>>16&15]+C[x>>>12&15]+C[x>>>8&15]+C[x>>>4&15]+C[x&15]+C[k>>>28&15]+C[k>>>24&15]+C[k>>>20&15]+C[k>>>16&15]+C[k>>>12&15]+C[k>>>8&15]+C[k>>>4&15]+C[k&15]+C[S>>>28&15]+C[S>>>24&15]+C[S>>>20&15]+C[S>>>16&15]+C[S>>>12&15]+C[S>>>8&15]+C[S>>>4&15]+C[S&15]},P.prototype.toString=P.prototype.hex,P.prototype.digest=function(){this.finalize();var g=this.h0,b=this.h1,x=this.h2,k=this.h3,S=this.h4;return[g>>>24&255,g>>>16&255,g>>>8&255,g&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,x>>>24&255,x>>>16&255,x>>>8&255,x&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,S>>>24&255,S>>>16&255,S>>>8&255,S&255]},P.prototype.array=P.prototype.digest,P.prototype.arrayBuffer=function(){this.finalize();var g=new ArrayBuffer(20),b=new DataView(g);return b.setUint32(0,this.h0),b.setUint32(4,this.h1),b.setUint32(8,this.h2),b.setUint32(12,this.h3),b.setUint32(16,this.h4),g};function le(g,b){var x,k=N(g);if(g=k[0],k[1]){var S=[],m=g.length,U=0,M;for(x=0;x<m;++x)M=g.charCodeAt(x),M<128?S[U++]=M:M<2048?(S[U++]=192|M>>>6,S[U++]=128|M&63):M<55296||M>=57344?(S[U++]=224|M>>>12,S[U++]=128|M>>>6&63,S[U++]=128|M&63):(M=65536+((M&1023)<<10|g.charCodeAt(++x)&1023),S[U++]=240|M>>>18,S[U++]=128|M>>>12&63,S[U++]=128|M>>>6&63,S[U++]=128|M&63);g=S}g.length>64&&(g=new P(!0).update(g).array());var K=[],ge=[];for(x=0;x<64;++x){var me=g[x]||0;K[x]=92^me,ge[x]=54^me}P.call(this,b),this.update(ge),this.oKeyPad=K,this.inner=!0,this.sharedMemory=b}le.prototype=new P,le.prototype.finalize=function(){if(P.prototype.finalize.call(this),this.inner){this.inner=!1;var g=this.array();P.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(g),P.prototype.finalize.call(this)}};var pe=ne();pe.sha1=pe,pe.sha1.hmac=ee(),D?e.exports=pe:l.sha1=pe})()})(Ws);var yn=Ws.exports;const xn=vn(yn);function bs(){return"http://worksheet.hexinedu.com"}function $t(){return"http://127.0.0.1:3000"}function ws(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const St=async(e,s,t,a={},l=8e3)=>{try{return await Promise.race([e(),new Promise((v,T)=>setTimeout(()=>T(new Error("WebSocket请求超时，切换到HTTP")),l))])}catch{try{let T;return s==="get"?T=await Rt.get(t,{params:a}):s==="post"?T=await Rt.post(t,a):s==="delete"&&(T=await Rt.delete(t)),T.data}catch(T){throw new Error(`请求失败: ${T.message||"未知错误"}`)}}};function kn(e,s,t,a){const l=[e,s,t,a].join(":");return xn(l)}function Tn(){const e=fe(""),s=fe(""),t=fe(""),a=vt({}),l=fe(""),v=fe("");let T="",D=null;const w=fe("c:\\Temp"),C=vt({appKey:"",appSecret:""}),O=vt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),F=vt({show:!1,title:"",message:"",type:"error"}),V=fe(""),I=fe("junior"),B=fe(null),Y=fe(!1),N=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],se=()=>Ke.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],ne=vt(se()),oe=async()=>{try{const n=await Q.getWatcherStatus();n.data&&n.data.watchDir&&(w.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${w.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},d=async()=>{var n,i,r;try{if(!C.appKey||!C.appSecret)throw new Error("未初始化app信息");const u=60,o=Date.now();let f;try{const q=window.Application.PluginStorage.getItem("token_info");q&&(f=JSON.parse(q))}catch(q){f=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${q.message}</span><br/>`}if(f&&f.access_token&&f.expired_time>o+u*1e3)return f.access_token;const p=C.appKey,$="1234567",_=Math.floor(Date.now()/1e3),E=kn(p,$,C.appSecret,_),R=await Rt.get(bs()+"/api/open/account/v1/auth/token",{params:{app_key:p,app_nonstr:$,app_timestamp:_,app_signature:E}});if((i=(n=R.data)==null?void 0:n.data)!=null&&i.access_token){const q=R.data.data.access_token;let Z;if(R.data.data.expired_time){const he=parseInt(R.data.data.expired_time);Z=o+he*1e3,t.value+=`<span class="log-item info">token已更新，有效期${he}秒</span><br/>`}else Z=o+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const te={access_token:q,expired_time:Z};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(te))}catch(he){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${he.message}</span><br/>`}return q}else throw new Error(((r=R.data)==null?void 0:r.message)||"获取access_token失败")}catch(u){throw t.value+=`<span class="log-item error">获取access_token失败: ${u.message}</span><br/>`,u}},ee=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},P=()=>{const n=window.Application,i=n.Documents.Count;for(let r=1;r<=i;r++){const u=n.Documents.Item(r);if(u.DocID===l.value)return u}return null},le=()=>{try{const n=P();if(!n)return{isValid:!1,message:"未找到当前文档"};let i="";try{i=n.Name||""}catch{try{i=m("getDocName")||""}catch{i=""}}if(i){const r=i.toLowerCase();return r.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:r.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},pe=(n,i="",r=0,u=null)=>{try{const o=window.Application,f=P();let p;if(u)p=u;else{const $=f.ActiveWindow.Selection;if(!$||$.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;p=$.Range}if(i){const $=p.Text,_=p.Find;_.ClearFormatting(),_.Text=i,_.Forward=!0,_.Wrap=0;let E=0,R=[];const q=p.Start,Z=p.End;for(;_.Execute()&&(_.Found&&_.Parent.Start>=q&&_.Parent.End<=Z);){const te=_.Parent.Start,he=_.Parent.End;if(R.some(ye=>te===ye.start&&he===ye.end)){const ye=Math.min(he,Z);if(ye>=Z)break;_.Parent.SetRange(ye,Z)}else{if(R.push({start:te,end:he}),r===-1||E===r){const ce=f.Comments.Add(_.Parent,n);try{ce&&ce.Range&&ce.Range.ParagraphFormat&&(ce.Range.ParagraphFormat.Reset(),ce.Range.ParagraphFormat.LineSpacingRule=3,ce.Range.ParagraphFormat.LineSpacing=10)}catch(Je){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${Je.message}</span><br/>`}if(r!==-1&&E===r)return!0}E++;const ye=Math.min(he,Z);if(console.log("nextStart",ye),ye>=Z)break;const Ce=f.Range(ye,Z);_.Parent.SetRange(ye,Z)}}return r!==-1&&E<=r?!1:r===-1&&E>0?!0:!(r===-1&&E===0)}else{const $=f.Comments.Add(p,n);try{$&&$.Range&&$.Range.ParagraphFormat&&($.Range.ParagraphFormat.Reset(),$.Range.ParagraphFormat.LineSpacingRule=3,$.Range.ParagraphFormat.LineSpacing=10)}catch(_){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${_.message}</span><br/>`}return t.value+=`<span class="log-item success">已为${u?"指定范围":"选中内容"}添加批注: "${n}"</span><br/>`,!0}}catch(o){return t.value+=`<span class="log-item error">添加批注失败: ${o.message}</span><br/>`,!1}},g=n=>n===0?"status-preparing":n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",b=n=>n===0?"准备中":n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"准备中",x=n=>{const i=Date.now()-n,r=Math.floor(i/1e3);return r<60?`${r}秒`:r<3600?`${Math.floor(r/60)}分${r%60}秒`:`${Math.floor(r/3600)}时${Math.floor(r%3600/60)}分`},k=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const r=P();if(r&&r.ContentControls)for(let u=1;u<=r.ContentControls.Count;u++)try{const o=r.ContentControls.Item(u);if(o&&o.Title&&(o.Title===`任务_${n}`||o.Title===`任务增强_${n}`||o.Title===`校对_${n}`)){const f=o.Title===`任务增强_${n}`||a[n].isEnhanced,p=o.Title===`校对_${n}`||a[n].isCheckTask;p?o.Title=`已停止校对_${n}`:f?o.Title=`已停止增强_${n}`:o.Title=`已停止_${n}`;const $=p?"校对":f?"增强":"普通";t.value+=`<span class="log-item info">已将${$}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(r){t.value+=`<span class="log-item warning">更新控件标题失败: ${r.message}</span><br/>`}let i=null;if(X[n]&&X[n].urlId){i=X[n].urlId;try{try{const r=await St(async()=>await Q.getUrlMonitorStatus(),"get",`${$t()}/api/url/status`),o=(Array.isArray(r)?r:r.data?Array.isArray(r.data)?r.data:[]:[]).find(f=>f.urlId===i);o&&o.downloadedPath&&(a[n].resultFile=o.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${o.downloadedPath}</span><br/>`)}catch(r){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${r.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await xe(i),delete X[n]}catch(r){t.value+=`<span class="log-item warning">停止URL监控出错: ${r.message}，将重试</span><br/>`,delete X[n],setTimeout(async()=>{try{i&&await St(async()=>await Q.stopUrlMonitoring(i),"delete",`${$t()}/api/url/monitor/${i}`)}catch(u){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${u.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(i){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${i.message}</span><br/>`,X[n]&&delete X[n]}},S=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const i=P();if(i&&i.ContentControls)for(let u=1;u<=i.ContentControls.Count;u++)try{const o=i.ContentControls.Item(u);if(o&&o.Title&&(o.Title===`任务_${n}`||o.Title===`任务增强_${n}`||o.Title===`校对_${n}`)){const f=o.Title===`任务增强_${n}`||a[n].isEnhanced,p=o.Title===`校对_${n}`||a[n].isCheckTask;p?o.Title=`已停止校对_${n}`:f?o.Title=`已停止增强_${n}`:o.Title=`已停止_${n}`,o.LockContents=!1;const $=p?"校对":f?"增强":"普通";t.value+=`<span class="log-item info">已将${$}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let r=null;if(X[n]&&X[n].urlId){r=X[n].urlId;try{const u=await Q.getUrlMonitorStatus(),f=(Array.isArray(u)?u:u.data?Array.isArray(u.data)?u.data:[]:[]).find(p=>p.urlId===r);f&&f.downloadedPath&&(a[n].resultFile=f.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${f.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await xe(r),delete X[n]}catch(u){t.value+=`<span class="log-item warning">停止URL监控出错: ${u.message}，将重试</span><br/>`,delete X[n]}}},m=n=>hn.onbuttonclick(n),U=()=>{try{e.value=m("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},M=()=>{P().ActiveWindow.Selection.Copy()},K=n=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),i.Close(),P().ActiveWindow.Activate()},ge=n=>{const i=window.Application.Documents.Add("",!1,0,!1);i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),i.Close(),P().ActiveWindow.Activate()},me=n=>{try{const r=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,u=window.Application.Documents.Add("",!1,0,!1);u.Content.Paste();const o=`${r}\\${n}`;u.SaveAs2(o,12,"","",!1),u.Close(),P().ActiveWindow.Activate(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${o}.docx</span><br/>`}catch(i){throw t.value+=`<span class="log-item error">方式三保存失败: ${i.message}</span><br/>`,i}},Ee=n=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),P().ActiveWindow.Activate()},Ue=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let i="method2";try{const r=await Q.getSaveMethod();if(r.success&&r.saveMethod){i=r.saveMethod;const u=i==="method1"?"方式一":i==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${u}</span><br/>`}}catch(r){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${r.message}</span><br/>`}i==="method1"?(K(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):i==="method2"?(ge(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):i==="method3"?(me(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):i==="method4"&&(Ee(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${w.value}\\${n}.docx</span><br/>`),Q.associateFileWithClient(`${n}.docx`).then(r=>{r.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${r.message||"未知错误"}</span><br/>`}).catch(r=>{t.value+=`<span class="log-item warning">关联文件时出错: ${r.message}</span><br/>`})}catch(i){t.value+=`<span class="log-item error">保存文件失败: ${i.message}</span><br/>`}},Ae=fe(null),Oe=vt([]),ke=new Set,Se=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),Me=async n=>{try{const i=n.slice(T.length);if(i.trim()){const r=Q.getClientId();if(!r){console.warn("无法获取客户端ID，跳过日志同步");return}const u=Se(i);if(!u.trim()){T=n;return}await Q.sendRequest("logger","syncLog",{content:u,timestamp:new Date().toISOString(),clientId:r}),T=n}}catch(i){console.error("同步日志到服务端失败:",i)}},Fe=()=>{Q.connect().then(()=>{oe()}).catch(i=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${i.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const i=[];for(const r in a)if(a.hasOwnProperty(r)){const u=a[r];(u.status===0||u.status===1)&&!u.terminated&&(i.includes(r)||i.push(r))}if(i.length>0){let r=!1;try{const u=P();if(u){const f=`taskpane_id_${u.DocID}`,p=window.Application.PluginStorage.getItem(f);if(p){const $=window.Application.GetTaskPane(p);$&&(r=$.Visible)}}}catch(u){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${u.message}</span><br/>`,r=!0}setTimeout(()=>{if(r){const u=`检测到 ${i.length} 个未完成的任务，是否继续？`;re(u).then(o=>{o?(t.value+=`<span class="log-item info">用户选择继续 ${i.length} 个进行中的任务 (by taskId)...</span><br/>`,Q.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:i}).then(f=>{f&&f.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${f.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(f==null?void 0:f.message)||"未知错误"}</span><br/>`}).catch(f=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${f.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',i.forEach(f=>{k(f)}),t.value+=`<span class="log-item success">${i.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(o=>{t.value+=`<span class="log-item error">弹窗错误: ${o.message}，默认停止任务（保留控件）</span><br/>`,i.forEach(f=>{k(f)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Q.setConnectionSuccessHandler(n),Q.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),Q.addEventListener("connection",i=>{i.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${i.reason||"未知"}, 代码: ${i.code||"N/A"}，将自动重连</span><br/>`)}),Q.addEventListener("watcher",i=>{var r,u;if(Oe.push(i),i.type,i.eventType==="uploadSuccess"){const o=(r=i.data)==null?void 0:r.file,f=o==null?void 0:o.replace(/\.docx$/,""),p=`${i.eventType}_${o}_${Date.now()}`;if(ke.has(p)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${o}</span><br/>`;return}if(ke.add(p),ke.size>100){const _=ke.values();ke.delete(_.next().value)}const $=f&&((u=a[f])==null?void 0:u.wordType);Le(i,$)}else i.eventType&&Le(i,null)}),Q.addEventListener("urlMonitor",i=>{Oe.push(i),i.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${i.eventType||i.action}</span><br/>`),i.eventType&&Le(i,null)}),Q.addEventListener("health",i=>{}),Q.addEventListener("error",i=>{const r=i.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${r}</span><br/>`,console.error("WebSocket错误:",i)})},X=vt({}),Ie=async(n,i,r=!1,u=5e3,o={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const f=await Q.startUrlMonitoring(n,u,{downloadOnSuccess:o.downloadOnSuccess!==void 0?o.downloadOnSuccess:!0,appKey:o.appKey,filename:o.filename,taskId:i}),p=f.success||(f==null?void 0:f.success),$=f.urlId||(f==null?void 0:f.urlId);if(p&&$){X[i]={urlId:$,url:n,isResultUrl:r,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${$}</span><br/>`;try{await Q.startUrlChecking($)}catch{}return $}else throw new Error("服务器返回失败")}catch(f){return t.value+=`<span class="log-item error">启动URL监控失败: ${f.message}</span><br/>`,null}},xe=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(X).forEach(r=>{X[r].urlId===n&&delete X[r]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const i=await St(async()=>await Q.stopUrlMonitoring(n),"delete",`${$t()}/api/url/monitor/${n}`);return i&&(i.success||i!=null&&i.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(i){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${i.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await St(async()=>await Q.stopUrlMonitoring(n),"delete",`${$t()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},rt=async()=>{try{const n=await St(async()=>await Q.getUrlMonitorStatus(),"get",`${$t()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Ge=async n=>{try{return await Q.forceUrlCheck(n)}catch{return!1}},Le=async(n,i)=>{var r;if(n.eventType==="uploadSuccess"){const u=n.data.file,o=u.replace(/\.docx$/,"");if(a[o]){if(a[o].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${o.substring(0,8)} 的上传通知</span><br/>`;return}if(a[o].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${o.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${u} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${i||a[o].wordType||"wps-analysis"}</span><br/>`,a[o].status=1,a[o].uploadSuccess=!0,await ve(o,i||a[o].wordType||"wps-analysis")}}else if(n.eventType==="encryptedFileError"){const u=n.data.file,o=u.replace(/\.docx$/,"");if(a[o]){t.value+=`<span class="log-item error">文件加密错误: ${u}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[o].status=-1,a[o].errorMessage=n.data.message||"文件已加密，无法处理";try{const f=P();if(f&&f.ContentControls)for(let p=1;p<=f.ContentControls.Count;p++)try{const $=f.ContentControls.Item(p);if($&&$.Title&&($.Title===`任务_${o}`||$.Title===`任务增强_${o}`||$.Title===`校对_${o}`)){const _=$.Title===`任务增强_${o}`||a[o].isEnhanced,E=$.Title===`校对_${o}`||a[o].isCheckTask;E?$.Title=`异常校对_${o}`:_?$.Title=`异常增强_${o}`:$.Title=`异常_${o}`;const R=E?"校对":_?"增强":"普通";t.value+=`<span class="log-item info">已将${R}任务${o.substring(0,8)}控件标记为异常（文件加密）</span><br/>`;break}}catch{continue}}catch(f){t.value+=`<span class="log-item warning">更新控件标题失败: ${f.message}</span><br/>`}be(o)}}else if(n.eventType==="uploadError"){const u=n.data.file,o=u.replace(/\.docx$/,"");if(a[o]){t.value+=`<span class="log-item error">文件上传错误: ${u}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[o].status=-1,a[o].errorMessage=n.data.message||"文件上传失败";try{const f=P();if(f&&f.ContentControls)for(let p=1;p<=f.ContentControls.Count;p++)try{const $=f.ContentControls.Item(p);if($&&$.Title&&($.Title===`任务_${o}`||$.Title===`任务增强_${o}`||$.Title===`校对_${o}`)){const _=$.Title===`任务增强_${o}`||a[o].isEnhanced,E=$.Title===`校对_${o}`||a[o].isCheckTask;E?$.Title=`异常校对_${o}`:_?$.Title=`异常增强_${o}`:$.Title=`异常_${o}`;const R=E?"校对":_?"增强":"普通";t.value+=`<span class="log-item info">已将${R}任务${o.substring(0,8)}控件标记为异常（文件上传失败）</span><br/>`;break}}catch{continue}}catch(f){t.value+=`<span class="log-item warning">更新控件标题失败: ${f.message}</span><br/>`}be(o)}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:u,url:o,taskId:f,downloadedPath:p}=n.data,$=Object.keys(X).filter(_=>X[_].urlId===u);$.length>0&&$.forEach(_=>{p&&a[_]&&(a[_].resultFile=p,a[_].resultDownloaded=!0),delete X[_]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:u,url:o,filePath:f,taskId:p}=n.data;if(!Object.values(X).some(_=>_.urlId===u)&&p&&((r=a[p])!=null&&r.terminated))return;if(p&&a[p]&&a[p].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${u} 的文件下载通知</span><br/>`,u)try{await Q.stopUrlMonitoring(u),X[p]&&delete X[p]}catch{}return}if(p&&a[p]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${f}</span><br/>`,a[p].isCheckTask&&f.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${f}</span><br/>`;try{const E=window.Application.FileSystem.ReadFile(f),R=JSON.parse(E);if(await ue(R,p)){a[p].status=2,t.value+=`<span class="log-item success">校对任务${p.substring(0,8)}已完成批注处理</span><br/>`;const Z=x(a[p].startTime);t.value+=`<span class="log-item success">校对任务${p.substring(0,8)}完成，总耗时${Z}</span><br/>`}}catch(_){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${_.message}</span><br/>`,_.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${f}</span><br/>`),a[p].status=-1,a[p].errorMessage=`JSON处理失败: ${_.message}`,t.value+=`<span class="log-item error">校对任务${p.substring(0,8)}处理失败</span><br/>`}}else{a[p].resultFile=f,a[p].resultDownloaded=!0;const _=x(a[p].startTime);t.value+=`<span class="log-item success">任务${p.substring(0,8)}完成，总耗时${_}</span><br/>`,await Ve(p)}X[p]&&X[p].urlId&&(xe(X[p].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete X[p])}else if(u){t.value+=`<span class="log-item info">URL文件已下载: ${f}</span><br/>`;const _=Object.keys(X).filter(E=>{var R;return X[E].urlId===u&&!((R=a[E])!=null&&R.terminated)});_.length>0&&_.forEach(async E=>{if(a[E]){if(t.value+=`<span class="log-item info">关联到任务: ${E.substring(0,8)}</span><br/>`,a[E].resultFile=f,a[E].resultDownloaded=!0,a[E].isCheckTask&&f.endsWith(".wps.json"))try{const q=window.Application.FileSystem.ReadFile(f),Z=JSON.parse(q);if(await ue(Z,E)&&a[E].status===1){a[E].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const he=x(a[E].startTime);t.value+=`<span class="log-item success">校对任务${E.substring(0,8)}完成，总耗时${he}</span><br/>`}}catch(R){t.value+=`<span class="log-item error">处理校对JSON失败: ${R.message}</span><br/>`,a[E].status===1&&(a[E].status=-1,a[E].errorMessage=`JSON处理失败: ${R.message}`,t.value+=`<span class="log-item error">校对任务${E.substring(0,8)}处理失败</span><br/>`)}else if(a[E].status===1){a[E].status=2;const R=x(a[E].startTime);t.value+=`<span class="log-item success">任务${E.substring(0,8)}完成，总耗时${R}</span><br/>`}}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:u,url:o,error:f,taskId:p}=n.data;if(p&&a[p]&&a[p].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${p.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${f}</span><br/>`,p&&a[p]){a[p].status=-1,a[p].errorMessage=`下载失败: ${f}`;try{const $=P();if($&&$.ContentControls)for(let _=1;_<=$.ContentControls.Count;_++)try{const E=$.ContentControls.Item(_);if(E&&E.Title&&(E.Title===`任务_${p}`||E.Title===`任务增强_${p}`||E.Title===`校对_${p}`)){const R=E.Title===`任务增强_${p}`||a[p].isEnhanced,q=E.Title===`校对_${p}`||a[p].isCheckTask;q?E.Title=`异常校对_${p}`:R?E.Title=`异常增强_${p}`:E.Title=`异常_${p}`;const Z=q?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${Z}任务${p.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch($){t.value+=`<span class="log-item warning">更新控件标题失败: ${$.message}</span><br/>`}be(p),X[p]&&delete X[p]}if(u)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${u}</span><br/>`,await St(async()=>await Q.stopUrlMonitoring(u),"delete",`${$t()}/api/url/monitor/${u}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},ve=async(n,i="wps-analysis")=>{try{if(!C.appKey)throw new Error("未初始化appKey信息");const r=await d(),u=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,o=await Rt.post(bs()+"/api/open/ticket/v1/ai_comment/create",{access_token:r,data:{app_key:C.appKey,subject:V.value,stage:I.value,file_name:`${n}`,word_url:u,word_type:i,is_ai_auto:!0,is_ai_edit:!0,create_user_id:C.userId,create_username:C.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),f=o.data.data.ticket_id;if(!f)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",be(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${f}，开始监控结果文件</span><br/>`;let p,$;i==="wps-check"?(p=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${C.appKey}/ai/${f}.wps.json`,$=`${f}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${$}</span><br/>`):(p=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${f}.wps.docx`,$=`${f}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${$}</span><br/>`);const _={downloadOnSuccess:!0,filename:$,appKey:C.appKey,ticketId:f,taskId:n},E=await Ie(p,n,!0,3e3,_);return a[n]&&(a[n].ticketId=f,a[n].resultUrl=p,a[n].urlMonitorId=E,a[n].status=1),o.data}catch(r){return t.value+=`<span class="log-item error">API调用失败: ${r.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${r.message}`,be(n)),!1}},dt=async(n,i="wps-analysis")=>new Promise((r,u)=>{try{t.value+=`<span class="log-item info">监控目录: ${w.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${i}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=i,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const o=1e3,f=10;let p=0;const $=setInterval(()=>{if(p++,a[n]&&a[n].uploadSuccess){clearInterval($),r(!0);return}if(a[n]&&a[n].status===-1&&(clearInterval($),t.value+=`<span class="log-item error">任务${n.substring(0,8)}已异常，停止等待上传完成</span><br/>`,u(new Error(a[n].errorMessage||"任务已异常"))),p>=f){clearInterval($),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',be(n);return}},o)}catch(o){t.value+=`<span class="log-item error">任务处理异常: ${o.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${o.message}`),be(n),u(o)}}),Ze=async()=>{var r;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,i=n.ActiveDocument;if(l.value=i.DocID,v.value=n.ActiveWindow.Index,i!=null&&i.ContentControls)for(let u=1;u<=i.ContentControls.Count;u++){const o=i.ContentControls.Item(u);if(o&&o.Title){let f=null,p=1,$=!1,_=!1;if(o.Title.startsWith("任务增强_")?(f=o.Title.substring(5),p=1,$=!0):o.Title.startsWith("任务_")?(f=o.Title.substring(3),p=1):o.Title.startsWith("校对_")?(f=o.Title.substring(3),p=1,_=!0):o.Title.startsWith("已完成增强_")?(f=o.Title.substring(6),p=2,$=!0):o.Title.startsWith("已完成校对_")?(f=o.Title.substring(6),p=2,_=!0):o.Title.startsWith("已完成_")?(f=o.Title.substring(4),p=2):o.Title.startsWith("异常增强_")?(f=o.Title.substring(5),p=-1,$=!0):o.Title.startsWith("异常校对_")?(f=o.Title.substring(5),p=-1,_=!0):o.Title.startsWith("异常_")?(f=o.Title.substring(3),p=-1):o.Title.startsWith("已停止增强_")?(f=o.Title.substring(6),p=4,$=!0):o.Title.startsWith("已停止校对_")?(f=o.Title.substring(6),p=4,_=!0):o.Title.startsWith("已停止_")&&(f=o.Title.substring(4),p=4),f&&!a[f]){let E="";try{E=((r=o.Range)==null?void 0:r.Text)||""}catch{}let R=Date.now()-24*60*60*1e3;try{if(f.length===24){const te=f.substring(0,8),he=parseInt(te,16);!isNaN(he)&&he>0&&he<2147483647&&(R=he*1e3)}else{const te=Date.now()-864e5;p===2?R=te-60*60*1e3:p===-1?R=te-30*60*1e3:p===4?R=te-45*60*1e3:R=te}}catch{}a[f]={status:p,startTime:R,contentControlId:o.ID,isEnhanced:$,isCheckTask:_,selectedText:E};const q=p===1?"进行中":p===2?"已完成":p===-1?"异常":p===4?"已停止":"未知",Z=_?"校对":$?"增强":"普通";t.value+=`<span class="log-item info">发现已有${Z}任务: ${f.substring(0,8)}, 状态: ${q}</span><br/>`}}}},be=(n,i=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}i&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const r=P();if(!r){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let u=!1;const o=a[n].isEnhanced;if(r.ContentControls&&r.ContentControls.Count>0)for(let f=r.ContentControls.Count;f>=1;f--)try{const p=r.ContentControls.Item(f);p&&p.Title&&p.Title.includes(n)&&(p.LockContents=!1,p.Delete(!1),u=!0,console.log(p.Title),t.value+=`<span class="log-item success">已解锁并删除${o?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(p){t.value+=`<span class="log-item warning">访问第${f}个控件时出错: ${p.message}</span><br/>`;continue}u?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${o?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(r){t.value+=`<span class="log-item error">删除内容控件失败: ${r.message}</span><br/>`}},ot=n=>{var i;try{const r=window.wps,u=P();if(!u||!u.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const o=(i=a[n])==null?void 0:i.isEnhanced;let f=null;for(let $=1;$<=u.ContentControls.Count;$++)try{const _=u.ContentControls.Item($);if(_&&_.Title&&_.Title.includes(n)){f=_;break}}catch{continue}if(!f){const $=a[n];$&&($.status===2||$.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}f.Range.Select();const p=r.Windows.Item(v.value);p&&p.Selection&&p.Selection.Range&&p.ScrollIntoView(p.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${o?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(r){t.value+=`<span class="log-item error">跳转到任务控件失败: ${r.message}</span><br/>`}},Ve=async n=>{var p,$,_;const i=P(),r=a[n];if(!r){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(r.terminated||r.status!==1)return;if(r.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const u=P();let o=null;if(u&&u.ContentControls)for(let E=1;E<=u.ContentControls.Count;E++){const R=u.ContentControls.Item(E);if(R&&R.Title&&R.Title.includes(n)){o=R;break}}if(!o){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,r.errorMessage&&r.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${r.errorMessage}</span><br/>`;return}return}if(r.errorMessage&&r.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${r.errorMessage}</span><br/>`,be(n);return}if(!r.resultFile)return;a[n].documentInserted=!0;const f=o.Range;o.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${r.resultFile}...</span><br/>`;const E=i.Range(f.End,f.End);E.InsertParagraphAfter(),f.Text.includes("\v")&&E.InsertAfter("\v");let R=E.End;const q=(p=f.Tables)==null?void 0:p.Count;if(q){const ce=f.Tables.Item(q);(($=ce==null?void 0:ce.Range)==null?void 0:$.End)>R&&(ce.Range.InsertParagraphAfter(),R=(_=ce==null?void 0:ce.Range)==null?void 0:_.End)}i.Range(R,R).InsertFile(r.resultFile);for(let ce=1;ce<=u.ContentControls.Count;ce++){const Je=u.ContentControls.Item(ce);if(Je&&Je.Title&&(Je.Title===`任务_${n}`||Je.Title===`任务增强_${n}`)){o=Je;break}}const te=i.Range(o.Range.End-1,o.Range.End);te.Text.includes("\r")&&te.Delete(),a[n].status=2,X[n]&&X[n].urlId&&Q.sendRequest("urlMonitor","updateTaskStatus",{urlId:X[n].urlId,status:"completed",taskId:n}).then(ce=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(ce=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${ce.message}</span><br/>`});const qe=x(r.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${qe}</span><br/>`;const ye=o.Title===`任务增强_${n}`||r.isEnhanced,Ce=o.Title===`校对_${n}`||r.isCheckTask;if(o){Ce?o.Title=`已完成校对_${n}`:ye?o.Title=`已完成增强_${n}`:o.Title=`已完成_${n}`;const ce=Ce?"校对":ye?"增强":"普通";t.value+=`<span class="log-item info">已将${ce}任务${n.substring(0,8)}控件标记为已完成</span><br/>`}}catch(E){a[n].documentInserted=!1,a[n].status=-1;const R=o.Title===`任务增强_${n}`||r.isEnhanced,q=o.Title===`校对_${n}`||r.isCheckTask;if(o){q?o.Title=`异常校对_${n}`:R?o.Title=`异常增强_${n}`:o.Title=`异常_${n}`;const Z=q?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${Z}任务${n.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${E.message}</span><br/>`}},_e=async()=>{const n=(u=1e3)=>new Promise(o=>{setTimeout(()=>o(),u)}),i=new Map,r=Object.keys(a).filter(u=>a[u].status===1&&!a[u].terminated);for(r.length?(r.forEach(u=>{i.has(u)||i.set(u,Date.now())}),await Promise.all(r.map(u=>Ve(u)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const u=Object.keys(a).filter(o=>a[o].status===1&&!a[o].terminated);u.forEach(o=>{i.has(o)||i.set(o,Date.now());const f=i.get(o);(Date.now()-f)/1e3/60>=5e4&&a[o]&&!a[o].terminated&&(a[o].terminated=!0,a[o].status=-1,t.value+=`<span class="log-item warning">任务 ${o} 执行超过5分钟，已自动终止</span><br/>`,i.delete(o))}),u.length&&await Promise.all(u.map(o=>Ve(o)))}},je=async(n="wps-analysis")=>{const i=y();if(i){const{primary:u,all:o}=i,{taskId:f,control:p,task:$,isEnhanced:_,overlapType:E}=u,R=o.filter(q=>q.task&&(q.task.status===0||q.task.status===1));if(R.length>0){const q=R.map(Z=>Z.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${q})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${o.length}个已有任务重叠，重叠类型: ${E}</span><br/>`,W();try{for(const q of o){const{taskId:Z,control:te,task:he,isEnhanced:qe}=q;t.value+=`<span class="log-item info">删除重叠的${qe?"增强":"普通"}任务 ${Z.substring(0,8)}，状态为 ${b((he==null?void 0:he.status)||0)}</span><br/>`,he&&(he.status=3,he.terminated=!0,he.errorMessage="用户重新创建任务时删除");try{te.LockContents=!1,te.Delete(!1),a[Z]&&(a[Z].placeholderRemoved=!0)}catch(ye){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ye.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${o.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(q){return H(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${q.message}</span><br/>`,Promise.reject(q)}H()}const r=ws().replace(/-/g,"").substring(0,8);return new Promise(async(u,o)=>{try{const f=window.wps,p=P(),$=p.ActiveWindow.Selection,_=$.Range,E=$.Text||"";if(s.value=E,M(),_){const R=_.Paragraphs,q=R.Item(1),Z=R.Item(R.Count),te=q.Range.Start,he=Z.Range.End,qe=_.Start,ye=_.End,Ce=p.Range(Math.min(te,qe),Math.max(he,ye));try{let ce=p.ContentControls.Add(f.Enum.wdContentControlRichText,Ce);if(!ce){if(console.log("创建内容控件失败"),ce=p.ContentControls.Add(f.Enum.wdContentControlRichText),!ce){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',o(new Error("创建内容控件失败"));return}_.Cut(),ce.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const Je=n==="wps-enhance_analysis";ce.Title=Je?`任务增强_${r}`:`任务_${r}`,ce.LockContents=!0,a[r]||(a[r]={}),a[r].contentControlId=ce.ID,a[r].isEnhanced=Je,t.value+=`<span class="log-item info">已创建${Je?"增强":"普通"}内容控件并锁定选区</span><br/>`;const rn=ce.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${rn.length}</span><br/>`}catch(ce){t.value+=`<span class="log-item error">创建内容控件失败: ${ce.message}</span><br/>`,o(ce);return}}a[r]={status:0,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:E},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${r}，类型: ${n}</span><br/>`,await Ue(r),a[r]&&(a[r].status=1);try{await dt(r,n)?u():(a[r]&&a[r].status===1&&(a[r].status=-1,a[r].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${r.substring(0,8)}失败</span><br/>`,ie(r)),o(new Error("API调用失败或超时")))}catch(R){a[r]&&(a[r].status=-1,a[r].errorMessage=`执行错误: ${R.message}`,t.value+=`<span class="log-item error">任务${r.substring(0,8)}执行出错: ${R.message}</span><br/>`,ie(r)),o(R)}}catch(f){o(f)}})},Qe=async()=>{const n=y();if(n){const{primary:r,all:u}=n,{taskId:o,control:f,task:p,isEnhanced:$,overlapType:_}=r,E=u.filter(R=>R.task&&(R.task.status===0||R.task.status===1));if(E.length>0){const R=E.map(q=>q.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${R})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${u.length}个已有任务重叠，重叠类型: ${_}</span><br/>`,W();try{for(const R of u){const{taskId:q,control:Z,task:te,isEnhanced:he}=R;t.value+=`<span class="log-item info">删除重叠的校对任务 ${q.substring(0,8)}，状态为 ${b((te==null?void 0:te.status)||0)}</span><br/>`,te&&(te.status=3,te.terminated=!0,te.errorMessage="用户重新创建校对任务时删除");try{Z.LockContents=!1,Z.Delete(!1),a[q]&&(a[q].placeholderRemoved=!0)}catch(qe){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${qe.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${u.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(R){return H(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${R.message}</span><br/>`,Promise.reject(R)}H()}const i=ws().replace(/-/g,"").substring(0,8);return new Promise(async(r,u)=>{try{const o=window.wps,f=P(),p=f.ActiveWindow.Selection,$=p.Range,_=p.Text||"";if(s.value=_,M(),$){const E=$.Paragraphs,R=E.Item(1),q=E.Item(E.Count),Z=R.Range.Start,te=q.Range.End,he=$.Start,qe=$.End,ye=f.Range(Math.min(Z,he),Math.max(te,qe));try{let Ce=f.ContentControls.Add(o.Enum.wdContentControlRichText,ye);if(!Ce){if(console.log("创建校对内容控件失败"),Ce=f.ContentControls.Add(o.Enum.wdContentControlRichText),!Ce){t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',u(new Error("创建校对内容控件失败"));return}$.Cut(),Ce.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',Ce.Title=`校对_${i}`,Ce.LockContents=!0,a[i]||(a[i]={}),a[i].contentControlId=Ce.ID,a[i].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const ce=Ce.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${ce.length}</span><br/>`}catch(Ce){t.value+=`<span class="log-item error">创建校对内容控件失败: ${Ce.message}</span><br/>`,u(Ce);return}}a[i]={status:0,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:_},t.value+=`<span class="log-item success">创建校对任务: ${i}，类型: wps-check</span><br/>`,await Ue(i),a[i]&&(a[i].status=1);try{await dt(i,"wps-check")?r():(a[i]&&a[i].status===1&&(a[i].status=-1,a[i].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}失败</span><br/>`,ie(i)),u(new Error("校对API调用失败或超时")))}catch(E){a[i]&&(a[i].status=-1,a[i].errorMessage=`校对执行错误: ${E.message}`,t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}执行出错: ${E.message}</span><br/>`,ie(i)),u(E)}}catch(o){u(o)}})},He=async()=>{},et=()=>lt()>0?"status-error":it()>0?"status-running":tt()>0?"status-completed":"",it=()=>Object.values(a).filter(n=>n.status===0||n.status===1).length,tt=()=>Object.values(a).filter(n=>n.status===2).length,pt=()=>Object.values(a).filter(n=>n.status===2||n.status===4).length,lt=()=>Object.values(a).filter(n=>n.status===-1).length,Ot=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,i=P();if(!i){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let r=0;if(Object.keys(a).forEach(u=>{try{a[u].status===1&&(a[u].status=3,a[u].terminated=!0,a[u].errorMessage="强制清除"),be(u),r++}catch(o){t.value+=`<span class="log-item warning">清除任务${u.substring(0,8)}失败: ${o.message}</span><br/>`}}),i.ContentControls&&i.ContentControls.Count>0)for(let u=i.ContentControls.Count;u>=1;u--)try{const o=i.ContentControls.Item(u);if(o&&o.Title&&(o.Title.startsWith("任务_")||o.Title.startsWith("任务增强_")||o.Title.startsWith("已完成_")||o.Title.startsWith("已完成增强_")||o.Title.startsWith("异常_")||o.Title.startsWith("异常增强_")||o.Title.startsWith("已停止_")||o.Title.startsWith("已停止增强_")))try{o.LockContents=!1,o.Delete(!1);let f;o.Title.startsWith("任务增强_")?f=o.Title.substring(5):o.Title.startsWith("任务_")?f=o.Title.substring(3):o.Title.startsWith("已完成增强_")?f=o.Title.substring(6):o.Title.startsWith("已完成_")?f=o.Title.substring(4):o.Title.startsWith("异常增强_")?f=o.Title.substring(5):o.Title.startsWith("异常_")?f=o.Title.substring(3):o.Title.startsWith("已停止增强_")?f=o.Title.substring(6):o.Title.startsWith("已停止_")&&(f=o.Title.substring(4)),a[f]?(a[f].placeholderRemoved=!0,a[f].status=3):a[f]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},r++,t.value+=`<span class="log-item success">已删除任务${f.substring(0,8)}的内容控件</span><br/>`}catch(f){t.value+=`<span class="log-item error">删除控件失败: ${f.message}</span><br/>`}}catch(o){t.value+=`<span class="log-item warning">访问控件时出错: ${o.message}</span><br/>`}r>0?t.value+=`<span class="log-item success">已清除${r}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},kt=async()=>{try{const n=Object.values(X).map(i=>i.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const i of n)await xe(i)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Tt=()=>{Ls(async()=>{try{const i=window.Application.PluginStorage.getItem("user_info");if(!i)throw new Error("未找到用户信息");const r=JSON.parse(i);if(!r.orgs||!r.orgs[0])throw new Error("未找到组织信息");C.appKey=r.appKey,C.userName=r.nickname,C.userId=r.userId,C.appSecret=r.appSecret}catch(i){t.value+=`<span class="log-item error">初始化appKey失败: ${i.message}</span><br/>`}await oe(),Jt([V,I],async()=>{await h()},{immediate:!1}),await A();const n=Ke.onVersionChange(()=>{const i=se();ne.splice(0,ne.length,...i),Ke.isSeniorEdition()&&I.value==="junior"?I.value="senior":!Ke.isSeniorEdition()&&I.value==="senior"&&(I.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Ke.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',U(),await Ze(),Fe(),Ct(),_e(),window.addEventListener("beforeunload",kt),()=>{D&&clearTimeout(D),n&&n()}}),Jt(t,n=>{D&&clearTimeout(D),D=setTimeout(()=>{Me(n)},10)},{immediate:!1})},Ct=()=>{Q.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==V.value&&(V.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${V.value}</span><br/>`),n.data.stage!==I.value&&(I.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${I.value}</span><br/>`))})},ft=fe(!1),y=()=>{try{const n=P(),i=n.ActiveWindow.Selection;if(!i||!n||!n.ContentControls)return null;const r=i.Range,u=[];for(let o=1;o<=n.ContentControls.Count;o++)try{const f=n.ContentControls.Item(o);if(!f)continue;const p=(f.Title||"").trim(),$=f.Range;if(r.Start<$.End&&r.End>$.Start){let _=null,E=!1,R=!1;if(!p)R=!0,_=`empty_${f.ID||Date.now()}`;else if(p.startsWith("任务_")||p.startsWith("任务增强_")||p.startsWith("校对_")||p.startsWith("已完成_")||p.startsWith("已完成增强_")||p.startsWith("已完成校对_")||p.startsWith("异常_")||p.startsWith("异常增强_")||p.startsWith("异常校对_")||p.startsWith("已停止_")||p.startsWith("已停止增强_")||p.startsWith("已停止校对_"))p.startsWith("任务增强_")?(_=p.substring(5),E=!0):p.startsWith("任务_")||p.startsWith("校对_")?_=p.substring(3):p.startsWith("已完成增强_")?(_=p.substring(6),E=!0):p.startsWith("已完成校对_")?_=p.substring(6):p.startsWith("已完成_")?_=p.substring(4):p.startsWith("异常增强_")?(_=p.substring(5),E=!0):p.startsWith("异常校对_")?_=p.substring(5):p.startsWith("异常_")?_=p.substring(3):p.startsWith("已停止增强_")?(_=p.substring(6),E=!0):p.startsWith("已停止校对_")?_=p.substring(6):p.startsWith("已停止_")&&(_=p.substring(4));else continue;if(_){let q;r.Start>=$.Start&&r.End<=$.End?q="completely_within":r.Start<=$.Start&&r.End>=$.End?q="completely_contains":q="partial_overlap",u.push({taskId:_,control:f,task:R?null:a[_]||null,isEnhanced:E,isEmptyTitle:R,overlapType:q,controlRange:{start:$.Start,end:$.End},selectionRange:{start:r.Start,end:r.End}})}}}catch{continue}return u.length===0?null:{primary:u[0],all:u}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},W=()=>{ft.value=!0},H=()=>{ft.value=!1},ie=async(n,i=!1)=>{W();try{await be(n,i)}finally{H()}},re=n=>new Promise((i,r)=>{O.show=!0,O.message=n,O.resolveCallback=i,O.rejectCallback=r}),we=n=>{O.show=!1,n&&O.resolveCallback?O.resolveCallback(!0):O.resolveCallback&&O.resolveCallback(!1),O.resolveCallback=null,O.rejectCallback=null},Te=(n,i,r="error")=>{F.show=!0,F.title=n,F.message=i,F.type=r},ze=()=>{F.show=!1,F.title="",F.message="",F.type="error"},A=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await Q.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(V.value=n.data.subject),n.data.stage&&(I.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${V.value})和年级(${I.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},h=async()=>{try{if(V.value||I.value){t.value+=`<span class="log-item info">正在保存学科(${V.value})和年级(${I.value})设置到服务器...</span><br/>`;const n=await Q.setSubjectAndStage(V.value,I.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}},J=async()=>{try{B.value=2,Y.value=B.value===2,Y.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${B.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${B.value}）</span><br/>`}catch(n){console.error("检查企业ID失败:",n),t.value+=`<span class="log-item error">检查企业ID失败: ${n.message}</span><br/>`,Y.value=!1}},G=(n,i)=>{try{const r=P();if(!r||!n)return!1;const u=r.Comments.Add(n,i);try{u!=null&&u.Range&&(u.Range.ParagraphFormat.Reset(),u.Range.ParagraphFormat.LineSpacingRule=3,u.Range.ParagraphFormat.LineSpacing=10,u.Edit())}catch(o){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${o.message}</span><br/>`}return!0}catch(r){return t.value+=`<span class="log-item error">为范围添加批注失败: ${r.message}</span><br/>`,!1}},ue=async(n,i)=>{try{if(!n||!Array.isArray(n))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const r=P();let u=null,o=null;if(r&&r.ContentControls)for(let _=1;_<=r.ContentControls.Count;_++)try{const E=r.ContentControls.Item(_);if((E==null?void 0:E.Title)===`校对_${i}`){u=E,o=E.Range,t.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!o){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const _=a[i];if(_&&_.selectedText)try{const E=r.Range().Find;if(E.ClearFormatting(),E.Text=_.selectedText.substring(0,Math.min(_.selectedText.length,100)),E.Forward=!0,E.Wrap=1,E.Execute()){const R=E.Parent;if(_.selectedText.length>100){const q=R.Start+_.selectedText.length;o=r.Range(R.Start,Math.min(q,r.Range().End))}else o=R;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(E){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${E.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let f=0,p=0,$=[];for(const _ of n){if(!_.mode1||!Array.isArray(_.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const E of _.mode1){if(!E.error_info||!Array.isArray(E.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const R=E.quest_html||"",q=E.quest_type||"";t.value+=`<span class="log-item info">处理${q}题目，发现${E.error_info.length}个错误信息</span><br/>`;for(const Z of E.error_info)try{let te="";if(Z.error_info&&(te+=`【错误类型】${Z.error_info}`),Z.fix_info&&(te+=`\r【修正建议】${Z.fix_info}`),Z.keywords&&Z.keywords.trim()){let he=u?u.Range:o;u.LockContents=!1,pe(te,Z.keywords.trim(),0,u.Range)?(f++,t.value+=`<span class="log-item success">已为关键词"${Z.keywords.trim()}"添加批注: ${te}</span><br/>`):($.push({comment:te,keyword:Z.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${Z.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else $.push({comment:te,keyword:null})}catch(te){p++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${te.message}</span><br/>`}}}if($.length>0){t.value+=`<span class="log-item info">为整个控件范围添加${$.length}个批注</span><br/>`;for(const _ of $)try{let E=_.comment,R=u?u.Range:o;u.LockContents=!1,G(u.Range,E)?(f++,t.value+=`<span class="log-item success">已为整个范围添加批注${_.keyword?`（关键词：${_.keyword}）`:""}</span><br/>`):p++}catch(E){p++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${E.message}</span><br/>`}}return f>0?(t.value+=`<span class="log-item success">校对任务${i.substring(0,8)}处理完成：成功添加${f}个批注</span><br/>`,p>0&&(t.value+=`<span class="log-item warning">校对任务${i.substring(0,8)}：${p}个批注添加失败</span><br/>`),u.Title=`已完成校对_${i}`,!0):(t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(r){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${r.message}</span><br/>`,!1}};return{docName:e,selected:s,logger:t,map:a,watchedDir:w,subject:V,stage:I,subjectOptions:N,stageOptions:ne,fetchWatchedDir:oe,clearLog:ee,getCurrentDocument:P,checkDocumentFormat:le,getTaskStatusClass:g,getTaskStatusText:b,getElapsedTime:x,terminateTask:S,stopTaskWithoutRemovingControl:k,run1:je,run2:He,runCheck:Qe,getHeaderStatusClass:et,getRunningTasksCount:it,getCompletedTasksCount:tt,getReleasableTasksCount:pt,getErrorTasksCount:lt,setupLifecycle:Tt,navigateToTaskControl:ot,forceCleanAllTasks:Ot,ws:Ae,wsMessages:Oe,initWebSocket:Fe,handleWatcherEvent:Le,urlMonitorTasks:X,monitorUrlForTask:Ie,stopUrlMonitoring:xe,getUrlMonitorStatus:rt,forceUrlCheck:Ge,cleanupUrlMonitoringTasks:kt,tryRemoveTaskPlaceholder:be,isLoading:ft,isSelectionInTaskControl:y,tryRemoveTaskPlaceholderWithLoading:ie,showConfirm:re,handleConfirm:we,confirmDialog:O,errorDialog:F,showErrorDialog:Te,hideErrorDialog:ze,loadSubjectAndStage:A,saveSubjectAndStage:h,enterpriseId:B,isCheckingVisible:Y,checkEnterpriseAndSetCheckingVisibility:J,processCheckingJson:ue}}const Cn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Ke.getVersionConfig(),selectedEdition:Ke.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),l=Math.floor(t%(1e3*60*60)/(1e3*60)),v=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${l}分 ${v}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Q.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Q[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Q.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Q.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Q.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Q.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Q.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Q.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Q.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Ke.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await ln()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Q.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Q.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Q.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Q.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Q.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Q.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Q.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Ke.onVersionChange(e=>{this.versionConfig=Ke.getVersionConfig(),this.selectedEdition=Ke.getEdition()}),await Q.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},$n={class:"file-watcher"},Sn={class:"settings-modal"},_n={class:"modal-content"},En={class:"modal-header"},An={class:"version-tag"},Mn={key:0,class:"user-info"},Dn={key:1,class:"user-info inner-tag"},Pn={class:"header-actions"},On={class:"modal-body"},In={class:"status-section"},Rn={class:"status-item"},Un={key:0,class:"status-item"},Fn={class:"directory-section"},Ln={class:"directory-form"},jn={class:"radio-group"},Wn={class:"radio-item"},Bn={class:"radio-item"},Nn={class:"radio-item"},Vn={key:0,class:"radio-item"},Hn={key:1,class:"directory-section"},zn={class:"directory-form"},qn={class:"form-group"},Jn=["placeholder"],Yn=["disabled"],Kn={key:2,class:"directory-section"},Xn={class:"directory-form"},Gn={class:"form-group"},Zn=["placeholder"],Qn=["disabled"],ea={key:3,class:"directory-section"},ta={class:"directory-form"},sa={class:"form-group"},na=["placeholder"],aa=["disabled"],ra={key:4,class:"events-section"},oa={class:"events-list"},ia={class:"event-time"},la={class:"event-message"},ca={key:1,class:"modal-footer"};function ua(e,s,t,a,l,v){var T,D,w,C,O,F,V,I,B,Y,N,se,ne,oe,d,ee,P,le,pe,g,b,x,k,S;return L(),j("div",$n,[c("div",Sn,[c("div",_n,[c("div",En,[c("h3",null,[cn(ae(l.versionConfig.shortName)+"设置 ",1),c("span",An,ae(l.versionConfig.appVersion),1),v.userInfo?(L(),j("span",Mn,"欢迎您，"+ae(v.userInfo.nickname),1)):z("",!0),((w=(D=(T=v.userInfo)==null?void 0:T.orgs)==null?void 0:D[0])==null?void 0:w.orgId)===2?(L(),j("span",Dn,"内部版本号：1.1.24")):z("",!0)]),c("div",Pn,[c("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>v.handleLogout&&v.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[c("span",{class:"icon-logout"},null,-1)])),c("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),c("div",On,[c("div",In,[c("div",Rn,[s[22]||(s[22]=c("span",{class:"label"},"状态：",-1)),c("span",{class:$e(["status-badge",l.status.status])},ae(l.status.status==="running"?"运行中":"已停止"),3)]),((F=(O=(C=v.userInfo)==null?void 0:C.orgs)==null?void 0:O[0])==null?void 0:F.orgId)===2?(L(),j("div",Un,[s[23]||(s[23]=c("span",{class:"label"},"本次上传：",-1)),c("span",null,ae(l.status.processedFiles||0)+" 个文件",1)])):z("",!0),c("div",Fn,[s[28]||(s[28]=c("h4",null,"保存方式设置",-1)),c("div",Ln,[c("div",jn,[c("label",Wn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>l.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[24]||(s[24]=c("span",{class:"radio-label"},"方式一",-1))]),c("label",Bn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>l.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[25]||(s[25]=c("span",{class:"radio-label"},"方式二 (默认)",-1))]),c("label",Nn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>l.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[26]||(s[26]=c("span",{class:"radio-label"},"方式三",-1))]),((B=(I=(V=v.userInfo)==null?void 0:V.orgs)==null?void 0:I[0])==null?void 0:B.orgId)===2?(L(),j("label",Vn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>l.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[27]||(s[27]=c("span",{class:"radio-label"},"方式四",-1))])):z("",!0)]),l.saveMethodUpdateMessage?(L(),j("div",{key:0,class:$e(["update-message",l.saveMethodUpdateSuccess?"success":"error"])},ae(l.saveMethodUpdateMessage),3)):z("",!0)])])]),z("",!0),((se=(N=(Y=v.userInfo)==null?void 0:Y.orgs)==null?void 0:N[0])==null?void 0:se.orgId)===2?(L(),j("div",Hn,[s[32]||(s[32]=c("h4",null,"上传目录设置",-1)),c("div",zn,[c("div",qn,[s[31]||(s[31]=c("span",{class:"label"},"路径：",-1)),Ye(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>l.newWatchDir=m),placeholder:l.status.watchDir||"C:\\Temp"},null,8,Jn),[[Zt,l.newWatchDir]]),c("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>v.updateWatchDir&&v.updateWatchDir(...m)),disabled:l.isUpdating||!l.newWatchDir},ae(l.isUpdating?"更新中...":"更新目录"),9,Yn)]),l.updateMessage?(L(),j("div",{key:0,class:$e(["update-message",l.updateSuccess?"success":"error"])},ae(l.updateMessage),3)):z("",!0)])])):z("",!0),((d=(oe=(ne=v.userInfo)==null?void 0:ne.orgs)==null?void 0:oe[0])==null?void 0:d.orgId)===2?(L(),j("div",Kn,[s[34]||(s[34]=c("h4",null,"下载目录设置",-1)),c("div",Xn,[c("div",Gn,[s[33]||(s[33]=c("span",{class:"label"},"路径：",-1)),Ye(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>l.newDownloadPath=m),placeholder:l.downloadPath||"C:\\Temp\\Downloads"},null,8,Zn),[[Zt,l.newDownloadPath]]),c("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>v.updateDownloadPath&&v.updateDownloadPath(...m)),disabled:l.isUpdatingDownloadPath||!l.newDownloadPath},ae(l.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Qn)]),l.downloadPathUpdateMessage?(L(),j("div",{key:0,class:$e(["update-message",l.downloadPathUpdateSuccess?"success":"error"])},ae(l.downloadPathUpdateMessage),3)):z("",!0)])])):z("",!0),((le=(P=(ee=v.userInfo)==null?void 0:ee.orgs)==null?void 0:P[0])==null?void 0:le.orgId)===2?(L(),j("div",ea,[s[36]||(s[36]=c("h4",null,"配置目录设置",-1)),c("div",ta,[c("div",sa,[s[35]||(s[35]=c("span",{class:"label"},"路径：",-1)),Ye(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>l.newAddonConfigPath=m),placeholder:l.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,na),[[Zt,l.newAddonConfigPath]]),c("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>v.updateAddonConfigPath&&v.updateAddonConfigPath(...m)),disabled:l.isUpdatingAddonConfigPath||!l.newAddonConfigPath},ae(l.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,aa)]),l.addonConfigPathUpdateMessage?(L(),j("div",{key:0,class:$e(["update-message",l.addonConfigPathUpdateSuccess?"success":"error"])},ae(l.addonConfigPathUpdateMessage),3)):z("",!0)])])):z("",!0),((b=(g=(pe=v.userInfo)==null?void 0:pe.orgs)==null?void 0:g[0])==null?void 0:b.orgId)===2?(L(),j("div",ra,[s[37]||(s[37]=c("h4",null,"最近事件",-1)),c("div",oa,[(L(!0),j(mt,null,_t(l.recentEvents,(m,U)=>(L(),j("div",{key:U,class:"event-item"},[c("span",ia,ae(v.formatTime(m.timestamp)),1),c("span",{class:$e(["event-type",m.eventType])},ae(v.getEventTypeText(m.eventType)),3),c("span",la,ae(v.getEventMessage(m)),1)]))),128))])])):z("",!0)]),z("",!0),((S=(k=(x=v.userInfo)==null?void 0:x.orgs)==null?void 0:k[0])==null?void 0:S.orgId)===2?(L(),j("div",ca,[c("button",{class:$e(["control-btn",l.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>v.controlService&&v.controlService(...m))},ae(l.status.status==="running"?"停止服务":"启动服务"),3)])):z("",!0)])])])}const da=js(Cn,[["render",ua],["__scopeId","data-v-3a2ef3d6"]]);var De="top",Be="bottom",Ne="right",Pe="left",ls="auto",Bt=[De,Be,Ne,Pe],At="start",jt="end",pa="clippingParents",Bs="viewport",It="popper",fa="reference",ys=Bt.reduce(function(e,s){return e.concat([s+"-"+At,s+"-"+jt])},[]),Ns=[].concat(Bt,[ls]).reduce(function(e,s){return e.concat([s,s+"-"+At,s+"-"+jt])},[]),ha="beforeRead",ga="read",va="afterRead",ma="beforeMain",ba="main",wa="afterMain",ya="beforeWrite",xa="write",ka="afterWrite",Ta=[ha,ga,va,ma,ba,wa,ya,xa,ka];function at(e){return e?(e.nodeName||"").toLowerCase():null}function Re(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function xt(e){var s=Re(e).Element;return e instanceof s||e instanceof Element}function We(e){var s=Re(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function cs(e){if(typeof ShadowRoot>"u")return!1;var s=Re(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function Ca(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},l=s.attributes[t]||{},v=s.elements[t];!We(v)||!at(v)||(Object.assign(v.style,a),Object.keys(l).forEach(function(T){var D=l[T];D===!1?v.removeAttribute(T):v.setAttribute(T,D===!0?"":D)}))})}function $a(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var l=s.elements[a],v=s.attributes[a]||{},T=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),D=T.reduce(function(w,C){return w[C]="",w},{});!We(l)||!at(l)||(Object.assign(l.style,D),Object.keys(v).forEach(function(w){l.removeAttribute(w)}))})}}const Vs={name:"applyStyles",enabled:!0,phase:"write",fn:Ca,effect:$a,requires:["computeStyles"]};function nt(e){return e.split("-")[0]}var wt=Math.max,Yt=Math.min,Mt=Math.round;function as(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Hs(){return!/^((?!chrome|android).)*safari/i.test(as())}function Dt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),l=1,v=1;s&&We(e)&&(l=e.offsetWidth>0&&Mt(a.width)/e.offsetWidth||1,v=e.offsetHeight>0&&Mt(a.height)/e.offsetHeight||1);var T=xt(e)?Re(e):window,D=T.visualViewport,w=!Hs()&&t,C=(a.left+(w&&D?D.offsetLeft:0))/l,O=(a.top+(w&&D?D.offsetTop:0))/v,F=a.width/l,V=a.height/v;return{width:F,height:V,top:O,right:C+F,bottom:O+V,left:C,x:C,y:O}}function us(e){var s=Dt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function zs(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&cs(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function ut(e){return Re(e).getComputedStyle(e)}function Sa(e){return["table","td","th"].indexOf(at(e))>=0}function gt(e){return((xt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Xt(e){return at(e)==="html"?e:e.assignedSlot||e.parentNode||(cs(e)?e.host:null)||gt(e)}function xs(e){return!We(e)||ut(e).position==="fixed"?null:e.offsetParent}function _a(e){var s=/firefox/i.test(as()),t=/Trident/i.test(as());if(t&&We(e)){var a=ut(e);if(a.position==="fixed")return null}var l=Xt(e);for(cs(l)&&(l=l.host);We(l)&&["html","body"].indexOf(at(l))<0;){var v=ut(l);if(v.transform!=="none"||v.perspective!=="none"||v.contain==="paint"||["transform","perspective"].indexOf(v.willChange)!==-1||s&&v.willChange==="filter"||s&&v.filter&&v.filter!=="none")return l;l=l.parentNode}return null}function Nt(e){for(var s=Re(e),t=xs(e);t&&Sa(t)&&ut(t).position==="static";)t=xs(t);return t&&(at(t)==="html"||at(t)==="body"&&ut(t).position==="static")?s:t||_a(e)||s}function ds(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ut(e,s,t){return wt(e,Yt(s,t))}function Ea(e,s,t){var a=Ut(e,s,t);return a>t?t:a}function qs(){return{top:0,right:0,bottom:0,left:0}}function Js(e){return Object.assign({},qs(),e)}function Ys(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var Aa=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Js(typeof s!="number"?s:Ys(s,Bt))};function Ma(e){var s,t=e.state,a=e.name,l=e.options,v=t.elements.arrow,T=t.modifiersData.popperOffsets,D=nt(t.placement),w=ds(D),C=[Pe,Ne].indexOf(D)>=0,O=C?"height":"width";if(!(!v||!T)){var F=Aa(l.padding,t),V=us(v),I=w==="y"?De:Pe,B=w==="y"?Be:Ne,Y=t.rects.reference[O]+t.rects.reference[w]-T[w]-t.rects.popper[O],N=T[w]-t.rects.reference[w],se=Nt(v),ne=se?w==="y"?se.clientHeight||0:se.clientWidth||0:0,oe=Y/2-N/2,d=F[I],ee=ne-V[O]-F[B],P=ne/2-V[O]/2+oe,le=Ut(d,P,ee),pe=w;t.modifiersData[a]=(s={},s[pe]=le,s.centerOffset=le-P,s)}}function Da(e){var s=e.state,t=e.options,a=t.element,l=a===void 0?"[data-popper-arrow]":a;l!=null&&(typeof l=="string"&&(l=s.elements.popper.querySelector(l),!l)||zs(s.elements.popper,l)&&(s.elements.arrow=l))}const Pa={name:"arrow",enabled:!0,phase:"main",fn:Ma,effect:Da,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Pt(e){return e.split("-")[1]}var Oa={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ia(e,s){var t=e.x,a=e.y,l=s.devicePixelRatio||1;return{x:Mt(t*l)/l||0,y:Mt(a*l)/l||0}}function ks(e){var s,t=e.popper,a=e.popperRect,l=e.placement,v=e.variation,T=e.offsets,D=e.position,w=e.gpuAcceleration,C=e.adaptive,O=e.roundOffsets,F=e.isFixed,V=T.x,I=V===void 0?0:V,B=T.y,Y=B===void 0?0:B,N=typeof O=="function"?O({x:I,y:Y}):{x:I,y:Y};I=N.x,Y=N.y;var se=T.hasOwnProperty("x"),ne=T.hasOwnProperty("y"),oe=Pe,d=De,ee=window;if(C){var P=Nt(t),le="clientHeight",pe="clientWidth";if(P===Re(t)&&(P=gt(t),ut(P).position!=="static"&&D==="absolute"&&(le="scrollHeight",pe="scrollWidth")),P=P,l===De||(l===Pe||l===Ne)&&v===jt){d=Be;var g=F&&P===ee&&ee.visualViewport?ee.visualViewport.height:P[le];Y-=g-a.height,Y*=w?1:-1}if(l===Pe||(l===De||l===Be)&&v===jt){oe=Ne;var b=F&&P===ee&&ee.visualViewport?ee.visualViewport.width:P[pe];I-=b-a.width,I*=w?1:-1}}var x=Object.assign({position:D},C&&Oa),k=O===!0?Ia({x:I,y:Y},Re(t)):{x:I,y:Y};if(I=k.x,Y=k.y,w){var S;return Object.assign({},x,(S={},S[d]=ne?"0":"",S[oe]=se?"0":"",S.transform=(ee.devicePixelRatio||1)<=1?"translate("+I+"px, "+Y+"px)":"translate3d("+I+"px, "+Y+"px, 0)",S))}return Object.assign({},x,(s={},s[d]=ne?Y+"px":"",s[oe]=se?I+"px":"",s.transform="",s))}function Ra(e){var s=e.state,t=e.options,a=t.gpuAcceleration,l=a===void 0?!0:a,v=t.adaptive,T=v===void 0?!0:v,D=t.roundOffsets,w=D===void 0?!0:D,C={placement:nt(s.placement),variation:Pt(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:l,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,ks(Object.assign({},C,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:T,roundOffsets:w})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,ks(Object.assign({},C,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:w})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Ua={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ra,data:{}};var Ht={passive:!0};function Fa(e){var s=e.state,t=e.instance,a=e.options,l=a.scroll,v=l===void 0?!0:l,T=a.resize,D=T===void 0?!0:T,w=Re(s.elements.popper),C=[].concat(s.scrollParents.reference,s.scrollParents.popper);return v&&C.forEach(function(O){O.addEventListener("scroll",t.update,Ht)}),D&&w.addEventListener("resize",t.update,Ht),function(){v&&C.forEach(function(O){O.removeEventListener("scroll",t.update,Ht)}),D&&w.removeEventListener("resize",t.update,Ht)}}const La={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Fa,data:{}};var ja={left:"right",right:"left",bottom:"top",top:"bottom"};function qt(e){return e.replace(/left|right|bottom|top/g,function(s){return ja[s]})}var Wa={start:"end",end:"start"};function Ts(e){return e.replace(/start|end/g,function(s){return Wa[s]})}function ps(e){var s=Re(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function fs(e){return Dt(gt(e)).left+ps(e).scrollLeft}function Ba(e,s){var t=Re(e),a=gt(e),l=t.visualViewport,v=a.clientWidth,T=a.clientHeight,D=0,w=0;if(l){v=l.width,T=l.height;var C=Hs();(C||!C&&s==="fixed")&&(D=l.offsetLeft,w=l.offsetTop)}return{width:v,height:T,x:D+fs(e),y:w}}function Na(e){var s,t=gt(e),a=ps(e),l=(s=e.ownerDocument)==null?void 0:s.body,v=wt(t.scrollWidth,t.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),T=wt(t.scrollHeight,t.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),D=-a.scrollLeft+fs(e),w=-a.scrollTop;return ut(l||t).direction==="rtl"&&(D+=wt(t.clientWidth,l?l.clientWidth:0)-v),{width:v,height:T,x:D,y:w}}function hs(e){var s=ut(e),t=s.overflow,a=s.overflowX,l=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+l+a)}function Ks(e){return["html","body","#document"].indexOf(at(e))>=0?e.ownerDocument.body:We(e)&&hs(e)?e:Ks(Xt(e))}function Ft(e,s){var t;s===void 0&&(s=[]);var a=Ks(e),l=a===((t=e.ownerDocument)==null?void 0:t.body),v=Re(a),T=l?[v].concat(v.visualViewport||[],hs(a)?a:[]):a,D=s.concat(T);return l?D:D.concat(Ft(Xt(T)))}function rs(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Va(e,s){var t=Dt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Cs(e,s,t){return s===Bs?rs(Ba(e,t)):xt(s)?Va(s,t):rs(Na(gt(e)))}function Ha(e){var s=Ft(Xt(e)),t=["absolute","fixed"].indexOf(ut(e).position)>=0,a=t&&We(e)?Nt(e):e;return xt(a)?s.filter(function(l){return xt(l)&&zs(l,a)&&at(l)!=="body"}):[]}function za(e,s,t,a){var l=s==="clippingParents"?Ha(e):[].concat(s),v=[].concat(l,[t]),T=v[0],D=v.reduce(function(w,C){var O=Cs(e,C,a);return w.top=wt(O.top,w.top),w.right=Yt(O.right,w.right),w.bottom=Yt(O.bottom,w.bottom),w.left=wt(O.left,w.left),w},Cs(e,T,a));return D.width=D.right-D.left,D.height=D.bottom-D.top,D.x=D.left,D.y=D.top,D}function Xs(e){var s=e.reference,t=e.element,a=e.placement,l=a?nt(a):null,v=a?Pt(a):null,T=s.x+s.width/2-t.width/2,D=s.y+s.height/2-t.height/2,w;switch(l){case De:w={x:T,y:s.y-t.height};break;case Be:w={x:T,y:s.y+s.height};break;case Ne:w={x:s.x+s.width,y:D};break;case Pe:w={x:s.x-t.width,y:D};break;default:w={x:s.x,y:s.y}}var C=l?ds(l):null;if(C!=null){var O=C==="y"?"height":"width";switch(v){case At:w[C]=w[C]-(s[O]/2-t[O]/2);break;case jt:w[C]=w[C]+(s[O]/2-t[O]/2);break}}return w}function Wt(e,s){s===void 0&&(s={});var t=s,a=t.placement,l=a===void 0?e.placement:a,v=t.strategy,T=v===void 0?e.strategy:v,D=t.boundary,w=D===void 0?pa:D,C=t.rootBoundary,O=C===void 0?Bs:C,F=t.elementContext,V=F===void 0?It:F,I=t.altBoundary,B=I===void 0?!1:I,Y=t.padding,N=Y===void 0?0:Y,se=Js(typeof N!="number"?N:Ys(N,Bt)),ne=V===It?fa:It,oe=e.rects.popper,d=e.elements[B?ne:V],ee=za(xt(d)?d:d.contextElement||gt(e.elements.popper),w,O,T),P=Dt(e.elements.reference),le=Xs({reference:P,element:oe,strategy:"absolute",placement:l}),pe=rs(Object.assign({},oe,le)),g=V===It?pe:P,b={top:ee.top-g.top+se.top,bottom:g.bottom-ee.bottom+se.bottom,left:ee.left-g.left+se.left,right:g.right-ee.right+se.right},x=e.modifiersData.offset;if(V===It&&x){var k=x[l];Object.keys(b).forEach(function(S){var m=[Ne,Be].indexOf(S)>=0?1:-1,U=[De,Be].indexOf(S)>=0?"y":"x";b[S]+=k[U]*m})}return b}function qa(e,s){s===void 0&&(s={});var t=s,a=t.placement,l=t.boundary,v=t.rootBoundary,T=t.padding,D=t.flipVariations,w=t.allowedAutoPlacements,C=w===void 0?Ns:w,O=Pt(a),F=O?D?ys:ys.filter(function(B){return Pt(B)===O}):Bt,V=F.filter(function(B){return C.indexOf(B)>=0});V.length===0&&(V=F);var I=V.reduce(function(B,Y){return B[Y]=Wt(e,{placement:Y,boundary:l,rootBoundary:v,padding:T})[nt(Y)],B},{});return Object.keys(I).sort(function(B,Y){return I[B]-I[Y]})}function Ja(e){if(nt(e)===ls)return[];var s=qt(e);return[Ts(e),s,Ts(s)]}function Ya(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var l=t.mainAxis,v=l===void 0?!0:l,T=t.altAxis,D=T===void 0?!0:T,w=t.fallbackPlacements,C=t.padding,O=t.boundary,F=t.rootBoundary,V=t.altBoundary,I=t.flipVariations,B=I===void 0?!0:I,Y=t.allowedAutoPlacements,N=s.options.placement,se=nt(N),ne=se===N,oe=w||(ne||!B?[qt(N)]:Ja(N)),d=[N].concat(oe).reduce(function(ke,Se){return ke.concat(nt(Se)===ls?qa(s,{placement:Se,boundary:O,rootBoundary:F,padding:C,flipVariations:B,allowedAutoPlacements:Y}):Se)},[]),ee=s.rects.reference,P=s.rects.popper,le=new Map,pe=!0,g=d[0],b=0;b<d.length;b++){var x=d[b],k=nt(x),S=Pt(x)===At,m=[De,Be].indexOf(k)>=0,U=m?"width":"height",M=Wt(s,{placement:x,boundary:O,rootBoundary:F,altBoundary:V,padding:C}),K=m?S?Ne:Pe:S?Be:De;ee[U]>P[U]&&(K=qt(K));var ge=qt(K),me=[];if(v&&me.push(M[k]<=0),D&&me.push(M[K]<=0,M[ge]<=0),me.every(function(ke){return ke})){g=x,pe=!1;break}le.set(x,me)}if(pe)for(var Ee=B?3:1,Ue=function(Se){var Me=d.find(function(Fe){var X=le.get(Fe);if(X)return X.slice(0,Se).every(function(Ie){return Ie})});if(Me)return g=Me,"break"},Ae=Ee;Ae>0;Ae--){var Oe=Ue(Ae);if(Oe==="break")break}s.placement!==g&&(s.modifiersData[a]._skip=!0,s.placement=g,s.reset=!0)}}const Ka={name:"flip",enabled:!0,phase:"main",fn:Ya,requiresIfExists:["offset"],data:{_skip:!1}};function $s(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function Ss(e){return[De,Ne,Be,Pe].some(function(s){return e[s]>=0})}function Xa(e){var s=e.state,t=e.name,a=s.rects.reference,l=s.rects.popper,v=s.modifiersData.preventOverflow,T=Wt(s,{elementContext:"reference"}),D=Wt(s,{altBoundary:!0}),w=$s(T,a),C=$s(D,l,v),O=Ss(w),F=Ss(C);s.modifiersData[t]={referenceClippingOffsets:w,popperEscapeOffsets:C,isReferenceHidden:O,hasPopperEscaped:F},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":O,"data-popper-escaped":F})}const Ga={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Xa};function Za(e,s,t){var a=nt(e),l=[Pe,De].indexOf(a)>=0?-1:1,v=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,T=v[0],D=v[1];return T=T||0,D=(D||0)*l,[Pe,Ne].indexOf(a)>=0?{x:D,y:T}:{x:T,y:D}}function Qa(e){var s=e.state,t=e.options,a=e.name,l=t.offset,v=l===void 0?[0,0]:l,T=Ns.reduce(function(O,F){return O[F]=Za(F,s.rects,v),O},{}),D=T[s.placement],w=D.x,C=D.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=w,s.modifiersData.popperOffsets.y+=C),s.modifiersData[a]=T}const er={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Qa};function tr(e){var s=e.state,t=e.name;s.modifiersData[t]=Xs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const sr={name:"popperOffsets",enabled:!0,phase:"read",fn:tr,data:{}};function nr(e){return e==="x"?"y":"x"}function ar(e){var s=e.state,t=e.options,a=e.name,l=t.mainAxis,v=l===void 0?!0:l,T=t.altAxis,D=T===void 0?!1:T,w=t.boundary,C=t.rootBoundary,O=t.altBoundary,F=t.padding,V=t.tether,I=V===void 0?!0:V,B=t.tetherOffset,Y=B===void 0?0:B,N=Wt(s,{boundary:w,rootBoundary:C,padding:F,altBoundary:O}),se=nt(s.placement),ne=Pt(s.placement),oe=!ne,d=ds(se),ee=nr(d),P=s.modifiersData.popperOffsets,le=s.rects.reference,pe=s.rects.popper,g=typeof Y=="function"?Y(Object.assign({},s.rects,{placement:s.placement})):Y,b=typeof g=="number"?{mainAxis:g,altAxis:g}:Object.assign({mainAxis:0,altAxis:0},g),x=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,k={x:0,y:0};if(P){if(v){var S,m=d==="y"?De:Pe,U=d==="y"?Be:Ne,M=d==="y"?"height":"width",K=P[d],ge=K+N[m],me=K-N[U],Ee=I?-pe[M]/2:0,Ue=ne===At?le[M]:pe[M],Ae=ne===At?-pe[M]:-le[M],Oe=s.elements.arrow,ke=I&&Oe?us(Oe):{width:0,height:0},Se=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:qs(),Me=Se[m],Fe=Se[U],X=Ut(0,le[M],ke[M]),Ie=oe?le[M]/2-Ee-X-Me-b.mainAxis:Ue-X-Me-b.mainAxis,xe=oe?-le[M]/2+Ee+X+Fe+b.mainAxis:Ae+X+Fe+b.mainAxis,rt=s.elements.arrow&&Nt(s.elements.arrow),Ge=rt?d==="y"?rt.clientTop||0:rt.clientLeft||0:0,Le=(S=x==null?void 0:x[d])!=null?S:0,ve=K+Ie-Le-Ge,dt=K+xe-Le,Ze=Ut(I?Yt(ge,ve):ge,K,I?wt(me,dt):me);P[d]=Ze,k[d]=Ze-K}if(D){var be,ot=d==="x"?De:Pe,Ve=d==="x"?Be:Ne,_e=P[ee],je=ee==="y"?"height":"width",Qe=_e+N[ot],He=_e-N[Ve],et=[De,Pe].indexOf(se)!==-1,it=(be=x==null?void 0:x[ee])!=null?be:0,tt=et?Qe:_e-le[je]-pe[je]-it+b.altAxis,pt=et?_e+le[je]+pe[je]-it-b.altAxis:He,lt=I&&et?Ea(tt,_e,pt):Ut(I?tt:Qe,_e,I?pt:He);P[ee]=lt,k[ee]=lt-_e}s.modifiersData[a]=k}}const rr={name:"preventOverflow",enabled:!0,phase:"main",fn:ar,requiresIfExists:["offset"]};function or(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function ir(e){return e===Re(e)||!We(e)?ps(e):or(e)}function lr(e){var s=e.getBoundingClientRect(),t=Mt(s.width)/e.offsetWidth||1,a=Mt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function cr(e,s,t){t===void 0&&(t=!1);var a=We(s),l=We(s)&&lr(s),v=gt(s),T=Dt(e,l,t),D={scrollLeft:0,scrollTop:0},w={x:0,y:0};return(a||!a&&!t)&&((at(s)!=="body"||hs(v))&&(D=ir(s)),We(s)?(w=Dt(s,!0),w.x+=s.clientLeft,w.y+=s.clientTop):v&&(w.x=fs(v))),{x:T.left+D.scrollLeft-w.x,y:T.top+D.scrollTop-w.y,width:T.width,height:T.height}}function ur(e){var s=new Map,t=new Set,a=[];e.forEach(function(v){s.set(v.name,v)});function l(v){t.add(v.name);var T=[].concat(v.requires||[],v.requiresIfExists||[]);T.forEach(function(D){if(!t.has(D)){var w=s.get(D);w&&l(w)}}),a.push(v)}return e.forEach(function(v){t.has(v.name)||l(v)}),a}function dr(e){var s=ur(e);return Ta.reduce(function(t,a){return t.concat(s.filter(function(l){return l.phase===a}))},[])}function pr(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function fr(e){var s=e.reduce(function(t,a){var l=t[a.name];return t[a.name]=l?Object.assign({},l,a,{options:Object.assign({},l.options,a.options),data:Object.assign({},l.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var _s={placement:"bottom",modifiers:[],strategy:"absolute"};function Es(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function hr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,l=s.defaultOptions,v=l===void 0?_s:l;return function(D,w,C){C===void 0&&(C=v);var O={placement:"bottom",orderedModifiers:[],options:Object.assign({},_s,v),modifiersData:{},elements:{reference:D,popper:w},attributes:{},styles:{}},F=[],V=!1,I={state:O,setOptions:function(se){var ne=typeof se=="function"?se(O.options):se;Y(),O.options=Object.assign({},v,O.options,ne),O.scrollParents={reference:xt(D)?Ft(D):D.contextElement?Ft(D.contextElement):[],popper:Ft(w)};var oe=dr(fr([].concat(a,O.options.modifiers)));return O.orderedModifiers=oe.filter(function(d){return d.enabled}),B(),I.update()},forceUpdate:function(){if(!V){var se=O.elements,ne=se.reference,oe=se.popper;if(Es(ne,oe)){O.rects={reference:cr(ne,Nt(oe),O.options.strategy==="fixed"),popper:us(oe)},O.reset=!1,O.placement=O.options.placement,O.orderedModifiers.forEach(function(b){return O.modifiersData[b.name]=Object.assign({},b.data)});for(var d=0;d<O.orderedModifiers.length;d++){if(O.reset===!0){O.reset=!1,d=-1;continue}var ee=O.orderedModifiers[d],P=ee.fn,le=ee.options,pe=le===void 0?{}:le,g=ee.name;typeof P=="function"&&(O=P({state:O,options:pe,name:g,instance:I})||O)}}}},update:pr(function(){return new Promise(function(N){I.forceUpdate(),N(O)})}),destroy:function(){Y(),V=!0}};if(!Es(D,w))return I;I.setOptions(C).then(function(N){!V&&C.onFirstUpdate&&C.onFirstUpdate(N)});function B(){O.orderedModifiers.forEach(function(N){var se=N.name,ne=N.options,oe=ne===void 0?{}:ne,d=N.effect;if(typeof d=="function"){var ee=d({state:O,name:se,instance:I,options:oe}),P=function(){};F.push(ee||P)}})}function Y(){F.forEach(function(N){return N()}),F=[]}return I}}var gr=[La,sr,Ua,Vs,er,Ka,rr,Pa,Ga],vr=hr({defaultModifiers:gr}),mr="tippy-box",Gs="tippy-content",br="tippy-backdrop",Zs="tippy-arrow",Qs="tippy-svg-arrow",bt={passive:!0,capture:!0},en=function(){return document.body};function es(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function gs(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function tn(e,s){return typeof e=="function"?e.apply(void 0,s):e}function As(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function wr(e){return e.split(/\s+/).filter(Boolean)}function Et(e){return[].concat(e)}function Ms(e,s){e.indexOf(s)===-1&&e.push(s)}function yr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function xr(e){return e.split("-")[0]}function Kt(e){return[].slice.call(e)}function Ds(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Lt(){return document.createElement("div")}function Gt(e){return["Element","Fragment"].some(function(s){return gs(e,s)})}function kr(e){return gs(e,"NodeList")}function Tr(e){return gs(e,"MouseEvent")}function Cr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function $r(e){return Gt(e)?[e]:kr(e)?Kt(e):Array.isArray(e)?e:Kt(document.querySelectorAll(e))}function ts(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function Ps(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function Sr(e){var s,t=Et(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function _r(e,s){var t=s.clientX,a=s.clientY;return e.every(function(l){var v=l.popperRect,T=l.popperState,D=l.props,w=D.interactiveBorder,C=xr(T.placement),O=T.modifiersData.offset;if(!O)return!0;var F=C==="bottom"?O.top.y:0,V=C==="top"?O.bottom.y:0,I=C==="right"?O.left.x:0,B=C==="left"?O.right.x:0,Y=v.top-a+F>w,N=a-v.bottom-V>w,se=v.left-t+I>w,ne=t-v.right-B>w;return Y||N||se||ne})}function ss(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(l){e[a](l,t)})}function Os(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var st={isTouch:!1},Is=0;function Er(){st.isTouch||(st.isTouch=!0,window.performance&&document.addEventListener("mousemove",sn))}function sn(){var e=performance.now();e-Is<20&&(st.isTouch=!1,document.removeEventListener("mousemove",sn)),Is=e}function Ar(){var e=document.activeElement;if(Cr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function Mr(){document.addEventListener("touchstart",Er,bt),window.addEventListener("blur",Ar)}var Dr=typeof window<"u"&&typeof document<"u",Pr=Dr?!!window.msCrypto:!1,Or={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ir={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Xe=Object.assign({appendTo:en,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Or,Ir),Rr=Object.keys(Xe),Ur=function(s){var t=Object.keys(s);t.forEach(function(a){Xe[a]=s[a]})};function nn(e){var s=e.plugins||[],t=s.reduce(function(a,l){var v=l.name,T=l.defaultValue;if(v){var D;a[v]=e[v]!==void 0?e[v]:(D=Xe[v])!=null?D:T}return a},{});return Object.assign({},e,t)}function Fr(e,s){var t=s?Object.keys(nn(Object.assign({},Xe,{plugins:s}))):Rr,a=t.reduce(function(l,v){var T=(e.getAttribute("data-tippy-"+v)||"").trim();if(!T)return l;if(v==="content")l[v]=T;else try{l[v]=JSON.parse(T)}catch{l[v]=T}return l},{});return a}function Rs(e,s){var t=Object.assign({},s,{content:tn(s.content,[e])},s.ignoreAttributes?{}:Fr(e,s.plugins));return t.aria=Object.assign({},Xe.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Lr=function(){return"innerHTML"};function os(e,s){e[Lr()]=s}function Us(e){var s=Lt();return e===!0?s.className=Zs:(s.className=Qs,Gt(e)?s.appendChild(e):os(s,e)),s}function Fs(e,s){Gt(s.content)?(os(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?os(e,s.content):e.textContent=s.content)}function is(e){var s=e.firstElementChild,t=Kt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(Gs)}),arrow:t.find(function(a){return a.classList.contains(Zs)||a.classList.contains(Qs)}),backdrop:t.find(function(a){return a.classList.contains(br)})}}function an(e){var s=Lt(),t=Lt();t.className=mr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=Lt();a.className=Gs,a.setAttribute("data-state","hidden"),Fs(a,e.props),s.appendChild(t),t.appendChild(a),l(e.props,e.props);function l(v,T){var D=is(s),w=D.box,C=D.content,O=D.arrow;T.theme?w.setAttribute("data-theme",T.theme):w.removeAttribute("data-theme"),typeof T.animation=="string"?w.setAttribute("data-animation",T.animation):w.removeAttribute("data-animation"),T.inertia?w.setAttribute("data-inertia",""):w.removeAttribute("data-inertia"),w.style.maxWidth=typeof T.maxWidth=="number"?T.maxWidth+"px":T.maxWidth,T.role?w.setAttribute("role",T.role):w.removeAttribute("role"),(v.content!==T.content||v.allowHTML!==T.allowHTML)&&Fs(C,e.props),T.arrow?O?v.arrow!==T.arrow&&(w.removeChild(O),w.appendChild(Us(T.arrow))):w.appendChild(Us(T.arrow)):O&&w.removeChild(O)}return{popper:s,onUpdate:l}}an.$$tippy=!0;var jr=1,zt=[],ns=[];function Wr(e,s){var t=Rs(e,Object.assign({},Xe,nn(Ds(s)))),a,l,v,T=!1,D=!1,w=!1,C=!1,O,F,V,I=[],B=As(ve,t.interactiveDebounce),Y,N=jr++,se=null,ne=yr(t.plugins),oe={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},d={id:N,reference:e,popper:Lt(),popperInstance:se,props:t,state:oe,plugins:ne,clearDelayTimeouts:tt,setProps:pt,setContent:lt,show:Ot,hide:kt,hideWithInteractivity:Tt,enable:et,disable:it,unmount:Ct,destroy:ft};if(!t.render)return d;var ee=t.render(d),P=ee.popper,le=ee.onUpdate;P.setAttribute("data-tippy-root",""),P.id="tippy-"+d.id,d.popper=P,e._tippy=d,P._tippy=d;var pe=ne.map(function(y){return y.fn(d)}),g=e.hasAttribute("aria-expanded");return rt(),Ee(),K(),ge("onCreate",[d]),t.showOnCreate&&Qe(),P.addEventListener("mouseenter",function(){d.props.interactive&&d.state.isVisible&&d.clearDelayTimeouts()}),P.addEventListener("mouseleave",function(){d.props.interactive&&d.props.trigger.indexOf("mouseenter")>=0&&m().addEventListener("mousemove",B)}),d;function b(){var y=d.props.touch;return Array.isArray(y)?y:[y,0]}function x(){return b()[0]==="hold"}function k(){var y;return!!((y=d.props.render)!=null&&y.$$tippy)}function S(){return Y||e}function m(){var y=S().parentNode;return y?Sr(y):document}function U(){return is(P)}function M(y){return d.state.isMounted&&!d.state.isVisible||st.isTouch||O&&O.type==="focus"?0:es(d.props.delay,y?0:1,Xe.delay)}function K(y){y===void 0&&(y=!1),P.style.pointerEvents=d.props.interactive&&!y?"":"none",P.style.zIndex=""+d.props.zIndex}function ge(y,W,H){if(H===void 0&&(H=!0),pe.forEach(function(re){re[y]&&re[y].apply(re,W)}),H){var ie;(ie=d.props)[y].apply(ie,W)}}function me(){var y=d.props.aria;if(y.content){var W="aria-"+y.content,H=P.id,ie=Et(d.props.triggerTarget||e);ie.forEach(function(re){var we=re.getAttribute(W);if(d.state.isVisible)re.setAttribute(W,we?we+" "+H:H);else{var Te=we&&we.replace(H,"").trim();Te?re.setAttribute(W,Te):re.removeAttribute(W)}})}}function Ee(){if(!(g||!d.props.aria.expanded)){var y=Et(d.props.triggerTarget||e);y.forEach(function(W){d.props.interactive?W.setAttribute("aria-expanded",d.state.isVisible&&W===S()?"true":"false"):W.removeAttribute("aria-expanded")})}}function Ue(){m().removeEventListener("mousemove",B),zt=zt.filter(function(y){return y!==B})}function Ae(y){if(!(st.isTouch&&(w||y.type==="mousedown"))){var W=y.composedPath&&y.composedPath()[0]||y.target;if(!(d.props.interactive&&Os(P,W))){if(Et(d.props.triggerTarget||e).some(function(H){return Os(H,W)})){if(st.isTouch||d.state.isVisible&&d.props.trigger.indexOf("click")>=0)return}else ge("onClickOutside",[d,y]);d.props.hideOnClick===!0&&(d.clearDelayTimeouts(),d.hide(),D=!0,setTimeout(function(){D=!1}),d.state.isMounted||Me())}}}function Oe(){w=!0}function ke(){w=!1}function Se(){var y=m();y.addEventListener("mousedown",Ae,!0),y.addEventListener("touchend",Ae,bt),y.addEventListener("touchstart",ke,bt),y.addEventListener("touchmove",Oe,bt)}function Me(){var y=m();y.removeEventListener("mousedown",Ae,!0),y.removeEventListener("touchend",Ae,bt),y.removeEventListener("touchstart",ke,bt),y.removeEventListener("touchmove",Oe,bt)}function Fe(y,W){Ie(y,function(){!d.state.isVisible&&P.parentNode&&P.parentNode.contains(P)&&W()})}function X(y,W){Ie(y,W)}function Ie(y,W){var H=U().box;function ie(re){re.target===H&&(ss(H,"remove",ie),W())}if(y===0)return W();ss(H,"remove",F),ss(H,"add",ie),F=ie}function xe(y,W,H){H===void 0&&(H=!1);var ie=Et(d.props.triggerTarget||e);ie.forEach(function(re){re.addEventListener(y,W,H),I.push({node:re,eventType:y,handler:W,options:H})})}function rt(){x()&&(xe("touchstart",Le,{passive:!0}),xe("touchend",dt,{passive:!0})),wr(d.props.trigger).forEach(function(y){if(y!=="manual")switch(xe(y,Le),y){case"mouseenter":xe("mouseleave",dt);break;case"focus":xe(Pr?"focusout":"blur",Ze);break;case"focusin":xe("focusout",Ze);break}})}function Ge(){I.forEach(function(y){var W=y.node,H=y.eventType,ie=y.handler,re=y.options;W.removeEventListener(H,ie,re)}),I=[]}function Le(y){var W,H=!1;if(!(!d.state.isEnabled||be(y)||D)){var ie=((W=O)==null?void 0:W.type)==="focus";O=y,Y=y.currentTarget,Ee(),!d.state.isVisible&&Tr(y)&&zt.forEach(function(re){return re(y)}),y.type==="click"&&(d.props.trigger.indexOf("mouseenter")<0||T)&&d.props.hideOnClick!==!1&&d.state.isVisible?H=!0:Qe(y),y.type==="click"&&(T=!H),H&&!ie&&He(y)}}function ve(y){var W=y.target,H=S().contains(W)||P.contains(W);if(!(y.type==="mousemove"&&H)){var ie=je().concat(P).map(function(re){var we,Te=re._tippy,ze=(we=Te.popperInstance)==null?void 0:we.state;return ze?{popperRect:re.getBoundingClientRect(),popperState:ze,props:t}:null}).filter(Boolean);_r(ie,y)&&(Ue(),He(y))}}function dt(y){var W=be(y)||d.props.trigger.indexOf("click")>=0&&T;if(!W){if(d.props.interactive){d.hideWithInteractivity(y);return}He(y)}}function Ze(y){d.props.trigger.indexOf("focusin")<0&&y.target!==S()||d.props.interactive&&y.relatedTarget&&P.contains(y.relatedTarget)||He(y)}function be(y){return st.isTouch?x()!==y.type.indexOf("touch")>=0:!1}function ot(){Ve();var y=d.props,W=y.popperOptions,H=y.placement,ie=y.offset,re=y.getReferenceClientRect,we=y.moveTransition,Te=k()?is(P).arrow:null,ze=re?{getBoundingClientRect:re,contextElement:re.contextElement||S()}:e,A={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(G){var ue=G.state;if(k()){var n=U(),i=n.box;["placement","reference-hidden","escaped"].forEach(function(r){r==="placement"?i.setAttribute("data-placement",ue.placement):ue.attributes.popper["data-popper-"+r]?i.setAttribute("data-"+r,""):i.removeAttribute("data-"+r)}),ue.attributes.popper={}}}},h=[{name:"offset",options:{offset:ie}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!we}},A];k()&&Te&&h.push({name:"arrow",options:{element:Te,padding:3}}),h.push.apply(h,(W==null?void 0:W.modifiers)||[]),d.popperInstance=vr(ze,P,Object.assign({},W,{placement:H,onFirstUpdate:V,modifiers:h}))}function Ve(){d.popperInstance&&(d.popperInstance.destroy(),d.popperInstance=null)}function _e(){var y=d.props.appendTo,W,H=S();d.props.interactive&&y===en||y==="parent"?W=H.parentNode:W=tn(y,[H]),W.contains(P)||W.appendChild(P),d.state.isMounted=!0,ot()}function je(){return Kt(P.querySelectorAll("[data-tippy-root]"))}function Qe(y){d.clearDelayTimeouts(),y&&ge("onTrigger",[d,y]),Se();var W=M(!0),H=b(),ie=H[0],re=H[1];st.isTouch&&ie==="hold"&&re&&(W=re),W?a=setTimeout(function(){d.show()},W):d.show()}function He(y){if(d.clearDelayTimeouts(),ge("onUntrigger",[d,y]),!d.state.isVisible){Me();return}if(!(d.props.trigger.indexOf("mouseenter")>=0&&d.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(y.type)>=0&&T)){var W=M(!1);W?l=setTimeout(function(){d.state.isVisible&&d.hide()},W):v=requestAnimationFrame(function(){d.hide()})}}function et(){d.state.isEnabled=!0}function it(){d.hide(),d.state.isEnabled=!1}function tt(){clearTimeout(a),clearTimeout(l),cancelAnimationFrame(v)}function pt(y){if(!d.state.isDestroyed){ge("onBeforeUpdate",[d,y]),Ge();var W=d.props,H=Rs(e,Object.assign({},W,Ds(y),{ignoreAttributes:!0}));d.props=H,rt(),W.interactiveDebounce!==H.interactiveDebounce&&(Ue(),B=As(ve,H.interactiveDebounce)),W.triggerTarget&&!H.triggerTarget?Et(W.triggerTarget).forEach(function(ie){ie.removeAttribute("aria-expanded")}):H.triggerTarget&&e.removeAttribute("aria-expanded"),Ee(),K(),le&&le(W,H),d.popperInstance&&(ot(),je().forEach(function(ie){requestAnimationFrame(ie._tippy.popperInstance.forceUpdate)})),ge("onAfterUpdate",[d,y])}}function lt(y){d.setProps({content:y})}function Ot(){var y=d.state.isVisible,W=d.state.isDestroyed,H=!d.state.isEnabled,ie=st.isTouch&&!d.props.touch,re=es(d.props.duration,0,Xe.duration);if(!(y||W||H||ie)&&!S().hasAttribute("disabled")&&(ge("onShow",[d],!1),d.props.onShow(d)!==!1)){if(d.state.isVisible=!0,k()&&(P.style.visibility="visible"),K(),Se(),d.state.isMounted||(P.style.transition="none"),k()){var we=U(),Te=we.box,ze=we.content;ts([Te,ze],0)}V=function(){var h;if(!(!d.state.isVisible||C)){if(C=!0,P.offsetHeight,P.style.transition=d.props.moveTransition,k()&&d.props.animation){var J=U(),G=J.box,ue=J.content;ts([G,ue],re),Ps([G,ue],"visible")}me(),Ee(),Ms(ns,d),(h=d.popperInstance)==null||h.forceUpdate(),ge("onMount",[d]),d.props.animation&&k()&&X(re,function(){d.state.isShown=!0,ge("onShown",[d])})}},_e()}}function kt(){var y=!d.state.isVisible,W=d.state.isDestroyed,H=!d.state.isEnabled,ie=es(d.props.duration,1,Xe.duration);if(!(y||W||H)&&(ge("onHide",[d],!1),d.props.onHide(d)!==!1)){if(d.state.isVisible=!1,d.state.isShown=!1,C=!1,T=!1,k()&&(P.style.visibility="hidden"),Ue(),Me(),K(!0),k()){var re=U(),we=re.box,Te=re.content;d.props.animation&&(ts([we,Te],ie),Ps([we,Te],"hidden"))}me(),Ee(),d.props.animation?k()&&Fe(ie,d.unmount):d.unmount()}}function Tt(y){m().addEventListener("mousemove",B),Ms(zt,B),B(y)}function Ct(){d.state.isVisible&&d.hide(),d.state.isMounted&&(Ve(),je().forEach(function(y){y._tippy.unmount()}),P.parentNode&&P.parentNode.removeChild(P),ns=ns.filter(function(y){return y!==d}),d.state.isMounted=!1,ge("onHidden",[d]))}function ft(){d.state.isDestroyed||(d.clearDelayTimeouts(),d.unmount(),Ge(),delete e._tippy,d.state.isDestroyed=!0,ge("onDestroy",[d]))}}function yt(e,s){s===void 0&&(s={});var t=Xe.plugins.concat(s.plugins||[]);Mr();var a=Object.assign({},s,{plugins:t}),l=$r(e),v=l.reduce(function(T,D){var w=D&&Wr(D,a);return w&&T.push(w),T},[]);return Gt(e)?v[0]:v}yt.defaultProps=Xe;yt.setDefaultProps=Ur;yt.currentInput=st;Object.assign({},Vs,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});yt.setDefaultProps({render:an});const Br={class:"task-pane"},Nr={key:0,class:"loading-overlay"},Vr={key:1,class:"format-error-overlay"},Hr={class:"format-error-content"},zr={class:"format-error-message"},qr={class:"format-error-actions"},Jr={class:"doc-header"},Yr={class:"doc-title"},Kr={class:"action-area"},Xr={class:"select-container"},Gr={class:"select-group"},Zr=["disabled"],Qr=["value"],eo={class:"select-group"},to=["disabled"],so=["value"],no=["title"],ao={key:0,class:"science-warning"},ro={class:"action-buttons"},oo=["disabled"],io={class:"btn-content"},lo={key:0,class:"button-loader"},co=["disabled"],uo={class:"btn-content"},po={key:0,class:"button-loader"},fo=["disabled"],ho={class:"btn-content"},go={key:0,class:"button-loader"},vo={class:"content-area"},mo={class:"modal-header"},bo={class:"modal-body"},wo={class:"selection-content"},yo={class:"modal-header"},xo={class:"modal-body"},ko={class:"alert-message"},To={class:"alert-actions"},Co={key:2,class:"modal-overlay"},$o={class:"modal-header"},So={class:"modal-body"},_o={class:"confirm-message"},Eo={class:"confirm-actions"},Ao={class:"modal-header"},Mo={class:"modal-title"},Do={class:"modal-body"},Po={class:"alert-message"},Oo={class:"alert-actions"},Io={class:"task-queue"},Ro={class:"queue-header"},Uo={class:"queue-status-filter"},Fo=["value"],Lo={class:"queue-actions"},jo=["disabled","title"],Wo={class:"task-count"},Bo={key:0,class:"queue-table-container"},No={class:"col-id"},Vo={class:"id-header"},Ho={key:0,class:"col-subject"},zo={class:"subject-header"},qo={class:"switch"},Jo=["title"],Yo={key:1,class:"col-status"},Ko=["onClick"],Xo={class:"col-id"},Go={class:"id-cell group-cell"},Zo={class:"group-toggle-icon"},Qo={class:"group-label"},ei={key:0,class:"col-subject"},ti={class:"subject-cell group-subject"},si={key:1,class:"col-status"},ni={class:"col-actions"},ai={class:"task-actions"},ri={class:"group-action-text"},oi=["onClick"],ii={class:"col-id"},li={class:"id-content"},ci={class:"task-id"},ui={key:0,class:"analysis-task-icon",title:"解析任务"},di={key:1,class:"enhance-task-icon",title:"增强解析任务"},pi={key:2,class:"check-task-icon",title:"校对任务"},fi={key:0,class:"status-in-id"},hi={key:0,class:"col-subject"},gi=["onMouseenter"],vi={key:1,class:"col-status"},mi={class:"status-cell"},bi=["onClick"],wi={class:"col-id"},yi={class:"id-content"},xi={class:"task-id"},ki={key:0,class:"analysis-task-icon",title:"解析任务"},Ti={key:1,class:"enhance-task-icon",title:"增强解析任务"},Ci={key:2,class:"check-task-icon",title:"校对任务"},$i={key:0,class:"status-in-id"},Si={key:0,class:"col-subject"},_i=["onMouseenter"],Ei={key:1,class:"col-status"},Ai={class:"status-cell"},Mi={class:"col-actions"},Di={class:"task-actions"},Pi=["onClick"],Oi=["onClick"],Ii={key:2,class:"no-action-icon",title:"无可用操作"},Ri={key:1,class:"empty-queue"},Ui={key:4,class:"log-container"},Fi={class:"log-actions"},Li={class:"toggle-icon"},ji=["innerHTML"],Wi={__name:"TaskPane",setup(e){const s=fe(!1),t=fe(!1),a=fe(!1),l=fe(""),v=fe(!1),T=fe(!1),D=fe(!1),w=fe(!1),C=fe(!0),O=fe(""),F=fe(!1),V=fe(window.innerWidth),I=ct(()=>V.value<750),B=ct(()=>V.value<380),Y=()=>{V.value=window.innerWidth},N=fe(null),se=fe(!1),ne=fe(""),oe=fe(new Set);fe(!1);const d={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},ee=fe(!1),P=fe([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"}]),{docName:le,selected:pe,logger:g,map:b,subject:x,stage:k,subjectOptions:S,stageOptions:m,appConfig:U,clearLog:M,checkDocumentFormat:K,getTaskStatusClass:ge,getTaskStatusText:me,terminateTask:Ee,run1:Ue,runCheck:Ae,setupLifecycle:Oe,navigateToTaskControl:ke,isLoading:Se,tryRemoveTaskPlaceholderWithLoading:Me,confirmDialog:Fe,handleConfirm:X,errorDialog:Ie,hideErrorDialog:xe,getCompletedTasksCount:rt,getReleasableTasksCount:Ge,showConfirm:Le}=Tn(),ve=fe(null);(()=>{var A;try{if((A=window.Application)!=null&&A.PluginStorage){const h=window.Application.PluginStorage.getItem("user_info");h?(ve.value=JSON.parse(h),console.log("用户信息已加载:",ve.value)):console.log("未找到用户信息")}}catch(h){console.error("解析用户信息时出错:",h)}})(),Jt(ve,A=>{A&&A.orgs&&A.orgs[0]&&console.log(`用户企业ID: ${A.orgs[0].orgId}, 校对功能${A.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Ze=ct(()=>!ve.value||ve.value.isAdmin||ve.value.isOwner?S:ve.value.subject?S.filter(A=>A.value===ve.value.subject):S),be=()=>{ve.value&&!ve.value.isAdmin&&!ve.value.isOwner&&ve.value.subject&&(x.value=ve.value.subject)},ot=ct(()=>["physics","chemistry","biology","math"].includes(x.value));Jt(U,A=>{A&&(console.log("TaskPane组件收到应用配置更新:",A),console.log("当前版本类型:",A.EDITION),console.log("当前年级选项:",m.value))},{deep:!0,immediate:!0});const Ve=()=>{try{const A=K();C.value=A.isValid,O.value=A.message,F.value=!A.isValid,A.isValid||console.warn("文档格式检查失败:",A.message)}catch(A){console.error("执行文档格式检查时出错:",A),C.value=!1,O.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",F.value=!0}},_e=ct(()=>{let A={};const h=b;if(ne.value==="")A={...h};else for(const J in h)if(Object.prototype.hasOwnProperty.call(h,J)){const G=h[J];G.status===ne.value&&(A[J]=G)}return se.value&&N.value?A[N.value]?{[N.value]:A[N.value]}:{}:A}),je=ct(()=>{const A=_e.value;return Object.entries(A).map(([J,G])=>({tid:J,...G})).sort((J,G)=>{const ue=J.startTime||0;return(G.startTime||0)-ue}).reduce((J,G)=>{const{tid:ue,...n}=G;return J[ue]=n,J},{})}),Qe=ct(()=>{const A=je.value,h=Object.entries(A).map(([n,i])=>({tid:n,...i})),J=h.filter(n=>n.status===3),G=h.filter(n=>n.status!==3),ue=[];if(G.forEach(n=>{ue.push({type:"task",...n})}),J.length>=2){const n="released-group-all",i=oe.value.has(n);ue.push({type:"group",groupId:n,isCollapsed:i,tasks:J,count:J.length})}else J.forEach(n=>{ue.push({type:"task",...n})});return ue}),He=A=>{oe.value.has(A)?oe.value.delete(A):oe.value.add(A)},et=(A="wps-analysis")=>{x.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(l.value="未选中内容",t.value=!0):(A==="wps-analysis"?T.value=!0:A==="wps-enhance_analysis"&&(D.value=!0),Ue(A).catch(h=>{console.log(h),h.message.includes("重叠")?(l.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",h),l.value=h.message,t.value=!0)}).finally(()=>{A==="wps-analysis"?T.value=!1:A==="wps-enhance_analysis"&&(D.value=!1)})):(l.value="请选择学科",t.value=!0)},it=()=>{x.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(l.value="未选中内容",t.value=!0):(w.value=!0,Ae().catch(A=>{console.log(A),A.message.includes("重叠")?(l.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",A),l.value=A.message,t.value=!0)}).finally(()=>{w.value=!1})):(l.value="请选择学科",t.value=!0)},tt=(A,h)=>{N.value=A,ke(A)},pt=A=>{b[A]&&(b[A].status=3),N.value===A&&(N.value=null),Me(A,!0)},lt=async()=>{const A=Object.entries(b).filter(([h,J])=>J.status===2||J.status===4);if(A.length===0){l.value="没有可释放的任务",t.value=!0;return}try{if(await Le(`确定要释放所有 ${A.length} 个可释放的任务吗？
此操作不可撤销。`)){let J=0;A.forEach(([G,ue])=>{b[G]&&(b[G].status=3,N.value===G&&(N.value=null),Me(G,!0),J++)}),l.value=`已成功释放 ${J} 个任务`,t.value=!0}}catch(h){console.error("释放任务时出错:",h),l.value="释放任务时出现错误",t.value=!0}},Ot=()=>{v.value=!v.value},kt=A=>A?A.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Tt=A=>{const h=Ct(A);return h?kt(h):"无题目内容"},Ct=A=>{if(!A.selectedText)return"";const h=A.selectedText.split("\r").filter(G=>G.trim());if(h.length===1){const G=A.selectedText.split(`
`).filter(ue=>ue.trim());G.length>1&&h.splice(0,1,...G)}const J=h.map((G,ue)=>{const n=G.trim();return n.length>200,n});return J.length===1?h[0].trim():J.join(`
`)},ft=(A,h)=>{if(!ee.value)return;const J=A.target,G=Ct(h).toString();if(!G||G.trim()===""){console.log("题目内容为空，不显示tooltip");return}const ue=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${G.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,n=A.clientX,i=A.clientY;if(d.subjects.has(J)){const u=d.subjects.get(J);u.setContent(ue),u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:n,right:n})}),u.show();return}const r=yt(J,{content:ue,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:n,right:n}),onHidden:()=>{r.setProps({getReferenceClientRect:null})}});d.subjects.set(J,r),r.show()},y=A=>{const h=A.currentTarget,J=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,G=A.clientX,ue=A.clientY;if(d.enhance.has(h)){const i=d.enhance.get(h);i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ue,bottom:ue,left:G,right:G})}),i.show();return}const n=yt(h,{content:J,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});d.enhance.set(h,n),n.show()},W=()=>{d.subjects.forEach(A=>{A.destroy()}),d.subjects.clear(),d.enhance.forEach(A=>{A.destroy()}),d.enhance.clear(),d.softBreak.forEach(A=>{A.destroy()}),d.softBreak.clear(),document.removeEventListener("click",H),document.removeEventListener("mousemove",re)},H=A=>{const h=document.querySelector(".tippy-box");h&&!h.contains(A.target)&&(d.subjects.forEach(J=>J.hide()),d.enhance.forEach(J=>J.hide()),d.softBreak.forEach(J=>J.hide()))};let ie=0;const re=A=>{const h=Date.now();if(h-ie<100)return;ie=h;const J=document.querySelector(".tippy-box");if(!J)return;const G=J.getBoundingClientRect();!(A.clientX>=G.left-20&&A.clientX<=G.right+20&&A.clientY>=G.top-20&&A.clientY<=G.bottom+20)&&!J.matches(":hover")&&(d.subjects.forEach(n=>n.hide()),d.enhance.forEach(n=>n.hide()),d.softBreak.forEach(n=>n.hide()))},we=()=>{document.addEventListener("click",H),document.addEventListener("mousemove",re)};Ls(()=>{window.addEventListener("resize",Y),we(),be(),setTimeout(()=>{Ve()},500);const A=document.createElement("style");A.id="tippy-custom-styles",A.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(A)}),un(()=>{window.removeEventListener("resize",Y),W();const A=document.getElementById("tippy-custom-styles");A&&A.remove()}),Oe();const Te=A=>A.selectedText?A.selectedText.includes("\v")||A.selectedText.includes("\v"):!1,ze=A=>{const h=A.currentTarget,J=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,G=A.clientX,ue=A.clientY;if(d.softBreak.has(h)){const i=d.softBreak.get(h);i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ue,bottom:ue,left:G,right:G})}),i.show();return}const n=yt(h,{content:J,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});d.softBreak.set(h,n),n.show()};return ct(()=>Qe.value.some(A=>A.type==="group")),ct(()=>oe.value.size>0),(A,h)=>{var J,G,ue,n,i;return L(),j("div",Br,[de(Se)?(L(),j("div",Nr,h[31]||(h[31]=[c("div",{class:"loading-spinner"},null,-1),c("div",{class:"loading-text"},"处理中...",-1)]))):z("",!0),F.value?(L(),j("div",Vr,[c("div",Hr,[h[32]||(h[32]=c("div",{class:"format-error-icon"},"⚠️",-1)),h[33]||(h[33]=c("div",{class:"format-error-title"},"文档格式不支持",-1)),c("div",zr,ae(O.value),1),c("div",qr,[c("button",{class:"retry-check-btn",onClick:h[0]||(h[0]=r=>Ve())},"重新检查")])])])):z("",!0),c("div",Jr,[c("div",Yr,ae(de(le)||"未选择文档"),1),c("button",{class:"settings-btn",onClick:h[1]||(h[1]=r=>a.value=!0)},h[34]||(h[34]=[c("i",{class:"icon-settings"},null,-1)]))]),c("div",Kr,[c("div",Xr,[c("div",Gr,[h[35]||(h[35]=c("label",{for:"stage-select"},"年级:",-1)),Ye(c("select",{id:"stage-select","onUpdate:modelValue":h[2]||(h[2]=r=>vs(k)?k.value=r:null),class:"select-input",disabled:F.value},[(L(!0),j(mt,null,_t(de(m),r=>(L(),j("option",{key:r.value,value:r.value},ae(r.label),9,Qr))),128))],8,Zr),[[Qt,de(k)]])]),c("div",eo,[h[36]||(h[36]=c("label",{for:"subject-select"},"学科:",-1)),Ye(c("select",{id:"subject-select","onUpdate:modelValue":h[3]||(h[3]=r=>vs(x)?x.value=r:null),class:"select-input",disabled:F.value},[(L(!0),j(mt,null,_t(Ze.value,r=>(L(),j("option",{key:r.value,value:r.value},ae(r.label),9,so))),128))],8,to),[[Qt,de(x)]]),ve.value&&!ve.value.isAdmin&&!ve.value.isOwner&&ve.value.subject?(L(),j("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((J=Ze.value.find(r=>r.value===ve.value.subject))==null?void 0:J.label)||ve.value.subject} 学科`}," 🔒 ",8,no)):z("",!0)])]),ot.value?(L(),j("div",ao," 理科可使用增强模式以获取更精准的解析 ")):z("",!0),c("div",ro,[c("button",{class:"action-btn primary",onClick:h[4]||(h[4]=r=>et("wps-analysis")),disabled:T.value||F.value},[c("div",io,[T.value?(L(),j("span",lo)):z("",!0),h[37]||(h[37]=c("span",{class:"btn-text"},"解析",-1))])],8,oo),ot.value?(L(),j("button",{key:0,class:"action-btn enhance",onClick:h[5]||(h[5]=r=>et("wps-enhance_analysis")),disabled:D.value||F.value},[c("div",uo,[D.value?(L(),j("span",po)):z("",!0),h[38]||(h[38]=c("span",{class:"btn-text"},"增强解析",-1))])],8,co)):z("",!0),((ue=(G=ve.value)==null?void 0:G.orgs[0])==null?void 0:ue.orgId)===2?(L(),j("button",{key:1,class:"action-btn secondary",onClick:h[6]||(h[6]=r=>it()),disabled:w.value||F.value},[c("div",ho,[w.value?(L(),j("span",go)):z("",!0),h[39]||(h[39]=c("span",{class:"btn-text"},"校对",-1))])],8,fo)):z("",!0)])]),c("div",vo,[s.value?(L(),j("div",{key:0,class:"modal-overlay",onClick:h[9]||(h[9]=r=>s.value=!1)},[c("div",{class:"modal-content",onClick:h[8]||(h[8]=ht(()=>{},["stop"]))},[c("div",mo,[h[40]||(h[40]=c("div",{class:"modal-title"},"选中内容",-1)),c("button",{class:"modal-close",onClick:h[7]||(h[7]=r=>s.value=!1)},"×")]),c("div",bo,[c("pre",wo,ae(de(pe)||"未选中内容"),1)])])])):z("",!0),t.value?(L(),j("div",{key:1,class:"modal-overlay",onClick:h[13]||(h[13]=r=>t.value=!1)},[c("div",{class:"modal-content alert-modal",onClick:h[12]||(h[12]=ht(()=>{},["stop"]))},[c("div",yo,[h[41]||(h[41]=c("div",{class:"modal-title"},"提示",-1)),c("button",{class:"modal-close",onClick:h[10]||(h[10]=r=>t.value=!1)},"×")]),c("div",xo,[c("div",ko,ae(l.value),1),c("div",To,[c("button",{class:"action-btn primary",onClick:h[11]||(h[11]=r=>t.value=!1)},"确定")])])])])):z("",!0),de(Fe).show?(L(),j("div",Co,[c("div",{class:"modal-content confirm-modal",onClick:h[17]||(h[17]=ht(()=>{},["stop"]))},[c("div",$o,[h[42]||(h[42]=c("div",{class:"modal-title"},"确认",-1)),c("button",{class:"modal-close",onClick:h[14]||(h[14]=r=>de(X)(!1))},"×")]),c("div",So,[c("div",_o,ae(de(Fe).message),1),c("div",Eo,[c("button",{class:"action-btn secondary",onClick:h[15]||(h[15]=r=>de(X)(!1))},"取消"),c("button",{class:"action-btn primary",onClick:h[16]||(h[16]=r=>de(X)(!0))},"确定")])])])])):z("",!0),de(Ie).show?(L(),j("div",{key:3,class:"modal-overlay",onClick:h[21]||(h[21]=r=>de(xe)())},[c("div",{class:"modal-content alert-modal",onClick:h[20]||(h[20]=ht(()=>{},["stop"]))},[c("div",Ao,[c("div",Mo,ae(de(Ie).title),1),c("button",{class:"modal-close",onClick:h[18]||(h[18]=r=>de(xe)())},"×")]),c("div",Do,[c("div",Po,ae(de(Ie).message),1),c("div",Oo,[c("button",{class:"action-btn primary",onClick:h[19]||(h[19]=r=>de(xe)())},"确定")])])])])):z("",!0),c("div",Io,[c("div",Ro,[h[43]||(h[43]=c("div",{class:"queue-title"},"任务队列",-1)),c("div",Uo,[Ye(c("select",{id:"status-filter-select","onUpdate:modelValue":h[22]||(h[22]=r=>ne.value=r),class:"status-filter-select-input"},[(L(!0),j(mt,null,_t(P.value,r=>(L(),j("option",{key:r.value,value:r.value},ae(r.label),9,Fo))),128))],512),[[Qt,ne.value]])]),c("div",Lo,[c("button",{class:"release-all-btn",onClick:lt,disabled:de(Ge)()===0,title:de(Ge)()===0?"无可释放任务":`释放所有${de(Ge)()}个可释放任务`}," 一键释放 ",8,jo)]),c("div",Wo,ae(Object.keys(_e.value).length)+"个任务",1)]),Object.keys(_e.value).length>0?(L(),j("div",Bo,[c("table",{class:$e(["queue-table",{"narrow-view":I.value,"ultra-narrow-view":B.value}])},[c("thead",null,[c("tr",null,[c("th",No,[c("div",Vo,[h[45]||(h[45]=c("span",null,"任务ID",-1)),c("span",{class:"help-icon",onMouseenter:h[23]||(h[23]=r=>y(r))},h[44]||(h[44]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),I.value?z("",!0):(L(),j("th",Ho,[c("div",zo,[h[46]||(h[46]=c("span",null,"题目内容",-1)),c("label",qo,[Ye(c("input",{type:"checkbox","onUpdate:modelValue":h[24]||(h[24]=r=>ee.value=r)},null,512),[[dn,ee.value]]),c("span",{class:"slider round",title:ee.value?"关闭题目预览":"开启题目预览"},null,8,Jo)])])])),B.value?z("",!0):(L(),j("th",Yo,"状态")),h[47]||(h[47]=c("th",{class:"col-actions"},"操作",-1))])]),c("tbody",null,[(L(!0),j(mt,null,_t(Qe.value,r=>(L(),j(mt,{key:r.type==="group"?r.groupId:r.tid},[r.type==="group"?(L(),j("tr",{key:0,class:"group-row",onClick:u=>He(r.groupId)},[c("td",Xo,[c("div",Go,[c("span",Zo,ae(r.isCollapsed?"▶":"▼"),1),c("span",Qo,"已释放任务组 ("+ae(r.count)+"个)",1)])]),I.value?z("",!0):(L(),j("td",ei,[c("div",ti,ae(r.isCollapsed?"点击展开查看详情":"点击折叠隐藏详情"),1)])),B.value?z("",!0):(L(),j("td",si,h[48]||(h[48]=[c("div",{class:"status-cell"},[c("span",{class:"task-tag status-released"},"已释放")],-1)]))),c("td",ni,[c("div",ai,[c("span",ri,ae(r.isCollapsed?"展开":"折叠"),1)])])],8,Ko)):z("",!0),r.type==="group"&&!r.isCollapsed?(L(!0),j(mt,{key:1},_t(r.tasks,(u,o)=>(L(),j("tr",{key:u.tid,class:$e(["task-row group-task-row",[de(ge)(u.status),{"selected-task-row":u.tid===N.value}]]),onClick:f=>tt(u.tid)},[c("td",ii,[c("div",{class:$e(["id-cell",{"id-with-status":B.value}])},[c("div",li,[h[50]||(h[50]=c("span",{class:"group-indent"},"└─",-1)),c("span",ci,ae(u.tid.substring(0,8)),1),u.wordType==="wps-analysis"?(L(),j("span",ui," 解 ")):z("",!0),u.wordType==="wps-enhance_analysis"||u.isEnhanced?(L(),j("span",di," 解 ")):z("",!0),u.wordType==="wps-check"||u.isCheckTask?(L(),j("span",pi," 校 ")):z("",!0),Te(u)?(L(),j("span",{key:3,class:"soft-break-warning-icon",onMouseenter:h[25]||(h[25]=f=>ze(f))},h[49]||(h[49]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("title",null,"提示"),c("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),c("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):z("",!0)]),B.value?(L(),j("div",fi,[c("span",{class:$e(["task-tag compact",de(ge)(u.status)])},ae(de(me)(u.status)),3)])):z("",!0)],2)]),I.value?z("",!0):(L(),j("td",hi,[c("div",{class:"subject-cell",onMouseenter:f=>ft(f,u)},ae(Tt(u)),41,gi)])),B.value?z("",!0):(L(),j("td",vi,[c("div",mi,[c("span",{class:$e(["task-tag",de(ge)(u.status)])},ae(de(me)(u.status)),3)])])),h[51]||(h[51]=c("td",{class:"col-actions"},[c("div",{class:"task-actions"},[c("span",{class:"no-action-icon",title:"无可用操作"},[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),c("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})])])])],-1))],10,oi))),128)):z("",!0),r.type==="task"?(L(),j("tr",{key:2,class:$e(["task-row",[de(ge)(r.status),{"selected-task-row":r.tid===N.value}]]),onClick:u=>tt(r.tid)},[c("td",wi,[c("div",{class:$e(["id-cell",{"id-with-status":B.value}])},[c("div",yi,[c("span",xi,ae(r.tid.substring(0,8)),1),!r.isEnhanced&&!r.isCheckTask?(L(),j("span",ki," 解 ")):z("",!0),r.isEnhanced?(L(),j("span",Ti," 解 ")):z("",!0),r.isCheckTask?(L(),j("span",Ci," 校 ")):z("",!0),Te(r)?(L(),j("span",{key:3,class:"soft-break-warning-icon",onMouseenter:h[26]||(h[26]=u=>ze(u))},h[52]||(h[52]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("title",null,"提示"),c("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),c("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):z("",!0)]),B.value?(L(),j("div",$i,[c("span",{class:$e(["task-tag compact",de(ge)(r.status)])},ae(de(me)(r.status)),3)])):z("",!0)],2)]),I.value?z("",!0):(L(),j("td",Si,[c("div",{class:"subject-cell",onMouseenter:u=>ft(u,r)},ae(Tt(r)),41,_i)])),B.value?z("",!0):(L(),j("td",Ei,[c("div",Ai,[c("span",{class:$e(["task-tag",de(ge)(r.status)])},ae(de(me)(r.status)),3)])])),c("td",Mi,[c("div",Di,[r.status===1?(L(),j("button",{key:0,onClick:ht(u=>de(Ee)(r.tid),["stop"]),class:"terminate-btn"}," 终止 ",8,Pi)):z("",!0),r.status===2||r.status===4?(L(),j("button",{key:1,onClick:ht(u=>pt(r.tid),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Oi)):z("",!0),r.status!==1&&r.status!==2&&r.status!==4?(L(),j("span",Ii,h[53]||(h[53]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),c("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):z("",!0)])])],10,bi)):z("",!0)],64))),128))])],2)])):(L(),j("div",Ri,h[54]||(h[54]=[c("div",{class:"empty-text"},"暂无任务",-1)])))]),((i=(n=ve.value)==null?void 0:n.orgs[0])==null?void 0:i.orgId)===2?(L(),j("div",Ui,[c("div",{class:"log-header",onClick:Ot},[h[55]||(h[55]=c("div",{class:"log-title"},"执行日志",-1)),c("div",Fi,[c("button",{class:"clear-btn",onClick:h[27]||(h[27]=ht(r=>de(M)(),["stop"]))},"清空日志"),c("span",Li,ae(v.value?"▼":"▶"),1)])]),v.value?(L(),j("div",{key:0,class:"log-content",innerHTML:de(g)},null,8,ji)):z("",!0)])):z("",!0)]),a.value?(L(),j("div",{key:2,class:"modal-overlay",onClick:h[30]||(h[30]=r=>a.value=!1)},[c("div",{class:"modal-content",onClick:h[29]||(h[29]=ht(()=>{},["stop"]))},[pn(da,{onClose:h[28]||(h[28]=r=>a.value=!1)})])])):z("",!0)])}}},Ni=js(Wi,[["__scopeId","data-v-c27526fc"]]);export{Ni as default};
