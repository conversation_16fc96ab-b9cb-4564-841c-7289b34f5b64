import{U as Ks,r as se,h as ct,v as _e,i as V,j as Tt,k as Es,m as qt,_ as $s,n as Xs,o as q,c as Y,a as p,p as Js,t as te,f as Z,q as ke,w as Oe,e as Wt,F as xt,s as Ct,u as Qe,x as Ot,y as ut,z as Gs,A as oe,B as Bt,C as os,D as Zs,E as Qs}from"./index-DbTGfdJl.js";function en(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=Ks.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const tn={onbuttonclick:en};var sn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function nn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function an(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var r=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,r.get?r:{enumerable:!0,get:function(){return e[a]}})}),t}var As={exports:{}};const on={},rn=Object.freeze(Object.defineProperty({__proto__:null,default:on},Symbol.toStringTag,{value:"Module"})),rs=an(rn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",r=a?window:{};r.JS_SHA1_NO_WINDOW&&(a=!1);var g=!a&&typeof self=="object",y=!r.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;y?r=sn:g&&(r=self);var S=!r.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,b=!r.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",x="0123456789abcdef".split(""),$=[-**********,8388608,32768,128],I=[24,16,8,0],M=["hex","array","digest","arrayBuffer"],O=[],H=Array.isArray;(r.JS_SHA1_NO_NODE_JS||!H)&&(H=function(f){return Object.prototype.toString.call(f)==="[object Array]"});var R=ArrayBuffer.isView;b&&(r.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!R)&&(R=function(f){return typeof f=="object"&&f.buffer&&f.buffer.constructor===ArrayBuffer});var z=function(f){var w=typeof f;if(w==="string")return[f,!0];if(w!=="object"||f===null)throw new Error(s);if(b&&f.constructor===ArrayBuffer)return[new Uint8Array(f),!1];if(!H(f)&&!R(f))throw new Error(s);return[f,!1]},K=function(f){return function(w){return new D(!0).update(w)[f]()}},L=function(){var f=K("hex");y&&(f=B(f)),f.create=function(){return new D},f.update=function(C){return f.create().update(C)};for(var w=0;w<M.length;++w){var k=M[w];f[k]=K(k)}return f},B=function(f){var w=rs,k=rs.Buffer,C;k.from&&!r.JS_SHA1_NO_BUFFER_FROM?C=k.from:C=function(A){return new k(A)};var E=function(A){if(typeof A=="string")return w.createHash("sha1").update(A,"utf8").digest("hex");if(A==null)throw new Error(s);return A.constructor===ArrayBuffer&&(A=new Uint8Array(A)),H(A)||R(A)||A.constructor===k?w.createHash("sha1").update(C(A)).digest("hex"):f(A)};return E},h=function(f){return function(w,k){return new J(w,!0).update(k)[f]()}},X=function(){var f=h("hex");f.create=function(C){return new J(C)},f.update=function(C,E){return f.create(C).update(E)};for(var w=0;w<M.length;++w){var k=M[w];f[k]=h(k)}return f};function D(f){f?(O[0]=O[16]=O[1]=O[2]=O[3]=O[4]=O[5]=O[6]=O[7]=O[8]=O[9]=O[10]=O[11]=O[12]=O[13]=O[14]=O[15]=0,this.blocks=O):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}D.prototype.update=function(f){if(this.finalized)throw new Error(t);var w=z(f);f=w[0];for(var k=w[1],C,E=0,A,_=f.length||0,T=this.blocks;E<_;){if(this.hashed&&(this.hashed=!1,T[0]=this.block,this.block=T[16]=T[1]=T[2]=T[3]=T[4]=T[5]=T[6]=T[7]=T[8]=T[9]=T[10]=T[11]=T[12]=T[13]=T[14]=T[15]=0),k)for(A=this.start;E<_&&A<64;++E)C=f.charCodeAt(E),C<128?T[A>>>2]|=C<<I[A++&3]:C<2048?(T[A>>>2]|=(192|C>>>6)<<I[A++&3],T[A>>>2]|=(128|C&63)<<I[A++&3]):C<55296||C>=57344?(T[A>>>2]|=(224|C>>>12)<<I[A++&3],T[A>>>2]|=(128|C>>>6&63)<<I[A++&3],T[A>>>2]|=(128|C&63)<<I[A++&3]):(C=65536+((C&1023)<<10|f.charCodeAt(++E)&1023),T[A>>>2]|=(240|C>>>18)<<I[A++&3],T[A>>>2]|=(128|C>>>12&63)<<I[A++&3],T[A>>>2]|=(128|C>>>6&63)<<I[A++&3],T[A>>>2]|=(128|C&63)<<I[A++&3]);else for(A=this.start;E<_&&A<64;++E)T[A>>>2]|=f[E]<<I[A++&3];this.lastByteIndex=A,this.bytes+=A-this.start,A>=64?(this.block=T[16],this.start=A-64,this.hash(),this.hashed=!0):this.start=A}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},D.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var f=this.blocks,w=this.lastByteIndex;f[16]=this.block,f[w>>>2]|=$[w&3],this.block=f[16],w>=56&&(this.hashed||this.hash(),f[0]=this.block,f[16]=f[1]=f[2]=f[3]=f[4]=f[5]=f[6]=f[7]=f[8]=f[9]=f[10]=f[11]=f[12]=f[13]=f[14]=f[15]=0),f[14]=this.hBytes<<3|this.bytes>>>29,f[15]=this.bytes<<3,this.hash()}},D.prototype.hash=function(){var f=this.h0,w=this.h1,k=this.h2,C=this.h3,E=this.h4,A,_,T,F=this.blocks;for(_=16;_<80;++_)T=F[_-3]^F[_-8]^F[_-14]^F[_-16],F[_]=T<<1|T>>>31;for(_=0;_<20;_+=5)A=w&k|~w&C,T=f<<5|f>>>27,E=T+A+E+1518500249+F[_]<<0,w=w<<30|w>>>2,A=f&w|~f&k,T=E<<5|E>>>27,C=T+A+C+1518500249+F[_+1]<<0,f=f<<30|f>>>2,A=E&f|~E&w,T=C<<5|C>>>27,k=T+A+k+1518500249+F[_+2]<<0,E=E<<30|E>>>2,A=C&E|~C&f,T=k<<5|k>>>27,w=T+A+w+1518500249+F[_+3]<<0,C=C<<30|C>>>2,A=k&C|~k&E,T=w<<5|w>>>27,f=T+A+f+1518500249+F[_+4]<<0,k=k<<30|k>>>2;for(;_<40;_+=5)A=w^k^C,T=f<<5|f>>>27,E=T+A+E+1859775393+F[_]<<0,w=w<<30|w>>>2,A=f^w^k,T=E<<5|E>>>27,C=T+A+C+1859775393+F[_+1]<<0,f=f<<30|f>>>2,A=E^f^w,T=C<<5|C>>>27,k=T+A+k+1859775393+F[_+2]<<0,E=E<<30|E>>>2,A=C^E^f,T=k<<5|k>>>27,w=T+A+w+1859775393+F[_+3]<<0,C=C<<30|C>>>2,A=k^C^E,T=w<<5|w>>>27,f=T+A+f+1859775393+F[_+4]<<0,k=k<<30|k>>>2;for(;_<60;_+=5)A=w&k|w&C|k&C,T=f<<5|f>>>27,E=T+A+E-1894007588+F[_]<<0,w=w<<30|w>>>2,A=f&w|f&k|w&k,T=E<<5|E>>>27,C=T+A+C-1894007588+F[_+1]<<0,f=f<<30|f>>>2,A=E&f|E&w|f&w,T=C<<5|C>>>27,k=T+A+k-1894007588+F[_+2]<<0,E=E<<30|E>>>2,A=C&E|C&f|E&f,T=k<<5|k>>>27,w=T+A+w-1894007588+F[_+3]<<0,C=C<<30|C>>>2,A=k&C|k&E|C&E,T=w<<5|w>>>27,f=T+A+f-1894007588+F[_+4]<<0,k=k<<30|k>>>2;for(;_<80;_+=5)A=w^k^C,T=f<<5|f>>>27,E=T+A+E-899497514+F[_]<<0,w=w<<30|w>>>2,A=f^w^k,T=E<<5|E>>>27,C=T+A+C-899497514+F[_+1]<<0,f=f<<30|f>>>2,A=E^f^w,T=C<<5|C>>>27,k=T+A+k-899497514+F[_+2]<<0,E=E<<30|E>>>2,A=C^E^f,T=k<<5|k>>>27,w=T+A+w-899497514+F[_+3]<<0,C=C<<30|C>>>2,A=k^C^E,T=w<<5|w>>>27,f=T+A+f-899497514+F[_+4]<<0,k=k<<30|k>>>2;this.h0=this.h0+f<<0,this.h1=this.h1+w<<0,this.h2=this.h2+k<<0,this.h3=this.h3+C<<0,this.h4=this.h4+E<<0},D.prototype.hex=function(){this.finalize();var f=this.h0,w=this.h1,k=this.h2,C=this.h3,E=this.h4;return x[f>>>28&15]+x[f>>>24&15]+x[f>>>20&15]+x[f>>>16&15]+x[f>>>12&15]+x[f>>>8&15]+x[f>>>4&15]+x[f&15]+x[w>>>28&15]+x[w>>>24&15]+x[w>>>20&15]+x[w>>>16&15]+x[w>>>12&15]+x[w>>>8&15]+x[w>>>4&15]+x[w&15]+x[k>>>28&15]+x[k>>>24&15]+x[k>>>20&15]+x[k>>>16&15]+x[k>>>12&15]+x[k>>>8&15]+x[k>>>4&15]+x[k&15]+x[C>>>28&15]+x[C>>>24&15]+x[C>>>20&15]+x[C>>>16&15]+x[C>>>12&15]+x[C>>>8&15]+x[C>>>4&15]+x[C&15]+x[E>>>28&15]+x[E>>>24&15]+x[E>>>20&15]+x[E>>>16&15]+x[E>>>12&15]+x[E>>>8&15]+x[E>>>4&15]+x[E&15]},D.prototype.toString=D.prototype.hex,D.prototype.digest=function(){this.finalize();var f=this.h0,w=this.h1,k=this.h2,C=this.h3,E=this.h4;return[f>>>24&255,f>>>16&255,f>>>8&255,f&255,w>>>24&255,w>>>16&255,w>>>8&255,w&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,C>>>24&255,C>>>16&255,C>>>8&255,C&255,E>>>24&255,E>>>16&255,E>>>8&255,E&255]},D.prototype.array=D.prototype.digest,D.prototype.arrayBuffer=function(){this.finalize();var f=new ArrayBuffer(20),w=new DataView(f);return w.setUint32(0,this.h0),w.setUint32(4,this.h1),w.setUint32(8,this.h2),w.setUint32(12,this.h3),w.setUint32(16,this.h4),f};function J(f,w){var k,C=z(f);if(f=C[0],C[1]){var E=[],A=f.length,_=0,T;for(k=0;k<A;++k)T=f.charCodeAt(k),T<128?E[_++]=T:T<2048?(E[_++]=192|T>>>6,E[_++]=128|T&63):T<55296||T>=57344?(E[_++]=224|T>>>12,E[_++]=128|T>>>6&63,E[_++]=128|T&63):(T=65536+((T&1023)<<10|f.charCodeAt(++k)&1023),E[_++]=240|T>>>18,E[_++]=128|T>>>12&63,E[_++]=128|T>>>6&63,E[_++]=128|T&63);f=E}f.length>64&&(f=new D(!0).update(f).array());var F=[],ae=[];for(k=0;k<64;++k){var re=f[k]||0;F[k]=92^re,ae[k]=54^re}D.call(this,w),this.update(ae),this.oKeyPad=F,this.inner=!0,this.sharedMemory=w}J.prototype=new D,J.prototype.finalize=function(){if(D.prototype.finalize.call(this),this.inner){this.inner=!1;var f=this.array();D.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(f),D.prototype.finalize.call(this)}};var G=L();G.sha1=G,G.sha1.hmac=X(),S?e.exports=G:r.sha1=G})()})(As);var ln=As.exports;const cn=nn(ln);function is(){return"http://worksheet.hexinedu.com"}function dt(){return"http://127.0.0.1:3000"}function un(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const pt=async(e,s,t,a={},r=8e3)=>{try{return await Promise.race([e(),new Promise((g,y)=>setTimeout(()=>y(new Error("WebSocket请求超时，切换到HTTP")),r))])}catch{try{let y;return s==="get"?y=await Tt.get(t,{params:a}):s==="post"?y=await Tt.post(t,a):s==="delete"&&(y=await Tt.delete(t)),y.data}catch(y){throw new Error(`请求失败: ${y.message||"未知错误"}`)}}};function dn(e,s,t,a){const r=[e,s,t,a].join(":");return cn(r)}function pn(){const e=se(""),s=se(""),t=se(""),a=ct({}),r=se(""),g=se("");let y="",S=null;const b=se("c:\\Temp"),x=ct({appKey:"",appSecret:""}),$=ct({show:!1,message:"",resolveCallback:null,rejectCallback:null}),I=se(""),M=se("junior"),O=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],H=()=>_e.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],R=ct(H()),z=async()=>{try{const n=await V.getWatcherStatus();n.data&&n.data.watchDir&&(b.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${b.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},K=async()=>{var n,o,i;try{if(!x.appKey||!x.appSecret)throw new Error("未初始化app信息");const v=60,u=Date.now();let d;try{const ee=window.Application.PluginStorage.getItem("token_info");ee&&(d=JSON.parse(ee))}catch(ee){d=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${ee.message}</span><br/>`}if(d&&d.access_token&&d.expired_time>u+v*1e3)return d.access_token;const m=x.appKey,P="1234567",U=Math.floor(Date.now()/1e3),N=dn(m,P,x.appSecret,U),W=await Tt.get(is()+"/api/open/account/v1/auth/token",{params:{app_key:m,app_nonstr:P,app_timestamp:U,app_signature:N}});if((o=(n=W.data)==null?void 0:n.data)!=null&&o.access_token){const ee=W.data.data.access_token;let ne;if(W.data.data.expired_time){const he=parseInt(W.data.data.expired_time);ne=u+he*1e3,t.value+=`<span class="log-item info">token已更新，有效期${he}秒</span><br/>`}else ne=u+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const le={access_token:ee,expired_time:ne};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(le))}catch(he){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${he.message}</span><br/>`}return ee}else throw new Error(((i=W.data)==null?void 0:i.message)||"获取access_token失败")}catch(v){throw t.value+=`<span class="log-item error">获取access_token失败: ${v.message}</span><br/>`,v}},L=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},B=()=>{const n=window.Application,o=n.Documents.Count;for(let i=1;i<=o;i++){const v=n.Documents.Item(i);if(v.DocID===r.value)return v}return null},h=()=>{try{const n=B();if(!n)return{isValid:!1,message:"未找到当前文档"};let o="";try{o=n.Name||""}catch{try{o=w("getDocName")||""}catch{o=""}}if(o){const i=o.toLowerCase();return i.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:i.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},X=n=>n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",D=n=>n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"进行中",J=n=>{const o=Date.now()-n,i=Math.floor(o/1e3);return i<60?`${i}秒`:i<3600?`${Math.floor(i/60)}分${i%60}秒`:`${Math.floor(i/3600)}时${Math.floor(i%3600/60)}分`},G=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const i=B();if(i&&i.ContentControls)for(let v=1;v<=i.ContentControls.Count;v++)try{const u=i.ContentControls.Item(v);if(u&&u.Title&&(u.Title===`任务_${n}`||u.Title===`任务增强_${n}`)){const d=u.Title===`任务增强_${n}`||a[n].isEnhanced;u.Title=d?`已停止增强_${n}`:`已停止_${n}`,t.value+=`<span class="log-item info">已将${d?"增强":"普通"}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(i){t.value+=`<span class="log-item warning">更新控件标题失败: ${i.message}</span><br/>`}let o=null;if(j[n]&&j[n].urlId){o=j[n].urlId;try{try{const i=await pt(async()=>await V.getUrlMonitorStatus(),"get",`${dt()}/api/url/status`),u=(Array.isArray(i)?i:i.data?Array.isArray(i.data)?i.data:[]:[]).find(d=>d.urlId===o);u&&u.downloadedPath&&(a[n].resultFile=u.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${u.downloadedPath}</span><br/>`)}catch(i){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${i.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ie(o),delete j[n]}catch(i){t.value+=`<span class="log-item warning">停止URL监控出错: ${i.message}，将重试</span><br/>`,delete j[n],setTimeout(async()=>{try{o&&await pt(async()=>await V.stopUrlMonitoring(o),"delete",`${dt()}/api/url/monitor/${o}`)}catch(v){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${v.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(o){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${o.message}</span><br/>`,j[n]&&delete j[n]}},f=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const o=B();if(o&&o.ContentControls)for(let v=1;v<=o.ContentControls.Count;v++)try{const u=o.ContentControls.Item(v);if(u&&u.Title&&(u.Title===`任务_${n}`||u.Title===`任务增强_${n}`)){const d=u.Title===`任务增强_${n}`||a[n].isEnhanced;u.Title=d?`已停止增强_${n}`:`已停止_${n}`,u.LockContents=!1,t.value+=`<span class="log-item info">已将${d?"增强":"普通"}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let i=null;if(j[n]&&j[n].urlId){i=j[n].urlId;try{const v=await V.getUrlMonitorStatus(),d=(Array.isArray(v)?v:v.data?Array.isArray(v.data)?v.data:[]:[]).find(m=>m.urlId===i);d&&d.downloadedPath&&(a[n].resultFile=d.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${d.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ie(i),delete j[n]}catch(v){t.value+=`<span class="log-item warning">停止URL监控出错: ${v.message}，将重试</span><br/>`,delete j[n]}}},w=n=>tn.onbuttonclick(n),k=()=>{try{e.value=w("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},C=()=>{B().ActiveWindow.Selection.Copy()},E=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${b.value}\\${n}`,12,"","",!1),o.Close(),B().ActiveWindow.Activate()},A=n=>{const o=window.Application.Documents.Add("",!1,0,!1);o.Content.Paste(),o.SaveAs2(`${b.value}\\${n}`,12,"","",!1),o.Close()},_=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${b.value}\\${n}`,12,"","",!1),B().ActiveWindow.Activate()},T=n=>{const o=window.Application.Documents.Add("",!1,0,!1);o.Content.Paste(),o.SaveAs2(`${b.value}\\${n}`,12,"","",!1)},F=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let o="method2";try{const i=await V.getSaveMethod();if(i.success&&i.saveMethod){o=i.saveMethod;const v=o==="method1"?"方式一":o==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${v}</span><br/>`}}catch(i){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${i.message}</span><br/>`}o==="method1"?(E(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${b.value}\\${n}.docx</span><br/>`):o==="method2"?(A(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${b.value}\\${n}.docx</span><br/>`):o==="method3"?(_(n),t.value+=`<span class="log-item success">文件已通过方式三保存到监控目录: ${b.value}\\${n}.docx</span><br/>`):o==="method4"&&(T(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${b.value}\\${n}.docx</span><br/>`),V.associateFileWithClient(`${n}.docx`).then(i=>{i.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${i.message||"未知错误"}</span><br/>`}).catch(i=>{t.value+=`<span class="log-item warning">关联文件时出错: ${i.message}</span><br/>`})}catch(o){t.value+=`<span class="log-item error">保存文件失败: ${o.message}</span><br/>`}},ae=se(null),re=ct([]),ce=new Set,Ae=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),ve=async n=>{try{const o=n.slice(y.length);if(o.trim()){const i=V.getClientId();if(!i){console.warn("无法获取客户端ID，跳过日志同步");return}const v=Ae(o);if(!v.trim()){y=n;return}await V.sendRequest("logger","syncLog",{content:v,timestamp:new Date().toISOString(),clientId:i}),y=n}}catch(o){console.error("同步日志到服务端失败:",o)}},we=()=>{V.connect().then(()=>{z()}).catch(o=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${o.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const o=[];for(const i in a)if(a.hasOwnProperty(i)){const v=a[i];v.status===1&&!v.terminated&&(o.includes(i)||o.push(i))}if(o.length>0){let i=!1;try{const v=B();if(v){const d=`taskpane_id_${v.DocID}`,m=window.Application.PluginStorage.getItem(d);if(m){const P=window.Application.GetTaskPane(m);P&&(i=P.Visible)}}}catch(v){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${v.message}</span><br/>`,i=!0}setTimeout(()=>{if(i){const v=`检测到 ${o.length} 个未完成的任务，是否继续？`;it(v).then(u=>{u?(t.value+=`<span class="log-item info">用户选择继续 ${o.length} 个进行中的任务 (by taskId)...</span><br/>`,V.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:o}).then(d=>{d&&d.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${d.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(d==null?void 0:d.message)||"未知错误"}</span><br/>`}).catch(d=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${d.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',o.forEach(d=>{G(d)}),t.value+=`<span class="log-item success">${o.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(u=>{t.value+=`<span class="log-item error">弹窗错误: ${u.message}，默认停止任务（保留控件）</span><br/>`,o.forEach(d=>{G(d)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};V.setConnectionSuccessHandler(n),V.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),V.addEventListener("connection",o=>{o.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${o.reason||"未知"}, 代码: ${o.code||"N/A"}，将自动重连</span><br/>`)}),V.addEventListener("watcher",o=>{var i,v;if(re.push(o),o.type,o.eventType==="uploadSuccess"){const u=(i=o.data)==null?void 0:i.file,d=u==null?void 0:u.replace(/\.docx$/,""),m=`${o.eventType}_${u}_${Date.now()}`;if(ce.has(m)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${u}</span><br/>`;return}if(ce.add(m),ce.size>100){const U=ce.values();ce.delete(U.next().value)}const P=d&&((v=a[d])==null?void 0:v.wordType);Te(o,P)}else o.eventType&&Te(o,null)}),V.addEventListener("urlMonitor",o=>{re.push(o),o.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${o.eventType||o.action}</span><br/>`),o.eventType&&Te(o,null)}),V.addEventListener("health",o=>{}),V.addEventListener("error",o=>{const i=o.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${i}</span><br/>`,console.error("WebSocket错误:",o)})},j=ct({}),ue=async(n,o,i=!1,v=5e3,u={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const d=await V.startUrlMonitoring(n,v,{downloadOnSuccess:u.downloadOnSuccess!==void 0?u.downloadOnSuccess:!0,appKey:u.appKey,filename:u.filename,taskId:o});t.value+=`<span class="log-item info">URL监控请求数据: ${JSON.stringify(d)}</span><br/>`;const m=d.success||(d==null?void 0:d.success),P=d.urlId||(d==null?void 0:d.urlId);if(m&&P){j[o]={urlId:P,url:n,isResultUrl:i,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${P}</span><br/>`;try{await V.startUrlChecking(P)}catch{}return P}else throw new Error("服务器返回失败")}catch(d){return t.value+=`<span class="log-item error">启动URL监控失败: ${d.message}</span><br/>`,null}},ie=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(j).forEach(i=>{j[i].urlId===n&&delete j[i]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const o=await pt(async()=>await V.stopUrlMonitoring(n),"delete",`${dt()}/api/url/monitor/${n}`);return o&&(o.success||o!=null&&o.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(o){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${o.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await pt(async()=>await V.stopUrlMonitoring(n),"delete",`${dt()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},Ie=async()=>{try{const n=await pt(async()=>await V.getUrlMonitorStatus(),"get",`${dt()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Q=async n=>{try{return await V.forceUrlCheck(n)}catch{return!1}},Te=async(n,o)=>{var i;if(n.eventType==="uploadSuccess"){const v=n.data.file,u=v.replace(/\.docx$/,"");if(a[u]){if(a[u].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${u.substring(0,8)} 的上传通知</span><br/>`;return}if(a[u].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${u.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${v} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${o||a[u].wordType||"wps-analysis"}</span><br/>`,a[u].status=1,a[u].uploadSuccess=!0,await ye(u,o||a[u].wordType||"wps-analysis")}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:v,url:u,taskId:d,downloadedPath:m}=n.data,P=Object.keys(j).filter(U=>j[U].urlId===v);P.length>0&&P.forEach(U=>{m&&a[U]&&(a[U].resultFile=m,a[U].resultDownloaded=!0),delete j[U]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:v,url:u,filePath:d,taskId:m}=n.data;if(!Object.values(j).some(U=>U.urlId===v)&&m&&((i=a[m])!=null&&i.terminated))return;if(m&&a[m]&&a[m].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${v} 的文件下载通知</span><br/>`,v)try{await V.stopUrlMonitoring(v),j[m]&&delete j[m]}catch{}return}if(m&&a[m]){t.value+=`<span class="log-item success">收到结果文件通知: ${d}</span><br/>`,a[m].resultFile=d,a[m].resultDownloaded=!0;const U=J(a[m].startTime);t.value+=`<span class="log-item success">任务${m.substring(0,8)}完成，总耗时${U}</span><br/>`,await Le(m),j[m]&&j[m].urlId&&(ie(j[m].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete j[m])}else if(v){t.value+=`<span class="log-item info">URL文件已下载: ${d}</span><br/>`;const U=Object.keys(j).filter(N=>{var W;return j[N].urlId===v&&!((W=a[N])!=null&&W.terminated)});U.length>0&&U.forEach(N=>{if(a[N]&&(t.value+=`<span class="log-item info">关联到任务: ${N.substring(0,8)}</span><br/>`,a[N].resultFile=d,a[N].resultDownloaded=!0,a[N].status===1)){a[N].status=2;const W=J(a[N].startTime);t.value+=`<span class="log-item success">任务${N.substring(0,8)}完成，总耗时${W}</span><br/>`}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:v,url:u,error:d,taskId:m}=n.data;if(m&&a[m]&&a[m].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${m.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${d}</span><br/>`,m&&a[m]&&(a[m].status=-1,a[m].errorMessage=`下载失败: ${d}`,de(m),j[m]&&delete j[m]),v)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${v}</span><br/>`,await pt(async()=>await V.stopUrlMonitoring(v),"delete",`${dt()}/api/url/monitor/${v}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},ye=async(n,o="wps-analysis")=>{try{if(!x.appKey)throw new Error("未初始化appKey信息");const i=await K(),v=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,u=await Tt.post(is()+"/api/open/ticket/v1/ai_comment/create",{access_token:i,data:{app_key:x.appKey,subject:I.value,stage:M.value,file_name:`${n}`,word_url:v,word_type:o,is_ai_auto:!0,is_ai_edit:!0,create_user_id:x.userId,create_username:x.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),d=u.data.data.ticket_id;if(!d)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",de(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${d}，开始监控结果文件</span><br/>`;const m=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${d}.wps.docx`,P={downloadOnSuccess:!0,filename:`${d}.wps.docx`,appKey:x.appKey,ticketId:d,taskId:n},U=await ue(m,n,!0,3e3,P);return a[n]&&(a[n].ticketId=d,a[n].resultUrl=m,a[n].urlMonitorId=U,a[n].status=1),u.data}catch(i){return t.value+=`<span class="log-item error">API调用失败: ${i.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${i.message}`,de(n)),!1}},Ue=async(n,o="wps-analysis")=>new Promise((i,v)=>{try{t.value+=`<span class="log-item info">监控目录: ${b.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${o}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=o,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const u=1e3,d=30;let m=0;const P=setInterval(()=>{if(m++,a[n]&&a[n].uploadSuccess){clearInterval(P),i(!0);return}m>=d&&(clearInterval(P),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',v(new Error("上传超时，未收到完成通知")))},u)}catch(u){t.value+=`<span class="log-item error">任务处理异常: ${u.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${u.message}`),de(n),v(u)}}),ze=async()=>{var i;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,o=n.ActiveDocument;if(r.value=o.DocID,g.value=n.ActiveWindow.Index,o!=null&&o.ContentControls)for(let v=1;v<=o.ContentControls.Count;v++){const u=o.ContentControls.Item(v);if(u&&u.Title){let d=null,m=1,P=!1;if(u.Title.startsWith("任务增强_")?(d=u.Title.substring(5),m=1,P=!0):u.Title.startsWith("任务_")?(d=u.Title.substring(3),m=1):u.Title.startsWith("已完成增强_")?(d=u.Title.substring(6),m=2,P=!0):u.Title.startsWith("已完成_")?(d=u.Title.substring(4),m=2):u.Title.startsWith("异常增强_")?(d=u.Title.substring(5),m=-1,P=!0):u.Title.startsWith("异常_")?(d=u.Title.substring(3),m=-1):u.Title.startsWith("已停止增强_")?(d=u.Title.substring(6),m=4,P=!0):u.Title.startsWith("已停止_")&&(d=u.Title.substring(4),m=4),d&&!a[d]){let U="";try{U=((i=u.Range)==null?void 0:i.Text)||""}catch{}let N=Date.now()-24*60*60*1e3;try{if(d.length===24){const ne=d.substring(0,8),le=parseInt(ne,16);!isNaN(le)&&le>0&&le<2147483647&&(N=le*1e3)}else{const ne=Date.now()-864e5;m===2?N=ne-60*60*1e3:m===-1?N=ne-30*60*1e3:m===4?N=ne-45*60*1e3:N=ne}}catch{}a[d]={status:m,startTime:N,contentControlId:u.ID,isEnhanced:P,selectedText:U};const W=m===1?"进行中":m===2?"已完成":m===-1?"异常":m===4?"已停止":"未知",ee=P?"增强":"普通";t.value+=`<span class="log-item info">发现已有${ee}任务: ${d.substring(0,8)}, 状态: ${W}</span><br/>`}}}},de=(n,o=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}o&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const i=B();if(!i){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let v=!1;const u=a[n].isEnhanced;if(i.ContentControls&&i.ContentControls.Count>0)for(let d=i.ContentControls.Count;d>=1;d--)try{const m=i.ContentControls.Item(d);m&&m.Title&&m.Title.includes(n)&&(m.LockContents=!1,m.Delete(!1),v=!0,console.log(m.Title),t.value+=`<span class="log-item success">已解锁并删除${u?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(m){t.value+=`<span class="log-item warning">访问第${d}个控件时出错: ${m.message}</span><br/>`;continue}v?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${u?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(i){t.value+=`<span class="log-item error">删除内容控件失败: ${i.message}</span><br/>`}},Re=n=>{var o;try{const i=window.wps,v=B();if(!v||!v.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const u=(o=a[n])==null?void 0:o.isEnhanced;let d=null;for(let P=1;P<=v.ContentControls.Count;P++)try{const U=v.ContentControls.Item(P);if(U&&U.Title&&U.Title.includes(n)){d=U;break}}catch{continue}if(!d){const P=a[n];P&&(P.status===2||P.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}d.Range.Select();const m=i.Windows.Item(g.value);m&&m.Selection&&m.Selection.Range&&m.ScrollIntoView(m.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${u?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(i){t.value+=`<span class="log-item error">跳转到任务控件失败: ${i.message}</span><br/>`}},Le=async n=>{var m,P,U;const o=B(),i=a[n];if(!i){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(i.terminated||i.status!==1)return;if(i.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const v=B();let u=null;if(v&&v.ContentControls)for(let N=1;N<=v.ContentControls.Count;N++){const W=v.ContentControls.Item(N);if(W&&W.Title&&W.Title.includes(n)){u=W;break}}if(!u){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,i.errorMessage&&i.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${i.errorMessage}</span><br/>`;return}return}if(i.errorMessage&&i.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${i.errorMessage}</span><br/>`,de(n);return}if(!i.resultFile)return;a[n].documentInserted=!0;const d=u.Range;u.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${i.resultFile}...</span><br/>`;const N=o.Range(d.End,d.End);N.InsertParagraphAfter(),d.Text.includes("\v")&&N.InsertAfter("\v");let W=N.End;const ee=(m=d.Tables)==null?void 0:m.Count;if(ee){const fe=d.Tables.Item(ee);((P=fe==null?void 0:fe.Range)==null?void 0:P.End)>W&&(fe.Range.InsertParagraphAfter(),W=(U=fe==null?void 0:fe.Range)==null?void 0:U.End)}o.Range(W,W).InsertFile(i.resultFile);for(let fe=1;fe<=v.ContentControls.Count;fe++){const pe=v.ContentControls.Item(fe);if(pe&&pe.Title&&(pe.Title===`任务_${n}`||pe.Title===`任务增强_${n}`)){u=pe;break}}const le=o.Range(u.Range.End-1,u.Range.End);le.Text.includes("\r")&&le.Delete(),a[n].status=2,j[n]&&j[n].urlId&&V.sendRequest("urlMonitor","updateTaskStatus",{urlId:j[n].urlId,status:"completed",taskId:n}).then(fe=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(fe=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${fe.message}</span><br/>`});const wt=J(i.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${wt}</span><br/>`;const lt=u.Title===`任务增强_${n}`||i.isEnhanced;u&&(u.Title=lt?`已完成增强_${n}`:`已完成_${n}`,t.value+=`<span class="log-item info">已将${lt?"增强":"普通"}任务${n.substring(0,8)}控件标记为已完成</span><br/>`)}catch(N){a[n].documentInserted=!1,a[n].status=-1;const W=u.Title===`任务增强_${n}`||i.isEnhanced;u&&(u.Title=W?`异常增强_${n}`:`异常_${n}`,t.value+=`<span class="log-item info">已将${W?"增强":"普通"}任务${n.substring(0,8)}控件标记为异常</span><br/>`),t.value+=`<span class="log-item error">插入文档失败: ${N.message}</span><br/>`}},Fe=async()=>{const n=(v=1e3)=>new Promise(u=>{setTimeout(()=>u(),v)}),o=new Map,i=Object.keys(a).filter(v=>a[v].status===1&&!a[v].terminated);for(i.length?(i.forEach(v=>{o.has(v)||o.set(v,Date.now())}),await Promise.all(i.map(v=>Le(v)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const v=Object.keys(a).filter(u=>a[u].status===1&&!a[u].terminated);v.forEach(u=>{o.has(u)||o.set(u,Date.now());const d=o.get(u);(Date.now()-d)/1e3/60>=5e4&&a[u]&&!a[u].terminated&&(a[u].terminated=!0,a[u].status=-1,t.value+=`<span class="log-item warning">任务 ${u} 执行超过5分钟，已自动终止</span><br/>`,o.delete(u))}),v.length&&await Promise.all(v.map(u=>Le(u)))}},qe=async(n="wps-analysis")=>{const o=Be();if(o){const{primary:v,all:u}=o,{taskId:d,control:m,task:P,isEnhanced:U,overlapType:N}=v,W=u.filter(ee=>ee.task&&ee.task.status===1);if(W.length>0){const ee=W.map(ne=>ne.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${ee})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${u.length}个已有任务重叠，重叠类型: ${N}</span><br/>`,tt();try{for(const ee of u){const{taskId:ne,control:le,task:he,isEnhanced:wt}=ee;t.value+=`<span class="log-item info">删除重叠的${wt?"增强":"普通"}任务 ${ne.substring(0,8)}，状态为 ${D((he==null?void 0:he.status)||0)}</span><br/>`,he&&(he.status=3,he.terminated=!0,he.errorMessage="用户重新创建任务时删除");try{le.LockContents=!1,le.Delete(!1),a[ne]&&(a[ne].placeholderRemoved=!0)}catch(lt){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${lt.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${u.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(ee){return Ze(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ee.message}</span><br/>`,Promise.reject(ee)}Ze()}const i=un().replace(/-/g,"").substring(0,8);return new Promise(async(v,u)=>{try{const d=window.wps,m=B(),P=m.ActiveWindow.Selection,U=P.Range,N=P.Text||"";if(s.value=N,C(),U){const W=U.Paragraphs,ee=W.Item(1),ne=W.Item(W.Count),le=ee.Range.Start,he=ne.Range.End,wt=U.Start,lt=U.End,fe=m.Range(Math.min(le,wt),Math.max(he,lt));try{let pe=m.ContentControls.Add(d.Enum.wdContentControlRichText,fe);if(!pe){if(console.log("创建内容控件失败"),pe=m.ContentControls.Add(d.Enum.wdContentControlRichText),!pe){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',u(new Error("创建内容控件失败"));return}U.Cut(),pe.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const jt=n==="wps-enhance_analysis";pe.Title=jt?`任务增强_${i}`:`任务_${i}`,pe.LockContents=!0,a[i]||(a[i]={}),a[i].contentControlId=pe.ID,a[i].isEnhanced=jt,t.value+=`<span class="log-item info">已创建${jt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const Ys=pe.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${Ys.length}</span><br/>`}catch(pe){t.value+=`<span class="log-item error">创建内容控件失败: ${pe.message}</span><br/>`,u(pe);return}}await F(i),a[i]={status:1,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:N},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${i}，类型: ${n}</span><br/>`;try{await Ue(i,n)?v():(a[i]&&a[i].status===1&&(a[i].status=-1,a[i].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${i.substring(0,8)}失败</span><br/>`,st(i)),u(new Error("API调用失败或超时")))}catch(W){a[i]&&(a[i].status=-1,a[i].errorMessage=`执行错误: ${W.message}`,t.value+=`<span class="log-item error">任务${i.substring(0,8)}执行出错: ${W.message}</span><br/>`,st(i)),u(W)}}catch(d){u(d)}})},Ke=async()=>{},Xe=()=>je()>0?"status-error":ge()>0?"status-running":xe()>0?"status-completed":"",ge=()=>Object.values(a).filter(n=>n.status===1).length,xe=()=>Object.values(a).filter(n=>n.status===2).length,je=()=>Object.values(a).filter(n=>n.status===-1).length,Me=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,o=B();if(!o){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let i=0;if(Object.keys(a).forEach(v=>{try{a[v].status===1&&(a[v].status=3,a[v].terminated=!0,a[v].errorMessage="强制清除"),de(v),i++}catch(u){t.value+=`<span class="log-item warning">清除任务${v.substring(0,8)}失败: ${u.message}</span><br/>`}}),o.ContentControls&&o.ContentControls.Count>0)for(let v=o.ContentControls.Count;v>=1;v--)try{const u=o.ContentControls.Item(v);if(u&&u.Title&&(u.Title.startsWith("任务_")||u.Title.startsWith("任务增强_")||u.Title.startsWith("已完成_")||u.Title.startsWith("已完成增强_")||u.Title.startsWith("异常_")||u.Title.startsWith("异常增强_")||u.Title.startsWith("已停止_")||u.Title.startsWith("已停止增强_")))try{u.LockContents=!1,u.Delete(!1);let d;u.Title.startsWith("任务增强_")?d=u.Title.substring(5):u.Title.startsWith("任务_")?d=u.Title.substring(3):u.Title.startsWith("已完成增强_")?d=u.Title.substring(6):u.Title.startsWith("已完成_")?d=u.Title.substring(4):u.Title.startsWith("异常增强_")?d=u.Title.substring(5):u.Title.startsWith("异常_")?d=u.Title.substring(3):u.Title.startsWith("已停止增强_")?d=u.Title.substring(6):u.Title.startsWith("已停止_")&&(d=u.Title.substring(4)),a[d]?(a[d].placeholderRemoved=!0,a[d].status=3):a[d]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},i++,t.value+=`<span class="log-item success">已删除任务${d.substring(0,8)}的内容控件</span><br/>`}catch(d){t.value+=`<span class="log-item error">删除控件失败: ${d.message}</span><br/>`}}catch(u){t.value+=`<span class="log-item warning">访问控件时出错: ${u.message}</span><br/>`}i>0?t.value+=`<span class="log-item success">已清除${i}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},De=async()=>{try{const n=Object.values(j).map(o=>o.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const o of n)await ie(o)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Je=()=>{Es(async()=>{try{const o=window.Application.PluginStorage.getItem("user_info");if(!o)throw new Error("未找到用户信息");const i=JSON.parse(o);if(!i.orgs||!i.orgs[0])throw new Error("未找到组织信息");x.appKey=i.appKey,x.userName=i.nickname,x.userId=i.userId,x.appSecret=i.appSecret}catch(o){t.value+=`<span class="log-item error">初始化appKey失败: ${o.message}</span><br/>`}await z(),qt([I,M],async()=>{await c()},{immediate:!1}),await l();const n=_e.onVersionChange(()=>{const o=H();R.splice(0,R.length,...o),_e.isSeniorEdition()&&M.value==="junior"?M.value="senior":!_e.isSeniorEdition()&&M.value==="senior"&&(M.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${_e.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',k(),await ze(),we(),Ge(),Fe(),window.addEventListener("beforeunload",De),()=>{S&&clearTimeout(S),n&&n()}}),qt(t,n=>{S&&clearTimeout(S),S=setTimeout(()=>{ve(n)},10)},{immediate:!1})},Ge=()=>{V.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==I.value&&(I.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${I.value}</span><br/>`),n.data.stage!==M.value&&(M.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${M.value}</span><br/>`))})},We=se(!1),Be=()=>{try{const n=B(),o=n.ActiveWindow.Selection;if(!o||!n||!n.ContentControls)return null;const i=o.Range,v=[];for(let u=1;u<=n.ContentControls.Count;u++)try{const d=n.ContentControls.Item(u);if(!d)continue;const m=(d.Title||"").trim(),P=d.Range;if(i.Start<P.End&&i.End>P.Start){let U=null,N=!1,W=!1;if(!m)W=!0,U=`empty_${d.ID||Date.now()}`;else if(m.startsWith("任务_")||m.startsWith("任务增强_")||m.startsWith("已完成_")||m.startsWith("已完成增强_")||m.startsWith("异常_")||m.startsWith("异常增强_")||m.startsWith("已停止_")||m.startsWith("已停止增强_"))m.startsWith("任务增强_")?(U=m.substring(5),N=!0):m.startsWith("任务_")?U=m.substring(3):m.startsWith("已完成增强_")?(U=m.substring(6),N=!0):m.startsWith("已完成_")?U=m.substring(4):m.startsWith("异常增强_")?(U=m.substring(5),N=!0):m.startsWith("异常_")?U=m.substring(3):m.startsWith("已停止增强_")?(U=m.substring(6),N=!0):m.startsWith("已停止_")&&(U=m.substring(4));else continue;if(U){let ee;i.Start>=P.Start&&i.End<=P.End?ee="completely_within":i.Start<=P.Start&&i.End>=P.End?ee="completely_contains":ee="partial_overlap",v.push({taskId:U,control:d,task:W?null:a[U]||null,isEnhanced:N,isEmptyTitle:W,overlapType:ee,controlRange:{start:P.Start,end:P.End},selectionRange:{start:i.Start,end:i.End}})}}}catch{continue}return v.length===0?null:{primary:v[0],all:v}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},tt=()=>{We.value=!0},Ze=()=>{We.value=!1},st=async(n,o=!1)=>{tt();try{await de(n,o)}finally{Ze()}},it=n=>new Promise((o,i)=>{$.show=!0,$.message=n,$.resolveCallback=o,$.rejectCallback=i}),bt=n=>{$.show=!1,n&&$.resolveCallback?$.resolveCallback(!0):$.resolveCallback&&$.resolveCallback(!1),$.resolveCallback=null,$.rejectCallback=null},l=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await V.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(I.value=n.data.subject),n.data.stage&&(M.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${I.value})和年级(${M.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},c=async()=>{try{if(I.value||M.value){t.value+=`<span class="log-item info">正在保存学科(${I.value})和年级(${M.value})设置到服务器...</span><br/>`;const n=await V.setSubjectAndStage(I.value,M.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}};return{docName:e,selected:s,logger:t,map:a,watchedDir:b,subject:I,stage:M,subjectOptions:O,stageOptions:R,fetchWatchedDir:z,clearLog:L,getCurrentDocument:B,checkDocumentFormat:h,getTaskStatusClass:X,getTaskStatusText:D,getElapsedTime:J,terminateTask:f,stopTaskWithoutRemovingControl:G,run1:qe,run2:Ke,getHeaderStatusClass:Xe,getRunningTasksCount:ge,getCompletedTasksCount:xe,getErrorTasksCount:je,setupLifecycle:Je,navigateToTaskControl:Re,forceCleanAllTasks:Me,ws:ae,wsMessages:re,initWebSocket:we,handleWatcherEvent:Te,urlMonitorTasks:j,monitorUrlForTask:ue,stopUrlMonitoring:ie,getUrlMonitorStatus:Ie,forceUrlCheck:Q,cleanupUrlMonitoringTasks:De,tryRemoveTaskPlaceholder:de,isLoading:We,isSelectionInTaskControl:Be,tryRemoveTaskPlaceholderWithLoading:st,showConfirm:it,handleConfirm:bt,confirmDialog:$,loadSubjectAndStage:l,saveSubjectAndStage:c}}const fn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:_e.getVersionConfig(),selectedEdition:_e.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,userNameClickCount:0,userNameClickTimer:null,showSaveMethodDialog:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),r=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${r}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await V.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await V[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await V.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await V.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await V.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await V.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await V.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await V.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await V.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await _e.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await Xs()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await V.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await V.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},handleUserNameClick(){this.userNameClickTimer&&clearTimeout(this.userNameClickTimer),this.userNameClickCount++,this.userNameClickCount>=3?(this.showSaveMethodDialog=!0,this.userNameClickCount=0):this.userNameClickTimer=setTimeout(()=>{this.userNameClickCount=0},2e3)},async fetchSaveMethod(){try{const e=await V.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await V.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=V.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=V.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=V.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=_e.onVersionChange(e=>{this.versionConfig=_e.getVersionConfig(),this.selectedEdition=_e.getEdition()}),await V.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},hn={class:"file-watcher"},vn={class:"settings-modal"},gn={class:"modal-content"},mn={class:"modal-header"},bn={class:"version-tag"},wn={class:"header-actions"},yn={key:0,class:"modal-body"},xn={class:"status-section"},Cn={class:"status-item"},Tn={class:"status-item"},kn={class:"directory-section"},Sn={class:"directory-form"},En={class:"form-group"},$n=["placeholder"],An=["disabled"],Mn={class:"directory-section"},Dn={class:"directory-form"},On={class:"form-group"},_n=["placeholder"],Pn=["disabled"],In={class:"directory-section"},Un={class:"directory-form"},Rn={class:"form-group"},Ln=["placeholder"],Fn=["disabled"],jn={class:"events-section"},Wn={class:"events-list"},Bn={class:"event-time"},Nn={class:"event-message"},Vn={class:"modal-header"},Hn={class:"modal-body"},zn={class:"save-method-section"},qn={class:"radio-group"},Yn={class:"radio-item"},Kn={class:"radio-item"},Xn={class:"radio-item"},Jn={class:"radio-item"},Gn={class:"modal-footer"},Zn={class:"modal-footer"},Qn=["disabled"];function ea(e,s,t,a,r,g){var y,S,b,x,$,I;return q(),Y("div",hn,[p("div",vn,[p("div",gn,[p("div",mn,[p("h3",null,[Js(te(r.versionConfig.shortName)+"设置 ",1),p("span",bn,te(r.versionConfig.appVersion),1),g.userInfo?(q(),Y("span",{key:0,class:"user-info",onClick:s[0]||(s[0]=(...M)=>g.handleUserNameClick&&g.handleUserNameClick(...M))},"欢迎您，"+te(g.userInfo.nickname),1)):Z("",!0)]),p("div",wn,[p("button",{class:"logout-btn",onClick:s[1]||(s[1]=(...M)=>g.handleLogout&&g.handleLogout(...M)),title:"退出登录"},s[26]||(s[26]=[p("span",{class:"icon-logout"},null,-1)])),p("button",{class:"close-btn",onClick:s[2]||(s[2]=()=>e.$emit("close"))},"×")])]),((b=(S=(y=g.userInfo)==null?void 0:y.orgs)==null?void 0:S[0])==null?void 0:b.orgId)===2?(q(),Y("div",yn,[p("div",xn,[p("div",Cn,[s[27]||(s[27]=p("span",{class:"label"},"状态：",-1)),p("span",{class:ke(["status-badge",r.status.status])},te(r.status.status==="running"?"运行中":"已停止"),3)]),p("div",Tn,[s[28]||(s[28]=p("span",{class:"label"},"本次上传：",-1)),p("span",null,te(r.status.processedFiles||0)+" 个文件",1)])]),Z("",!0),p("div",kn,[s[32]||(s[32]=p("h4",null,"上传目录设置",-1)),p("div",Sn,[p("div",En,[s[31]||(s[31]=p("span",{class:"label"},"路径：",-1)),Oe(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[5]||(s[5]=M=>r.newWatchDir=M),placeholder:r.status.watchDir||"C:\\Temp"},null,8,$n),[[Wt,r.newWatchDir]]),p("button",{class:"action-btn",onClick:s[6]||(s[6]=(...M)=>g.updateWatchDir&&g.updateWatchDir(...M)),disabled:r.isUpdating||!r.newWatchDir},te(r.isUpdating?"更新中...":"更新目录"),9,An)]),r.updateMessage?(q(),Y("div",{key:0,class:ke(["update-message",r.updateSuccess?"success":"error"])},te(r.updateMessage),3)):Z("",!0)])]),p("div",Mn,[s[34]||(s[34]=p("h4",null,"下载目录设置",-1)),p("div",Dn,[p("div",On,[s[33]||(s[33]=p("span",{class:"label"},"路径：",-1)),Oe(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[7]||(s[7]=M=>r.newDownloadPath=M),placeholder:r.downloadPath||"C:\\Temp\\Downloads"},null,8,_n),[[Wt,r.newDownloadPath]]),p("button",{class:"action-btn",onClick:s[8]||(s[8]=(...M)=>g.updateDownloadPath&&g.updateDownloadPath(...M)),disabled:r.isUpdatingDownloadPath||!r.newDownloadPath},te(r.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Pn)]),r.downloadPathUpdateMessage?(q(),Y("div",{key:0,class:ke(["update-message",r.downloadPathUpdateSuccess?"success":"error"])},te(r.downloadPathUpdateMessage),3)):Z("",!0)])]),p("div",In,[s[36]||(s[36]=p("h4",null,"配置目录设置",-1)),p("div",Un,[p("div",Rn,[s[35]||(s[35]=p("span",{class:"label"},"路径：",-1)),Oe(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[9]||(s[9]=M=>r.newAddonConfigPath=M),placeholder:r.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,Ln),[[Wt,r.newAddonConfigPath]]),p("button",{class:"action-btn",onClick:s[10]||(s[10]=(...M)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...M)),disabled:r.isUpdatingAddonConfigPath||!r.newAddonConfigPath},te(r.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,Fn)]),r.addonConfigPathUpdateMessage?(q(),Y("div",{key:0,class:ke(["update-message",r.addonConfigPathUpdateSuccess?"success":"error"])},te(r.addonConfigPathUpdateMessage),3)):Z("",!0)])]),p("div",jn,[s[37]||(s[37]=p("h4",null,"最近事件",-1)),p("div",Wn,[(q(!0),Y(xt,null,Ct(r.recentEvents,(M,O)=>(q(),Y("div",{key:O,class:"event-item"},[p("span",Bn,te(g.formatTime(M.timestamp)),1),p("span",{class:ke(["event-type",M.eventType])},te(g.getEventTypeText(M.eventType)),3),p("span",Nn,te(g.getEventMessage(M)),1)]))),128))])])])):Z("",!0),Z("",!0),r.showSaveMethodDialog?(q(),Y("div",{key:2,class:"modal-overlay",onClick:s[24]||(s[24]=M=>r.showSaveMethodDialog=!1)},[p("div",{class:"modal-content save-method-modal",onClick:s[23]||(s[23]=Qe(()=>{},["stop"]))},[p("div",Vn,[s[42]||(s[42]=p("h4",null,"Debug - 保存方式设置",-1)),p("button",{class:"close-btn",onClick:s[13]||(s[13]=M=>r.showSaveMethodDialog=!1)},"×")]),p("div",Hn,[p("div",zn,[s[47]||(s[47]=p("h5",null,"选择保存方式：",-1)),p("div",qn,[p("label",Yn,[Oe(p("input",{type:"radio","onUpdate:modelValue":s[14]||(s[14]=M=>r.currentSaveMethod=M),value:"method1",onChange:s[15]||(s[15]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Ot,r.currentSaveMethod]]),s[43]||(s[43]=p("span",{class:"radio-label"},"方式一",-1))]),p("label",Kn,[Oe(p("input",{type:"radio","onUpdate:modelValue":s[16]||(s[16]=M=>r.currentSaveMethod=M),value:"method2",onChange:s[17]||(s[17]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Ot,r.currentSaveMethod]]),s[44]||(s[44]=p("span",{class:"radio-label"},"方式二 (默认)",-1))]),p("label",Xn,[Oe(p("input",{type:"radio","onUpdate:modelValue":s[18]||(s[18]=M=>r.currentSaveMethod=M),value:"method3",onChange:s[19]||(s[19]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Ot,r.currentSaveMethod]]),s[45]||(s[45]=p("span",{class:"radio-label"},"方式三",-1))]),p("label",Jn,[Oe(p("input",{type:"radio","onUpdate:modelValue":s[20]||(s[20]=M=>r.currentSaveMethod=M),value:"method4",onChange:s[21]||(s[21]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Ot,r.currentSaveMethod]]),s[46]||(s[46]=p("span",{class:"radio-label"},"方式四",-1))])]),r.saveMethodUpdateMessage?(q(),Y("div",{key:0,class:ke(["update-message",r.saveMethodUpdateSuccess?"success":"error"])},te(r.saveMethodUpdateMessage),3)):Z("",!0)])]),p("div",Gn,[p("button",{class:"action-btn",onClick:s[22]||(s[22]=M=>r.showSaveMethodDialog=!1)},"关闭")])])])):Z("",!0),p("div",Zn,[p("button",{class:ke(["control-btn",r.status.status==="running"?"stop":"start"]),onClick:s[25]||(s[25]=(...M)=>g.controlService&&g.controlService(...M)),disabled:!((I=($=(x=g.userInfo)==null?void 0:x.orgs)==null?void 0:$[0])!=null&&I.orgId)===2},te(r.status.status==="running"?"停止服务":"启动服务"),11,Qn)])])])])}const ta=$s(fn,[["render",ea],["__scopeId","data-v-f063b704"]]);var me="top",Ee="bottom",$e="right",be="left",Gt="auto",Mt=[me,Ee,$e,be],ht="start",$t="end",sa="clippingParents",Ms="viewport",yt="popper",na="reference",ls=Mt.reduce(function(e,s){return e.concat([s+"-"+ht,s+"-"+$t])},[]),Ds=[].concat(Mt,[Gt]).reduce(function(e,s){return e.concat([s,s+"-"+ht,s+"-"+$t])},[]),aa="beforeRead",oa="read",ra="afterRead",ia="beforeMain",la="main",ca="afterMain",ua="beforeWrite",da="write",pa="afterWrite",fa=[aa,oa,ra,ia,la,ca,ua,da,pa];function He(e){return e?(e.nodeName||"").toLowerCase():null}function Ce(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function rt(e){var s=Ce(e).Element;return e instanceof s||e instanceof Element}function Se(e){var s=Ce(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function Zt(e){if(typeof ShadowRoot>"u")return!1;var s=Ce(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function ha(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},r=s.attributes[t]||{},g=s.elements[t];!Se(g)||!He(g)||(Object.assign(g.style,a),Object.keys(r).forEach(function(y){var S=r[y];S===!1?g.removeAttribute(y):g.setAttribute(y,S===!0?"":S)}))})}function va(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var r=s.elements[a],g=s.attributes[a]||{},y=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),S=y.reduce(function(b,x){return b[x]="",b},{});!Se(r)||!He(r)||(Object.assign(r.style,S),Object.keys(g).forEach(function(b){r.removeAttribute(b)}))})}}const Os={name:"applyStyles",enabled:!0,phase:"write",fn:ha,effect:va,requires:["computeStyles"]};function Ve(e){return e.split("-")[0]}var at=Math.max,Ut=Math.min,vt=Math.round;function Yt(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function _s(){return!/^((?!chrome|android).)*safari/i.test(Yt())}function gt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),r=1,g=1;s&&Se(e)&&(r=e.offsetWidth>0&&vt(a.width)/e.offsetWidth||1,g=e.offsetHeight>0&&vt(a.height)/e.offsetHeight||1);var y=rt(e)?Ce(e):window,S=y.visualViewport,b=!_s()&&t,x=(a.left+(b&&S?S.offsetLeft:0))/r,$=(a.top+(b&&S?S.offsetTop:0))/g,I=a.width/r,M=a.height/g;return{width:I,height:M,top:$,right:x+I,bottom:$+M,left:x,x,y:$}}function Qt(e){var s=gt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function Ps(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&Zt(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function Ye(e){return Ce(e).getComputedStyle(e)}function ga(e){return["table","td","th"].indexOf(He(e))>=0}function et(e){return((rt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Lt(e){return He(e)==="html"?e:e.assignedSlot||e.parentNode||(Zt(e)?e.host:null)||et(e)}function cs(e){return!Se(e)||Ye(e).position==="fixed"?null:e.offsetParent}function ma(e){var s=/firefox/i.test(Yt()),t=/Trident/i.test(Yt());if(t&&Se(e)){var a=Ye(e);if(a.position==="fixed")return null}var r=Lt(e);for(Zt(r)&&(r=r.host);Se(r)&&["html","body"].indexOf(He(r))<0;){var g=Ye(r);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return r;r=r.parentNode}return null}function Dt(e){for(var s=Ce(e),t=cs(e);t&&ga(t)&&Ye(t).position==="static";)t=cs(t);return t&&(He(t)==="html"||He(t)==="body"&&Ye(t).position==="static")?s:t||ma(e)||s}function es(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function kt(e,s,t){return at(e,Ut(s,t))}function ba(e,s,t){var a=kt(e,s,t);return a>t?t:a}function Is(){return{top:0,right:0,bottom:0,left:0}}function Us(e){return Object.assign({},Is(),e)}function Rs(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var wa=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Us(typeof s!="number"?s:Rs(s,Mt))};function ya(e){var s,t=e.state,a=e.name,r=e.options,g=t.elements.arrow,y=t.modifiersData.popperOffsets,S=Ve(t.placement),b=es(S),x=[be,$e].indexOf(S)>=0,$=x?"height":"width";if(!(!g||!y)){var I=wa(r.padding,t),M=Qt(g),O=b==="y"?me:be,H=b==="y"?Ee:$e,R=t.rects.reference[$]+t.rects.reference[b]-y[b]-t.rects.popper[$],z=y[b]-t.rects.reference[b],K=Dt(g),L=K?b==="y"?K.clientHeight||0:K.clientWidth||0:0,B=R/2-z/2,h=I[O],X=L-M[$]-I[H],D=L/2-M[$]/2+B,J=kt(h,D,X),G=b;t.modifiersData[a]=(s={},s[G]=J,s.centerOffset=J-D,s)}}function xa(e){var s=e.state,t=e.options,a=t.element,r=a===void 0?"[data-popper-arrow]":a;r!=null&&(typeof r=="string"&&(r=s.elements.popper.querySelector(r),!r)||Ps(s.elements.popper,r)&&(s.elements.arrow=r))}const Ca={name:"arrow",enabled:!0,phase:"main",fn:ya,effect:xa,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function mt(e){return e.split("-")[1]}var Ta={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ka(e,s){var t=e.x,a=e.y,r=s.devicePixelRatio||1;return{x:vt(t*r)/r||0,y:vt(a*r)/r||0}}function us(e){var s,t=e.popper,a=e.popperRect,r=e.placement,g=e.variation,y=e.offsets,S=e.position,b=e.gpuAcceleration,x=e.adaptive,$=e.roundOffsets,I=e.isFixed,M=y.x,O=M===void 0?0:M,H=y.y,R=H===void 0?0:H,z=typeof $=="function"?$({x:O,y:R}):{x:O,y:R};O=z.x,R=z.y;var K=y.hasOwnProperty("x"),L=y.hasOwnProperty("y"),B=be,h=me,X=window;if(x){var D=Dt(t),J="clientHeight",G="clientWidth";if(D===Ce(t)&&(D=et(t),Ye(D).position!=="static"&&S==="absolute"&&(J="scrollHeight",G="scrollWidth")),D=D,r===me||(r===be||r===$e)&&g===$t){h=Ee;var f=I&&D===X&&X.visualViewport?X.visualViewport.height:D[J];R-=f-a.height,R*=b?1:-1}if(r===be||(r===me||r===Ee)&&g===$t){B=$e;var w=I&&D===X&&X.visualViewport?X.visualViewport.width:D[G];O-=w-a.width,O*=b?1:-1}}var k=Object.assign({position:S},x&&Ta),C=$===!0?ka({x:O,y:R},Ce(t)):{x:O,y:R};if(O=C.x,R=C.y,b){var E;return Object.assign({},k,(E={},E[h]=L?"0":"",E[B]=K?"0":"",E.transform=(X.devicePixelRatio||1)<=1?"translate("+O+"px, "+R+"px)":"translate3d("+O+"px, "+R+"px, 0)",E))}return Object.assign({},k,(s={},s[h]=L?R+"px":"",s[B]=K?O+"px":"",s.transform="",s))}function Sa(e){var s=e.state,t=e.options,a=t.gpuAcceleration,r=a===void 0?!0:a,g=t.adaptive,y=g===void 0?!0:g,S=t.roundOffsets,b=S===void 0?!0:S,x={placement:Ve(s.placement),variation:mt(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:r,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,us(Object.assign({},x,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:y,roundOffsets:b})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,us(Object.assign({},x,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:b})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Ea={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Sa,data:{}};var _t={passive:!0};function $a(e){var s=e.state,t=e.instance,a=e.options,r=a.scroll,g=r===void 0?!0:r,y=a.resize,S=y===void 0?!0:y,b=Ce(s.elements.popper),x=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&x.forEach(function($){$.addEventListener("scroll",t.update,_t)}),S&&b.addEventListener("resize",t.update,_t),function(){g&&x.forEach(function($){$.removeEventListener("scroll",t.update,_t)}),S&&b.removeEventListener("resize",t.update,_t)}}const Aa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:$a,data:{}};var Ma={left:"right",right:"left",bottom:"top",top:"bottom"};function It(e){return e.replace(/left|right|bottom|top/g,function(s){return Ma[s]})}var Da={start:"end",end:"start"};function ds(e){return e.replace(/start|end/g,function(s){return Da[s]})}function ts(e){var s=Ce(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function ss(e){return gt(et(e)).left+ts(e).scrollLeft}function Oa(e,s){var t=Ce(e),a=et(e),r=t.visualViewport,g=a.clientWidth,y=a.clientHeight,S=0,b=0;if(r){g=r.width,y=r.height;var x=_s();(x||!x&&s==="fixed")&&(S=r.offsetLeft,b=r.offsetTop)}return{width:g,height:y,x:S+ss(e),y:b}}function _a(e){var s,t=et(e),a=ts(e),r=(s=e.ownerDocument)==null?void 0:s.body,g=at(t.scrollWidth,t.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),y=at(t.scrollHeight,t.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),S=-a.scrollLeft+ss(e),b=-a.scrollTop;return Ye(r||t).direction==="rtl"&&(S+=at(t.clientWidth,r?r.clientWidth:0)-g),{width:g,height:y,x:S,y:b}}function ns(e){var s=Ye(e),t=s.overflow,a=s.overflowX,r=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+r+a)}function Ls(e){return["html","body","#document"].indexOf(He(e))>=0?e.ownerDocument.body:Se(e)&&ns(e)?e:Ls(Lt(e))}function St(e,s){var t;s===void 0&&(s=[]);var a=Ls(e),r=a===((t=e.ownerDocument)==null?void 0:t.body),g=Ce(a),y=r?[g].concat(g.visualViewport||[],ns(a)?a:[]):a,S=s.concat(y);return r?S:S.concat(St(Lt(y)))}function Kt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Pa(e,s){var t=gt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function ps(e,s,t){return s===Ms?Kt(Oa(e,t)):rt(s)?Pa(s,t):Kt(_a(et(e)))}function Ia(e){var s=St(Lt(e)),t=["absolute","fixed"].indexOf(Ye(e).position)>=0,a=t&&Se(e)?Dt(e):e;return rt(a)?s.filter(function(r){return rt(r)&&Ps(r,a)&&He(r)!=="body"}):[]}function Ua(e,s,t,a){var r=s==="clippingParents"?Ia(e):[].concat(s),g=[].concat(r,[t]),y=g[0],S=g.reduce(function(b,x){var $=ps(e,x,a);return b.top=at($.top,b.top),b.right=Ut($.right,b.right),b.bottom=Ut($.bottom,b.bottom),b.left=at($.left,b.left),b},ps(e,y,a));return S.width=S.right-S.left,S.height=S.bottom-S.top,S.x=S.left,S.y=S.top,S}function Fs(e){var s=e.reference,t=e.element,a=e.placement,r=a?Ve(a):null,g=a?mt(a):null,y=s.x+s.width/2-t.width/2,S=s.y+s.height/2-t.height/2,b;switch(r){case me:b={x:y,y:s.y-t.height};break;case Ee:b={x:y,y:s.y+s.height};break;case $e:b={x:s.x+s.width,y:S};break;case be:b={x:s.x-t.width,y:S};break;default:b={x:s.x,y:s.y}}var x=r?es(r):null;if(x!=null){var $=x==="y"?"height":"width";switch(g){case ht:b[x]=b[x]-(s[$]/2-t[$]/2);break;case $t:b[x]=b[x]+(s[$]/2-t[$]/2);break}}return b}function At(e,s){s===void 0&&(s={});var t=s,a=t.placement,r=a===void 0?e.placement:a,g=t.strategy,y=g===void 0?e.strategy:g,S=t.boundary,b=S===void 0?sa:S,x=t.rootBoundary,$=x===void 0?Ms:x,I=t.elementContext,M=I===void 0?yt:I,O=t.altBoundary,H=O===void 0?!1:O,R=t.padding,z=R===void 0?0:R,K=Us(typeof z!="number"?z:Rs(z,Mt)),L=M===yt?na:yt,B=e.rects.popper,h=e.elements[H?L:M],X=Ua(rt(h)?h:h.contextElement||et(e.elements.popper),b,$,y),D=gt(e.elements.reference),J=Fs({reference:D,element:B,strategy:"absolute",placement:r}),G=Kt(Object.assign({},B,J)),f=M===yt?G:D,w={top:X.top-f.top+K.top,bottom:f.bottom-X.bottom+K.bottom,left:X.left-f.left+K.left,right:f.right-X.right+K.right},k=e.modifiersData.offset;if(M===yt&&k){var C=k[r];Object.keys(w).forEach(function(E){var A=[$e,Ee].indexOf(E)>=0?1:-1,_=[me,Ee].indexOf(E)>=0?"y":"x";w[E]+=C[_]*A})}return w}function Ra(e,s){s===void 0&&(s={});var t=s,a=t.placement,r=t.boundary,g=t.rootBoundary,y=t.padding,S=t.flipVariations,b=t.allowedAutoPlacements,x=b===void 0?Ds:b,$=mt(a),I=$?S?ls:ls.filter(function(H){return mt(H)===$}):Mt,M=I.filter(function(H){return x.indexOf(H)>=0});M.length===0&&(M=I);var O=M.reduce(function(H,R){return H[R]=At(e,{placement:R,boundary:r,rootBoundary:g,padding:y})[Ve(R)],H},{});return Object.keys(O).sort(function(H,R){return O[H]-O[R]})}function La(e){if(Ve(e)===Gt)return[];var s=It(e);return[ds(e),s,ds(s)]}function Fa(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var r=t.mainAxis,g=r===void 0?!0:r,y=t.altAxis,S=y===void 0?!0:y,b=t.fallbackPlacements,x=t.padding,$=t.boundary,I=t.rootBoundary,M=t.altBoundary,O=t.flipVariations,H=O===void 0?!0:O,R=t.allowedAutoPlacements,z=s.options.placement,K=Ve(z),L=K===z,B=b||(L||!H?[It(z)]:La(z)),h=[z].concat(B).reduce(function(j,ue){return j.concat(Ve(ue)===Gt?Ra(s,{placement:ue,boundary:$,rootBoundary:I,padding:x,flipVariations:H,allowedAutoPlacements:R}):ue)},[]),X=s.rects.reference,D=s.rects.popper,J=new Map,G=!0,f=h[0],w=0;w<h.length;w++){var k=h[w],C=Ve(k),E=mt(k)===ht,A=[me,Ee].indexOf(C)>=0,_=A?"width":"height",T=At(s,{placement:k,boundary:$,rootBoundary:I,altBoundary:M,padding:x}),F=A?E?$e:be:E?Ee:me;X[_]>D[_]&&(F=It(F));var ae=It(F),re=[];if(g&&re.push(T[C]<=0),S&&re.push(T[F]<=0,T[ae]<=0),re.every(function(j){return j})){f=k,G=!1;break}J.set(k,re)}if(G)for(var ce=H?3:1,Ae=function(ue){var ie=h.find(function(Ie){var Q=J.get(Ie);if(Q)return Q.slice(0,ue).every(function(Te){return Te})});if(ie)return f=ie,"break"},ve=ce;ve>0;ve--){var we=Ae(ve);if(we==="break")break}s.placement!==f&&(s.modifiersData[a]._skip=!0,s.placement=f,s.reset=!0)}}const ja={name:"flip",enabled:!0,phase:"main",fn:Fa,requiresIfExists:["offset"],data:{_skip:!1}};function fs(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function hs(e){return[me,$e,Ee,be].some(function(s){return e[s]>=0})}function Wa(e){var s=e.state,t=e.name,a=s.rects.reference,r=s.rects.popper,g=s.modifiersData.preventOverflow,y=At(s,{elementContext:"reference"}),S=At(s,{altBoundary:!0}),b=fs(y,a),x=fs(S,r,g),$=hs(b),I=hs(x);s.modifiersData[t]={referenceClippingOffsets:b,popperEscapeOffsets:x,isReferenceHidden:$,hasPopperEscaped:I},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":$,"data-popper-escaped":I})}const Ba={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Wa};function Na(e,s,t){var a=Ve(e),r=[be,me].indexOf(a)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,y=g[0],S=g[1];return y=y||0,S=(S||0)*r,[be,$e].indexOf(a)>=0?{x:S,y}:{x:y,y:S}}function Va(e){var s=e.state,t=e.options,a=e.name,r=t.offset,g=r===void 0?[0,0]:r,y=Ds.reduce(function($,I){return $[I]=Na(I,s.rects,g),$},{}),S=y[s.placement],b=S.x,x=S.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=b,s.modifiersData.popperOffsets.y+=x),s.modifiersData[a]=y}const Ha={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Va};function za(e){var s=e.state,t=e.name;s.modifiersData[t]=Fs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const qa={name:"popperOffsets",enabled:!0,phase:"read",fn:za,data:{}};function Ya(e){return e==="x"?"y":"x"}function Ka(e){var s=e.state,t=e.options,a=e.name,r=t.mainAxis,g=r===void 0?!0:r,y=t.altAxis,S=y===void 0?!1:y,b=t.boundary,x=t.rootBoundary,$=t.altBoundary,I=t.padding,M=t.tether,O=M===void 0?!0:M,H=t.tetherOffset,R=H===void 0?0:H,z=At(s,{boundary:b,rootBoundary:x,padding:I,altBoundary:$}),K=Ve(s.placement),L=mt(s.placement),B=!L,h=es(K),X=Ya(h),D=s.modifiersData.popperOffsets,J=s.rects.reference,G=s.rects.popper,f=typeof R=="function"?R(Object.assign({},s.rects,{placement:s.placement})):R,w=typeof f=="number"?{mainAxis:f,altAxis:f}:Object.assign({mainAxis:0,altAxis:0},f),k=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,C={x:0,y:0};if(D){if(g){var E,A=h==="y"?me:be,_=h==="y"?Ee:$e,T=h==="y"?"height":"width",F=D[h],ae=F+z[A],re=F-z[_],ce=O?-G[T]/2:0,Ae=L===ht?J[T]:G[T],ve=L===ht?-G[T]:-J[T],we=s.elements.arrow,j=O&&we?Qt(we):{width:0,height:0},ue=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Is(),ie=ue[A],Ie=ue[_],Q=kt(0,J[T],j[T]),Te=B?J[T]/2-ce-Q-ie-w.mainAxis:Ae-Q-ie-w.mainAxis,ye=B?-J[T]/2+ce+Q+Ie+w.mainAxis:ve+Q+Ie+w.mainAxis,Ue=s.elements.arrow&&Dt(s.elements.arrow),ze=Ue?h==="y"?Ue.clientTop||0:Ue.clientLeft||0:0,de=(E=k==null?void 0:k[h])!=null?E:0,Re=F+Te-de-ze,Le=F+ye-de,Fe=kt(O?Ut(ae,Re):ae,F,O?at(re,Le):re);D[h]=Fe,C[h]=Fe-F}if(S){var qe,Ke=h==="x"?me:be,Xe=h==="x"?Ee:$e,ge=D[X],xe=X==="y"?"height":"width",je=ge+z[Ke],Me=ge-z[Xe],De=[me,be].indexOf(K)!==-1,Je=(qe=k==null?void 0:k[X])!=null?qe:0,Ge=De?je:ge-J[xe]-G[xe]-Je+w.altAxis,We=De?ge+J[xe]+G[xe]-Je-w.altAxis:Me,Be=O&&De?ba(Ge,ge,We):kt(O?Ge:je,ge,O?We:Me);D[X]=Be,C[X]=Be-ge}s.modifiersData[a]=C}}const Xa={name:"preventOverflow",enabled:!0,phase:"main",fn:Ka,requiresIfExists:["offset"]};function Ja(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Ga(e){return e===Ce(e)||!Se(e)?ts(e):Ja(e)}function Za(e){var s=e.getBoundingClientRect(),t=vt(s.width)/e.offsetWidth||1,a=vt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function Qa(e,s,t){t===void 0&&(t=!1);var a=Se(s),r=Se(s)&&Za(s),g=et(s),y=gt(e,r,t),S={scrollLeft:0,scrollTop:0},b={x:0,y:0};return(a||!a&&!t)&&((He(s)!=="body"||ns(g))&&(S=Ga(s)),Se(s)?(b=gt(s,!0),b.x+=s.clientLeft,b.y+=s.clientTop):g&&(b.x=ss(g))),{x:y.left+S.scrollLeft-b.x,y:y.top+S.scrollTop-b.y,width:y.width,height:y.height}}function eo(e){var s=new Map,t=new Set,a=[];e.forEach(function(g){s.set(g.name,g)});function r(g){t.add(g.name);var y=[].concat(g.requires||[],g.requiresIfExists||[]);y.forEach(function(S){if(!t.has(S)){var b=s.get(S);b&&r(b)}}),a.push(g)}return e.forEach(function(g){t.has(g.name)||r(g)}),a}function to(e){var s=eo(e);return fa.reduce(function(t,a){return t.concat(s.filter(function(r){return r.phase===a}))},[])}function so(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function no(e){var s=e.reduce(function(t,a){var r=t[a.name];return t[a.name]=r?Object.assign({},r,a,{options:Object.assign({},r.options,a.options),data:Object.assign({},r.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var vs={placement:"bottom",modifiers:[],strategy:"absolute"};function gs(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function ao(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,r=s.defaultOptions,g=r===void 0?vs:r;return function(S,b,x){x===void 0&&(x=g);var $={placement:"bottom",orderedModifiers:[],options:Object.assign({},vs,g),modifiersData:{},elements:{reference:S,popper:b},attributes:{},styles:{}},I=[],M=!1,O={state:$,setOptions:function(K){var L=typeof K=="function"?K($.options):K;R(),$.options=Object.assign({},g,$.options,L),$.scrollParents={reference:rt(S)?St(S):S.contextElement?St(S.contextElement):[],popper:St(b)};var B=to(no([].concat(a,$.options.modifiers)));return $.orderedModifiers=B.filter(function(h){return h.enabled}),H(),O.update()},forceUpdate:function(){if(!M){var K=$.elements,L=K.reference,B=K.popper;if(gs(L,B)){$.rects={reference:Qa(L,Dt(B),$.options.strategy==="fixed"),popper:Qt(B)},$.reset=!1,$.placement=$.options.placement,$.orderedModifiers.forEach(function(w){return $.modifiersData[w.name]=Object.assign({},w.data)});for(var h=0;h<$.orderedModifiers.length;h++){if($.reset===!0){$.reset=!1,h=-1;continue}var X=$.orderedModifiers[h],D=X.fn,J=X.options,G=J===void 0?{}:J,f=X.name;typeof D=="function"&&($=D({state:$,options:G,name:f,instance:O})||$)}}}},update:so(function(){return new Promise(function(z){O.forceUpdate(),z($)})}),destroy:function(){R(),M=!0}};if(!gs(S,b))return O;O.setOptions(x).then(function(z){!M&&x.onFirstUpdate&&x.onFirstUpdate(z)});function H(){$.orderedModifiers.forEach(function(z){var K=z.name,L=z.options,B=L===void 0?{}:L,h=z.effect;if(typeof h=="function"){var X=h({state:$,name:K,instance:O,options:B}),D=function(){};I.push(X||D)}})}function R(){I.forEach(function(z){return z()}),I=[]}return O}}var oo=[Aa,qa,Ea,Os,Ha,ja,Xa,Ca,Ba],ro=ao({defaultModifiers:oo}),io="tippy-box",js="tippy-content",lo="tippy-backdrop",Ws="tippy-arrow",Bs="tippy-svg-arrow",nt={passive:!0,capture:!0},Ns=function(){return document.body};function Nt(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function as(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function Vs(e,s){return typeof e=="function"?e.apply(void 0,s):e}function ms(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function co(e){return e.split(/\s+/).filter(Boolean)}function ft(e){return[].concat(e)}function bs(e,s){e.indexOf(s)===-1&&e.push(s)}function uo(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function po(e){return e.split("-")[0]}function Rt(e){return[].slice.call(e)}function ws(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Et(){return document.createElement("div")}function Ft(e){return["Element","Fragment"].some(function(s){return as(e,s)})}function fo(e){return as(e,"NodeList")}function ho(e){return as(e,"MouseEvent")}function vo(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function go(e){return Ft(e)?[e]:fo(e)?Rt(e):Array.isArray(e)?e:Rt(document.querySelectorAll(e))}function Vt(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function ys(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function mo(e){var s,t=ft(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function bo(e,s){var t=s.clientX,a=s.clientY;return e.every(function(r){var g=r.popperRect,y=r.popperState,S=r.props,b=S.interactiveBorder,x=po(y.placement),$=y.modifiersData.offset;if(!$)return!0;var I=x==="bottom"?$.top.y:0,M=x==="top"?$.bottom.y:0,O=x==="right"?$.left.x:0,H=x==="left"?$.right.x:0,R=g.top-a+I>b,z=a-g.bottom-M>b,K=g.left-t+O>b,L=t-g.right-H>b;return R||z||K||L})}function Ht(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(r){e[a](r,t)})}function xs(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var Ne={isTouch:!1},Cs=0;function wo(){Ne.isTouch||(Ne.isTouch=!0,window.performance&&document.addEventListener("mousemove",Hs))}function Hs(){var e=performance.now();e-Cs<20&&(Ne.isTouch=!1,document.removeEventListener("mousemove",Hs)),Cs=e}function yo(){var e=document.activeElement;if(vo(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function xo(){document.addEventListener("touchstart",wo,nt),window.addEventListener("blur",yo)}var Co=typeof window<"u"&&typeof document<"u",To=Co?!!window.msCrypto:!1,ko={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},So={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Pe=Object.assign({appendTo:Ns,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},ko,So),Eo=Object.keys(Pe),$o=function(s){var t=Object.keys(s);t.forEach(function(a){Pe[a]=s[a]})};function zs(e){var s=e.plugins||[],t=s.reduce(function(a,r){var g=r.name,y=r.defaultValue;if(g){var S;a[g]=e[g]!==void 0?e[g]:(S=Pe[g])!=null?S:y}return a},{});return Object.assign({},e,t)}function Ao(e,s){var t=s?Object.keys(zs(Object.assign({},Pe,{plugins:s}))):Eo,a=t.reduce(function(r,g){var y=(e.getAttribute("data-tippy-"+g)||"").trim();if(!y)return r;if(g==="content")r[g]=y;else try{r[g]=JSON.parse(y)}catch{r[g]=y}return r},{});return a}function Ts(e,s){var t=Object.assign({},s,{content:Vs(s.content,[e])},s.ignoreAttributes?{}:Ao(e,s.plugins));return t.aria=Object.assign({},Pe.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Mo=function(){return"innerHTML"};function Xt(e,s){e[Mo()]=s}function ks(e){var s=Et();return e===!0?s.className=Ws:(s.className=Bs,Ft(e)?s.appendChild(e):Xt(s,e)),s}function Ss(e,s){Ft(s.content)?(Xt(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?Xt(e,s.content):e.textContent=s.content)}function Jt(e){var s=e.firstElementChild,t=Rt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(js)}),arrow:t.find(function(a){return a.classList.contains(Ws)||a.classList.contains(Bs)}),backdrop:t.find(function(a){return a.classList.contains(lo)})}}function qs(e){var s=Et(),t=Et();t.className=io,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=Et();a.className=js,a.setAttribute("data-state","hidden"),Ss(a,e.props),s.appendChild(t),t.appendChild(a),r(e.props,e.props);function r(g,y){var S=Jt(s),b=S.box,x=S.content,$=S.arrow;y.theme?b.setAttribute("data-theme",y.theme):b.removeAttribute("data-theme"),typeof y.animation=="string"?b.setAttribute("data-animation",y.animation):b.removeAttribute("data-animation"),y.inertia?b.setAttribute("data-inertia",""):b.removeAttribute("data-inertia"),b.style.maxWidth=typeof y.maxWidth=="number"?y.maxWidth+"px":y.maxWidth,y.role?b.setAttribute("role",y.role):b.removeAttribute("role"),(g.content!==y.content||g.allowHTML!==y.allowHTML)&&Ss(x,e.props),y.arrow?$?g.arrow!==y.arrow&&(b.removeChild($),b.appendChild(ks(y.arrow))):b.appendChild(ks(y.arrow)):$&&b.removeChild($)}return{popper:s,onUpdate:r}}qs.$$tippy=!0;var Do=1,Pt=[],zt=[];function Oo(e,s){var t=Ts(e,Object.assign({},Pe,zs(ws(s)))),a,r,g,y=!1,S=!1,b=!1,x=!1,$,I,M,O=[],H=ms(Re,t.interactiveDebounce),R,z=Do++,K=null,L=uo(t.plugins),B={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},h={id:z,reference:e,popper:Et(),popperInstance:K,props:t,state:B,plugins:L,clearDelayTimeouts:Ge,setProps:We,setContent:Be,show:tt,hide:Ze,hideWithInteractivity:st,enable:De,disable:Je,unmount:it,destroy:bt};if(!t.render)return h;var X=t.render(h),D=X.popper,J=X.onUpdate;D.setAttribute("data-tippy-root",""),D.id="tippy-"+h.id,h.popper=D,e._tippy=h,D._tippy=h;var G=L.map(function(l){return l.fn(h)}),f=e.hasAttribute("aria-expanded");return Ue(),ce(),F(),ae("onCreate",[h]),t.showOnCreate&&je(),D.addEventListener("mouseenter",function(){h.props.interactive&&h.state.isVisible&&h.clearDelayTimeouts()}),D.addEventListener("mouseleave",function(){h.props.interactive&&h.props.trigger.indexOf("mouseenter")>=0&&A().addEventListener("mousemove",H)}),h;function w(){var l=h.props.touch;return Array.isArray(l)?l:[l,0]}function k(){return w()[0]==="hold"}function C(){var l;return!!((l=h.props.render)!=null&&l.$$tippy)}function E(){return R||e}function A(){var l=E().parentNode;return l?mo(l):document}function _(){return Jt(D)}function T(l){return h.state.isMounted&&!h.state.isVisible||Ne.isTouch||$&&$.type==="focus"?0:Nt(h.props.delay,l?0:1,Pe.delay)}function F(l){l===void 0&&(l=!1),D.style.pointerEvents=h.props.interactive&&!l?"":"none",D.style.zIndex=""+h.props.zIndex}function ae(l,c,n){if(n===void 0&&(n=!0),G.forEach(function(i){i[l]&&i[l].apply(i,c)}),n){var o;(o=h.props)[l].apply(o,c)}}function re(){var l=h.props.aria;if(l.content){var c="aria-"+l.content,n=D.id,o=ft(h.props.triggerTarget||e);o.forEach(function(i){var v=i.getAttribute(c);if(h.state.isVisible)i.setAttribute(c,v?v+" "+n:n);else{var u=v&&v.replace(n,"").trim();u?i.setAttribute(c,u):i.removeAttribute(c)}})}}function ce(){if(!(f||!h.props.aria.expanded)){var l=ft(h.props.triggerTarget||e);l.forEach(function(c){h.props.interactive?c.setAttribute("aria-expanded",h.state.isVisible&&c===E()?"true":"false"):c.removeAttribute("aria-expanded")})}}function Ae(){A().removeEventListener("mousemove",H),Pt=Pt.filter(function(l){return l!==H})}function ve(l){if(!(Ne.isTouch&&(b||l.type==="mousedown"))){var c=l.composedPath&&l.composedPath()[0]||l.target;if(!(h.props.interactive&&xs(D,c))){if(ft(h.props.triggerTarget||e).some(function(n){return xs(n,c)})){if(Ne.isTouch||h.state.isVisible&&h.props.trigger.indexOf("click")>=0)return}else ae("onClickOutside",[h,l]);h.props.hideOnClick===!0&&(h.clearDelayTimeouts(),h.hide(),S=!0,setTimeout(function(){S=!1}),h.state.isMounted||ie())}}}function we(){b=!0}function j(){b=!1}function ue(){var l=A();l.addEventListener("mousedown",ve,!0),l.addEventListener("touchend",ve,nt),l.addEventListener("touchstart",j,nt),l.addEventListener("touchmove",we,nt)}function ie(){var l=A();l.removeEventListener("mousedown",ve,!0),l.removeEventListener("touchend",ve,nt),l.removeEventListener("touchstart",j,nt),l.removeEventListener("touchmove",we,nt)}function Ie(l,c){Te(l,function(){!h.state.isVisible&&D.parentNode&&D.parentNode.contains(D)&&c()})}function Q(l,c){Te(l,c)}function Te(l,c){var n=_().box;function o(i){i.target===n&&(Ht(n,"remove",o),c())}if(l===0)return c();Ht(n,"remove",I),Ht(n,"add",o),I=o}function ye(l,c,n){n===void 0&&(n=!1);var o=ft(h.props.triggerTarget||e);o.forEach(function(i){i.addEventListener(l,c,n),O.push({node:i,eventType:l,handler:c,options:n})})}function Ue(){k()&&(ye("touchstart",de,{passive:!0}),ye("touchend",Le,{passive:!0})),co(h.props.trigger).forEach(function(l){if(l!=="manual")switch(ye(l,de),l){case"mouseenter":ye("mouseleave",Le);break;case"focus":ye(To?"focusout":"blur",Fe);break;case"focusin":ye("focusout",Fe);break}})}function ze(){O.forEach(function(l){var c=l.node,n=l.eventType,o=l.handler,i=l.options;c.removeEventListener(n,o,i)}),O=[]}function de(l){var c,n=!1;if(!(!h.state.isEnabled||qe(l)||S)){var o=((c=$)==null?void 0:c.type)==="focus";$=l,R=l.currentTarget,ce(),!h.state.isVisible&&ho(l)&&Pt.forEach(function(i){return i(l)}),l.type==="click"&&(h.props.trigger.indexOf("mouseenter")<0||y)&&h.props.hideOnClick!==!1&&h.state.isVisible?n=!0:je(l),l.type==="click"&&(y=!n),n&&!o&&Me(l)}}function Re(l){var c=l.target,n=E().contains(c)||D.contains(c);if(!(l.type==="mousemove"&&n)){var o=xe().concat(D).map(function(i){var v,u=i._tippy,d=(v=u.popperInstance)==null?void 0:v.state;return d?{popperRect:i.getBoundingClientRect(),popperState:d,props:t}:null}).filter(Boolean);bo(o,l)&&(Ae(),Me(l))}}function Le(l){var c=qe(l)||h.props.trigger.indexOf("click")>=0&&y;if(!c){if(h.props.interactive){h.hideWithInteractivity(l);return}Me(l)}}function Fe(l){h.props.trigger.indexOf("focusin")<0&&l.target!==E()||h.props.interactive&&l.relatedTarget&&D.contains(l.relatedTarget)||Me(l)}function qe(l){return Ne.isTouch?k()!==l.type.indexOf("touch")>=0:!1}function Ke(){Xe();var l=h.props,c=l.popperOptions,n=l.placement,o=l.offset,i=l.getReferenceClientRect,v=l.moveTransition,u=C()?Jt(D).arrow:null,d=i?{getBoundingClientRect:i,contextElement:i.contextElement||E()}:e,m={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(N){var W=N.state;if(C()){var ee=_(),ne=ee.box;["placement","reference-hidden","escaped"].forEach(function(le){le==="placement"?ne.setAttribute("data-placement",W.placement):W.attributes.popper["data-popper-"+le]?ne.setAttribute("data-"+le,""):ne.removeAttribute("data-"+le)}),W.attributes.popper={}}}},P=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!v}},m];C()&&u&&P.push({name:"arrow",options:{element:u,padding:3}}),P.push.apply(P,(c==null?void 0:c.modifiers)||[]),h.popperInstance=ro(d,D,Object.assign({},c,{placement:n,onFirstUpdate:M,modifiers:P}))}function Xe(){h.popperInstance&&(h.popperInstance.destroy(),h.popperInstance=null)}function ge(){var l=h.props.appendTo,c,n=E();h.props.interactive&&l===Ns||l==="parent"?c=n.parentNode:c=Vs(l,[n]),c.contains(D)||c.appendChild(D),h.state.isMounted=!0,Ke()}function xe(){return Rt(D.querySelectorAll("[data-tippy-root]"))}function je(l){h.clearDelayTimeouts(),l&&ae("onTrigger",[h,l]),ue();var c=T(!0),n=w(),o=n[0],i=n[1];Ne.isTouch&&o==="hold"&&i&&(c=i),c?a=setTimeout(function(){h.show()},c):h.show()}function Me(l){if(h.clearDelayTimeouts(),ae("onUntrigger",[h,l]),!h.state.isVisible){ie();return}if(!(h.props.trigger.indexOf("mouseenter")>=0&&h.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(l.type)>=0&&y)){var c=T(!1);c?r=setTimeout(function(){h.state.isVisible&&h.hide()},c):g=requestAnimationFrame(function(){h.hide()})}}function De(){h.state.isEnabled=!0}function Je(){h.hide(),h.state.isEnabled=!1}function Ge(){clearTimeout(a),clearTimeout(r),cancelAnimationFrame(g)}function We(l){if(!h.state.isDestroyed){ae("onBeforeUpdate",[h,l]),ze();var c=h.props,n=Ts(e,Object.assign({},c,ws(l),{ignoreAttributes:!0}));h.props=n,Ue(),c.interactiveDebounce!==n.interactiveDebounce&&(Ae(),H=ms(Re,n.interactiveDebounce)),c.triggerTarget&&!n.triggerTarget?ft(c.triggerTarget).forEach(function(o){o.removeAttribute("aria-expanded")}):n.triggerTarget&&e.removeAttribute("aria-expanded"),ce(),F(),J&&J(c,n),h.popperInstance&&(Ke(),xe().forEach(function(o){requestAnimationFrame(o._tippy.popperInstance.forceUpdate)})),ae("onAfterUpdate",[h,l])}}function Be(l){h.setProps({content:l})}function tt(){var l=h.state.isVisible,c=h.state.isDestroyed,n=!h.state.isEnabled,o=Ne.isTouch&&!h.props.touch,i=Nt(h.props.duration,0,Pe.duration);if(!(l||c||n||o)&&!E().hasAttribute("disabled")&&(ae("onShow",[h],!1),h.props.onShow(h)!==!1)){if(h.state.isVisible=!0,C()&&(D.style.visibility="visible"),F(),ue(),h.state.isMounted||(D.style.transition="none"),C()){var v=_(),u=v.box,d=v.content;Vt([u,d],0)}M=function(){var P;if(!(!h.state.isVisible||x)){if(x=!0,D.offsetHeight,D.style.transition=h.props.moveTransition,C()&&h.props.animation){var U=_(),N=U.box,W=U.content;Vt([N,W],i),ys([N,W],"visible")}re(),ce(),bs(zt,h),(P=h.popperInstance)==null||P.forceUpdate(),ae("onMount",[h]),h.props.animation&&C()&&Q(i,function(){h.state.isShown=!0,ae("onShown",[h])})}},ge()}}function Ze(){var l=!h.state.isVisible,c=h.state.isDestroyed,n=!h.state.isEnabled,o=Nt(h.props.duration,1,Pe.duration);if(!(l||c||n)&&(ae("onHide",[h],!1),h.props.onHide(h)!==!1)){if(h.state.isVisible=!1,h.state.isShown=!1,x=!1,y=!1,C()&&(D.style.visibility="hidden"),Ae(),ie(),F(!0),C()){var i=_(),v=i.box,u=i.content;h.props.animation&&(Vt([v,u],o),ys([v,u],"hidden"))}re(),ce(),h.props.animation?C()&&Ie(o,h.unmount):h.unmount()}}function st(l){A().addEventListener("mousemove",H),bs(Pt,H),H(l)}function it(){h.state.isVisible&&h.hide(),h.state.isMounted&&(Xe(),xe().forEach(function(l){l._tippy.unmount()}),D.parentNode&&D.parentNode.removeChild(D),zt=zt.filter(function(l){return l!==h}),h.state.isMounted=!1,ae("onHidden",[h]))}function bt(){h.state.isDestroyed||(h.clearDelayTimeouts(),h.unmount(),ze(),delete e._tippy,h.state.isDestroyed=!0,ae("onDestroy",[h]))}}function ot(e,s){s===void 0&&(s={});var t=Pe.plugins.concat(s.plugins||[]);xo();var a=Object.assign({},s,{plugins:t}),r=go(e),g=r.reduce(function(y,S){var b=S&&Oo(S,a);return b&&y.push(b),y},[]);return Ft(e)?g[0]:g}ot.defaultProps=Pe;ot.setDefaultProps=$o;ot.currentInput=Ne;Object.assign({},Os,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});ot.setDefaultProps({render:qs});const _o={class:"task-pane"},Po={key:0,class:"loading-overlay"},Io={key:1,class:"format-error-overlay"},Uo={class:"format-error-content"},Ro={class:"format-error-message"},Lo={class:"format-error-actions"},Fo={class:"doc-header"},jo={class:"doc-title"},Wo={class:"action-area"},Bo={class:"select-container"},No={class:"select-group"},Vo=["disabled"],Ho=["value"],zo={class:"select-group"},qo=["disabled"],Yo=["value"],Ko=["title"],Xo={key:0,class:"science-warning"},Jo={class:"action-buttons"},Go=["disabled"],Zo={class:"btn-content"},Qo={key:0,class:"button-loader"},er=["disabled"],tr={class:"btn-content"},sr={key:0,class:"button-loader"},nr=["disabled"],ar={class:"content-area"},or={class:"modal-header"},rr={class:"modal-body"},ir={class:"selection-content"},lr={class:"modal-header"},cr={class:"modal-body"},ur={class:"alert-message"},dr={class:"alert-actions"},pr={key:2,class:"modal-overlay"},fr={class:"modal-header"},hr={class:"modal-body"},vr={class:"confirm-message"},gr={class:"confirm-actions"},mr={class:"task-queue"},br={class:"queue-header"},wr={class:"queue-status-filter"},yr=["value"],xr={class:"queue-actions"},Cr=["disabled","title"],Tr={class:"task-count"},kr={key:0,class:"queue-table-container"},Sr={class:"col-id"},Er={class:"id-header"},$r={key:0,class:"col-subject"},Ar={class:"subject-header"},Mr={class:"switch"},Dr=["title"],Or={key:1,class:"col-status"},_r=["onClick"],Pr={class:"col-id"},Ir={class:"id-content"},Ur={class:"task-id"},Rr={key:0,class:"enhance-svg-icon"},Lr={key:0,class:"status-in-id"},Fr={key:0,class:"col-subject"},jr=["onMouseenter"],Wr={key:1,class:"col-status"},Br={class:"status-cell"},Nr={class:"col-actions"},Vr={class:"task-actions"},Hr=["onClick"],zr=["onClick"],qr={key:2,class:"no-action-icon",title:"无可用操作"},Yr={key:1,class:"empty-queue"},Kr={key:3,class:"log-container"},Xr={class:"log-actions"},Jr={class:"toggle-icon"},Gr=["innerHTML"],Zr={__name:"TaskPane",setup(e){const s=se(!1),t=se(!1),a=se(!1),r=se(""),g=se(!1),y=se(!1),S=se(!1),b=se(!0),x=se(""),$=se(!1),I=se(window.innerWidth),M=ut(()=>I.value<750),O=ut(()=>I.value<380),H=()=>{I.value=window.innerWidth},R=se(null),z=se(!1),K=se(""),L={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},B=se(!1),h=se([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"}]),{docName:X,selected:D,logger:J,map:G,subject:f,stage:w,subjectOptions:k,stageOptions:C,appConfig:E,clearLog:A,checkDocumentFormat:_,getTaskStatusClass:T,getTaskStatusText:F,terminateTask:ae,run1:re,setupLifecycle:ce,navigateToTaskControl:Ae,isLoading:ve,tryRemoveTaskPlaceholderWithLoading:we,confirmDialog:j,handleConfirm:ue,getCompletedTasksCount:ie,showConfirm:Ie}=pn(),Q=se(null);(()=>{var l;try{if((l=window.Application)!=null&&l.PluginStorage){const c=window.Application.PluginStorage.getItem("user_info");c?(Q.value=JSON.parse(c),console.log("用户信息已加载:",Q.value)):console.log("未找到用户信息")}}catch(c){console.error("解析用户信息时出错:",c)}})();const ye=ut(()=>!Q.value||Q.value.isAdmin||Q.value.isOwner?k:Q.value.subject?k.filter(l=>l.value===Q.value.subject):k),Ue=()=>{Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject&&(f.value=Q.value.subject)},ze=ut(()=>["physics","chemistry","biology","math"].includes(f.value));qt(E,l=>{l&&(console.log("TaskPane组件收到应用配置更新:",l),console.log("当前版本类型:",l.EDITION),console.log("当前年级选项:",C.value))},{deep:!0,immediate:!0});const de=()=>{try{const l=_();b.value=l.isValid,x.value=l.message,$.value=!l.isValid,l.isValid||console.warn("文档格式检查失败:",l.message)}catch(l){console.error("执行文档格式检查时出错:",l),b.value=!1,x.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",$.value=!0}},Re=ut(()=>{let l={};const c=G;if(K.value==="")l={...c};else for(const n in c)if(Object.prototype.hasOwnProperty.call(c,n)){const o=c[n];o.status===K.value&&(l[n]=o)}return z.value&&R.value?l[R.value]?{[R.value]:l[R.value]}:{}:l}),Le=ut(()=>{const l=Re.value;return Object.entries(l).map(([n,o])=>({tid:n,...o})).sort((n,o)=>{const i=n.startTime||0;return(o.startTime||0)-i}).reduce((n,o)=>{const{tid:i,...v}=o;return n[i]=v,n},{})}),Fe=(l="wps-analysis")=>{f.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(r.value="未选中内容",t.value=!0):(l==="wps-analysis"?y.value=!0:l==="wps-enhance_analysis"&&(S.value=!0),re(l).catch(c=>{console.log(c),c.message.includes("重叠")?(r.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",c),r.value=c.message,t.value=!0)}).finally(()=>{l==="wps-analysis"?y.value=!1:l==="wps-enhance_analysis"&&(S.value=!1)})):(r.value="请选择学科",t.value=!0)},qe=()=>{r.value="功能开发中……敬请期待",t.value=!0},Ke=(l,c)=>{R.value=l,Ae(l)},Xe=l=>{G[l]&&(G[l].status=3),R.value===l&&(R.value=null),we(l,!0)},ge=async()=>{const l=Object.entries(G).filter(([c,n])=>n.status===2);if(l.length===0){r.value="没有已完成的任务可释放",t.value=!0;return}try{if(await Ie(`确定要释放所有 ${l.length} 个已完成的任务吗？
此操作不可撤销。`)){let n=0;l.forEach(([o,i])=>{G[o]&&(G[o].status=3,R.value===o&&(R.value=null),we(o,!0),n++)}),r.value=`已成功释放 ${n} 个任务`,t.value=!0}}catch(c){console.error("释放任务时出错:",c),r.value="释放任务时出现错误",t.value=!0}},xe=()=>{g.value=!g.value},je=l=>l?l.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Me=l=>{const c=De(l);return c?je(c):"无题目内容"},De=l=>{if(!l.selectedText)return"";const c=l.selectedText.split("\r").filter(o=>o.trim());if(c.length===1){const o=l.selectedText.split(`
`).filter(i=>i.trim());o.length>1&&c.splice(0,1,...o)}const n=c.map((o,i)=>{const v=o.trim();return v.length>200,v});return n.length===1?c[0].trim():n.join(`
`)},Je=(l,c)=>{if(!B.value)return;const n=l.target,o=De(c).toString();if(!o||o.trim()===""){console.log("题目内容为空，不显示tooltip");return}const i=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${o.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,v=l.clientX,u=l.clientY;if(L.subjects.has(n)){const m=L.subjects.get(n);m.setContent(i),m.setProps({getReferenceClientRect:()=>({width:0,height:0,top:u,bottom:u,left:v,right:v})}),m.show();return}const d=ot(n,{content:i,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:u,bottom:u,left:v,right:v}),onHidden:()=>{d.setProps({getReferenceClientRect:null})}});L.subjects.set(n,d),d.show()},Ge=l=>{const c=l.currentTarget,n=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,o=l.clientX,i=l.clientY;if(L.enhance.has(c)){const u=L.enhance.get(c);u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:o,right:o})}),u.show();return}const v=ot(c,{content:n,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});L.enhance.set(c,v),v.show()},We=()=>{L.subjects.forEach(l=>{l.destroy()}),L.subjects.clear(),L.enhance.forEach(l=>{l.destroy()}),L.enhance.clear(),L.softBreak.forEach(l=>{l.destroy()}),L.softBreak.clear(),document.removeEventListener("click",Be),document.removeEventListener("mousemove",Ze)},Be=l=>{const c=document.querySelector(".tippy-box");c&&!c.contains(l.target)&&(L.subjects.forEach(n=>n.hide()),L.enhance.forEach(n=>n.hide()),L.softBreak.forEach(n=>n.hide()))};let tt=0;const Ze=l=>{const c=Date.now();if(c-tt<100)return;tt=c;const n=document.querySelector(".tippy-box");if(!n)return;const o=n.getBoundingClientRect();!(l.clientX>=o.left-20&&l.clientX<=o.right+20&&l.clientY>=o.top-20&&l.clientY<=o.bottom+20)&&!n.matches(":hover")&&(L.subjects.forEach(v=>v.hide()),L.enhance.forEach(v=>v.hide()),L.softBreak.forEach(v=>v.hide()))},st=()=>{document.addEventListener("click",Be),document.addEventListener("mousemove",Ze)};Es(()=>{window.addEventListener("resize",H),st(),Ue(),setTimeout(()=>{de()},500);const l=document.createElement("style");l.id="tippy-custom-styles",l.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(l)}),Gs(()=>{window.removeEventListener("resize",H),We();const l=document.getElementById("tippy-custom-styles");l&&l.remove()}),ce();const it=l=>l.selectedText?l.selectedText.includes("\v")||l.selectedText.includes("\v"):!1,bt=l=>{const c=l.currentTarget,n=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,o=l.clientX,i=l.clientY;if(L.softBreak.has(c)){const u=L.softBreak.get(c);u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:o,right:o})}),u.show();return}const v=ot(c,{content:n,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});L.softBreak.set(c,v),v.show()};return(l,c)=>{var n,o,i,v,u;return q(),Y("div",_o,[oe(ve)?(q(),Y("div",Po,c[26]||(c[26]=[p("div",{class:"loading-spinner"},null,-1),p("div",{class:"loading-text"},"处理中...",-1)]))):Z("",!0),$.value?(q(),Y("div",Io,[p("div",Uo,[c[27]||(c[27]=p("div",{class:"format-error-icon"},"⚠️",-1)),c[28]||(c[28]=p("div",{class:"format-error-title"},"文档格式不支持",-1)),p("div",Ro,te(x.value),1),p("div",Lo,[p("button",{class:"retry-check-btn",onClick:c[0]||(c[0]=d=>de())},"重新检查")])])])):Z("",!0),p("div",Fo,[p("div",jo,te(oe(X)||"未选择文档"),1),p("button",{class:"settings-btn",onClick:c[1]||(c[1]=d=>a.value=!0)},c[29]||(c[29]=[p("i",{class:"icon-settings"},null,-1)]))]),p("div",Wo,[p("div",Bo,[p("div",No,[c[30]||(c[30]=p("label",{for:"stage-select"},"年级:",-1)),Oe(p("select",{id:"stage-select","onUpdate:modelValue":c[2]||(c[2]=d=>os(w)?w.value=d:null),class:"select-input",disabled:$.value},[(q(!0),Y(xt,null,Ct(oe(C),d=>(q(),Y("option",{key:d.value,value:d.value},te(d.label),9,Ho))),128))],8,Vo),[[Bt,oe(w)]])]),p("div",zo,[c[31]||(c[31]=p("label",{for:"subject-select"},"学科:",-1)),Oe(p("select",{id:"subject-select","onUpdate:modelValue":c[3]||(c[3]=d=>os(f)?f.value=d:null),class:"select-input",disabled:$.value},[(q(!0),Y(xt,null,Ct(ye.value,d=>(q(),Y("option",{key:d.value,value:d.value},te(d.label),9,Yo))),128))],8,qo),[[Bt,oe(f)]]),Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject?(q(),Y("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((n=ye.value.find(d=>d.value===Q.value.subject))==null?void 0:n.label)||Q.value.subject} 学科`}," 🔒 ",8,Ko)):Z("",!0)])]),ze.value?(q(),Y("div",Xo," 理科可使用增强模式以获取更精准的解析 ")):Z("",!0),p("div",Jo,[p("button",{class:"action-btn primary",onClick:c[4]||(c[4]=d=>Fe("wps-analysis")),disabled:y.value||$.value},[p("div",Zo,[y.value?(q(),Y("span",Qo)):Z("",!0),c[32]||(c[32]=p("span",{class:"btn-text"},"解析",-1))])],8,Go),ze.value?(q(),Y("button",{key:0,class:"action-btn enhance",onClick:c[5]||(c[5]=d=>Fe("wps-enhance_analysis")),disabled:S.value||$.value},[p("div",tr,[S.value?(q(),Y("span",sr)):Z("",!0),c[33]||(c[33]=p("span",{class:"btn-text"},"增强解析",-1))])],8,er)):Z("",!0),((i=(o=Q.value)==null?void 0:o.orgs[0])==null?void 0:i.orgId)===2?(q(),Y("button",{key:1,class:"action-btn secondary",onClick:c[6]||(c[6]=d=>qe()),disabled:$.value},c[34]||(c[34]=[p("div",{class:"btn-content"},[p("span",{class:"btn-text"},"校对")],-1)]),8,nr)):Z("",!0)])]),p("div",ar,[s.value?(q(),Y("div",{key:0,class:"modal-overlay",onClick:c[9]||(c[9]=d=>s.value=!1)},[p("div",{class:"modal-content",onClick:c[8]||(c[8]=Qe(()=>{},["stop"]))},[p("div",or,[c[35]||(c[35]=p("div",{class:"modal-title"},"选中内容",-1)),p("button",{class:"modal-close",onClick:c[7]||(c[7]=d=>s.value=!1)},"×")]),p("div",rr,[p("pre",ir,te(oe(D)||"未选中内容"),1)])])])):Z("",!0),t.value?(q(),Y("div",{key:1,class:"modal-overlay",onClick:c[13]||(c[13]=d=>t.value=!1)},[p("div",{class:"modal-content alert-modal",onClick:c[12]||(c[12]=Qe(()=>{},["stop"]))},[p("div",lr,[c[36]||(c[36]=p("div",{class:"modal-title"},"提示",-1)),p("button",{class:"modal-close",onClick:c[10]||(c[10]=d=>t.value=!1)},"×")]),p("div",cr,[p("div",ur,te(r.value),1),p("div",dr,[p("button",{class:"action-btn primary",onClick:c[11]||(c[11]=d=>t.value=!1)},"确定")])])])])):Z("",!0),oe(j).show?(q(),Y("div",pr,[p("div",{class:"modal-content confirm-modal",onClick:c[17]||(c[17]=Qe(()=>{},["stop"]))},[p("div",fr,[c[37]||(c[37]=p("div",{class:"modal-title"},"确认",-1)),p("button",{class:"modal-close",onClick:c[14]||(c[14]=d=>oe(ue)(!1))},"×")]),p("div",hr,[p("div",vr,te(oe(j).message),1),p("div",gr,[p("button",{class:"action-btn secondary",onClick:c[15]||(c[15]=d=>oe(ue)(!1))},"取消"),p("button",{class:"action-btn primary",onClick:c[16]||(c[16]=d=>oe(ue)(!0))},"确定")])])])])):Z("",!0),p("div",mr,[p("div",br,[c[38]||(c[38]=p("div",{class:"queue-title"},"任务队列",-1)),p("div",wr,[Oe(p("select",{id:"status-filter-select","onUpdate:modelValue":c[18]||(c[18]=d=>K.value=d),class:"status-filter-select-input"},[(q(!0),Y(xt,null,Ct(h.value,d=>(q(),Y("option",{key:d.value,value:d.value},te(d.label),9,yr))),128))],512),[[Bt,K.value]])]),p("div",xr,[p("button",{class:"release-all-btn",onClick:ge,disabled:oe(ie)()===0,title:oe(ie)()===0?"无已完成任务可释放":`释放所有${oe(ie)()}个已完成任务`}," 一键释放 ",8,Cr)]),p("div",Tr,te(Object.keys(Re.value).length)+"个任务",1)]),Object.keys(Re.value).length>0?(q(),Y("div",kr,[p("table",{class:ke(["queue-table",{"narrow-view":M.value,"ultra-narrow-view":O.value}])},[p("thead",null,[p("tr",null,[p("th",Sr,[p("div",Er,[c[40]||(c[40]=p("span",null,"任务ID",-1)),p("span",{class:"help-icon",onMouseenter:c[19]||(c[19]=d=>Ge(d))},c[39]||(c[39]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("circle",{cx:"12",cy:"12",r:"10"}),p("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),p("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),M.value?Z("",!0):(q(),Y("th",$r,[p("div",Ar,[c[41]||(c[41]=p("span",null,"题目内容",-1)),p("label",Mr,[Oe(p("input",{type:"checkbox","onUpdate:modelValue":c[20]||(c[20]=d=>B.value=d)},null,512),[[Zs,B.value]]),p("span",{class:"slider round",title:B.value?"关闭题目预览":"开启题目预览"},null,8,Dr)])])])),O.value?Z("",!0):(q(),Y("th",Or,"状态")),c[42]||(c[42]=p("th",{class:"col-actions"},"操作",-1))])]),p("tbody",null,[(q(!0),Y(xt,null,Ct(Le.value,(d,m)=>(q(),Y("tr",{key:m,class:ke(["task-row",[oe(T)(d.status),{"selected-task-row":m===R.value}]]),onClick:P=>Ke(m)},[p("td",Pr,[p("div",{class:ke(["id-cell",{"id-with-status":O.value}])},[p("div",Ir,[p("span",Ur,te(m.substring(0,8)),1),d.wordType==="wps-enhance_analysis"||d.isEnhanced?(q(),Y("span",Rr,c[43]||(c[43]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("title",null,"增强模式"),p("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):Z("",!0),it(d)?(q(),Y("span",{key:1,class:"soft-break-warning-icon",onMouseenter:c[21]||(c[21]=P=>bt(P))},c[44]||(c[44]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("title",null,"提示"),p("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),p("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),p("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):Z("",!0)]),O.value?(q(),Y("div",Lr,[p("span",{class:ke(["task-tag compact",oe(T)(d.status)])},te(oe(F)(d.status)),3)])):Z("",!0)],2)]),M.value?Z("",!0):(q(),Y("td",Fr,[p("div",{class:"subject-cell",onMouseenter:P=>Je(P,d)},te(Me(d)),41,jr)])),O.value?Z("",!0):(q(),Y("td",Wr,[p("div",Br,[p("span",{class:ke(["task-tag",oe(T)(d.status)])},te(oe(F)(d.status)),3)])])),p("td",Nr,[p("div",Vr,[d.status===1?(q(),Y("button",{key:0,onClick:Qe(P=>oe(ae)(m),["stop"]),class:"terminate-btn"}," 终止 ",8,Hr)):Z("",!0),d.status===2?(q(),Y("button",{key:1,onClick:Qe(P=>Xe(m),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,zr)):Z("",!0),d.status!==1&&d.status!==2?(q(),Y("span",qr,c[45]||(c[45]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[p("circle",{cx:"12",cy:"12",r:"10"}),p("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),p("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):Z("",!0)])])],10,_r))),128))])],2)])):(q(),Y("div",Yr,c[46]||(c[46]=[p("div",{class:"empty-text"},"暂无任务",-1)])))]),((u=(v=Q.value)==null?void 0:v.orgs[0])==null?void 0:u.orgId)===2?(q(),Y("div",Kr,[p("div",{class:"log-header",onClick:xe},[c[47]||(c[47]=p("div",{class:"log-title"},"执行日志",-1)),p("div",Xr,[p("button",{class:"clear-btn",onClick:c[22]||(c[22]=Qe(d=>oe(A)(),["stop"]))},"清空日志"),p("span",Jr,te(g.value?"▼":"▶"),1)])]),g.value?(q(),Y("div",{key:0,class:"log-content",innerHTML:oe(J)},null,8,Gr)):Z("",!0)])):Z("",!0)]),a.value?(q(),Y("div",{key:2,class:"modal-overlay",onClick:c[25]||(c[25]=d=>a.value=!1)},[p("div",{class:"modal-content",onClick:c[24]||(c[24]=Qe(()=>{},["stop"]))},[Qs(ta,{onClose:c[23]||(c[23]=d=>a.value=!1)})])])):Z("",!0)])}}},ei=$s(Zr,[["__scopeId","data-v-e69ce6c3"]]);export{ei as default};
