import{U as on,r as ge,h as vt,v as qe,i as Z,j as Rt,k as Fs,m as Jt,_ as js,n as ln,o as J,c as Y,a as p,p as cn,t as pe,f as ae,q as We,w as ze,s as Vt,e as Zt,F as Ot,u as It,x as Tt,y as un,z as me,A as Qt,B as gs,C as pt,D as dn,E as pn}from"./index-B55TjUwb.js";function fn(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=on.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let n=window.Application.GetTaskPane(t);n.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let n=window.Application.GetTaskPane(t);n.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let n=window.Application.GetTaskPane(t);n.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let n=window.Application.Selection.Range;n&&n.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const hn={onbuttonclick:fn};var vn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function gn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function mn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function n(){return this instanceof n?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}),t}var Ws={exports:{}};const bn={},wn=Object.freeze(Object.defineProperty({__proto__:null,default:bn},Symbol.toStringTag,{value:"Module"})),ms=mn(wn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",n=typeof window=="object",o=n?window:{};o.JS_SHA1_NO_WINDOW&&(n=!1);var m=!n&&typeof self=="object",T=!o.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;T?o=vn:m&&(o=self);var _=!o.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,w=!o.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",C="0123456789abcdef".split(""),D=[-**********,8388608,32768,128],F=[24,16,8,0],W=["hex","array","digest","arrayBuffer"],I=[],V=Array.isArray;(o.JS_SHA1_NO_NODE_JS||!V)&&(V=function(u){return Object.prototype.toString.call(u)==="[object Array]"});var q=ArrayBuffer.isView;w&&(o.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!q)&&(q=function(u){return typeof u=="object"&&u.buffer&&u.buffer.constructor===ArrayBuffer});var B=function(u){var b=typeof u;if(b==="string")return[u,!0];if(b!=="object"||u===null)throw new Error(s);if(w&&u.constructor===ArrayBuffer)return[new Uint8Array(u),!1];if(!V(u)&&!q(u))throw new Error(s);return[u,!1]},te=function(u){return function(b){return new P(!0).update(b)[u]()}},se=function(){var u=te("hex");T&&(u=H(u)),u.create=function(){return new P},u.update=function(x){return u.create().update(x)};for(var b=0;b<W.length;++b){var g=W[b];u[g]=te(g)}return u},H=function(u){var b=ms,g=ms.Buffer,x;g.from&&!o.JS_SHA1_NO_BUFFER_FROM?x=g.from:x=function(M){return new g(M)};var $=function(M){if(typeof M=="string")return b.createHash("sha1").update(M,"utf8").digest("hex");if(M==null)throw new Error(s);return M.constructor===ArrayBuffer&&(M=new Uint8Array(M)),V(M)||q(M)||M.constructor===g?b.createHash("sha1").update(x(M)).digest("hex"):u(M)};return $},d=function(u){return function(b,g){return new ie(b,!0).update(g)[u]()}},ne=function(){var u=d("hex");u.create=function(x){return new ie(x)},u.update=function(x,$){return u.create(x).update($)};for(var b=0;b<W.length;++b){var g=W[b];u[g]=d(g)}return u};function P(u){u?(I[0]=I[16]=I[1]=I[2]=I[3]=I[4]=I[5]=I[6]=I[7]=I[8]=I[9]=I[10]=I[11]=I[12]=I[13]=I[14]=I[15]=0,this.blocks=I):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}P.prototype.update=function(u){if(this.finalized)throw new Error(t);var b=B(u);u=b[0];for(var g=b[1],x,$=0,M,L=u.length||0,S=this.blocks;$<L;){if(this.hashed&&(this.hashed=!1,S[0]=this.block,this.block=S[16]=S[1]=S[2]=S[3]=S[4]=S[5]=S[6]=S[7]=S[8]=S[9]=S[10]=S[11]=S[12]=S[13]=S[14]=S[15]=0),g)for(M=this.start;$<L&&M<64;++$)x=u.charCodeAt($),x<128?S[M>>>2]|=x<<F[M++&3]:x<2048?(S[M>>>2]|=(192|x>>>6)<<F[M++&3],S[M>>>2]|=(128|x&63)<<F[M++&3]):x<55296||x>=57344?(S[M>>>2]|=(224|x>>>12)<<F[M++&3],S[M>>>2]|=(128|x>>>6&63)<<F[M++&3],S[M>>>2]|=(128|x&63)<<F[M++&3]):(x=65536+((x&1023)<<10|u.charCodeAt(++$)&1023),S[M>>>2]|=(240|x>>>18)<<F[M++&3],S[M>>>2]|=(128|x>>>12&63)<<F[M++&3],S[M>>>2]|=(128|x>>>6&63)<<F[M++&3],S[M>>>2]|=(128|x&63)<<F[M++&3]);else for(M=this.start;$<L&&M<64;++$)S[M>>>2]|=u[$]<<F[M++&3];this.lastByteIndex=M,this.bytes+=M-this.start,M>=64?(this.block=S[16],this.start=M-64,this.hash(),this.hashed=!0):this.start=M}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},P.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var u=this.blocks,b=this.lastByteIndex;u[16]=this.block,u[b>>>2]|=D[b&3],this.block=u[16],b>=56&&(this.hashed||this.hash(),u[0]=this.block,u[16]=u[1]=u[2]=u[3]=u[4]=u[5]=u[6]=u[7]=u[8]=u[9]=u[10]=u[11]=u[12]=u[13]=u[14]=u[15]=0),u[14]=this.hBytes<<3|this.bytes>>>29,u[15]=this.bytes<<3,this.hash()}},P.prototype.hash=function(){var u=this.h0,b=this.h1,g=this.h2,x=this.h3,$=this.h4,M,L,S,K=this.blocks;for(L=16;L<80;++L)S=K[L-3]^K[L-8]^K[L-14]^K[L-16],K[L]=S<<1|S>>>31;for(L=0;L<20;L+=5)M=b&g|~b&x,S=u<<5|u>>>27,$=S+M+$+1518500249+K[L]<<0,b=b<<30|b>>>2,M=u&b|~u&g,S=$<<5|$>>>27,x=S+M+x+1518500249+K[L+1]<<0,u=u<<30|u>>>2,M=$&u|~$&b,S=x<<5|x>>>27,g=S+M+g+1518500249+K[L+2]<<0,$=$<<30|$>>>2,M=x&$|~x&u,S=g<<5|g>>>27,b=S+M+b+1518500249+K[L+3]<<0,x=x<<30|x>>>2,M=g&x|~g&$,S=b<<5|b>>>27,u=S+M+u+1518500249+K[L+4]<<0,g=g<<30|g>>>2;for(;L<40;L+=5)M=b^g^x,S=u<<5|u>>>27,$=S+M+$+1859775393+K[L]<<0,b=b<<30|b>>>2,M=u^b^g,S=$<<5|$>>>27,x=S+M+x+1859775393+K[L+1]<<0,u=u<<30|u>>>2,M=$^u^b,S=x<<5|x>>>27,g=S+M+g+1859775393+K[L+2]<<0,$=$<<30|$>>>2,M=x^$^u,S=g<<5|g>>>27,b=S+M+b+1859775393+K[L+3]<<0,x=x<<30|x>>>2,M=g^x^$,S=b<<5|b>>>27,u=S+M+u+1859775393+K[L+4]<<0,g=g<<30|g>>>2;for(;L<60;L+=5)M=b&g|b&x|g&x,S=u<<5|u>>>27,$=S+M+$-1894007588+K[L]<<0,b=b<<30|b>>>2,M=u&b|u&g|b&g,S=$<<5|$>>>27,x=S+M+x-1894007588+K[L+1]<<0,u=u<<30|u>>>2,M=$&u|$&b|u&b,S=x<<5|x>>>27,g=S+M+g-1894007588+K[L+2]<<0,$=$<<30|$>>>2,M=x&$|x&u|$&u,S=g<<5|g>>>27,b=S+M+b-1894007588+K[L+3]<<0,x=x<<30|x>>>2,M=g&x|g&$|x&$,S=b<<5|b>>>27,u=S+M+u-1894007588+K[L+4]<<0,g=g<<30|g>>>2;for(;L<80;L+=5)M=b^g^x,S=u<<5|u>>>27,$=S+M+$-899497514+K[L]<<0,b=b<<30|b>>>2,M=u^b^g,S=$<<5|$>>>27,x=S+M+x-899497514+K[L+1]<<0,u=u<<30|u>>>2,M=$^u^b,S=x<<5|x>>>27,g=S+M+g-899497514+K[L+2]<<0,$=$<<30|$>>>2,M=x^$^u,S=g<<5|g>>>27,b=S+M+b-899497514+K[L+3]<<0,x=x<<30|x>>>2,M=g^x^$,S=b<<5|b>>>27,u=S+M+u-899497514+K[L+4]<<0,g=g<<30|g>>>2;this.h0=this.h0+u<<0,this.h1=this.h1+b<<0,this.h2=this.h2+g<<0,this.h3=this.h3+x<<0,this.h4=this.h4+$<<0},P.prototype.hex=function(){this.finalize();var u=this.h0,b=this.h1,g=this.h2,x=this.h3,$=this.h4;return C[u>>>28&15]+C[u>>>24&15]+C[u>>>20&15]+C[u>>>16&15]+C[u>>>12&15]+C[u>>>8&15]+C[u>>>4&15]+C[u&15]+C[b>>>28&15]+C[b>>>24&15]+C[b>>>20&15]+C[b>>>16&15]+C[b>>>12&15]+C[b>>>8&15]+C[b>>>4&15]+C[b&15]+C[g>>>28&15]+C[g>>>24&15]+C[g>>>20&15]+C[g>>>16&15]+C[g>>>12&15]+C[g>>>8&15]+C[g>>>4&15]+C[g&15]+C[x>>>28&15]+C[x>>>24&15]+C[x>>>20&15]+C[x>>>16&15]+C[x>>>12&15]+C[x>>>8&15]+C[x>>>4&15]+C[x&15]+C[$>>>28&15]+C[$>>>24&15]+C[$>>>20&15]+C[$>>>16&15]+C[$>>>12&15]+C[$>>>8&15]+C[$>>>4&15]+C[$&15]},P.prototype.toString=P.prototype.hex,P.prototype.digest=function(){this.finalize();var u=this.h0,b=this.h1,g=this.h2,x=this.h3,$=this.h4;return[u>>>24&255,u>>>16&255,u>>>8&255,u&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,g>>>24&255,g>>>16&255,g>>>8&255,g&255,x>>>24&255,x>>>16&255,x>>>8&255,x&255,$>>>24&255,$>>>16&255,$>>>8&255,$&255]},P.prototype.array=P.prototype.digest,P.prototype.arrayBuffer=function(){this.finalize();var u=new ArrayBuffer(20),b=new DataView(u);return b.setUint32(0,this.h0),b.setUint32(4,this.h1),b.setUint32(8,this.h2),b.setUint32(12,this.h3),b.setUint32(16,this.h4),u};function ie(u,b){var g,x=B(u);if(u=x[0],x[1]){var $=[],M=u.length,L=0,S;for(g=0;g<M;++g)S=u.charCodeAt(g),S<128?$[L++]=S:S<2048?($[L++]=192|S>>>6,$[L++]=128|S&63):S<55296||S>=57344?($[L++]=224|S>>>12,$[L++]=128|S>>>6&63,$[L++]=128|S&63):(S=65536+((S&1023)<<10|u.charCodeAt(++g)&1023),$[L++]=240|S>>>18,$[L++]=128|S>>>12&63,$[L++]=128|S>>>6&63,$[L++]=128|S&63);u=$}u.length>64&&(u=new P(!0).update(u).array());var K=[],be=[];for(g=0;g<64;++g){var xe=u[g]||0;K[g]=92^xe,be[g]=54^xe}P.call(this,b),this.update(be),this.oKeyPad=K,this.inner=!0,this.sharedMemory=b}ie.prototype=new P,ie.prototype.finalize=function(){if(P.prototype.finalize.call(this),this.inner){this.inner=!1;var u=this.array();P.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(u),P.prototype.finalize.call(this)}};var ce=se();ce.sha1=ce,ce.sha1.hmac=ne(),_?e.exports=ce:o.sha1=ce})()})(Ws);var yn=Ws.exports;const xn=gn(yn);function bs(){return"http://worksheet.hexinedu.com"}function Ct(){return"http://127.0.0.1:3000"}function ws(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const kt=async(e,s,t,n={},o=8e3)=>{try{return await Promise.race([e(),new Promise((m,T)=>setTimeout(()=>T(new Error("WebSocket请求超时，切换到HTTP")),o))])}catch{try{let T;return s==="get"?T=await Rt.get(t,{params:n}):s==="post"?T=await Rt.post(t,n):s==="delete"&&(T=await Rt.delete(t)),T.data}catch(T){throw new Error(`请求失败: ${T.message||"未知错误"}`)}}};function Tn(e,s,t,n){const o=[e,s,t,n].join(":");return xn(o)}function Cn(){const e=ge(""),s=ge(""),t=ge(""),n=vt({}),o=ge(""),m=ge("");let T="",_=null;const w=ge("c:\\Temp"),C=vt({appKey:"",appSecret:""}),D=vt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),F=vt({show:!1,title:"",message:"",type:"error"}),W=ge(""),I=ge("junior"),V=ge(null),q=ge(!1),B=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],te=()=>qe.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],se=vt(te()),H=async()=>{try{const a=await Z.getWatcherStatus();a.data&&a.data.watchDir&&(w.value=a.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${w.value}</span><br/>`)}catch(a){t.value+=`<span class="log-item error">获取监控目录失败: ${a.message}</span><br/>`,console.error("获取监控目录失败:",a)}},d=async()=>{var a,i,l;try{if(!C.appKey||!C.appSecret)throw new Error("未初始化app信息");const v=60,r=Date.now();let h;try{const N=window.Application.PluginStorage.getItem("token_info");N&&(h=JSON.parse(N))}catch(N){h=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${N.message}</span><br/>`}if(h&&h.access_token&&h.expired_time>r+v*1e3)return h.access_token;const c=C.appKey,O="1234567",A=Math.floor(Date.now()/1e3),E=Tn(c,O,C.appSecret,A),R=await Rt.get(bs()+"/api/open/account/v1/auth/token",{params:{app_key:c,app_nonstr:O,app_timestamp:A,app_signature:E}});if((i=(a=R.data)==null?void 0:a.data)!=null&&i.access_token){const N=R.data.data.access_token;let ee;if(R.data.data.expired_time){const he=parseInt(R.data.data.expired_time);ee=r+he*1e3,t.value+=`<span class="log-item info">token已更新，有效期${he}秒</span><br/>`}else ee=r+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const oe={access_token:N,expired_time:ee};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(oe))}catch(he){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${he.message}</span><br/>`}return N}else throw new Error(((l=R.data)==null?void 0:l.message)||"获取access_token失败")}catch(v){throw t.value+=`<span class="log-item error">获取access_token失败: ${v.message}</span><br/>`,v}},ne=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},P=()=>{const a=window.Application,i=a.Documents.Count;for(let l=1;l<=i;l++){const v=a.Documents.Item(l);if(v.DocID===o.value)return v}return null},ie=()=>{try{const a=P();if(!a)return{isValid:!1,message:"未找到当前文档"};let i="";try{i=a.Name||""}catch{try{i=M("getDocName")||""}catch{i=""}}if(i){const l=i.toLowerCase();return l.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:l.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(a){return console.error("检查文档格式时出错:",a),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},ce=(a,i="",l=0,v=null)=>{try{const r=window.Application,h=P();let c;if(v)c=v;else{const O=h.ActiveWindow.Selection;if(!O||O.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;c=O.Range}if(i){const O=c.Text,A=c.Find;A.ClearFormatting(),A.Text=i,A.Forward=!0,A.Wrap=1;let E=0,R=[];for(;A.Execute()&&(A.Found&&c.Start<=A.Parent.Start&&A.Parent.End<=c.End);){const N=A.Parent.Start,ee=A.Parent.End;if(R.some(he=>N===he.start&&ee===he.end))A.Parent.Start=ee;else{if(R.push({start:N,end:ee}),l===-1||E===l){const he=h.Comments.Add(A.Parent,a);if(l!==-1&&E===l)return t.value+=`<span class="log-item success">已为第${l+1}个"${i}"添加批注: "${a}"</span><br/>`,!0}E++,A.Parent.Start=ee}}return l!==-1&&E<=l?(t.value+=`<span class="log-item error">在${v?"指定范围":"选中内容"}中未找到第${l+1}个"${i}"</span><br/>`,!1):l===-1&&E>0?(t.value+=`<span class="log-item success">已为${E}处"${i}"添加批注: "${a}"</span><br/>`,!0):l===-1&&E===0?(t.value+=`<span class="log-item error">在${v?"指定范围":"选中内容"}中未找到关键字"${i}"</span><br/>`,!1):!0}else{const O=h.Comments.Add(c,a);return t.value+=`<span class="log-item success">已为${v?"指定范围":"选中内容"}添加批注: "${a}"</span><br/>`,!0}}catch(r){return t.value+=`<span class="log-item error">添加批注失败: ${r.message}</span><br/>`,!1}},u=a=>a===0?"status-preparing":a===1?"status-running":a===2?"status-completed":a===-1?"status-error":a===3?"status-released":a===4?"status-stopped":"",b=a=>a===0?"准备中":a===1?"进行中":a===2?"已完成":a===-1?"异常":a===3?"已释放":a===4?"已停止":"准备中",g=a=>{const i=Date.now()-a,l=Math.floor(i/1e3);return l<60?`${l}秒`:l<3600?`${Math.floor(l/60)}分${l%60}秒`:`${Math.floor(l/3600)}时${Math.floor(l%3600/60)}分`},x=async a=>{try{if(!n[a]){t.value+=`<span class="log-item error">找不到任务${a.substring(0,8)}的数据</span><br/>`;return}n[a].status=4,n[a].terminated=!0,n[a].errorMessage="用户选择不继续";try{const l=P();if(l&&l.ContentControls)for(let v=1;v<=l.ContentControls.Count;v++)try{const r=l.ContentControls.Item(v);if(r&&r.Title&&(r.Title===`任务_${a}`||r.Title===`任务增强_${a}`||r.Title===`校对_${a}`)){const h=r.Title===`任务增强_${a}`||n[a].isEnhanced,c=r.Title===`校对_${a}`||n[a].isCheckTask;c?r.Title=`已停止校对_${a}`:h?r.Title=`已停止增强_${a}`:r.Title=`已停止_${a}`;const O=c?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${a.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(l){t.value+=`<span class="log-item warning">更新控件标题失败: ${l.message}</span><br/>`}let i=null;if(G[a]&&G[a].urlId){i=G[a].urlId;try{try{const l=await kt(async()=>await Z.getUrlMonitorStatus(),"get",`${Ct()}/api/url/status`),r=(Array.isArray(l)?l:l.data?Array.isArray(l.data)?l.data:[]:[]).find(h=>h.urlId===i);r&&r.downloadedPath&&(n[a].resultFile=r.downloadedPath,n[a].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${a.substring(0,8)}已下载文件: ${r.downloadedPath}</span><br/>`)}catch(l){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${l.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${a.substring(0,8)}的URL监控</span><br/>`,await $e(i),delete G[a]}catch(l){t.value+=`<span class="log-item warning">停止URL监控出错: ${l.message}，将重试</span><br/>`,delete G[a],setTimeout(async()=>{try{i&&await kt(async()=>await Z.stopUrlMonitoring(i),"delete",`${Ct()}/api/url/monitor/${i}`)}catch(v){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${v.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${a.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(i){t.value+=`<span class="log-item error">停止任务${a.substring(0,8)}出错: ${i.message}</span><br/>`,G[a]&&delete G[a]}},$=async a=>{if(!n[a]){t.value+=`<span class="log-item error">找不到任务${a.substring(0,8)}的数据</span><br/>`;return}n[a].status=4,n[a].terminated=!0,n[a].errorMessage="用户手动终止";const i=P();if(i&&i.ContentControls)for(let v=1;v<=i.ContentControls.Count;v++)try{const r=i.ContentControls.Item(v);if(r&&r.Title&&(r.Title===`任务_${a}`||r.Title===`任务增强_${a}`||r.Title===`校对_${a}`)){const h=r.Title===`任务增强_${a}`||n[a].isEnhanced,c=r.Title===`校对_${a}`||n[a].isCheckTask;c?r.Title=`已停止校对_${a}`:h?r.Title=`已停止增强_${a}`:r.Title=`已停止_${a}`,r.LockContents=!1;const O=c?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${a.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let l=null;if(G[a]&&G[a].urlId){l=G[a].urlId;try{const v=await Z.getUrlMonitorStatus(),h=(Array.isArray(v)?v:v.data?Array.isArray(v.data)?v.data:[]:[]).find(c=>c.urlId===l);h&&h.downloadedPath&&(n[a].resultFile=h.downloadedPath,n[a].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${a.substring(0,8)}已下载文件: ${h.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${a.substring(0,8)}的URL监控</span><br/>`,await $e(l),delete G[a]}catch(v){t.value+=`<span class="log-item warning">停止URL监控出错: ${v.message}，将重试</span><br/>`,delete G[a]}}},M=a=>hn.onbuttonclick(a),L=()=>{try{e.value=M("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},S=()=>{P().ActiveWindow.Selection.Copy()},K=a=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${a}`,12,"","",!1),i.Close(),P().ActiveWindow.Activate()},be=a=>{const i=window.Application.Documents.Add("",!1,0,!1);i.Content.Paste(),i.SaveAs2(`${w.value}\\${a}`,12,"","",!1),i.Close()},xe=a=>{try{const l=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,v=window.Application.Documents.Add("",!1,0,!1);v.Content.Paste();const r=`${l}\\${a}`;v.SaveAs2(r,12,"","",!1),v.Close(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${r}.docx</span><br/>`}catch(i){throw t.value+=`<span class="log-item error">方式三保存失败: ${i.message}</span><br/>`,i}},Se=a=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${a}`,12,"","",!1),P().ActiveWindow.Activate()},Le=async a=>{try{t.value+=`<span class="log-item info">开始生成文档: ${a}</span><br/>`;let i="method2";try{const l=await Z.getSaveMethod();if(l.success&&l.saveMethod){i=l.saveMethod;const v=i==="method1"?"方式一":i==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${v}</span><br/>`}}catch(l){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${l.message}</span><br/>`}i==="method1"?(K(a),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${w.value}\\${a}.docx</span><br/>`):i==="method2"?(be(a),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${w.value}\\${a}.docx</span><br/>`):i==="method3"?(xe(a),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):i==="method4"&&(Se(a),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${w.value}\\${a}.docx</span><br/>`),Z.associateFileWithClient(`${a}.docx`).then(l=>{l.success?t.value+=`<span class="log-item info">文件 ${a}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${l.message||"未知错误"}</span><br/>`}).catch(l=>{t.value+=`<span class="log-item warning">关联文件时出错: ${l.message}</span><br/>`})}catch(i){t.value+=`<span class="log-item error">保存文件失败: ${i.message}</span><br/>`}},Ee=ge(null),De=vt([]),Te=new Set,Ce=a=>!a||typeof a!="string"?"":a.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),_e=async a=>{try{const i=a.slice(T.length);if(i.trim()){const l=Z.getClientId();if(!l){console.warn("无法获取客户端ID，跳过日志同步");return}const v=Ce(i);if(!v.trim()){T=a;return}await Z.sendRequest("logger","syncLog",{content:v,timestamp:new Date().toISOString(),clientId:l}),T=a}}catch(i){console.error("同步日志到服务端失败:",i)}},Pe=()=>{Z.connect().then(()=>{H()}).catch(i=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${i.message}</span><br/>`});const a=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const i=[];for(const l in n)if(n.hasOwnProperty(l)){const v=n[l];(v.status===0||v.status===1)&&!v.terminated&&(i.includes(l)||i.push(l))}if(i.length>0){let l=!1;try{const v=P();if(v){const h=`taskpane_id_${v.DocID}`,c=window.Application.PluginStorage.getItem(h);if(c){const O=window.Application.GetTaskPane(c);O&&(l=O.Visible)}}}catch(v){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${v.message}</span><br/>`,l=!0}setTimeout(()=>{if(l){const v=`检测到 ${i.length} 个未完成的任务，是否继续？`;re(v).then(r=>{r?(t.value+=`<span class="log-item info">用户选择继续 ${i.length} 个进行中的任务 (by taskId)...</span><br/>`,Z.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:i}).then(h=>{h&&h.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${h.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(h==null?void 0:h.message)||"未知错误"}</span><br/>`}).catch(h=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${h.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',i.forEach(h=>{x(h)}),t.value+=`<span class="log-item success">${i.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(r=>{t.value+=`<span class="log-item error">弹窗错误: ${r.message}，默认停止任务（保留控件）</span><br/>`,i.forEach(h=>{x(h)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Z.setConnectionSuccessHandler(a),Z.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',a()),Z.addEventListener("connection",i=>{i.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${i.reason||"未知"}, 代码: ${i.code||"N/A"}，将自动重连</span><br/>`)}),Z.addEventListener("watcher",i=>{var l,v;if(De.push(i),i.type,i.eventType==="uploadSuccess"){const r=(l=i.data)==null?void 0:l.file,h=r==null?void 0:r.replace(/\.docx$/,""),c=`${i.eventType}_${r}_${Date.now()}`;if(Te.has(c)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${r}</span><br/>`;return}if(Te.add(c),Te.size>100){const A=Te.values();Te.delete(A.next().value)}const O=h&&((v=n[h])==null?void 0:v.wordType);ue(i,O)}else i.eventType&&ue(i,null)}),Z.addEventListener("urlMonitor",i=>{De.push(i),i.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${i.eventType||i.action}</span><br/>`),i.eventType&&ue(i,null)}),Z.addEventListener("health",i=>{}),Z.addEventListener("error",i=>{const l=i.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${l}</span><br/>`,console.error("WebSocket错误:",i)})},G=vt({}),Oe=async(a,i,l=!1,v=5e3,r={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${a}</span><br/>`;const h=await Z.startUrlMonitoring(a,v,{downloadOnSuccess:r.downloadOnSuccess!==void 0?r.downloadOnSuccess:!0,appKey:r.appKey,filename:r.filename,taskId:i}),c=h.success||(h==null?void 0:h.success),O=h.urlId||(h==null?void 0:h.urlId);if(c&&O){G[i]={urlId:O,url:a,isResultUrl:l,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${O}</span><br/>`;try{await Z.startUrlChecking(O)}catch{}return O}else throw new Error("服务器返回失败")}catch(h){return t.value+=`<span class="log-item error">启动URL监控失败: ${h.message}</span><br/>`,null}},$e=async a=>{if(!a)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(G).forEach(l=>{G[l].urlId===a&&delete G[l]}),t.value+=`<span class="log-item info">正在停止URL监控: ${a}</span><br/>`;const i=await kt(async()=>await Z.stopUrlMonitoring(a),"delete",`${Ct()}/api/url/monitor/${a}`);return i&&(i.success||i!=null&&i.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${a}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(i){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${i.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await kt(async()=>await Z.stopUrlMonitoring(a),"delete",`${Ct()}/api/url/monitor/${a}`)}catch{}},1e3)}catch{}return!0}},Fe=async()=>{try{const a=await kt(async()=>await Z.getUrlMonitorStatus(),"get",`${Ct()}/api/url/status`);return a.data||a}catch(a){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${a.message}</span><br/>`,[]}},lt=async a=>{try{return await Z.forceUrlCheck(a)}catch{return!1}},ue=async(a,i)=>{var l;if(a.eventType==="uploadSuccess"){const v=a.data.file,r=v.replace(/\.docx$/,"");if(n[r]){if(n[r].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${r.substring(0,8)} 的上传通知</span><br/>`;return}if(n[r].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${r.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${v} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${i||n[r].wordType||"wps-analysis"}</span><br/>`,n[r].status=1,n[r].uploadSuccess=!0,await ht(r,i||n[r].wordType||"wps-analysis")}}else if(a.eventType==="encryptedFileError"){const v=a.data.file,r=v.replace(/\.docx$/,"");if(n[r]){t.value+=`<span class="log-item error">文件加密错误: ${v}</span><br/>`,t.value+=`<span class="log-item error">${a.data.message}</span><br/>`,n[r].status=-1,n[r].errorMessage=a.data.message||"文件已加密，无法处理";try{const h=P();if(h&&h.ContentControls)for(let c=1;c<=h.ContentControls.Count;c++)try{const O=h.ContentControls.Item(c);if(O&&O.Title&&(O.Title===`任务_${r}`||O.Title===`任务增强_${r}`||O.Title===`校对_${r}`)){const A=O.Title===`任务增强_${r}`||n[r].isEnhanced,E=O.Title===`校对_${r}`||n[r].isCheckTask;E?O.Title=`异常校对_${r}`:A?O.Title=`异常增强_${r}`:O.Title=`异常_${r}`;const R=E?"校对":A?"增强":"普通";t.value+=`<span class="log-item info">已将${R}任务${r.substring(0,8)}控件标记为异常（文件加密）</span><br/>`;break}}catch{continue}}catch(h){t.value+=`<span class="log-item warning">更新控件标题失败: ${h.message}</span><br/>`}ye(r)}}else if(a.eventType!=="urlMonitorUpdate")if(a.eventType==="urlMonitorStopped"){const{urlId:v,url:r,taskId:h,downloadedPath:c}=a.data,O=Object.keys(G).filter(A=>G[A].urlId===v);O.length>0&&O.forEach(A=>{c&&n[A]&&(n[A].resultFile=c,n[A].resultDownloaded=!0),delete G[A]})}else if(a.eventType==="urlFileDownloaded"){const{urlId:v,url:r,filePath:h,taskId:c}=a.data;if(!Object.values(G).some(A=>A.urlId===v)&&c&&((l=n[c])!=null&&l.terminated))return;if(c&&n[c]&&n[c].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${v} 的文件下载通知</span><br/>`,v)try{await Z.stopUrlMonitoring(v),G[c]&&delete G[c]}catch{}return}if(c&&n[c]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${h}</span><br/>`,n[c].isCheckTask&&h.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${h}</span><br/>`;try{const E=window.Application.FileSystem.ReadFile(h),R=JSON.parse(E);if(await U(R,c)){n[c].status=2,t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}已完成批注处理</span><br/>`;const ee=g(n[c].startTime);t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}完成，总耗时${ee}</span><br/>`}}catch(A){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${A.message}</span><br/>`,A.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${h}</span><br/>`),n[c].status=-1,n[c].errorMessage=`JSON处理失败: ${A.message}`,t.value+=`<span class="log-item error">校对任务${c.substring(0,8)}处理失败</span><br/>`}}else{n[c].resultFile=h,n[c].resultDownloaded=!0;const A=g(n[c].startTime);t.value+=`<span class="log-item success">任务${c.substring(0,8)}完成，总耗时${A}</span><br/>`,await je(c)}G[c]&&G[c].urlId&&($e(G[c].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete G[c])}else if(v){t.value+=`<span class="log-item info">URL文件已下载: ${h}</span><br/>`;const A=Object.keys(G).filter(E=>{var R;return G[E].urlId===v&&!((R=n[E])!=null&&R.terminated)});A.length>0&&A.forEach(async E=>{if(n[E]){if(t.value+=`<span class="log-item info">关联到任务: ${E.substring(0,8)}</span><br/>`,n[E].resultFile=h,n[E].resultDownloaded=!0,n[E].isCheckTask&&h.endsWith(".wps.json"))try{const N=window.Application.FileSystem.ReadFile(h),ee=JSON.parse(N);if(await U(ee,E)&&n[E].status===1){n[E].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const he=g(n[E].startTime);t.value+=`<span class="log-item success">校对任务${E.substring(0,8)}完成，总耗时${he}</span><br/>`}}catch(R){t.value+=`<span class="log-item error">处理校对JSON失败: ${R.message}</span><br/>`,n[E].status===1&&(n[E].status=-1,n[E].errorMessage=`JSON处理失败: ${R.message}`,t.value+=`<span class="log-item error">校对任务${E.substring(0,8)}处理失败</span><br/>`)}else if(n[E].status===1){n[E].status=2;const R=g(n[E].startTime);t.value+=`<span class="log-item success">任务${E.substring(0,8)}完成，总耗时${R}</span><br/>`}}})}}else if(a.eventType==="urlFileDownloadError"){const{urlId:v,url:r,error:h,taskId:c}=a.data;if(c&&n[c]&&n[c].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${c.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${h}</span><br/>`,c&&n[c]){n[c].status=-1,n[c].errorMessage=`下载失败: ${h}`;try{const O=P();if(O&&O.ContentControls)for(let A=1;A<=O.ContentControls.Count;A++)try{const E=O.ContentControls.Item(A);if(E&&E.Title&&(E.Title===`任务_${c}`||E.Title===`任务增强_${c}`||E.Title===`校对_${c}`)){const R=E.Title===`任务增强_${c}`||n[c].isEnhanced,N=E.Title===`校对_${c}`||n[c].isCheckTask;N?E.Title=`异常校对_${c}`:R?E.Title=`异常增强_${c}`:E.Title=`异常_${c}`;const ee=N?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${ee}任务${c.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(O){t.value+=`<span class="log-item warning">更新控件标题失败: ${O.message}</span><br/>`}ye(c),G[c]&&delete G[c]}if(v)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${v}</span><br/>`,await kt(async()=>await Z.stopUrlMonitoring(v),"delete",`${Ct()}/api/url/monitor/${v}`)}catch{}}else a.eventType==="resumeUrlMonitors"&&console.log(a.data)},ht=async(a,i="wps-analysis")=>{try{if(!C.appKey)throw new Error("未初始化appKey信息");const l=await d(),v=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${a}.docx`,r=await Rt.post(bs()+"/api/open/ticket/v1/ai_comment/create",{access_token:l,data:{app_key:C.appKey,subject:W.value,stage:I.value,file_name:`${a}`,word_url:v,word_type:i,is_ai_auto:!0,is_ai_edit:!0,create_user_id:C.userId,create_username:C.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),h=r.data.data.ticket_id;if(!h)return n[a]&&(n[a].status=-1,n[a].errorMessage="无法获取ticket_id",ye(a)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${h}，开始监控结果文件</span><br/>`;let c,O;i==="wps-check"?(c=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${C.appKey}/ai/${h}.wps.json`,O=`${h}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${O}</span><br/>`):(c=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${h}.wps.docx`,O=`${h}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${O}</span><br/>`);const A={downloadOnSuccess:!0,filename:O,appKey:C.appKey,ticketId:h,taskId:a},E=await Oe(c,a,!0,3e3,A);return n[a]&&(n[a].ticketId=h,n[a].resultUrl=c,n[a].urlMonitorId=E,n[a].status=1),r.data}catch(l){return t.value+=`<span class="log-item error">API调用失败: ${l.message}</span><br/>`,n[a]&&(n[a].status=-1,n[a].errorMessage=`API调用失败: ${l.message}`,ye(a)),!1}},Ye=async(a,i="wps-analysis")=>new Promise((l,v)=>{try{t.value+=`<span class="log-item info">监控目录: ${w.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${i}</span><br/>`,n[a]&&(n[a].status=0,n[a].wordType=i,n[a].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const r=1e3,h=10;let c=0;const O=setInterval(()=>{if(c++,n[a]&&n[a].uploadSuccess){clearInterval(O),l(!0);return}if(n[a]&&n[a].status===-1&&(clearInterval(O),t.value+=`<span class="log-item error">任务${a.substring(0,8)}已异常，停止等待上传完成</span><br/>`,v(new Error(n[a].errorMessage||"任务已异常"))),c>=h){clearInterval(O),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',ye(a);return}},r)}catch(r){t.value+=`<span class="log-item error">任务处理异常: ${r.message}</span><br/>`,n[a]&&(n[a].status=-1,n[a].errorMessage=`任务处理异常: ${r.message}`),ye(a),v(r)}}),Qe=async()=>{var l;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const a=window.wps,i=a.ActiveDocument;if(o.value=i.DocID,m.value=a.ActiveWindow.Index,i!=null&&i.ContentControls)for(let v=1;v<=i.ContentControls.Count;v++){const r=i.ContentControls.Item(v);if(r&&r.Title){let h=null,c=1,O=!1,A=!1;if(r.Title.startsWith("任务增强_")?(h=r.Title.substring(5),c=1,O=!0):r.Title.startsWith("任务_")?(h=r.Title.substring(3),c=1):r.Title.startsWith("校对_")?(h=r.Title.substring(3),c=1,A=!0):r.Title.startsWith("已完成增强_")?(h=r.Title.substring(6),c=2,O=!0):r.Title.startsWith("已完成校对_")?(h=r.Title.substring(6),c=2,A=!0):r.Title.startsWith("已完成_")?(h=r.Title.substring(4),c=2):r.Title.startsWith("异常增强_")?(h=r.Title.substring(5),c=-1,O=!0):r.Title.startsWith("异常校对_")?(h=r.Title.substring(5),c=-1,A=!0):r.Title.startsWith("异常_")?(h=r.Title.substring(3),c=-1):r.Title.startsWith("已停止增强_")?(h=r.Title.substring(6),c=4,O=!0):r.Title.startsWith("已停止校对_")?(h=r.Title.substring(6),c=4,A=!0):r.Title.startsWith("已停止_")&&(h=r.Title.substring(4),c=4),h&&!n[h]){let E="";try{E=((l=r.Range)==null?void 0:l.Text)||""}catch{}let R=Date.now()-24*60*60*1e3;try{if(h.length===24){const oe=h.substring(0,8),he=parseInt(oe,16);!isNaN(he)&&he>0&&he<2147483647&&(R=he*1e3)}else{const oe=Date.now()-864e5;c===2?R=oe-60*60*1e3:c===-1?R=oe-30*60*1e3:c===4?R=oe-45*60*1e3:R=oe}}catch{}n[h]={status:c,startTime:R,contentControlId:r.ID,isEnhanced:O,isCheckTask:A,selectedText:E};const N=c===1?"进行中":c===2?"已完成":c===-1?"异常":c===4?"已停止":"未知",ee=A?"校对":O?"增强":"普通";t.value+=`<span class="log-item info">发现已有${ee}任务: ${h.substring(0,8)}, 状态: ${N}</span><br/>`}}}},ye=(a,i=!1)=>{try{if(!n[a]){t.value+=`<span class="log-item warning">找不到任务${a.substring(0,8)}的记录，无法清除控件</span><br/>`;return}i&&n[a].status===1&&(n[a].status=3,n[a].errorMessage="用户主动释放");const l=P();if(!l){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let v=!1;const r=n[a].isEnhanced;if(l.ContentControls&&l.ContentControls.Count>0)for(let h=l.ContentControls.Count;h>=1;h--)try{const c=l.ContentControls.Item(h);c&&c.Title&&c.Title.includes(a)&&(c.LockContents=!1,c.Delete(!1),v=!0,console.log(c.Title),t.value+=`<span class="log-item success">已解锁并删除${r?"增强":"普通"}任务${a.substring(0,8)}的内容控件</span><br/>`)}catch(c){t.value+=`<span class="log-item warning">访问第${h}个控件时出错: ${c.message}</span><br/>`;continue}v?n[a].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${r?"增强":"普通"}任务${a.substring(0,8)}的内容控件</span><br/>`}catch(l){t.value+=`<span class="log-item error">删除内容控件失败: ${l.message}</span><br/>`}},et=a=>{var i;try{const l=window.wps,v=P();if(!v||!v.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const r=(i=n[a])==null?void 0:i.isEnhanced;let h=null;for(let O=1;O<=v.ContentControls.Count;O++)try{const A=v.ContentControls.Item(O);if(A&&A.Title&&A.Title.includes(a)){h=A;break}}catch{continue}if(!h){const O=n[a];O&&(O.status===2||O.status===-1)?t.value+=`<span class="log-item info">任务ID: ${a.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${a.substring(0,8)} 对应的内容控件</span><br/>`;return}h.Range.Select();const c=l.Windows.Item(m.value);c&&c.Selection&&c.Selection.Range&&c.ScrollIntoView(c.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${r?"增强":"普通"}任务ID: ${a.substring(0,8)} 位置</span><br/>`}catch(l){t.value+=`<span class="log-item error">跳转到任务控件失败: ${l.message}</span><br/>`}},je=async a=>{var c,O,A;const i=P(),l=n[a];if(!l){t.value+=`<span class="log-item error">找不到ID为${a.substring(0,8)}的任务, 现有任务ID: ${Object.keys(n).join(", ")}</span><br/>`;return}if(l.terminated||l.status!==1)return;if(l.documentInserted){t.value+=`<span class="log-item info">任务${a.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const v=P();let r=null;if(v&&v.ContentControls)for(let E=1;E<=v.ContentControls.Count;E++){const R=v.ContentControls.Item(E);if(R&&R.Title&&R.Title.includes(a)){r=R;break}}if(!r){if(t.value+=`<span class="log-item error">找不到任务${a.substring(0,8)}的内容控件</span><br/>`,l.errorMessage&&l.status===1){n[a].status=-1,t.value+=`<span class="log-item error">任务${a.substring(0,8)}失败: ${l.errorMessage}</span><br/>`;return}return}if(l.errorMessage&&l.status===1){n[a].status=-1,t.value+=`<span class="log-item error">任务${a.substring(0,8)}失败: ${l.errorMessage}</span><br/>`,ye(a);return}if(!l.resultFile)return;n[a].documentInserted=!0;const h=r.Range;r.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${l.resultFile}...</span><br/>`;const E=i.Range(h.End,h.End);E.InsertParagraphAfter(),h.Text.includes("\v")&&E.InsertAfter("\v");let R=E.End;const N=(c=h.Tables)==null?void 0:c.Count;if(N){const ve=h.Tables.Item(N);((O=ve==null?void 0:ve.Range)==null?void 0:O.End)>R&&(ve.Range.InsertParagraphAfter(),R=(A=ve==null?void 0:ve.Range)==null?void 0:A.End)}i.Range(R,R).InsertFile(l.resultFile);for(let ve=1;ve<=v.ContentControls.Count;ve++){const ot=v.ContentControls.Item(ve);if(ot&&ot.Title&&(ot.Title===`任务_${a}`||ot.Title===`任务增强_${a}`)){r=ot;break}}const oe=i.Range(r.Range.End-1,r.Range.End);oe.Text.includes("\r")&&oe.Delete(),n[a].status=2,G[a]&&G[a].urlId&&Z.sendRequest("urlMonitor","updateTaskStatus",{urlId:G[a].urlId,status:"completed",taskId:a}).then(ve=>{t.value+=`<span class="log-item info">已通知服务端更新任务${a.substring(0,8)}状态为完成</span><br/>`}).catch(ve=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${ve.message}</span><br/>`});const Ke=g(l.startTime);t.value+=`<span class="log-item success">任务${a.substring(0,8)}处理完成，总耗时${Ke}</span><br/>`;const dt=r.Title===`任务增强_${a}`||l.isEnhanced,ke=r.Title===`校对_${a}`||l.isCheckTask;if(r){ke?r.Title=`已完成校对_${a}`:dt?r.Title=`已完成增强_${a}`:r.Title=`已完成_${a}`;const ve=ke?"校对":dt?"增强":"普通";t.value+=`<span class="log-item info">已将${ve}任务${a.substring(0,8)}控件标记为已完成</span><br/>`}}catch(E){n[a].documentInserted=!1,n[a].status=-1;const R=r.Title===`任务增强_${a}`||l.isEnhanced,N=r.Title===`校对_${a}`||l.isCheckTask;if(r){N?r.Title=`异常校对_${a}`:R?r.Title=`异常增强_${a}`:r.Title=`异常_${a}`;const ee=N?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${ee}任务${a.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${E.message}</span><br/>`}},Ie=async()=>{const a=(v=1e3)=>new Promise(r=>{setTimeout(()=>r(),v)}),i=new Map,l=Object.keys(n).filter(v=>n[v].status===1&&!n[v].terminated);for(l.length?(l.forEach(v=>{i.has(v)||i.set(v,Date.now())}),await Promise.all(l.map(v=>je(v)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await a(3e3);const v=Object.keys(n).filter(r=>n[r].status===1&&!n[r].terminated);v.forEach(r=>{i.has(r)||i.set(r,Date.now());const h=i.get(r);(Date.now()-h)/1e3/60>=5e4&&n[r]&&!n[r].terminated&&(n[r].terminated=!0,n[r].status=-1,t.value+=`<span class="log-item warning">任务 ${r} 执行超过5分钟，已自动终止</span><br/>`,i.delete(r))}),v.length&&await Promise.all(v.map(r=>je(r)))}},Re=async(a="wps-analysis")=>{const i=y();if(i){const{primary:v,all:r}=i,{taskId:h,control:c,task:O,isEnhanced:A,overlapType:E}=v,R=r.filter(N=>N.task&&(N.task.status===0||N.task.status===1));if(R.length>0){const N=R.map(ee=>ee.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${N})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${r.length}个已有任务重叠，重叠类型: ${E}</span><br/>`,j();try{for(const N of r){const{taskId:ee,control:oe,task:he,isEnhanced:Ke}=N;t.value+=`<span class="log-item info">删除重叠的${Ke?"增强":"普通"}任务 ${ee.substring(0,8)}，状态为 ${b((he==null?void 0:he.status)||0)}</span><br/>`,he&&(he.status=3,he.terminated=!0,he.errorMessage="用户重新创建任务时删除");try{oe.LockContents=!1,oe.Delete(!1),n[ee]&&(n[ee].placeholderRemoved=!0)}catch(dt){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${dt.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${r.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(N){return z(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${N.message}</span><br/>`,Promise.reject(N)}z()}const l=ws().replace(/-/g,"").substring(0,8);return new Promise(async(v,r)=>{try{const h=window.wps,c=P(),O=c.ActiveWindow.Selection,A=O.Range,E=O.Text||"";if(s.value=E,S(),A){const R=A.Paragraphs,N=R.Item(1),ee=R.Item(R.Count),oe=N.Range.Start,he=ee.Range.End,Ke=A.Start,dt=A.End,ke=c.Range(Math.min(oe,Ke),Math.max(he,dt));try{let ve=c.ContentControls.Add(h.Enum.wdContentControlRichText,ke);if(!ve){if(console.log("创建内容控件失败"),ve=c.ContentControls.Add(h.Enum.wdContentControlRichText),!ve){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',r(new Error("创建内容控件失败"));return}A.Cut(),ve.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const ot=a==="wps-enhance_analysis";ve.Title=ot?`任务增强_${l}`:`任务_${l}`,ve.LockContents=!0,n[l]||(n[l]={}),n[l].contentControlId=ve.ID,n[l].isEnhanced=ot,t.value+=`<span class="log-item info">已创建${ot?"增强":"普通"}内容控件并锁定选区</span><br/>`;const rn=ve.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${rn.length}</span><br/>`}catch(ve){t.value+=`<span class="log-item error">创建内容控件失败: ${ve.message}</span><br/>`,r(ve);return}}n[l]={status:0,startTime:Date.now(),wordType:a,isEnhanced:a==="wps-enhance_analysis",selectedText:E},t.value+=`<span class="log-item success">创建${a==="wps-enhanced"?"增强":"普通"}任务: ${l}，类型: ${a}</span><br/>`,await Le(l),n[l]&&(n[l].status=1);try{await Ye(l,a)?v():(n[l]&&n[l].status===1&&(n[l].status=-1,n[l].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${l.substring(0,8)}失败</span><br/>`,le(l)),r(new Error("API调用失败或超时")))}catch(R){n[l]&&(n[l].status=-1,n[l].errorMessage=`执行错误: ${R.message}`,t.value+=`<span class="log-item error">任务${l.substring(0,8)}执行出错: ${R.message}</span><br/>`,le(l)),r(R)}}catch(h){r(h)}})},tt=async()=>{const a=y();if(a){const{primary:l,all:v}=a,{taskId:r,control:h,task:c,isEnhanced:O,overlapType:A}=l,E=v.filter(R=>R.task&&(R.task.status===0||R.task.status===1));if(E.length>0){const R=E.map(N=>N.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${R})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${v.length}个已有任务重叠，重叠类型: ${A}</span><br/>`,j();try{for(const R of v){const{taskId:N,control:ee,task:oe,isEnhanced:he}=R;t.value+=`<span class="log-item info">删除重叠的校对任务 ${N.substring(0,8)}，状态为 ${b((oe==null?void 0:oe.status)||0)}</span><br/>`,oe&&(oe.status=3,oe.terminated=!0,oe.errorMessage="用户重新创建校对任务时删除");try{ee.LockContents=!1,ee.Delete(!1),n[N]&&(n[N].placeholderRemoved=!0)}catch(Ke){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${Ke.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${v.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(R){return z(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${R.message}</span><br/>`,Promise.reject(R)}z()}const i=ws().replace(/-/g,"").substring(0,8);return console.log("uuid",i),new Promise(async(l,v)=>{try{const r=window.wps,h=P(),c=h.ActiveWindow.Selection,O=c.Range,A=c.Text||"";if(s.value=A,S(),O){const E=O.Paragraphs,R=E.Item(1),N=E.Item(E.Count),ee=R.Range.Start,oe=N.Range.End,he=O.Start,Ke=O.End,dt=h.Range(Math.min(ee,he),Math.max(oe,Ke));try{let ke=h.ContentControls.Add(r.Enum.wdContentControlRichText,dt);if(!ke){if(console.log("创建校对内容控件失败"),ke=h.ContentControls.Add(r.Enum.wdContentControlRichText),!ke){t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',v(new Error("创建校对内容控件失败"));return}O.Cut(),ke.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',ke.Title=`校对_${i}`,ke.LockContents=!0,n[i]||(n[i]={}),n[i].contentControlId=ke.ID,n[i].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const ve=ke.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${ve.length}</span><br/>`}catch(ke){t.value+=`<span class="log-item error">创建校对内容控件失败: ${ke.message}</span><br/>`,v(ke);return}}n[i]={status:0,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:A},t.value+=`<span class="log-item success">创建校对任务: ${i}，类型: wps-check</span><br/>`,console.log("uuid",i),await Le(i),n[i]&&(n[i].status=1);try{await Ye(i,"wps-check")?l():(n[i]&&n[i].status===1&&(n[i].status=-1,n[i].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}失败</span><br/>`,le(i)),v(new Error("校对API调用失败或超时")))}catch(E){n[i]&&(n[i].status=-1,n[i].errorMessage=`校对执行错误: ${E.message}`,t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}执行出错: ${E.message}</span><br/>`,le(i)),v(E)}}catch(r){v(r)}})},He=async()=>{},st=()=>rt()>0?"status-error":nt()>0?"status-running":at()>0?"status-completed":"",nt=()=>Object.values(n).filter(a=>a.status===0||a.status===1).length,at=()=>Object.values(n).filter(a=>a.status===2).length,ct=()=>Object.values(n).filter(a=>a.status===2||a.status===4).length,rt=()=>Object.values(n).filter(a=>a.status===-1).length,yt=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const a=window.wps,i=P();if(!i){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let l=0;if(Object.keys(n).forEach(v=>{try{n[v].status===1&&(n[v].status=3,n[v].terminated=!0,n[v].errorMessage="强制清除"),ye(v),l++}catch(r){t.value+=`<span class="log-item warning">清除任务${v.substring(0,8)}失败: ${r.message}</span><br/>`}}),i.ContentControls&&i.ContentControls.Count>0)for(let v=i.ContentControls.Count;v>=1;v--)try{const r=i.ContentControls.Item(v);if(r&&r.Title&&(r.Title.startsWith("任务_")||r.Title.startsWith("任务增强_")||r.Title.startsWith("已完成_")||r.Title.startsWith("已完成增强_")||r.Title.startsWith("异常_")||r.Title.startsWith("异常增强_")||r.Title.startsWith("已停止_")||r.Title.startsWith("已停止增强_")))try{r.LockContents=!1,r.Delete(!1);let h;r.Title.startsWith("任务增强_")?h=r.Title.substring(5):r.Title.startsWith("任务_")?h=r.Title.substring(3):r.Title.startsWith("已完成增强_")?h=r.Title.substring(6):r.Title.startsWith("已完成_")?h=r.Title.substring(4):r.Title.startsWith("异常增强_")?h=r.Title.substring(5):r.Title.startsWith("异常_")?h=r.Title.substring(3):r.Title.startsWith("已停止增强_")?h=r.Title.substring(6):r.Title.startsWith("已停止_")&&(h=r.Title.substring(4)),n[h]?(n[h].placeholderRemoved=!0,n[h].status=3):n[h]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},l++,t.value+=`<span class="log-item success">已删除任务${h.substring(0,8)}的内容控件</span><br/>`}catch(h){t.value+=`<span class="log-item error">删除控件失败: ${h.message}</span><br/>`}}catch(r){t.value+=`<span class="log-item warning">访问控件时出错: ${r.message}</span><br/>`}l>0?t.value+=`<span class="log-item success">已清除${l}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(a){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${a.message}</span><br/>`}},xt=async()=>{try{const a=Object.values(G).map(i=>i.urlId);if(a.length>0){t.value+=`<span class="log-item info">清理${a.length}个URL监控任务...</span><br/>`;for(const i of a)await $e(i)}}catch(a){t.value+=`<span class="log-item error">清理URL监控任务失败: ${a.message}</span><br/>`}},Mt=()=>{Fs(async()=>{try{const i=window.Application.PluginStorage.getItem("user_info");if(!i)throw new Error("未找到用户信息");const l=JSON.parse(i);if(!l.orgs||!l.orgs[0])throw new Error("未找到组织信息");C.appKey=l.appKey,C.userName=l.nickname,C.userId=l.userId,C.appSecret=l.appSecret}catch(i){t.value+=`<span class="log-item error">初始化appKey失败: ${i.message}</span><br/>`}await H(),Jt([W,I],async()=>{await fe()},{immediate:!1}),await Q();const a=qe.onVersionChange(()=>{const i=te();se.splice(0,se.length,...i),qe.isSeniorEdition()&&I.value==="junior"?I.value="senior":!qe.isSeniorEdition()&&I.value==="senior"&&(I.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${qe.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',L(),await Qe(),Pe(),Dt(),Ie(),window.addEventListener("beforeunload",xt),()=>{_&&clearTimeout(_),a&&a()}}),Jt(t,a=>{_&&clearTimeout(_),_=setTimeout(()=>{_e(a)},10)},{immediate:!1})},Dt=()=>{Z.addEventListener("config",a=>{a.eventType==="subjectAndStageChanged"&&a.data&&(a.data.subject!==W.value&&(W.value=a.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${W.value}</span><br/>`),a.data.stage!==I.value&&(I.value=a.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${I.value}</span><br/>`))})},ut=ge(!1),y=()=>{try{const a=P(),i=a.ActiveWindow.Selection;if(!i||!a||!a.ContentControls)return null;const l=i.Range,v=[];for(let r=1;r<=a.ContentControls.Count;r++)try{const h=a.ContentControls.Item(r);if(!h)continue;const c=(h.Title||"").trim(),O=h.Range;if(l.Start<O.End&&l.End>O.Start){let A=null,E=!1,R=!1;if(!c)R=!0,A=`empty_${h.ID||Date.now()}`;else if(c.startsWith("任务_")||c.startsWith("任务增强_")||c.startsWith("校对_")||c.startsWith("已完成_")||c.startsWith("已完成增强_")||c.startsWith("已完成校对_")||c.startsWith("异常_")||c.startsWith("异常增强_")||c.startsWith("异常校对_")||c.startsWith("已停止_")||c.startsWith("已停止增强_")||c.startsWith("已停止校对_"))c.startsWith("任务增强_")?(A=c.substring(5),E=!0):c.startsWith("任务_")||c.startsWith("校对_")?A=c.substring(3):c.startsWith("已完成增强_")?(A=c.substring(6),E=!0):c.startsWith("已完成校对_")?A=c.substring(6):c.startsWith("已完成_")?A=c.substring(4):c.startsWith("异常增强_")?(A=c.substring(5),E=!0):c.startsWith("异常校对_")?A=c.substring(5):c.startsWith("异常_")?A=c.substring(3):c.startsWith("已停止增强_")?(A=c.substring(6),E=!0):c.startsWith("已停止校对_")?A=c.substring(6):c.startsWith("已停止_")&&(A=c.substring(4));else continue;if(A){let N;l.Start>=O.Start&&l.End<=O.End?N="completely_within":l.Start<=O.Start&&l.End>=O.End?N="completely_contains":N="partial_overlap",v.push({taskId:A,control:h,task:R?null:n[A]||null,isEnhanced:E,isEmptyTitle:R,overlapType:N,controlRange:{start:O.Start,end:O.End},selectionRange:{start:l.Start,end:l.End}})}}}catch{continue}return v.length===0?null:{primary:v[0],all:v}}catch(a){return t.value+=`<span class="log-item error">检查选区位置出错: ${a.message}</span><br/>`,null}},j=()=>{ut.value=!0},z=()=>{ut.value=!1},le=async(a,i=!1)=>{j();try{await ye(a,i)}finally{z()}},re=a=>new Promise((i,l)=>{D.show=!0,D.message=a,D.resolveCallback=i,D.rejectCallback=l}),k=a=>{D.show=!1,a&&D.resolveCallback?D.resolveCallback(!0):D.resolveCallback&&D.resolveCallback(!1),D.resolveCallback=null,D.rejectCallback=null},f=(a,i,l="error")=>{F.show=!0,F.title=a,F.message=i,F.type=l},X=()=>{F.show=!1,F.title="",F.message="",F.type="error"},Q=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const a=await Z.getSubjectAndStage();a.success&&a.data?(a.data.subject&&(W.value=a.data.subject),a.data.stage&&(I.value=a.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${W.value})和年级(${I.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(a){console.error("从服务器加载学科和年级设置失败:",a),t.value+=`<span class="log-item error">从服务器加载设置失败: ${a.message}</span><br/>`}},fe=async()=>{try{if(W.value||I.value){t.value+=`<span class="log-item info">正在保存学科(${W.value})和年级(${I.value})设置到服务器...</span><br/>`;const a=await Z.setSubjectAndStage(W.value,I.value);a&&a.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(a==null?void 0:a.message)||"未知错误"}</span><br/>`}}catch(a){console.error("保存学科和年级到服务器失败:",a),t.value+=`<span class="log-item error">保存设置到服务器失败: ${a.message}</span><br/>`}},de=async()=>{try{V.value=2,q.value=V.value===2,q.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${V.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${V.value}）</span><br/>`}catch(a){console.error("检查企业ID失败:",a),t.value+=`<span class="log-item error">检查企业ID失败: ${a.message}</span><br/>`,q.value=!1}},we=(a,i)=>{try{const l=P();if(!l||!a)return!1;console.log("批注",a,i);const v=l.Comments.Add(a,i);return!0}catch(l){return t.value+=`<span class="log-item error">为范围添加批注失败: ${l.message}</span><br/>`,!1}},U=async(a,i)=>{try{if(!a||!Array.isArray(a))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const l=P();let v=null,r=null;if(l&&l.ContentControls)for(let A=1;A<=l.ContentControls.Count;A++)try{const E=l.ContentControls.Item(A);if((E==null?void 0:E.Title)===`校对_${i}`){v=E,r=E.Range,t.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!r){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const A=n[i];if(A&&A.selectedText)try{const E=l.Range().Find;if(E.ClearFormatting(),E.Text=A.selectedText.substring(0,Math.min(A.selectedText.length,100)),E.Forward=!0,E.Wrap=1,E.Execute()){const R=E.Parent;if(A.selectedText.length>100){const N=R.Start+A.selectedText.length;r=l.Range(R.Start,Math.min(N,l.Range().End))}else r=R;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(E){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${E.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let h=0,c=0,O=[];for(const A of a){if(!A.mode1||!Array.isArray(A.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const E of A.mode1){if(!E.error_info||!Array.isArray(E.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const R=E.quest_html||"",N=E.quest_type||"";t.value+=`<span class="log-item info">处理${N}题目，发现${E.error_info.length}个错误信息</span><br/>`;for(const ee of E.error_info)try{let oe="";if(ee.error_info&&(oe+=`【错误类型】${ee.error_info}`),ee.fix_info&&(oe+=`\r
【修正建议】${ee.fix_info}`),ee.keywords&&ee.keywords.trim()){let he=v?v.Range:r;ce(oe,ee.keywords.trim(),-1,v.Range)?(h++,t.value+=`<span class="log-item success">已为关键词"${ee.keywords.trim()}"添加批注</span><br/>`):(O.push({comment:oe,keyword:ee.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${ee.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else O.push({comment:oe,keyword:null})}catch(oe){c++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${oe.message}</span><br/>`}}}if(O.length>0){t.value+=`<span class="log-item info">为整个控件范围添加${O.length}个批注</span><br/>`;for(const A of O)try{let E=A.comment,R=v?v.Range:r;v.LockContents=!1,we(v.Range,E)?(h++,t.value+=`<span class="log-item success">已为整个范围添加批注${A.keyword?`（关键词：${A.keyword}）`:""}</span><br/>`):c++}catch(E){c++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${E.message}</span><br/>`}}return h>0?(t.value+=`<span class="log-item success">校对任务${i.substring(0,8)}处理完成：成功添加${h}个批注</span><br/>`,c>0&&(t.value+=`<span class="log-item warning">校对任务${i.substring(0,8)}：${c}个批注添加失败</span><br/>`),!0):(t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(l){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${l.message}</span><br/>`,!1}};return{docName:e,selected:s,logger:t,map:n,watchedDir:w,subject:W,stage:I,subjectOptions:B,stageOptions:se,fetchWatchedDir:H,clearLog:ne,getCurrentDocument:P,checkDocumentFormat:ie,getTaskStatusClass:u,getTaskStatusText:b,getElapsedTime:g,terminateTask:$,stopTaskWithoutRemovingControl:x,run1:Re,run2:He,runCheck:tt,getHeaderStatusClass:st,getRunningTasksCount:nt,getCompletedTasksCount:at,getReleasableTasksCount:ct,getErrorTasksCount:rt,setupLifecycle:Mt,navigateToTaskControl:et,forceCleanAllTasks:yt,ws:Ee,wsMessages:De,initWebSocket:Pe,handleWatcherEvent:ue,urlMonitorTasks:G,monitorUrlForTask:Oe,stopUrlMonitoring:$e,getUrlMonitorStatus:Fe,forceUrlCheck:lt,cleanupUrlMonitoringTasks:xt,tryRemoveTaskPlaceholder:ye,isLoading:ut,isSelectionInTaskControl:y,tryRemoveTaskPlaceholderWithLoading:le,showConfirm:re,handleConfirm:k,confirmDialog:D,errorDialog:F,showErrorDialog:f,hideErrorDialog:X,loadSubjectAndStage:Q,saveSubjectAndStage:fe,enterpriseId:V,isCheckingVisible:q,checkEnterpriseAndSetCheckingVisibility:de,processCheckingJson:U}}const kn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:qe.getVersionConfig(),selectedEdition:qe.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,n=Math.floor(t/(1e3*60*60)),o=Math.floor(t%(1e3*60*60)/(1e3*60)),m=Math.floor(t%(1e3*60)/1e3);return`${n}小时 ${o}分 ${m}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Z.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Z[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Z.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Z.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Z.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Z.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Z.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Z.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Z.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await qe.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await ln()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Z.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Z.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Z.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Z.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Z.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Z.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Z.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=qe.onVersionChange(e=>{this.versionConfig=qe.getVersionConfig(),this.selectedEdition=qe.getEdition()}),await Z.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},$n={class:"file-watcher"},Sn={class:"settings-modal"},En={class:"modal-content"},_n={class:"modal-header"},An={class:"version-tag"},Mn={key:0,class:"user-info"},Dn={class:"header-actions"},Pn={class:"modal-body"},On={class:"status-section"},In={class:"status-item"},Rn={key:0,class:"status-item"},Un={class:"directory-section"},Ln={class:"directory-form"},Fn={class:"radio-group"},jn={class:"radio-item"},Wn={class:"radio-item"},Bn={class:"radio-item"},Nn={key:0,class:"radio-item"},Vn={key:1,class:"directory-section"},Hn={class:"directory-form"},zn={class:"form-group"},qn=["placeholder"],Jn=["disabled"],Yn={key:2,class:"directory-section"},Kn={class:"directory-form"},Xn={class:"form-group"},Gn=["placeholder"],Zn=["disabled"],Qn={key:3,class:"directory-section"},ea={class:"directory-form"},ta={class:"form-group"},sa=["placeholder"],na=["disabled"],aa={key:4,class:"events-section"},ra={class:"events-list"},oa={class:"event-time"},ia={class:"event-message"},la={key:1,class:"modal-footer"};function ca(e,s,t,n,o,m){var T,_,w,C,D,F,W,I,V,q,B,te,se,H,d,ne,P,ie,ce,u,b;return J(),Y("div",$n,[p("div",Sn,[p("div",En,[p("div",_n,[p("h3",null,[cn(pe(o.versionConfig.shortName)+"设置 ",1),p("span",An,pe(o.versionConfig.appVersion),1),m.userInfo?(J(),Y("span",Mn,"欢迎您，"+pe(m.userInfo.nickname),1)):ae("",!0)]),p("div",Dn,[p("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...g)=>m.handleLogout&&m.handleLogout(...g)),title:"退出登录"},s[21]||(s[21]=[p("span",{class:"icon-logout"},null,-1)])),p("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),p("div",Pn,[p("div",On,[p("div",In,[s[22]||(s[22]=p("span",{class:"label"},"状态：",-1)),p("span",{class:We(["status-badge",o.status.status])},pe(o.status.status==="running"?"运行中":"已停止"),3)]),((w=(_=(T=m.userInfo)==null?void 0:T.orgs)==null?void 0:_[0])==null?void 0:w.orgId)===2?(J(),Y("div",Rn,[s[23]||(s[23]=p("span",{class:"label"},"本次上传：",-1)),p("span",null,pe(o.status.processedFiles||0)+" 个文件",1)])):ae("",!0),p("div",Un,[s[28]||(s[28]=p("h4",null,"保存方式设置",-1)),p("div",Ln,[p("div",Fn,[p("label",jn,[ze(p("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=g=>o.currentSaveMethod=g),value:"method1",onChange:s[3]||(s[3]=(...g)=>m.updateSaveMethod&&m.updateSaveMethod(...g))},null,544),[[Vt,o.currentSaveMethod]]),s[24]||(s[24]=p("span",{class:"radio-label"},"方式一",-1))]),p("label",Wn,[ze(p("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=g=>o.currentSaveMethod=g),value:"method2",onChange:s[5]||(s[5]=(...g)=>m.updateSaveMethod&&m.updateSaveMethod(...g))},null,544),[[Vt,o.currentSaveMethod]]),s[25]||(s[25]=p("span",{class:"radio-label"},"方式二 (默认)",-1))]),p("label",Bn,[ze(p("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=g=>o.currentSaveMethod=g),value:"method3",onChange:s[7]||(s[7]=(...g)=>m.updateSaveMethod&&m.updateSaveMethod(...g))},null,544),[[Vt,o.currentSaveMethod]]),s[26]||(s[26]=p("span",{class:"radio-label"},"方式三",-1))]),((F=(D=(C=m.userInfo)==null?void 0:C.orgs)==null?void 0:D[0])==null?void 0:F.orgId)===2?(J(),Y("label",Nn,[ze(p("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=g=>o.currentSaveMethod=g),value:"method4",onChange:s[9]||(s[9]=(...g)=>m.updateSaveMethod&&m.updateSaveMethod(...g))},null,544),[[Vt,o.currentSaveMethod]]),s[27]||(s[27]=p("span",{class:"radio-label"},"方式四",-1))])):ae("",!0)]),o.saveMethodUpdateMessage?(J(),Y("div",{key:0,class:We(["update-message",o.saveMethodUpdateSuccess?"success":"error"])},pe(o.saveMethodUpdateMessage),3)):ae("",!0)])])]),ae("",!0),((V=(I=(W=m.userInfo)==null?void 0:W.orgs)==null?void 0:I[0])==null?void 0:V.orgId)===2?(J(),Y("div",Vn,[s[32]||(s[32]=p("h4",null,"上传目录设置",-1)),p("div",Hn,[p("div",zn,[s[31]||(s[31]=p("span",{class:"label"},"路径：",-1)),ze(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=g=>o.newWatchDir=g),placeholder:o.status.watchDir||"C:\\Temp"},null,8,qn),[[Zt,o.newWatchDir]]),p("button",{class:"action-btn",onClick:s[13]||(s[13]=(...g)=>m.updateWatchDir&&m.updateWatchDir(...g)),disabled:o.isUpdating||!o.newWatchDir},pe(o.isUpdating?"更新中...":"更新目录"),9,Jn)]),o.updateMessage?(J(),Y("div",{key:0,class:We(["update-message",o.updateSuccess?"success":"error"])},pe(o.updateMessage),3)):ae("",!0)])])):ae("",!0),((te=(B=(q=m.userInfo)==null?void 0:q.orgs)==null?void 0:B[0])==null?void 0:te.orgId)===2?(J(),Y("div",Yn,[s[34]||(s[34]=p("h4",null,"下载目录设置",-1)),p("div",Kn,[p("div",Xn,[s[33]||(s[33]=p("span",{class:"label"},"路径：",-1)),ze(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=g=>o.newDownloadPath=g),placeholder:o.downloadPath||"C:\\Temp\\Downloads"},null,8,Gn),[[Zt,o.newDownloadPath]]),p("button",{class:"action-btn",onClick:s[15]||(s[15]=(...g)=>m.updateDownloadPath&&m.updateDownloadPath(...g)),disabled:o.isUpdatingDownloadPath||!o.newDownloadPath},pe(o.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Zn)]),o.downloadPathUpdateMessage?(J(),Y("div",{key:0,class:We(["update-message",o.downloadPathUpdateSuccess?"success":"error"])},pe(o.downloadPathUpdateMessage),3)):ae("",!0)])])):ae("",!0),((d=(H=(se=m.userInfo)==null?void 0:se.orgs)==null?void 0:H[0])==null?void 0:d.orgId)===2?(J(),Y("div",Qn,[s[36]||(s[36]=p("h4",null,"配置目录设置",-1)),p("div",ea,[p("div",ta,[s[35]||(s[35]=p("span",{class:"label"},"路径：",-1)),ze(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=g=>o.newAddonConfigPath=g),placeholder:o.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,sa),[[Zt,o.newAddonConfigPath]]),p("button",{class:"action-btn",onClick:s[17]||(s[17]=(...g)=>m.updateAddonConfigPath&&m.updateAddonConfigPath(...g)),disabled:o.isUpdatingAddonConfigPath||!o.newAddonConfigPath},pe(o.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,na)]),o.addonConfigPathUpdateMessage?(J(),Y("div",{key:0,class:We(["update-message",o.addonConfigPathUpdateSuccess?"success":"error"])},pe(o.addonConfigPathUpdateMessage),3)):ae("",!0)])])):ae("",!0),((ie=(P=(ne=m.userInfo)==null?void 0:ne.orgs)==null?void 0:P[0])==null?void 0:ie.orgId)===2?(J(),Y("div",aa,[s[37]||(s[37]=p("h4",null,"最近事件",-1)),p("div",ra,[(J(!0),Y(Ot,null,It(o.recentEvents,(g,x)=>(J(),Y("div",{key:x,class:"event-item"},[p("span",oa,pe(m.formatTime(g.timestamp)),1),p("span",{class:We(["event-type",g.eventType])},pe(m.getEventTypeText(g.eventType)),3),p("span",ia,pe(m.getEventMessage(g)),1)]))),128))])])):ae("",!0)]),ae("",!0),((b=(u=(ce=m.userInfo)==null?void 0:ce.orgs)==null?void 0:u[0])==null?void 0:b.orgId)===2?(J(),Y("div",la,[p("button",{class:We(["control-btn",o.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...g)=>m.controlService&&m.controlService(...g))},pe(o.status.status==="running"?"停止服务":"启动服务"),3)])):ae("",!0)])])])}const ua=js(kn,[["render",ca],["__scopeId","data-v-48242008"]]);var Ae="top",Ne="bottom",Ve="right",Me="left",ls="auto",Bt=[Ae,Ne,Ve,Me],St="start",jt="end",da="clippingParents",Bs="viewport",Pt="popper",pa="reference",ys=Bt.reduce(function(e,s){return e.concat([s+"-"+St,s+"-"+jt])},[]),Ns=[].concat(Bt,[ls]).reduce(function(e,s){return e.concat([s,s+"-"+St,s+"-"+jt])},[]),fa="beforeRead",ha="read",va="afterRead",ga="beforeMain",ma="main",ba="afterMain",wa="beforeWrite",ya="write",xa="afterWrite",Ta=[fa,ha,va,ga,ma,ba,wa,ya,xa];function Ze(e){return e?(e.nodeName||"").toLowerCase():null}function Ue(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function wt(e){var s=Ue(e).Element;return e instanceof s||e instanceof Element}function Be(e){var s=Ue(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function cs(e){if(typeof ShadowRoot>"u")return!1;var s=Ue(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function Ca(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var n=s.styles[t]||{},o=s.attributes[t]||{},m=s.elements[t];!Be(m)||!Ze(m)||(Object.assign(m.style,n),Object.keys(o).forEach(function(T){var _=o[T];_===!1?m.removeAttribute(T):m.setAttribute(T,_===!0?"":_)}))})}function ka(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(n){var o=s.elements[n],m=s.attributes[n]||{},T=Object.keys(s.styles.hasOwnProperty(n)?s.styles[n]:t[n]),_=T.reduce(function(w,C){return w[C]="",w},{});!Be(o)||!Ze(o)||(Object.assign(o.style,_),Object.keys(m).forEach(function(w){o.removeAttribute(w)}))})}}const Vs={name:"applyStyles",enabled:!0,phase:"write",fn:Ca,effect:ka,requires:["computeStyles"]};function Ge(e){return e.split("-")[0]}var mt=Math.max,Yt=Math.min,Et=Math.round;function as(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Hs(){return!/^((?!chrome|android).)*safari/i.test(as())}function _t(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var n=e.getBoundingClientRect(),o=1,m=1;s&&Be(e)&&(o=e.offsetWidth>0&&Et(n.width)/e.offsetWidth||1,m=e.offsetHeight>0&&Et(n.height)/e.offsetHeight||1);var T=wt(e)?Ue(e):window,_=T.visualViewport,w=!Hs()&&t,C=(n.left+(w&&_?_.offsetLeft:0))/o,D=(n.top+(w&&_?_.offsetTop:0))/m,F=n.width/o,W=n.height/m;return{width:F,height:W,top:D,right:C+F,bottom:D+W,left:C,x:C,y:D}}function us(e){var s=_t(e),t=e.offsetWidth,n=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-n)<=1&&(n=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:n}}function zs(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&cs(t)){var n=s;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function it(e){return Ue(e).getComputedStyle(e)}function $a(e){return["table","td","th"].indexOf(Ze(e))>=0}function ft(e){return((wt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Xt(e){return Ze(e)==="html"?e:e.assignedSlot||e.parentNode||(cs(e)?e.host:null)||ft(e)}function xs(e){return!Be(e)||it(e).position==="fixed"?null:e.offsetParent}function Sa(e){var s=/firefox/i.test(as()),t=/Trident/i.test(as());if(t&&Be(e)){var n=it(e);if(n.position==="fixed")return null}var o=Xt(e);for(cs(o)&&(o=o.host);Be(o)&&["html","body"].indexOf(Ze(o))<0;){var m=it(o);if(m.transform!=="none"||m.perspective!=="none"||m.contain==="paint"||["transform","perspective"].indexOf(m.willChange)!==-1||s&&m.willChange==="filter"||s&&m.filter&&m.filter!=="none")return o;o=o.parentNode}return null}function Nt(e){for(var s=Ue(e),t=xs(e);t&&$a(t)&&it(t).position==="static";)t=xs(t);return t&&(Ze(t)==="html"||Ze(t)==="body"&&it(t).position==="static")?s:t||Sa(e)||s}function ds(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ut(e,s,t){return mt(e,Yt(s,t))}function Ea(e,s,t){var n=Ut(e,s,t);return n>t?t:n}function qs(){return{top:0,right:0,bottom:0,left:0}}function Js(e){return Object.assign({},qs(),e)}function Ys(e,s){return s.reduce(function(t,n){return t[n]=e,t},{})}var _a=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Js(typeof s!="number"?s:Ys(s,Bt))};function Aa(e){var s,t=e.state,n=e.name,o=e.options,m=t.elements.arrow,T=t.modifiersData.popperOffsets,_=Ge(t.placement),w=ds(_),C=[Me,Ve].indexOf(_)>=0,D=C?"height":"width";if(!(!m||!T)){var F=_a(o.padding,t),W=us(m),I=w==="y"?Ae:Me,V=w==="y"?Ne:Ve,q=t.rects.reference[D]+t.rects.reference[w]-T[w]-t.rects.popper[D],B=T[w]-t.rects.reference[w],te=Nt(m),se=te?w==="y"?te.clientHeight||0:te.clientWidth||0:0,H=q/2-B/2,d=F[I],ne=se-W[D]-F[V],P=se/2-W[D]/2+H,ie=Ut(d,P,ne),ce=w;t.modifiersData[n]=(s={},s[ce]=ie,s.centerOffset=ie-P,s)}}function Ma(e){var s=e.state,t=e.options,n=t.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o=="string"&&(o=s.elements.popper.querySelector(o),!o)||zs(s.elements.popper,o)&&(s.elements.arrow=o))}const Da={name:"arrow",enabled:!0,phase:"main",fn:Aa,effect:Ma,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function At(e){return e.split("-")[1]}var Pa={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Oa(e,s){var t=e.x,n=e.y,o=s.devicePixelRatio||1;return{x:Et(t*o)/o||0,y:Et(n*o)/o||0}}function Ts(e){var s,t=e.popper,n=e.popperRect,o=e.placement,m=e.variation,T=e.offsets,_=e.position,w=e.gpuAcceleration,C=e.adaptive,D=e.roundOffsets,F=e.isFixed,W=T.x,I=W===void 0?0:W,V=T.y,q=V===void 0?0:V,B=typeof D=="function"?D({x:I,y:q}):{x:I,y:q};I=B.x,q=B.y;var te=T.hasOwnProperty("x"),se=T.hasOwnProperty("y"),H=Me,d=Ae,ne=window;if(C){var P=Nt(t),ie="clientHeight",ce="clientWidth";if(P===Ue(t)&&(P=ft(t),it(P).position!=="static"&&_==="absolute"&&(ie="scrollHeight",ce="scrollWidth")),P=P,o===Ae||(o===Me||o===Ve)&&m===jt){d=Ne;var u=F&&P===ne&&ne.visualViewport?ne.visualViewport.height:P[ie];q-=u-n.height,q*=w?1:-1}if(o===Me||(o===Ae||o===Ne)&&m===jt){H=Ve;var b=F&&P===ne&&ne.visualViewport?ne.visualViewport.width:P[ce];I-=b-n.width,I*=w?1:-1}}var g=Object.assign({position:_},C&&Pa),x=D===!0?Oa({x:I,y:q},Ue(t)):{x:I,y:q};if(I=x.x,q=x.y,w){var $;return Object.assign({},g,($={},$[d]=se?"0":"",$[H]=te?"0":"",$.transform=(ne.devicePixelRatio||1)<=1?"translate("+I+"px, "+q+"px)":"translate3d("+I+"px, "+q+"px, 0)",$))}return Object.assign({},g,(s={},s[d]=se?q+"px":"",s[H]=te?I+"px":"",s.transform="",s))}function Ia(e){var s=e.state,t=e.options,n=t.gpuAcceleration,o=n===void 0?!0:n,m=t.adaptive,T=m===void 0?!0:m,_=t.roundOffsets,w=_===void 0?!0:_,C={placement:Ge(s.placement),variation:At(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:o,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,Ts(Object.assign({},C,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:T,roundOffsets:w})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,Ts(Object.assign({},C,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:w})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Ra={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ia,data:{}};var Ht={passive:!0};function Ua(e){var s=e.state,t=e.instance,n=e.options,o=n.scroll,m=o===void 0?!0:o,T=n.resize,_=T===void 0?!0:T,w=Ue(s.elements.popper),C=[].concat(s.scrollParents.reference,s.scrollParents.popper);return m&&C.forEach(function(D){D.addEventListener("scroll",t.update,Ht)}),_&&w.addEventListener("resize",t.update,Ht),function(){m&&C.forEach(function(D){D.removeEventListener("scroll",t.update,Ht)}),_&&w.removeEventListener("resize",t.update,Ht)}}const La={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Ua,data:{}};var Fa={left:"right",right:"left",bottom:"top",top:"bottom"};function qt(e){return e.replace(/left|right|bottom|top/g,function(s){return Fa[s]})}var ja={start:"end",end:"start"};function Cs(e){return e.replace(/start|end/g,function(s){return ja[s]})}function ps(e){var s=Ue(e),t=s.pageXOffset,n=s.pageYOffset;return{scrollLeft:t,scrollTop:n}}function fs(e){return _t(ft(e)).left+ps(e).scrollLeft}function Wa(e,s){var t=Ue(e),n=ft(e),o=t.visualViewport,m=n.clientWidth,T=n.clientHeight,_=0,w=0;if(o){m=o.width,T=o.height;var C=Hs();(C||!C&&s==="fixed")&&(_=o.offsetLeft,w=o.offsetTop)}return{width:m,height:T,x:_+fs(e),y:w}}function Ba(e){var s,t=ft(e),n=ps(e),o=(s=e.ownerDocument)==null?void 0:s.body,m=mt(t.scrollWidth,t.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),T=mt(t.scrollHeight,t.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),_=-n.scrollLeft+fs(e),w=-n.scrollTop;return it(o||t).direction==="rtl"&&(_+=mt(t.clientWidth,o?o.clientWidth:0)-m),{width:m,height:T,x:_,y:w}}function hs(e){var s=it(e),t=s.overflow,n=s.overflowX,o=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+o+n)}function Ks(e){return["html","body","#document"].indexOf(Ze(e))>=0?e.ownerDocument.body:Be(e)&&hs(e)?e:Ks(Xt(e))}function Lt(e,s){var t;s===void 0&&(s=[]);var n=Ks(e),o=n===((t=e.ownerDocument)==null?void 0:t.body),m=Ue(n),T=o?[m].concat(m.visualViewport||[],hs(n)?n:[]):n,_=s.concat(T);return o?_:_.concat(Lt(Xt(T)))}function rs(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Na(e,s){var t=_t(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function ks(e,s,t){return s===Bs?rs(Wa(e,t)):wt(s)?Na(s,t):rs(Ba(ft(e)))}function Va(e){var s=Lt(Xt(e)),t=["absolute","fixed"].indexOf(it(e).position)>=0,n=t&&Be(e)?Nt(e):e;return wt(n)?s.filter(function(o){return wt(o)&&zs(o,n)&&Ze(o)!=="body"}):[]}function Ha(e,s,t,n){var o=s==="clippingParents"?Va(e):[].concat(s),m=[].concat(o,[t]),T=m[0],_=m.reduce(function(w,C){var D=ks(e,C,n);return w.top=mt(D.top,w.top),w.right=Yt(D.right,w.right),w.bottom=Yt(D.bottom,w.bottom),w.left=mt(D.left,w.left),w},ks(e,T,n));return _.width=_.right-_.left,_.height=_.bottom-_.top,_.x=_.left,_.y=_.top,_}function Xs(e){var s=e.reference,t=e.element,n=e.placement,o=n?Ge(n):null,m=n?At(n):null,T=s.x+s.width/2-t.width/2,_=s.y+s.height/2-t.height/2,w;switch(o){case Ae:w={x:T,y:s.y-t.height};break;case Ne:w={x:T,y:s.y+s.height};break;case Ve:w={x:s.x+s.width,y:_};break;case Me:w={x:s.x-t.width,y:_};break;default:w={x:s.x,y:s.y}}var C=o?ds(o):null;if(C!=null){var D=C==="y"?"height":"width";switch(m){case St:w[C]=w[C]-(s[D]/2-t[D]/2);break;case jt:w[C]=w[C]+(s[D]/2-t[D]/2);break}}return w}function Wt(e,s){s===void 0&&(s={});var t=s,n=t.placement,o=n===void 0?e.placement:n,m=t.strategy,T=m===void 0?e.strategy:m,_=t.boundary,w=_===void 0?da:_,C=t.rootBoundary,D=C===void 0?Bs:C,F=t.elementContext,W=F===void 0?Pt:F,I=t.altBoundary,V=I===void 0?!1:I,q=t.padding,B=q===void 0?0:q,te=Js(typeof B!="number"?B:Ys(B,Bt)),se=W===Pt?pa:Pt,H=e.rects.popper,d=e.elements[V?se:W],ne=Ha(wt(d)?d:d.contextElement||ft(e.elements.popper),w,D,T),P=_t(e.elements.reference),ie=Xs({reference:P,element:H,strategy:"absolute",placement:o}),ce=rs(Object.assign({},H,ie)),u=W===Pt?ce:P,b={top:ne.top-u.top+te.top,bottom:u.bottom-ne.bottom+te.bottom,left:ne.left-u.left+te.left,right:u.right-ne.right+te.right},g=e.modifiersData.offset;if(W===Pt&&g){var x=g[o];Object.keys(b).forEach(function($){var M=[Ve,Ne].indexOf($)>=0?1:-1,L=[Ae,Ne].indexOf($)>=0?"y":"x";b[$]+=x[L]*M})}return b}function za(e,s){s===void 0&&(s={});var t=s,n=t.placement,o=t.boundary,m=t.rootBoundary,T=t.padding,_=t.flipVariations,w=t.allowedAutoPlacements,C=w===void 0?Ns:w,D=At(n),F=D?_?ys:ys.filter(function(V){return At(V)===D}):Bt,W=F.filter(function(V){return C.indexOf(V)>=0});W.length===0&&(W=F);var I=W.reduce(function(V,q){return V[q]=Wt(e,{placement:q,boundary:o,rootBoundary:m,padding:T})[Ge(q)],V},{});return Object.keys(I).sort(function(V,q){return I[V]-I[q]})}function qa(e){if(Ge(e)===ls)return[];var s=qt(e);return[Cs(e),s,Cs(s)]}function Ja(e){var s=e.state,t=e.options,n=e.name;if(!s.modifiersData[n]._skip){for(var o=t.mainAxis,m=o===void 0?!0:o,T=t.altAxis,_=T===void 0?!0:T,w=t.fallbackPlacements,C=t.padding,D=t.boundary,F=t.rootBoundary,W=t.altBoundary,I=t.flipVariations,V=I===void 0?!0:I,q=t.allowedAutoPlacements,B=s.options.placement,te=Ge(B),se=te===B,H=w||(se||!V?[qt(B)]:qa(B)),d=[B].concat(H).reduce(function(Te,Ce){return Te.concat(Ge(Ce)===ls?za(s,{placement:Ce,boundary:D,rootBoundary:F,padding:C,flipVariations:V,allowedAutoPlacements:q}):Ce)},[]),ne=s.rects.reference,P=s.rects.popper,ie=new Map,ce=!0,u=d[0],b=0;b<d.length;b++){var g=d[b],x=Ge(g),$=At(g)===St,M=[Ae,Ne].indexOf(x)>=0,L=M?"width":"height",S=Wt(s,{placement:g,boundary:D,rootBoundary:F,altBoundary:W,padding:C}),K=M?$?Ve:Me:$?Ne:Ae;ne[L]>P[L]&&(K=qt(K));var be=qt(K),xe=[];if(m&&xe.push(S[x]<=0),_&&xe.push(S[K]<=0,S[be]<=0),xe.every(function(Te){return Te})){u=g,ce=!1;break}ie.set(g,xe)}if(ce)for(var Se=V?3:1,Le=function(Ce){var _e=d.find(function(Pe){var G=ie.get(Pe);if(G)return G.slice(0,Ce).every(function(Oe){return Oe})});if(_e)return u=_e,"break"},Ee=Se;Ee>0;Ee--){var De=Le(Ee);if(De==="break")break}s.placement!==u&&(s.modifiersData[n]._skip=!0,s.placement=u,s.reset=!0)}}const Ya={name:"flip",enabled:!0,phase:"main",fn:Ja,requiresIfExists:["offset"],data:{_skip:!1}};function $s(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function Ss(e){return[Ae,Ve,Ne,Me].some(function(s){return e[s]>=0})}function Ka(e){var s=e.state,t=e.name,n=s.rects.reference,o=s.rects.popper,m=s.modifiersData.preventOverflow,T=Wt(s,{elementContext:"reference"}),_=Wt(s,{altBoundary:!0}),w=$s(T,n),C=$s(_,o,m),D=Ss(w),F=Ss(C);s.modifiersData[t]={referenceClippingOffsets:w,popperEscapeOffsets:C,isReferenceHidden:D,hasPopperEscaped:F},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":D,"data-popper-escaped":F})}const Xa={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ka};function Ga(e,s,t){var n=Ge(e),o=[Me,Ae].indexOf(n)>=0?-1:1,m=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,T=m[0],_=m[1];return T=T||0,_=(_||0)*o,[Me,Ve].indexOf(n)>=0?{x:_,y:T}:{x:T,y:_}}function Za(e){var s=e.state,t=e.options,n=e.name,o=t.offset,m=o===void 0?[0,0]:o,T=Ns.reduce(function(D,F){return D[F]=Ga(F,s.rects,m),D},{}),_=T[s.placement],w=_.x,C=_.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=w,s.modifiersData.popperOffsets.y+=C),s.modifiersData[n]=T}const Qa={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Za};function er(e){var s=e.state,t=e.name;s.modifiersData[t]=Xs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const tr={name:"popperOffsets",enabled:!0,phase:"read",fn:er,data:{}};function sr(e){return e==="x"?"y":"x"}function nr(e){var s=e.state,t=e.options,n=e.name,o=t.mainAxis,m=o===void 0?!0:o,T=t.altAxis,_=T===void 0?!1:T,w=t.boundary,C=t.rootBoundary,D=t.altBoundary,F=t.padding,W=t.tether,I=W===void 0?!0:W,V=t.tetherOffset,q=V===void 0?0:V,B=Wt(s,{boundary:w,rootBoundary:C,padding:F,altBoundary:D}),te=Ge(s.placement),se=At(s.placement),H=!se,d=ds(te),ne=sr(d),P=s.modifiersData.popperOffsets,ie=s.rects.reference,ce=s.rects.popper,u=typeof q=="function"?q(Object.assign({},s.rects,{placement:s.placement})):q,b=typeof u=="number"?{mainAxis:u,altAxis:u}:Object.assign({mainAxis:0,altAxis:0},u),g=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,x={x:0,y:0};if(P){if(m){var $,M=d==="y"?Ae:Me,L=d==="y"?Ne:Ve,S=d==="y"?"height":"width",K=P[d],be=K+B[M],xe=K-B[L],Se=I?-ce[S]/2:0,Le=se===St?ie[S]:ce[S],Ee=se===St?-ce[S]:-ie[S],De=s.elements.arrow,Te=I&&De?us(De):{width:0,height:0},Ce=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:qs(),_e=Ce[M],Pe=Ce[L],G=Ut(0,ie[S],Te[S]),Oe=H?ie[S]/2-Se-G-_e-b.mainAxis:Le-G-_e-b.mainAxis,$e=H?-ie[S]/2+Se+G+Pe+b.mainAxis:Ee+G+Pe+b.mainAxis,Fe=s.elements.arrow&&Nt(s.elements.arrow),lt=Fe?d==="y"?Fe.clientTop||0:Fe.clientLeft||0:0,ue=($=g==null?void 0:g[d])!=null?$:0,ht=K+Oe-ue-lt,Ye=K+$e-ue,Qe=Ut(I?Yt(be,ht):be,K,I?mt(xe,Ye):xe);P[d]=Qe,x[d]=Qe-K}if(_){var ye,et=d==="x"?Ae:Me,je=d==="x"?Ne:Ve,Ie=P[ne],Re=ne==="y"?"height":"width",tt=Ie+B[et],He=Ie-B[je],st=[Ae,Me].indexOf(te)!==-1,nt=(ye=g==null?void 0:g[ne])!=null?ye:0,at=st?tt:Ie-ie[Re]-ce[Re]-nt+b.altAxis,ct=st?Ie+ie[Re]+ce[Re]-nt-b.altAxis:He,rt=I&&st?Ea(at,Ie,ct):Ut(I?at:tt,Ie,I?ct:He);P[ne]=rt,x[ne]=rt-Ie}s.modifiersData[n]=x}}const ar={name:"preventOverflow",enabled:!0,phase:"main",fn:nr,requiresIfExists:["offset"]};function rr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function or(e){return e===Ue(e)||!Be(e)?ps(e):rr(e)}function ir(e){var s=e.getBoundingClientRect(),t=Et(s.width)/e.offsetWidth||1,n=Et(s.height)/e.offsetHeight||1;return t!==1||n!==1}function lr(e,s,t){t===void 0&&(t=!1);var n=Be(s),o=Be(s)&&ir(s),m=ft(s),T=_t(e,o,t),_={scrollLeft:0,scrollTop:0},w={x:0,y:0};return(n||!n&&!t)&&((Ze(s)!=="body"||hs(m))&&(_=or(s)),Be(s)?(w=_t(s,!0),w.x+=s.clientLeft,w.y+=s.clientTop):m&&(w.x=fs(m))),{x:T.left+_.scrollLeft-w.x,y:T.top+_.scrollTop-w.y,width:T.width,height:T.height}}function cr(e){var s=new Map,t=new Set,n=[];e.forEach(function(m){s.set(m.name,m)});function o(m){t.add(m.name);var T=[].concat(m.requires||[],m.requiresIfExists||[]);T.forEach(function(_){if(!t.has(_)){var w=s.get(_);w&&o(w)}}),n.push(m)}return e.forEach(function(m){t.has(m.name)||o(m)}),n}function ur(e){var s=cr(e);return Ta.reduce(function(t,n){return t.concat(s.filter(function(o){return o.phase===n}))},[])}function dr(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function pr(e){var s=e.reduce(function(t,n){var o=t[n.name];return t[n.name]=o?Object.assign({},o,n,{options:Object.assign({},o.options,n.options),data:Object.assign({},o.data,n.data)}):n,t},{});return Object.keys(s).map(function(t){return s[t]})}var Es={placement:"bottom",modifiers:[],strategy:"absolute"};function _s(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function fr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,n=t===void 0?[]:t,o=s.defaultOptions,m=o===void 0?Es:o;return function(_,w,C){C===void 0&&(C=m);var D={placement:"bottom",orderedModifiers:[],options:Object.assign({},Es,m),modifiersData:{},elements:{reference:_,popper:w},attributes:{},styles:{}},F=[],W=!1,I={state:D,setOptions:function(te){var se=typeof te=="function"?te(D.options):te;q(),D.options=Object.assign({},m,D.options,se),D.scrollParents={reference:wt(_)?Lt(_):_.contextElement?Lt(_.contextElement):[],popper:Lt(w)};var H=ur(pr([].concat(n,D.options.modifiers)));return D.orderedModifiers=H.filter(function(d){return d.enabled}),V(),I.update()},forceUpdate:function(){if(!W){var te=D.elements,se=te.reference,H=te.popper;if(_s(se,H)){D.rects={reference:lr(se,Nt(H),D.options.strategy==="fixed"),popper:us(H)},D.reset=!1,D.placement=D.options.placement,D.orderedModifiers.forEach(function(b){return D.modifiersData[b.name]=Object.assign({},b.data)});for(var d=0;d<D.orderedModifiers.length;d++){if(D.reset===!0){D.reset=!1,d=-1;continue}var ne=D.orderedModifiers[d],P=ne.fn,ie=ne.options,ce=ie===void 0?{}:ie,u=ne.name;typeof P=="function"&&(D=P({state:D,options:ce,name:u,instance:I})||D)}}}},update:dr(function(){return new Promise(function(B){I.forceUpdate(),B(D)})}),destroy:function(){q(),W=!0}};if(!_s(_,w))return I;I.setOptions(C).then(function(B){!W&&C.onFirstUpdate&&C.onFirstUpdate(B)});function V(){D.orderedModifiers.forEach(function(B){var te=B.name,se=B.options,H=se===void 0?{}:se,d=B.effect;if(typeof d=="function"){var ne=d({state:D,name:te,instance:I,options:H}),P=function(){};F.push(ne||P)}})}function q(){F.forEach(function(B){return B()}),F=[]}return I}}var hr=[La,tr,Ra,Vs,Qa,Ya,ar,Da,Xa],vr=fr({defaultModifiers:hr}),gr="tippy-box",Gs="tippy-content",mr="tippy-backdrop",Zs="tippy-arrow",Qs="tippy-svg-arrow",gt={passive:!0,capture:!0},en=function(){return document.body};function es(e,s,t){if(Array.isArray(e)){var n=e[s];return n??(Array.isArray(t)?t[s]:t)}return e}function vs(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function tn(e,s){return typeof e=="function"?e.apply(void 0,s):e}function As(e,s){if(s===0)return e;var t;return function(n){clearTimeout(t),t=setTimeout(function(){e(n)},s)}}function br(e){return e.split(/\s+/).filter(Boolean)}function $t(e){return[].concat(e)}function Ms(e,s){e.indexOf(s)===-1&&e.push(s)}function wr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function yr(e){return e.split("-")[0]}function Kt(e){return[].slice.call(e)}function Ds(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Ft(){return document.createElement("div")}function Gt(e){return["Element","Fragment"].some(function(s){return vs(e,s)})}function xr(e){return vs(e,"NodeList")}function Tr(e){return vs(e,"MouseEvent")}function Cr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function kr(e){return Gt(e)?[e]:xr(e)?Kt(e):Array.isArray(e)?e:Kt(document.querySelectorAll(e))}function ts(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function Ps(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function $r(e){var s,t=$t(e),n=t[0];return n!=null&&(s=n.ownerDocument)!=null&&s.body?n.ownerDocument:document}function Sr(e,s){var t=s.clientX,n=s.clientY;return e.every(function(o){var m=o.popperRect,T=o.popperState,_=o.props,w=_.interactiveBorder,C=yr(T.placement),D=T.modifiersData.offset;if(!D)return!0;var F=C==="bottom"?D.top.y:0,W=C==="top"?D.bottom.y:0,I=C==="right"?D.left.x:0,V=C==="left"?D.right.x:0,q=m.top-n+F>w,B=n-m.bottom-W>w,te=m.left-t+I>w,se=t-m.right-V>w;return q||B||te||se})}function ss(e,s,t){var n=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(o){e[n](o,t)})}function Os(e,s){for(var t=s;t;){var n;if(e.contains(t))return!0;t=t.getRootNode==null||(n=t.getRootNode())==null?void 0:n.host}return!1}var Xe={isTouch:!1},Is=0;function Er(){Xe.isTouch||(Xe.isTouch=!0,window.performance&&document.addEventListener("mousemove",sn))}function sn(){var e=performance.now();e-Is<20&&(Xe.isTouch=!1,document.removeEventListener("mousemove",sn)),Is=e}function _r(){var e=document.activeElement;if(Cr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function Ar(){document.addEventListener("touchstart",Er,gt),window.addEventListener("blur",_r)}var Mr=typeof window<"u"&&typeof document<"u",Dr=Mr?!!window.msCrypto:!1,Pr={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Or={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Je=Object.assign({appendTo:en,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Pr,Or),Ir=Object.keys(Je),Rr=function(s){var t=Object.keys(s);t.forEach(function(n){Je[n]=s[n]})};function nn(e){var s=e.plugins||[],t=s.reduce(function(n,o){var m=o.name,T=o.defaultValue;if(m){var _;n[m]=e[m]!==void 0?e[m]:(_=Je[m])!=null?_:T}return n},{});return Object.assign({},e,t)}function Ur(e,s){var t=s?Object.keys(nn(Object.assign({},Je,{plugins:s}))):Ir,n=t.reduce(function(o,m){var T=(e.getAttribute("data-tippy-"+m)||"").trim();if(!T)return o;if(m==="content")o[m]=T;else try{o[m]=JSON.parse(T)}catch{o[m]=T}return o},{});return n}function Rs(e,s){var t=Object.assign({},s,{content:tn(s.content,[e])},s.ignoreAttributes?{}:Ur(e,s.plugins));return t.aria=Object.assign({},Je.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Lr=function(){return"innerHTML"};function os(e,s){e[Lr()]=s}function Us(e){var s=Ft();return e===!0?s.className=Zs:(s.className=Qs,Gt(e)?s.appendChild(e):os(s,e)),s}function Ls(e,s){Gt(s.content)?(os(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?os(e,s.content):e.textContent=s.content)}function is(e){var s=e.firstElementChild,t=Kt(s.children);return{box:s,content:t.find(function(n){return n.classList.contains(Gs)}),arrow:t.find(function(n){return n.classList.contains(Zs)||n.classList.contains(Qs)}),backdrop:t.find(function(n){return n.classList.contains(mr)})}}function an(e){var s=Ft(),t=Ft();t.className=gr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var n=Ft();n.className=Gs,n.setAttribute("data-state","hidden"),Ls(n,e.props),s.appendChild(t),t.appendChild(n),o(e.props,e.props);function o(m,T){var _=is(s),w=_.box,C=_.content,D=_.arrow;T.theme?w.setAttribute("data-theme",T.theme):w.removeAttribute("data-theme"),typeof T.animation=="string"?w.setAttribute("data-animation",T.animation):w.removeAttribute("data-animation"),T.inertia?w.setAttribute("data-inertia",""):w.removeAttribute("data-inertia"),w.style.maxWidth=typeof T.maxWidth=="number"?T.maxWidth+"px":T.maxWidth,T.role?w.setAttribute("role",T.role):w.removeAttribute("role"),(m.content!==T.content||m.allowHTML!==T.allowHTML)&&Ls(C,e.props),T.arrow?D?m.arrow!==T.arrow&&(w.removeChild(D),w.appendChild(Us(T.arrow))):w.appendChild(Us(T.arrow)):D&&w.removeChild(D)}return{popper:s,onUpdate:o}}an.$$tippy=!0;var Fr=1,zt=[],ns=[];function jr(e,s){var t=Rs(e,Object.assign({},Je,nn(Ds(s)))),n,o,m,T=!1,_=!1,w=!1,C=!1,D,F,W,I=[],V=As(ht,t.interactiveDebounce),q,B=Fr++,te=null,se=wr(t.plugins),H={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},d={id:B,reference:e,popper:Ft(),popperInstance:te,props:t,state:H,plugins:se,clearDelayTimeouts:at,setProps:ct,setContent:rt,show:yt,hide:xt,hideWithInteractivity:Mt,enable:st,disable:nt,unmount:Dt,destroy:ut};if(!t.render)return d;var ne=t.render(d),P=ne.popper,ie=ne.onUpdate;P.setAttribute("data-tippy-root",""),P.id="tippy-"+d.id,d.popper=P,e._tippy=d,P._tippy=d;var ce=se.map(function(y){return y.fn(d)}),u=e.hasAttribute("aria-expanded");return Fe(),Se(),K(),be("onCreate",[d]),t.showOnCreate&&tt(),P.addEventListener("mouseenter",function(){d.props.interactive&&d.state.isVisible&&d.clearDelayTimeouts()}),P.addEventListener("mouseleave",function(){d.props.interactive&&d.props.trigger.indexOf("mouseenter")>=0&&M().addEventListener("mousemove",V)}),d;function b(){var y=d.props.touch;return Array.isArray(y)?y:[y,0]}function g(){return b()[0]==="hold"}function x(){var y;return!!((y=d.props.render)!=null&&y.$$tippy)}function $(){return q||e}function M(){var y=$().parentNode;return y?$r(y):document}function L(){return is(P)}function S(y){return d.state.isMounted&&!d.state.isVisible||Xe.isTouch||D&&D.type==="focus"?0:es(d.props.delay,y?0:1,Je.delay)}function K(y){y===void 0&&(y=!1),P.style.pointerEvents=d.props.interactive&&!y?"":"none",P.style.zIndex=""+d.props.zIndex}function be(y,j,z){if(z===void 0&&(z=!0),ce.forEach(function(re){re[y]&&re[y].apply(re,j)}),z){var le;(le=d.props)[y].apply(le,j)}}function xe(){var y=d.props.aria;if(y.content){var j="aria-"+y.content,z=P.id,le=$t(d.props.triggerTarget||e);le.forEach(function(re){var k=re.getAttribute(j);if(d.state.isVisible)re.setAttribute(j,k?k+" "+z:z);else{var f=k&&k.replace(z,"").trim();f?re.setAttribute(j,f):re.removeAttribute(j)}})}}function Se(){if(!(u||!d.props.aria.expanded)){var y=$t(d.props.triggerTarget||e);y.forEach(function(j){d.props.interactive?j.setAttribute("aria-expanded",d.state.isVisible&&j===$()?"true":"false"):j.removeAttribute("aria-expanded")})}}function Le(){M().removeEventListener("mousemove",V),zt=zt.filter(function(y){return y!==V})}function Ee(y){if(!(Xe.isTouch&&(w||y.type==="mousedown"))){var j=y.composedPath&&y.composedPath()[0]||y.target;if(!(d.props.interactive&&Os(P,j))){if($t(d.props.triggerTarget||e).some(function(z){return Os(z,j)})){if(Xe.isTouch||d.state.isVisible&&d.props.trigger.indexOf("click")>=0)return}else be("onClickOutside",[d,y]);d.props.hideOnClick===!0&&(d.clearDelayTimeouts(),d.hide(),_=!0,setTimeout(function(){_=!1}),d.state.isMounted||_e())}}}function De(){w=!0}function Te(){w=!1}function Ce(){var y=M();y.addEventListener("mousedown",Ee,!0),y.addEventListener("touchend",Ee,gt),y.addEventListener("touchstart",Te,gt),y.addEventListener("touchmove",De,gt)}function _e(){var y=M();y.removeEventListener("mousedown",Ee,!0),y.removeEventListener("touchend",Ee,gt),y.removeEventListener("touchstart",Te,gt),y.removeEventListener("touchmove",De,gt)}function Pe(y,j){Oe(y,function(){!d.state.isVisible&&P.parentNode&&P.parentNode.contains(P)&&j()})}function G(y,j){Oe(y,j)}function Oe(y,j){var z=L().box;function le(re){re.target===z&&(ss(z,"remove",le),j())}if(y===0)return j();ss(z,"remove",F),ss(z,"add",le),F=le}function $e(y,j,z){z===void 0&&(z=!1);var le=$t(d.props.triggerTarget||e);le.forEach(function(re){re.addEventListener(y,j,z),I.push({node:re,eventType:y,handler:j,options:z})})}function Fe(){g()&&($e("touchstart",ue,{passive:!0}),$e("touchend",Ye,{passive:!0})),br(d.props.trigger).forEach(function(y){if(y!=="manual")switch($e(y,ue),y){case"mouseenter":$e("mouseleave",Ye);break;case"focus":$e(Dr?"focusout":"blur",Qe);break;case"focusin":$e("focusout",Qe);break}})}function lt(){I.forEach(function(y){var j=y.node,z=y.eventType,le=y.handler,re=y.options;j.removeEventListener(z,le,re)}),I=[]}function ue(y){var j,z=!1;if(!(!d.state.isEnabled||ye(y)||_)){var le=((j=D)==null?void 0:j.type)==="focus";D=y,q=y.currentTarget,Se(),!d.state.isVisible&&Tr(y)&&zt.forEach(function(re){return re(y)}),y.type==="click"&&(d.props.trigger.indexOf("mouseenter")<0||T)&&d.props.hideOnClick!==!1&&d.state.isVisible?z=!0:tt(y),y.type==="click"&&(T=!z),z&&!le&&He(y)}}function ht(y){var j=y.target,z=$().contains(j)||P.contains(j);if(!(y.type==="mousemove"&&z)){var le=Re().concat(P).map(function(re){var k,f=re._tippy,X=(k=f.popperInstance)==null?void 0:k.state;return X?{popperRect:re.getBoundingClientRect(),popperState:X,props:t}:null}).filter(Boolean);Sr(le,y)&&(Le(),He(y))}}function Ye(y){var j=ye(y)||d.props.trigger.indexOf("click")>=0&&T;if(!j){if(d.props.interactive){d.hideWithInteractivity(y);return}He(y)}}function Qe(y){d.props.trigger.indexOf("focusin")<0&&y.target!==$()||d.props.interactive&&y.relatedTarget&&P.contains(y.relatedTarget)||He(y)}function ye(y){return Xe.isTouch?g()!==y.type.indexOf("touch")>=0:!1}function et(){je();var y=d.props,j=y.popperOptions,z=y.placement,le=y.offset,re=y.getReferenceClientRect,k=y.moveTransition,f=x()?is(P).arrow:null,X=re?{getBoundingClientRect:re,contextElement:re.contextElement||$()}:e,Q={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(we){var U=we.state;if(x()){var a=L(),i=a.box;["placement","reference-hidden","escaped"].forEach(function(l){l==="placement"?i.setAttribute("data-placement",U.placement):U.attributes.popper["data-popper-"+l]?i.setAttribute("data-"+l,""):i.removeAttribute("data-"+l)}),U.attributes.popper={}}}},fe=[{name:"offset",options:{offset:le}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!k}},Q];x()&&f&&fe.push({name:"arrow",options:{element:f,padding:3}}),fe.push.apply(fe,(j==null?void 0:j.modifiers)||[]),d.popperInstance=vr(X,P,Object.assign({},j,{placement:z,onFirstUpdate:W,modifiers:fe}))}function je(){d.popperInstance&&(d.popperInstance.destroy(),d.popperInstance=null)}function Ie(){var y=d.props.appendTo,j,z=$();d.props.interactive&&y===en||y==="parent"?j=z.parentNode:j=tn(y,[z]),j.contains(P)||j.appendChild(P),d.state.isMounted=!0,et()}function Re(){return Kt(P.querySelectorAll("[data-tippy-root]"))}function tt(y){d.clearDelayTimeouts(),y&&be("onTrigger",[d,y]),Ce();var j=S(!0),z=b(),le=z[0],re=z[1];Xe.isTouch&&le==="hold"&&re&&(j=re),j?n=setTimeout(function(){d.show()},j):d.show()}function He(y){if(d.clearDelayTimeouts(),be("onUntrigger",[d,y]),!d.state.isVisible){_e();return}if(!(d.props.trigger.indexOf("mouseenter")>=0&&d.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(y.type)>=0&&T)){var j=S(!1);j?o=setTimeout(function(){d.state.isVisible&&d.hide()},j):m=requestAnimationFrame(function(){d.hide()})}}function st(){d.state.isEnabled=!0}function nt(){d.hide(),d.state.isEnabled=!1}function at(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(m)}function ct(y){if(!d.state.isDestroyed){be("onBeforeUpdate",[d,y]),lt();var j=d.props,z=Rs(e,Object.assign({},j,Ds(y),{ignoreAttributes:!0}));d.props=z,Fe(),j.interactiveDebounce!==z.interactiveDebounce&&(Le(),V=As(ht,z.interactiveDebounce)),j.triggerTarget&&!z.triggerTarget?$t(j.triggerTarget).forEach(function(le){le.removeAttribute("aria-expanded")}):z.triggerTarget&&e.removeAttribute("aria-expanded"),Se(),K(),ie&&ie(j,z),d.popperInstance&&(et(),Re().forEach(function(le){requestAnimationFrame(le._tippy.popperInstance.forceUpdate)})),be("onAfterUpdate",[d,y])}}function rt(y){d.setProps({content:y})}function yt(){var y=d.state.isVisible,j=d.state.isDestroyed,z=!d.state.isEnabled,le=Xe.isTouch&&!d.props.touch,re=es(d.props.duration,0,Je.duration);if(!(y||j||z||le)&&!$().hasAttribute("disabled")&&(be("onShow",[d],!1),d.props.onShow(d)!==!1)){if(d.state.isVisible=!0,x()&&(P.style.visibility="visible"),K(),Ce(),d.state.isMounted||(P.style.transition="none"),x()){var k=L(),f=k.box,X=k.content;ts([f,X],0)}W=function(){var fe;if(!(!d.state.isVisible||C)){if(C=!0,P.offsetHeight,P.style.transition=d.props.moveTransition,x()&&d.props.animation){var de=L(),we=de.box,U=de.content;ts([we,U],re),Ps([we,U],"visible")}xe(),Se(),Ms(ns,d),(fe=d.popperInstance)==null||fe.forceUpdate(),be("onMount",[d]),d.props.animation&&x()&&G(re,function(){d.state.isShown=!0,be("onShown",[d])})}},Ie()}}function xt(){var y=!d.state.isVisible,j=d.state.isDestroyed,z=!d.state.isEnabled,le=es(d.props.duration,1,Je.duration);if(!(y||j||z)&&(be("onHide",[d],!1),d.props.onHide(d)!==!1)){if(d.state.isVisible=!1,d.state.isShown=!1,C=!1,T=!1,x()&&(P.style.visibility="hidden"),Le(),_e(),K(!0),x()){var re=L(),k=re.box,f=re.content;d.props.animation&&(ts([k,f],le),Ps([k,f],"hidden"))}xe(),Se(),d.props.animation?x()&&Pe(le,d.unmount):d.unmount()}}function Mt(y){M().addEventListener("mousemove",V),Ms(zt,V),V(y)}function Dt(){d.state.isVisible&&d.hide(),d.state.isMounted&&(je(),Re().forEach(function(y){y._tippy.unmount()}),P.parentNode&&P.parentNode.removeChild(P),ns=ns.filter(function(y){return y!==d}),d.state.isMounted=!1,be("onHidden",[d]))}function ut(){d.state.isDestroyed||(d.clearDelayTimeouts(),d.unmount(),lt(),delete e._tippy,d.state.isDestroyed=!0,be("onDestroy",[d]))}}function bt(e,s){s===void 0&&(s={});var t=Je.plugins.concat(s.plugins||[]);Ar();var n=Object.assign({},s,{plugins:t}),o=kr(e),m=o.reduce(function(T,_){var w=_&&jr(_,n);return w&&T.push(w),T},[]);return Gt(e)?m[0]:m}bt.defaultProps=Je;bt.setDefaultProps=Rr;bt.currentInput=Xe;Object.assign({},Vs,{effect:function(s){var t=s.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}});bt.setDefaultProps({render:an});const Wr={class:"task-pane"},Br={key:0,class:"loading-overlay"},Nr={key:1,class:"format-error-overlay"},Vr={class:"format-error-content"},Hr={class:"format-error-message"},zr={class:"format-error-actions"},qr={class:"doc-header"},Jr={class:"doc-title"},Yr={class:"action-area"},Kr={class:"select-container"},Xr={class:"select-group"},Gr=["disabled"],Zr=["value"],Qr={class:"select-group"},eo=["disabled"],to=["value"],so=["title"],no={key:0,class:"science-warning"},ao={class:"action-buttons"},ro=["disabled"],oo={class:"btn-content"},io={key:0,class:"button-loader"},lo=["disabled"],co={class:"btn-content"},uo={key:0,class:"button-loader"},po=["disabled"],fo={class:"btn-content"},ho={key:0,class:"button-loader"},vo={class:"content-area"},go={class:"modal-header"},mo={class:"modal-body"},bo={class:"selection-content"},wo={class:"modal-header"},yo={class:"modal-body"},xo={class:"alert-message"},To={class:"alert-actions"},Co={key:2,class:"modal-overlay"},ko={class:"modal-header"},$o={class:"modal-body"},So={class:"confirm-message"},Eo={class:"confirm-actions"},_o={class:"modal-header"},Ao={class:"modal-title"},Mo={class:"modal-body"},Do={class:"alert-message"},Po={class:"alert-actions"},Oo={class:"task-queue"},Io={class:"queue-header"},Ro={class:"queue-status-filter"},Uo=["value"],Lo={class:"queue-actions"},Fo=["disabled","title"],jo={class:"task-count"},Wo={key:0,class:"queue-table-container"},Bo={class:"col-id"},No={class:"id-header"},Vo={key:0,class:"col-subject"},Ho={class:"subject-header"},zo={class:"switch"},qo=["title"],Jo={key:1,class:"col-status"},Yo=["onClick"],Ko={class:"col-id"},Xo={class:"id-content"},Go={class:"task-id"},Zo={key:0,class:"enhance-svg-icon"},Qo={key:0,class:"status-in-id"},ei={key:0,class:"col-subject"},ti=["onMouseenter"],si={key:1,class:"col-status"},ni={class:"status-cell"},ai={class:"col-actions"},ri={class:"task-actions"},oi=["onClick"],ii=["onClick"],li={key:2,class:"no-action-icon",title:"无可用操作"},ci={key:1,class:"empty-queue"},ui={key:4,class:"log-container"},di={class:"log-actions"},pi={class:"toggle-icon"},fi=["innerHTML"],hi={__name:"TaskPane",setup(e){const s=ge(!1),t=ge(!1),n=ge(!1),o=ge(""),m=ge(!1),T=ge(!1),_=ge(!1),w=ge(!1),C=ge(!0),D=ge(""),F=ge(!1),W=ge(window.innerWidth),I=Tt(()=>W.value<750),V=Tt(()=>W.value<380),q=()=>{W.value=window.innerWidth},B=ge(null),te=ge(!1),se=ge(""),H={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},d=ge(!1),ne=ge([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"}]),{docName:P,selected:ie,logger:ce,map:u,subject:b,stage:g,subjectOptions:x,stageOptions:$,appConfig:M,clearLog:L,checkDocumentFormat:S,getTaskStatusClass:K,getTaskStatusText:be,terminateTask:xe,run1:Se,runCheck:Le,setupLifecycle:Ee,navigateToTaskControl:De,isLoading:Te,tryRemoveTaskPlaceholderWithLoading:Ce,confirmDialog:_e,handleConfirm:Pe,errorDialog:G,hideErrorDialog:Oe,getCompletedTasksCount:$e,getReleasableTasksCount:Fe,showConfirm:lt}=Cn(),ue=ge(null);(()=>{var k;try{if((k=window.Application)!=null&&k.PluginStorage){const f=window.Application.PluginStorage.getItem("user_info");f?(ue.value=JSON.parse(f),console.log("用户信息已加载:",ue.value)):console.log("未找到用户信息")}}catch(f){console.error("解析用户信息时出错:",f)}})(),Jt(ue,k=>{k&&k.orgs&&k.orgs[0]&&console.log(`用户企业ID: ${k.orgs[0].orgId}, 校对功能${k.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Ye=Tt(()=>!ue.value||ue.value.isAdmin||ue.value.isOwner?x:ue.value.subject?x.filter(k=>k.value===ue.value.subject):x),Qe=()=>{ue.value&&!ue.value.isAdmin&&!ue.value.isOwner&&ue.value.subject&&(b.value=ue.value.subject)},ye=Tt(()=>["physics","chemistry","biology","math"].includes(b.value));Jt(M,k=>{k&&(console.log("TaskPane组件收到应用配置更新:",k),console.log("当前版本类型:",k.EDITION),console.log("当前年级选项:",$.value))},{deep:!0,immediate:!0});const et=()=>{try{const k=S();C.value=k.isValid,D.value=k.message,F.value=!k.isValid,k.isValid||console.warn("文档格式检查失败:",k.message)}catch(k){console.error("执行文档格式检查时出错:",k),C.value=!1,D.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",F.value=!0}},je=Tt(()=>{let k={};const f=u;if(se.value==="")k={...f};else for(const X in f)if(Object.prototype.hasOwnProperty.call(f,X)){const Q=f[X];Q.status===se.value&&(k[X]=Q)}return te.value&&B.value?k[B.value]?{[B.value]:k[B.value]}:{}:k}),Ie=Tt(()=>{const k=je.value;return Object.entries(k).map(([X,Q])=>({tid:X,...Q})).sort((X,Q)=>{const fe=X.startTime||0;return(Q.startTime||0)-fe}).reduce((X,Q)=>{const{tid:fe,...de}=Q;return X[fe]=de,X},{})}),Re=(k="wps-analysis")=>{b.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(o.value="未选中内容",t.value=!0):(k==="wps-analysis"?T.value=!0:k==="wps-enhance_analysis"&&(_.value=!0),Se(k).catch(f=>{console.log(f),f.message.includes("重叠")?(o.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",f),o.value=f.message,t.value=!0)}).finally(()=>{k==="wps-analysis"?T.value=!1:k==="wps-enhance_analysis"&&(_.value=!1)})):(o.value="请选择学科",t.value=!0)},tt=()=>{b.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(o.value="未选中内容",t.value=!0):(w.value=!0,Le().catch(k=>{console.log(k),k.message.includes("重叠")?(o.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",k),o.value=k.message,t.value=!0)}).finally(()=>{w.value=!1})):(o.value="请选择学科",t.value=!0)},He=(k,f)=>{B.value=k,De(k)},st=k=>{u[k]&&(u[k].status=3),B.value===k&&(B.value=null),Ce(k,!0)},nt=async()=>{const k=Object.entries(u).filter(([f,X])=>X.status===2||X.status===4);if(k.length===0){o.value="没有可释放的任务",t.value=!0;return}try{if(await lt(`确定要释放所有 ${k.length} 个可释放的任务吗？
此操作不可撤销。`)){let X=0;k.forEach(([Q,fe])=>{u[Q]&&(u[Q].status=3,B.value===Q&&(B.value=null),Ce(Q,!0),X++)}),o.value=`已成功释放 ${X} 个任务`,t.value=!0}}catch(f){console.error("释放任务时出错:",f),o.value="释放任务时出现错误",t.value=!0}},at=()=>{m.value=!m.value},ct=k=>k?k.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",rt=k=>{const f=yt(k);return f?ct(f):"无题目内容"},yt=k=>{if(!k.selectedText)return"";const f=k.selectedText.split("\r").filter(Q=>Q.trim());if(f.length===1){const Q=k.selectedText.split(`
`).filter(fe=>fe.trim());Q.length>1&&f.splice(0,1,...Q)}const X=f.map((Q,fe)=>{const de=Q.trim();return de.length>200,de});return X.length===1?f[0].trim():X.join(`
`)},xt=(k,f)=>{if(!d.value)return;const X=k.target,Q=yt(f).toString();if(!Q||Q.trim()===""){console.log("题目内容为空，不显示tooltip");return}const fe=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${Q.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,de=k.clientX,we=k.clientY;if(H.subjects.has(X)){const a=H.subjects.get(X);a.setContent(fe),a.setProps({getReferenceClientRect:()=>({width:0,height:0,top:we,bottom:we,left:de,right:de})}),a.show();return}const U=bt(X,{content:fe,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:we,bottom:we,left:de,right:de}),onHidden:()=>{U.setProps({getReferenceClientRect:null})}});H.subjects.set(X,U),U.show()},Mt=k=>{const f=k.currentTarget,X=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,Q=k.clientX,fe=k.clientY;if(H.enhance.has(f)){const we=H.enhance.get(f);we.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:Q,right:Q})}),we.show();return}const de=bt(f,{content:X,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});H.enhance.set(f,de),de.show()},Dt=()=>{H.subjects.forEach(k=>{k.destroy()}),H.subjects.clear(),H.enhance.forEach(k=>{k.destroy()}),H.enhance.clear(),H.softBreak.forEach(k=>{k.destroy()}),H.softBreak.clear(),document.removeEventListener("click",ut),document.removeEventListener("mousemove",j)},ut=k=>{const f=document.querySelector(".tippy-box");f&&!f.contains(k.target)&&(H.subjects.forEach(X=>X.hide()),H.enhance.forEach(X=>X.hide()),H.softBreak.forEach(X=>X.hide()))};let y=0;const j=k=>{const f=Date.now();if(f-y<100)return;y=f;const X=document.querySelector(".tippy-box");if(!X)return;const Q=X.getBoundingClientRect();!(k.clientX>=Q.left-20&&k.clientX<=Q.right+20&&k.clientY>=Q.top-20&&k.clientY<=Q.bottom+20)&&!X.matches(":hover")&&(H.subjects.forEach(de=>de.hide()),H.enhance.forEach(de=>de.hide()),H.softBreak.forEach(de=>de.hide()))},z=()=>{document.addEventListener("click",ut),document.addEventListener("mousemove",j)};Fs(()=>{window.addEventListener("resize",q),z(),Qe(),setTimeout(()=>{et()},500);const k=document.createElement("style");k.id="tippy-custom-styles",k.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(k)}),un(()=>{window.removeEventListener("resize",q),Dt();const k=document.getElementById("tippy-custom-styles");k&&k.remove()}),Ee();const le=k=>k.selectedText?k.selectedText.includes("\v")||k.selectedText.includes("\v"):!1,re=k=>{const f=k.currentTarget,X=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,Q=k.clientX,fe=k.clientY;if(H.softBreak.has(f)){const we=H.softBreak.get(f);we.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:Q,right:Q})}),we.show();return}const de=bt(f,{content:X,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});H.softBreak.set(f,de),de.show()};return(k,f)=>{var X,Q,fe,de,we;return J(),Y("div",Wr,[me(Te)?(J(),Y("div",Br,f[30]||(f[30]=[p("div",{class:"loading-spinner"},null,-1),p("div",{class:"loading-text"},"处理中...",-1)]))):ae("",!0),F.value?(J(),Y("div",Nr,[p("div",Vr,[f[31]||(f[31]=p("div",{class:"format-error-icon"},"⚠️",-1)),f[32]||(f[32]=p("div",{class:"format-error-title"},"文档格式不支持",-1)),p("div",Hr,pe(D.value),1),p("div",zr,[p("button",{class:"retry-check-btn",onClick:f[0]||(f[0]=U=>et())},"重新检查")])])])):ae("",!0),p("div",qr,[p("div",Jr,pe(me(P)||"未选择文档"),1),p("button",{class:"settings-btn",onClick:f[1]||(f[1]=U=>n.value=!0)},f[33]||(f[33]=[p("i",{class:"icon-settings"},null,-1)]))]),p("div",Yr,[p("div",Kr,[p("div",Xr,[f[34]||(f[34]=p("label",{for:"stage-select"},"年级:",-1)),ze(p("select",{id:"stage-select","onUpdate:modelValue":f[2]||(f[2]=U=>gs(g)?g.value=U:null),class:"select-input",disabled:F.value},[(J(!0),Y(Ot,null,It(me($),U=>(J(),Y("option",{key:U.value,value:U.value},pe(U.label),9,Zr))),128))],8,Gr),[[Qt,me(g)]])]),p("div",Qr,[f[35]||(f[35]=p("label",{for:"subject-select"},"学科:",-1)),ze(p("select",{id:"subject-select","onUpdate:modelValue":f[3]||(f[3]=U=>gs(b)?b.value=U:null),class:"select-input",disabled:F.value},[(J(!0),Y(Ot,null,It(Ye.value,U=>(J(),Y("option",{key:U.value,value:U.value},pe(U.label),9,to))),128))],8,eo),[[Qt,me(b)]]),ue.value&&!ue.value.isAdmin&&!ue.value.isOwner&&ue.value.subject?(J(),Y("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((X=Ye.value.find(U=>U.value===ue.value.subject))==null?void 0:X.label)||ue.value.subject} 学科`}," 🔒 ",8,so)):ae("",!0)])]),ye.value?(J(),Y("div",no," 理科可使用增强模式以获取更精准的解析 ")):ae("",!0),p("div",ao,[p("button",{class:"action-btn primary",onClick:f[4]||(f[4]=U=>Re("wps-analysis")),disabled:T.value||F.value},[p("div",oo,[T.value?(J(),Y("span",io)):ae("",!0),f[36]||(f[36]=p("span",{class:"btn-text"},"解析",-1))])],8,ro),ye.value?(J(),Y("button",{key:0,class:"action-btn enhance",onClick:f[5]||(f[5]=U=>Re("wps-enhance_analysis")),disabled:_.value||F.value},[p("div",co,[_.value?(J(),Y("span",uo)):ae("",!0),f[37]||(f[37]=p("span",{class:"btn-text"},"增强解析",-1))])],8,lo)):ae("",!0),((fe=(Q=ue.value)==null?void 0:Q.orgs[0])==null?void 0:fe.orgId)===2?(J(),Y("button",{key:1,class:"action-btn secondary",onClick:f[6]||(f[6]=U=>tt()),disabled:w.value||F.value},[p("div",fo,[w.value?(J(),Y("span",ho)):ae("",!0),f[38]||(f[38]=p("span",{class:"btn-text"},"校对",-1))])],8,po)):ae("",!0)])]),p("div",vo,[s.value?(J(),Y("div",{key:0,class:"modal-overlay",onClick:f[9]||(f[9]=U=>s.value=!1)},[p("div",{class:"modal-content",onClick:f[8]||(f[8]=pt(()=>{},["stop"]))},[p("div",go,[f[39]||(f[39]=p("div",{class:"modal-title"},"选中内容",-1)),p("button",{class:"modal-close",onClick:f[7]||(f[7]=U=>s.value=!1)},"×")]),p("div",mo,[p("pre",bo,pe(me(ie)||"未选中内容"),1)])])])):ae("",!0),t.value?(J(),Y("div",{key:1,class:"modal-overlay",onClick:f[13]||(f[13]=U=>t.value=!1)},[p("div",{class:"modal-content alert-modal",onClick:f[12]||(f[12]=pt(()=>{},["stop"]))},[p("div",wo,[f[40]||(f[40]=p("div",{class:"modal-title"},"提示",-1)),p("button",{class:"modal-close",onClick:f[10]||(f[10]=U=>t.value=!1)},"×")]),p("div",yo,[p("div",xo,pe(o.value),1),p("div",To,[p("button",{class:"action-btn primary",onClick:f[11]||(f[11]=U=>t.value=!1)},"确定")])])])])):ae("",!0),me(_e).show?(J(),Y("div",Co,[p("div",{class:"modal-content confirm-modal",onClick:f[17]||(f[17]=pt(()=>{},["stop"]))},[p("div",ko,[f[41]||(f[41]=p("div",{class:"modal-title"},"确认",-1)),p("button",{class:"modal-close",onClick:f[14]||(f[14]=U=>me(Pe)(!1))},"×")]),p("div",$o,[p("div",So,pe(me(_e).message),1),p("div",Eo,[p("button",{class:"action-btn secondary",onClick:f[15]||(f[15]=U=>me(Pe)(!1))},"取消"),p("button",{class:"action-btn primary",onClick:f[16]||(f[16]=U=>me(Pe)(!0))},"确定")])])])])):ae("",!0),me(G).show?(J(),Y("div",{key:3,class:"modal-overlay",onClick:f[21]||(f[21]=U=>me(Oe)())},[p("div",{class:"modal-content alert-modal",onClick:f[20]||(f[20]=pt(()=>{},["stop"]))},[p("div",_o,[p("div",Ao,pe(me(G).title),1),p("button",{class:"modal-close",onClick:f[18]||(f[18]=U=>me(Oe)())},"×")]),p("div",Mo,[p("div",Do,pe(me(G).message),1),p("div",Po,[p("button",{class:"action-btn primary",onClick:f[19]||(f[19]=U=>me(Oe)())},"确定")])])])])):ae("",!0),p("div",Oo,[p("div",Io,[f[42]||(f[42]=p("div",{class:"queue-title"},"任务队列",-1)),p("div",Ro,[ze(p("select",{id:"status-filter-select","onUpdate:modelValue":f[22]||(f[22]=U=>se.value=U),class:"status-filter-select-input"},[(J(!0),Y(Ot,null,It(ne.value,U=>(J(),Y("option",{key:U.value,value:U.value},pe(U.label),9,Uo))),128))],512),[[Qt,se.value]])]),p("div",Lo,[p("button",{class:"release-all-btn",onClick:nt,disabled:me(Fe)()===0,title:me(Fe)()===0?"无可释放任务":`释放所有${me(Fe)()}个可释放任务`}," 一键释放 ",8,Fo)]),p("div",jo,pe(Object.keys(je.value).length)+"个任务",1)]),Object.keys(je.value).length>0?(J(),Y("div",Wo,[p("table",{class:We(["queue-table",{"narrow-view":I.value,"ultra-narrow-view":V.value}])},[p("thead",null,[p("tr",null,[p("th",Bo,[p("div",No,[f[44]||(f[44]=p("span",null,"任务ID",-1)),p("span",{class:"help-icon",onMouseenter:f[23]||(f[23]=U=>Mt(U))},f[43]||(f[43]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("circle",{cx:"12",cy:"12",r:"10"}),p("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),p("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),I.value?ae("",!0):(J(),Y("th",Vo,[p("div",Ho,[f[45]||(f[45]=p("span",null,"题目内容",-1)),p("label",zo,[ze(p("input",{type:"checkbox","onUpdate:modelValue":f[24]||(f[24]=U=>d.value=U)},null,512),[[dn,d.value]]),p("span",{class:"slider round",title:d.value?"关闭题目预览":"开启题目预览"},null,8,qo)])])])),V.value?ae("",!0):(J(),Y("th",Jo,"状态")),f[46]||(f[46]=p("th",{class:"col-actions"},"操作",-1))])]),p("tbody",null,[(J(!0),Y(Ot,null,It(Ie.value,(U,a)=>(J(),Y("tr",{key:a,class:We(["task-row",[me(K)(U.status),{"selected-task-row":a===B.value}]]),onClick:i=>He(a)},[p("td",Ko,[p("div",{class:We(["id-cell",{"id-with-status":V.value}])},[p("div",Xo,[p("span",Go,pe(a.substring(0,8)),1),U.wordType==="wps-enhance_analysis"||U.isEnhanced?(J(),Y("span",Zo,f[47]||(f[47]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("title",null,"增强模式"),p("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):ae("",!0),le(U)?(J(),Y("span",{key:1,class:"soft-break-warning-icon",onMouseenter:f[25]||(f[25]=i=>re(i))},f[48]||(f[48]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("title",null,"提示"),p("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),p("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),p("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):ae("",!0)]),V.value?(J(),Y("div",Qo,[p("span",{class:We(["task-tag compact",me(K)(U.status)])},pe(me(be)(U.status)),3)])):ae("",!0)],2)]),I.value?ae("",!0):(J(),Y("td",ei,[p("div",{class:"subject-cell",onMouseenter:i=>xt(i,U)},pe(rt(U)),41,ti)])),V.value?ae("",!0):(J(),Y("td",si,[p("div",ni,[p("span",{class:We(["task-tag",me(K)(U.status)])},pe(me(be)(U.status)),3)])])),p("td",ai,[p("div",ri,[U.status===1?(J(),Y("button",{key:0,onClick:pt(i=>me(xe)(a),["stop"]),class:"terminate-btn"}," 终止 ",8,oi)):ae("",!0),U.status===2||U.status===4?(J(),Y("button",{key:1,onClick:pt(i=>st(a),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,ii)):ae("",!0),U.status!==1&&U.status!==2&&U.status!==4?(J(),Y("span",li,f[49]||(f[49]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[p("circle",{cx:"12",cy:"12",r:"10"}),p("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),p("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):ae("",!0)])])],10,Yo))),128))])],2)])):(J(),Y("div",ci,f[50]||(f[50]=[p("div",{class:"empty-text"},"暂无任务",-1)])))]),((we=(de=ue.value)==null?void 0:de.orgs[0])==null?void 0:we.orgId)===2?(J(),Y("div",ui,[p("div",{class:"log-header",onClick:at},[f[51]||(f[51]=p("div",{class:"log-title"},"执行日志",-1)),p("div",di,[p("button",{class:"clear-btn",onClick:f[26]||(f[26]=pt(U=>me(L)(),["stop"]))},"清空日志"),p("span",pi,pe(m.value?"▼":"▶"),1)])]),m.value?(J(),Y("div",{key:0,class:"log-content",innerHTML:me(ce)},null,8,fi)):ae("",!0)])):ae("",!0)]),n.value?(J(),Y("div",{key:2,class:"modal-overlay",onClick:f[29]||(f[29]=U=>n.value=!1)},[p("div",{class:"modal-content",onClick:f[28]||(f[28]=pt(()=>{},["stop"]))},[pn(ua,{onClose:f[27]||(f[27]=U=>n.value=!1)})])])):ae("",!0)])}}},gi=js(hi,[["__scopeId","data-v-a0516a83"]]);export{gi as default};
