const fs = require('node:fs');
const path = require('node:path');
const Oss = require('ali-oss');
const chalk = require('chalk');


// OSS配置信息
const ossInfo = {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe',
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
    bucket: 'hexin-worksheet',
};

// 初始化OSS客户端
const ossClient = new Oss({
    region: ossInfo.region,
    accessKeyId: ossInfo.accessKeyId,
    accessKeySecret: ossInfo.accessKeySecret,
    bucket: ossInfo.bucket,
    timeout: '120s'
});

// 递归获取目录下所有文件
function getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath).filter(file => file !== 'win-unpacked'); // 过滤掉隐藏文件

    files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        if (fs.statSync(fullPath).isDirectory()) {
            getAllFiles(fullPath, arrayOfFiles);
        } else {
            arrayOfFiles.push(fullPath);
        }
    });

    return arrayOfFiles;
}

// 上传单个版本的函数
async function uploadSingleVersion(distPath, ossPrefix, versionName) {
    if (!fs.existsSync(distPath)) {
        console.log(chalk.yellow(`Warning: ${versionName} dist folder not found: ${distPath}`));
        return 0;
    }

    // 获取所有文件
    const allFiles = getAllFiles(distPath);
    console.log(chalk.blue(`\n${versionName}: Found ${allFiles.length} files to upload`));

    let uploadedCount = 0;
    const totalFiles = allFiles.length;

    for (const filePath of allFiles) {
        const fileContent = fs.readFileSync(filePath);
        // 计算相对路径作为OSS key
        const relativePath = path.relative(distPath, filePath);
        const ossKey = `${ossPrefix}/${relativePath}`.replace(/\\/g, '/');

        try {
            await ossClient.put(ossKey, fileContent);
            uploadedCount++;
            const progress = ((uploadedCount / totalFiles) * 100).toFixed(2);
            const progressBar = '='.repeat(Math.floor(progress / 2)) + '>' + ' '.repeat(50 - Math.floor(progress / 2));
            process.stdout.write(`\r[${versionName}] [${progressBar}] ${progress}% | ${uploadedCount}/${totalFiles} files uploaded`);
        } catch (err) {
            console.error(chalk.red(`\nError uploading ${relativePath}:`), err);
        }
    }

    console.log(chalk.green(`\n${versionName} upload completed successfully!`));
    console.log(chalk.blue(`${versionName} files uploaded: ${uploadedCount}/${totalFiles}`));
    return uploadedCount;
}

async function uploadDistFolder() {
    try {
        console.log(chalk.cyan('Starting multi-version upload process...'));
        
        // 使用项目根目录
        const projectRoot = path.resolve(__dirname, '..');
        
        // 定义版本配置
        const versions = [
            {
                name: '万唯版本',
                distPath: path.join(projectRoot, 'dist'),
                ossPrefix: 'exe/wps-addon'
            },
            {
                name: '合心版本', 
                distPath: path.join(projectRoot, 'dist-hexin'),
                ossPrefix: 'exe/hexin-wps-addon'
            },
            {
                name: '万唯高中版本',
                distPath: path.join(projectRoot, 'dist-wwsenior'),
                ossPrefix: 'exe/wwsenior-wps-addon'
            }
        ];

        let totalUploaded = 0;

        // 逐个上传各版本
        for (const version of versions) {
            const uploaded = await uploadSingleVersion(version.distPath, version.ossPrefix, version.name);
            totalUploaded += uploaded;
        }

        console.log(chalk.green('\n\n=== All versions upload completed! ==='));
        console.log(chalk.blue(`Total files uploaded across all versions: ${totalUploaded}`));
        
        // 显示上传的路径信息
        console.log(chalk.cyan('\nUpload paths:'));
        for (const version of versions) {
            if (fs.existsSync(version.distPath)) {
                console.log(chalk.white(`  ${version.name}: https://dl.hexinedu.com/${version.ossPrefix}/`));
            }
        }
        
    } catch (error) {
        console.error(chalk.red('\nError during upload:'), error);
        process.exit(1);
    }
}

uploadDistFolder();
