diff --git a/node_modules/wpsjs/src/lib/build.js b/node_modules/wpsjs/src/lib/build.js
index 8efe843..bbc6a8f 100644
--- a/node_modules/wpsjs/src/lib/build.js
+++ b/node_modules/wpsjs/src/lib/build.js
@@ -12,33 +12,20 @@ const { serialize } = require('v8');
 const buildDirectory = jsUtil.GetBuildDir()
 
 async function build(cmd) {
-    if (cmd.parent.rawArgs[cmd.parent.rawArgs.length - 1].includes("exe"))
-    {
-        if (false == new RegExp("^[!-~]+$").test(projectCfg.name)){
-            console.log(`${chalk.red("项目名只能是字母、数字和下划线")}...`)
-            return
-	    }
-        buildwithSelfArchive()
-        return
+    if (cmd.parent.rawArgs[cmd.parent.rawArgs.length - 1].includes("exe")) {
+        if (false == new RegExp("^[!-~]+$").test(projectCfg.name)) {
+            console.log(`${chalk.red("项目名只能是字母、数字和下划线")}...`);
+            return;
+        }
+        buildwithSelfArchive();
+        return;
     }
-    inquirer.prompt({
-        name: 'pluginType',
-        type: 'list',
-        message: `选择 WPS 加载项发布类型:`,
-        choices: [{
-                name: '在线插件',
-                value: 'online'
-            },
-            {
-                name: '离线插件',
-                value: 'offline'
-            }
-        ]
-    }).then(answers => {
-        return buildWithArgs(answers)
-    }).then(buildDir=>{
-        console.log(chalk.cyan(`\n==>>  编译成功。请将目录${buildDir}下的文件署到服务器...`))
-    })
+    // 默认使用在线模式，不再提示选择
+    const answers = { pluginType: 'online' };
+    // const answers = { pluginType: 'offline' };
+    buildWithArgs(answers).then(buildDir => {
+        console.log(chalk.cyan(`\n==>>  编译成功。请将目录${buildDir}下的文件署到服务器...`));
+    });
 }
 
 async function buildWithArgs(answers) {
@@ -56,8 +43,8 @@ async function buildWithArgs(answers) {
                 buildDir = require('./buildreact')(answers)
             }
         }
-        let publishRoot = path.resolve(curDir, jsUtil.GetPublishDir());
-        fsEx.removeSync(publishRoot);
+        // let publishRoot = path.resolve(curDir, jsUtil.GetPublishDir());
+        // fsEx.removeSync(publishRoot);
         let buildRoot = path.resolve(curDir, buildDirectory);
         let distPath = buildRoot
         if (answers.pluginType == "offline")
diff --git a/node_modules/wpsjs/src/lib/debug_publish.js b/node_modules/wpsjs/src/lib/debug_publish.js
index 77f1019..5628dc1 100644
--- a/node_modules/wpsjs/src/lib/debug_publish.js
+++ b/node_modules/wpsjs/src/lib/debug_publish.js
@@ -257,13 +257,13 @@ function startWps(){
 			cmd += " " + `/JsApiUserDataDir=${userDataDir}`
 		}
 		if (os.platform() == 'win32') {
-			cp.spawn(cmd, args, { detached: true, stdio: ['ignore'] })
-        } else if (os.platform() == 'darwin') {
-            args = ['-a', `${cmd}`]
-            cmd = 'open'
-			cp.spawn(cmd, args, { detached: true, stdio: ['ignore'] })
-        } else {
-			cp.spawn(cmd, { detached: true, stdio: ['ignore'] })
+			  cp.spawn(cmd, args, { detached: true, stdio: ['ignore'] })
+    } else if (os.platform() == 'darwin') {
+        args = ['-a', `${cmd}`]
+        cmd = 'open'
+			  cp.spawn(cmd, args, { detached: true, stdio: ['ignore'] })
+    } else {
+			  cp.spawn(cmd, { detached: true, stdio: ['ignore'] })
 		}
 	})
 }
diff --git a/node_modules/wpsjs/src/lib/publishlist.json b/node_modules/wpsjs/src/lib/publishlist.json
index be9c108..0967ef4 100644
--- a/node_modules/wpsjs/src/lib/publishlist.json
+++ b/node_modules/wpsjs/src/lib/publishlist.json
@@ -1 +1 @@
-{"test123":{"name":"test123","addonType":"wps","online":"true","multiUser":"false","url":"http://www.test.a/"}}
\ No newline at end of file
+{}
diff --git a/node_modules/wpsjs/src/lib/util.js b/node_modules/wpsjs/src/lib/util.js
index f9b77c2..6000656 100644
--- a/node_modules/wpsjs/src/lib/util.js
+++ b/node_modules/wpsjs/src/lib/util.js
@@ -36,13 +36,13 @@ function getNow() {
 async function GetWebSiteHost(port, cb) {
 	cb = cb || function() {}
 	if (bInited) {
-		cb(`http://127.0.0.1:${netPort}`, netPort)
+		cb(`http://public.hexinedu.com:${netPort}`, netPort)
 	} else {
 		bInited = true
 		port = port || netPort
 		portfinder.basePort = port
 		netPort = await portfinder.getPortPromise()
-		cb(`http://127.0.0.1:${netPort}`, netPort)
+		cb(`http://public.hexinedu.com:${netPort}`, netPort)
 	}
 }
 
