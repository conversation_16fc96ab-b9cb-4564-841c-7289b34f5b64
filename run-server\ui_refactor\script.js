// script.js: Main UI logic and event handling

// Ensure logToUI is available (it's defined in wps-utils.js, which should be loaded first)
if (typeof logToUI === 'undefined') {
  // Fallback if wps-utils.js didn't load or logToUI wasn't global
  window.logToUI = function(message) {
    console.log(`[UI LOG FALLBACK] ${new Date().toLocaleTimeString()}: ${message}`);
  };
  logToUI('Warning: logToUI from wps-utils.js not found, using fallback.');
}


// Immediately log renderer process start
logToUI('UI初始化 (UI Initialization)');

// Check if window.ipcRenderer exists (Electron specific)
if (!window.ipcRenderer) {
  logToUI('错误: ipcRenderer未找到! 桌面应用功能将受限。 (Error: ipcRenderer not found! Desktop app functions will be limited.)');
  // Provide a mock ipcRenderer for environments where it's not available (e.g., browser testing)
  window.ipcRenderer = {
    send: function(channel, data) {
      logToUI(`兼容模式发送消息 (Compatibility mode send message): ${channel}`);
      console.log(`模拟发送 (Simulated send): ${channel}`, data);
    },
    on: function(channel, callback) {
      logToUI(`兼容模式注册监听器 (Compatibility mode register listener): ${channel}`);
      console.log(`模拟注册 (Simulated register): ${channel}`);
      // Example: Simulate 'show-login' event for testing
      if (channel === 'show-login') {
        setTimeout(() => {
          logToUI('兼容模式: 自动触发show-login事件 (Compatibility mode: auto-trigger show-login event)');
          callback({}, {}); // Simulate event and args
        }, 1000);
      }
    },
    sendSync: function(channel, data) {
      logToUI(`兼容模式同步发送消息 (Compatibility mode sync send message): ${channel}`);
      console.log(`模拟同步发送 (Simulated sync send): ${channel}`, data);
      if (channel === 'get-user-info-sync') return { data: { nickname: '测试用户 (Test User)' } };
      return null;
    }
  };
} else {
  logToUI('ipcRenderer已加载 (ipcRenderer loaded)');
}

// Prevent default double-click behavior on title bar (e.g., maximize)
const titleBar = document.querySelector('.title-bar');
if (titleBar) {
  titleBar.addEventListener('dblclick', function(e) {
    e.preventDefault();
    e.stopPropagation();
    return false;
  });
}

// DOM Elements
const loginForm = document.getElementById('login-form');
const statusPanel = document.getElementById('status-panel');
const loadingContainer = document.getElementById('loading-container');
const permissionCheckContainer = document.getElementById('permission-check-container');
const loginButton = document.getElementById('login-button');
const logoutButton = document.getElementById('logout-button');
const errorMessage = document.getElementById('error-message');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const minimizeButton = document.getElementById('minimize-button');
const closeButton = document.getElementById('close-button');
const addonStatus = document.getElementById('addon-status');
const addonInstallButton = document.getElementById('addon-install-button');
const openWpsButton = document.getElementById('open-wps-button');
const userGreeting = document.getElementById('user-greeting');
const disableAllAddonsButton = document.getElementById('disable-all-addons-button');
const checkUpdateButton = document.getElementById('check-update-button');

// Permission check related DOM elements
const permissionProgressFill = document.getElementById('permission-progress-fill');
const permissionProgressPercent = document.getElementById('permission-progress-percent');
const currentPermissionName = document.getElementById('current-permission-name');
const permissionStatusText = document.getElementById('permission-status-text');

// Network folder selection related DOM elements
const networkFolderContainer = document.getElementById('network-folder-container');
const currentWatchDirElement = document.getElementById('current-watch-dir');
const selectNetworkFolderBtn = document.getElementById('select-network-folder-btn');
const confirmNetworkFolderBtn = document.getElementById('confirm-network-folder-btn');
// const skipNetworkFolderBtn = document.getElementById('skip-network-folder-btn'); // Commented out in HTML
const networkFolderMessage = document.getElementById('network-folder-message');

let let_messageTimeout = null; // Timeout ID for temporary messages

/**
 * Shows a temporary message in the error message area.
 * @param {string} message - The message to display.
 * @param {boolean} isSuccess - True if the message indicates success, false for error.
 */
function showTemporaryMessage(message, isSuccess) {
  if (!errorMessage) return;
  clearTimeout(let_messageTimeout);
  errorMessage.textContent = message;
  errorMessage.style.color = isSuccess ? '#28a745' : '#dc3545'; // Green for success, red for error
  errorMessage.style.display = 'block';
  logToUI(`临时消息 (Temporary message): ${message} (成功 (Success): ${isSuccess})`);
  let_messageTimeout = setTimeout(() => {
    errorMessage.textContent = '';
    errorMessage.style.display = 'none';
    errorMessage.style.color = '#dc3545'; // Reset to default error color
  }, 3000); // Message disappears after 3 seconds
}

// Window control button event listeners
minimizeButton?.addEventListener('click', () => {
  logToUI('点击最小化按钮 (Clicked minimize button)');
  window.ipcRenderer?.send('minimize-window');
});
closeButton?.addEventListener('click', () => {
  logToUI('点击关闭按钮 (Clicked close button)');
  window.ipcRenderer?.send('hide-window'); // Or 'close-window' depending on desired behavior
});
checkUpdateButton?.addEventListener('click', () => {
  logToUI('点击检查更新按钮 (Clicked check update button)');
  window.ipcRenderer?.send('check-update');
  showTemporaryMessage('正在检查更新... (Checking for updates...)', true);
});

/**
 * Shows the loading indicator and hides other panels.
 * @param {string} [text='正在检查登录状态...'] - Text to display below the spinner.
 */
function showLoading(text = '正在检查登录状态... (Checking login status...)') {
  logToUI(`显示加载状态 (Show loading state): ${text}`);
  if (loadingContainer) loadingContainer.style.display = 'flex';
  if (loadingContainer) document.querySelector('#loading-container .loading-text').textContent = text;
  if (loginForm) loginForm.style.display = 'none';
  if (statusPanel) statusPanel.style.display = 'none';
  if (permissionCheckContainer) permissionCheckContainer.style.display = 'none';
  if (networkFolderContainer) networkFolderContainer.style.display = 'none';
}

/**
 * Shows the login form and hides other panels.
 */
function showLogin() {
  logToUI('显示登录表单 (Show login form)');
  if (loadingContainer) loadingContainer.style.display = 'none';
  if (loginForm) loginForm.style.display = 'flex';
  if (statusPanel) statusPanel.style.display = 'none';
  if (permissionCheckContainer) permissionCheckContainer.style.display = 'none';
  if (networkFolderContainer) networkFolderContainer.style.display = 'none';
  if (errorMessage) {
    errorMessage.style.display = 'none';
    errorMessage.textContent = '';
  }
}

/**
 * Shows the permission check UI and hides other panels.
 */
function showPermissionCheck() {
  logToUI('显示权限检查界面 (Show permission check UI)');
  if (loadingContainer) loadingContainer.style.display = 'none';
  if (loginForm) loginForm.style.display = 'none';
  if (statusPanel) statusPanel.style.display = 'none';
  if (permissionCheckContainer) permissionCheckContainer.style.display = 'flex';
  if (networkFolderContainer) networkFolderContainer.style.display = 'none';

  // Reset progress bar and text
  if (permissionProgressFill) permissionProgressFill.style.width = '0%';
  if (permissionProgressPercent) permissionProgressPercent.textContent = '0%';
  if (currentPermissionName) currentPermissionName.textContent = '准备检查... (Preparing to check...)';
  if (permissionStatusText) permissionStatusText.textContent = '正在初始化权限检查... (Initializing permission check...)';
}

/**
 * Shows the network folder selection UI.
 * @param {object} data - Data object, potentially containing currentWatchDir.
 */
function showNetworkFolderSelection(data) {
  logToUI('显示网络文件夹选择界面 (Show network folder selection UI)');
  logToUI(`当前界面显示状态 (Current UI display state) - Loading: ${loadingContainer?.style.display}, Login: ${loginForm?.style.display}, Status: ${statusPanel?.style.display}, Permission: ${permissionCheckContainer?.style.display}, Network: ${networkFolderContainer?.style.display}`);

  if (loadingContainer) loadingContainer.style.display = 'none';
  if (loginForm) loginForm.style.display = 'none';
  if (statusPanel) statusPanel.style.display = 'none';
  if (permissionCheckContainer) permissionCheckContainer.style.display = 'none';
  if (networkFolderContainer) networkFolderContainer.style.display = 'flex';

  // Display current watch directory if available
  if (currentWatchDirElement && data && data.currentWatchDir) {
    currentWatchDirElement.textContent = data.currentWatchDir;
  } else if (currentWatchDirElement) {
    currentWatchDirElement.textContent = '未设置 (Not set)';
  }

  // Hide any previous messages
  if (networkFolderMessage) {
    networkFolderMessage.style.display = 'none';
    networkFolderMessage.textContent = '';
  }

  // Reset button states
  if (selectNetworkFolderBtn) {
    selectNetworkFolderBtn.disabled = false;
    selectNetworkFolderBtn.style.display = 'block'; // Ensure it's visible
    selectNetworkFolderBtn.textContent = '选择网络设备 (Select Network Device)';
    // Re-attach event listener if it was changed or removed
    selectNetworkFolderBtn.onclick = selectNetworkFolder;
  }
  if (confirmNetworkFolderBtn) {
    confirmNetworkFolderBtn.disabled = true; // Should be enabled only after selection
    confirmNetworkFolderBtn.style.display = 'none'; // Initially hidden
  }
  // if (skipNetworkFolderBtn) skipNetworkFolderBtn.disabled = false; // If this button is re-enabled
}

/**
 * Displays a message in the network folder selection panel.
 * @param {string} message - The message to show.
 * @param {boolean} [isError=false] - True if it's an error message, for styling.
 */
function showNetworkFolderMessage(message, isError = false) {
  if (networkFolderMessage) {
    networkFolderMessage.textContent = message;
    networkFolderMessage.style.display = 'block';
    networkFolderMessage.style.backgroundColor = isError ? '#f8d7da' : '#d4edda'; // Reddish for error, greenish for success
    networkFolderMessage.style.color = isError ? '#721c24' : '#155724';
    networkFolderMessage.style.borderColor = isError ? '#f5c6cb' : '#c3e6cb'; // Matching border colors
  }
  logToUI(`网络文件夹消息 (Network folder message): ${message} (错误 (Error): ${isError})`);
}

/**
 * Shows the status panel with user information and hides other panels.
 * @param {object} userData - User data, expected to have a 'nickname' property.
 */
function showStatusPanel(userData) {
  logToUI('显示状态面板 (Show status panel)');
  if (loadingContainer) loadingContainer.style.display = 'none';
  if (loginForm) loginForm.style.display = 'none';
  if (statusPanel) statusPanel.style.display = 'flex';
  if (permissionCheckContainer) permissionCheckContainer.style.display = 'none';
  if (networkFolderContainer) networkFolderContainer.style.display = 'none';

  if (userGreeting && userData && userData.nickname) {
    userGreeting.textContent = `你好，${userData.nickname} (Hello, ${userData.nickname})`;
  } else if (userGreeting && window.ipcRenderer) {
    // Fallback: try to get user info synchronously if not provided
    try {
      const userInfoSync = window.ipcRenderer.sendSync('get-user-info-sync');
      if (userInfoSync && userInfoSync.data && userInfoSync.data.nickname) {
        userGreeting.textContent = `你好，${userInfoSync.data.nickname} (Hello, ${userInfoSync.data.nickname})`;
      } else {
        userGreeting.textContent = `你好！ (Hello!)`;
      }
    } catch (e) {
      logToUI('同步获取用户信息失败 (Failed to get user info synchronously): ' + e.message);
      userGreeting.textContent = `你好！ (Hello!)`;
    }
  } else if (userGreeting) {
    userGreeting.textContent = `你好！ (Hello!)`;
  }
  checkAddonStatus(); // Update WPS addon status on showing the panel
}

// Initial state: show loading
showLoading();

// IPC Event Listeners from main process
window.ipcRenderer?.on('show-login', () => {
  logToUI('收到show-login事件 (Received show-login event)');
  showLogin();
});

window.ipcRenderer?.on('login-success', (event, userData) => {
  logToUI('收到login-success事件 (Received login-success event)');
  showStatusPanel(userData || { nickname: '用户 (User)' }); // Provide a default if userData is minimal
});

window.ipcRenderer?.on('login-failure', (event, data) => {
  logToUI(`收到login-failure事件 (Received login-failure event): ${data.message}`);
  if (errorMessage) {
    errorMessage.textContent = data.message || '登录失败，请重试 (Login failed, please try again)';
    errorMessage.style.display = 'block';
  }
  showLogin(); // Show login form again on failure
});

window.ipcRenderer?.on('logout-success', () => {
  logToUI('收到logout-success事件 (Received logout-success event)');
  showLogin();
});

// Permission check related event listeners
window.ipcRenderer?.on('show-permission-check', () => {
  logToUI('收到show-permission-check事件 (Received show-permission-check event)');
  showPermissionCheck();
});

window.ipcRenderer?.on('permission-check-progress', (event, data) => {
  logToUI(`收到permission-check-progress事件 (Received permission-check-progress event): ${JSON.stringify(data)}`);
  if (permissionProgressFill) permissionProgressFill.style.width = `${data.percent}%`;
  if (permissionProgressPercent) permissionProgressPercent.textContent = `${data.percent}%`;
  if (currentPermissionName && data.currentPermission) {
    currentPermissionName.textContent = data.currentPermission.name || '未知权限 (Unknown permission)';
  }
  if (permissionStatusText) {
    permissionStatusText.textContent = `正在检查 (Checking): ${data.current}/${data.total}`;
  }
});

window.ipcRenderer?.on('permission-check-complete', (event, data) => {
  logToUI(`收到permission-check-complete事件 (Received permission-check-complete event): ${JSON.stringify(data)}`);
  if (permissionProgressFill) permissionProgressFill.style.width = '100%';
  if (permissionProgressPercent) permissionProgressPercent.textContent = '100%';
  if (permissionStatusText) {
    permissionStatusText.textContent = data.success ?
      '所有权限检查通过，即将进入应用... (All permissions passed, entering application...)' :
      `权限检查失败 (Permission check failed): ${data.message || '未知错误 (Unknown error)'}`;
  }

  // If successful, briefly show completion then proceed. If failed, show error then login.
  setTimeout(() => {
    if (data.success) {
      logToUI('权限检查通过，切换到加载状态 (Permissions passed, switching to loading state)');
      showLoading('权限检查通过，正在检查登录状态... (Permissions passed, checking login status...)');
      // The main process should then send 'show-login' or 'login-success'
    } else {
      logToUI('权限检查失败，显示登录表单 (Permission check failed, showing login form)');
      showLogin();
      if (errorMessage) {
        errorMessage.textContent = `权限检查失败 (Permission check failed): ${data.message || '未知错误 (Unknown error)'}`;
        errorMessage.style.display = 'block';
      }
    }
  }, 1500); // Delay to allow user to read message
});

window.ipcRenderer?.on('permission-check-failed', (event, data) => { // This might be redundant if 'permission-check-complete' handles failure
  logToUI(`收到permission-check-failed事件 (Received permission-check-failed event): ${JSON.stringify(data)}`);
  if (permissionStatusText) {
    permissionStatusText.textContent = `权限检查失败 (Permission check failed): ${data.message || '未知错误 (Unknown error)'}`;
  }
  setTimeout(() => {
    showLogin();
    if (errorMessage) {
      errorMessage.textContent = `权限检查失败 (Permission check failed): ${data.message || '未知错误 (Unknown error)'}`;
      errorMessage.style.display = 'block';
    }
  }, 1500);
});


// Network folder selection related event listeners
window.ipcRenderer?.on('need-network-folder-selection', (event, data) => {
  logToUI(`收到need-network-folder-selection事件 (Received need-network-folder-selection event): ${JSON.stringify(data)}`);
  if (networkFolderContainer && networkFolderContainer.style.display === 'flex') {
    logToUI('网络文件夹选择界面已经在显示中，忽略重复事件 (Network folder selection UI already visible, ignoring duplicate event)');
    return;
  }
  showNetworkFolderSelection(data);
});

window.ipcRenderer?.on('network-folder-selected', (event, data) => {
  logToUI(`收到network-folder-selected事件 (Received network-folder-selected event): ${JSON.stringify(data)}`);
  if (data.success) {
    const message = data.autoCreated ?
      `网络文件夹已创建成功于 (Network folder created successfully at): ${data.path}` :
      `已选择网络文件夹 (Selected network folder): ${data.path}`;
    showNetworkFolderMessage(message + '，请点击确认按钮继续 (Please click confirm to continue)', false);

    if (selectNetworkFolderBtn) {
      selectNetworkFolderBtn.style.display = 'none'; // Hide original select button
    }
    if (confirmNetworkFolderBtn) {
      confirmNetworkFolderBtn.style.display = 'block'; // Show confirm button
      confirmNetworkFolderBtn.disabled = false;
      confirmNetworkFolderBtn.onclick = () => {
        logToUI(`确认使用网络文件夹 (Confirming network folder): ${data.path}`);
        confirmNetworkFolderBtn.disabled = true;
        confirmNetworkFolderBtn.textContent = '正在配置... (Configuring...)';
        window.ipcRenderer?.send('confirm-network-folder', { path: data.path });
      };
    }
  } else if (data.canceled) {
    showNetworkFolderMessage('未选择网络设备 (No network device selected)', true);
    if (selectNetworkFolderBtn) {
      selectNetworkFolderBtn.disabled = false;
      selectNetworkFolderBtn.textContent = '选择网络设备 (Select Network Device)';
    }
    if (confirmNetworkFolderBtn) {
      confirmNetworkFolderBtn.style.display = 'none';
    }
  } else {
    showNetworkFolderMessage(data.message || '选择网络设备失败 (Failed to select network device)', true);
    if (selectNetworkFolderBtn) {
      selectNetworkFolderBtn.disabled = false;
      selectNetworkFolderBtn.textContent = '选择网络设备 (Select Network Device)';
    }
    if (confirmNetworkFolderBtn) {
      confirmNetworkFolderBtn.style.display = 'none';
    }
  }
});

window.ipcRenderer?.on('network-folder-confirmed', (event, data) => {
  logToUI(`收到network-folder-confirmed事件 (Received network-folder-confirmed event): ${JSON.stringify(data)}`);
  if (data.success) {
    showNetworkFolderMessage('网络文件夹配置成功，继续启动应用... (Network folder configured, continuing app start...)', false);
    // Expect 'permission-check-complete' or similar to follow from main process to continue flow
    // Or directly transition to loading/login check
    setTimeout(() => {
      showLoading('正在检查登录状态... (Checking login status...)');
      // Main process should guide the next step
    }, 1500);
  } else {
    showNetworkFolderMessage(data.message || '配置失败，请重试 (Configuration failed, please retry)', true);
    // Reset buttons to allow re-selection
    if (selectNetworkFolderBtn) {
      selectNetworkFolderBtn.disabled = false;
      selectNetworkFolderBtn.textContent = '选择网络设备 (Select Network Device)';
      selectNetworkFolderBtn.style.display = 'block';
      selectNetworkFolderBtn.onclick = selectNetworkFolder; // Re-attach original handler
    }
    if (confirmNetworkFolderBtn) {
      confirmNetworkFolderBtn.style.display = 'none';
      confirmNetworkFolderBtn.disabled = true;
    }
  }
});


// Timeout to prevent indefinite loading screen
const loadingTimeout = setTimeout(() => {
  if (loadingContainer && loadingContainer.style.display === 'flex') {
    logToUI('加载状态超时，请检查主进程通讯 (Loading state timeout, check main process communication)');
    // Potentially show an error or fallback to login, but this might interfere with normal flow
    // showLogin();
    // showTemporaryMessage('加载超时，请重试或联系支持。(Loading timed out, please retry or contact support.)', false);
  }
}, 15000); // Increased timeout to 15 seconds

// Login form submission
loginButton?.addEventListener('click', () => {
  const username = usernameInput.value.trim();
  const password = passwordInput.value.trim();
  if (!username || !password) {
    if (errorMessage) {
      errorMessage.textContent = '请输入用户名和密码 (Please enter username and password)';
      errorMessage.style.display = 'block';
    }
    return;
  }
  if (errorMessage) errorMessage.style.display = 'none';
  logToUI(`尝试登录 (Attempting login): ${username}`);
  showLoading('正在登录... (Logging in...)');
  window.ipcRenderer?.send('login-attempt', { username, password });
  if (passwordInput) passwordInput.value = ''; // Clear password field
});

// Logout button
logoutButton?.addEventListener('click', () => {
  logToUI('退出登录 (Logout)');
  window.ipcRenderer?.send('logout');
});

// Allow login on Enter key in password field
passwordInput?.addEventListener('keyup', (event) => {
  if (event.key === 'Enter') {
    loginButton?.click();
  }
});

// WPS Addon related global variables (can be encapsulated if preferred)
var serverId; // Will be initialized by getServerId()
var serverVersion = 'wait'; // Initial status, updated by checkAddonStatus
var addonInstalled = false; // Tracks if the specific addon is installed

/**
 * Checks the status of the WPS addon.
 * Fetches WPS server version and then the list of published addons.
 */
function checkAddonStatus() {
  logToUI('检查WPS插件状态... (Checking WPS plugin status...)');
  if (addonInstallButton) {
    addonInstallButton.disabled = true;
    addonInstallButton.textContent = '正在检查... (Checking...)';
  }

  function fetchPublishListAndUpdate() {
    var baseData = JSON.stringify({ serverId: getServerId() }); // Ensure serverId is current
    var listReq = { url: 'http://127.0.0.1:58890/publishlist', type: 'POST' };
    logToUI('请求插件列表 /publishlist (Requesting plugin list /publishlist)');
    startWps(listReq, baseData, function(listRes) { // startWps is from wps-utils.js
      if (listRes.status === 0 && listRes.res && listRes.res.target) {
        try {
          var responseText = listRes.res.target.response;
          logToUI('插件列表响应 (Plugin list response): ' + responseText.substring(0, 200));
          var installedAddons = JSON.parse(responseText);
          // Check if our specific addon is in the list and enabled
          addonInstalled = installedAddons.some(function(addon) {
            return addon.name === '万唯AI编辑WPS插件服务' &&
              addon.url.includes('wps-addon-build') && // Verify URL part
              addon.enable !== 'false'; // Check if not explicitly disabled
          });
          updateAddonUI(addonInstalled);
        } catch (e) {
          logToUI('解析publishlist失败 (Failed to parse publishlist): ' + e.message + '. Response: ' + responseText);
          updateAddonUI(false); // Assume not installed on parse error
        }
      } else {
        logToUI('获取publishlist失败 (Failed to get publishlist): ' + (listRes.message || '未知错误 (Unknown error)'));
        updateAddonUI(false); // Assume not installed on request error
      }
      if (addonInstallButton) {
        addonInstallButton.disabled = false;
        addonInstallButton.textContent = addonInstalled ? '卸载插件 (Uninstall Plugin)' : '安装插件 (Install Plugin)';
      }
    });
  }

  // First, get WPS server version if not already known or if previous attempt failed
  if (serverVersion === 'wait' || serverVersion === 'error') {
    var versionReq = { url: 'http://127.0.0.1:58890/version', type: 'POST' };
    logToUI('请求WPS服务版本 /version (Requesting WPS server version /version)');
    startWps(versionReq, JSON.stringify({ serverId: getServerId() }), function(versionRes) {
      if (versionRes.status === 0 && versionRes.res && versionRes.res.target && versionRes.res.target.response) {
        serverVersion = versionRes.res.target.response;
        logToUI('WPS Server Version: ' + serverVersion);
        fetchPublishListAndUpdate(); // Now get addon list
      } else {
        logToUI('获取WPS Server Version失败 (Failed to get WPS Server Version): ' + (versionRes.message || '未知错误 (Unknown error)'));
        serverVersion = 'error'; // Mark as error to avoid retrying version check immediately
        updateAddonUI(false);
        if (addonInstallButton) {
          addonInstallButton.disabled = false;
          addonInstallButton.textContent = '安装插件 (Install Plugin)';
        }
        showTemporaryMessage('无法连接到WPS服务，请确保WPS已运行或尝试重启WPS。 (Cannot connect to WPS service, ensure WPS is running or try restarting WPS.)', false);
      }
    });
  } else {
    // serverVersion is already known and not 'error', directly fetch addon list
    fetchPublishListAndUpdate();
  }
}

/**
 * Updates the UI elements related to the addon status.
 * @param {boolean} installed - True if the addon is considered installed.
 */
function updateAddonUI(installed) {
  addonInstalled = installed; // Update global state
  logToUI(`更新插件UI状态 (Update plugin UI state): ${installed ? '已安装 (Installed)' : '未安装 (Not Installed)'}`);
  if (addonStatus) {
    addonStatus.textContent = installed ? '已安装 (Installed)' : '未安装 (Not Installed)';
    addonStatus.className = installed ? 'wps-addon-status status-installed' : 'wps-addon-status status-not-installed';
  }
  if (addonInstallButton) {
    addonInstallButton.textContent = installed ? '卸载插件 (Uninstall Plugin)' : '安装插件 (Install Plugin)';
  }
}

/**
 * Installs or uninstalls the WPS addon based on its current state.
 */
function installAddon() {
  logToUI(`尝试 ${addonInstalled ? '卸载 (uninstall)' : '安装 (install)'} 插件 (plugin)`);
  if (serverVersion === 'wait') {
    showTemporaryMessage('WPS服务版本信息尚未获取，请稍后再试。 (WPS service version info not yet available, please try again later.)', false);
    logToUI('安装/卸载插件失败 (Install/Uninstall plugin failed): serverVersion 未就绪 (not ready)');
    return;
  }
  if (serverVersion === 'error') {
    showTemporaryMessage('无法连接到WPS服务，操作失败。请检查WPS是否运行。 (Cannot connect to WPS service, operation failed. Check if WPS is running.)', false);
    logToUI('安装/卸载插件失败 (Install/Uninstall plugin failed): serverVersion 获取错误 (fetch error)');
    return;
  }

  var addonInfo = {
    'name': '万唯AI编辑WPS插件服务',
    'addonType': 'wps', // Type of addon
    'online': 'true',   // Indicates if it's an online addon
    'multiUser': 'false', // If addon supports multiple users
    'url': 'http://wwwps.hexinedu.com/wps-addon-build/' // URL of the addon
  };
  var cmd = addonInstalled ? 'disable' : 'enable'; // Command to send
  var data = FormartData(addonInfo, cmd); // FormartData is from wps-utils.js
  var req = { url: 'http://127.0.0.1:58890/deployaddons/runParams', type: 'POST' };

  logToUI(`发送 ${cmd} 命令到 /deployaddons/runParams (Sending ${cmd} command to /deployaddons/runParams)`);
  if (addonInstallButton) addonInstallButton.disabled = true; // Disable button during operation

  startWps(req, data, function(res) { // startWps is from wps-utils.js
    if (addonInstallButton) addonInstallButton.disabled = false; // Re-enable button
    if (res.status === 0 && res.res && res.res.target) {
      // Successful request, check response content
      if (res.res.target.response === 'OK' || (res.res.target.response === '' && res.res.target.status === 200) ) {
        var successfullyChanged = !addonInstalled; // If it was 'disable', it's now uninstalled (false). If 'enable', it's now installed (true).
        updateAddonUI(successfullyChanged);
        showTemporaryMessage(successfullyChanged ? '插件安装成功！ (Plugin installed successfully!)' : '插件卸载成功！ (Plugin uninstalled successfully!)', true);
      } else {
        logToUI(`插件操作失败，响应 (Plugin operation failed, response): ${res.res.target.response}`);
        showTemporaryMessage((addonInstalled ? '插件卸载失败！ (Plugin uninstall failed!)' : '插件安装失败！ (Plugin install failed!)') + '原因 (Reason): ' + res.res.target.response, false);
      }
    } else {
      // Request itself failed (network error, WPS service down, etc.)
      logToUI(`插件操作请求失败 (Plugin operation request failed): ${res.message}`);
      showTemporaryMessage(res.message || (addonInstalled ? '插件卸载请求失败！ (Plugin uninstall request failed!)' : '插件安装请求失败！ (Plugin install request failed!)'), false);
    }
  });
}

/**
 * Disables all WPS addons. Requires user confirmation.
 */
function disableAllAddons() {
  logToUI('尝试禁用所有插件 (Attempting to disable all plugins)');
  // Using a custom modal for confirmation is preferred over window.confirm in Electron apps.
  // For this refactor, we'll keep the logic but note that UI should ideally use a modal.
  // const confirmed = confirm('确定要禁用所有WPS加载项吗？此操作无法单独撤销，需要重新安装特定插件。 (Are you sure you want to disable all WPS add-ins? This action cannot be undone individually and requires reinstalling specific plugins.)');
  // if (!confirmed) {
  //   logToUI('用户取消了禁用所有插件的操作。 (User cancelled disabling all plugins.)');
  //   return;
  // }
  // Since confirm() is problematic in Electron renderers or sandboxed environments,
  // we'll proceed as if confirmed for now, or this function should be triggered by a modal.
  // For a real app, replace with a custom dialog.
  logToUI('模拟确认：禁用所有插件 (Simulated confirmation: Disable all plugins). 主应用应使用模态对话框 (Main app should use a modal dialog).');


  if (serverVersion === 'wait') {
    showTemporaryMessage('WPS服务版本信息尚未获取，请稍后再试。 (WPS service version info not yet available, please try again later.)', false);
    return;
  }
  if (serverVersion === 'error') {
    showTemporaryMessage('无法连接到WPS服务，操作失败。请检查WPS是否运行。 (Cannot connect to WPS service, operation failed. Check if WPS is running.)', false);
    return;
  }

  var element = {}; // For 'disableall', name/url are not needed for this specific command structure
  var cmd = 'disableall';
  var data = FormartData(element, cmd); // FormartData handles empty element for this cmd
  var req = { url: 'http://127.0.0.1:58890/deployaddons/runParams', type: 'POST' };
  logToUI(`发送 ${cmd} 命令到 /deployaddons/runParams (Sending ${cmd} command to /deployaddons/runParams)`);

  if (disableAllAddonsButton) disableAllAddonsButton.disabled = true;

  startWps(req, data, function(res) {
    if (disableAllAddonsButton) disableAllAddonsButton.disabled = false;
    if (res.status === 0 && res.res && res.res.target) {
      if (res.res.target.response === 'OK' || (res.res.target.response === '' && res.res.target.status === 200)) {
        showTemporaryMessage('所有WPS加载项已成功禁用！ (All WPS add-ins disabled successfully!)', true);
        logToUI('所有插件已禁用，重新检查当前插件状态。 (All plugins disabled, re-checking current plugin status.)');
        checkAddonStatus(); // Re-check and update UI for the specific addon
      } else {
        logToUI(`禁用所有插件失败，响应 (Disable all plugins failed, response): ${res.res.target.response}`);
        showTemporaryMessage('禁用所有插件失败！原因 (Disable all plugins failed! Reason): ' + res.res.target.response, false);
      }
    } else {
      logToUI(`禁用所有插件请求失败 (Disable all plugins request failed): ${res.message}`);
      showTemporaryMessage(res.message || '禁用所有插件的请求失败！ (Request to disable all plugins failed!)', false);
    }
  });
}

/**
 * Attempts to open the WPS application via an IPC message to the main process.
 */
function openWps() {
  logToUI('尝试打开WPS (Attempting to open WPS)');
  showTemporaryMessage('正在尝试打开WPS... (Attempting to open WPS...)', true);
  window.ipcRenderer?.send('start-wps'); // Ask main process to start WPS
}


// Event listeners for addon buttons
addonInstallButton?.addEventListener('click', installAddon);
openWpsButton?.addEventListener('click', openWps);
disableAllAddonsButton?.addEventListener('click', () => {
  // It's better to show a custom modal here.
  // For now, we'll add a placeholder for that confirmation.
  logToUI(" '禁用所有插件' 按钮被点击。生产环境应显示确认对话框。('Disable All Plugins' button clicked. Production should show a confirmation dialog.)");
  // Example: if (await showConfirmationModal('Are you sure...?')) { disableAllAddons(); }
  // For this refactor, we'll call it directly if you uncomment the next line, but it's not ideal without real confirmation.
  // disableAllAddons();
  showTemporaryMessage("请在代码中实现一个确认对话框来调用 disableAllAddons()。(Please implement a confirmation dialog in the code to call disableAllAddons())", false);
});


// Network folder selection functionality
function selectNetworkFolder() {
  logToUI('用户点击选择网络设备按钮 (User clicked select network device button)');
  try {
    if (selectNetworkFolderBtn) {
      selectNetworkFolderBtn.disabled = true;
      selectNetworkFolderBtn.textContent = '正在打开选择对话框... (Opening selection dialog...)';
    }
    window.ipcRenderer?.send('select-network-folder');
  } catch (error) {
    logToUI(`selectNetworkFolder 函数执行错误 (function execution error): ${error.message}`);
    if (selectNetworkFolderBtn) { // Restore button state on error
      selectNetworkFolderBtn.disabled = false;
      selectNetworkFolderBtn.textContent = '选择网络设备 (Select Network Device)';
    }
    showNetworkFolderMessage('操作失败，请重试 (Operation failed, please retry)', true);
  }
}

// Event listener for network folder selection button
selectNetworkFolderBtn?.addEventListener('click', selectNetworkFolder);
// skipNetworkFolderBtn was commented out in HTML, so no listener added here.

// Listen for app version from main process
window.ipcRenderer?.on('app-version', (event, version) => {
  const versionElement = document.getElementById('version-number');
  if (versionElement) {
    versionElement.textContent = `V${version}`;
  }
  logToUI(`应用版本号 (App version): ${version}`);
});

// Initialize serverId (used by WPS functions)
serverId = getServerId(); // getServerId is from wps-utils.js
logToUI('serverId 初始化为 (initialized to): ' + serverId);

// Developer tools shortcut (F12 or Ctrl+Shift+I)
document.addEventListener('keydown', (e) => {
  if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'i'))) {
    logToUI('检测到开发者工具快捷键 (Detected dev tools shortcut)');
    window.ipcRenderer?.send('open-devtools');
  }
});

// Cleanup timeouts on window unload
window.addEventListener('beforeunload', () => {
  clearTimeout(loadingTimeout);
  clearTimeout(let_messageTimeout);
});
