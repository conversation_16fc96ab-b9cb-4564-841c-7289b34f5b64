appId: com.wanwei.wps-addon-server-senior
productName: 万唯高中AI编辑WPS插件服务
directories:
  output: dist-wwsenior
  buildResources: assets
files:
  - "**/*"
  - "!data/**"
  - "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}"
  - "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}"
  - "!**/node_modules/.bin"
  - "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}"
  - "!**/node_modules/**/{.editorconfig,.eslintrc,typings.json,tsconfig.json,webpack.config.js}"
  - "!**/node_modules/**/{appveyor.yml,.travis.yml,circle.yml}"
  - "!**/node_modules/**/LICENSE*"
  - "!**/node_modules/**/license*"
  - "!**/node_modules/**/NOTICE*"
  - "!**/node_modules/**/doc/**"
  - "!**/node_modules/**/docs/**"
  - "!**/node_modules/**/man/**"
  - "!dist-hexin/**/*"
  - "!dist/**/*"
extraResources:
  - from: "assets"
    to: "assets"
    filter: ["**/*"]
  - from: "ui"
    to: "ui"
    filter: ["**/*"]
asar: true
compression: maximum
# 设置环境变量
buildDependenciesFromSource: true
electronDownload:
  cache: ~/.electron-cache
afterPack: "./scripts/verify-resources.js"
win:
  target: nsis
  icon: assets/ww.png
  # 添加环境变量
  extraResources:
    - from: "env-config/wwsenior.json"
      to: "env-config.json"
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  differentialPackage: false
  artifactName: "${productName}-Setup-${version}.${ext}"
mac: null # 禁用macOS构建选项
dmg: null
mas: null
linux: null # 如果不需要Linux构建，也可以禁用
# 发布配置 (自动更新)
publish:
  - provider: generic
    url: https://dl.hexinedu.com/exe/wps-addon/
