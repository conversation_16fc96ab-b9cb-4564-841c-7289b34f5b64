const fs = require('fs');
const path = require('path');

/**
 * 生成更新日志的脚本
 * 用于在构建过程中自动生成 latest.yml 中的 releaseNotes
 */

function generateReleaseNotes(version, edition = 'wanwei') {
    // 可以从多种来源获取更新日志：
    // 1. 从 CHANGELOG.md 文件
    // 2. 从 git commit 历史
    // 3. 从预定义的模板
    // 4. 从外部API或配置文件

    const defaultReleaseNotes = `## v${version} 更新内容

**新功能：**
- 优化了用户界面体验
- 增强了系统稳定性
- 改进了文件处理效率

**修复：**
- 修复了已知的问题和漏洞
- 优化了内存使用
- 改善了错误处理机制

**改进：**
- 提升了整体性能
- 更新了用户帮助文档
- 增强了兼容性支持

如有问题请联系技术支持。`;

    return defaultReleaseNotes;
}

function updateLatestYml(distPath, version, edition) {
    const latestYmlPath = path.join(distPath, 'latest.yml');
    
    if (!fs.existsSync(latestYmlPath)) {
        console.log(`警告: ${latestYmlPath} 文件不存在`);
        return;
    }

    try {
        let content = fs.readFileSync(latestYmlPath, 'utf8');
        const releaseNotes = generateReleaseNotes(version, edition);
        
        // 如果已存在 releaseNotes，则替换；否则添加
        if (content.includes('releaseNotes:')) {
            // 替换现有的 releaseNotes
            content = content.replace(
                /releaseNotes:[\s\S]*?(?=\n[a-zA-Z]|\n$|$)/,
                `releaseNotes: |\n  ${releaseNotes.split('\n').join('\n  ')}`
            );
        } else {
            // 添加 releaseNotes 到文件末尾
            content = content.trim() + `\nreleaseNotes: |\n  ${releaseNotes.split('\n').join('\n  ')}\n`;
        }

        fs.writeFileSync(latestYmlPath, content);
        console.log(`✅ 已更新 ${edition} 版本的更新日志到 ${latestYmlPath}`);
        
    } catch (error) {
        console.error(`❌ 更新 ${latestYmlPath} 时发生错误:`, error.message);
    }
}

// 从外部文件读取更新日志的函数
function loadReleaseNotesFromFile(version, edition) {
    const releaseNotesPath = path.join(__dirname, '..', 'release-notes', `${version}.md`);
    
    if (fs.existsSync(releaseNotesPath)) {
        return fs.readFileSync(releaseNotesPath, 'utf8');
    }
    
    // 如果没有找到对应版本的文件，返回默认内容
    return generateReleaseNotes(version, edition);
}

// 主函数
function main() {
    const version = process.argv[2] || require('../package.json').version;
    const editions = [
        { name: '万唯版本', distPath: path.join(__dirname, '..', 'dist'), edition: 'wanwei' },
        { name: '合心版本', distPath: path.join(__dirname, '..', 'dist-hexin'), edition: 'hexin' },
        { name: '万唯高中版本', distPath: path.join(__dirname, '..', 'dist-wwsenior'), edition: 'wwsenior' }
    ];

    console.log(`🚀 开始为版本 ${version} 生成更新日志...`);

    editions.forEach(({ name, distPath, edition }) => {
        if (fs.existsSync(distPath)) {
            console.log(`📝 处理 ${name}...`);
            updateLatestYml(distPath, version, edition);
        } else {
            console.log(`⚠️  跳过 ${name}，目录不存在: ${distPath}`);
        }
    });

    console.log('✨ 更新日志生成完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    generateReleaseNotes,
    updateLatestYml,
    loadReleaseNotesFromFile
}; 