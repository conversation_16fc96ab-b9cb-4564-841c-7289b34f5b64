// Simple logger service for capturing and displaying logs
const MAX_LOGS = 100;

class Logger {
  constructor() {
    this.logs = [];
    this.listeners = [];
  }

  log(message, type = 'info', data = null) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      message,
      type, // info, warning, error, success
      data: data ? JSON.stringify(data) : null
    };
    
    console.log(`${type.toUpperCase()}: ${message}`, data || '');
    
    this.logs.unshift(logEntry); // Add to start of array for newest first
    
    // Keep logs under the maximum limit
    if (this.logs.length > MAX_LOGS) {
      this.logs.pop();
    }
    
    // Notify listeners
    this.notifyListeners();
  }
  
  info(message, data = null) {
    this.log(message, 'info', data);
  }
  
  warn(message, data = null) {
    this.log(message, 'warning', data);
  }
  
  error(message, data = null) {
    this.log(message, 'error', data);
  }
  
  success(message, data = null) {
    this.log(message, 'success', data);
  }
  
  clear() {
    this.logs = [];
    this.notifyListeners();
  }
  
  getLogs() {
    return [...this.logs];
  }
  
  addListener(callback) {
    this.listeners.push(callback);
  }
  
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }
  
  notifyListeners() {
    this.listeners.forEach(listener => listener(this.logs));
  }
}

// Create singleton instance
const logger = new Logger();

export default logger; 