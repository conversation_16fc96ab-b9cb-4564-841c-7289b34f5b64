<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本信息测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .version-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 3px;
        }
        .log-entry.info {
            color: #0066cc;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.version {
            color: #6f42c1;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket 版本信息测试</h1>
        
        <!-- 连接状态 -->
        <div class="section">
            <h3>连接状态</h3>
            <div id="connectionStatus" class="status disconnected">未连接</div>
            <button id="connectBtn" class="btn btn-primary">连接 WebSocket</button>
            <button id="disconnectBtn" class="btn btn-secondary" disabled>断开连接</button>
        </div>

        <!-- 版本信息显示 -->
        <div class="section">
            <h3>当前版本信息</h3>
            <div id="versionInfo" class="version-info">等待获取版本信息...</div>
            <button id="requestVersionBtn" class="btn btn-primary" disabled>请求版本信息</button>
        </div>

        <!-- 版本切换测试 -->
        <div class="section">
            <h3>版本切换测试</h3>
            <div style="margin-bottom: 15px;">
                <label for="editionSelect">选择版本：</label>
                <select id="editionSelect">
                    <option value="wanwei">万唯版本</option>
                    <option value="hexin">合心版本</option>
                </select>
                <button id="switchEditionBtn" class="btn btn-success" disabled>切换版本</button>
            </div>
            <div id="switchResult"></div>
        </div>

        <!-- 消息日志 -->
        <div class="section">
            <h3>消息日志</h3>
            <button id="clearLogBtn" class="btn btn-secondary">清空日志</button>
            <div id="messageLog" class="log"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageId = 0;

        const elements = {
            connectionStatus: document.getElementById('connectionStatus'),
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            versionInfo: document.getElementById('versionInfo'),
            requestVersionBtn: document.getElementById('requestVersionBtn'),
            editionSelect: document.getElementById('editionSelect'),
            switchEditionBtn: document.getElementById('switchEditionBtn'),
            switchResult: document.getElementById('switchResult'),
            messageLog: document.getElementById('messageLog'),
            clearLogBtn: document.getElementById('clearLogBtn')
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            elements.messageLog.appendChild(entry);
            elements.messageLog.scrollTop = elements.messageLog.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            if (connected) {
                elements.connectionStatus.textContent = '已连接';
                elements.connectionStatus.className = 'status connected';
                elements.connectBtn.disabled = true;
                elements.disconnectBtn.disabled = false;
                elements.requestVersionBtn.disabled = false;
                elements.switchEditionBtn.disabled = false;
            } else {
                elements.connectionStatus.textContent = '未连接';
                elements.connectionStatus.className = 'status disconnected';
                elements.connectBtn.disabled = false;
                elements.disconnectBtn.disabled = true;
                elements.requestVersionBtn.disabled = true;
                elements.switchEditionBtn.disabled = true;
            }
        }

        function updateVersionInfo(versionData) {
            const info = `
版本类型: ${versionData.edition}
应用版本: ${versionData.appVersion}
构建日期: ${versionData.buildDate}
运行环境: ${versionData.environment}
绕过加密: ${versionData.bypassEncryption ? '是' : '否'}
            `.trim();
            elements.versionInfo.textContent = info;
            elements.editionSelect.value = versionData.edition;
        }

        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket 已经连接', 'info');
                return;
            }

            log('正在连接 WebSocket...', 'info');
            ws = new WebSocket('ws://127.0.0.1:3000');

            ws.onopen = function(event) {
                log('WebSocket 连接成功', 'success');
                updateConnectionStatus(true);
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${data.type}/${data.action || data.eventType || ''}`, 'info');

                    // 处理版本信息
                    if (data.type === 'version') {
                        if (data.action === 'info' && data.data) {
                            log('收到版本信息', 'version');
                            updateVersionInfo(data.data);
                        } else if (data.action === 'setEdition') {
                            if (data.success) {
                                log(`版本切换成功: ${data.message}`, 'success');
                                elements.switchResult.innerHTML = `<span style="color: green;">${data.message}</span>`;
                            } else {
                                log(`版本切换失败: ${data.message}`, 'error');
                                elements.switchResult.innerHTML = `<span style="color: red;">${data.message}</span>`;
                            }
                        }
                    }

                    // 处理连接消息
                    if (data.type === 'connection' && data.action === 'connected') {
                        log(`客户端ID: ${data.clientId}`, 'info');
                    }
                } catch (error) {
                    log(`解析消息失败: ${error.message}`, 'error');
                }
            };

            ws.onclose = function(event) {
                log(`WebSocket 连接关闭: ${event.code} ${event.reason}`, 'error');
                updateConnectionStatus(false);
            };

            ws.onerror = function(error) {
                log(`WebSocket 错误: ${error}`, 'error');
                updateConnectionStatus(false);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage(type, action, data = {}) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket 未连接，无法发送消息', 'error');
                return;
            }

            const message = {
                type,
                action,
                data: {
                    ...data,
                    messageId: ++messageId
                }
            };

            log(`发送消息: ${type}/${action}`, 'info');
            ws.send(JSON.stringify(message));
        }

        function requestVersionInfo() {
            sendMessage('version', 'getInfo');
        }

        function switchEdition() {
            const selectedEdition = elements.editionSelect.value;
            log(`请求切换版本到: ${selectedEdition}`, 'info');
            sendMessage('version', 'setEdition', { edition: selectedEdition });
        }

        function clearLog() {
            elements.messageLog.innerHTML = '';
        }

        // 事件监听器
        elements.connectBtn.addEventListener('click', connectWebSocket);
        elements.disconnectBtn.addEventListener('click', disconnectWebSocket);
        elements.requestVersionBtn.addEventListener('click', requestVersionInfo);
        elements.switchEditionBtn.addEventListener('click', switchEdition);
        elements.clearLogBtn.addEventListener('click', clearLog);

        // 页面加载完成后自动连接
        window.addEventListener('load', function() {
            log('页面加载完成，准备连接 WebSocket', 'info');
            setTimeout(connectWebSocket, 1000);
        });
    </script>
</body>
</html> 