const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 验证资源文件是否正确复制到resources目录
 * 这个脚本在electron-builder的afterPack钩子中运行
 */
module.exports = async (context) => {
  const { appOutDir, outDir } = context;
  const platform = context.electronPlatformName;
  
  console.log('========================================');
  console.log('          验证资源文件完整性           ');
  console.log('========================================');
  
  console.log(`打包平台: ${platform}`);
  console.log(`输出目录: ${outDir}`);
  console.log(`应用目录: ${appOutDir}`);
  
  // 根据不同平台确定resources目录路径
  let resourcesDir;
  if (platform === 'win32') {
    resourcesDir = path.join(appOutDir, 'resources');
  } else if (platform === 'darwin') {
    resourcesDir = path.join(appOutDir, 'Contents', 'Resources');
  } else {
    resourcesDir = path.join(appOutDir, 'resources');
  }
  
  console.log(`资源目录: ${resourcesDir}`);
  
  // 检查资源目录是否存在
  if (!fs.existsSync(resourcesDir)) {
    console.error('错误: 资源目录不存在!');
    return;
  }
  
  // 检查关键资源是否存在
  const requiredResources = [
    'assets',
    'assets/ww.png',
    'ui',
    'ui/index.html',
    'env-config.json'
  ];
  
  let allValid = true;
  for (const resource of requiredResources) {
    const resourcePath = path.join(resourcesDir, resource);
    const exists = fs.existsSync(resourcePath);
    console.log(`检查: ${resource} - ${exists ? '存在✓' : '不存在✗'}`);
    
    if (!exists) {
      allValid = false;
    }
  }
  
  // 创建环境配置文件(如果不存在)
  const envConfigPath = path.join(resourcesDir, 'env-config.json');
  if (!fs.existsSync(envConfigPath)) {
    console.log('创建环境配置文件...');
    const envConfig = {
      ENV: 'production',
      APP_VERSION: process.env.npm_package_version || '1.0.0',
      BUILD_DATE: new Date().toISOString()
    };
    
    try {
      fs.writeFileSync(envConfigPath, JSON.stringify(envConfig, null, 2));
      console.log('环境配置文件已创建');
    } catch (err) {
      console.error('创建环境配置文件失败:', err);
      allValid = false;
    }
  }
  
  console.log('========================================');
  console.log(`验证结果: ${allValid ? '通过✓' : '失败✗'}`);
  console.log('========================================');
};

/**
 * 检查package.json版本号是否更新
 */
function checkVersionUpdate() {
  const rootDir = path.resolve(__dirname, '..');
  const packageJsonPath = path.join(rootDir, 'package.json');

  if (!fs.existsSync(packageJsonPath)) {
    console.error('错误: package.json文件不存在!');
    return false;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const currentVersion = packageJson.version;

  console.log(`当前版本号: ${currentVersion}`);

  try {
    // 检查git仓库中最近提交的package.json版本
    const lastCommitVersion = execSync('git show HEAD:package.json', { cwd: rootDir })
      .toString()
      .match(/"version":\s*"([^"]+)"/)[1];

    console.log(`Git中的版本号: ${lastCommitVersion}`);

    // 比较版本号
    if (currentVersion === lastCommitVersion) {
      console.warn('警告: 版本号未更新! 建议在构建前更新版本号。');
      console.warn('可以运行 "npm run check-version" 来更新版本号。');
      return false;
    } else {
      console.log('版本号已更新，继续验证。');
      return true;
    }
  } catch (error) {
    console.error('获取Git历史版本号时出错:', error.message);
    return false;
  }
} 