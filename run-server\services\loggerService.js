const fs = require('fs');
const path = require('path');
const { defaultLogger } = require('../utils/logger');
const { app } = require('electron');

class LoggerService {
    constructor() {
        this.clientLogs = new Map(); // 存储每个客户端的日志
        this.logDir = path.join(app.getPath('userData'), 'logs', 'client-logs');
        this.maxLogSize = 10 * 1024 * 1024; // 10MB 最大日志文件大小
        this.maxLogFiles = 5; // 最多保留5个日志文件
        
        // 确保日志目录存在
        this.ensureLogDirectory();
    }

    ensureLogDirectory() {
        try {
            if (!fs.existsSync(this.logDir)) {
                fs.mkdirSync(this.logDir, { recursive: true });
                defaultLogger.info(`客户端日志目录已创建: ${this.logDir}`);
            }
        } catch (error) {
            defaultLogger.error('创建客户端日志目录失败:', error);
        }
    }

    /**
     * 同步客户端日志
     * @param {string} clientId 客户端ID
     * @param {string} content 日志内容
     * @param {string} timestamp 时间戳
     */
    syncLog(clientId, content, timestamp) {
        try {
            if (!clientId || !content) {
                return { success: false, message: '缺少必要参数' };
            }

            // 清理HTML标签，只保留纯文本
            const cleanContent = this.cleanLogContent(content);
            
            if (!cleanContent.trim()) {
                return { success: true, message: '空内容，跳过' };
            }

            // 获取或创建客户端日志文件路径
            const logFilePath = this.getLogFilePath(clientId);
            
            // 格式化日志条目
            const logEntry = `${timestamp} [${clientId.substring(0, 8)}] ${cleanContent}\n`;
            
            // 检查文件大小，如果太大则轮转
            this.rotateLogIfNeeded(logFilePath);
            
            // 写入日志
            fs.appendFileSync(logFilePath, logEntry, 'utf8');
            
            // 更新内存中的日志记录
            if (!this.clientLogs.has(clientId)) {
                this.clientLogs.set(clientId, {
                    lastSync: new Date(),
                    totalEntries: 0,
                    totalSize: 0
                });
            }
            
            const clientLogInfo = this.clientLogs.get(clientId);
            clientLogInfo.lastSync = new Date();
            clientLogInfo.totalEntries++;
            clientLogInfo.totalSize += logEntry.length;
            
            return { success: true, message: '日志已同步' };
            
        } catch (error) {
            defaultLogger.error(`同步客户端日志失败 [${clientId}]:`, error);
            return { success: false, message: `同步失败: ${error.message}` };
        }
    }

    /**
     * 清理日志内容，移除HTML标签
     * @param {string} content 原始日志内容
     * @returns {string} 清理后的内容
     */
    cleanLogContent(content) {
        if (!content || typeof content !== 'string') {
            return '';
        }

        // 移除HTML标签，保留文本内容
        return content
            // 将<br/>转换为换行符
            .replace(/<br\s*\/?>/gi, '\n')
            // 移除所有HTML标签，但保留内部文本
            .replace(/<[^>]*>/g, '')
            // 解码HTML实体
            .replace(/&nbsp;/g, ' ')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            // 清理多余的空白字符
            .replace(/\s+/g, ' ')
            // 清理多余的换行符
            .replace(/\n\s*\n/g, '\n')
            .trim();
    }

    /**
     * 获取客户端日志文件路径
     * @param {string} clientId 客户端ID
     * @returns {string} 日志文件路径
     */
    getLogFilePath(clientId) {
        const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        const fileName = `client_${clientId.substring(0, 8)}_${date}.log`;
        return path.join(this.logDir, fileName);
    }

    /**
     * 如果日志文件太大则进行轮转
     * @param {string} logFilePath 日志文件路径
     */
    rotateLogIfNeeded(logFilePath) {
        try {
            if (fs.existsSync(logFilePath)) {
                const stats = fs.statSync(logFilePath);
                if (stats.size > this.maxLogSize) {
                    // 创建备份文件名
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                    const backupPath = logFilePath.replace('.log', `_${timestamp}.log`);
                    
                    // 移动当前文件为备份
                    fs.renameSync(logFilePath, backupPath);
                    
                    defaultLogger.info(`日志文件已轮转: ${logFilePath} -> ${backupPath}`);
                    
                    // 清理旧的备份文件
                    this.cleanupOldLogFiles(path.dirname(logFilePath));
                }
            }
        } catch (error) {
            defaultLogger.error('日志文件轮转失败:', error);
        }
    }

    /**
     * 清理旧的日志文件
     * @param {string} logDir 日志目录
     */
    cleanupOldLogFiles(logDir) {
        try {
            const files = fs.readdirSync(logDir)
                .filter(file => file.endsWith('.log'))
                .map(file => ({
                    name: file,
                    path: path.join(logDir, file),
                    mtime: fs.statSync(path.join(logDir, file)).mtime
                }))
                .sort((a, b) => b.mtime - a.mtime); // 按修改时间降序排列

            // 如果文件数量超过限制，删除最旧的文件
            if (files.length > this.maxLogFiles) {
                const filesToDelete = files.slice(this.maxLogFiles);
                filesToDelete.forEach(file => {
                    try {
                        fs.unlinkSync(file.path);
                        defaultLogger.info(`已删除旧日志文件: ${file.name}`);
                    } catch (error) {
                        defaultLogger.error(`删除旧日志文件失败: ${file.name}`, error);
                    }
                });
            }
        } catch (error) {
            defaultLogger.error('清理旧日志文件失败:', error);
        }
    }

    /**
     * 获取客户端日志统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            totalClients: this.clientLogs.size,
            clients: []
        };

        this.clientLogs.forEach((info, clientId) => {
            stats.clients.push({
                clientId: clientId.substring(0, 8),
                lastSync: info.lastSync.toISOString(),
                totalEntries: info.totalEntries,
                totalSize: info.totalSize
            });
        });

        return stats;
    }

    /**
     * 读取客户端日志文件
     * @param {string} clientId 客户端ID
     * @param {number} lines 读取的行数，默认100行
     * @returns {Object} 日志内容
     */
    readClientLog(clientId, lines = 100) {
        try {
            const logFilePath = this.getLogFilePath(clientId);
            
            if (!fs.existsSync(logFilePath)) {
                return { success: false, message: '日志文件不存在' };
            }

            const content = fs.readFileSync(logFilePath, 'utf8');
            const logLines = content.split('\n').filter(line => line.trim());
            
            // 返回最后N行
            const recentLines = logLines.slice(-lines);
            
            return {
                success: true,
                data: {
                    lines: recentLines,
                    totalLines: logLines.length,
                    filePath: logFilePath
                }
            };
            
        } catch (error) {
            defaultLogger.error(`读取客户端日志失败 [${clientId}]:`, error);
            return { success: false, message: `读取失败: ${error.message}` };
        }
    }
}

module.exports = new LoggerService(); 