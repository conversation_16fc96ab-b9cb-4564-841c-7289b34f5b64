<template>
  <transition name="notification-fade">
    <div v-if="showNotification" class="notification" :class="notificationType">
      {{ notificationMessage }}
    </div>
  </transition>
</template>

<script>
import { inject } from 'vue'
import '../assets/notification.css'

export default {
  name: 'NotificationContainer',
  setup() {
    // 从依赖注入系统获取通知功能
    const notification = inject('notification')

    return {
      showNotification: notification.showNotification,
      notificationMessage: notification.notificationMessage,
      notificationType: notification.notificationType
    }
  }
}
</script> 