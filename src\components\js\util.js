//在后续的wps版本中，wps的所有枚举值都会通过wps.Enum对象来自动支持，现阶段先人工定义
var WPS_Enum = {
  msoCTPDockPositionLeft: 0,
  msoCTPDockPositionRight: 2
}

function GetUrlPath() {
  // 在本地网页的情况下获取路径
  if (window.location.protocol === 'file:') {
    const path = window.location.href;
    // 删除文件名以获取根路径
    return path.substring(0, path.lastIndexOf('/'));
  }

  // 在非本地网页的情况下获取根路径
  const { protocol, hostname, port } = window.location;
  const portPart = port ? `:${port}` : '';
  return `${protocol}//${hostname}${portPart}`;
}

function GetRouterHash() {
  if (window.location.protocol === 'file:') {
    return '';
  }

  return '/#'
}

/**
 * 检测当前用户打开的窗格类型
 * @returns {string|null} 'taskpane', 'image-split' 或 null
 */
function detectCurrentPaneType() {
  try {
    if (!window.Application) return null

    const currentDoc = window.Application.ActiveDocument
    if (!currentDoc) return null

    const docId = currentDoc.DocID

    // 检查解题窗格
    const taskpaneId = window.Application.PluginStorage.getItem(`taskpane_id_${docId}`)
    if (taskpaneId) {
      const taskpane = window.Application.GetTaskPane(taskpaneId)
      if (taskpane && taskpane.Visible) {
        return 'taskpane'
      }
    }

    // 检查切割窗格
    const imageSplitId = window.Application.PluginStorage.getItem(`imagesplit_taskpane_id_${docId}`)
    if (imageSplitId) {
      const imageSplitPane = window.Application.GetTaskPane(imageSplitId)
      if (imageSplitPane && imageSplitPane.Visible) {
        return 'image-split'
      }
    }

    return null
  } catch (error) {
    console.error('检测窗格类型失败:', error)
    return null
  }
}

/**
 * 根据当前窗格类型进行智能跳转
 * @param {string} defaultRoute 默认路由，如果检测不到窗格类型时使用
 */
function redirectToCurrentPane(defaultRoute = '/taskpane') {
  try {
    const paneType = detectCurrentPaneType()

    let targetRoute = defaultRoute
    if (paneType === 'image-split') {
      targetRoute = '/image-split'
    } else if (paneType === 'taskpane') {
      targetRoute = '/taskpane'
    }

    console.log('智能跳转:', {
      detectedPaneType: paneType,
      targetRoute: targetRoute
    })

    window.location.hash = `#${targetRoute}`
  } catch (error) {
    console.error('智能跳转失败，使用默认路由:', error)
    window.location.hash = `#${defaultRoute}`
  }
}

export default {
  WPS_Enum,
  GetUrlPath,
  GetRouterHash,
  detectCurrentPaneType,
  redirectToCurrentPane,
}
