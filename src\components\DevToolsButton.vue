<template>
  <div v-if="shouldShowDevTools">
    <button @click="toggleDevTools" class="dev-tools-btn" :class="{ active: isDevMode }">
      <span class="dev-icon">⚙</span>
      <span class="dev-text">{{ isDevMode ? '关闭开发者模式' : '开发者模式' }}</span>
    </button>
    <LogViewer :visible="showLogs" @close="showLogs = false" />
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import LogViewer from './LogViewer.vue';
import logger from './js/logger';
import { getUserInfo } from './js/auth';

export default {
  name: 'DevToolsButton',
  components: {
    LogViewer
  },
  setup() {
    const isDevMode = ref(false);
    const showLogs = ref(false);

    const shouldShowDevTools = computed(() => {
      const userInfo = getUserInfo();
      return userInfo?.orgs[0]?.orgId === 2;
    });

    const toggleDevTools = () => {
      isDevMode.value = !isDevMode.value;
      showLogs.value = isDevMode.value;

      if (isDevMode.value) {
        logger.info('开发者模式已启用');
      } else {
        logger.info('开发者模式已关闭');
      }
    };

    return {
      isDevMode,
      showLogs,
      toggleDevTools,
      shouldShowDevTools
    };
  }
};
</script>

<style scoped>
.dev-tools-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  z-index: 9999;
  transition: background-color 0.2s;
  opacity: 0.7;
}

.dev-tools-btn:hover {
  opacity: 1;
  background-color: #444;
}

.dev-tools-btn.active {
  background-color: #1976D2;
  opacity: 1;
}

.dev-icon {
  font-size: 16px;
}

.dev-text {
  font-size: 12px;
}
</style>
