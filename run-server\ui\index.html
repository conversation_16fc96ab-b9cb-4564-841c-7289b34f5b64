<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WPS插件服务</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    body {
      background-color: #f0f2f5;
      height: 100vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      color: #333;
    }

    /* 自定义标题栏样式 */
    .title-bar {
      height: 36px;
      background-color: #229a52;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px;
      -webkit-app-region: drag;
      color: white;
      font-size: 13px;
      user-select: none;
      -webkit-user-select: none;
    }

    .title-bar .title {
      font-weight: 600;
      pointer-events: auto;
    }

    .title-bar .controls {
      display: flex;
      -webkit-app-region: no-drag;
    }

    .title-bar .controls button {
      width: 28px;
      height: 28px;
      background-color: transparent;
      border: none;
      color: white;
      font-size: 14px;
      cursor: pointer;
      margin-left: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      transition: background-color 0.2s;
    }

    .title-bar .controls button:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .title-bar .controls .close-button:hover {
      background-color: #e81123;
    }

    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #fff;
    }

    .header {
      text-align: center;
      padding: 20px 0 25px 0;
      border-bottom: 1px solid #e8eaed;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .header h1 {
      font-size: 20px;
      color: #2c3e50;
      margin: 0;
      font-weight: 700;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .panel-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 30px 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
    }

    .login-form,
    .status-panel {
      display: none;
      flex-direction: column;
    }

    .login-form {
      /* gap: 20px;
      max-width: 320px;
      margin: 0 auto;
      padding: 35px 25px;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(34, 154, 82, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(34, 154, 82, 0.1); */
    }

    .login-header {
      text-align: center;
      margin-bottom: 0px;
    }

    .login-header h2 {
      font-size: 22px;
      color: #2c3e50;
      margin: 0 0 8px 0;
      font-weight: 700;
    }

    .login-header p {
      font-size: 13px;
      color: #7f8c8d;
      margin: 0;
      font-weight: 400;
    }

    .status-panel {
      flex: 1;
      justify-content: center;
      /* Adjusted for better centering of content */
    }

    .form-group {
      display: flex;
      flex-direction: column;
      position: relative;
      margin-bottom: 9px;
    }

    label {
      font-size: 13px;
      color: #2c3e50;
      font-weight: 600;
      margin-bottom: 2px;
      transition: color 0.3s ease;
    }

    .form-group:focus-within label {
      color: #229a52;
    }

    input {
      height: 42px;
      padding: 0 14px;
      border: 2px solid #e1e8ed;
      border-radius: 8px;
      font-size: 14px;
      background-color: #ffffff;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    }

    input:focus {
      border-color: #229a52;
      outline: none;
      box-shadow: 0 0 0 3px rgba(34, 154, 82, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }

    input:hover:not(:focus) {
      border-color: #bdc3c7;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    input::placeholder {
      color: #95a5a6;
      font-weight: 400;
    }

    .button-container {
      margin-top: 10px;
    }

    button {
      height: 44px;
      width: 100%;
      background: linear-gradient(135deg, #229a52 0%, #1cc660 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 3px 12px rgba(34, 154, 82, 0.3);
      position: relative;
      overflow: hidden;
    }

    button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    button:hover::before {
      left: 100%;
    }

    button:disabled {
      background: linear-gradient(135deg, #a8d5ba 0%, #9fd8b8 100%);
      cursor: not-allowed;
      opacity: 0.7;
      box-shadow: none;
      transform: none;
    }

    button:disabled::before {
      display: none;
    }

    button:hover:not(:disabled) {
      background: linear-gradient(135deg, #1cc660 0%, #16a652 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(34, 154, 82, 0.4);
    }

    button:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(34, 154, 82, 0.3);
    }

    button.secondary {
      background-color: #6c757d;
      margin-top: 10px;
      /* Adjusted margin for spacing */
    }

    button.secondary:hover:not(:disabled) {
      background-color: #5a6268;
    }

    button.button-danger {
      /* New style for danger button */
      background-color: #dc3545;
      margin-top: 10px;
      /* Consistent margin */
    }

    button.button-danger:hover:not(:disabled) {
      background-color: #c82333;
    }


    .error-message {
      color: #e74c3c;
      font-size: 13px;
      text-align: center;
      display: none;
      margin-top: 15px;
      min-height: 18px;
      padding: 10px 12px;
      background-color: rgba(231, 76, 60, 0.08);
      border: 1px solid rgba(231, 76, 60, 0.2);
      border-radius: 6px;
      font-weight: 500;
      /* Ensure space for message */
    }

    /* 加载指示器样式 */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      gap: 15px;
    }

    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(34, 154, 82, 0.2);
      border-top-color: #229a52;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    /* WPS插件面板样式 */
    .wps-addon-panel {
      display: flex;
      /* Changed to flex for better alignment */
      flex-direction: column;
      /* Stack info and buttons vertically */
      margin: 15px 0;
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      gap: 10px;
      /* Added gap between info and buttons */
    }

    .wps-addon-title {
      /* Not used directly, but good for potential future use */
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #333;
    }

    .wps-addon-info {
      display: flex;
      /* Use flex for info line */
      align-items: center;
      justify-content: space-between;
      /* Space out name and status */
    }

    .wps-addon-name {
      font-weight: 500;
    }

    .wps-addon-status {
      padding: 4px 8px;
      border-radius: 50px;
      font-size: 12px;
      min-width: 60px;
      /* Ensure consistent width */
      text-align: center;
    }

    .status-installed {
      color: #34a853;
      background-color: rgba(52, 168, 83, 0.1);
      border: 1px solid rgba(52, 168, 83, 0.2);
    }

    .status-not-installed {
      color: #ea4335;
      background-color: rgba(234, 67, 53, 0.1);
      border: 1px solid rgba(234, 67, 53, 0.2);
    }

    .wps-addon-buttons {
      display: flex;
      gap: 10px;
    }

    .wps-addon-buttons button {
      flex: 1;
    }

    /* 修复用户界面以匹配截图 */
    html,
    body {
      height: 360px;
      /* Fixed height as per original UI */
      max-height: 360px;
      overflow: hidden;
    }

    #user-greeting {
      margin-bottom: 10px !important;
    }

    .panel-container {
      padding: 15px;
    }

    #logout-button {
      margin-top: 15px;
      /* Adjusted from auto to fixed for consistency */
    }

    /* 网络文件夹选择面板样式 */
    .network-folder-container {
      display: none;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      gap: 15px;
      padding: 20px;
    }

    .network-folder-header {
      text-align: center;
      margin-bottom: 15px;
    }

    .network-folder-header h3 {
      color: #e67e22;
      margin-bottom: 10px;
    }

    .network-folder-header p {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }

    .current-path-info {
      width: 100%;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 6px;
      margin-bottom: 15px;
    }

    .current-path-info div:first-child {
      font-size: 12px;
      color: #666;
      margin-bottom: 5px;
    }

    .current-path-info div:last-child {
      font-size: 13px;
      color: #333;
      word-break: break-all;
    }

    .network-folder-buttons {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .network-folder-buttons button {
      background-color: #e67e22;
    }

    .network-folder-message {
      display: none;
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 13px;
      text-align: center;
    }

    .network-folder-help {
      margin-top: 15px;
      font-size: 12px;
      color: #999;
      text-align: center;
      line-height: 1.3;
    }

    /* 通用弹窗样式 */
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }

    /* 机器码弹窗样式 */
    .machine-code-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }

    .machine-code-content {
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      width: 300px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      display: flex;
      flex-direction: column;
    }

    .machine-code-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .machine-code-header h3 {
      margin: 0;
      color: #333;
    }

    .machine-code-close {
      cursor: pointer;
      font-size: 18px;
      color: #777;
    }

    .machine-code-body {
      margin-bottom: 15px;
    }

    .machine-code-value {
      background-color: #f0f2f5;
      padding: 8px;
      border-radius: 4px;
      font-family: monospace;
      word-break: break-all;
      margin-top: 5px;
      margin-bottom: 10px;
      font-size: 12px;
    }

    .machine-code-info {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }

    .machine-code-footer {
      display: flex;
      justify-content: flex-end;
    }

    .machine-code-refresh {
      background-color: #229a52;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
    }

    .machine-code-refresh:hover {
      background-color: #1cc660;
    }

    /* 帮助弹窗样式 */
    .help-modal {
      z-index: 1100;
    }

    .help-content {
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      width: 90%;
      max-width: 500px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
    }

    .help-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .help-header h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
    }

    .help-close {
      cursor: pointer;
      font-size: 20px;
      color: #999;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;
    }

    .help-close:hover {
      background-color: #f0f0f0;
      color: #666;
    }

    .help-body {
      flex: 1;
      overflow-y: auto;
    }

    .help-section {
      margin-bottom: 20px;
    }

    .help-section h4 {
      margin: 0 0 8px 0;
      color: #e67e22;
      font-size: 14px;
      font-weight: 600;
    }

    .help-section p {
      margin: 0 0 8px 0;
      font-size: 13px;
      color: #666;
      line-height: 1.4;
    }

    .help-steps {
      list-style: none;
      padding: 0;
      margin: 8px 0;
    }

    .help-steps li {
      font-size: 12px;
      color: #555;
      margin-bottom: 6px;
      padding-left: 20px;
      position: relative;
      line-height: 1.3;
    }

    .help-steps li:before {
      content: counter(step-counter);
      counter-increment: step-counter;
      position: absolute;
      left: 0;
      top: 0;
      background-color: #e67e22;
      color: white;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
    }

    .help-steps {
      counter-reset: step-counter;
    }

    .help-command {
      background-color: #f4f4f4;
      padding: 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 11px;
      color: #333;
      margin: 8px 0;
      word-break: break-all;
      border-left: 3px solid #e67e22;
    }

    .help-warning {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 8px;
      border-radius: 4px;
      margin: 8px 0;
    }

    .help-warning p {
      margin: 0;
      font-size: 12px;
      color: #856404;
    }

    .help-troubleshooting {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      margin-top: 15px;
    }

    .help-troubleshooting h5 {
      margin: 0 0 8px 0;
      font-size: 13px;
      color: #dc3545;
    }

    .help-troubleshooting ul {
      margin: 0;
      padding-left: 16px;
    }

    .help-troubleshooting li {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
      line-height: 1.3;
    }

    .help-footer {
      margin-top: 15px;
      padding-top: 10px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    .help-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .help-btn-primary {
      background-color: #229a52;
      color: white;
    }

    .help-btn-primary:hover {
      background-color: #1cc660;
    }

    .help-btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .help-btn-secondary:hover {
      background-color: #5a6268;
    }

    /* 更新弹窗样式 */
    .update-modal {
      z-index: 1010;
    }

    .update-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 480px; /* 增加最大宽度以容纳更新日志 */
      max-height: 80vh; /* 限制最大高度 */
      overflow: hidden;
      display: flex;
      flex-direction: column;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }

    .update-header {
      padding: 16px 20px;
      border-bottom: 1px solid #e8eaed;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f8f9fa;
    }

    .update-header h3 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }

    .update-close {
      font-size: 20px;
      cursor: pointer;
      color: #666;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: all 0.2s;
    }

    .update-close:hover {
      background-color: #e9ecef;
      color: #333;
    }

    .update-body {
      padding: 20px;
      flex: 1;
      overflow-y: auto; /* 允许内容区域滚动 */
    }

    .update-status {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .update-icon {
      margin-right: 12px;
      font-size: 20px;
    }

    .update-icon.checking {
      /* animation: spin 1s linear infinite; */
    }

    .update-icon.available {
      color: #28a745;
    }

    .update-icon.downloading {
      color: #007bff;
      animation: pulse 1.5s infinite;
    }

    .update-icon.downloaded {
      color: #28a745;
    }

    .update-icon.error {
      color: #dc3545;
    }

    .update-icon.latest {
      color: #6c757d;
    }

    .update-message {
      flex: 1;
      font-size: 14px;
      color: #333;
      line-height: 1.4;
    }

    /* 版本信息样式 */
    .update-version-info {
      margin: 15px 0;
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #229a52;
    }

    .update-version-info .current-version {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .update-version-info .new-version {
      font-size: 14px;
      color: #229a52;
      font-weight: 600;
    }

    /* 新增：更新日志样式 */
    .update-release-notes {
      margin: 15px 0;
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #007bff;
      /* 让外层统一控制滚动 */
    }

    .update-release-notes-title {
      font-size: 13px;
      font-weight: 600;
      color: #007bff;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }

    .update-release-notes-title::before {
      content: "📋";
      margin-right: 6px;
    }

    .update-release-notes-content {
      font-size: 12px;
      color: #555;
      line-height: 1.5;
      white-space: pre-wrap; /* 保持换行和空格 */
    }

    .update-release-notes-content h1,
    .update-release-notes-content h2,
    .update-release-notes-content h3,
    .update-release-notes-content h4,
    .update-release-notes-content h5,
    .update-release-notes-content h6 {
      font-size: 13px;
      font-weight: 600;
      margin: 8px 0 4px 0;
      color: #333;
    }

    .update-release-notes-content ul,
    .update-release-notes-content ol {
      margin: 4px 0;
      padding-left: 16px;
    }

    .update-release-notes-content li {
      margin: 2px 0;
    }

    .update-release-notes-content p {
      margin: 4px 0;
    }

    .update-release-notes-content strong {
      font-weight: 600;
      color: #333;
    }

    .update-release-notes-content code {
      background-color: #e9ecef;
      padding: 1px 4px;
      border-radius: 3px;
      font-family: monospace;
      font-size: 11px;
    }

    /* 进度条样式 */
    .update-progress-container {
      margin: 15px 0;
    }

    .update-progress-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 12px;
      color: #666;
    }

    .update-progress-bar {
      width: 100%;
      height: 8px;
      background-color: #e9ecef;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;
    }

    .update-progress-fill {
      height: 100%;
      background-color: #007bff;
      transition: width 0.3s ease;
      width: 0%;
    }

    .update-speed-info {
      font-size: 11px;
      color: #6c757d;
      text-align: center;
    }

    .update-footer {
      padding: 16px 20px;
      border-top: 1px solid #e8eaed;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      background: #f8f9fa;
    }

    .update-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      min-width: 80px;
    }

    .update-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .update-btn-primary {
      background-color: #229a52;
      color: white;
    }

    .update-btn-primary:hover:not(:disabled) {
      background-color: #1e7e42;
    }

    .update-btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .update-btn-secondary:hover:not(:disabled) {
      background-color: #5a6268;
    }

    .update-btn-danger {
      background-color: #dc3545;
      color: white;
    }

    .update-btn-danger:hover:not(:disabled) {
      background-color: #c82333;
    }

    /* 状态弹窗样式 */
    .status-modal {
      z-index: 1200;
    }

    .status-content {
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      width: 90%;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      text-align: center;
    }

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .status-header h3 {
      margin: 0;
      color: #333;
      font-size: 16px;
    }

    .status-close {
      cursor: pointer;
      font-size: 18px;
      color: #999;
      width: 22px;
      height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;
    }

    .status-close:hover {
      background-color: #f0f0f0;
      color: #666;
    }

    .status-body {
      margin-bottom: 15px;
    }

    .status-icon {
      font-size: 32px;
      margin-bottom: 10px;
    }

    .status-icon.success {
      color: #28a745;
    }

    .status-icon.error {
      color: #dc3545;
    }

    .status-icon.warning {
      color: #ffc107;
    }

    .status-icon.info {
      color: #17a2b8;
    }

    .status-message {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
      margin-bottom: 15px;
    }

    .status-footer {
      display: flex;
      justify-content: center;
      gap: 10px;
    }

    .status-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      transition: background-color 0.2s;
      min-width: 80px;
    }

    .status-btn-primary {
      background-color: #229a52;
      color: white;
    }

    .status-btn-primary:hover {
      background-color: #1cc660;
    }

    .status-btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .status-btn-secondary:hover {
      background-color: #5a6268;
    }

    /* 启动失败弹窗样式 */
    .startup-error-modal {
      z-index: 1400;
    }

    .startup-error-content {
      background-color: #fff;
      padding: 0;
      border-radius: 8px;
      width: 90%;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
      display: flex;
      flex-direction: column;
      border: 2px solid rgba(220, 53, 69, 0.1);
    }

    .startup-error-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background-color: #dc3545;
      border-radius: 8px 8px 0 0;
      color: white;
    }

    .startup-error-header h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .startup-error-close {
      cursor: pointer;
      font-size: 20px;
      color: white;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;
    }

    .startup-error-close:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .startup-error-body {
      padding: 20px;
      text-align: center;
    }

    .startup-error-icon {
      font-size: 48px;
      color: #dc3545;
      margin-bottom: 15px;
    }

    .startup-error-message {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
      margin-bottom: 15px;
    }

    .startup-error-details {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 6px;
      font-size: 12px;
      color: #495057;
      font-family: monospace;
      word-break: break-all;
      margin-bottom: 15px;
      max-height: 120px;
      overflow-y: auto;
      border: 1px solid #dee2e6;
      text-align: left;
    }

    .startup-error-footer {
      padding: 15px 20px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: center;
      gap: 10px;
      flex-wrap: wrap;
      background-color: #f8f9fa;
      border-radius: 0 0 8px 8px;
    }

    .startup-error-btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;
      min-width: 80px;
      font-weight: 500;
    }

    .startup-error-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .startup-error-btn-primary {
      background-color: #28a745;
      color: white;
    }

    .startup-error-btn-primary:hover:not(:disabled) {
      background-color: #218838;
    }

    .startup-error-btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .startup-error-btn-secondary:hover:not(:disabled) {
      background-color: #5a6268;
    }

    .startup-error-btn-info {
      background-color: #17a2b8;
      color: white;
    }

    .startup-error-btn-info:hover:not(:disabled) {
      background-color: #138496;
    }
  </style>
</head>

<body>
  <div class="title-bar">
    <div class="title" id="app-title">加载中... <span id="version-number" style="font-size: 12px; opacity: 0.8;"></span></div>
    <div class="controls">
      <label for="debug-switch"
        style="cursor: pointer; color: white; font-size: 12px; display: flex; align-items: center; -webkit-app-region: no-drag;">
      </label>
      <button id="check-update-button" title="检查更新" style="-webkit-app-region: no-drag;">↻</button>
      <button id="minimize-button" title="最小化">-</button>
      <button id="close-button" class="close-button" title="关闭">×</button>
    </div>
  </div>

  <!-- 机器码弹窗 -->
  <div id="machine-code-modal" class="machine-code-modal">
    <div class="machine-code-content">
      <div class="machine-code-header">
        <h3>机器码信息</h3>
        <span class="machine-code-close" id="machine-code-close">×</span>
      </div>
      <div class="machine-code-body">
        <div>当前机器码：</div>
        <div id="machine-code-value" class="machine-code-value">正在获取...</div>
        <div class="machine-code-info" id="machine-code-info"></div>

        <!-- 新增：当前监控文件夹信息 -->
        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
          <div style="font-size: 12px; color: #666; margin-bottom: 5px;">当前监控文件夹：</div>
          <div id="current-monitor-dir" style="font-size: 12px; color: #333; word-break: break-all; background-color: #f8f9fa; padding: 5px 8px; border-radius: 3px;">正在获取...</div>
        </div>
      </div>
      <div class="machine-code-footer">
        <button id="machine-code-refresh" class="machine-code-refresh">刷新机器码</button>
        <!-- 新增：重新设置监控文件夹按钮 -->
        <button id="reset-monitor-folder" class="machine-code-refresh" style="background-color: #e67e22; margin-left: 10px;">重设文件夹</button>
      </div>
    </div>
  </div>

  <!-- 帮助弹窗 -->
  <div id="help-modal" class="modal help-modal">
    <div class="help-content">
      <div class="help-header">
        <h3 id="help-title">网络连接配置帮助</h3>
        <span class="help-close" id="help-close">×</span>
      </div>
      <div class="help-body" id="help-body">
        <div class="loading-container" style="padding: 20px;">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载帮助信息...</div>
        </div>
      </div>
      <div class="help-footer">
        <button id="help-auto-enable" class="help-btn help-btn-primary" style="display: none;">尝试自动启用</button>
        <button id="help-close-btn" class="help-btn help-btn-secondary">关闭</button>
      </div>
    </div>
  </div>

  <!-- 更新弹窗 -->
  <div id="update-modal" class="modal update-modal">
    <div class="update-content">
      <div class="update-header">
        <h3 id="update-title">软件更新</h3>
        <span class="update-close" id="update-close">×</span>
      </div>
      <div class="update-body">
        <div class="update-status">
          <div id="update-icon" class="update-icon checking">🔄</div>
          <div id="update-message" class="update-message">正在检查更新...</div>
        </div>

        <!-- 版本信息 -->
        <div id="update-version-info" class="update-version-info" style="display: none;">
          <div id="current-version" class="current-version">当前版本: 加载中...</div>
          <div id="new-version" class="new-version">最新版本: 加载中...</div>
        </div>

        <!-- 更新日志 -->
        <div id="update-release-notes" class="update-release-notes" style="display: none;">
          <div class="update-release-notes-title">更新内容</div>
          <div id="update-release-notes-content" class="update-release-notes-content">
            正在加载更新日志...
          </div>
        </div>

        <!-- 进度条容器 -->
        <div id="update-progress-container" class="update-progress-container" style="display: none;">
          <div class="update-progress-info">
            <span id="progress-text">正在下载...</span>
            <span id="progress-percent">0%</span>
          </div>
          <div class="update-progress-bar">
            <div id="update-progress-fill" class="update-progress-fill"></div>
          </div>
          <div id="update-speed-info" class="update-speed-info">下载速度: 计算中...</div>
        </div>
      </div>
      <div class="update-footer">
        <button id="update-install-btn" class="update-btn update-btn-primary" style="display: none;">立即安装</button>
        <button id="update-download-btn" class="update-btn update-btn-secondary" style="display: none;">立即下载</button>
      </div>
    </div>
  </div>

  <!-- 状态弹窗 -->
  <div id="status-modal" class="modal status-modal">
    <div class="status-content">
      <div class="status-header">
        <h3 id="status-title">操作状态</h3>
        <span class="status-close" id="status-close">×</span>
      </div>
      <div class="status-body">
        <div id="status-icon" class="status-icon">ℹ️</div>
        <div id="status-message" class="status-message">正在处理...</div>
      </div>
      <div class="status-footer">
        <button id="status-ok-btn" class="status-btn status-btn-primary">确定</button>
      </div>
    </div>
  </div>

  <div class="content-area">
    <div class="panel-container">
      <div id="loading-container" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在检查登录状态...</div>
      </div>

      <!-- 启动失败弹窗 -->
      <div id="startup-error-modal" class="modal startup-error-modal">
        <div class="startup-error-content">
          <div class="startup-error-header">
            <h3 id="startup-error-title">启动失败</h3>
            <span class="startup-error-close" id="startup-error-close">×</span>
          </div>
          <div class="startup-error-body">
            <div class="startup-error-icon">⚠️</div>
            <div id="startup-error-message" class="startup-error-message">
              应用启动过程中遇到问题，请查看详细信息并尝试解决。
            </div>
            <div id="startup-error-details" class="startup-error-details" style="display: none;">
              错误详情...
            </div>
          </div>
          <!-- <div class="startup-error-footer">
            <button id="startup-retry-btn" type="button" class="startup-error-btn startup-error-btn-primary">重试启动</button>
            <button id="startup-restart-btn" type="button" class="startup-error-btn startup-error-btn-secondary">重启应用</button>
            <button id="startup-copy-error-btn" type="button" class="startup-error-btn startup-error-btn-info" style="display: none;">复制错误信息</button>
          </div> -->
        </div>
      </div>

      <!-- 权限检查进度条面板 -->
      <div id="permission-check-container" class="permission-check-container" style="display: none; flex-direction: column; align-items: center; justify-content: center; height: 100%; gap: 15px;">
        <h3 style="margin-bottom: 10px; text-align: center;">权限检查</h3>
        <div class="permission-progress-info" style="width: 100%; display: flex; justify-content: space-between; font-size: 12px; margin-bottom: 5px;">
          <span id="current-permission-name">准备检查...</span>
          <span id="permission-progress-percent">0%</span>
        </div>
        <div class="permission-progress-bar" style="width: 100%; height: 8px; background-color: #f0f0f0; border-radius: 4px; overflow: hidden;">
          <div id="permission-progress-fill" style="height: 100%; width: 0%; background-color: #229a52; transition: width 0.3s ease;"></div>
        </div>
        <div class="permission-check-status" style="margin-top: 10px; text-align: center; font-size: 14px; color: #666;">
          <span id="permission-status-text">正在初始化权限检查...</span>
        </div>
      </div>

      <!-- 网络连接提示面板 -->
      <div id="network-connection-container" class="network-connection-container" style="display: none; flex-direction: column; align-items: center; justify-content: center; height: 100%; gap: 8px; padding: 15px;">
        <div class="network-connection-header" style="text-align: center; margin-bottom: 8px;">
          <h3 style="color: #dc3545; margin-bottom: 5px; font-size: 16px;">🌐 网络连接问题</h3>
          <!-- <p id="network-connection-message" style="font-size: 13px; color: #666; line-height: 1.3;">
            无法连接到网络主机，请检查网络连接。
          </p> -->
        </div>

        <div class="network-host-info" style="width: 100%; background-color: #f8f9fa; padding: 8px; border-radius: 4px; margin-bottom: 8px;">
          <div style="font-size: 11px; color: #666; margin-bottom: 3px;">目标网络主机：</div>
          <div id="network-host-address" style="font-size: 12px; color: #333; font-family: monospace;">\\***********</div>
        </div>

        <div class="network-connection-instructions" style="width: 100%; background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 8px; border-radius: 4px; margin-bottom: 10px;">
          <div style="font-size: 11px; color: #856404; margin-bottom: 4px; font-weight: 500;">请尝试以下步骤：</div>
          <div style="font-size: 11px; color: #856404; line-height: 1.3;">
            1. 确保连接网络：WWJY<br>
            2. 在文件管理器中访问该网络主机<br>
            3. 完成后点击"重试连接"按钮
          </div>
        </div>

        <div class="network-connection-buttons" style="width: 100%; display: flex; flex-direction: column; gap: 8px;">
          <button id="retry-network-connection-btn" type="button" style="background-color: #28a745;">重试连接</button>
          <button id="show-help-btn" type="button" style="background-color: #e67e22;">查看配置帮助</button>
          <!-- <button id="skip-network-connection-btn" type="button" class="secondary">跳过网络连接</button> -->
        </div>


      </div>

      <!-- 网络文件夹选择面板 -->
      <div id="network-folder-container" class="network-folder-container" style="display: none; flex-direction: column; align-items: center; justify-content: center; height: 100%; gap: 15px; padding: 5px;">
        <div class="network-folder-header" style="text-align: center; margin-bottom: 15px;">
          <h3 style="color: #e67e22; margin-bottom: 10px;">⚠️ 检测到加密软件</h3>
          <p style="font-size: 14px; color: #666; line-height: 1.4;">
            请选择一个网络设备，程序将自动创建所需的文件夹。
          </p>
        </div>

        <div class="current-path-info" style="width: 100%; background-color: #f8f9fa; padding: 10px; border-radius: 6px; margin-bottom: 15px;">
          <div style="font-size: 12px; color: #666; margin-bottom: 5px;">当前数据目录：</div>
          <div id="current-watch-dir" style="font-size: 13px; color: #333; word-break: break-all;"></div>
        </div>

        <div class="network-folder-buttons" style="width: 100%; display: flex; flex-direction: column; gap: 10px;">
          <button id="select-network-folder-btn" type="button" style="background-color: #e67e22;">选择网络设备</button>
          <button id="confirm-network-folder-btn" type="button" style="background-color: #28a745; display: none;">确认使用此文件夹</button>
          <!-- <button id="skip-network-folder-btn" type="button" class="secondary">暂时跳过</button> -->
        </div>

        <div id="network-folder-message" class="network-folder-message" style="display: none; margin-top: 10px; padding: 8px; border-radius: 4px; font-size: 13px; text-align: center;"></div>

        <!-- <div class="network-folder-help" style="margin-top: 15px; font-size: 12px; color: #999; text-align: center; line-height: 1.3;">
          <div>网络设备示例：</div>
          <div>\\\\************ 或 Z:\\ （映射的网络驱动器）</div>
          <div style="margin-top: 5px; font-size: 11px;">程序将自动创建：[设备]\ww-wps-addon\Temp</div>
        </div> -->
      </div>

      <div id="login-form" class="login-form">
        <div class="login-header">
          <h2>用户登录</h2>
        </div>
        <div class="form-group">
          <label for="username">用户名</label>
          <input type="text" id="username" placeholder="请输入用户名" required>
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input type="password" id="password" placeholder="请输入密码" required>
        </div>
        <div id="error-message" class="error-message"></div>
        <div class="button-container">
          <button id="login-button" type="button">登录</button>
        </div>
      </div>

      <div id="status-panel" class="status-panel">
        <div id="user-greeting" style="text-align: center; margin-bottom: 20px; font-size: 16px;"></div>

        <div class="wps-addon-panel">
          <div class="wps-addon-info">
            <div id="addon-name" class="wps-addon-name">插件: AI编辑WPS插件</div>
            <div id="addon-status" class="wps-addon-status status-not-installed">未安装</div>
          </div>
          <div class="wps-addon-buttons">
            <button id="addon-install-button">安装插件</button>
            <button id="open-wps-button">打开WPS</button>
          </div>
        </div>

        <button id="disable-all-addons-button" type="button" class="button-danger">禁用所有插件</button>

        <button id="logout-button" type="button" class="secondary">退出登录</button>
      </div>
    </div>
  </div>

  <script>
    // 添加调试函数
    function logToUI(message) {
      console.log(`[UI LOG] ${new Date().toLocaleTimeString()}: ${message}`)
    }

    // 立即记录渲染器进程启动
    logToUI('UI初始化')

    // 添加自动检查更新的函数，在应用启动时静默检查更新
    function autoCheckUpdate() {
      logToUI('开始自动检查更新（静默）')
      try {
        if (window.ipcRenderer && typeof window.ipcRenderer.send === 'function') {
          // 发送自动检查更新的消息
          window.ipcRenderer.send('auto-check-update')
          logToUI('已发送自动检查更新请求')
        } else {
          logToUI('ipcRenderer未准备就绪，跳过自动检查更新')
        }
      } catch (error) {
        logToUI(`自动检查更新出错: ${error.message}`)
      }
    }

    // 检查window.ipcRenderer是否存在
    if (!window.ipcRenderer) {
      logToUI('错误: ipcRenderer未找到! 桌面应用功能将受限。')
      window.ipcRenderer = {
        send: function (channel, data) {
          logToUI(`兼容模式发送消息: ${channel}`)
          console.log(`模拟发送: ${channel}`, data)
        },
        on: function (channel, callback) {
          logToUI(`兼容模式注册监听器: ${channel}`)
          console.log(`模拟注册: ${channel}`)
          if (channel === 'show-login') {
            setTimeout(() => {
              logToUI('兼容模式: 自动触发show-login事件')
              callback({}, {})
            }, 1000)
          }
        },
        sendSync: function (channel, data) {
          logToUI(`兼容模式同步发送消息: ${channel}`)
          console.log(`模拟同步发送: ${channel}`, data)
          if (channel === 'get-user-info-sync') return { data: { nickname: '测试用户' } }
          return null
        }
      }
    } else {
      logToUI('ipcRenderer已加载')
      
      // 应用启动后延迟几秒检查更新，确保在 ipcRenderer 准备就绪后进行
      setTimeout(autoCheckUpdate, 8000)
    }

    // 阻止标题栏双击全屏
    const titleBar = document.querySelector('.title-bar')
    if (titleBar) {
      // 阻止双击事件
      titleBar.addEventListener('dblclick', function (e) {
        e.preventDefault()
        e.stopPropagation()
        return false
      })

      // 阻止双击可能触发的mousedown事件
      titleBar.addEventListener('mousedown', function(e) {
        if (e.detail > 1) { // 仅当不是单击时阻止
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      })
    }

    // 应用配置变量
    let appConfig = {
      EDITION: 'wanwei',
      APP_NAME: 'WPS插件服务',
      BYPASS_ENCRYPTION: false
    }

    // DOM Elements
    const loginForm = document.getElementById('login-form')
    const statusPanel = document.getElementById('status-panel')
    const loadingContainer = document.getElementById('loading-container')
    const permissionCheckContainer = document.getElementById('permission-check-container')
    const loginButton = document.getElementById('login-button')
    const logoutButton = document.getElementById('logout-button')
    const errorMessage = document.getElementById('error-message')
    const usernameInput = document.getElementById('username')
    const passwordInput = document.getElementById('password')
    const minimizeButton = document.getElementById('minimize-button')
    const closeButton = document.getElementById('close-button')
    const addonStatus = document.getElementById('addon-status')
    const addonInstallButton = document.getElementById('addon-install-button')
    const openWpsButton = document.getElementById('open-wps-button')
    const userGreeting = document.getElementById('user-greeting')
    const disableAllAddonsButton = document.getElementById('disable-all-addons-button') // 新按钮
    const checkUpdateButton = document.getElementById('check-update-button')
    const appTitle = document.getElementById('app-title')
    const addonName = document.getElementById('addon-name')
    // 权限检查相关DOM元素
    const permissionProgressFill = document.getElementById('permission-progress-fill')
    const permissionProgressPercent = document.getElementById('permission-progress-percent')
    const currentPermissionName = document.getElementById('current-permission-name')
    const permissionStatusText = document.getElementById('permission-status-text')

    // 网络连接相关DOM元素
    const networkConnectionContainer = document.getElementById('network-connection-container')
    const networkConnectionMessage = document.getElementById('network-connection-message')
    const networkHostAddress = document.getElementById('network-host-address')
    const retryNetworkConnectionBtn = document.getElementById('retry-network-connection-btn')
    const skipNetworkConnectionBtn = document.getElementById('skip-network-connection-btn')
    const networkConnectionStatus = document.getElementById('network-connection-status')
    const showHelpBtn = document.getElementById('show-help-btn')

    // 帮助弹窗相关DOM元素
    const helpModal = document.getElementById('help-modal')
    const helpTitle = document.getElementById('help-title')
    const helpBody = document.getElementById('help-body')
    const helpClose = document.getElementById('help-close')
    const helpCloseBtn = document.getElementById('help-close-btn')
    const helpAutoEnableBtn = document.getElementById('help-auto-enable')

    // 状态弹窗相关DOM元素
    const statusModal = document.getElementById('status-modal')
    const statusTitle = document.getElementById('status-title')
    const statusIcon = document.getElementById('status-icon')
    const statusMessage = document.getElementById('status-message')
    const statusClose = document.getElementById('status-close')
    const statusOkBtn = document.getElementById('status-ok-btn')

    // 更新弹窗相关DOM元素
    const updateModal = document.getElementById('update-modal')
    const updateTitle = document.getElementById('update-title')
    const updateIcon = document.getElementById('update-icon')
    const updateMessage = document.getElementById('update-message')
    const updateVersionInfo = document.getElementById('update-version-info')
    const currentVersionElement = document.getElementById('current-version')
    const newVersionElement = document.getElementById('new-version')
    const updateReleaseNotes = document.getElementById('update-release-notes')
    const updateReleaseNotesContent = document.getElementById('update-release-notes-content')
    const updateProgressContainer = document.getElementById('update-progress-container')
    const progressText = document.getElementById('progress-text')
    const progressPercent = document.getElementById('progress-percent')
    const updateProgressFill = document.getElementById('update-progress-fill')
    const updateSpeedInfo = document.getElementById('update-speed-info')
    const updateClose = document.getElementById('update-close')
    const updateDownloadBtn = document.getElementById('update-download-btn')
    const updateInstallBtn = document.getElementById('update-install-btn')
    // const updateLaterBtn = document.getElementById('update-later-btn') // 已移除稍后按钮

    // 添加下载状态跟踪变量
    let isDownloadCompleted = false

    // 网络文件夹选择相关DOM元素
    const networkFolderContainer = document.getElementById('network-folder-container')
    const currentWatchDirElement = document.getElementById('current-watch-dir')
    const selectNetworkFolderBtn = document.getElementById('select-network-folder-btn')
    const confirmNetworkFolderBtn = document.getElementById('confirm-network-folder-btn')
    const skipNetworkFolderBtn = document.getElementById('skip-network-folder-btn')
    const networkFolderMessage = document.getElementById('network-folder-message')

    // 启动失败相关DOM元素
    const startupErrorModal = document.getElementById('startup-error-modal')
    const startupErrorTitle = document.getElementById('startup-error-title')
    const startupErrorMessage = document.getElementById('startup-error-message')
    const startupErrorDetails = document.getElementById('startup-error-details')
    const startupErrorClose = document.getElementById('startup-error-close')
    const startupRetryBtn = document.getElementById('startup-retry-btn')
    const startupRestartBtn = document.getElementById('startup-restart-btn')
    const startupCopyErrorBtn = document.getElementById('startup-copy-error-btn')

    let_messageTimeout = null

    function showTemporaryMessage(message, isSuccess, useModal = false) {
      if (useModal) {
        // 使用弹窗显示
        const title = isSuccess ? '操作成功' : '操作失败'
        const type = isSuccess ? 'success' : 'error'
        showStatusDialog(title, message, type, true) // 自动关闭
      } else {
        // 使用原来的内联显示方式
        if (!errorMessage) return
        clearTimeout(let_messageTimeout)
        errorMessage.textContent = message
        errorMessage.style.color = isSuccess ? '#28a745' : '#dc3545'
        errorMessage.style.display = 'block'
        logToUI(`临时消息: ${message} (成功: ${isSuccess})`)
        let_messageTimeout = setTimeout(() => {
          errorMessage.textContent = ''
          errorMessage.style.display = 'none'
          errorMessage.style.color = '#dc3545'
        }, 3000)
      }
    }

    minimizeButton?.addEventListener('click', () => {
      logToUI('点击最小化按钮')
      window.ipcRenderer?.send('minimize-window')
    })
    closeButton?.addEventListener('click', () => {
      logToUI('点击关闭按钮')
      window.ipcRenderer?.send('hide-window')
    })
    checkUpdateButton?.addEventListener('click', () => {
      logToUI('点击检查更新按钮')
      window.ipcRenderer?.send('check-update')
      showUpdateDialog() // 显示更新弹窗
    })

    function showLoading(text = '正在检查登录状态...') {
      logToUI(`显示加载状态: ${text}`)
      if (loadingContainer) loadingContainer.style.display = 'flex'
      if (loadingContainer) document.querySelector('#loading-container .loading-text').textContent = text
      if (loginForm) loginForm.style.display = 'none'
      if (statusPanel) statusPanel.style.display = 'none'
      if (permissionCheckContainer) permissionCheckContainer.style.display = 'none'
      if (networkConnectionContainer) networkConnectionContainer.style.display = 'none'
      if (networkFolderContainer) networkFolderContainer.style.display = 'none'
    }

    function showLogin() {
      logToUI('显示登录表单')
      if (loadingContainer) loadingContainer.style.display = 'none'
      if (loginForm) loginForm.style.display = 'flex'
      if (statusPanel) statusPanel.style.display = 'none'
      if (permissionCheckContainer) permissionCheckContainer.style.display = 'none'
      if (networkConnectionContainer) networkConnectionContainer.style.display = 'none'
      if (networkFolderContainer) networkFolderContainer.style.display = 'none'
      if (errorMessage) {
        // errorMessage.style.display = 'none'
        // errorMessage.textContent = ''
      }
    }

    // 显示权限检查界面
    function showPermissionCheck() {
      logToUI('显示权限检查界面')
      if (loadingContainer) loadingContainer.style.display = 'none'
      if (loginForm) loginForm.style.display = 'none'
      if (statusPanel) statusPanel.style.display = 'none'
      if (permissionCheckContainer) permissionCheckContainer.style.display = 'flex'
      if (networkConnectionContainer) networkConnectionContainer.style.display = 'none'
      if (networkFolderContainer) networkFolderContainer.style.display = 'none'

      // 重置进度条
      if (permissionProgressFill) permissionProgressFill.style.width = '0%'
      if (permissionProgressPercent) permissionProgressPercent.textContent = '0%'
      if (currentPermissionName) currentPermissionName.textContent = '准备检查...'
      if (permissionStatusText) permissionStatusText.textContent = '正在初始化权限检查...'
    }

    // 显示网络连接界面
    function showNetworkConnection(data) {
      logToUI('显示网络连接界面')
      logToUI(`当前界面显示状态 - Loading: ${loadingContainer?.style.display}, Login: ${loginForm?.style.display}, Status: ${statusPanel?.style.display}, Permission: ${permissionCheckContainer?.style.display}, NetworkConnection: ${networkConnectionContainer?.style.display}`)

      if (loadingContainer) loadingContainer.style.display = 'none'
      if (loginForm) loginForm.style.display = 'none'
      if (statusPanel) statusPanel.style.display = 'none'
      if (permissionCheckContainer) permissionCheckContainer.style.display = 'none'
      if (networkFolderContainer) networkFolderContainer.style.display = 'none'
      if (networkConnectionContainer) networkConnectionContainer.style.display = 'flex'

      // 显示网络连接信息
      if (networkConnectionMessage && data.message) {
        networkConnectionMessage.textContent = data.message
      }
      if (networkHostAddress && data.networkHost) {
        networkHostAddress.textContent = data.networkHost
      }

      // 隐藏状态消息
      if (networkConnectionStatus) {
        networkConnectionStatus.style.display = 'none'
        networkConnectionStatus.textContent = ''
      }

      // 重置按钮状态
      if (retryNetworkConnectionBtn) {
        retryNetworkConnectionBtn.disabled = false
        retryNetworkConnectionBtn.textContent = '重试连接'
      }
      if (skipNetworkConnectionBtn) {
        skipNetworkConnectionBtn.disabled = false
        skipNetworkConnectionBtn.textContent = '跳过网络连接'
      }
    }

    // 显示网络连接状态弹窗
    function showNetworkConnectionStatus(message, isError = false) {
      const title = isError ? '连接失败' : '连接成功'
      const type = isError ? 'error' : 'success'
      showStatusDialog(title, message, type)
    }

    // 显示网络文件夹选择界面
    function showNetworkFolderSelection(data) {
      logToUI('显示网络文件夹选择界面')
      logToUI(`当前界面显示状态 - Loading: ${loadingContainer?.style.display}, Login: ${loginForm?.style.display}, Status: ${statusPanel?.style.display}, Permission: ${permissionCheckContainer?.style.display}, Network: ${networkFolderContainer?.style.display}`)

      if (loadingContainer) loadingContainer.style.display = 'none'
      if (loginForm) loginForm.style.display = 'none'
      if (statusPanel) statusPanel.style.display = 'none'
      if (permissionCheckContainer) permissionCheckContainer.style.display = 'none'
      if (networkConnectionContainer) networkConnectionContainer.style.display = 'none'
      if (networkFolderContainer) networkFolderContainer.style.display = 'flex'

      // 显示当前 watchDir
      if (currentWatchDirElement && data.currentWatchDir) {
        currentWatchDirElement.textContent = data.currentWatchDir
      }

      // 隐藏消息
      if (networkFolderMessage) {
        networkFolderMessage.style.display = 'none'
        networkFolderMessage.textContent = ''
      }

      // 重置按钮状态
      if (selectNetworkFolderBtn) {
        selectNetworkFolderBtn.disabled = false
        selectNetworkFolderBtn.style.display = 'block'
        selectNetworkFolderBtn.textContent = '选择网络设备'
      }
      if (confirmNetworkFolderBtn) {
        confirmNetworkFolderBtn.disabled = false
        confirmNetworkFolderBtn.style.display = 'none'
      }
      if (skipNetworkFolderBtn) skipNetworkFolderBtn.disabled = false
    }

    // 显示网络文件夹消息
    function showNetworkFolderMessage(message, isError = false) {
      if (networkFolderMessage) {
        networkFolderMessage.textContent = message
        networkFolderMessage.style.display = 'block'
        networkFolderMessage.style.backgroundColor = isError ? '#f8d7da' : '#d4edda'
        networkFolderMessage.style.color = isError ? '#721c24' : '#155724'
        networkFolderMessage.style.borderColor = isError ? '#f5c6cb' : '#c3e6cb'
      }
      logToUI(`网络文件夹消息: ${message} (错误: ${isError})`)
    }

    // 显示启动失败弹窗
    function showStartupError(errorData) {
      logToUI(`显示启动失败弹窗: ${JSON.stringify(errorData)}`)

      // 显示启动失败弹窗
      if (startupErrorModal) {
        startupErrorModal.style.display = 'flex'
      }

      // 设置错误信息
      if (startupErrorTitle && errorData.title) {
        startupErrorTitle.textContent = errorData.title
      }

      if (startupErrorMessage && errorData.message) {
        startupErrorMessage.textContent = errorData.message
      }

      // 显示详细错误信息（如果有）
      if (startupErrorDetails && errorData.details) {
        startupErrorDetails.textContent = errorData.details
        startupErrorDetails.style.display = 'block'
        // 显示复制错误信息按钮
        if (startupCopyErrorBtn) {
          startupCopyErrorBtn.style.display = 'inline-block'
        }
      } else {
        if (startupErrorDetails) startupErrorDetails.style.display = 'none'
        if (startupCopyErrorBtn) startupCopyErrorBtn.style.display = 'none'
      }

      // 设置按钮状态
      if (startupRetryBtn) {
        startupRetryBtn.disabled = false
        startupRetryBtn.textContent = errorData.retryText || '重试启动'
      }

      if (startupRestartBtn) {
        startupRestartBtn.disabled = false
        startupRestartBtn.textContent = '重启应用'
      }
    }

    // 隐藏启动失败弹窗
    function hideStartupError() {
      logToUI('隐藏启动失败弹窗')
      if (startupErrorModal) {
        startupErrorModal.style.display = 'none'
      }
    }

    function showStatusPanel(userData) {
      logToUI('显示状态面板')
      if (loadingContainer) loadingContainer.style.display = 'none'
      if (loginForm) loginForm.style.display = 'none'
      if (statusPanel) statusPanel.style.display = 'flex'
      if (permissionCheckContainer) permissionCheckContainer.style.display = 'none'
      if (networkConnectionContainer) networkConnectionContainer.style.display = 'none'
      if (networkFolderContainer) networkFolderContainer.style.display = 'none'

      if (userGreeting && userData && userData.nickname) {
        userGreeting.textContent = `你好，${userData.nickname}`
      } else if (userGreeting && window.ipcRenderer) {
        try {
          const userInfoSync = window.ipcRenderer.sendSync('get-user-info-sync')
          if (userInfoSync && userInfoSync.data && userInfoSync.data.nickname) {
            userGreeting.textContent = `你好，${userInfoSync.data.nickname}`
          } else {
            userGreeting.textContent = `你好！`
          }
        } catch (e) {
          logToUI('同步获取用户信息失败: ' + e.message)
          userGreeting.textContent = `你好！`
        }
      }
      checkAddonStatus()
    }

    showLoading()

    window.ipcRenderer?.on('show-login', () => {
      logToUI('收到show-login事件')
      showLogin()
    })
    window.ipcRenderer?.on('login-success', (event, userData) => {
      logToUI('收到login-success事件')
      showStatusPanel(userData || { nickname: '用户' })
    })
    window.ipcRenderer?.on('login-failure', (event, data) => {
      logToUI(`收到login-failure事件: ${data.message}`)

      // 检查是否是 AI 编辑功能未开启的错误
      if (data.code === 'AI_EDIT_DISABLED') {
        showStatusDialog('AI 编辑功能未开启', data.message || '该企业尚未开启 AI 编辑功能，请联系您的企业管理员。', 'warning')
        showLogin()
        return
      }

      if (errorMessage) {
        errorMessage.textContent = data.message || '登录失败，请重试'
        errorMessage.style.display = 'block'
      }
      showLogin()
    })
    window.ipcRenderer?.on('logout-success', () => {
      logToUI('收到logout-success事件')
      showLogin()
    })

    // 权限检查相关事件监听
    window.ipcRenderer?.on('show-permission-check', () => {
      logToUI('收到show-permission-check事件')
      showPermissionCheck()
    })

    window.ipcRenderer?.on('permission-check-progress', (event, data) => {
      logToUI(`收到permission-check-progress事件: ${JSON.stringify(data)}`)

      // 更新进度条
      if (permissionProgressFill) permissionProgressFill.style.width = `${data.percent}%`
      if (permissionProgressPercent) permissionProgressPercent.textContent = `${data.percent}%`

      // 更新当前检查的权限名称
      if (currentPermissionName && data.currentPermission) {
        currentPermissionName.textContent = data.currentPermission.name || '未知权限'
      }

      // 更新状态文本
      if (permissionStatusText) {
        permissionStatusText.textContent = `正在检查: ${data.current}/${data.total}`
      }
    })

    window.ipcRenderer?.on('permission-check-complete', (event, data) => {
      logToUI(`收到permission-check-complete事件: ${JSON.stringify(data)}`)

      // 完成进度条
      if (permissionProgressFill) permissionProgressFill.style.width = '100%'
      if (permissionProgressPercent) permissionProgressPercent.textContent = '100%'

      // 更新状态文本
      if (permissionStatusText) {
        permissionStatusText.textContent = data.success
          ? '所有权限检查通过，即将进入应用...'
          : `权限检查失败: ${data.message || '未知错误'}`
      }
      // 延迟关闭权限检查界面
      showLoading('权限检查通过，正在检查登录状态...')
      setTimeout(() => {
        if (data.success) {
          // 不删除容器，只是隐藏它
          if (permissionCheckContainer) permissionCheckContainer.style.display = 'none';
        } else {
          logToUI('权限检查失败，显示登录表单')
          showLogin()
          if (errorMessage) {
            errorMessage.textContent = `权限检查失败: ${data.message || '未知错误'}`
            errorMessage.style.display = 'block'
          }
        }
      }, 1500)
    })

    window.ipcRenderer?.on('permission-check-failed', (event, data) => {
      logToUI(`收到permission-check-failed事件: ${JSON.stringify(data)}`)

      // 更新状态文本
      if (permissionStatusText) {
        permissionStatusText.textContent = `权限检查失败: ${data.message || '未知错误'}`
      }

      // 延迟关闭权限检查界面并显示登录表单
      setTimeout(() => {
        showLogin()
        if (errorMessage) {
          errorMessage.textContent = `权限检查失败: ${data.message || '未知错误'}`
          errorMessage.style.display = 'block'
        }
      }, 1500)
    })

    // 网络连接相关事件监听
    window.ipcRenderer?.on('network-connection-required', (event, data) => {
      logToUI(`收到network-connection-required事件: ${JSON.stringify(data)}`)

      // 检查是否已经在显示网络连接界面
      if (networkConnectionContainer && networkConnectionContainer.style.display === 'flex') {
        logToUI('网络连接界面已经在显示中，忽略重复事件')
        return
      }

      showNetworkConnection(data)
    })

    window.ipcRenderer?.on('network-connection-retry-result', (event, data) => {
      logToUI(`收到network-connection-retry-result事件: ${JSON.stringify(data)}`)

      // 恢复重试按钮状态
      if (retryNetworkConnectionBtn) {
        retryNetworkConnectionBtn.disabled = false
        retryNetworkConnectionBtn.textContent = '重试连接'
      }

      if (data.success) {
        showNetworkConnectionStatus(`连接成功: ${data.message}`, false)
        // 连接成功，1秒后会收到权限检查完成事件
        setTimeout(() => {
          showLoading('连接成功，继续启动应用...')
        }, 2000)
      } else {
        showNetworkConnectionStatus(`连接失败: ${data.message}`, true)
      }
    })

    window.ipcRenderer?.on('network-connection-skip-result', (event, data) => {
      logToUI(`收到network-connection-skip-result事件: ${JSON.stringify(data)}`)

      if (data.success) {
        showNetworkConnectionStatus('已跳过网络连接，使用默认路径', false)
        // 跳过成功，1秒后会收到权限检查完成事件
        setTimeout(() => {
          showLoading('继续启动应用...')
        }, 1000)
      } else {
        showNetworkConnectionStatus(`跳过失败: ${data.message}`, true)
      }
    })

    // 网络文件夹选择相关事件监听
    window.ipcRenderer?.on('need-network-folder-selection', (event, data) => {
      logToUI(`收到need-network-folder-selection事件: ${JSON.stringify(data)}`)

      // 检查是否已经在显示网络文件夹选择界面
      if (networkFolderContainer && networkFolderContainer.style.display === 'flex') {
        logToUI('网络文件夹选择界面已经在显示中，忽略重复事件')
        return
      }

      showNetworkFolderSelection(data)
    })

    window.ipcRenderer?.on('network-folder-selected', (event, data) => {
      logToUI(`收到network-folder-selected事件: ${JSON.stringify(data)}`)

      if (data.success) {
        if (data.autoCreated) {
          showNetworkFolderMessage(`网络文件夹已创建成功，请点击确认按钮继续`, false)
        } else {
          showNetworkFolderMessage(`已选择网络文件夹: ${data.path}`, false)
        }

        // 隐藏选择按钮，显示确认按钮
        if (selectNetworkFolderBtn) {
          selectNetworkFolderBtn.style.display = 'none'
        }
        if (confirmNetworkFolderBtn) {
          confirmNetworkFolderBtn.style.display = 'block'
          confirmNetworkFolderBtn.disabled = false
          // 存储选择的路径到按钮的数据属性中
          confirmNetworkFolderBtn.setAttribute('data-selected-path', data.path)
        }
      } else if (data.canceled) {
        showNetworkFolderMessage('未选择网络设备', true)
        // 恢复选择按钮状态
        if (selectNetworkFolderBtn) {
          selectNetworkFolderBtn.disabled = false
          selectNetworkFolderBtn.style.display = 'block'
        }
        if (confirmNetworkFolderBtn) {
          confirmNetworkFolderBtn.style.display = 'none'
        }
      } else {
        showNetworkFolderMessage(data.message || '选择网络设备失败', true)
        // 恢复选择按钮状态
        if (selectNetworkFolderBtn) {
          selectNetworkFolderBtn.disabled = false
          selectNetworkFolderBtn.style.display = 'block'
        }
        if (confirmNetworkFolderBtn) {
          confirmNetworkFolderBtn.style.display = 'none'
        }
      }
    })

    window.ipcRenderer?.on('network-folder-confirmed', (event, data) => {
      logToUI(`收到network-folder-confirmed事件: ${JSON.stringify(data)}`)

      if (data.success) {
        showNetworkFolderMessage('网络文件夹配置成功，继续启动应用...', false)
        // 1秒后会收到 permission-check-complete 事件
      } else {
        showNetworkFolderMessage(data.message || '配置失败，请重试', true)
        // 恢复按钮状态：隐藏确认按钮，显示选择按钮
        if (confirmNetworkFolderBtn) {
          confirmNetworkFolderBtn.disabled = false
          confirmNetworkFolderBtn.style.display = 'none'
        }
        if (selectNetworkFolderBtn) {
          selectNetworkFolderBtn.disabled = false
          selectNetworkFolderBtn.style.display = 'block'
        }
      }
    })

    const loadingTimeout = setTimeout(() => {
      if (loadingContainer && loadingContainer.style.display === 'flex') {
        // logToUI('紧急修复: 加载状态超时，强制显示登录表单')
        // showLogin()
      }
    }, 8000)

    loginButton?.addEventListener('click', () => {
      const username = usernameInput.value.trim()
      const password = passwordInput.value.trim()
      if (!username || !password) {
        if (errorMessage) {
          errorMessage.textContent = '请输入用户名和密码'
          errorMessage.style.display = 'block'
        }
        return
      }
      if (errorMessage) errorMessage.style.display = 'none'
      logToUI(`尝试登录: ${username}`)
      showLoading('正在登录...')
      window.ipcRenderer?.send('login-attempt', { username, password })
      if (passwordInput) passwordInput.value = ''
    })
    logoutButton?.addEventListener('click', () => {
      logToUI('退出登录')
      window.ipcRenderer?.send('logout')
    })
    passwordInput?.addEventListener('keyup', (event) => {
      if (event.key === 'Enter') {
        loginButton?.click()
      }
    })

    // 获取插件URL
    function getAddonUrl() {
      if (appConfig.EDITION === 'hexin') {
        return 'http://wps.hexinedu.com/hexin-wps-addon-build/'
      } else {
        return 'http://wwwps.hexinedu.com/wps-addon-build/'
      }
    }

    // 获取插件名称
    function getAddonDisplayName() {
      if (appConfig.EDITION === 'hexin') {
        return '合心AI编辑WPS插件'
      } else {
        return '万唯AI编辑WPS插件'
      }
    }

    // 更新UI显示名称
    function updateUINames() {
      if (appTitle && appConfig.APP_NAME) {
        const versionSpan = appTitle.querySelector('#version-number')
        const versionText = versionSpan ? versionSpan.outerHTML : ''
        appTitle.innerHTML = `${appConfig.APP_NAME} ${versionText}`
      }

      if (addonName) {
        addonName.textContent = `插件: ${getAddonDisplayName()}`
      }

      // 更新页面标题
      if (appConfig.APP_NAME) {
        document.title = appConfig.APP_NAME
      }

      logToUI(`UI名称已更新 - 版本: ${appConfig.EDITION}, 名称: ${appConfig.APP_NAME}`)
    }

    // WPS插件相关功能
    var serverId = undefined
    var serverVersion = 'wait'
    var addonInstalled = false

    function getHttpObj() {
      var httpobj = null
      if (IEVersion() < 10) {
        try {
          httpobj = new XDomainRequest()
        } catch (e1) {
          httpobj = new createXHR()
        }
      } else {
        httpobj = new createXHR()
      }
      return httpobj
    }

    function createXHR() {
      if (typeof XMLHttpRequest != 'undefined') {
        return new XMLHttpRequest()
      } else if (typeof ActiveXObject != 'undefined') {
        var versions = ['MSXML2.XMLHttp.6.0', 'MSXML2.XMLHttp.3.0', 'MSXML2.XMLHttp']
        for (var i = 0; i < versions.length; i++) {
          try {
            return new ActiveXObject(versions[i])
          } catch (e) { /*跳过*/
          }
        }
      } else {
        throw new Error('您的浏览器不支持XHR对象')
      }
    }

    var fromCharCode = String.fromCharCode
    var cb_utob = function (c) {
      if (c.length < 2) {
        var cc = c.charCodeAt(0)
        return cc < 0x80 ? c : cc < 0x800 ? (fromCharCode(0xc0 | (cc >>> 6)) + fromCharCode(0x80 | (cc & 0x3f))) : (fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) + fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) + fromCharCode(0x80 | (cc & 0x3f)))
      } else {
        var cc = 0x10000 + (c.charCodeAt(0) - 0xD800) * 0x400 + (c.charCodeAt(1) - 0xDC00)
        return (fromCharCode(0xf0 | ((cc >>> 18) & 0x07)) + fromCharCode(0x80 | ((cc >>> 12) & 0x3f)) + fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) + fromCharCode(0x80 | (cc & 0x3f)))
      }
    }
    var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g
    var utob = function (u) {
      return u.replace(re_utob, cb_utob)
    }
    var _encode = function (u) {
      var isUint8Array = Object.prototype.toString.call(u) === '[object Uint8Array]'
      if (isUint8Array) return u.toString('base64')
      else return btoa(utob(String(u)))
    }
    if (typeof btoa !== 'function') {
      window.btoa = function func_btoa(input) {
        var str = String(input)
        var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
        for (var block, charCode, idx = 0, map = chars, output = ''; str.charAt(idx | 0) || (map = '=', idx % 1); output += map.charAt(63 & block >> 8 - idx % 1 * 8)) {
          charCode = str.charCodeAt(idx += 3 / 4)
          if (charCode > 0xFF) {
            throw new Error('\'btoa\' failed: The string to be encoded contains characters outside of the Latin1 range.')
          }
          block = block << 8 | charCode
        }
        return output
      }
    }

    function encode(u, urisafe) {
      return !urisafe ? _encode(u) : _encode(String(u)).replace(/[+\/]/g, function (m0) {
        return m0 == '+' ? '-' : '_'
      }).replace(/=/g, '')
    }

    function IEVersion() {
      var userAgent = navigator.userAgent
      var isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1
      var isEdge = userAgent.indexOf('Edge') > -1 && !isIE
      var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1
      if (isIE) {
        var reIE = new RegExp('MSIE (\\d+\\.\\d+);')
        reIE.test(userAgent)
        var fIEVersion = parseFloat(RegExp['$1'])
        if (fIEVersion == 7) return 7
        else if (fIEVersion == 8) return 8
        else if (fIEVersion == 9) return 9
        else if (fIEVersion == 10) return 10
        else return 6
      } else if (isEdge) return 20
      else if (isIE11) return 11
      else return 30
    }

    function guid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      })
    }

    function InitWpsCloudSvr() {
      logToUI('尝试启动WPS Cloud Server')
      const url = serverId ? 'ksoWPSCloudSvr://start=RelayHttpServer' + '&serverId=' + serverId : 'ksoWPSCloudSvr://start=RelayHttpServer'
      window.location.href = url
    }

    function getServerId() {
      if (window.localStorage) {
        if (!localStorage.getItem('serverId')) {
          localStorage.setItem('serverId', guid())
        }
        return localStorage.getItem('serverId')
      } else {
        if (!window._tempServerId) {
          window._tempServerId = guid()
        }
        return window._tempServerId
      }
    }

    function startWps(req, t, callback) {
      function startWpsInner(reqInner, tryCount, bPop) {
        if (tryCount < 1) {
          logToUI('startWpsInner: 尝试次数用尽')
          if (callback) callback({ status: 2, message: '请允许浏览器打开WPS Office或检查WPS服务是否运行' })
          return
        }
        var bRetry = true
        var xmlReq = getHttpObj()
        logToUI(`WPS请求 [${reqInner.type}] 到 ${reqInner.url} (尝试 ${5 - tryCount}/4)`)
        xmlReq.open(reqInner.type, reqInner.url)

        // 添加 referer 请求头，如果 userData.nickname 不存在则使用默认值
        var refererValue = 'https://www.wanweiedu.com/'
        xmlReq.setRequestHeader('Referer', refererValue)

        xmlReq.onload = function (res) {
          logToUI(`WPS请求 ${reqInner.url} onload: status=${res.target.status}, responseText=${res.target.responseText.substring(0, 100)}`)
          if (res.target.status >= 200 && res.target.status < 300) {
            if (callback) callback({ status: 0, res: res })
          } else {
            var responseStr = IEVersion() < 10 ? xmlReq.responseText : res.target.response
            var errorMessageText = '请求失败'
            try {
              var errorData = JSON.parse(responseStr)
              errorMessageText = errorData.message || errorData.data || errorMessageText
              if (errorData.data == 'Subserver not available.' && tryCount == 4 && bPop) {
                logToUI('WPS子服务不可用，尝试启动WPS服务')
                InitWpsCloudSvr()
                setTimeout(function () {
                  if (bRetry) {
                    bRetry = false
                    startWpsInner(reqInner, --tryCount, false)
                  }
                }, 3000)
                return
              }
            } catch (e) {
              logToUI('解析WPS错误响应失败: ' + e.message + ', 原始响应: ' + responseStr)
              errorMessageText = responseStr || errorMessageText
            }
            if (callback) callback({
              status: 1,
              message: `WPS服务请求错误: ${res.target.status} - ${errorMessageText}`,
              res: res
            })
          }
        }
        xmlReq.ontimeout = xmlReq.onerror = function (resEvent) {
          logToUI(`WPS请求 ${reqInner.url} onerror/ontimeout. Type: ${resEvent.type}`)
          xmlReq.bTimeout = true
          if (bPop && tryCount > 1) {
            logToUI('WPS请求失败或超时，尝试启动WPS服务')
            InitWpsCloudSvr()
          }
          setTimeout(function () {
            if (bRetry) {
              bRetry = false
              startWpsInner(reqInner, --tryCount, false)
            }
          }, tryCount === 4 ? 3000 : 1000)
        }
        if (IEVersion() < 10) {
          xmlReq.onreadystatechange = function () {
            if (xmlReq.readyState != 4) return
            if (xmlReq.bTimeout) return
            if (xmlReq.status === 200) xmlReq.onload({ target: xmlReq })
            else xmlReq.onerror({ type: 'error' })
          }
        }
        xmlReq.timeout = 3000
        try {
          xmlReq.send(t)
        } catch (e) {
          logToUI('xmlReq.send 异常: ' + e.message)
          if (callback) callback({ status: 3, message: '发送请求到WPS服务失败: ' + e.message })
        }
      }

      startWpsInner(req, 4, true)
    }

    function checkAddonStatus() {
      logToUI('检查WPS插件状态...')
      if (addonInstallButton) {
        addonInstallButton.disabled = true
        addonInstallButton.textContent = '正在检查...'
      }

      function fetchPublishListAndUpdate() {
        var baseData = JSON.stringify({ serverId: getServerId() })
        var listReq = { url: 'http://127.0.0.1:58890/publishlist', type: 'POST' }
        logToUI('请求插件列表 /publishlist')
        startWps(listReq, baseData, function (listRes) {
          if (listRes.status === 0 && listRes.res && listRes.res.target) {
            try {
              var responseText = listRes.res.target.response
              logToUI('插件列表响应: ' + responseText.substring(0, 200))
              var installedAddons = JSON.parse(responseText)
              addonInstalled = installedAddons.some(function (addon) {
                return addon.name === (getAddonDisplayName() + '服务') && addon.url.includes('wps-addon-build') && addon.enable !== 'false'
              })
              updateAddonUI(addonInstalled)
            } catch (e) {
              logToUI('解析publishlist失败: ' + e.message + '. Response: ' + responseText)
              updateAddonUI(false)
            }
          } else {
            logToUI('获取publishlist失败: ' + (listRes.message || '未知错误'))
            updateAddonUI(false)
          }
          if (addonInstallButton) {
            addonInstallButton.disabled = false
            addonInstallButton.textContent = addonInstalled ? '卸载插件' : '安装插件'
          }
        })
      }

      if (serverVersion === 'wait') {
        var versionReq = { url: 'http://127.0.0.1:58890/version', type: 'POST' }
        logToUI('请求WPS服务版本 /version')
        startWps(versionReq, JSON.stringify({ serverId: getServerId() }), function (versionRes) {
          if (versionRes.status === 0 && versionRes.res && versionRes.res.target && versionRes.res.target.response) {
            serverVersion = versionRes.res.target.response
            logToUI('WPS Server Version: ' + serverVersion)
            fetchPublishListAndUpdate()
          } else {
            logToUI('获取WPS Server Version失败: ' + (versionRes.message || '未知错误'))
            serverVersion = 'error'
            updateAddonUI(false)
            if (addonInstallButton) {
              addonInstallButton.disabled = false
              addonInstallButton.textContent = '安装插件'
            }
            showTemporaryMessage('无法连接到WPS服务，请确保WPS已运行或尝试重启WPS。', false)
          }
        })
      } else if (serverVersion === 'error') {
        logToUI('之前获取WPS Server Version失败，跳过插件状态检查。')
        updateAddonUI(false)
        if (addonInstallButton) {
          addonInstallButton.disabled = false
          addonInstallButton.textContent = '安装插件'
        }
      } else {
        fetchPublishListAndUpdate()
      }
    }

    function updateAddonUI(installed) {
      addonInstalled = installed
      logToUI(`更新插件UI状态: ${installed ? '已安装' : '未安装'}`)
      if (addonStatus) {
        addonStatus.textContent = installed ? '已安装' : '未安装'
        addonStatus.className = installed ? 'wps-addon-status status-installed' : 'wps-addon-status status-not-installed'
      }
      if (addonInstallButton) {
        addonInstallButton.textContent = installed ? '卸载插件' : '安装插件'
      }
    }

    function installAddon() {
      logToUI(`尝试 ${addonInstalled ? '卸载' : '安装'} 插件`)
      if (serverVersion === 'wait') {
        showTemporaryMessage('WPS服务版本信息尚未获取，请稍后再试。', false)
        logToUI('安装/卸载插件失败: serverVersion 未就绪')
        return
      }
      if (serverVersion === 'error') {
        showTemporaryMessage('无法连接到WPS服务，操作失败。请检查WPS是否运行。', false)
        logToUI('安装/卸载插件失败: serverVersion 获取错误')
        return
      }
      var addonInfo = {
        'name': getAddonDisplayName(),
        'addonType': 'wps',
        'online': 'true',
        'multiUser': 'false',
        'url': getAddonUrl()
      }
      var cmd = addonInstalled ? 'disable' : 'enable'
      var data = FormartData(addonInfo, cmd)
      var req = { url: 'http://127.0.0.1:58890/deployaddons/runParams', type: 'POST' }
      logToUI(`发送 ${cmd} 命令到 /deployaddons/runParams`)
      if (addonInstallButton) addonInstallButton.disabled = true
      startWps(req, data, function (res) {
        if (addonInstallButton) addonInstallButton.disabled = false
        if (res.status === 0 && res.res && res.res.target) {
          if (res.res.target.response === 'OK' || (res.res.target.response === '' && res.res.target.status === 200)) {
            var successfullyChanged = !addonInstalled
            updateAddonUI(successfullyChanged)
            showTemporaryMessage(successfullyChanged ? '插件安装成功！' : '插件卸载成功！', true, true) // 使用弹窗
          } else {
            logToUI(`插件操作失败，响应: ${res.res.target.response}`)
            showTemporaryMessage(addonInstalled ? '插件卸载失败！' : '插件安装失败！原因: ' + res.res.target.response, false)
          }
        } else {
          logToUI(`插件操作请求失败: ${res.message}`)
          showTemporaryMessage(res.message || (addonInstalled ? '插件卸载请求失败！' : '插件安装请求失败！'), false)
        }
      })
    }

    // 新增：禁用所有插件的功能
    function disableAllAddons() {
      logToUI('尝试禁用所有插件')
      if (!confirm('确定要禁用所有WPS加载项吗？此操作无法单独撤销，需要重新安装特定插件。')) {
        logToUI('用户取消了禁用所有插件的操作。')
        return
      }

      if (serverVersion === 'wait') {
        showTemporaryMessage('WPS服务版本信息尚未获取，请稍后再试。', false)
        logToUI('禁用所有插件失败: serverVersion 未就绪')
        return
      }
      if (serverVersion === 'error') {
        showTemporaryMessage('无法连接到WPS服务，操作失败。请检查WPS是否运行。', false)
        logToUI('禁用所有插件失败: serverVersion 获取错误')
        return
      }

      var element = {} // 对于 disableall，name, url 等字段不需要
      var cmd = 'disableall'
      var data = FormartData(element, cmd) // FormartData 会处理空 element 的情况
      var req = { url: 'http://127.0.0.1:58890/deployaddons/runParams', type: 'POST' }
      logToUI(`发送 ${cmd} 命令到 /deployaddons/runParams`)

      if (disableAllAddonsButton) disableAllAddonsButton.disabled = true // 操作期间禁用按钮

      startWps(req, data, function (res) {
        if (disableAllAddonsButton) disableAllAddonsButton.disabled = false // 操作完成恢复按钮

        if (res.status === 0 && res.res && res.res.target) {
          if (res.res.target.response === 'OK' || (res.res.target.response === '' && res.res.target.status === 200)) {
            showTemporaryMessage('所有WPS加载项已成功禁用！', true, true) // 使用弹窗
            logToUI('所有插件已禁用，重新检查当前插件状态。')
            checkAddonStatus() // 重新检查并更新UI（特定插件的状态）
          } else {
            logToUI(`禁用所有插件失败，响应: ${res.res.target.response}`)
            showTemporaryMessage('禁用所有插件失败！原因: ' + res.res.target.response, false)
          }
        } else {
          logToUI(`禁用所有插件请求失败: ${res.message}`)
          showTemporaryMessage(res.message || '禁用所有插件的请求失败！', false)
        }
      })
    }


    function openWps() {
      logToUI('尝试打开WPS')
      showTemporaryMessage('正在尝试打开WPS...', true)
      window.ipcRenderer?.send('start-wps')
    }

    function FormartData(element, cmd) {
      var data = {
        'cmd': cmd,
        'name': element.name,
        'url': element.url,
        'addonType': element.addonType,
        'online': element.online,
        'version': element.version || '',
        'customDomain': element.customDomain || ''
      }
      return FormatSendData(data)
    }

    function FormatSendData(data) {
      var strData = JSON.stringify(data)
      if (serverVersion && serverVersion >= '1.0.2' && serverId != undefined) {
        var base64Data = encode(strData)
        logToUI('FormatSendData: 使用Base64编码 (serverVersion >= 1.0.2)')
        return JSON.stringify({ serverId: serverId, data: base64Data })
      } else {
        logToUI('FormatSendData: serverVersion < 1.0.2 或 serverId 未定义')
        if (serverVersion && serverVersion < '1.0.2') {
          logToUI('FormatSendData: serverVersion < 1.0.2, 返回纯Base64编码数据')
          return encode(strData)
        }
        logToUI('FormatSendData: serverVersion 未达到1.0.2或serverId未定义，返回原始JSON字符串（理论上不应发生）。')
        return strData
      }
    }

    // 事件监听
    addonInstallButton?.addEventListener('click', installAddon)
    openWpsButton?.addEventListener('click', openWps)
    disableAllAddonsButton?.addEventListener('click', disableAllAddons) // 新按钮的事件监听

    // 网络文件夹选择功能
    function selectNetworkFolder() {
      logToUI('用户点击选择网络设备按钮')
      try {
        if (selectNetworkFolderBtn) {
          selectNetworkFolderBtn.disabled = true
        }
        window.ipcRenderer?.send('select-network-folder')
      } catch (error) {
        logToUI(`selectNetworkFolder 函数执行错误: ${error.message}`)
        // 恢复按钮状态
        if (selectNetworkFolderBtn) {
          selectNetworkFolderBtn.disabled = false
        }
        showNetworkFolderMessage('操作失败，请重试', true)
      }
    }

    // 确认网络文件夹功能
    function confirmNetworkFolder() {
      logToUI('用户点击确认使用文件夹按钮')
      try {
        if (confirmNetworkFolderBtn) {
          confirmNetworkFolderBtn.disabled = true
          const selectedPath = confirmNetworkFolderBtn.getAttribute('data-selected-path')
          if (selectedPath) {
            window.ipcRenderer?.send('confirm-network-folder', { path: selectedPath })
          } else {
            logToUI('错误：未找到选择的路径')
            showNetworkFolderMessage('配置失败：未找到选择的路径', true)
            confirmNetworkFolderBtn.disabled = false
          }
        }
      } catch (error) {
        logToUI(`confirmNetworkFolder 函数执行错误: ${error.message}`)
        // 恢复按钮状态
        if (confirmNetworkFolderBtn) {
          confirmNetworkFolderBtn.disabled = false
        }
        showNetworkFolderMessage('操作失败，请重试', true)
      }
    }

    // 网络连接功能
    function retryNetworkConnection() {
      logToUI('用户点击重试网络连接按钮')
      try {
        if (retryNetworkConnectionBtn) {
          retryNetworkConnectionBtn.disabled = true
          retryNetworkConnectionBtn.textContent = '正在重试...'
        }
        window.ipcRenderer?.send('retry-network-connection')
      } catch (error) {
        logToUI(`retryNetworkConnection 函数执行错误: ${error.message}`)
        // 恢复按钮状态
        if (retryNetworkConnectionBtn) {
          retryNetworkConnectionBtn.disabled = false
          retryNetworkConnectionBtn.textContent = '重试连接'
        }
        showNetworkConnectionStatus('操作失败，请重试', true)
      }
    }

    function skipNetworkConnection() {
      logToUI('用户点击跳过网络连接按钮')
      try {
        if (skipNetworkConnectionBtn) {
          skipNetworkConnectionBtn.disabled = true
          skipNetworkConnectionBtn.textContent = '正在跳过...'
        }
        window.ipcRenderer?.send('skip-network-connection')
      } catch (error) {
        logToUI(`skipNetworkConnection 函数执行错误: ${error.message}`)
        // 恢复按钮状态
        if (skipNetworkConnectionBtn) {
          skipNetworkConnectionBtn.disabled = false
          skipNetworkConnectionBtn.textContent = '跳过网络连接'
        }
        showNetworkConnectionStatus('操作失败，请重试', true)
      }
    }

    // 帮助弹窗功能
    function showHelpDialog() {
      logToUI('显示帮助弹窗')
      if (helpModal) {
        helpModal.style.display = 'flex'
        // 重置帮助内容为加载状态
        if (helpBody) {
          helpBody.innerHTML = `
            <div class="loading-container" style="padding: 20px;">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载帮助信息...</div>
            </div>
          `
        }
        if (helpAutoEnableBtn) {
          helpAutoEnableBtn.style.display = 'none'
        }

        // 请求帮助信息
        window.ipcRenderer?.send('get-guest-login-help')
      }
    }

    function closeHelpDialog() {
      logToUI('关闭帮助弹窗')
      if (helpModal) {
        helpModal.style.display = 'none'
      }
    }

    function renderHelpContent(helpInfo) {
      logToUI('渲染帮助内容')
      if (!helpBody || !helpInfo) return

      // 清除之前可能存在的状态提示
      const existingStatus = helpBody.querySelector('.auto-enable-error, .auto-enable-success');
      if (existingStatus) {
        existingStatus.remove();
      }

      let html = `
        <div class="help-section">
          <h4>${helpInfo.title || '配置说明'}</h4>
          <p>${helpInfo.description || ''}</p>
        </div>
      `

      // 主要步骤
      if (helpInfo.steps && helpInfo.steps.length > 0) {
        html += `
          <div class="help-section">
            <h4>配置步骤</h4>
            <ol class="help-steps">
        `
        helpInfo.steps.forEach(step => {
          html += `<li>${step}</li>`
        })
        html += `
            </ol>
          </div>
        `
      }

      // 替代方法（注册表）
      if (helpInfo.alternativeMethod) {
        const altMethod = helpInfo.alternativeMethod
        html += `
          <div class="help-section">
            <h4>${altMethod.title || '替代方法'}</h4>
        `

        if (altMethod.warning) {
          html += `
            <div class="help-warning">
              <p><strong>⚠️ 警告：</strong>${altMethod.warning}</p>
            </div>
          `
        }

                 if (altMethod.steps && altMethod.steps.length > 0) {
           html += `<ol class="help-steps">`
           altMethod.steps.forEach(step => {
             if (step.includes('reg add ')) {
               // 查找包含reg add的步骤，分离描述和命令
               const regIndex = step.indexOf('reg add ')
               if (regIndex > 0) {
                 // 有描述文字的情况
                 html += `<li>${step.substring(0, regIndex).trim()}</li>`
                 html += `<div class="help-command">${step.substring(regIndex)}</div>`
               } else {
                 // 纯命令的情况，不显示序号
                 html += `<div class="help-command" style="margin: 8px 0;">${step}</div>`
               }
             } else if (step.toLowerCase().includes('执行以下命令') || step.toLowerCase().includes('execute')) {
               // 对于"执行以下命令"这种提示，不显示序号
               html += `<div style="margin: 8px 0; font-weight: 500; color: #333;">${step}</div>`
             } else {
               html += `<li>${step}</li>`
             }
           })
           html += `</ol>`
         }

        html += `</div>`
      }

      // 故障排查
      if (helpInfo.troubleshooting && helpInfo.troubleshooting.length > 0) {
        html += `
          <div class="help-troubleshooting">
            <h5>故障排查</h5>
            <ul>
        `
        helpInfo.troubleshooting.forEach(item => {
          html += `<li>${item}</li>`
        })
        html += `
            </ul>
          </div>
        `
      }

      helpBody.innerHTML = html

      // 显示自动启用按钮（如果是Windows环境）
      if (helpAutoEnableBtn && navigator.platform.includes('Win')) {
        helpAutoEnableBtn.style.display = 'inline-block'
      }
    }

    function autoEnableGuestLogin() {
      logToUI('尝试自动启用组策略')
      if (helpAutoEnableBtn) {
        helpAutoEnableBtn.disabled = true
        helpAutoEnableBtn.textContent = '正在启用...'
      }

      window.ipcRenderer?.send('enable-guest-login-policy')
    }

    // 更新弹窗功能
    function showUpdateDialog() {
      logToUI('显示更新弹窗')
      if (updateModal) {
        updateModal.style.display = 'flex'

        // 重置弹窗状态
        resetUpdateDialog()

        // 设置初始状态
        if (updateIcon) {
          updateIcon.textContent = '🔄'
          updateIcon.className = 'update-icon checking'
        }
        if (updateMessage) {
          updateMessage.textContent = '正在检查更新...'
        }

        // 隐藏所有可选元素
        hideAllUpdateElements()
      }
    }

    function hideAllUpdateElements() {
      if (updateVersionInfo) updateVersionInfo.style.display = 'none'
      if (updateReleaseNotes) updateReleaseNotes.style.display = 'none'
      if (updateProgressContainer) updateProgressContainer.style.display = 'none'
      if (updateDownloadBtn) updateDownloadBtn.style.display = 'none'
      if (updateInstallBtn) updateInstallBtn.style.display = 'none'
    }
    
    // 定义updateCancelBtn变量为null，避免引用未定义的变量
    const updateCancelBtn = null;

    function resetUpdateDialog() {
      // 重置进度条
      if (updateProgressFill) updateProgressFill.style.width = '0%'
      if (progressPercent) progressPercent.textContent = '0%'
      if (progressText) progressText.textContent = '正在下载...'
      if (updateSpeedInfo) updateSpeedInfo.textContent = '下载速度: 计算中...'

      // 重置按钮状态
      if (updateDownloadBtn) updateDownloadBtn.disabled = false
      if (updateInstallBtn) updateInstallBtn.disabled = false
    }

    function closeUpdateDialog() {
      logToUI('关闭更新弹窗')
      if (updateModal) {
        updateModal.style.display = 'none'
      }
    }

    function updateCheckComplete(data) {
      logToUI(`更新检查完成: ${JSON.stringify(data)}`)

      // 重置下载状态
      isDownloadCompleted = false

      if (!data.success) {
        // 检查失败
        if (updateIcon) {
          updateIcon.textContent = '❌'
          updateIcon.className = 'update-icon error'
        }
        if (updateMessage) {
          updateMessage.textContent = data.message || '检查更新失败'
        }
        return
      }

      if (data.hasUpdate) {
        // 发现新版本
        if (updateIcon) {
          updateIcon.textContent = '🎉'
          updateIcon.className = 'update-icon available'
        }
        if (updateMessage) {
          updateMessage.textContent = '发现新版本！'
        }

        // 显示版本信息
        if (updateVersionInfo) {
          updateVersionInfo.style.display = 'block'
          if (currentVersionElement && data.currentVersion) {
            currentVersionElement.textContent = `当前版本: ${data.currentVersion}`
          }
          if (newVersionElement && data.newVersion) {
            newVersionElement.textContent = `最新版本: ${data.newVersion}`
          }
        }

        // 显示更新日志
        if (updateReleaseNotes && data.releaseNotes) {
          updateReleaseNotes.style.display = 'block'
          if (updateReleaseNotesContent) {
            updateReleaseNotesContent.innerHTML = formatReleaseNotes(data.releaseNotes)
          }
        } else if (updateReleaseNotes) {
          // 隐藏更新日志区域如果没有内容
          updateReleaseNotes.style.display = 'none'
        }

        // 显示下载按钮
        if (updateDownloadBtn) {
          updateDownloadBtn.style.display = 'inline-block'
          updateDownloadBtn.textContent = '立即下载'
        }

      } else {
        // 已是最新版本
        if (updateIcon) {
          updateIcon.textContent = '✅'
          updateIcon.className = 'update-icon latest'
        }
        if (updateMessage) {
          updateMessage.textContent = '您使用的已是最新版本'
        }
      }
    }

    function updateDownloadProgress(data) {
      logToUI(`更新下载进度: ${JSON.stringify(data)}`)

      // 显示进度条
      if (updateProgressContainer) {
        updateProgressContainer.style.display = 'block'
      }

      // 更新图标为下载中
      if (updateIcon) {
        updateIcon.textContent = '⬇️'
        updateIcon.className = 'update-icon downloading'
      }

      // 更新消息
      if (updateMessage) {
        updateMessage.textContent = '正在下载更新...'
      }

      // 更新进度条
      if (updateProgressFill && data.percent !== undefined) {
        updateProgressFill.style.width = `${data.percent}%`
      }

      if (progressPercent && data.percent !== undefined) {
        progressPercent.textContent = `${Math.round(data.percent)}%`
      }

      // 更新下载信息
      if (progressText && data.transferred && data.total) {
        const transferred = formatBytes(data.transferred)
        const total = formatBytes(data.total)
        progressText.textContent = `${transferred} / ${total}`
      }

      if (updateSpeedInfo && data.bytesPerSecond) {
        const speed = formatBytes(data.bytesPerSecond)
        updateSpeedInfo.textContent = `下载速度: ${speed}/s`
      }

      // 隐藏所有按钮
      if (updateDownloadBtn) updateDownloadBtn.style.display = 'none'
      if (updateInstallBtn) updateInstallBtn.style.display = 'none'
    }

    function updateDownloadComplete(data) {
      logToUI(`更新下载完成: ${JSON.stringify(data)}`)

      if (data.success) {
        // 下载成功
        isDownloadCompleted = true

        if (updateIcon) {
          updateIcon.textContent = '✅'
          updateIcon.className = 'update-icon downloaded'
        }
        if (updateMessage) {
          updateMessage.textContent = '下载完成，准备安装'
        }

        // 隐藏进度条
        if (updateProgressContainer) {
          updateProgressContainer.style.display = 'none'
        }

        // 显示安装按钮和重新下载按钮，安装按钮在前
        if (updateInstallBtn) updateInstallBtn.style.display = 'inline-block'
        if (updateDownloadBtn) {
          updateDownloadBtn.style.display = 'inline-block'
          updateDownloadBtn.textContent = '重新下载'
        }

      } else {
        // 下载失败
        if (updateIcon) {
          updateIcon.textContent = '❌'
          updateIcon.className = 'update-icon error'
        }
        if (updateMessage) {
          updateMessage.textContent = data.message || '下载失败'
        }

        // 恢复下载按钮
        if (updateDownloadBtn) {
          updateDownloadBtn.style.display = 'inline-block'
          updateDownloadBtn.textContent = '立即下载'
        }
      }
    }

    // 格式化字节数
    function formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 格式化更新日志
    function formatReleaseNotes(releaseNotes) {
      if (!releaseNotes) {
        return '<p>暂无更新日志</p>'
      }

      // 如果是字符串，处理换行和基本格式
      if (typeof releaseNotes === 'string') {
        let formatted = releaseNotes

        // 转义HTML特殊字符
        formatted = formatted
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#x27;')

        // 处理Markdown风格的格式化
        formatted = formatted
          // 处理标题（### 标题 -> <h3>标题</h3>）
          .replace(/^### (.*$)/gm, '<h3>$1</h3>')
          .replace(/^## (.*$)/gm, '<h2>$1</h2>')
          .replace(/^# (.*$)/gm, '<h1>$1</h1>')
          
          // 处理粗体文本（**文本** -> <strong>文本</strong>）
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          
          // 处理代码块（`代码` -> <code>代码</code>）
          .replace(/`([^`]+)`/g, '<code>$1</code>')
          
          // 处理列表项（- 项目 -> <li>项目</li>）
          .replace(/^[\s]*[-\*\+] (.+)$/gm, '<li>$1</li>')
          
          // 将连续的<li>包装在<ul>中
          .replace(/(<li>.*<\/li>)/gs, (match) => {
            // 检查是否已经被<ul>包装
            if (!match.includes('<ul>')) {
              return '<ul>' + match + '</ul>'
            }
            return match
          })
          
          // 处理换行（双换行 -> <p>）
          .replace(/\n\n/g, '</p><p>')
          .replace(/\n/g, '<br>')

        // 包装在段落中
        if (formatted && !formatted.startsWith('<')) {
          formatted = '<p>' + formatted + '</p>'
        }

        return formatted
      }

      // 如果是对象或数组，尝试处理
      if (typeof releaseNotes === 'object') {
        try {
          return '<pre>' + JSON.stringify(releaseNotes, null, 2) + '</pre>'
        } catch (e) {
          return '<p>更新日志格式错误</p>'
        }
      }

      return '<p>暂无更新日志</p>'
    }

    // 状态弹窗功能
    function showStatusDialog(title, message, type = 'info', autoClose = false) {
      logToUI(`显示状态弹窗: ${title} - ${message}`)

      if (statusModal && statusTitle && statusIcon && statusMessage) {
        // 设置标题
        statusTitle.textContent = title

        // 设置图标和样式
        const iconMap = {
          success: { icon: '✅', class: 'success' },
          error: { icon: '❌', class: 'error' },
          warning: { icon: '⚠️', class: 'warning' },
          info: { icon: 'ℹ️', class: 'info' }
        }

        const iconConfig = iconMap[type] || iconMap.info
        statusIcon.textContent = iconConfig.icon
        statusIcon.className = `status-icon ${iconConfig.class}`

        // 设置消息内容
        statusMessage.innerHTML = message

        // 显示弹窗
        statusModal.style.display = 'flex'

        // 自动关闭（如果指定）
        if (autoClose) {
          setTimeout(() => {
            closeStatusDialog()
          }, 3000)
        }
      }
    }

    function closeStatusDialog() {
      logToUI('关闭状态弹窗')
      if (statusModal) {
        statusModal.style.display = 'none'
      }
    }

    // 网络连接按钮事件监听
    retryNetworkConnectionBtn?.addEventListener('click', retryNetworkConnection)
    skipNetworkConnectionBtn?.addEventListener('click', skipNetworkConnection)
    showHelpBtn?.addEventListener('click', showHelpDialog)

    // 网络文件夹按钮事件监听
    selectNetworkFolderBtn?.addEventListener('click', selectNetworkFolder)
    confirmNetworkFolderBtn?.addEventListener('click', confirmNetworkFolder)
    // skipNetworkFolderBtn?.addEventListener('click', skipNetworkFolder) // 按钮已注释，不需要事件监听

    // 监听版本号
    window.ipcRenderer?.on('app-version', (event, version) => {
      const versionElement = document.getElementById('version-number');
      if (versionElement) {
        versionElement.textContent = `V${version}`;
      }
      logToUI(`应用版本号: ${version}`);
    });

    // 监听应用配置
    window.ipcRenderer?.on('app-config', (event, config) => {
      logToUI(`收到应用配置: ${JSON.stringify(config)}`);

      // 更新配置
      if (config) {
        appConfig = { ...appConfig, ...config };
        logToUI(`更新应用配置: ${JSON.stringify(appConfig)}`);
        if (appConfig.EDITION === 'hexin') {
          appConfig.APP_NAME = '合心科技AI编辑WPS插件服务';
        } else {
          appConfig.APP_NAME = '万唯AI编辑WPS插件服务';
        }

        // 更新UI显示
        updateUINames();
      }
    });

    // 处理机器码相关逻辑
    // const appTitle = document.getElementById('app-title');
    const machineCodeModal = document.getElementById('machine-code-modal');
    const machineCodeClose = document.getElementById('machine-code-close');
    const machineCodeValue = document.getElementById('machine-code-value');
    const machineCodeInfo = document.getElementById('machine-code-info');
    const machineCodeRefresh = document.getElementById('machine-code-refresh');
    // 新增：监控文件夹相关DOM元素
    const currentMonitorDir = document.getElementById('current-monitor-dir');
    const resetMonitorFolderBtn = document.getElementById('reset-monitor-folder');

    // 连续点击计数器和定时器
    let clickCount = 0;
    let clickTimer = null;
    const CLICK_THRESHOLD = 500; // 毫秒内的点击才算连续
    const REQUIRED_CLICKS = 3; // 需要连续点击的次数

    // 点击标题栏的处理函数
    if (userGreeting) {
      userGreeting.addEventListener('click', (e) => {
        // 仅监听标题文本部分的点击，忽略版本号
        if (e.target === userGreeting || (e.target.parentNode === userGreeting && e.target.id !== 'version-number')) {
          clickCount++;

          // 清除之前的定时器
          if (clickTimer) {
            clearTimeout(clickTimer);
          }

          // 设置新定时器，如果在指定时间内没有足够的点击，重置计数
          clickTimer = setTimeout(() => {
            if (clickCount >= REQUIRED_CLICKS) {
              logToUI(`检测到连续${REQUIRED_CLICKS}次点击标题，显示机器码`);
              showMachineCodeDialog();
            }
            clickCount = 0;
          }, CLICK_THRESHOLD);
        }
      });
    }

    // 显示机器码弹窗
    function showMachineCodeDialog() {
      if (machineCodeModal) {
        machineCodeModal.style.display = 'flex';
        fetchMachineCode();
        // 新增：获取当前监控文件夹
        fetchCurrentMonitorDir();
      }
    }

    // 获取机器码
    function fetchMachineCode() {
      if (machineCodeValue) {
        machineCodeValue.textContent = '正在获取...';
      }
      if (machineCodeInfo) {
        machineCodeInfo.textContent = '';
      }

      window.ipcRenderer?.send('get-machine-code');
    }

    // 新增：获取当前监控文件夹
    function fetchCurrentMonitorDir() {
      if (currentMonitorDir) {
        currentMonitorDir.textContent = '正在获取...';
      }

      window.ipcRenderer?.send('get-current-monitor-dir');
    }

    // 新增：重新设置监控文件夹
    function resetMonitorFolder() {
      if (resetMonitorFolderBtn) {
        resetMonitorFolderBtn.disabled = true;
        resetMonitorFolderBtn.textContent = '正在选择...';
      }

      logToUI('开始重新设置监控文件夹');
      window.ipcRenderer?.send('select-network-folder');
    }

    // 刷新机器码
    function refreshMachineCode() {
      if (machineCodeRefresh) {
        machineCodeRefresh.disabled = true;
        machineCodeRefresh.textContent = '刷新中...';
      }
      if (machineCodeValue) {
        machineCodeValue.textContent = '正在刷新...';
      }

      window.ipcRenderer?.send('refresh-machine-code');
    }

    // 关闭机器码弹窗
    function closeMachineCodeDialog() {
      if (machineCodeModal) {
        machineCodeModal.style.display = 'none';
      }
    }

    // 监听获取机器码的响应
    window.ipcRenderer?.on('machine-code-response', (event, data) => {
      logToUI(`收到机器码响应: ${JSON.stringify(data)}`);

      if (machineCodeRefresh) {
        machineCodeRefresh.disabled = false;
        machineCodeRefresh.textContent = '刷新机器码';
      }

      if (data.success) {
        if (machineCodeValue) {
          machineCodeValue.textContent = data.code || '未获取到有效机器码';
        }
        if (machineCodeInfo) {
          const createdDate = data.createdAt ? new Date(data.createdAt).toLocaleString() : '未知';
          machineCodeInfo.textContent = `版本: ${data.version || '1'}, 创建时间: ${createdDate}`;
        }
      } else {
        if (machineCodeValue) {
          machineCodeValue.textContent = '获取失败';
        }
        if (machineCodeInfo) {
          machineCodeInfo.textContent = data.message || '未知错误';
        }
      }
    });

    // 监听刷新机器码的响应
    window.ipcRenderer?.on('machine-code-refresh-response', (event, data) => {
      logToUI(`收到刷新机器码响应: ${JSON.stringify(data)}`);

      if (machineCodeRefresh) {
        machineCodeRefresh.disabled = false;
        machineCodeRefresh.textContent = '刷新机器码';
      }

      if (data.success) {
        if (machineCodeValue) {
          machineCodeValue.textContent = data.code || '未获取到有效机器码';
        }
        if (machineCodeInfo) {
          const createdDate = data.createdAt ? new Date(data.createdAt).toLocaleString() : '未知';
          machineCodeInfo.textContent = `版本: ${data.version || '1'}, 创建时间: ${createdDate}`;
        }
      } else {
        if (machineCodeValue) {
          machineCodeValue.textContent = '刷新失败';
        }
        if (machineCodeInfo) {
          machineCodeInfo.textContent = data.message || '未知错误';
        }
      }
    });

    // 新增：监听当前监控文件夹响应
    window.ipcRenderer?.on('current-monitor-dir-response', (event, data) => {
      logToUI(`收到当前监控文件夹响应: ${JSON.stringify(data)}`);

      if (data.success) {
        if (currentMonitorDir) {
          currentMonitorDir.textContent = data.path || '未设置';
        }
      } else {
        if (currentMonitorDir) {
          currentMonitorDir.textContent = '获取失败';
        }
      }
    });

    // 新增：监听网络文件夹选择响应（在机器码弹窗模式下）
    window.ipcRenderer?.on('monitor-folder-reset-response', (event, data) => {
      logToUI(`收到监控文件夹重设响应: ${JSON.stringify(data)}`);

      // 恢复按钮状态
      if (resetMonitorFolderBtn) {
        resetMonitorFolderBtn.disabled = false;
        resetMonitorFolderBtn.textContent = '重新设置监控文件夹';
      }

      if (data.success) {
        if (currentMonitorDir) {
          currentMonitorDir.textContent = data.newPath || '设置成功';
        }
        showTemporaryMessage('监控文件夹重新设置成功！', true, true); // 使用弹窗
      } else {
        showTemporaryMessage(data.message || '监控文件夹设置失败', false);
      }
    });

    // 帮助相关IPC事件监听
    window.ipcRenderer?.on('guest-login-help-response', (event, data) => {
      logToUI(`收到帮助信息响应: ${JSON.stringify(data)}`);

      if (data.success && data.helpInfo) {
        renderHelpContent(data.helpInfo);
      } else {
        if (helpBody) {
          helpBody.innerHTML = `
            <div class="help-section">
              <h4>获取帮助信息失败</h4>
              <p>${data.message || '无法加载帮助信息，请重试。'}</p>
            </div>
          `;
        }
      }
    });

    window.ipcRenderer?.on('guest-login-policy-enable-response', (event, data) => {
      logToUI(`收到组策略启用响应: ${JSON.stringify(data)}`);

      // 恢复按钮状态
      if (helpAutoEnableBtn) {
        helpAutoEnableBtn.disabled = false;
        helpAutoEnableBtn.textContent = '尝试自动启用';
      }

      if (data.success) {
        showTemporaryMessage('组策略设置已启用！' + (data.needReboot ? ' 建议重启系统。' : ''), true);

        // 成功启用后，可以关闭帮助弹窗或显示成功信息
        if (helpBody) {
          // 移除可能存在的旧状态提示
          const existingStatus = helpBody.querySelector('.auto-enable-error, .auto-enable-success');
          if (existingStatus) {
            existingStatus.remove();
          }

          const successHtml = `
            <div class="help-section auto-enable-success">
              <h4>✅ 设置成功</h4>
              <p>不安全的来宾登录已成功启用。</p>
              ${data.needReboot ? '<p style="color: #e67e22;"><strong>建议重启计算机以确保设置完全生效。</strong></p>' : ''}
              <p>您现在可以重试网络连接。</p>
            </div>
          `;

          // 将成功信息插入到内容顶部
          helpBody.insertAdjacentHTML('afterbegin', successHtml);
        }

        // 隐藏自动启用按钮
        if (helpAutoEnableBtn) {
          helpAutoEnableBtn.style.display = 'none';
        }
      } else {
        showTemporaryMessage('自动启用失败：' + (data.message || '未知错误'), false);

        // 在帮助内容中显示错误信息，但先检查是否已存在错误提示
        if (helpBody) {
          // 移除可能存在的旧错误提示
          const existingError = helpBody.querySelector('.auto-enable-error');
          if (existingError) {
            existingError.remove();
          }

          const errorHtml = `
            <div class="help-section auto-enable-error">
              <h4>❌ 自动启用失败</h4>
              <p>${data.message || '自动启用组策略失败，请尝试手动配置。'}</p>
            </div>
          `;

          // 将错误信息插入到内容顶部
          helpBody.insertAdjacentHTML('afterbegin', errorHtml);
        }
      }
    });

    // 添加事件监听器
    if (machineCodeClose) {
      machineCodeClose.addEventListener('click', closeMachineCodeDialog);
    }
    if (machineCodeRefresh) {
      machineCodeRefresh.addEventListener('click', refreshMachineCode);
    }
    // 新增：重新设置监控文件夹按钮事件监听
    if (resetMonitorFolderBtn) {
      resetMonitorFolderBtn.addEventListener('click', resetMonitorFolder);
    }
    if (machineCodeModal) {
      machineCodeModal.addEventListener('click', (e) => {
        // 点击弹窗外部区域关闭弹窗
        if (e.target === machineCodeModal) {
          closeMachineCodeDialog();
        }
      });
    }

    // 帮助弹窗事件监听器
    if (helpClose) {
      helpClose.addEventListener('click', closeHelpDialog);
    }
    if (helpCloseBtn) {
      helpCloseBtn.addEventListener('click', closeHelpDialog);
    }
    if (helpAutoEnableBtn) {
      helpAutoEnableBtn.addEventListener('click', autoEnableGuestLogin);
    }
    if (helpModal) {
      helpModal.addEventListener('click', (e) => {
        // 点击弹窗外部区域关闭弹窗
        if (e.target === helpModal) {
          closeHelpDialog();
        }
      });
    }

    // 更新弹窗事件监听器
    if (updateClose) {
      updateClose.addEventListener('click', closeUpdateDialog);
    }
    if (updateDownloadBtn) {
      updateDownloadBtn.addEventListener('click', () => {
        const buttonText = updateDownloadBtn.textContent
        logToUI(`点击下载按钮: ${buttonText}`)

        // 重置下载完成状态
        isDownloadCompleted = false

        // 立即显示下载界面
        if (updateProgressContainer) {
          updateProgressContainer.style.display = 'block'
        }

        // 更新图标为下载中
        if (updateIcon) {
          updateIcon.textContent = '⬇️'
          updateIcon.className = 'update-icon downloading'
        }

        // 更新消息
        if (updateMessage) {
          updateMessage.textContent = '正在准备下载...'
        }

        // 初始化进度条
        if (updateProgressFill) {
          updateProgressFill.style.width = '……'
        }
        if (progressPercent) {
          progressPercent.textContent = '……'
        }
        if (progressText) {
          progressText.textContent = '0 B / …… MB'
        }
        if (updateSpeedInfo) {
          updateSpeedInfo.textContent = '下载速度: 准备中...'
        }

        // 隐藏所有按钮
        if (updateDownloadBtn) updateDownloadBtn.style.display = 'none'
        if (updateInstallBtn) updateInstallBtn.style.display = 'none'

        window.ipcRenderer?.send('download-update');
      });
    }
    if (updateInstallBtn) {
      updateInstallBtn.addEventListener('click', () => {
        logToUI('点击立即安装按钮');
        window.ipcRenderer?.send('install-update');
        closeUpdateDialog();
      });
    }
          // 稍后按钮已移除
      // if (updateLaterBtn) {
      //   updateLaterBtn.addEventListener('click', () => {
      //     logToUI('点击稍后按钮');
      //     closeUpdateDialog();
      //   });
      // }


    // 状态弹窗事件监听器
    if (statusClose) {
      statusClose.addEventListener('click', closeStatusDialog);
    }
    if (statusOkBtn) {
      statusOkBtn.addEventListener('click', closeStatusDialog);
    }
    if (statusModal) {
      statusModal.addEventListener('click', (e) => {
        // 点击弹窗外部区域关闭弹窗
        if (e.target === statusModal) {
          closeStatusDialog();
        }
      });
    }

    // 启动失败相关事件监听
    window.ipcRenderer?.on('wps-start-error', (event, data) => {
      logToUI(`收到WPS启动失败事件: ${JSON.stringify(data)}`)
      
      showStartupError({
        title: 'WPS启动失败',
        message: '启动WPS时发生错误，请尝试手动开启 wps',
        // details: data.details || data.error || null,
        // retryText: '重新启动WPS'
      })
    })

    window.ipcRenderer?.on('app-startup-error', (event, data) => {
      logToUI(`收到应用启动失败事件: ${JSON.stringify(data)}`)
      
      showStartupError({
        title: '应用启动失败',
        message: data.message || '应用启动过程中遇到问题，请尝试重启应用。',
        details: data.details || data.error || null,
        retryText: '重试启动'
      })
    })

    window.ipcRenderer?.on('server-start-error', (event, data) => {
      logToUI(`收到服务器启动失败事件: ${JSON.stringify(data)}`)
      
      showStartupError({
        title: '服务器启动失败',
        message: data.message || '内置服务器启动失败，这可能会影响部分功能。',
        details: data.details || data.error || null,
        retryText: '重试连接'
      })
    })

    // 启动失败按钮处理
    function handleStartupRetry() {
      logToUI('用户点击重试启动按钮')
      
      if (startupRetryBtn) {
        startupRetryBtn.disabled = true
        startupRetryBtn.textContent = '正在重试...'
      }

      // 隐藏错误界面，显示加载界面
      hideStartupError()
      showLoading('正在重试启动...')

      // 延迟重新检查登录状态（模拟重启过程）
      setTimeout(() => {
        // 重新开始应用启动流程
        location.reload() // 简单的重启方式
      }, 1000)
    }

    function handleStartupRestart() {
      logToUI('用户点击重启应用按钮')
      
      if (startupRestartBtn) {
        startupRestartBtn.disabled = true
        startupRestartBtn.textContent = '正在重启...'
      }

      // 刷新页面来重启应用
      location.reload()
    }

    function copyErrorInfo() {
      logToUI('用户点击复制错误信息按钮')
      
      if (startupErrorDetails && startupErrorDetails.textContent) {
        // 尝试复制到剪贴板
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(startupErrorDetails.textContent).then(() => {
            showTemporaryMessage('错误信息已复制到剪贴板', true, true)
          }).catch(err => {
            logToUI(`复制到剪贴板失败: ${err.message}`)
            showTemporaryMessage('复制失败，请手动选择文本复制', false)
          })
        } else {
          // 降级方案：选择文本
          try {
            const range = document.createRange()
            range.selectNode(startupErrorDetails)
            window.getSelection().removeAllRanges()
            window.getSelection().addRange(range)
            showTemporaryMessage('错误信息已选择，请按Ctrl+C复制', true)
          } catch (err) {
            logToUI(`选择文本失败: ${err.message}`)
            showTemporaryMessage('复制失败', false)
          }
        }
      }
    }

    // 更新相关IPC事件监听
    window.ipcRenderer?.on('update-check-result', (event, data) => {
      logToUI(`收到更新检查结果: ${JSON.stringify(data)}`);
      
      // 判断是否是自动检查更新或手动检查更新
      const isAutoCheck = data.autoCheck === true;
      logToUI(`更新检查类型: ${isAutoCheck ? '自动' : '手动'}`);
      
      // 如果是自动检查且有更新，则显示更新弹窗
      if (isAutoCheck) {
        if (data.success && data.hasUpdate) {
          logToUI('自动检查发现更新，显示更新弹窗');
          showUpdateDialog();
          updateCheckComplete(data);
        } else {
          logToUI('自动检查无更新或检查失败，不显示弹窗');
        }
      } else {
        // 手动检查更新，先显示弹窗，再更新内容
        showUpdateDialog();
        updateCheckComplete(data);
      }
    });

    window.ipcRenderer?.on('update-download-progress', (event, data) => {
      logToUI(`收到更新下载进度: ${JSON.stringify(data)}`);
      updateDownloadProgress(data);
    });

    window.ipcRenderer?.on('update-download-complete', (event, data) => {
      logToUI(`收到更新下载完成: ${JSON.stringify(data)}`);
      updateDownloadComplete(data);
    });

    window.ipcRenderer?.on('update-error', (event, data) => {
      logToUI(`收到更新错误: ${JSON.stringify(data)}`);

      // 显示错误状态
      if (updateIcon) {
        updateIcon.textContent = '❌';
        updateIcon.className = 'update-icon error';
      }
      if (updateMessage) {
        updateMessage.textContent = data.message || '更新过程中发生错误';
      }

      // 恢复按钮状态
      if (updateDownloadBtn) {
        updateDownloadBtn.disabled = false;
        updateDownloadBtn.style.display = 'inline-block';
        updateDownloadBtn.textContent = isDownloadCompleted ? '重新下载' : '立即下载';
      }
      if (isDownloadCompleted && updateInstallBtn) {
        updateInstallBtn.style.display = 'inline-block';
      }
    });

    serverId = getServerId()
    logToUI('serverId 初始化为: ' + serverId)

    // 启动失败按钮事件监听
    if (startupRetryBtn) {
      startupRetryBtn.addEventListener('click', handleStartupRetry)
    }
    if (startupRestartBtn) {
      startupRestartBtn.addEventListener('click', handleStartupRestart)
    }
    if (startupCopyErrorBtn) {
      startupCopyErrorBtn.addEventListener('click', copyErrorInfo)
    }
    if (startupErrorClose) {
      startupErrorClose.addEventListener('click', hideStartupError)
    }
    // 点击弹窗背景关闭弹窗
    if (startupErrorModal) {
      startupErrorModal.addEventListener('click', (e) => {
        if (e.target === startupErrorModal) {
          hideStartupError()
        }
      })
    }

    // 初始化UI名称（使用默认配置）
    updateUINames()

    document.addEventListener('keydown', (e) => {
      if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'i'))) {
        logToUI('检测到开发者工具快捷键')
        window.ipcRenderer?.send('open-devtools')
      }
    })
    window.addEventListener('beforeunload', () => {
      clearTimeout(loadingTimeout)
      clearTimeout(let_messageTimeout)
    })

  </script>
</body>

</html>
