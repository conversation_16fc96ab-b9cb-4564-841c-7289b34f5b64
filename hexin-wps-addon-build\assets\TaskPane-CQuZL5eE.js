import{U as tn,r as pe,h as mt,v as Ne,i as Z,j as Mt,k as Os,m as Nt,_ as Is,n as sn,o as J,c as Y,a as f,p as nn,t as de,f as ne,q as Ie,w as Be,s as Ft,e as Jt,F as _t,u as At,x as bt,y as an,z as he,A as Yt,B as ps,C as pt,D as rn,E as on}from"./index-DFsvZdb2.js";function ln(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=tn.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let n=window.Application.GetTaskPane(t);n.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let n=window.Application.GetTaskPane(t);n.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let n=window.Application.GetTaskPane(t);n.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let n=window.Application.Selection.Range;n&&n.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const cn={onbuttonclick:ln};var un=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function pn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function dn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function n(){return this instanceof n?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}),t}var Rs={exports:{}};const fn={},hn=Object.freeze(Object.defineProperty({__proto__:null,default:fn},Symbol.toStringTag,{value:"Module"})),ds=dn(hn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",n=typeof window=="object",o=n?window:{};o.JS_SHA1_NO_WINDOW&&(n=!1);var g=!n&&typeof self=="object",T=!o.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;T?o=un:g&&(o=self);var S=!o.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,y=!o.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",C="0123456789abcdef".split(""),A=[-**********,8388608,32768,128],F=[24,16,8,0],W=["hex","array","digest","arrayBuffer"],R=[],H=Array.isArray;(o.JS_SHA1_NO_NODE_JS||!H)&&(H=function(u){return Object.prototype.toString.call(u)==="[object Array]"});var K=ArrayBuffer.isView;y&&(o.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!K)&&(K=function(u){return typeof u=="object"&&u.buffer&&u.buffer.constructor===ArrayBuffer});var V=function(u){var w=typeof u;if(w==="string")return[u,!0];if(w!=="object"||u===null)throw new Error(s);if(y&&u.constructor===ArrayBuffer)return[new Uint8Array(u),!1];if(!H(u)&&!K(u))throw new Error(s);return[u,!1]},ee=function(u){return function(w){return new P(!0).update(w)[u]()}},te=function(){var u=ee("hex");T&&(u=z(u)),u.create=function(){return new P},u.update=function(k){return u.create().update(k)};for(var w=0;w<W.length;++w){var m=W[w];u[m]=ee(m)}return u},z=function(u){var w=ds,m=ds.Buffer,k;m.from&&!o.JS_SHA1_NO_BUFFER_FROM?k=m.from:k=function(_){return new m(_)};var $=function(_){if(typeof _=="string")return w.createHash("sha1").update(_,"utf8").digest("hex");if(_==null)throw new Error(s);return _.constructor===ArrayBuffer&&(_=new Uint8Array(_)),H(_)||K(_)||_.constructor===m?w.createHash("sha1").update(k(_)).digest("hex"):u(_)};return $},d=function(u){return function(w,m){return new ae(w,!0).update(m)[u]()}},B=function(){var u=d("hex");u.create=function(k){return new ae(k)},u.update=function(k,$){return u.create(k).update($)};for(var w=0;w<W.length;++w){var m=W[w];u[m]=d(m)}return u};function P(u){u?(R[0]=R[16]=R[1]=R[2]=R[3]=R[4]=R[5]=R[6]=R[7]=R[8]=R[9]=R[10]=R[11]=R[12]=R[13]=R[14]=R[15]=0,this.blocks=R):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}P.prototype.update=function(u){if(this.finalized)throw new Error(t);var w=V(u);u=w[0];for(var m=w[1],k,$=0,_,L=u.length||0,E=this.blocks;$<L;){if(this.hashed&&(this.hashed=!1,E[0]=this.block,this.block=E[16]=E[1]=E[2]=E[3]=E[4]=E[5]=E[6]=E[7]=E[8]=E[9]=E[10]=E[11]=E[12]=E[13]=E[14]=E[15]=0),m)for(_=this.start;$<L&&_<64;++$)k=u.charCodeAt($),k<128?E[_>>>2]|=k<<F[_++&3]:k<2048?(E[_>>>2]|=(192|k>>>6)<<F[_++&3],E[_>>>2]|=(128|k&63)<<F[_++&3]):k<55296||k>=57344?(E[_>>>2]|=(224|k>>>12)<<F[_++&3],E[_>>>2]|=(128|k>>>6&63)<<F[_++&3],E[_>>>2]|=(128|k&63)<<F[_++&3]):(k=65536+((k&1023)<<10|u.charCodeAt(++$)&1023),E[_>>>2]|=(240|k>>>18)<<F[_++&3],E[_>>>2]|=(128|k>>>12&63)<<F[_++&3],E[_>>>2]|=(128|k>>>6&63)<<F[_++&3],E[_>>>2]|=(128|k&63)<<F[_++&3]);else for(_=this.start;$<L&&_<64;++$)E[_>>>2]|=u[$]<<F[_++&3];this.lastByteIndex=_,this.bytes+=_-this.start,_>=64?(this.block=E[16],this.start=_-64,this.hash(),this.hashed=!0):this.start=_}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},P.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var u=this.blocks,w=this.lastByteIndex;u[16]=this.block,u[w>>>2]|=A[w&3],this.block=u[16],w>=56&&(this.hashed||this.hash(),u[0]=this.block,u[16]=u[1]=u[2]=u[3]=u[4]=u[5]=u[6]=u[7]=u[8]=u[9]=u[10]=u[11]=u[12]=u[13]=u[14]=u[15]=0),u[14]=this.hBytes<<3|this.bytes>>>29,u[15]=this.bytes<<3,this.hash()}},P.prototype.hash=function(){var u=this.h0,w=this.h1,m=this.h2,k=this.h3,$=this.h4,_,L,E,q=this.blocks;for(L=16;L<80;++L)E=q[L-3]^q[L-8]^q[L-14]^q[L-16],q[L]=E<<1|E>>>31;for(L=0;L<20;L+=5)_=w&m|~w&k,E=u<<5|u>>>27,$=E+_+$+1518500249+q[L]<<0,w=w<<30|w>>>2,_=u&w|~u&m,E=$<<5|$>>>27,k=E+_+k+1518500249+q[L+1]<<0,u=u<<30|u>>>2,_=$&u|~$&w,E=k<<5|k>>>27,m=E+_+m+1518500249+q[L+2]<<0,$=$<<30|$>>>2,_=k&$|~k&u,E=m<<5|m>>>27,w=E+_+w+1518500249+q[L+3]<<0,k=k<<30|k>>>2,_=m&k|~m&$,E=w<<5|w>>>27,u=E+_+u+1518500249+q[L+4]<<0,m=m<<30|m>>>2;for(;L<40;L+=5)_=w^m^k,E=u<<5|u>>>27,$=E+_+$+1859775393+q[L]<<0,w=w<<30|w>>>2,_=u^w^m,E=$<<5|$>>>27,k=E+_+k+1859775393+q[L+1]<<0,u=u<<30|u>>>2,_=$^u^w,E=k<<5|k>>>27,m=E+_+m+1859775393+q[L+2]<<0,$=$<<30|$>>>2,_=k^$^u,E=m<<5|m>>>27,w=E+_+w+1859775393+q[L+3]<<0,k=k<<30|k>>>2,_=m^k^$,E=w<<5|w>>>27,u=E+_+u+1859775393+q[L+4]<<0,m=m<<30|m>>>2;for(;L<60;L+=5)_=w&m|w&k|m&k,E=u<<5|u>>>27,$=E+_+$-1894007588+q[L]<<0,w=w<<30|w>>>2,_=u&w|u&m|w&m,E=$<<5|$>>>27,k=E+_+k-1894007588+q[L+1]<<0,u=u<<30|u>>>2,_=$&u|$&w|u&w,E=k<<5|k>>>27,m=E+_+m-1894007588+q[L+2]<<0,$=$<<30|$>>>2,_=k&$|k&u|$&u,E=m<<5|m>>>27,w=E+_+w-1894007588+q[L+3]<<0,k=k<<30|k>>>2,_=m&k|m&$|k&$,E=w<<5|w>>>27,u=E+_+u-1894007588+q[L+4]<<0,m=m<<30|m>>>2;for(;L<80;L+=5)_=w^m^k,E=u<<5|u>>>27,$=E+_+$-899497514+q[L]<<0,w=w<<30|w>>>2,_=u^w^m,E=$<<5|$>>>27,k=E+_+k-899497514+q[L+1]<<0,u=u<<30|u>>>2,_=$^u^w,E=k<<5|k>>>27,m=E+_+m-899497514+q[L+2]<<0,$=$<<30|$>>>2,_=k^$^u,E=m<<5|m>>>27,w=E+_+w-899497514+q[L+3]<<0,k=k<<30|k>>>2,_=m^k^$,E=w<<5|w>>>27,u=E+_+u-899497514+q[L+4]<<0,m=m<<30|m>>>2;this.h0=this.h0+u<<0,this.h1=this.h1+w<<0,this.h2=this.h2+m<<0,this.h3=this.h3+k<<0,this.h4=this.h4+$<<0},P.prototype.hex=function(){this.finalize();var u=this.h0,w=this.h1,m=this.h2,k=this.h3,$=this.h4;return C[u>>>28&15]+C[u>>>24&15]+C[u>>>20&15]+C[u>>>16&15]+C[u>>>12&15]+C[u>>>8&15]+C[u>>>4&15]+C[u&15]+C[w>>>28&15]+C[w>>>24&15]+C[w>>>20&15]+C[w>>>16&15]+C[w>>>12&15]+C[w>>>8&15]+C[w>>>4&15]+C[w&15]+C[m>>>28&15]+C[m>>>24&15]+C[m>>>20&15]+C[m>>>16&15]+C[m>>>12&15]+C[m>>>8&15]+C[m>>>4&15]+C[m&15]+C[k>>>28&15]+C[k>>>24&15]+C[k>>>20&15]+C[k>>>16&15]+C[k>>>12&15]+C[k>>>8&15]+C[k>>>4&15]+C[k&15]+C[$>>>28&15]+C[$>>>24&15]+C[$>>>20&15]+C[$>>>16&15]+C[$>>>12&15]+C[$>>>8&15]+C[$>>>4&15]+C[$&15]},P.prototype.toString=P.prototype.hex,P.prototype.digest=function(){this.finalize();var u=this.h0,w=this.h1,m=this.h2,k=this.h3,$=this.h4;return[u>>>24&255,u>>>16&255,u>>>8&255,u&255,w>>>24&255,w>>>16&255,w>>>8&255,w&255,m>>>24&255,m>>>16&255,m>>>8&255,m&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,$>>>24&255,$>>>16&255,$>>>8&255,$&255]},P.prototype.array=P.prototype.digest,P.prototype.arrayBuffer=function(){this.finalize();var u=new ArrayBuffer(20),w=new DataView(u);return w.setUint32(0,this.h0),w.setUint32(4,this.h1),w.setUint32(8,this.h2),w.setUint32(12,this.h3),w.setUint32(16,this.h4),u};function ae(u,w){var m,k=V(u);if(u=k[0],k[1]){var $=[],_=u.length,L=0,E;for(m=0;m<_;++m)E=u.charCodeAt(m),E<128?$[L++]=E:E<2048?($[L++]=192|E>>>6,$[L++]=128|E&63):E<55296||E>=57344?($[L++]=224|E>>>12,$[L++]=128|E>>>6&63,$[L++]=128|E&63):(E=65536+((E&1023)<<10|u.charCodeAt(++m)&1023),$[L++]=240|E>>>18,$[L++]=128|E>>>12&63,$[L++]=128|E>>>6&63,$[L++]=128|E&63);u=$}u.length>64&&(u=new P(!0).update(u).array());var q=[],fe=[];for(m=0;m<64;++m){var ge=u[m]||0;q[m]=92^ge,fe[m]=54^ge}P.call(this,w),this.update(fe),this.oKeyPad=q,this.inner=!0,this.sharedMemory=w}ae.prototype=new P,ae.prototype.finalize=function(){if(P.prototype.finalize.call(this),this.inner){this.inner=!1;var u=this.array();P.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(u),P.prototype.finalize.call(this)}};var le=te();le.sha1=le,le.sha1.hmac=B(),S?e.exports=le:o.sha1=le})()})(Rs);var vn=Rs.exports;const gn=pn(vn);function fs(){return"http://worksheet.hexinedu.com"}function wt(){return"http://127.0.0.1:3000"}function hs(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const yt=async(e,s,t,n={},o=8e3)=>{try{return await Promise.race([e(),new Promise((g,T)=>setTimeout(()=>T(new Error("WebSocket请求超时，切换到HTTP")),o))])}catch{try{let T;return s==="get"?T=await Mt.get(t,{params:n}):s==="post"?T=await Mt.post(t,n):s==="delete"&&(T=await Mt.delete(t)),T.data}catch(T){throw new Error(`请求失败: ${T.message||"未知错误"}`)}}};function mn(e,s,t,n){const o=[e,s,t,n].join(":");return gn(o)}function bn(){const e=pe(""),s=pe(""),t=pe(""),n=mt({}),o=pe(""),g=pe("");let T="",S=null;const y=pe("c:\\Temp"),C=mt({appKey:"",appSecret:""}),A=mt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),F=pe(""),W=pe("junior"),R=pe(null),H=pe(!1),K=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],V=()=>Ne.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],ee=mt(V()),te=async()=>{try{const a=await Z.getWatcherStatus();a.data&&a.data.watchDir&&(y.value=a.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${y.value}</span><br/>`)}catch(a){t.value+=`<span class="log-item error">获取监控目录失败: ${a.message}</span><br/>`,console.error("获取监控目录失败:",a)}},z=async()=>{var a,r,l;try{if(!C.appKey||!C.appSecret)throw new Error("未初始化app信息");const v=60,i=Date.now();let h;try{const X=window.Application.PluginStorage.getItem("token_info");X&&(h=JSON.parse(X))}catch(X){h=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${X.message}</span><br/>`}if(h&&h.access_token&&h.expired_time>i+v*1e3)return h.access_token;const c=C.appKey,O="1234567",M=Math.floor(Date.now()/1e3),D=mn(c,O,C.appSecret,M),U=await Mt.get(fs()+"/api/open/account/v1/auth/token",{params:{app_key:c,app_nonstr:O,app_timestamp:M,app_signature:D}});if((r=(a=U.data)==null?void 0:a.data)!=null&&r.access_token){const X=U.data.data.access_token;let se;if(U.data.data.expired_time){const Q=parseInt(U.data.data.expired_time);se=i+Q*1e3,t.value+=`<span class="log-item info">token已更新，有效期${Q}秒</span><br/>`}else se=i+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const ie={access_token:X,expired_time:se};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(ie))}catch(Q){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${Q.message}</span><br/>`}return X}else throw new Error(((l=U.data)==null?void 0:l.message)||"获取access_token失败")}catch(v){throw t.value+=`<span class="log-item error">获取access_token失败: ${v.message}</span><br/>`,v}},d=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},B=()=>{const a=window.Application,r=a.Documents.Count;for(let l=1;l<=r;l++){const v=a.Documents.Item(l);if(v.DocID===o.value)return v}return null},P=()=>{try{const a=B();if(!a)return{isValid:!1,message:"未找到当前文档"};let r="";try{r=a.Name||""}catch{try{r=$("getDocName")||""}catch{r=""}}if(r){const l=r.toLowerCase();return l.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:l.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(a){return console.error("检查文档格式时出错:",a),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},ae=(a,r="",l=0)=>{try{const v=window.Application,i=B(),h=i.ActiveWindow.Selection;if(!h||h.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;if(r){const c=h.Range,O=h.Text,M=c.Find;M.ClearFormatting(),M.Text=r,M.Forward=!0,M.Wrap=1;let D=0,U=[];for(;M.Execute()&&(M.Found&&c.Start<=M.Parent.Start&&M.Parent.End<=c.End);){const X=M.Parent.Start,se=M.Parent.End;if(U.some(Q=>X===Q.start&&se===Q.end))M.Parent.Start=se;else{if(U.push({start:X,end:se}),l===-1||D===l){const Q=i.Comments.Add(M.Parent,a);if(l!==-1&&D===l)return t.value+=`<span class="log-item success">已为第${l+1}个"${r}"添加批注: "${a}"</span><br/>`,!0}D++,M.Parent.Start=se}}return l!==-1&&D<=l?(t.value+=`<span class="log-item error">在选中内容中未找到第${l+1}个"${r}"</span><br/>`,!1):l===-1&&D>0?(t.value+=`<span class="log-item success">已为${D}处"${r}"添加批注: "${a}"</span><br/>`,!0):l===-1&&D===0?(t.value+=`<span class="log-item error">在选中内容中未找到关键字"${r}"</span><br/>`,!1):!0}else{const c=i.Comments.Add(h.Range,a);return t.value+=`<span class="log-item success">已为选中内容添加批注: "${a}"</span><br/>`,!0}}catch(v){return t.value+=`<span class="log-item error">添加批注失败: ${v.message}</span><br/>`,!1}},le=a=>a===1?"status-running":a===2?"status-completed":a===-1?"status-error":a===3?"status-released":a===4?"status-stopped":"",u=a=>a===1?"进行中":a===2?"已完成":a===-1?"异常":a===3?"已释放":a===4?"已停止":"进行中",w=a=>{const r=Date.now()-a,l=Math.floor(r/1e3);return l<60?`${l}秒`:l<3600?`${Math.floor(l/60)}分${l%60}秒`:`${Math.floor(l/3600)}时${Math.floor(l%3600/60)}分`},m=async a=>{try{if(!n[a]){t.value+=`<span class="log-item error">找不到任务${a.substring(0,8)}的数据</span><br/>`;return}n[a].status=4,n[a].terminated=!0,n[a].errorMessage="用户选择不继续";try{const l=B();if(l&&l.ContentControls)for(let v=1;v<=l.ContentControls.Count;v++)try{const i=l.ContentControls.Item(v);if(i&&i.Title&&(i.Title===`任务_${a}`||i.Title===`任务增强_${a}`||i.Title===`校对_${a}`)){const h=i.Title===`任务增强_${a}`||n[a].isEnhanced,c=i.Title===`校对_${a}`||n[a].isCheckTask;c?i.Title=`已停止校对_${a}`:h?i.Title=`已停止增强_${a}`:i.Title=`已停止_${a}`;const O=c?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${a.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(l){t.value+=`<span class="log-item warning">更新控件标题失败: ${l.message}</span><br/>`}let r=null;if(G[a]&&G[a].urlId){r=G[a].urlId;try{try{const l=await yt(async()=>await Z.getUrlMonitorStatus(),"get",`${wt()}/api/url/status`),i=(Array.isArray(l)?l:l.data?Array.isArray(l.data)?l.data:[]:[]).find(h=>h.urlId===r);i&&i.downloadedPath&&(n[a].resultFile=i.downloadedPath,n[a].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${a.substring(0,8)}已下载文件: ${i.downloadedPath}</span><br/>`)}catch(l){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${l.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${a.substring(0,8)}的URL监控</span><br/>`,await $e(r),delete G[a]}catch(l){t.value+=`<span class="log-item warning">停止URL监控出错: ${l.message}，将重试</span><br/>`,delete G[a],setTimeout(async()=>{try{r&&await yt(async()=>await Z.stopUrlMonitoring(r),"delete",`${wt()}/api/url/monitor/${r}`)}catch(v){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${v.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${a.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(r){t.value+=`<span class="log-item error">停止任务${a.substring(0,8)}出错: ${r.message}</span><br/>`,G[a]&&delete G[a]}},k=async a=>{if(!n[a]){t.value+=`<span class="log-item error">找不到任务${a.substring(0,8)}的数据</span><br/>`;return}n[a].status=4,n[a].terminated=!0,n[a].errorMessage="用户手动终止";const r=B();if(r&&r.ContentControls)for(let v=1;v<=r.ContentControls.Count;v++)try{const i=r.ContentControls.Item(v);if(i&&i.Title&&(i.Title===`任务_${a}`||i.Title===`任务增强_${a}`||i.Title===`校对_${a}`)){const h=i.Title===`任务增强_${a}`||n[a].isEnhanced,c=i.Title===`校对_${a}`||n[a].isCheckTask;c?i.Title=`已停止校对_${a}`:h?i.Title=`已停止增强_${a}`:i.Title=`已停止_${a}`,i.LockContents=!1;const O=c?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${a.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let l=null;if(G[a]&&G[a].urlId){l=G[a].urlId;try{const v=await Z.getUrlMonitorStatus(),h=(Array.isArray(v)?v:v.data?Array.isArray(v.data)?v.data:[]:[]).find(c=>c.urlId===l);h&&h.downloadedPath&&(n[a].resultFile=h.downloadedPath,n[a].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${a.substring(0,8)}已下载文件: ${h.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${a.substring(0,8)}的URL监控</span><br/>`,await $e(l),delete G[a]}catch(v){t.value+=`<span class="log-item warning">停止URL监控出错: ${v.message}，将重试</span><br/>`,delete G[a]}}},$=a=>cn.onbuttonclick(a),_=()=>{try{e.value=$("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},L=()=>{B().ActiveWindow.Selection.Copy()},E=a=>{const r=window.Application.Documents.Add();r.Content.Paste(),r.SaveAs2(`${y.value}\\${a}`,12,"","",!1),r.Close(),B().ActiveWindow.Activate()},q=a=>{const r=window.Application.Documents.Add("",!1,0,!1);r.Content.Paste(),r.SaveAs2(`${y.value}\\${a}`,12,"","",!1),r.Close()},fe=a=>{try{const l=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,v=window.Application.Documents.Add("",!1,0,!1);v.Content.Paste();const i=`${l}\\${a}`;v.SaveAs2(i,12,"","",!1),v.Close(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${i}.docx</span><br/>`}catch(r){throw t.value+=`<span class="log-item error">方式三保存失败: ${r.message}</span><br/>`,r}},ge=a=>{const r=window.Application.Documents.Add();r.Content.Paste(),r.SaveAs2(`${y.value}\\${a}`,12,"","",!1),B().ActiveWindow.Activate()},xe=async a=>{try{t.value+=`<span class="log-item info">开始生成文档: ${a}</span><br/>`;let r="method2";try{const l=await Z.getSaveMethod();if(l.success&&l.saveMethod){r=l.saveMethod;const v=r==="method1"?"方式一":r==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${v}</span><br/>`}}catch(l){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${l.message}</span><br/>`}r==="method1"?(E(a),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${y.value}\\${a}.docx</span><br/>`):r==="method2"?(q(a),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${y.value}\\${a}.docx</span><br/>`):r==="method3"?(fe(a),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):r==="method4"&&(ge(a),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${y.value}\\${a}.docx</span><br/>`),Z.associateFileWithClient(`${a}.docx`).then(l=>{l.success?t.value+=`<span class="log-item info">文件 ${a}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${l.message||"未知错误"}</span><br/>`}).catch(l=>{t.value+=`<span class="log-item warning">关联文件时出错: ${l.message}</span><br/>`})}catch(r){t.value+=`<span class="log-item error">保存文件失败: ${r.message}</span><br/>`}},Fe=pe(null),me=mt([]),Te=new Set,_e=a=>!a||typeof a!="string"?"":a.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),be=async a=>{try{const r=a.slice(T.length);if(r.trim()){const l=Z.getClientId();if(!l){console.warn("无法获取客户端ID，跳过日志同步");return}const v=_e(r);if(!v.trim()){T=a;return}await Z.sendRequest("logger","syncLog",{content:v,timestamp:new Date().toISOString(),clientId:l}),T=a}}catch(r){console.error("同步日志到服务端失败:",r)}},Ce=()=>{Z.connect().then(()=>{te()}).catch(r=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${r.message}</span><br/>`});const a=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const r=[];for(const l in n)if(n.hasOwnProperty(l)){const v=n[l];v.status===1&&!v.terminated&&(r.includes(l)||r.push(l))}if(r.length>0){let l=!1;try{const v=B();if(v){const h=`taskpane_id_${v.DocID}`,c=window.Application.PluginStorage.getItem(h);if(c){const O=window.Application.GetTaskPane(c);O&&(l=O.Visible)}}}catch(v){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${v.message}</span><br/>`,l=!0}setTimeout(()=>{if(l){const v=`检测到 ${r.length} 个未完成的任务，是否继续？`;b(v).then(i=>{i?(t.value+=`<span class="log-item info">用户选择继续 ${r.length} 个进行中的任务 (by taskId)...</span><br/>`,Z.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:r}).then(h=>{h&&h.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${h.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(h==null?void 0:h.message)||"未知错误"}</span><br/>`}).catch(h=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${h.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',r.forEach(h=>{m(h)}),t.value+=`<span class="log-item success">${r.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(i=>{t.value+=`<span class="log-item error">弹窗错误: ${i.message}，默认停止任务（保留控件）</span><br/>`,r.forEach(h=>{m(h)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Z.setConnectionSuccessHandler(a),Z.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',a()),Z.addEventListener("connection",r=>{r.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${r.reason||"未知"}, 代码: ${r.code||"N/A"}，将自动重连</span><br/>`)}),Z.addEventListener("watcher",r=>{var l,v;if(me.push(r),r.type,r.eventType==="uploadSuccess"){const i=(l=r.data)==null?void 0:l.file,h=i==null?void 0:i.replace(/\.docx$/,""),c=`${r.eventType}_${i}_${Date.now()}`;if(Te.has(c)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${i}</span><br/>`;return}if(Te.add(c),Te.size>100){const M=Te.values();Te.delete(M.next().value)}const O=h&&((v=n[h])==null?void 0:v.wordType);Pe(r,O)}else r.eventType&&Pe(r,null)}),Z.addEventListener("urlMonitor",r=>{me.push(r),r.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${r.eventType||r.action}</span><br/>`),r.eventType&&Pe(r,null)}),Z.addEventListener("health",r=>{}),Z.addEventListener("error",r=>{const l=r.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${l}</span><br/>`,console.error("WebSocket错误:",r)})},G=mt({}),we=async(a,r,l=!1,v=5e3,i={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${a}</span><br/>`;const h=await Z.startUrlMonitoring(a,v,{downloadOnSuccess:i.downloadOnSuccess!==void 0?i.downloadOnSuccess:!0,appKey:i.appKey,filename:i.filename,taskId:r}),c=h.success||(h==null?void 0:h.success),O=h.urlId||(h==null?void 0:h.urlId);if(c&&O){G[r]={urlId:O,url:a,isResultUrl:l,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${O}</span><br/>`;try{await Z.startUrlChecking(O)}catch{}return O}else throw new Error("服务器返回失败")}catch(h){return t.value+=`<span class="log-item error">启动URL监控失败: ${h.message}</span><br/>`,null}},$e=async a=>{if(!a)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(G).forEach(l=>{G[l].urlId===a&&delete G[l]}),t.value+=`<span class="log-item info">正在停止URL监控: ${a}</span><br/>`;const r=await yt(async()=>await Z.stopUrlMonitoring(a),"delete",`${wt()}/api/url/monitor/${a}`);return r&&(r.success||r!=null&&r.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${a}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(r){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${r.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await yt(async()=>await Z.stopUrlMonitoring(a),"delete",`${wt()}/api/url/monitor/${a}`)}catch{}},1e3)}catch{}return!0}},ce=async()=>{try{const a=await yt(async()=>await Z.getUrlMonitorStatus(),"get",`${wt()}/api/url/status`);return a.data||a}catch(a){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${a.message}</span><br/>`,[]}},Xe=async a=>{try{return await Z.forceUrlCheck(a)}catch{return!1}},Pe=async(a,r)=>{var l;if(a.eventType==="uploadSuccess"){const v=a.data.file,i=v.replace(/\.docx$/,"");if(n[i]){if(n[i].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${i.substring(0,8)} 的上传通知</span><br/>`;return}if(n[i].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${i.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${v} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${r||n[i].wordType||"wps-analysis"}</span><br/>`,n[i].status=1,n[i].uploadSuccess=!0,await Ge(i,r||n[i].wordType||"wps-analysis")}}else if(a.eventType!=="urlMonitorUpdate")if(a.eventType==="urlMonitorStopped"){const{urlId:v,url:i,taskId:h,downloadedPath:c}=a.data,O=Object.keys(G).filter(M=>G[M].urlId===v);O.length>0&&O.forEach(M=>{c&&n[M]&&(n[M].resultFile=c,n[M].resultDownloaded=!0),delete G[M]})}else if(a.eventType==="urlFileDownloaded"){const{urlId:v,url:i,filePath:h,taskId:c}=a.data;if(!Object.values(G).some(M=>M.urlId===v)&&c&&((l=n[c])!=null&&l.terminated))return;if(c&&n[c]&&n[c].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${v} 的文件下载通知</span><br/>`,v)try{await Z.stopUrlMonitoring(v),G[c]&&delete G[c]}catch{}return}if(c&&n[c]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${h}</span><br/>`,n[c].isCheckTask&&h.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${h}</span><br/>`;try{const D=window.Application.FileSystem.ReadFile(h),U=JSON.parse(D);t.value+='<span class="log-item info">删除校对控件以便添加批注</span><br/>';try{const se=B();if(se&&se.ContentControls)for(let ie=1;ie<=se.ContentControls.Count;ie++)try{const Q=se.ContentControls.Item(ie);if(Q&&Q.Title&&Q.Title===`校对_${c}`){Q.LockContents=!1,Q.Delete(!1),t.value+='<span class="log-item success">已删除校对控件，保留内容用于添加批注</span><br/>';break}}catch{continue}}catch(se){t.value+=`<span class="log-item warning">删除校对控件失败: ${se.message}，但继续处理批注</span><br/>`}if(await re(U,c)){n[c].status=2,t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}已完成批注处理</span><br/>`;const se=w(n[c].startTime);t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}完成，总耗时${se}</span><br/>`}}catch(M){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${M.message}</span><br/>`,M.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${h}</span><br/>`),n[c].status=-1,n[c].errorMessage=`JSON处理失败: ${M.message}`,t.value+=`<span class="log-item error">校对任务${c.substring(0,8)}处理失败</span><br/>`}}else{n[c].resultFile=h,n[c].resultDownloaded=!0;const M=w(n[c].startTime);t.value+=`<span class="log-item success">任务${c.substring(0,8)}完成，总耗时${M}</span><br/>`,await je(c)}G[c]&&G[c].urlId&&($e(G[c].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete G[c])}else if(v){t.value+=`<span class="log-item info">URL文件已下载: ${h}</span><br/>`;const M=Object.keys(G).filter(D=>{var U;return G[D].urlId===v&&!((U=n[D])!=null&&U.terminated)});M.length>0&&M.forEach(async D=>{if(n[D]){if(t.value+=`<span class="log-item info">关联到任务: ${D.substring(0,8)}</span><br/>`,n[D].resultFile=h,n[D].resultDownloaded=!0,n[D].isCheckTask&&h.endsWith(".wps.json"))try{const X=window.Application.FileSystem.ReadFile(h),se=JSON.parse(X);t.value+='<span class="log-item info">删除校对控件以便添加批注</span><br/>';try{const Q=B();if(Q&&Q.ContentControls)for(let Me=1;Me<=Q.ContentControls.Count;Me++)try{const ke=Q.ContentControls.Item(Me);if(ke&&ke.Title&&ke.Title===`校对_${D}`){ke.LockContents=!1,ke.Delete(!1),t.value+='<span class="log-item success">已删除校对控件，保留内容用于添加批注</span><br/>';break}}catch{continue}}catch(Q){t.value+=`<span class="log-item warning">删除校对控件失败: ${Q.message}，但继续处理批注</span><br/>`}if(await re(se,D)&&n[D].status===1){n[D].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const Q=w(n[D].startTime);t.value+=`<span class="log-item success">校对任务${D.substring(0,8)}完成，总耗时${Q}</span><br/>`}}catch(U){t.value+=`<span class="log-item error">处理校对JSON失败: ${U.message}</span><br/>`,n[D].status===1&&(n[D].status=-1,n[D].errorMessage=`JSON处理失败: ${U.message}`,t.value+=`<span class="log-item error">校对任务${D.substring(0,8)}处理失败</span><br/>`)}else if(n[D].status===1){n[D].status=2;const U=w(n[D].startTime);t.value+=`<span class="log-item success">任务${D.substring(0,8)}完成，总耗时${U}</span><br/>`}}})}}else if(a.eventType==="urlFileDownloadError"){const{urlId:v,url:i,error:h,taskId:c}=a.data;if(c&&n[c]&&n[c].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${c.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${h}</span><br/>`,c&&n[c]){n[c].status=-1,n[c].errorMessage=`下载失败: ${h}`;try{const O=B();if(O&&O.ContentControls)for(let M=1;M<=O.ContentControls.Count;M++)try{const D=O.ContentControls.Item(M);if(D&&D.Title&&(D.Title===`任务_${c}`||D.Title===`任务增强_${c}`||D.Title===`校对_${c}`)){const U=D.Title===`任务增强_${c}`||n[c].isEnhanced,X=D.Title===`校对_${c}`||n[c].isCheckTask;X?D.Title=`异常校对_${c}`:U?D.Title=`异常增强_${c}`:D.Title=`异常_${c}`;const se=X?"校对":U?"增强":"普通";t.value+=`<span class="log-item info">已将${se}任务${c.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(O){t.value+=`<span class="log-item warning">更新控件标题失败: ${O.message}</span><br/>`}ve(c),G[c]&&delete G[c]}if(v)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${v}</span><br/>`,await yt(async()=>await Z.stopUrlMonitoring(v),"delete",`${wt()}/api/url/monitor/${v}`)}catch{}}else a.eventType==="resumeUrlMonitors"&&console.log(a.data)},Ge=async(a,r="wps-analysis")=>{try{if(!C.appKey)throw new Error("未初始化appKey信息");const l=await z(),v=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${a}.docx`,i=await Mt.post(fs()+"/api/open/ticket/v1/ai_comment/create",{access_token:l,data:{app_key:C.appKey,subject:F.value,stage:W.value,file_name:`${a}`,word_url:v,word_type:r,is_ai_auto:!0,is_ai_edit:!0,create_user_id:C.userId,create_username:C.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),h=i.data.data.ticket_id;if(!h)return n[a]&&(n[a].status=-1,n[a].errorMessage="无法获取ticket_id",ve(a)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${h}，开始监控结果文件</span><br/>`;let c,O;r==="wps-check"?(c=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${C.appKey}/ai/${h}.wps.json`,O=`${h}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${O}</span><br/>`):(c=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${h}.wps.docx`,O=`${h}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${O}</span><br/>`);const M={downloadOnSuccess:!0,filename:O,appKey:C.appKey,ticketId:h,taskId:a},D=await we(c,a,!0,3e3,M);return n[a]&&(n[a].ticketId=h,n[a].resultUrl=c,n[a].urlMonitorId=D,n[a].status=1),i.data}catch(l){return t.value+=`<span class="log-item error">API调用失败: ${l.message}</span><br/>`,n[a]&&(n[a].status=-1,n[a].errorMessage=`API调用失败: ${l.message}`,ve(a)),!1}},He=async(a,r="wps-analysis")=>new Promise((l,v)=>{try{t.value+=`<span class="log-item info">监控目录: ${y.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${r}</span><br/>`,n[a]&&(n[a].status=0,n[a].wordType=r,n[a].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const i=1e3,h=30;let c=0;const O=setInterval(()=>{if(c++,n[a]&&n[a].uploadSuccess){clearInterval(O),l(!0);return}c>=h&&(clearInterval(O),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',v(new Error("上传超时，未收到完成通知")))},i)}catch(i){t.value+=`<span class="log-item error">任务处理异常: ${i.message}</span><br/>`,n[a]&&(n[a].status=-1,n[a].errorMessage=`任务处理异常: ${i.message}`),ve(a),v(i)}}),Ze=async()=>{var l;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const a=window.wps,r=a.ActiveDocument;if(o.value=r.DocID,g.value=a.ActiveWindow.Index,r!=null&&r.ContentControls)for(let v=1;v<=r.ContentControls.Count;v++){const i=r.ContentControls.Item(v);if(i&&i.Title){let h=null,c=1,O=!1,M=!1;if(i.Title.startsWith("任务增强_")?(h=i.Title.substring(5),c=1,O=!0):i.Title.startsWith("任务_")?(h=i.Title.substring(3),c=1):i.Title.startsWith("校对_")?(h=i.Title.substring(3),c=1,M=!0):i.Title.startsWith("已完成增强_")?(h=i.Title.substring(6),c=2,O=!0):i.Title.startsWith("已完成校对_")?(h=i.Title.substring(6),c=2,M=!0):i.Title.startsWith("已完成_")?(h=i.Title.substring(4),c=2):i.Title.startsWith("异常增强_")?(h=i.Title.substring(5),c=-1,O=!0):i.Title.startsWith("异常校对_")?(h=i.Title.substring(5),c=-1,M=!0):i.Title.startsWith("异常_")?(h=i.Title.substring(3),c=-1):i.Title.startsWith("已停止增强_")?(h=i.Title.substring(6),c=4,O=!0):i.Title.startsWith("已停止校对_")?(h=i.Title.substring(6),c=4,M=!0):i.Title.startsWith("已停止_")&&(h=i.Title.substring(4),c=4),h&&!n[h]){let D="";try{D=((l=i.Range)==null?void 0:l.Text)||""}catch{}let U=Date.now()-24*60*60*1e3;try{if(h.length===24){const ie=h.substring(0,8),Q=parseInt(ie,16);!isNaN(Q)&&Q>0&&Q<2147483647&&(U=Q*1e3)}else{const ie=Date.now()-864e5;c===2?U=ie-60*60*1e3:c===-1?U=ie-30*60*1e3:c===4?U=ie-45*60*1e3:U=ie}}catch{}n[h]={status:c,startTime:U,contentControlId:i.ID,isEnhanced:O,isCheckTask:M,selectedText:D};const X=c===1?"进行中":c===2?"已完成":c===-1?"异常":c===4?"已停止":"未知",se=M?"校对":O?"增强":"普通";t.value+=`<span class="log-item info">发现已有${se}任务: ${h.substring(0,8)}, 状态: ${X}</span><br/>`}}}},ve=(a,r=!1)=>{try{if(!n[a]){t.value+=`<span class="log-item warning">找不到任务${a.substring(0,8)}的记录，无法清除控件</span><br/>`;return}r&&n[a].status===1&&(n[a].status=3,n[a].errorMessage="用户主动释放");const l=B();if(!l){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let v=!1;const i=n[a].isEnhanced;if(l.ContentControls&&l.ContentControls.Count>0)for(let h=l.ContentControls.Count;h>=1;h--)try{const c=l.ContentControls.Item(h);c&&c.Title&&c.Title.includes(a)&&(c.LockContents=!1,c.Delete(!1),v=!0,console.log(c.Title),t.value+=`<span class="log-item success">已解锁并删除${i?"增强":"普通"}任务${a.substring(0,8)}的内容控件</span><br/>`)}catch(c){t.value+=`<span class="log-item warning">访问第${h}个控件时出错: ${c.message}</span><br/>`;continue}v?n[a].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${i?"增强":"普通"}任务${a.substring(0,8)}的内容控件</span><br/>`}catch(l){t.value+=`<span class="log-item error">删除内容控件失败: ${l.message}</span><br/>`}},Qe=a=>{var r;try{const l=window.wps,v=B();if(!v||!v.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const i=(r=n[a])==null?void 0:r.isEnhanced;let h=null;for(let O=1;O<=v.ContentControls.Count;O++)try{const M=v.ContentControls.Item(O);if(M&&M.Title&&M.Title.includes(a)){h=M;break}}catch{continue}if(!h){const O=n[a];O&&(O.status===2||O.status===-1)?t.value+=`<span class="log-item info">任务ID: ${a.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${a.substring(0,8)} 对应的内容控件</span><br/>`;return}h.Range.Select();const c=l.Windows.Item(g.value);c&&c.Selection&&c.Selection.Range&&c.ScrollIntoView(c.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${i?"增强":"普通"}任务ID: ${a.substring(0,8)} 位置</span><br/>`}catch(l){t.value+=`<span class="log-item error">跳转到任务控件失败: ${l.message}</span><br/>`}},je=async a=>{var c,O,M;const r=B(),l=n[a];if(!l){t.value+=`<span class="log-item error">找不到ID为${a.substring(0,8)}的任务, 现有任务ID: ${Object.keys(n).join(", ")}</span><br/>`;return}if(l.terminated||l.status!==1)return;if(l.documentInserted){t.value+=`<span class="log-item info">任务${a.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const v=B();let i=null;if(v&&v.ContentControls)for(let D=1;D<=v.ContentControls.Count;D++){const U=v.ContentControls.Item(D);if(U&&U.Title&&U.Title.includes(a)){i=U;break}}if(!i){if(t.value+=`<span class="log-item error">找不到任务${a.substring(0,8)}的内容控件</span><br/>`,l.errorMessage&&l.status===1){n[a].status=-1,t.value+=`<span class="log-item error">任务${a.substring(0,8)}失败: ${l.errorMessage}</span><br/>`;return}return}if(l.errorMessage&&l.status===1){n[a].status=-1,t.value+=`<span class="log-item error">任务${a.substring(0,8)}失败: ${l.errorMessage}</span><br/>`,ve(a);return}if(!l.resultFile)return;n[a].documentInserted=!0;const h=i.Range;i.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${l.resultFile}...</span><br/>`;const D=r.Range(h.End,h.End);D.InsertParagraphAfter(),h.Text.includes("\v")&&D.InsertAfter("\v");let U=D.End;const X=(c=h.Tables)==null?void 0:c.Count;if(X){const ue=h.Tables.Item(X);((O=ue==null?void 0:ue.Range)==null?void 0:O.End)>U&&(ue.Range.InsertParagraphAfter(),U=(M=ue==null?void 0:ue.Range)==null?void 0:M.End)}r.Range(U,U).InsertFile(l.resultFile);for(let ue=1;ue<=v.ContentControls.Count;ue++){const nt=v.ContentControls.Item(ue);if(nt&&nt.Title&&(nt.Title===`任务_${a}`||nt.Title===`任务增强_${a}`)){i=nt;break}}const ie=r.Range(i.Range.End-1,i.Range.End);ie.Text.includes("\r")&&ie.Delete(),n[a].status=2,G[a]&&G[a].urlId&&Z.sendRequest("urlMonitor","updateTaskStatus",{urlId:G[a].urlId,status:"completed",taskId:a}).then(ue=>{t.value+=`<span class="log-item info">已通知服务端更新任务${a.substring(0,8)}状态为完成</span><br/>`}).catch(ue=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${ue.message}</span><br/>`});const Me=w(l.startTime);t.value+=`<span class="log-item success">任务${a.substring(0,8)}处理完成，总耗时${Me}</span><br/>`;const ke=i.Title===`任务增强_${a}`||l.isEnhanced,ye=i.Title===`校对_${a}`||l.isCheckTask;if(i){ye?i.Title=`已完成校对_${a}`:ke?i.Title=`已完成增强_${a}`:i.Title=`已完成_${a}`;const ue=ye?"校对":ke?"增强":"普通";t.value+=`<span class="log-item info">已将${ue}任务${a.substring(0,8)}控件标记为已完成</span><br/>`}}catch(D){n[a].documentInserted=!1,n[a].status=-1;const U=i.Title===`任务增强_${a}`||l.isEnhanced,X=i.Title===`校对_${a}`||l.isCheckTask;if(i){X?i.Title=`异常校对_${a}`:U?i.Title=`异常增强_${a}`:i.Title=`异常_${a}`;const se=X?"校对":U?"增强":"普通";t.value+=`<span class="log-item info">已将${se}任务${a.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${D.message}</span><br/>`}},rt=async()=>{const a=(v=1e3)=>new Promise(i=>{setTimeout(()=>i(),v)}),r=new Map,l=Object.keys(n).filter(v=>n[v].status===1&&!n[v].terminated);for(l.length?(l.forEach(v=>{r.has(v)||r.set(v,Date.now())}),await Promise.all(l.map(v=>je(v)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await a(3e3);const v=Object.keys(n).filter(i=>n[i].status===1&&!n[i].terminated);v.forEach(i=>{r.has(i)||r.set(i,Date.now());const h=r.get(i);(Date.now()-h)/1e3/60>=5e4&&n[i]&&!n[i].terminated&&(n[i].terminated=!0,n[i].status=-1,t.value+=`<span class="log-item warning">任务 ${i} 执行超过5分钟，已自动终止</span><br/>`,r.delete(i))}),v.length&&await Promise.all(v.map(i=>je(i)))}},Ae=async(a="wps-analysis")=>{const r=lt();if(r){const{primary:v,all:i}=r,{taskId:h,control:c,task:O,isEnhanced:M,overlapType:D}=v,U=i.filter(X=>X.task&&X.task.status===1);if(U.length>0){const X=U.map(se=>se.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${X})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${i.length}个已有任务重叠，重叠类型: ${D}</span><br/>`,ut();try{for(const X of i){const{taskId:se,control:ie,task:Q,isEnhanced:Me}=X;t.value+=`<span class="log-item info">删除重叠的${Me?"增强":"普通"}任务 ${se.substring(0,8)}，状态为 ${u((Q==null?void 0:Q.status)||0)}</span><br/>`,Q&&(Q.status=3,Q.terminated=!0,Q.errorMessage="用户重新创建任务时删除");try{ie.LockContents=!1,ie.Delete(!1),n[se]&&(n[se].placeholderRemoved=!0)}catch(ke){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ke.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${i.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(X){return x(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${X.message}</span><br/>`,Promise.reject(X)}x()}const l=hs().replace(/-/g,"").substring(0,8);return new Promise(async(v,i)=>{try{const h=window.wps,c=B(),O=c.ActiveWindow.Selection,M=O.Range,D=O.Text||"";if(s.value=D,L(),M){const U=M.Paragraphs,X=U.Item(1),se=U.Item(U.Count),ie=X.Range.Start,Q=se.Range.End,Me=M.Start,ke=M.End,ye=c.Range(Math.min(ie,Me),Math.max(Q,ke));try{let ue=c.ContentControls.Add(h.Enum.wdContentControlRichText,ye);if(!ue){if(console.log("创建内容控件失败"),ue=c.ContentControls.Add(h.Enum.wdContentControlRichText),!ue){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',i(new Error("创建内容控件失败"));return}M.Cut(),ue.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const nt=a==="wps-enhance_analysis";ue.Title=nt?`任务增强_${l}`:`任务_${l}`,ue.LockContents=!0,n[l]||(n[l]={}),n[l].contentControlId=ue.ID,n[l].isEnhanced=nt,t.value+=`<span class="log-item info">已创建${nt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const en=ue.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${en.length}</span><br/>`}catch(ue){t.value+=`<span class="log-item error">创建内容控件失败: ${ue.message}</span><br/>`,i(ue);return}}await xe(l),n[l]={status:1,startTime:Date.now(),wordType:a,isEnhanced:a==="wps-enhance_analysis",selectedText:D},t.value+=`<span class="log-item success">创建${a==="wps-enhanced"?"增强":"普通"}任务: ${l}，类型: ${a}</span><br/>`;try{await He(l,a)?v():(n[l]&&n[l].status===1&&(n[l].status=-1,n[l].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${l.substring(0,8)}失败</span><br/>`,j(l)),i(new Error("API调用失败或超时")))}catch(U){n[l]&&(n[l].status=-1,n[l].errorMessage=`执行错误: ${U.message}`,t.value+=`<span class="log-item error">任务${l.substring(0,8)}执行出错: ${U.message}</span><br/>`,j(l)),i(U)}}catch(h){i(h)}})},Oe=async()=>{const a=lt();if(a){const{primary:l,all:v}=a,{taskId:i,control:h,task:c,isEnhanced:O,overlapType:M}=l,D=v.filter(U=>U.task&&U.task.status===1);if(D.length>0){const U=D.map(X=>X.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${U})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${v.length}个已有任务重叠，重叠类型: ${M}</span><br/>`,ut();try{for(const U of v){const{taskId:X,control:se,task:ie,isEnhanced:Q}=U;t.value+=`<span class="log-item info">删除重叠的校对任务 ${X.substring(0,8)}，状态为 ${u((ie==null?void 0:ie.status)||0)}</span><br/>`,ie&&(ie.status=3,ie.terminated=!0,ie.errorMessage="用户重新创建校对任务时删除");try{se.LockContents=!1,se.Delete(!1),n[X]&&(n[X].placeholderRemoved=!0)}catch(Me){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${Me.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${v.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(U){return x(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${U.message}</span><br/>`,Promise.reject(U)}x()}const r=hs().replace(/-/g,"").substring(0,8);return console.log("uuid",r),new Promise(async(l,v)=>{try{const i=window.wps,h=B(),c=h.ActiveWindow.Selection,O=c.Range,M=c.Text||"";if(s.value=M,L(),O){const D=O.Paragraphs,U=D.Item(1),X=D.Item(D.Count),se=U.Range.Start,ie=X.Range.End,Q=O.Start,Me=O.End,ke=h.Range(Math.min(se,Q),Math.max(ie,Me));try{let ye=h.ContentControls.Add(i.Enum.wdContentControlRichText,ke);if(!ye){if(console.log("创建校对内容控件失败"),ye=h.ContentControls.Add(i.Enum.wdContentControlRichText),!ye){t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',v(new Error("创建校对内容控件失败"));return}O.Cut(),ye.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',ye.Title=`校对_${r}`,ye.LockContents=!0,n[r]||(n[r]={}),n[r].contentControlId=ye.ID,n[r].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const ue=ye.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${ue.length}</span><br/>`}catch(ye){t.value+=`<span class="log-item error">创建校对内容控件失败: ${ye.message}</span><br/>`,v(ye);return}}await xe(r),n[r]={status:1,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:M},t.value+=`<span class="log-item success">创建校对任务: ${r}，类型: wps-check</span><br/>`,console.log("uuid",r);try{await He(r,"wps-check")?l():(n[r]&&n[r].status===1&&(n[r].status=-1,n[r].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${r.substring(0,8)}失败</span><br/>`,j(r)),v(new Error("校对API调用失败或超时")))}catch(D){n[r]&&(n[r].status=-1,n[r].errorMessage=`校对执行错误: ${D.message}`,t.value+=`<span class="log-item error">校对任务${r.substring(0,8)}执行出错: ${D.message}</span><br/>`,j(r)),v(D)}}catch(i){v(i)}})},et=async()=>{},We=()=>qe()>0?"status-error":ze()>0?"status-running":tt()>0?"status-completed":"",ze=()=>Object.values(n).filter(a=>a.status===1).length,tt=()=>Object.values(n).filter(a=>a.status===2).length,qe=()=>Object.values(n).filter(a=>a.status===-1).length,ot=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const a=window.wps,r=B();if(!r){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let l=0;if(Object.keys(n).forEach(v=>{try{n[v].status===1&&(n[v].status=3,n[v].terminated=!0,n[v].errorMessage="强制清除"),ve(v),l++}catch(i){t.value+=`<span class="log-item warning">清除任务${v.substring(0,8)}失败: ${i.message}</span><br/>`}}),r.ContentControls&&r.ContentControls.Count>0)for(let v=r.ContentControls.Count;v>=1;v--)try{const i=r.ContentControls.Item(v);if(i&&i.Title&&(i.Title.startsWith("任务_")||i.Title.startsWith("任务增强_")||i.Title.startsWith("已完成_")||i.Title.startsWith("已完成增强_")||i.Title.startsWith("异常_")||i.Title.startsWith("异常增强_")||i.Title.startsWith("已停止_")||i.Title.startsWith("已停止增强_")))try{i.LockContents=!1,i.Delete(!1);let h;i.Title.startsWith("任务增强_")?h=i.Title.substring(5):i.Title.startsWith("任务_")?h=i.Title.substring(3):i.Title.startsWith("已完成增强_")?h=i.Title.substring(6):i.Title.startsWith("已完成_")?h=i.Title.substring(4):i.Title.startsWith("异常增强_")?h=i.Title.substring(5):i.Title.startsWith("异常_")?h=i.Title.substring(3):i.Title.startsWith("已停止增强_")?h=i.Title.substring(6):i.Title.startsWith("已停止_")&&(h=i.Title.substring(4)),n[h]?(n[h].placeholderRemoved=!0,n[h].status=3):n[h]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},l++,t.value+=`<span class="log-item success">已删除任务${h.substring(0,8)}的内容控件</span><br/>`}catch(h){t.value+=`<span class="log-item error">删除控件失败: ${h.message}</span><br/>`}}catch(i){t.value+=`<span class="log-item warning">访问控件时出错: ${i.message}</span><br/>`}l>0?t.value+=`<span class="log-item success">已清除${l}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(a){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${a.message}</span><br/>`}},st=async()=>{try{const a=Object.values(G).map(r=>r.urlId);if(a.length>0){t.value+=`<span class="log-item info">清理${a.length}个URL监控任务...</span><br/>`;for(const r of a)await $e(r)}}catch(a){t.value+=`<span class="log-item error">清理URL监控任务失败: ${a.message}</span><br/>`}},St=()=>{Os(async()=>{try{const r=window.Application.PluginStorage.getItem("user_info");if(!r)throw new Error("未找到用户信息");const l=JSON.parse(r);if(!l.orgs||!l.orgs[0])throw new Error("未找到组织信息");C.appKey=l.appKey,C.userName=l.nickname,C.userId=l.userId,C.appSecret=l.appSecret}catch(r){t.value+=`<span class="log-item error">初始化appKey失败: ${r.message}</span><br/>`}await te(),Nt([F,W],async()=>{await N()},{immediate:!1}),await I();const a=Ne.onVersionChange(()=>{const r=V();ee.splice(0,ee.length,...r),Ne.isSeniorEdition()&&W.value==="junior"?W.value="senior":!Ne.isSeniorEdition()&&W.value==="senior"&&(W.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Ne.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',_(),await Ze(),Ce(),gt(),rt(),window.addEventListener("beforeunload",st),()=>{S&&clearTimeout(S),a&&a()}}),Nt(t,a=>{S&&clearTimeout(S),S=setTimeout(()=>{be(a)},10)},{immediate:!1})},gt=()=>{Z.addEventListener("config",a=>{a.eventType==="subjectAndStageChanged"&&a.data&&(a.data.subject!==F.value&&(F.value=a.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${F.value}</span><br/>`),a.data.stage!==W.value&&(W.value=a.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${W.value}</span><br/>`))})},it=pe(!1),lt=()=>{try{const a=B(),r=a.ActiveWindow.Selection;if(!r||!a||!a.ContentControls)return null;const l=r.Range,v=[];for(let i=1;i<=a.ContentControls.Count;i++)try{const h=a.ContentControls.Item(i);if(!h)continue;const c=(h.Title||"").trim(),O=h.Range;if(l.Start<O.End&&l.End>O.Start){let M=null,D=!1,U=!1;if(!c)U=!0,M=`empty_${h.ID||Date.now()}`;else if(c.startsWith("任务_")||c.startsWith("任务增强_")||c.startsWith("校对_")||c.startsWith("已完成_")||c.startsWith("已完成增强_")||c.startsWith("已完成校对_")||c.startsWith("异常_")||c.startsWith("异常增强_")||c.startsWith("异常校对_")||c.startsWith("已停止_")||c.startsWith("已停止增强_")||c.startsWith("已停止校对_"))c.startsWith("任务增强_")?(M=c.substring(5),D=!0):c.startsWith("任务_")||c.startsWith("校对_")?M=c.substring(3):c.startsWith("已完成增强_")?(M=c.substring(6),D=!0):c.startsWith("已完成校对_")?M=c.substring(6):c.startsWith("已完成_")?M=c.substring(4):c.startsWith("异常增强_")?(M=c.substring(5),D=!0):c.startsWith("异常校对_")?M=c.substring(5):c.startsWith("异常_")?M=c.substring(3):c.startsWith("已停止增强_")?(M=c.substring(6),D=!0):c.startsWith("已停止校对_")?M=c.substring(6):c.startsWith("已停止_")&&(M=c.substring(4));else continue;if(M){let X;l.Start>=O.Start&&l.End<=O.End?X="completely_within":l.Start<=O.Start&&l.End>=O.End?X="completely_contains":X="partial_overlap",v.push({taskId:M,control:h,task:U?null:n[M]||null,isEnhanced:D,isEmptyTitle:U,overlapType:X,controlRange:{start:O.Start,end:O.End},selectionRange:{start:l.Start,end:l.End}})}}}catch{continue}return v.length===0?null:{primary:v[0],all:v}}catch(a){return t.value+=`<span class="log-item error">检查选区位置出错: ${a.message}</span><br/>`,null}},ut=()=>{it.value=!0},x=()=>{it.value=!1},j=async(a,r=!1)=>{ut();try{await ve(a,r)}finally{x()}},b=a=>new Promise((r,l)=>{A.show=!0,A.message=a,A.resolveCallback=r,A.rejectCallback=l}),p=a=>{A.show=!1,a&&A.resolveCallback?A.resolveCallback(!0):A.resolveCallback&&A.resolveCallback(!1),A.resolveCallback=null,A.rejectCallback=null},I=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const a=await Z.getSubjectAndStage();a.success&&a.data?(a.data.subject&&(F.value=a.data.subject),a.data.stage&&(W.value=a.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${F.value})和年级(${W.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(a){console.error("从服务器加载学科和年级设置失败:",a),t.value+=`<span class="log-item error">从服务器加载设置失败: ${a.message}</span><br/>`}},N=async()=>{try{if(F.value||W.value){t.value+=`<span class="log-item info">正在保存学科(${F.value})和年级(${W.value})设置到服务器...</span><br/>`;const a=await Z.setSubjectAndStage(F.value,W.value);a&&a.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(a==null?void 0:a.message)||"未知错误"}</span><br/>`}}catch(a){console.error("保存学科和年级到服务器失败:",a),t.value+=`<span class="log-item error">保存设置到服务器失败: ${a.message}</span><br/>`}},oe=async()=>{try{R.value=2,H.value=R.value===2,H.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${R.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${R.value}）</span><br/>`}catch(a){console.error("检查企业ID失败:",a),t.value+=`<span class="log-item error">检查企业ID失败: ${a.message}</span><br/>`,H.value=!1}},re=async(a,r)=>{try{if(!a||!Array.isArray(a))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;let l=0,v=0;for(const i of a){if(!i.mode1||!Array.isArray(i.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const h of i.mode1){if(!h.error_info||!Array.isArray(h.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const c=h.quest_html||"",O=h.quest_type||"";t.value+=`<span class="log-item info">处理${O}题目，发现${h.error_info.length}个错误信息</span><br/>`;for(const M of h.error_info)try{let D="";M.error_info&&(D+=`【错误类型】${M.error_info}`),M.fix_info&&(D+=`\r
【修正建议】${M.fix_info}`),M.keywords&&M.keywords.trim()?ae(D,M.keywords.trim(),-1)?(l++,t.value+=`<span class="log-item success">已为关键词"${M.keywords.trim()}"添加批注</span><br/>`):(v++,t.value+=`<span class="log-item warning">无法为关键词"${M.keywords.trim()}"添加批注</span><br/>`):ae(D,"",0)?(l++,t.value+='<span class="log-item success">已为选中内容添加通用批注</span><br/>'):(v++,t.value+='<span class="log-item warning">无法添加通用批注，可能没有选中内容</span><br/>')}catch(D){v++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${D.message}</span><br/>`}}}return l>0?(t.value+=`<span class="log-item success">校对任务${r.substring(0,8)}处理完成：成功添加${l}个批注</span><br/>`,v>0&&(t.value+=`<span class="log-item warning">校对任务${r.substring(0,8)}：${v}个批注添加失败</span><br/>`),!0):(t.value+=`<span class="log-item error">校对任务${r.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(l){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${l.message}</span><br/>`,!1}};return{docName:e,selected:s,logger:t,map:n,watchedDir:y,subject:F,stage:W,subjectOptions:K,stageOptions:ee,fetchWatchedDir:te,clearLog:d,getCurrentDocument:B,checkDocumentFormat:P,getTaskStatusClass:le,getTaskStatusText:u,getElapsedTime:w,terminateTask:k,stopTaskWithoutRemovingControl:m,run1:Ae,run2:et,runCheck:Oe,getHeaderStatusClass:We,getRunningTasksCount:ze,getCompletedTasksCount:tt,getErrorTasksCount:qe,setupLifecycle:St,navigateToTaskControl:Qe,forceCleanAllTasks:ot,ws:Fe,wsMessages:me,initWebSocket:Ce,handleWatcherEvent:Pe,urlMonitorTasks:G,monitorUrlForTask:we,stopUrlMonitoring:$e,getUrlMonitorStatus:ce,forceUrlCheck:Xe,cleanupUrlMonitoringTasks:st,tryRemoveTaskPlaceholder:ve,isLoading:it,isSelectionInTaskControl:lt,tryRemoveTaskPlaceholderWithLoading:j,showConfirm:b,handleConfirm:p,confirmDialog:A,loadSubjectAndStage:I,saveSubjectAndStage:N,enterpriseId:R,isCheckingVisible:H,checkEnterpriseAndSetCheckingVisibility:oe,processCheckingJson:re}}const wn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Ne.getVersionConfig(),selectedEdition:Ne.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,n=Math.floor(t/(1e3*60*60)),o=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${n}小时 ${o}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Z.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Z[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Z.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Z.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Z.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Z.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Z.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Z.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Z.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Ne.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await sn()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Z.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Z.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Z.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Z.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Z.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Z.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Z.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Ne.onVersionChange(e=>{this.versionConfig=Ne.getVersionConfig(),this.selectedEdition=Ne.getEdition()}),await Z.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},yn={class:"file-watcher"},xn={class:"settings-modal"},Tn={class:"modal-content"},Cn={class:"modal-header"},kn={class:"version-tag"},$n={key:0,class:"user-info"},Sn={class:"header-actions"},En={class:"modal-body"},_n={class:"status-section"},An={class:"status-item"},Mn={key:0,class:"status-item"},Dn={class:"directory-section"},Pn={class:"directory-form"},On={class:"radio-group"},In={class:"radio-item"},Rn={class:"radio-item"},Un={class:"radio-item"},Ln={key:0,class:"radio-item"},Fn={key:1,class:"directory-section"},jn={class:"directory-form"},Wn={class:"form-group"},Bn=["placeholder"],Nn=["disabled"],Vn={key:2,class:"directory-section"},Hn={class:"directory-form"},zn={class:"form-group"},qn=["placeholder"],Jn=["disabled"],Yn={key:3,class:"directory-section"},Kn={class:"directory-form"},Xn={class:"form-group"},Gn=["placeholder"],Zn=["disabled"],Qn={key:4,class:"events-section"},ea={class:"events-list"},ta={class:"event-time"},sa={class:"event-message"},na={key:1,class:"modal-footer"};function aa(e,s,t,n,o,g){var T,S,y,C,A,F,W,R,H,K,V,ee,te,z,d,B,P,ae,le,u,w;return J(),Y("div",yn,[f("div",xn,[f("div",Tn,[f("div",Cn,[f("h3",null,[nn(de(o.versionConfig.shortName)+"设置 ",1),f("span",kn,de(o.versionConfig.appVersion),1),g.userInfo?(J(),Y("span",$n,"欢迎您，"+de(g.userInfo.nickname),1)):ne("",!0)]),f("div",Sn,[f("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>g.handleLogout&&g.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[f("span",{class:"icon-logout"},null,-1)])),f("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),f("div",En,[f("div",_n,[f("div",An,[s[22]||(s[22]=f("span",{class:"label"},"状态：",-1)),f("span",{class:Ie(["status-badge",o.status.status])},de(o.status.status==="running"?"运行中":"已停止"),3)]),((y=(S=(T=g.userInfo)==null?void 0:T.orgs)==null?void 0:S[0])==null?void 0:y.orgId)===2?(J(),Y("div",Mn,[s[23]||(s[23]=f("span",{class:"label"},"本次上传：",-1)),f("span",null,de(o.status.processedFiles||0)+" 个文件",1)])):ne("",!0),f("div",Dn,[s[28]||(s[28]=f("h4",null,"保存方式设置",-1)),f("div",Pn,[f("div",On,[f("label",In,[Be(f("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>o.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Ft,o.currentSaveMethod]]),s[24]||(s[24]=f("span",{class:"radio-label"},"方式一",-1))]),f("label",Rn,[Be(f("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>o.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Ft,o.currentSaveMethod]]),s[25]||(s[25]=f("span",{class:"radio-label"},"方式二 (默认)",-1))]),f("label",Un,[Be(f("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>o.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Ft,o.currentSaveMethod]]),s[26]||(s[26]=f("span",{class:"radio-label"},"方式三",-1))]),((F=(A=(C=g.userInfo)==null?void 0:C.orgs)==null?void 0:A[0])==null?void 0:F.orgId)===2?(J(),Y("label",Ln,[Be(f("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>o.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Ft,o.currentSaveMethod]]),s[27]||(s[27]=f("span",{class:"radio-label"},"方式四",-1))])):ne("",!0)]),o.saveMethodUpdateMessage?(J(),Y("div",{key:0,class:Ie(["update-message",o.saveMethodUpdateSuccess?"success":"error"])},de(o.saveMethodUpdateMessage),3)):ne("",!0)])])]),ne("",!0),((H=(R=(W=g.userInfo)==null?void 0:W.orgs)==null?void 0:R[0])==null?void 0:H.orgId)===2?(J(),Y("div",Fn,[s[32]||(s[32]=f("h4",null,"上传目录设置",-1)),f("div",jn,[f("div",Wn,[s[31]||(s[31]=f("span",{class:"label"},"路径：",-1)),Be(f("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>o.newWatchDir=m),placeholder:o.status.watchDir||"C:\\Temp"},null,8,Bn),[[Jt,o.newWatchDir]]),f("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>g.updateWatchDir&&g.updateWatchDir(...m)),disabled:o.isUpdating||!o.newWatchDir},de(o.isUpdating?"更新中...":"更新目录"),9,Nn)]),o.updateMessage?(J(),Y("div",{key:0,class:Ie(["update-message",o.updateSuccess?"success":"error"])},de(o.updateMessage),3)):ne("",!0)])])):ne("",!0),((ee=(V=(K=g.userInfo)==null?void 0:K.orgs)==null?void 0:V[0])==null?void 0:ee.orgId)===2?(J(),Y("div",Vn,[s[34]||(s[34]=f("h4",null,"下载目录设置",-1)),f("div",Hn,[f("div",zn,[s[33]||(s[33]=f("span",{class:"label"},"路径：",-1)),Be(f("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>o.newDownloadPath=m),placeholder:o.downloadPath||"C:\\Temp\\Downloads"},null,8,qn),[[Jt,o.newDownloadPath]]),f("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>g.updateDownloadPath&&g.updateDownloadPath(...m)),disabled:o.isUpdatingDownloadPath||!o.newDownloadPath},de(o.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Jn)]),o.downloadPathUpdateMessage?(J(),Y("div",{key:0,class:Ie(["update-message",o.downloadPathUpdateSuccess?"success":"error"])},de(o.downloadPathUpdateMessage),3)):ne("",!0)])])):ne("",!0),((d=(z=(te=g.userInfo)==null?void 0:te.orgs)==null?void 0:z[0])==null?void 0:d.orgId)===2?(J(),Y("div",Yn,[s[36]||(s[36]=f("h4",null,"配置目录设置",-1)),f("div",Kn,[f("div",Xn,[s[35]||(s[35]=f("span",{class:"label"},"路径：",-1)),Be(f("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>o.newAddonConfigPath=m),placeholder:o.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,Gn),[[Jt,o.newAddonConfigPath]]),f("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...m)),disabled:o.isUpdatingAddonConfigPath||!o.newAddonConfigPath},de(o.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,Zn)]),o.addonConfigPathUpdateMessage?(J(),Y("div",{key:0,class:Ie(["update-message",o.addonConfigPathUpdateSuccess?"success":"error"])},de(o.addonConfigPathUpdateMessage),3)):ne("",!0)])])):ne("",!0),((ae=(P=(B=g.userInfo)==null?void 0:B.orgs)==null?void 0:P[0])==null?void 0:ae.orgId)===2?(J(),Y("div",Qn,[s[37]||(s[37]=f("h4",null,"最近事件",-1)),f("div",ea,[(J(!0),Y(_t,null,At(o.recentEvents,(m,k)=>(J(),Y("div",{key:k,class:"event-item"},[f("span",ta,de(g.formatTime(m.timestamp)),1),f("span",{class:Ie(["event-type",m.eventType])},de(g.getEventTypeText(m.eventType)),3),f("span",sa,de(g.getEventMessage(m)),1)]))),128))])])):ne("",!0)]),ne("",!0),((w=(u=(le=g.userInfo)==null?void 0:le.orgs)==null?void 0:u[0])==null?void 0:w.orgId)===2?(J(),Y("div",na,[f("button",{class:Ie(["control-btn",o.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>g.controlService&&g.controlService(...m))},de(o.status.status==="running"?"停止服务":"启动服务"),3)])):ne("",!0)])])])}const ra=Is(wn,[["render",aa],["__scopeId","data-v-48242008"]]);var Se="top",Ue="bottom",Le="right",Ee="left",ns="auto",Ut=[Se,Ue,Le,Ee],Tt="start",It="end",oa="clippingParents",Us="viewport",Et="popper",ia="reference",vs=Ut.reduce(function(e,s){return e.concat([s+"-"+Tt,s+"-"+It])},[]),Ls=[].concat(Ut,[ns]).reduce(function(e,s){return e.concat([s,s+"-"+Tt,s+"-"+It])},[]),la="beforeRead",ca="read",ua="afterRead",pa="beforeMain",da="main",fa="afterMain",ha="beforeWrite",va="write",ga="afterWrite",ma=[la,ca,ua,pa,da,fa,ha,va,ga];function Ke(e){return e?(e.nodeName||"").toLowerCase():null}function De(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function vt(e){var s=De(e).Element;return e instanceof s||e instanceof Element}function Re(e){var s=De(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function as(e){if(typeof ShadowRoot>"u")return!1;var s=De(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function ba(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var n=s.styles[t]||{},o=s.attributes[t]||{},g=s.elements[t];!Re(g)||!Ke(g)||(Object.assign(g.style,n),Object.keys(o).forEach(function(T){var S=o[T];S===!1?g.removeAttribute(T):g.setAttribute(T,S===!0?"":S)}))})}function wa(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(n){var o=s.elements[n],g=s.attributes[n]||{},T=Object.keys(s.styles.hasOwnProperty(n)?s.styles[n]:t[n]),S=T.reduce(function(y,C){return y[C]="",y},{});!Re(o)||!Ke(o)||(Object.assign(o.style,S),Object.keys(g).forEach(function(y){o.removeAttribute(y)}))})}}const Fs={name:"applyStyles",enabled:!0,phase:"write",fn:ba,effect:wa,requires:["computeStyles"]};function Ye(e){return e.split("-")[0]}var ft=Math.max,Vt=Math.min,Ct=Math.round;function Qt(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function js(){return!/^((?!chrome|android).)*safari/i.test(Qt())}function kt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var n=e.getBoundingClientRect(),o=1,g=1;s&&Re(e)&&(o=e.offsetWidth>0&&Ct(n.width)/e.offsetWidth||1,g=e.offsetHeight>0&&Ct(n.height)/e.offsetHeight||1);var T=vt(e)?De(e):window,S=T.visualViewport,y=!js()&&t,C=(n.left+(y&&S?S.offsetLeft:0))/o,A=(n.top+(y&&S?S.offsetTop:0))/g,F=n.width/o,W=n.height/g;return{width:F,height:W,top:A,right:C+F,bottom:A+W,left:C,x:C,y:A}}function rs(e){var s=kt(e),t=e.offsetWidth,n=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-n)<=1&&(n=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:n}}function Ws(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&as(t)){var n=s;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function at(e){return De(e).getComputedStyle(e)}function ya(e){return["table","td","th"].indexOf(Ke(e))>=0}function ct(e){return((vt(e)?e.ownerDocument:e.document)||window.document).documentElement}function zt(e){return Ke(e)==="html"?e:e.assignedSlot||e.parentNode||(as(e)?e.host:null)||ct(e)}function gs(e){return!Re(e)||at(e).position==="fixed"?null:e.offsetParent}function xa(e){var s=/firefox/i.test(Qt()),t=/Trident/i.test(Qt());if(t&&Re(e)){var n=at(e);if(n.position==="fixed")return null}var o=zt(e);for(as(o)&&(o=o.host);Re(o)&&["html","body"].indexOf(Ke(o))<0;){var g=at(o);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return o;o=o.parentNode}return null}function Lt(e){for(var s=De(e),t=gs(e);t&&ya(t)&&at(t).position==="static";)t=gs(t);return t&&(Ke(t)==="html"||Ke(t)==="body"&&at(t).position==="static")?s:t||xa(e)||s}function os(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Dt(e,s,t){return ft(e,Vt(s,t))}function Ta(e,s,t){var n=Dt(e,s,t);return n>t?t:n}function Bs(){return{top:0,right:0,bottom:0,left:0}}function Ns(e){return Object.assign({},Bs(),e)}function Vs(e,s){return s.reduce(function(t,n){return t[n]=e,t},{})}var Ca=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Ns(typeof s!="number"?s:Vs(s,Ut))};function ka(e){var s,t=e.state,n=e.name,o=e.options,g=t.elements.arrow,T=t.modifiersData.popperOffsets,S=Ye(t.placement),y=os(S),C=[Ee,Le].indexOf(S)>=0,A=C?"height":"width";if(!(!g||!T)){var F=Ca(o.padding,t),W=rs(g),R=y==="y"?Se:Ee,H=y==="y"?Ue:Le,K=t.rects.reference[A]+t.rects.reference[y]-T[y]-t.rects.popper[A],V=T[y]-t.rects.reference[y],ee=Lt(g),te=ee?y==="y"?ee.clientHeight||0:ee.clientWidth||0:0,z=K/2-V/2,d=F[R],B=te-W[A]-F[H],P=te/2-W[A]/2+z,ae=Dt(d,P,B),le=y;t.modifiersData[n]=(s={},s[le]=ae,s.centerOffset=ae-P,s)}}function $a(e){var s=e.state,t=e.options,n=t.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o=="string"&&(o=s.elements.popper.querySelector(o),!o)||Ws(s.elements.popper,o)&&(s.elements.arrow=o))}const Sa={name:"arrow",enabled:!0,phase:"main",fn:ka,effect:$a,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function $t(e){return e.split("-")[1]}var Ea={top:"auto",right:"auto",bottom:"auto",left:"auto"};function _a(e,s){var t=e.x,n=e.y,o=s.devicePixelRatio||1;return{x:Ct(t*o)/o||0,y:Ct(n*o)/o||0}}function ms(e){var s,t=e.popper,n=e.popperRect,o=e.placement,g=e.variation,T=e.offsets,S=e.position,y=e.gpuAcceleration,C=e.adaptive,A=e.roundOffsets,F=e.isFixed,W=T.x,R=W===void 0?0:W,H=T.y,K=H===void 0?0:H,V=typeof A=="function"?A({x:R,y:K}):{x:R,y:K};R=V.x,K=V.y;var ee=T.hasOwnProperty("x"),te=T.hasOwnProperty("y"),z=Ee,d=Se,B=window;if(C){var P=Lt(t),ae="clientHeight",le="clientWidth";if(P===De(t)&&(P=ct(t),at(P).position!=="static"&&S==="absolute"&&(ae="scrollHeight",le="scrollWidth")),P=P,o===Se||(o===Ee||o===Le)&&g===It){d=Ue;var u=F&&P===B&&B.visualViewport?B.visualViewport.height:P[ae];K-=u-n.height,K*=y?1:-1}if(o===Ee||(o===Se||o===Ue)&&g===It){z=Le;var w=F&&P===B&&B.visualViewport?B.visualViewport.width:P[le];R-=w-n.width,R*=y?1:-1}}var m=Object.assign({position:S},C&&Ea),k=A===!0?_a({x:R,y:K},De(t)):{x:R,y:K};if(R=k.x,K=k.y,y){var $;return Object.assign({},m,($={},$[d]=te?"0":"",$[z]=ee?"0":"",$.transform=(B.devicePixelRatio||1)<=1?"translate("+R+"px, "+K+"px)":"translate3d("+R+"px, "+K+"px, 0)",$))}return Object.assign({},m,(s={},s[d]=te?K+"px":"",s[z]=ee?R+"px":"",s.transform="",s))}function Aa(e){var s=e.state,t=e.options,n=t.gpuAcceleration,o=n===void 0?!0:n,g=t.adaptive,T=g===void 0?!0:g,S=t.roundOffsets,y=S===void 0?!0:S,C={placement:Ye(s.placement),variation:$t(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:o,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,ms(Object.assign({},C,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:T,roundOffsets:y})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,ms(Object.assign({},C,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:y})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Ma={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Aa,data:{}};var jt={passive:!0};function Da(e){var s=e.state,t=e.instance,n=e.options,o=n.scroll,g=o===void 0?!0:o,T=n.resize,S=T===void 0?!0:T,y=De(s.elements.popper),C=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&C.forEach(function(A){A.addEventListener("scroll",t.update,jt)}),S&&y.addEventListener("resize",t.update,jt),function(){g&&C.forEach(function(A){A.removeEventListener("scroll",t.update,jt)}),S&&y.removeEventListener("resize",t.update,jt)}}const Pa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Da,data:{}};var Oa={left:"right",right:"left",bottom:"top",top:"bottom"};function Bt(e){return e.replace(/left|right|bottom|top/g,function(s){return Oa[s]})}var Ia={start:"end",end:"start"};function bs(e){return e.replace(/start|end/g,function(s){return Ia[s]})}function is(e){var s=De(e),t=s.pageXOffset,n=s.pageYOffset;return{scrollLeft:t,scrollTop:n}}function ls(e){return kt(ct(e)).left+is(e).scrollLeft}function Ra(e,s){var t=De(e),n=ct(e),o=t.visualViewport,g=n.clientWidth,T=n.clientHeight,S=0,y=0;if(o){g=o.width,T=o.height;var C=js();(C||!C&&s==="fixed")&&(S=o.offsetLeft,y=o.offsetTop)}return{width:g,height:T,x:S+ls(e),y}}function Ua(e){var s,t=ct(e),n=is(e),o=(s=e.ownerDocument)==null?void 0:s.body,g=ft(t.scrollWidth,t.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),T=ft(t.scrollHeight,t.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),S=-n.scrollLeft+ls(e),y=-n.scrollTop;return at(o||t).direction==="rtl"&&(S+=ft(t.clientWidth,o?o.clientWidth:0)-g),{width:g,height:T,x:S,y}}function cs(e){var s=at(e),t=s.overflow,n=s.overflowX,o=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+o+n)}function Hs(e){return["html","body","#document"].indexOf(Ke(e))>=0?e.ownerDocument.body:Re(e)&&cs(e)?e:Hs(zt(e))}function Pt(e,s){var t;s===void 0&&(s=[]);var n=Hs(e),o=n===((t=e.ownerDocument)==null?void 0:t.body),g=De(n),T=o?[g].concat(g.visualViewport||[],cs(n)?n:[]):n,S=s.concat(T);return o?S:S.concat(Pt(zt(T)))}function es(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function La(e,s){var t=kt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function ws(e,s,t){return s===Us?es(Ra(e,t)):vt(s)?La(s,t):es(Ua(ct(e)))}function Fa(e){var s=Pt(zt(e)),t=["absolute","fixed"].indexOf(at(e).position)>=0,n=t&&Re(e)?Lt(e):e;return vt(n)?s.filter(function(o){return vt(o)&&Ws(o,n)&&Ke(o)!=="body"}):[]}function ja(e,s,t,n){var o=s==="clippingParents"?Fa(e):[].concat(s),g=[].concat(o,[t]),T=g[0],S=g.reduce(function(y,C){var A=ws(e,C,n);return y.top=ft(A.top,y.top),y.right=Vt(A.right,y.right),y.bottom=Vt(A.bottom,y.bottom),y.left=ft(A.left,y.left),y},ws(e,T,n));return S.width=S.right-S.left,S.height=S.bottom-S.top,S.x=S.left,S.y=S.top,S}function zs(e){var s=e.reference,t=e.element,n=e.placement,o=n?Ye(n):null,g=n?$t(n):null,T=s.x+s.width/2-t.width/2,S=s.y+s.height/2-t.height/2,y;switch(o){case Se:y={x:T,y:s.y-t.height};break;case Ue:y={x:T,y:s.y+s.height};break;case Le:y={x:s.x+s.width,y:S};break;case Ee:y={x:s.x-t.width,y:S};break;default:y={x:s.x,y:s.y}}var C=o?os(o):null;if(C!=null){var A=C==="y"?"height":"width";switch(g){case Tt:y[C]=y[C]-(s[A]/2-t[A]/2);break;case It:y[C]=y[C]+(s[A]/2-t[A]/2);break}}return y}function Rt(e,s){s===void 0&&(s={});var t=s,n=t.placement,o=n===void 0?e.placement:n,g=t.strategy,T=g===void 0?e.strategy:g,S=t.boundary,y=S===void 0?oa:S,C=t.rootBoundary,A=C===void 0?Us:C,F=t.elementContext,W=F===void 0?Et:F,R=t.altBoundary,H=R===void 0?!1:R,K=t.padding,V=K===void 0?0:K,ee=Ns(typeof V!="number"?V:Vs(V,Ut)),te=W===Et?ia:Et,z=e.rects.popper,d=e.elements[H?te:W],B=ja(vt(d)?d:d.contextElement||ct(e.elements.popper),y,A,T),P=kt(e.elements.reference),ae=zs({reference:P,element:z,strategy:"absolute",placement:o}),le=es(Object.assign({},z,ae)),u=W===Et?le:P,w={top:B.top-u.top+ee.top,bottom:u.bottom-B.bottom+ee.bottom,left:B.left-u.left+ee.left,right:u.right-B.right+ee.right},m=e.modifiersData.offset;if(W===Et&&m){var k=m[o];Object.keys(w).forEach(function($){var _=[Le,Ue].indexOf($)>=0?1:-1,L=[Se,Ue].indexOf($)>=0?"y":"x";w[$]+=k[L]*_})}return w}function Wa(e,s){s===void 0&&(s={});var t=s,n=t.placement,o=t.boundary,g=t.rootBoundary,T=t.padding,S=t.flipVariations,y=t.allowedAutoPlacements,C=y===void 0?Ls:y,A=$t(n),F=A?S?vs:vs.filter(function(H){return $t(H)===A}):Ut,W=F.filter(function(H){return C.indexOf(H)>=0});W.length===0&&(W=F);var R=W.reduce(function(H,K){return H[K]=Rt(e,{placement:K,boundary:o,rootBoundary:g,padding:T})[Ye(K)],H},{});return Object.keys(R).sort(function(H,K){return R[H]-R[K]})}function Ba(e){if(Ye(e)===ns)return[];var s=Bt(e);return[bs(e),s,bs(s)]}function Na(e){var s=e.state,t=e.options,n=e.name;if(!s.modifiersData[n]._skip){for(var o=t.mainAxis,g=o===void 0?!0:o,T=t.altAxis,S=T===void 0?!0:T,y=t.fallbackPlacements,C=t.padding,A=t.boundary,F=t.rootBoundary,W=t.altBoundary,R=t.flipVariations,H=R===void 0?!0:R,K=t.allowedAutoPlacements,V=s.options.placement,ee=Ye(V),te=ee===V,z=y||(te||!H?[Bt(V)]:Ba(V)),d=[V].concat(z).reduce(function(_e,be){return _e.concat(Ye(be)===ns?Wa(s,{placement:be,boundary:A,rootBoundary:F,padding:C,flipVariations:H,allowedAutoPlacements:K}):be)},[]),B=s.rects.reference,P=s.rects.popper,ae=new Map,le=!0,u=d[0],w=0;w<d.length;w++){var m=d[w],k=Ye(m),$=$t(m)===Tt,_=[Se,Ue].indexOf(k)>=0,L=_?"width":"height",E=Rt(s,{placement:m,boundary:A,rootBoundary:F,altBoundary:W,padding:C}),q=_?$?Le:Ee:$?Ue:Se;B[L]>P[L]&&(q=Bt(q));var fe=Bt(q),ge=[];if(g&&ge.push(E[k]<=0),S&&ge.push(E[q]<=0,E[fe]<=0),ge.every(function(_e){return _e})){u=m,le=!1;break}ae.set(m,ge)}if(le)for(var xe=H?3:1,Fe=function(be){var Ce=d.find(function(G){var we=ae.get(G);if(we)return we.slice(0,be).every(function($e){return $e})});if(Ce)return u=Ce,"break"},me=xe;me>0;me--){var Te=Fe(me);if(Te==="break")break}s.placement!==u&&(s.modifiersData[n]._skip=!0,s.placement=u,s.reset=!0)}}const Va={name:"flip",enabled:!0,phase:"main",fn:Na,requiresIfExists:["offset"],data:{_skip:!1}};function ys(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function xs(e){return[Se,Le,Ue,Ee].some(function(s){return e[s]>=0})}function Ha(e){var s=e.state,t=e.name,n=s.rects.reference,o=s.rects.popper,g=s.modifiersData.preventOverflow,T=Rt(s,{elementContext:"reference"}),S=Rt(s,{altBoundary:!0}),y=ys(T,n),C=ys(S,o,g),A=xs(y),F=xs(C);s.modifiersData[t]={referenceClippingOffsets:y,popperEscapeOffsets:C,isReferenceHidden:A,hasPopperEscaped:F},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":A,"data-popper-escaped":F})}const za={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ha};function qa(e,s,t){var n=Ye(e),o=[Ee,Se].indexOf(n)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,T=g[0],S=g[1];return T=T||0,S=(S||0)*o,[Ee,Le].indexOf(n)>=0?{x:S,y:T}:{x:T,y:S}}function Ja(e){var s=e.state,t=e.options,n=e.name,o=t.offset,g=o===void 0?[0,0]:o,T=Ls.reduce(function(A,F){return A[F]=qa(F,s.rects,g),A},{}),S=T[s.placement],y=S.x,C=S.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=y,s.modifiersData.popperOffsets.y+=C),s.modifiersData[n]=T}const Ya={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Ja};function Ka(e){var s=e.state,t=e.name;s.modifiersData[t]=zs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const Xa={name:"popperOffsets",enabled:!0,phase:"read",fn:Ka,data:{}};function Ga(e){return e==="x"?"y":"x"}function Za(e){var s=e.state,t=e.options,n=e.name,o=t.mainAxis,g=o===void 0?!0:o,T=t.altAxis,S=T===void 0?!1:T,y=t.boundary,C=t.rootBoundary,A=t.altBoundary,F=t.padding,W=t.tether,R=W===void 0?!0:W,H=t.tetherOffset,K=H===void 0?0:H,V=Rt(s,{boundary:y,rootBoundary:C,padding:F,altBoundary:A}),ee=Ye(s.placement),te=$t(s.placement),z=!te,d=os(ee),B=Ga(d),P=s.modifiersData.popperOffsets,ae=s.rects.reference,le=s.rects.popper,u=typeof K=="function"?K(Object.assign({},s.rects,{placement:s.placement})):K,w=typeof u=="number"?{mainAxis:u,altAxis:u}:Object.assign({mainAxis:0,altAxis:0},u),m=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,k={x:0,y:0};if(P){if(g){var $,_=d==="y"?Se:Ee,L=d==="y"?Ue:Le,E=d==="y"?"height":"width",q=P[d],fe=q+V[_],ge=q-V[L],xe=R?-le[E]/2:0,Fe=te===Tt?ae[E]:le[E],me=te===Tt?-le[E]:-ae[E],Te=s.elements.arrow,_e=R&&Te?rs(Te):{width:0,height:0},be=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Bs(),Ce=be[_],G=be[L],we=Dt(0,ae[E],_e[E]),$e=z?ae[E]/2-xe-we-Ce-w.mainAxis:Fe-we-Ce-w.mainAxis,ce=z?-ae[E]/2+xe+we+G+w.mainAxis:me+we+G+w.mainAxis,Xe=s.elements.arrow&&Lt(s.elements.arrow),Pe=Xe?d==="y"?Xe.clientTop||0:Xe.clientLeft||0:0,Ge=($=m==null?void 0:m[d])!=null?$:0,He=q+$e-Ge-Pe,Ze=q+ce-Ge,ve=Dt(R?Vt(fe,He):fe,q,R?ft(ge,Ze):ge);P[d]=ve,k[d]=ve-q}if(S){var Qe,je=d==="x"?Se:Ee,rt=d==="x"?Ue:Le,Ae=P[B],Oe=B==="y"?"height":"width",et=Ae+V[je],We=Ae-V[rt],ze=[Se,Ee].indexOf(ee)!==-1,tt=(Qe=m==null?void 0:m[B])!=null?Qe:0,qe=ze?et:Ae-ae[Oe]-le[Oe]-tt+w.altAxis,ot=ze?Ae+ae[Oe]+le[Oe]-tt-w.altAxis:We,st=R&&ze?Ta(qe,Ae,ot):Dt(R?qe:et,Ae,R?ot:We);P[B]=st,k[B]=st-Ae}s.modifiersData[n]=k}}const Qa={name:"preventOverflow",enabled:!0,phase:"main",fn:Za,requiresIfExists:["offset"]};function er(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function tr(e){return e===De(e)||!Re(e)?is(e):er(e)}function sr(e){var s=e.getBoundingClientRect(),t=Ct(s.width)/e.offsetWidth||1,n=Ct(s.height)/e.offsetHeight||1;return t!==1||n!==1}function nr(e,s,t){t===void 0&&(t=!1);var n=Re(s),o=Re(s)&&sr(s),g=ct(s),T=kt(e,o,t),S={scrollLeft:0,scrollTop:0},y={x:0,y:0};return(n||!n&&!t)&&((Ke(s)!=="body"||cs(g))&&(S=tr(s)),Re(s)?(y=kt(s,!0),y.x+=s.clientLeft,y.y+=s.clientTop):g&&(y.x=ls(g))),{x:T.left+S.scrollLeft-y.x,y:T.top+S.scrollTop-y.y,width:T.width,height:T.height}}function ar(e){var s=new Map,t=new Set,n=[];e.forEach(function(g){s.set(g.name,g)});function o(g){t.add(g.name);var T=[].concat(g.requires||[],g.requiresIfExists||[]);T.forEach(function(S){if(!t.has(S)){var y=s.get(S);y&&o(y)}}),n.push(g)}return e.forEach(function(g){t.has(g.name)||o(g)}),n}function rr(e){var s=ar(e);return ma.reduce(function(t,n){return t.concat(s.filter(function(o){return o.phase===n}))},[])}function or(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function ir(e){var s=e.reduce(function(t,n){var o=t[n.name];return t[n.name]=o?Object.assign({},o,n,{options:Object.assign({},o.options,n.options),data:Object.assign({},o.data,n.data)}):n,t},{});return Object.keys(s).map(function(t){return s[t]})}var Ts={placement:"bottom",modifiers:[],strategy:"absolute"};function Cs(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function lr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,n=t===void 0?[]:t,o=s.defaultOptions,g=o===void 0?Ts:o;return function(S,y,C){C===void 0&&(C=g);var A={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ts,g),modifiersData:{},elements:{reference:S,popper:y},attributes:{},styles:{}},F=[],W=!1,R={state:A,setOptions:function(ee){var te=typeof ee=="function"?ee(A.options):ee;K(),A.options=Object.assign({},g,A.options,te),A.scrollParents={reference:vt(S)?Pt(S):S.contextElement?Pt(S.contextElement):[],popper:Pt(y)};var z=rr(ir([].concat(n,A.options.modifiers)));return A.orderedModifiers=z.filter(function(d){return d.enabled}),H(),R.update()},forceUpdate:function(){if(!W){var ee=A.elements,te=ee.reference,z=ee.popper;if(Cs(te,z)){A.rects={reference:nr(te,Lt(z),A.options.strategy==="fixed"),popper:rs(z)},A.reset=!1,A.placement=A.options.placement,A.orderedModifiers.forEach(function(w){return A.modifiersData[w.name]=Object.assign({},w.data)});for(var d=0;d<A.orderedModifiers.length;d++){if(A.reset===!0){A.reset=!1,d=-1;continue}var B=A.orderedModifiers[d],P=B.fn,ae=B.options,le=ae===void 0?{}:ae,u=B.name;typeof P=="function"&&(A=P({state:A,options:le,name:u,instance:R})||A)}}}},update:or(function(){return new Promise(function(V){R.forceUpdate(),V(A)})}),destroy:function(){K(),W=!0}};if(!Cs(S,y))return R;R.setOptions(C).then(function(V){!W&&C.onFirstUpdate&&C.onFirstUpdate(V)});function H(){A.orderedModifiers.forEach(function(V){var ee=V.name,te=V.options,z=te===void 0?{}:te,d=V.effect;if(typeof d=="function"){var B=d({state:A,name:ee,instance:R,options:z}),P=function(){};F.push(B||P)}})}function K(){F.forEach(function(V){return V()}),F=[]}return R}}var cr=[Pa,Xa,Ma,Fs,Ya,Va,Qa,Sa,za],ur=lr({defaultModifiers:cr}),pr="tippy-box",qs="tippy-content",dr="tippy-backdrop",Js="tippy-arrow",Ys="tippy-svg-arrow",dt={passive:!0,capture:!0},Ks=function(){return document.body};function Kt(e,s,t){if(Array.isArray(e)){var n=e[s];return n??(Array.isArray(t)?t[s]:t)}return e}function us(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function Xs(e,s){return typeof e=="function"?e.apply(void 0,s):e}function ks(e,s){if(s===0)return e;var t;return function(n){clearTimeout(t),t=setTimeout(function(){e(n)},s)}}function fr(e){return e.split(/\s+/).filter(Boolean)}function xt(e){return[].concat(e)}function $s(e,s){e.indexOf(s)===-1&&e.push(s)}function hr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function vr(e){return e.split("-")[0]}function Ht(e){return[].slice.call(e)}function Ss(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Ot(){return document.createElement("div")}function qt(e){return["Element","Fragment"].some(function(s){return us(e,s)})}function gr(e){return us(e,"NodeList")}function mr(e){return us(e,"MouseEvent")}function br(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function wr(e){return qt(e)?[e]:gr(e)?Ht(e):Array.isArray(e)?e:Ht(document.querySelectorAll(e))}function Xt(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function Es(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function yr(e){var s,t=xt(e),n=t[0];return n!=null&&(s=n.ownerDocument)!=null&&s.body?n.ownerDocument:document}function xr(e,s){var t=s.clientX,n=s.clientY;return e.every(function(o){var g=o.popperRect,T=o.popperState,S=o.props,y=S.interactiveBorder,C=vr(T.placement),A=T.modifiersData.offset;if(!A)return!0;var F=C==="bottom"?A.top.y:0,W=C==="top"?A.bottom.y:0,R=C==="right"?A.left.x:0,H=C==="left"?A.right.x:0,K=g.top-n+F>y,V=n-g.bottom-W>y,ee=g.left-t+R>y,te=t-g.right-H>y;return K||V||ee||te})}function Gt(e,s,t){var n=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(o){e[n](o,t)})}function _s(e,s){for(var t=s;t;){var n;if(e.contains(t))return!0;t=t.getRootNode==null||(n=t.getRootNode())==null?void 0:n.host}return!1}var Je={isTouch:!1},As=0;function Tr(){Je.isTouch||(Je.isTouch=!0,window.performance&&document.addEventListener("mousemove",Gs))}function Gs(){var e=performance.now();e-As<20&&(Je.isTouch=!1,document.removeEventListener("mousemove",Gs)),As=e}function Cr(){var e=document.activeElement;if(br(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function kr(){document.addEventListener("touchstart",Tr,dt),window.addEventListener("blur",Cr)}var $r=typeof window<"u"&&typeof document<"u",Sr=$r?!!window.msCrypto:!1,Er={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},_r={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Ve=Object.assign({appendTo:Ks,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Er,_r),Ar=Object.keys(Ve),Mr=function(s){var t=Object.keys(s);t.forEach(function(n){Ve[n]=s[n]})};function Zs(e){var s=e.plugins||[],t=s.reduce(function(n,o){var g=o.name,T=o.defaultValue;if(g){var S;n[g]=e[g]!==void 0?e[g]:(S=Ve[g])!=null?S:T}return n},{});return Object.assign({},e,t)}function Dr(e,s){var t=s?Object.keys(Zs(Object.assign({},Ve,{plugins:s}))):Ar,n=t.reduce(function(o,g){var T=(e.getAttribute("data-tippy-"+g)||"").trim();if(!T)return o;if(g==="content")o[g]=T;else try{o[g]=JSON.parse(T)}catch{o[g]=T}return o},{});return n}function Ms(e,s){var t=Object.assign({},s,{content:Xs(s.content,[e])},s.ignoreAttributes?{}:Dr(e,s.plugins));return t.aria=Object.assign({},Ve.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Pr=function(){return"innerHTML"};function ts(e,s){e[Pr()]=s}function Ds(e){var s=Ot();return e===!0?s.className=Js:(s.className=Ys,qt(e)?s.appendChild(e):ts(s,e)),s}function Ps(e,s){qt(s.content)?(ts(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?ts(e,s.content):e.textContent=s.content)}function ss(e){var s=e.firstElementChild,t=Ht(s.children);return{box:s,content:t.find(function(n){return n.classList.contains(qs)}),arrow:t.find(function(n){return n.classList.contains(Js)||n.classList.contains(Ys)}),backdrop:t.find(function(n){return n.classList.contains(dr)})}}function Qs(e){var s=Ot(),t=Ot();t.className=pr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var n=Ot();n.className=qs,n.setAttribute("data-state","hidden"),Ps(n,e.props),s.appendChild(t),t.appendChild(n),o(e.props,e.props);function o(g,T){var S=ss(s),y=S.box,C=S.content,A=S.arrow;T.theme?y.setAttribute("data-theme",T.theme):y.removeAttribute("data-theme"),typeof T.animation=="string"?y.setAttribute("data-animation",T.animation):y.removeAttribute("data-animation"),T.inertia?y.setAttribute("data-inertia",""):y.removeAttribute("data-inertia"),y.style.maxWidth=typeof T.maxWidth=="number"?T.maxWidth+"px":T.maxWidth,T.role?y.setAttribute("role",T.role):y.removeAttribute("role"),(g.content!==T.content||g.allowHTML!==T.allowHTML)&&Ps(C,e.props),T.arrow?A?g.arrow!==T.arrow&&(y.removeChild(A),y.appendChild(Ds(T.arrow))):y.appendChild(Ds(T.arrow)):A&&y.removeChild(A)}return{popper:s,onUpdate:o}}Qs.$$tippy=!0;var Or=1,Wt=[],Zt=[];function Ir(e,s){var t=Ms(e,Object.assign({},Ve,Zs(Ss(s)))),n,o,g,T=!1,S=!1,y=!1,C=!1,A,F,W,R=[],H=ks(He,t.interactiveDebounce),K,V=Or++,ee=null,te=hr(t.plugins),z={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},d={id:V,reference:e,popper:Ot(),popperInstance:ee,props:t,state:z,plugins:te,clearDelayTimeouts:qe,setProps:ot,setContent:st,show:St,hide:gt,hideWithInteractivity:it,enable:ze,disable:tt,unmount:lt,destroy:ut};if(!t.render)return d;var B=t.render(d),P=B.popper,ae=B.onUpdate;P.setAttribute("data-tippy-root",""),P.id="tippy-"+d.id,d.popper=P,e._tippy=d,P._tippy=d;var le=te.map(function(x){return x.fn(d)}),u=e.hasAttribute("aria-expanded");return Xe(),xe(),q(),fe("onCreate",[d]),t.showOnCreate&&et(),P.addEventListener("mouseenter",function(){d.props.interactive&&d.state.isVisible&&d.clearDelayTimeouts()}),P.addEventListener("mouseleave",function(){d.props.interactive&&d.props.trigger.indexOf("mouseenter")>=0&&_().addEventListener("mousemove",H)}),d;function w(){var x=d.props.touch;return Array.isArray(x)?x:[x,0]}function m(){return w()[0]==="hold"}function k(){var x;return!!((x=d.props.render)!=null&&x.$$tippy)}function $(){return K||e}function _(){var x=$().parentNode;return x?yr(x):document}function L(){return ss(P)}function E(x){return d.state.isMounted&&!d.state.isVisible||Je.isTouch||A&&A.type==="focus"?0:Kt(d.props.delay,x?0:1,Ve.delay)}function q(x){x===void 0&&(x=!1),P.style.pointerEvents=d.props.interactive&&!x?"":"none",P.style.zIndex=""+d.props.zIndex}function fe(x,j,b){if(b===void 0&&(b=!0),le.forEach(function(I){I[x]&&I[x].apply(I,j)}),b){var p;(p=d.props)[x].apply(p,j)}}function ge(){var x=d.props.aria;if(x.content){var j="aria-"+x.content,b=P.id,p=xt(d.props.triggerTarget||e);p.forEach(function(I){var N=I.getAttribute(j);if(d.state.isVisible)I.setAttribute(j,N?N+" "+b:b);else{var oe=N&&N.replace(b,"").trim();oe?I.setAttribute(j,oe):I.removeAttribute(j)}})}}function xe(){if(!(u||!d.props.aria.expanded)){var x=xt(d.props.triggerTarget||e);x.forEach(function(j){d.props.interactive?j.setAttribute("aria-expanded",d.state.isVisible&&j===$()?"true":"false"):j.removeAttribute("aria-expanded")})}}function Fe(){_().removeEventListener("mousemove",H),Wt=Wt.filter(function(x){return x!==H})}function me(x){if(!(Je.isTouch&&(y||x.type==="mousedown"))){var j=x.composedPath&&x.composedPath()[0]||x.target;if(!(d.props.interactive&&_s(P,j))){if(xt(d.props.triggerTarget||e).some(function(b){return _s(b,j)})){if(Je.isTouch||d.state.isVisible&&d.props.trigger.indexOf("click")>=0)return}else fe("onClickOutside",[d,x]);d.props.hideOnClick===!0&&(d.clearDelayTimeouts(),d.hide(),S=!0,setTimeout(function(){S=!1}),d.state.isMounted||Ce())}}}function Te(){y=!0}function _e(){y=!1}function be(){var x=_();x.addEventListener("mousedown",me,!0),x.addEventListener("touchend",me,dt),x.addEventListener("touchstart",_e,dt),x.addEventListener("touchmove",Te,dt)}function Ce(){var x=_();x.removeEventListener("mousedown",me,!0),x.removeEventListener("touchend",me,dt),x.removeEventListener("touchstart",_e,dt),x.removeEventListener("touchmove",Te,dt)}function G(x,j){$e(x,function(){!d.state.isVisible&&P.parentNode&&P.parentNode.contains(P)&&j()})}function we(x,j){$e(x,j)}function $e(x,j){var b=L().box;function p(I){I.target===b&&(Gt(b,"remove",p),j())}if(x===0)return j();Gt(b,"remove",F),Gt(b,"add",p),F=p}function ce(x,j,b){b===void 0&&(b=!1);var p=xt(d.props.triggerTarget||e);p.forEach(function(I){I.addEventListener(x,j,b),R.push({node:I,eventType:x,handler:j,options:b})})}function Xe(){m()&&(ce("touchstart",Ge,{passive:!0}),ce("touchend",Ze,{passive:!0})),fr(d.props.trigger).forEach(function(x){if(x!=="manual")switch(ce(x,Ge),x){case"mouseenter":ce("mouseleave",Ze);break;case"focus":ce(Sr?"focusout":"blur",ve);break;case"focusin":ce("focusout",ve);break}})}function Pe(){R.forEach(function(x){var j=x.node,b=x.eventType,p=x.handler,I=x.options;j.removeEventListener(b,p,I)}),R=[]}function Ge(x){var j,b=!1;if(!(!d.state.isEnabled||Qe(x)||S)){var p=((j=A)==null?void 0:j.type)==="focus";A=x,K=x.currentTarget,xe(),!d.state.isVisible&&mr(x)&&Wt.forEach(function(I){return I(x)}),x.type==="click"&&(d.props.trigger.indexOf("mouseenter")<0||T)&&d.props.hideOnClick!==!1&&d.state.isVisible?b=!0:et(x),x.type==="click"&&(T=!b),b&&!p&&We(x)}}function He(x){var j=x.target,b=$().contains(j)||P.contains(j);if(!(x.type==="mousemove"&&b)){var p=Oe().concat(P).map(function(I){var N,oe=I._tippy,re=(N=oe.popperInstance)==null?void 0:N.state;return re?{popperRect:I.getBoundingClientRect(),popperState:re,props:t}:null}).filter(Boolean);xr(p,x)&&(Fe(),We(x))}}function Ze(x){var j=Qe(x)||d.props.trigger.indexOf("click")>=0&&T;if(!j){if(d.props.interactive){d.hideWithInteractivity(x);return}We(x)}}function ve(x){d.props.trigger.indexOf("focusin")<0&&x.target!==$()||d.props.interactive&&x.relatedTarget&&P.contains(x.relatedTarget)||We(x)}function Qe(x){return Je.isTouch?m()!==x.type.indexOf("touch")>=0:!1}function je(){rt();var x=d.props,j=x.popperOptions,b=x.placement,p=x.offset,I=x.getReferenceClientRect,N=x.moveTransition,oe=k()?ss(P).arrow:null,re=I?{getBoundingClientRect:I,contextElement:I.contextElement||$()}:e,a={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(v){var i=v.state;if(k()){var h=L(),c=h.box;["placement","reference-hidden","escaped"].forEach(function(O){O==="placement"?c.setAttribute("data-placement",i.placement):i.attributes.popper["data-popper-"+O]?c.setAttribute("data-"+O,""):c.removeAttribute("data-"+O)}),i.attributes.popper={}}}},r=[{name:"offset",options:{offset:p}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!N}},a];k()&&oe&&r.push({name:"arrow",options:{element:oe,padding:3}}),r.push.apply(r,(j==null?void 0:j.modifiers)||[]),d.popperInstance=ur(re,P,Object.assign({},j,{placement:b,onFirstUpdate:W,modifiers:r}))}function rt(){d.popperInstance&&(d.popperInstance.destroy(),d.popperInstance=null)}function Ae(){var x=d.props.appendTo,j,b=$();d.props.interactive&&x===Ks||x==="parent"?j=b.parentNode:j=Xs(x,[b]),j.contains(P)||j.appendChild(P),d.state.isMounted=!0,je()}function Oe(){return Ht(P.querySelectorAll("[data-tippy-root]"))}function et(x){d.clearDelayTimeouts(),x&&fe("onTrigger",[d,x]),be();var j=E(!0),b=w(),p=b[0],I=b[1];Je.isTouch&&p==="hold"&&I&&(j=I),j?n=setTimeout(function(){d.show()},j):d.show()}function We(x){if(d.clearDelayTimeouts(),fe("onUntrigger",[d,x]),!d.state.isVisible){Ce();return}if(!(d.props.trigger.indexOf("mouseenter")>=0&&d.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(x.type)>=0&&T)){var j=E(!1);j?o=setTimeout(function(){d.state.isVisible&&d.hide()},j):g=requestAnimationFrame(function(){d.hide()})}}function ze(){d.state.isEnabled=!0}function tt(){d.hide(),d.state.isEnabled=!1}function qe(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(g)}function ot(x){if(!d.state.isDestroyed){fe("onBeforeUpdate",[d,x]),Pe();var j=d.props,b=Ms(e,Object.assign({},j,Ss(x),{ignoreAttributes:!0}));d.props=b,Xe(),j.interactiveDebounce!==b.interactiveDebounce&&(Fe(),H=ks(He,b.interactiveDebounce)),j.triggerTarget&&!b.triggerTarget?xt(j.triggerTarget).forEach(function(p){p.removeAttribute("aria-expanded")}):b.triggerTarget&&e.removeAttribute("aria-expanded"),xe(),q(),ae&&ae(j,b),d.popperInstance&&(je(),Oe().forEach(function(p){requestAnimationFrame(p._tippy.popperInstance.forceUpdate)})),fe("onAfterUpdate",[d,x])}}function st(x){d.setProps({content:x})}function St(){var x=d.state.isVisible,j=d.state.isDestroyed,b=!d.state.isEnabled,p=Je.isTouch&&!d.props.touch,I=Kt(d.props.duration,0,Ve.duration);if(!(x||j||b||p)&&!$().hasAttribute("disabled")&&(fe("onShow",[d],!1),d.props.onShow(d)!==!1)){if(d.state.isVisible=!0,k()&&(P.style.visibility="visible"),q(),be(),d.state.isMounted||(P.style.transition="none"),k()){var N=L(),oe=N.box,re=N.content;Xt([oe,re],0)}W=function(){var r;if(!(!d.state.isVisible||C)){if(C=!0,P.offsetHeight,P.style.transition=d.props.moveTransition,k()&&d.props.animation){var l=L(),v=l.box,i=l.content;Xt([v,i],I),Es([v,i],"visible")}ge(),xe(),$s(Zt,d),(r=d.popperInstance)==null||r.forceUpdate(),fe("onMount",[d]),d.props.animation&&k()&&we(I,function(){d.state.isShown=!0,fe("onShown",[d])})}},Ae()}}function gt(){var x=!d.state.isVisible,j=d.state.isDestroyed,b=!d.state.isEnabled,p=Kt(d.props.duration,1,Ve.duration);if(!(x||j||b)&&(fe("onHide",[d],!1),d.props.onHide(d)!==!1)){if(d.state.isVisible=!1,d.state.isShown=!1,C=!1,T=!1,k()&&(P.style.visibility="hidden"),Fe(),Ce(),q(!0),k()){var I=L(),N=I.box,oe=I.content;d.props.animation&&(Xt([N,oe],p),Es([N,oe],"hidden"))}ge(),xe(),d.props.animation?k()&&G(p,d.unmount):d.unmount()}}function it(x){_().addEventListener("mousemove",H),$s(Wt,H),H(x)}function lt(){d.state.isVisible&&d.hide(),d.state.isMounted&&(rt(),Oe().forEach(function(x){x._tippy.unmount()}),P.parentNode&&P.parentNode.removeChild(P),Zt=Zt.filter(function(x){return x!==d}),d.state.isMounted=!1,fe("onHidden",[d]))}function ut(){d.state.isDestroyed||(d.clearDelayTimeouts(),d.unmount(),Pe(),delete e._tippy,d.state.isDestroyed=!0,fe("onDestroy",[d]))}}function ht(e,s){s===void 0&&(s={});var t=Ve.plugins.concat(s.plugins||[]);kr();var n=Object.assign({},s,{plugins:t}),o=wr(e),g=o.reduce(function(T,S){var y=S&&Ir(S,n);return y&&T.push(y),T},[]);return qt(e)?g[0]:g}ht.defaultProps=Ve;ht.setDefaultProps=Mr;ht.currentInput=Je;Object.assign({},Fs,{effect:function(s){var t=s.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}});ht.setDefaultProps({render:Qs});const Rr={class:"task-pane"},Ur={key:0,class:"loading-overlay"},Lr={key:1,class:"format-error-overlay"},Fr={class:"format-error-content"},jr={class:"format-error-message"},Wr={class:"format-error-actions"},Br={class:"doc-header"},Nr={class:"doc-title"},Vr={class:"action-area"},Hr={class:"select-container"},zr={class:"select-group"},qr=["disabled"],Jr=["value"],Yr={class:"select-group"},Kr=["disabled"],Xr=["value"],Gr=["title"],Zr={key:0,class:"science-warning"},Qr={class:"action-buttons"},eo=["disabled"],to={class:"btn-content"},so={key:0,class:"button-loader"},no=["disabled"],ao={class:"btn-content"},ro={key:0,class:"button-loader"},oo=["disabled"],io={class:"btn-content"},lo={key:0,class:"button-loader"},co={class:"content-area"},uo={class:"modal-header"},po={class:"modal-body"},fo={class:"selection-content"},ho={class:"modal-header"},vo={class:"modal-body"},go={class:"alert-message"},mo={class:"alert-actions"},bo={key:2,class:"modal-overlay"},wo={class:"modal-header"},yo={class:"modal-body"},xo={class:"confirm-message"},To={class:"confirm-actions"},Co={class:"task-queue"},ko={class:"queue-header"},$o={class:"queue-status-filter"},So=["value"],Eo={class:"queue-actions"},_o=["disabled","title"],Ao={class:"task-count"},Mo={key:0,class:"queue-table-container"},Do={class:"col-id"},Po={class:"id-header"},Oo={key:0,class:"col-subject"},Io={class:"subject-header"},Ro={class:"switch"},Uo=["title"],Lo={key:1,class:"col-status"},Fo=["onClick"],jo={class:"col-id"},Wo={class:"id-content"},Bo={class:"task-id"},No={key:0,class:"enhance-svg-icon"},Vo={key:0,class:"status-in-id"},Ho={key:0,class:"col-subject"},zo=["onMouseenter"],qo={key:1,class:"col-status"},Jo={class:"status-cell"},Yo={class:"col-actions"},Ko={class:"task-actions"},Xo=["onClick"],Go=["onClick"],Zo={key:2,class:"no-action-icon",title:"无可用操作"},Qo={key:1,class:"empty-queue"},ei={key:3,class:"log-container"},ti={class:"log-actions"},si={class:"toggle-icon"},ni=["innerHTML"],ai={__name:"TaskPane",setup(e){const s=pe(!1),t=pe(!1),n=pe(!1),o=pe(""),g=pe(!1),T=pe(!1),S=pe(!1),y=pe(!1),C=pe(!0),A=pe(""),F=pe(!1),W=pe(window.innerWidth),R=bt(()=>W.value<750),H=bt(()=>W.value<380),K=()=>{W.value=window.innerWidth},V=pe(null),ee=pe(!1),te=pe(""),z={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},d=pe(!1),B=pe([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"}]),{docName:P,selected:ae,logger:le,map:u,subject:w,stage:m,subjectOptions:k,stageOptions:$,appConfig:_,clearLog:L,checkDocumentFormat:E,getTaskStatusClass:q,getTaskStatusText:fe,terminateTask:ge,run1:xe,runCheck:Fe,setupLifecycle:me,navigateToTaskControl:Te,isLoading:_e,tryRemoveTaskPlaceholderWithLoading:be,confirmDialog:Ce,handleConfirm:G,getCompletedTasksCount:we,showConfirm:$e}=bn(),ce=pe(null);(()=>{var b;try{if((b=window.Application)!=null&&b.PluginStorage){const p=window.Application.PluginStorage.getItem("user_info");p?(ce.value=JSON.parse(p),console.log("用户信息已加载:",ce.value)):console.log("未找到用户信息")}}catch(p){console.error("解析用户信息时出错:",p)}})(),Nt(ce,b=>{b&&b.orgs&&b.orgs[0]&&console.log(`用户企业ID: ${b.orgs[0].orgId}, 校对功能${b.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Pe=bt(()=>!ce.value||ce.value.isAdmin||ce.value.isOwner?k:ce.value.subject?k.filter(b=>b.value===ce.value.subject):k),Ge=()=>{ce.value&&!ce.value.isAdmin&&!ce.value.isOwner&&ce.value.subject&&(w.value=ce.value.subject)},He=bt(()=>["physics","chemistry","biology","math"].includes(w.value));Nt(_,b=>{b&&(console.log("TaskPane组件收到应用配置更新:",b),console.log("当前版本类型:",b.EDITION),console.log("当前年级选项:",$.value))},{deep:!0,immediate:!0});const Ze=()=>{try{const b=E();C.value=b.isValid,A.value=b.message,F.value=!b.isValid,b.isValid||console.warn("文档格式检查失败:",b.message)}catch(b){console.error("执行文档格式检查时出错:",b),C.value=!1,A.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",F.value=!0}},ve=bt(()=>{let b={};const p=u;if(te.value==="")b={...p};else for(const I in p)if(Object.prototype.hasOwnProperty.call(p,I)){const N=p[I];N.status===te.value&&(b[I]=N)}return ee.value&&V.value?b[V.value]?{[V.value]:b[V.value]}:{}:b}),Qe=bt(()=>{const b=ve.value;return Object.entries(b).map(([I,N])=>({tid:I,...N})).sort((I,N)=>{const oe=I.startTime||0;return(N.startTime||0)-oe}).reduce((I,N)=>{const{tid:oe,...re}=N;return I[oe]=re,I},{})}),je=(b="wps-analysis")=>{w.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(o.value="未选中内容",t.value=!0):(b==="wps-analysis"?T.value=!0:b==="wps-enhance_analysis"&&(S.value=!0),xe(b).catch(p=>{console.log(p),p.message.includes("重叠")?(o.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",p),o.value=p.message,t.value=!0)}).finally(()=>{b==="wps-analysis"?T.value=!1:b==="wps-enhance_analysis"&&(S.value=!1)})):(o.value="请选择学科",t.value=!0)},rt=()=>{w.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(o.value="未选中内容",t.value=!0):(y.value=!0,Fe().catch(b=>{console.log(b),b.message.includes("重叠")?(o.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",b),o.value=b.message,t.value=!0)}).finally(()=>{y.value=!1})):(o.value="请选择学科",t.value=!0)},Ae=(b,p)=>{V.value=b,Te(b)},Oe=b=>{u[b]&&(u[b].status=3),V.value===b&&(V.value=null),be(b,!0)},et=async()=>{const b=Object.entries(u).filter(([p,I])=>I.status===2);if(b.length===0){o.value="没有已完成的任务可释放",t.value=!0;return}try{if(await $e(`确定要释放所有 ${b.length} 个已完成的任务吗？
此操作不可撤销。`)){let I=0;b.forEach(([N,oe])=>{u[N]&&(u[N].status=3,V.value===N&&(V.value=null),be(N,!0),I++)}),o.value=`已成功释放 ${I} 个任务`,t.value=!0}}catch(p){console.error("释放任务时出错:",p),o.value="释放任务时出现错误",t.value=!0}},We=()=>{g.value=!g.value},ze=b=>b?b.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",tt=b=>{const p=qe(b);return p?ze(p):"无题目内容"},qe=b=>{if(!b.selectedText)return"";const p=b.selectedText.split("\r").filter(N=>N.trim());if(p.length===1){const N=b.selectedText.split(`
`).filter(oe=>oe.trim());N.length>1&&p.splice(0,1,...N)}const I=p.map((N,oe)=>{const re=N.trim();return re.length>200,re});return I.length===1?p[0].trim():I.join(`
`)},ot=(b,p)=>{if(!d.value)return;const I=b.target,N=qe(p).toString();if(!N||N.trim()===""){console.log("题目内容为空，不显示tooltip");return}const oe=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${N.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,re=b.clientX,a=b.clientY;if(z.subjects.has(I)){const l=z.subjects.get(I);l.setContent(oe),l.setProps({getReferenceClientRect:()=>({width:0,height:0,top:a,bottom:a,left:re,right:re})}),l.show();return}const r=ht(I,{content:oe,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:a,bottom:a,left:re,right:re}),onHidden:()=>{r.setProps({getReferenceClientRect:null})}});z.subjects.set(I,r),r.show()},st=b=>{const p=b.currentTarget,I=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,N=b.clientX,oe=b.clientY;if(z.enhance.has(p)){const a=z.enhance.get(p);a.setProps({getReferenceClientRect:()=>({width:0,height:0,top:oe,bottom:oe,left:N,right:N})}),a.show();return}const re=ht(p,{content:I,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});z.enhance.set(p,re),re.show()},St=()=>{z.subjects.forEach(b=>{b.destroy()}),z.subjects.clear(),z.enhance.forEach(b=>{b.destroy()}),z.enhance.clear(),z.softBreak.forEach(b=>{b.destroy()}),z.softBreak.clear(),document.removeEventListener("click",gt),document.removeEventListener("mousemove",lt)},gt=b=>{const p=document.querySelector(".tippy-box");p&&!p.contains(b.target)&&(z.subjects.forEach(I=>I.hide()),z.enhance.forEach(I=>I.hide()),z.softBreak.forEach(I=>I.hide()))};let it=0;const lt=b=>{const p=Date.now();if(p-it<100)return;it=p;const I=document.querySelector(".tippy-box");if(!I)return;const N=I.getBoundingClientRect();!(b.clientX>=N.left-20&&b.clientX<=N.right+20&&b.clientY>=N.top-20&&b.clientY<=N.bottom+20)&&!I.matches(":hover")&&(z.subjects.forEach(re=>re.hide()),z.enhance.forEach(re=>re.hide()),z.softBreak.forEach(re=>re.hide()))},ut=()=>{document.addEventListener("click",gt),document.addEventListener("mousemove",lt)};Os(()=>{window.addEventListener("resize",K),ut(),Ge(),setTimeout(()=>{Ze()},500);const b=document.createElement("style");b.id="tippy-custom-styles",b.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(b)}),an(()=>{window.removeEventListener("resize",K),St();const b=document.getElementById("tippy-custom-styles");b&&b.remove()}),me();const x=b=>b.selectedText?b.selectedText.includes("\v")||b.selectedText.includes("\v"):!1,j=b=>{const p=b.currentTarget,I=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,N=b.clientX,oe=b.clientY;if(z.softBreak.has(p)){const a=z.softBreak.get(p);a.setProps({getReferenceClientRect:()=>({width:0,height:0,top:oe,bottom:oe,left:N,right:N})}),a.show();return}const re=ht(p,{content:I,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});z.softBreak.set(p,re),re.show()};return(b,p)=>{var I,N,oe,re,a;return J(),Y("div",Rr,[he(_e)?(J(),Y("div",Ur,p[26]||(p[26]=[f("div",{class:"loading-spinner"},null,-1),f("div",{class:"loading-text"},"处理中...",-1)]))):ne("",!0),F.value?(J(),Y("div",Lr,[f("div",Fr,[p[27]||(p[27]=f("div",{class:"format-error-icon"},"⚠️",-1)),p[28]||(p[28]=f("div",{class:"format-error-title"},"文档格式不支持",-1)),f("div",jr,de(A.value),1),f("div",Wr,[f("button",{class:"retry-check-btn",onClick:p[0]||(p[0]=r=>Ze())},"重新检查")])])])):ne("",!0),f("div",Br,[f("div",Nr,de(he(P)||"未选择文档"),1),f("button",{class:"settings-btn",onClick:p[1]||(p[1]=r=>n.value=!0)},p[29]||(p[29]=[f("i",{class:"icon-settings"},null,-1)]))]),f("div",Vr,[f("div",Hr,[f("div",zr,[p[30]||(p[30]=f("label",{for:"stage-select"},"年级:",-1)),Be(f("select",{id:"stage-select","onUpdate:modelValue":p[2]||(p[2]=r=>ps(m)?m.value=r:null),class:"select-input",disabled:F.value},[(J(!0),Y(_t,null,At(he($),r=>(J(),Y("option",{key:r.value,value:r.value},de(r.label),9,Jr))),128))],8,qr),[[Yt,he(m)]])]),f("div",Yr,[p[31]||(p[31]=f("label",{for:"subject-select"},"学科:",-1)),Be(f("select",{id:"subject-select","onUpdate:modelValue":p[3]||(p[3]=r=>ps(w)?w.value=r:null),class:"select-input",disabled:F.value},[(J(!0),Y(_t,null,At(Pe.value,r=>(J(),Y("option",{key:r.value,value:r.value},de(r.label),9,Xr))),128))],8,Kr),[[Yt,he(w)]]),ce.value&&!ce.value.isAdmin&&!ce.value.isOwner&&ce.value.subject?(J(),Y("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((I=Pe.value.find(r=>r.value===ce.value.subject))==null?void 0:I.label)||ce.value.subject} 学科`}," 🔒 ",8,Gr)):ne("",!0)])]),He.value?(J(),Y("div",Zr," 理科可使用增强模式以获取更精准的解析 ")):ne("",!0),f("div",Qr,[f("button",{class:"action-btn primary",onClick:p[4]||(p[4]=r=>je("wps-analysis")),disabled:T.value||F.value},[f("div",to,[T.value?(J(),Y("span",so)):ne("",!0),p[32]||(p[32]=f("span",{class:"btn-text"},"解析",-1))])],8,eo),He.value?(J(),Y("button",{key:0,class:"action-btn enhance",onClick:p[5]||(p[5]=r=>je("wps-enhance_analysis")),disabled:S.value||F.value},[f("div",ao,[S.value?(J(),Y("span",ro)):ne("",!0),p[33]||(p[33]=f("span",{class:"btn-text"},"增强解析",-1))])],8,no)):ne("",!0),((oe=(N=ce.value)==null?void 0:N.orgs[0])==null?void 0:oe.orgId)===2?(J(),Y("button",{key:1,class:"action-btn secondary",onClick:p[6]||(p[6]=r=>rt()),disabled:y.value||F.value},[f("div",io,[y.value?(J(),Y("span",lo)):ne("",!0),p[34]||(p[34]=f("span",{class:"btn-text"},"校对",-1))])],8,oo)):ne("",!0)])]),f("div",co,[s.value?(J(),Y("div",{key:0,class:"modal-overlay",onClick:p[9]||(p[9]=r=>s.value=!1)},[f("div",{class:"modal-content",onClick:p[8]||(p[8]=pt(()=>{},["stop"]))},[f("div",uo,[p[35]||(p[35]=f("div",{class:"modal-title"},"选中内容",-1)),f("button",{class:"modal-close",onClick:p[7]||(p[7]=r=>s.value=!1)},"×")]),f("div",po,[f("pre",fo,de(he(ae)||"未选中内容"),1)])])])):ne("",!0),t.value?(J(),Y("div",{key:1,class:"modal-overlay",onClick:p[13]||(p[13]=r=>t.value=!1)},[f("div",{class:"modal-content alert-modal",onClick:p[12]||(p[12]=pt(()=>{},["stop"]))},[f("div",ho,[p[36]||(p[36]=f("div",{class:"modal-title"},"提示",-1)),f("button",{class:"modal-close",onClick:p[10]||(p[10]=r=>t.value=!1)},"×")]),f("div",vo,[f("div",go,de(o.value),1),f("div",mo,[f("button",{class:"action-btn primary",onClick:p[11]||(p[11]=r=>t.value=!1)},"确定")])])])])):ne("",!0),he(Ce).show?(J(),Y("div",bo,[f("div",{class:"modal-content confirm-modal",onClick:p[17]||(p[17]=pt(()=>{},["stop"]))},[f("div",wo,[p[37]||(p[37]=f("div",{class:"modal-title"},"确认",-1)),f("button",{class:"modal-close",onClick:p[14]||(p[14]=r=>he(G)(!1))},"×")]),f("div",yo,[f("div",xo,de(he(Ce).message),1),f("div",To,[f("button",{class:"action-btn secondary",onClick:p[15]||(p[15]=r=>he(G)(!1))},"取消"),f("button",{class:"action-btn primary",onClick:p[16]||(p[16]=r=>he(G)(!0))},"确定")])])])])):ne("",!0),f("div",Co,[f("div",ko,[p[38]||(p[38]=f("div",{class:"queue-title"},"任务队列",-1)),f("div",$o,[Be(f("select",{id:"status-filter-select","onUpdate:modelValue":p[18]||(p[18]=r=>te.value=r),class:"status-filter-select-input"},[(J(!0),Y(_t,null,At(B.value,r=>(J(),Y("option",{key:r.value,value:r.value},de(r.label),9,So))),128))],512),[[Yt,te.value]])]),f("div",Eo,[f("button",{class:"release-all-btn",onClick:et,disabled:he(we)()===0,title:he(we)()===0?"无已完成任务可释放":`释放所有${he(we)()}个已完成任务`}," 一键释放 ",8,_o)]),f("div",Ao,de(Object.keys(ve.value).length)+"个任务",1)]),Object.keys(ve.value).length>0?(J(),Y("div",Mo,[f("table",{class:Ie(["queue-table",{"narrow-view":R.value,"ultra-narrow-view":H.value}])},[f("thead",null,[f("tr",null,[f("th",Do,[f("div",Po,[p[40]||(p[40]=f("span",null,"任务ID",-1)),f("span",{class:"help-icon",onMouseenter:p[19]||(p[19]=r=>st(r))},p[39]||(p[39]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),f("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),R.value?ne("",!0):(J(),Y("th",Oo,[f("div",Io,[p[41]||(p[41]=f("span",null,"题目内容",-1)),f("label",Ro,[Be(f("input",{type:"checkbox","onUpdate:modelValue":p[20]||(p[20]=r=>d.value=r)},null,512),[[rn,d.value]]),f("span",{class:"slider round",title:d.value?"关闭题目预览":"开启题目预览"},null,8,Uo)])])])),H.value?ne("",!0):(J(),Y("th",Lo,"状态")),p[42]||(p[42]=f("th",{class:"col-actions"},"操作",-1))])]),f("tbody",null,[(J(!0),Y(_t,null,At(Qe.value,(r,l)=>(J(),Y("tr",{key:l,class:Ie(["task-row",[he(q)(r.status),{"selected-task-row":l===V.value}]]),onClick:v=>Ae(l)},[f("td",jo,[f("div",{class:Ie(["id-cell",{"id-with-status":H.value}])},[f("div",Wo,[f("span",Bo,de(l.substring(0,8)),1),r.wordType==="wps-enhance_analysis"||r.isEnhanced?(J(),Y("span",No,p[43]||(p[43]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[f("title",null,"增强模式"),f("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):ne("",!0),x(r)?(J(),Y("span",{key:1,class:"soft-break-warning-icon",onMouseenter:p[21]||(p[21]=v=>j(v))},p[44]||(p[44]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[f("title",null,"提示"),f("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),f("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),f("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):ne("",!0)]),H.value?(J(),Y("div",Vo,[f("span",{class:Ie(["task-tag compact",he(q)(r.status)])},de(he(fe)(r.status)),3)])):ne("",!0)],2)]),R.value?ne("",!0):(J(),Y("td",Ho,[f("div",{class:"subject-cell",onMouseenter:v=>ot(v,r)},de(tt(r)),41,zo)])),H.value?ne("",!0):(J(),Y("td",qo,[f("div",Jo,[f("span",{class:Ie(["task-tag",he(q)(r.status)])},de(he(fe)(r.status)),3)])])),f("td",Yo,[f("div",Ko,[r.status===1?(J(),Y("button",{key:0,onClick:pt(v=>he(ge)(l),["stop"]),class:"terminate-btn"}," 终止 ",8,Xo)):ne("",!0),r.status===2?(J(),Y("button",{key:1,onClick:pt(v=>Oe(l),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Go)):ne("",!0),r.status!==1&&r.status!==2?(J(),Y("span",Zo,p[45]||(p[45]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):ne("",!0)])])],10,Fo))),128))])],2)])):(J(),Y("div",Qo,p[46]||(p[46]=[f("div",{class:"empty-text"},"暂无任务",-1)])))]),((a=(re=ce.value)==null?void 0:re.orgs[0])==null?void 0:a.orgId)===2?(J(),Y("div",ei,[f("div",{class:"log-header",onClick:We},[p[47]||(p[47]=f("div",{class:"log-title"},"执行日志",-1)),f("div",ti,[f("button",{class:"clear-btn",onClick:p[22]||(p[22]=pt(r=>he(L)(),["stop"]))},"清空日志"),f("span",si,de(g.value?"▼":"▶"),1)])]),g.value?(J(),Y("div",{key:0,class:"log-content",innerHTML:he(le)},null,8,ni)):ne("",!0)])):ne("",!0)]),n.value?(J(),Y("div",{key:2,class:"modal-overlay",onClick:p[25]||(p[25]=r=>n.value=!1)},[f("div",{class:"modal-content",onClick:p[24]||(p[24]=pt(()=>{},["stop"]))},[on(ra,{onClose:p[23]||(p[23]=r=>n.value=!1)})])])):ne("",!0)])}}},oi=Is(ai,[["__scopeId","data-v-e7c6c2a7"]]);export{oi as default};
