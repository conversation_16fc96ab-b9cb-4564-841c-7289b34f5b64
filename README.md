## 外挂 js 脚本编译为 exe 可执行文件
**【必须使用 commonjs】**
```sh
 pkg run.cjs --targets node16-win-x64 --output hexin_wps.exe
```

## 调试
```sh
# 首次启动时，请先运行 npm run postinstall
npm run debug

# 万唯版本开发服务器
npm run dev:wanwei

# 合心版本开发服务器  
npm run dev:hexin
```

> 详细的启动说明和开发指南请参考 [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)

## wpsjs 打包相关
[https://p.wpseco.cn/wiki/doc/6333724ed7c60f8bf1fd8646](wps 开发参考文档)

```shell
npm run wps-build
```

```shell
# 非必要请勿运行此命令
wpsjs publish --serverUrl="http://wwwps.hexinedu.com/wps-addon-build/" 
```

## 使用
1. 访问 http://wwwps.hexinedu.com/wps-addon-publish/publish.html
2. 找到 hexin-wps-addon, 点击安装（或者更新）
3. 安装完成后，打开 wps，并打开任意文档，会提示是否信任
4. 点击信任，即可使用


# WebSocket 版本信息系统

本系统提供了通过 WebSocket 向客户端发送版本信息的功能，并支持客户端根据不同版本显示不同内容。

## 功能概述

1. **自动版本信息推送**：客户端连接时自动接收版本信息
2. **动态版本切换**：支持运行时切换版本类型（用于测试）
3. **版本相关配置**：根据版本类型提供不同的配置信息
4. **UI 动态更新**：客户端根据版本信息动态更新界面显示

## 系统架构

### 服务器端

#### 1. WebSocket 服务器增强 (`run-server/services/wsServer.js`)

- 新增 `sendVersionInfo(client)` 方法：向指定客户端发送版本信息
- 新增 `broadcastVersionInfo()` 方法：向所有客户端广播版本信息
- 新增 `getVersionInfo()` 方法：获取当前版本信息
- 支持自定义版本信息提供者

#### 2. 版本信息处理器 (`run-server/server.js`)

```javascript
// 版本信息 WebSocket 消息处理
wsServer.registerMessageHandler('version', (client, action, data) => {
  switch (action) {
    case 'getInfo':
      // 发送版本信息
      wsServer.sendVersionInfo(client);
      break;
    
    case 'setEdition':
      // 设置版本类型（用于测试）
      // ...
      break;
  }
});
```

#### 3. 环境配置集成 (`run-server/main.js`)

```javascript
// 将ENV配置暴露为全局变量，供其他模块使用
global.ENV = ENV;

// 在服务器中设置版本信息提供者
wsServer.setVersionInfoProvider(() => {
  return {
    edition: global.ENV?.config?.EDITION || 'wanwei',
    appVersion: global.ENV?.config?.APP_VERSION || '1.0.0',
    buildDate: global.ENV?.config?.BUILD_DATE || new Date().toISOString(),
    environment: global.ENV?.config?.ENV || 'development',
    bypassEncryption: global.ENV?.config?.BYPASS_ENCRYPTION || false
  };
});
```

### 客户端

#### 1. 版本管理器 (`src/components/js/versionManager.js`)

主要功能：
- 监听 WebSocket 版本信息
- 提供版本相关的配置信息
- 支持版本变更回调
- 提供版本判断方法

```javascript
import versionManager from './js/versionManager';

// 监听版本变更
const removeListener = versionManager.onVersionChange((versionInfo, oldVersionInfo) => {
  console.log('版本已更新:', versionInfo);
  // 更新UI显示
});

// 获取版本配置
const config = versionManager.getVersionConfig();
// 返回：
// {
//   edition: 'wanwei',
//   appName: '万唯AI编辑WPS插件服务',
//   shortName: '万唯WPS插件',
//   companyName: '万唯科技',
//   iconName: 'ww.png',
//   primaryColor: '#1890ff',
//   apiBaseUrl: 'https://uc.101.com',
//   uploadUrl: 'https://wwwps.hexinedu.com'
// }

// 版本判断
if (versionManager.isWanweiEdition()) {
  // 万唯版本特定逻辑
} else if (versionManager.isHexinEdition()) {
  // 合心版本特定逻辑
}
```

#### 2. WebSocket 客户端增强 (`src/components/js/wsClient.js`)

- 新增 `version` 事件监听器
- 支持版本信息的自动处理

#### 3. UI 组件集成

##### App.vue
```vue
<template>
  <div class="app">
    <p>请确保 {{ appDisplayName }} 已启动并正常运行。</p>
  </div>
</template>

<script setup>
import versionManager from './components/js/versionManager'

// 版本相关的响应式变量
const appDisplayName = ref('万唯AI编辑WPS插件服务') // 默认万唯版本
const versionConfig = ref(versionManager.getVersionConfig())

// 设置版本信息监听
removeVersionListener = versionManager.onVersionChange((versionInfo) => {
  const config = versionManager.getVersionConfig();
  versionConfig.value = config;
  appDisplayName.value = config.appName;
});
</script>
```

##### Login.vue
```vue
<template>
  <div class="login-form">
    <h2>{{ loginTitle }}</h2>
  </div>
</template>

<script>
import versionManager from './js/versionManager';

export default {
  data() {
    return {
      loginTitle: '欢迎使用，请登录',
    }
  },
  methods: {
    updateLoginTitle() {
      if (versionManager.isHexinEdition()) {
        this.loginTitle = '欢迎使用合心科技WPS插件，请登录';
      } else {
        this.loginTitle = '欢迎使用万唯WPS插件，请登录';
      }
    }
  }
}
</script>
```

##### ServerSetting.vue
```vue
<template>
  <h3>{{ versionConfig.shortName }}设置 
    <span class="version-tag">{{ versionConfig.appVersion }}</span>
    <span class="user-info" v-if="userInfo">
      {{ versionConfig.companyName }}欢迎您，{{ userInfo.nickname }}
    </span>
  </h3>
  
  <!-- 版本切换调试区域 -->
  <div class="directory-section">
    <h4>版本信息 (调试)</h4>
    <select v-model="selectedEdition">
      <option value="wanwei">万唯版本</option>
      <option value="hexin">合心版本</option>
    </select>
    <button @click="switchEdition">切换版本</button>
  </div>
</template>
```

## WebSocket 消息格式

### 1. 版本信息推送

**服务器 → 客户端**
```json
{
  "type": "version",
  "action": "info",
  "success": true,
  "data": {
    "edition": "wanwei",
    "appVersion": "1.0.0",
    "buildDate": "2024-01-01T00:00:00.000Z",
    "environment": "development",
    "bypassEncryption": false
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. 请求版本信息

**客户端 → 服务器**
```json
{
  "type": "version",
  "action": "getInfo",
  "data": {
    "messageId": 123
  }
}
```

### 3. 设置版本类型（测试用）

**客户端 → 服务器**
```json
{
  "type": "version",
  "action": "setEdition",
  "data": {
    "edition": "hexin",
    "messageId": 124
  }
}
```

**服务器 → 客户端**
```json
{
  "type": "version",
  "action": "setEdition",
  "success": true,
  "messageId": 124,
  "message": "版本已切换到: hexin",
  "data": {
    "edition": "hexin"
  }
}
```

## 版本配置说明

### 万唯版本 (wanwei)
```javascript
{
  appName: '万唯AI编辑WPS插件服务',
  shortName: '万唯WPS插件',
  companyName: '万唯科技',
  iconName: 'ww.png',
  primaryColor: '#1890ff',
  apiBaseUrl: 'https://uc.101.com',
  uploadUrl: 'https://wwwps.hexinedu.com'
}
```

### 合心版本 (hexin)
```javascript
{
  appName: '合心科技AI编辑WPS插件服务',
  shortName: '合心WPS插件',
  companyName: '合心科技',
  iconName: 'hexin.png',
  primaryColor: '#52c41a',
  apiBaseUrl: 'https://uc.101.com',
  uploadUrl: 'https://hexinwps.hexinedu.com'
}
```

## 测试功能

### HTML 测试页面

创建了一个独立的 HTML 测试页面 (`run-server/test-version.html`)，可以用于测试 WebSocket 版本信息功能：

1. 连接 WebSocket 服务器
2. 接收和显示版本信息
3. 测试版本切换功能
4. 查看消息日志

### 使用方法

1. 启动应用服务器
2. 在浏览器中打开 `http://127.0.0.1:3000/test-version.html`
3. 点击"连接 WebSocket"按钮
4. 查看版本信息和测试版本切换

## 集成到现有组件

### 基本步骤

1. **导入版本管理器**
```javascript
import versionManager from './js/versionManager';
```

2. **设置版本监听**
```javascript
// Vue 3 Composition API
const removeVersionListener = versionManager.onVersionChange((versionInfo) => {
  // 更新响应式数据
  versionConfig.value = versionManager.getVersionConfig();
});

// Vue 2 Options API
mounted() {
  this.removeVersionListener = versionManager.onVersionChange((versionInfo) => {
    this.versionConfig = versionManager.getVersionConfig();
  });
}
```

3. **使用版本信息**
```javascript
// 在模板中使用
{{ versionConfig.appName }}
{{ versionConfig.shortName }}

// 在逻辑中判断
if (versionManager.isHexinEdition()) {
  // 合心版本特定逻辑
}
```

4. **清理监听器**
```javascript
// Vue 3
onUnmounted(() => {
  if (removeVersionListener) {
    removeVersionListener();
  }
});

// Vue 2
beforeDestroy() {
  if (this.removeVersionListener) {
    this.removeVersionListener();
  }
}
```

## 注意事项

1. **版本信息的及时性**：版本信息在客户端连接时自动推送，确保客户端始终获得最新信息
2. **错误处理**：所有版本相关操作都包含错误处理和日志记录
3. **向后兼容**：版本管理器提供默认值，确保在版本信息未加载时的正常运行
4. **测试功能**：版本切换功能仅用于开发和测试，生产环境应通过配置文件控制
5. **内存管理**：注意及时清理版本变更监听器，避免内存泄漏

## 扩展建议

1. **主题切换**：可以根据版本类型动态切换UI主题色
2. **功能开关**：不同版本可能有不同的功能开关配置
3. **API 端点**：根据版本使用不同的API端点
4. **资源路径**：根据版本加载不同的图标、样式等资源 


# 开发指南 - 双版本启动

本文档介绍如何在开发过程中启动不同版本的应用。

## 版本类型

项目支持两个版本：

1. **万唯版本 (wanwei)** - 默认版本
    - 应用名称：万唯AI编辑WPS插件服务
    - 公司：万唯科技
    - 图标：ww.png
    - 主题色：#1890ff

2. **合心版本 (hexin)**
    - 应用名称：合心科技AI编辑WPS插件服务
    - 公司：合心科技
    - 图标：hexin.png
    - 主题色：#52c41a

## 启动命令

### Electron 桌面应用启动

```bash
# 启动万唯版本（默认）
npm run electron
# 或
npm run electron:wanwei

# 启动合心版本
npm run electron:hexin
```

### 开发服务器启动

```bash
# 启动万唯版本开发服务器
npm run dev:wanwei

# 启动合心版本开发服务器
npm run dev:hexin

# 默认开发服务器（万唯版本）
npm run dev
```

### 常规服务器启动

```bash
# 启动万唯版本服务器
cross-env APP_EDITION=wanwei npm start

# 启动合心版本服务器
cross-env APP_EDITION=hexin npm start

# 默认启动（万唯版本）
npm start
```

## 环境变量

系统通过 `APP_EDITION` 环境变量来控制版本类型：

- `APP_EDITION=wanwei` - 万唯版本
- `APP_EDITION=hexin` - 合心版本
- 未设置 - 默认为万唯版本

## 版本差异

启动不同版本时，应用会：

1. **自动选择对应图标**
    - 万唯版本：使用 `assets/ww.png`
    - 合心版本：使用 `assets/hexin.png`

2. **发送对应版本信息到客户端**
    - WebSocket 连接时自动推送版本配置
    - 客户端根据版本信息动态更新UI

3. **显示对应的品牌信息**
    - 窗口标题、托盘提示等会根据版本调整
    - 登录页面、设置页面显示对应公司信息

## 开发调试

### 实时版本切换

在开发过程中，可以通过以下方式测试版本切换：

1. **打开测试页面**
   ```
   http://127.0.0.1:3000/test-version.html
   ```

2. **在设置页面切换版本**
    - 登录后进入设置页面
    - 在"版本信息(调试)"区域切换版本
    - 观察UI的实时变化

3. **重启应用测试**
    - 使用不同的启动命令重启应用
    - 验证版本配置是否正确加载

### 版本验证

启动应用后，可以通过以下方式验证版本：

1. **查看应用日志**
   ```
   [时间戳] 版本类型: wanwei
   [时间戳] 尝试获取图标: path/to/assets/ww.png (版本: wanwei)
   ```

2. **查看客户端显示**
    - 登录页面标题是否正确
    - 设置页面公司名称是否正确
    - 应用名称是否正确

3. **WebSocket 测试**
    - 打开测试页面
    - 查看接收到的版本信息是否正确

## 构建打包

### 单版本构建

```bash
# 构建万唯版本
npm run build

# 构建合心版本
npm run build:hexin
```

### 多版本构建

```bash
# 同时构建两个版本
npm run build:multi

# 优化构建（包含检查、构建、上传）
npm run build:optimized
```

## 常见问题

### Q: 启动时版本不正确？

A: 检查以下项目：
1. 确认使用了正确的启动命令
2. 检查 `APP_EDITION` 环境变量是否设置正确
3. 查看应用日志中的版本信息

### Q: 图标显示不正确？

A: 确认对应的图标文件存在：
- 万唯版本：`assets/ww.png`
- 合心版本：`assets/hexin.png`

### Q: 客户端显示的版本信息不更新？

A: 尝试以下步骤：
1. 刷新客户端页面
2. 重新连接 WebSocket
3. 检查服务器日志是否有错误

### Q: 开发时如何快速切换版本？

A: 推荐的工作流程：
1. 使用 `npm run electron:wanwei` 启动万唯版本
2. 使用 `npm run electron:hexin` 启动合心版本
3. 或者在设置页面使用调试功能实时切换

## 注意事项

1. **环境变量优先级**：命令行设置的 `APP_EDITION` 环境变量优先级最高
2. **配置文件**：生产环境会从配置文件读取版本信息
3. **图标路径**：确保两个版本的图标文件都存在
4. **客户端缓存**：版本切换后可能需要刷新客户端页面
5. **日志查看**：开发时建议查看控制台日志以确认版本加载状态 


# 多版本构建系统说明

## 概述

本项目现在支持同时构建两个版本的WPS插件：

1. **万唯版本** - 默认版本，带加密软件检测
2. **合心版本** - 新版本，绕过加密软件检测，使用不同的图标和应用名称

## 版本差异

| 特性 | 万唯版本 | 合心版本 |
|------|----------|----------|
| 应用名称 | 万唯AI编辑WPS插件服务 | 合心科技AI编辑WPS插件服务 |
| 图标 | ww.png | hexin.png |
| 加密软件检测 | 启用 | 绕过（禁用） |
| 更新URL | exe/wps-addon/ | exe/hexin-wps-addon/ |
| 输出目录 | dist | dist-hexin |

## 构建命令

### 单版本构建

```bash
# 构建万唯版本
npm run build

# 构建合心版本
npm run build:hexin

# 构建到目录（不打包）
npm run build:dir
```

### 多版本构建

```bash
# 同时构建两个版本
npm run build:multi

# 完整的生产构建流程（构建+上传）
npm run build:optimized
```

### 其他命令

```bash
# 更新版本信息
npm run update-version

# 仅上传已构建的产物
npm run upload
```

## 构建流程

### 完整构建流程 (`npm run build:optimized`)

1. **预构建检查** - 验证环境和依赖
2. **更新版本信息** - 自动更新配置文件中的版本号和构建时间
3. **多版本构建** - 同时构建万唯和合心两个版本
4. **上传分发** - 上传到不同的OSS路径

### 文件结构

```
run-server/
├── env-config/
│   ├── production.json          # 万唯版本配置
│   ├── hexin-production.json    # 合心版本配置
│   └── development.json         # 开发环境配置
├── assets/
│   ├── ww.png                   # 万唯版本图标
│   └── hexin.png                # 合心版本图标
├── electron-builder.yml         # 万唯版本构建配置
├── electron-builder-hexin.yml   # 合心版本构建配置
├── dist/                        # 万唯版本输出目录
├── dist-hexin/                  # 合心版本输出目录
└── scripts/
    ├── build-multi-version.js   # 多版本构建脚本
    ├── update-version-info.js   # 版本信息更新脚本
    └── upload-dist.js           # 上传脚本
```

## 配置说明

### 环境配置文件

配置文件会在构建前自动更新，包含以下关键字段：

- `EDITION`: 版本类型 (`'wanwei'` 或 `'hexin'`)
- `BYPASS_ENCRYPTION`: 是否绕过加密软件检测
- `APP_NAME`: 应用显示名称
- `APP_VERSION`: 当前版本号（自动从package.json获取）
- `BUILD_DATE`: 构建时间（自动生成）

### Electron Builder 配置

每个版本都有独立的 electron-builder 配置文件：

- `electron-builder.yml` - 万唯版本
- `electron-builder-hexin.yml` - 合心版本

主要差异：
- `productName` - 应用名称
- `icon` - 图标路径
- `extraResources` - 环境配置文件路径
- `directories.output` - 输出目录
- `publish.url` - 更新服务器URL

## 上传路径

构建完成后，两个版本会上传到不同的OSS路径：

- **万唯版本**: `https://dl.hexinedu.com/exe/wps-addon/`
- **合心版本**: `https://dl.hexinedu.com/exe/hexin-wps-addon/`

## 自动更新

应用运行时会根据 `EDITION` 配置自动选择对应的更新服务器：

```javascript
const updateUrl = ENV.config.EDITION === 'hexin' 
  ? 'https://dl.hexinedu.com/exe/hexin-wps-addon/' 
  : 'https://dl.hexinedu.com/exe/wps-addon/';
```

## 故障排除

### 常见问题

1. **构建失败**: 检查node_modules是否完整安装
2. **配置文件错误**: 运行 `npm run update-version` 重新生成
3. **图标文件缺失**: 确保 `assets/hexin.png` 存在
4. **上传失败**: 检查OSS配置和网络连接

### 调试命令

```bash
# 检查配置文件
cat env-config/production.json
cat env-config/hexin-production.json

# 验证构建配置
electron-builder --help

# 测试单版本构建
npm run build:hexin
```

## 开发建议

1. 修改版本配置时，建议通过 `update-version-info.js` 脚本统一更新
2. 新增配置项时，同时更新两个版本的配置文件
3. 测试时可以先用 `build:multi` 验证构建，再用 `build:optimized` 完整发布
4. 确保两个版本的功能差异仅限于配置不同，核心逻辑保持一致 