# WebSocket事件监听修复说明

## 问题描述
前端无法接收到后端发送的图片上传成功事件，导致图片分割工具一直显示loading状态，无法加载真实的OSS图片。

## 问题根因
**事件名称不匹配**：
- 后端发送的事件格式：`{ type: 'watcher', eventType: 'imageSplitUploadSuccess', data: {...} }`
- 前端监听的事件：`wsClient.on('imageSplitUploadSuccess', ...)`

后端通过 `wsServer.sendWatcherEventToClient()` 发送事件，消息类型是 `watcher`，具体的事件类型在 `eventType` 字段中。

## 解决方案
修改前端事件监听逻辑，监听 `watcher` 类型的消息，然后根据 `eventType` 分发到具体的处理函数。

## 实现细节

### 1. 后端事件发送格式
```javascript
// imageSplitWatcher.js
this.sendImageSplitEvent('imageSplitUploadSuccess', {
  file: file,
  ossUrl: uploadResult.ossUrl,
  fileSize: uploadResult.fileSize,
  totalProcessed: Object.keys(this.uploadedFilesMap).length
}, file);

// wsServer.js
sendWatcherEventToClient(eventType, data, clientId, messageId = null) {
  const message = {
    type: 'watcher',        // 消息类型
    eventType,              // 具体事件类型：imageSplitUploadSuccess
    data,                   // 事件数据
    messageId,
    timestamp: new Date().toISOString()
  };
  return this.sendToClient(targetClient, message);
}
```

### 2. 前端事件监听修复

#### 修复前（错误的监听方式）
```javascript
// 直接监听事件名，无法接收到后端发送的消息
wsClient.on('imageSplitUploadSuccess', (data) => {
  // 这里永远不会被调用
})
```

#### 修复后（正确的监听方式）
```javascript
// 监听watcher类型的消息，然后根据eventType分发
wsClient.addEventListener('watcher', (message) => {
  const { eventType, data } = message
  
  if (eventType === 'imageSplitUploadSuccess') {
    handleImageUploadSuccess(data)
  }
  else if (eventType === 'imageSplitUploadError') {
    handleImageUploadError(data)
  }
  // ... 其他事件类型
})
```

### 3. 事件处理函数重构

#### 分离事件处理逻辑
```javascript
// 处理图片上传成功事件
const handleImageUploadSuccess = async (data) => {
  console.log('图片上传成功:', data.file, data.ossUrl)
  
  // 检查是否是当前等待的原图文件
  if (isLoadingImage.value && currentImageInfo.value && 
      data.file === currentImageInfo.value.fileName) {
    
    try {
      // 加载真实OSS图片
      await loadImageFromOSS(data.ossUrl, currentImageInfo.value.width, currentImageInfo.value.height)
      isLoadingImage.value = false
      successMessage.value = '图片加载成功！'
    } catch (loadError) {
      // 加载失败，保持演示图片
      isLoadingImage.value = false
      errorMessage.value = '加载真实图片失败，使用演示图片'
    }
  } else {
    // 其他分割图片上传成功的通知
    successMessage.value = `图片 ${data.file} 上传到OSS成功！`
  }
}
```

## 支持的事件类型

### 图片分割相关事件
1. **imageSplitUploadStart**: 图片开始上传
2. **imageSplitUploadSuccess**: 图片上传成功
3. **imageSplitUploadError**: 图片上传失败
4. **imageSplitError**: 图片分割服务错误
5. **imageSplitFilesFound**: 发现新的图片文件

### 事件数据结构

#### 上传成功事件
```javascript
{
  type: 'watcher',
  eventType: 'imageSplitUploadSuccess',
  data: {
    file: 'image_split_1234567890.png',
    ossUrl: 'https://sigma-temp.oss-cn-shanghai.aliyuncs.com/image_split/image_split_1234567890.png',
    fileSize: 12345,
    totalProcessed: 1
  }
}
```

#### 上传失败事件
```javascript
{
  type: 'watcher',
  eventType: 'imageSplitUploadError',
  data: {
    file: 'image_split_1234567890.png',
    error: 'uploadFailed'
  }
}
```

## 工作流程

### 修复后的完整流程
```
1. 用户选择图片
2. 保存到监控目录
3. 通知服务端关联文件
4. 显示loading状态
5. 服务端自动检测文件并上传OSS
6. 服务端发送 watcher/imageSplitUploadSuccess 事件
7. 前端接收事件并加载真实OSS图片
8. 隐藏loading，显示真实图片
```

## 技术优势

### 1. 事件驱动架构
- 松耦合的前后端通信
- 实时状态更新
- 支持多种事件类型

### 2. 错误处理完善
- 上传失败时的降级处理
- 网络异常时的容错机制
- 清晰的错误提示

### 3. 用户体验优化
- 实时的loading状态
- 自动的图片切换
- 详细的状态反馈

## 测试验证

### 验证步骤
1. 选择图片并打开分割工具
2. 观察loading状态是否正确显示
3. 等待几秒钟，检查是否自动加载真实图片
4. 验证Console中是否有正确的事件日志

### 预期结果
- Loading状态正常显示和隐藏
- 真实OSS图片成功加载到Canvas
- Console显示事件接收和处理日志
- 用户可以对真实图片进行分割操作

## 注意事项

1. **事件监听器注册时机**: 确保在组件挂载时正确注册事件监听器
2. **内存泄漏防护**: 在组件卸载时清理事件监听器
3. **错误边界处理**: 确保事件处理函数中的错误不会影响其他功能
4. **调试信息**: 保留详细的Console日志便于问题排查

## 相关文件

- `src/components/ImageSplit.vue`: 前端事件监听和处理
- `run-server/services/imageSplitWatcher.js`: 后端事件发送
- `run-server/services/wsServer.js`: WebSocket服务器事件分发
- `src/components/js/wsClient.js`: WebSocket客户端事件接收
