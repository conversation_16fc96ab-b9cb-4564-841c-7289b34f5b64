import { useNotification } from '../composables/useNotification'

/**
 * 通知插件 - 可以通过 app.config.globalProperties.$notify 访问
 * 使用方法：
 * 1. 在组件中: this.$notify('消息内容', '类型', 持续时间)
 * 2. 在 setup() 中使用 inject: const notification = inject('notification')
 */
export const notificationPlugin = {
  install: (app) => {
    // 创建全局通知实例
    const notification = useNotification()
    
    // 提供给依赖注入系统
    app.provide('notification', notification)
    
    // 添加到全局属性
    app.config.globalProperties.$notify = notification.showNotify
  }
} 
