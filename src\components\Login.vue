<template>
  <div class="login-container">
    <div class="login-form">
      <h2>{{ loginTitle }}</h2>

      <div class="form-group">
        <label for="username">用户名</label>
        <input
          type="text"
          id="username"
          v-model="form.username"
          placeholder="请输入手机号或邮箱"
          @input="clearError('username')"
        />
        <div v-if="errors.username" class="error-message">{{ errors.username }}</div>
      </div>

      <div class="form-group">
        <label for="password">密码</label>
        <input
          type="password"
          id="password"
          v-model="form.password"
          placeholder="请输入密码"
          @input="clearError('password')"
          @keyup.enter="handleLogin"
        />
        <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
      </div>

      <div v-if="loginError" class="login-error">{{ loginError }}</div>

      <button
        class="login-btn"
        @click="handleLogin"
        :disabled="isLoading"
      >
        {{ isLoading ? '登录中...' : '登录' }}
      </button>
    </div>
  </div>
</template>

<script>
import { login, validateLoginForm } from './js/auth';
import logger from './js/logger';
import versionManager from './js/versionManager';
import Util from './js/util';

export default {
  name: 'Login',
  data() {
    return {
      form: {
        username: '',
        password: ''
      },
      errors: {},
      loginError: '',
      isLoading: false,
      loginTitle: '欢迎使用，请登录', // 默认标题
      versionConfig: versionManager.getVersionConfig()
    }
  },
  mounted() {
    // 设置版本信息监听
    this.removeVersionListener = versionManager.onVersionChange((versionInfo) => {
      this.versionConfig = versionManager.getVersionConfig();
      this.updateLoginTitle();
    });

    // 初始化登录标题
    this.updateLoginTitle();
  },
  beforeUnmount() {
    // 清理版本监听器
    if (this.removeVersionListener) {
      this.removeVersionListener();
    }
  },
  methods: {
    updateLoginTitle() {
      if (versionManager.isHexinEdition()) {
        this.loginTitle = '欢迎使用合心科技WPS插件，请登录';
      } else {
        this.loginTitle = '欢迎使用万唯WPS插件，请登录';
      }
    },
    clearError(field) {
      if (this.errors[field]) {
        this.errors = {
          ...this.errors,
          [field]: ''
        };
      }
      this.loginError = '';
    },
    async handleLogin() {
      // Reset errors
      this.errors = {};
      this.loginError = '';



      const validation = validateLoginForm(this.form.username, this.form.password);
      if (!validation.isValid) {
        logger.warn('登录表单验证失败', validation.errors);
        this.errors = validation.errors;
        return;
      }

      // Submit login
      this.isLoading = true;
      try {

        const result = await login(this.form.username, this.form.password);
        console.log('login result:', result);
        logger.info('登录请求返回结果', { success: result.success, message: result.message });

        if (result.success) {
          // Redirect to appropriate page based on current pane type
          logger.success('登录成功，准备智能跳转');

          // 记录跳转前状态
          logger.info('Router对象信息', {
            currentRoute: this.$router.currentRoute.value,
            routes: this.$router.getRoutes().map(r => r.path)
          });

          // 使用智能跳转函数
          Util.redirectToCurrentPane('/taskpane');

          // 记录路由跳转后状态
          setTimeout(() => {
            logger.info('路由跳转完成后状态', {
              hash: window.location.hash,
              href: window.location.href
            });
          }, 100);
        } else {
          // 检查是否是 AI 编辑功能未开启的错误
          if (result.code === 'AI_EDIT_DISABLED') {
            this.loginError = '该企业尚未开启 AI 编辑功能，请联系您的企业管理员。';
            logger.warn('AI编辑功能未开启', {
              reason: result.message,
              code: result.code
            });
          } else {
            this.loginError = result.message;
            logger.error('登录失败', {
              reason: result.message,
              code: result.code
            });
          }
        }
      } catch (error) {
        this.loginError = '登录失败，请稍后重试';
        logger.error('登录过程发生异常', error);
        console.error('Login error:', error);
      } finally {
        this.isLoading = false;
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.login-form {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 350px;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

input:focus {
  outline: none;
  border-color: #2196F3;
}

.error-message, .login-error {
  color: #F44336;
  font-size: 14px;
  margin-top: 6px;
}

.login-error {
  text-align: center;
  margin-bottom: 15px;
}

.login-btn {
  width: 100%;
  padding: 12px;
  background-color: #229a52;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-btn:hover {
  background-color: #1cc660;
}

.login-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
