const os = require('os');
const crypto = require('crypto');
const { execSync } = require('child_process');
const fs = require('fs');
const configStore = require('../services/configStore');

/**
 * 机器码生成器类
 * 用于生成和管理与机器硬件绑定的唯一标识码
 */
class MachineCodeGenerator {
  constructor() {
    // 机器码在数据库中的键名
    this.MACHINE_CODE_KEY = 'machineCode';
    this.MACHINE_CODE_CREATED_AT = 'machineCodeCreatedAt';
    this.MACHINE_CODE_VERSION = 'machineCodeVersion';
  }

  /**
   * 获取CPU信息
   * @returns {string} CPU标识信息
   */
  getCpuInfo() {
    try {
      if (process.platform === 'win32') {
        // Windows系统
        const cpuInfo = execSync('wmic cpu get ProcessorId').toString().trim();
        const lines = cpuInfo.split('\n').filter(line => line.trim() !== 'ProcessorId');
        return lines[0]?.trim() || '';
      } else if (process.platform === 'darwin') {
        // macOS
        const cpuInfo = execSync('sysctl -n machdep.cpu.brand_string').toString().trim();
        return cpuInfo || '';
      } else {
        // Linux系统
        const cpuInfo = fs.readFileSync('/proc/cpuinfo', 'utf8');
        const serialLine = cpuInfo.split('\n').find(line => line.includes('serial') || line.includes('Serial'));
        const serial = serialLine ? serialLine.split(':')[1].trim() : '';
        return serial || os.cpus()[0].model;
      }
    } catch (error) {
      console.error('获取CPU信息失败:', error.message);
      return os.cpus()[0].model;
    }
  }

  /**
   * 获取主板信息
   * @returns {string} 主板标识信息
   */
  getMotherboardInfo() {
    try {
      if (process.platform === 'win32') {
        // Windows系统
        const motherboardInfo = execSync('wmic baseboard get SerialNumber').toString().trim();
        const lines = motherboardInfo.split('\n').filter(line => line.trim() !== 'SerialNumber');
        return lines[0]?.trim() || '';
      } else if (process.platform === 'darwin') {
        // macOS
        const motherboardInfo = execSync('ioreg -l | grep IOPlatformSerialNumber').toString().trim();
        const match = motherboardInfo.match(/"IOPlatformSerialNumber" = "([^"]+)"/);
        return match ? match[1] : '';
      } else {
        // Linux系统
        const motherboardInfo = execSync('dmidecode -t 2 | grep Serial').toString().trim();
        const match = motherboardInfo.match(/Serial Number: (.+)/);
        return match ? match[1] : '';
      }
    } catch (error) {
      console.error('获取主板信息失败:', error.message);
      return '';
    }
  }

  /**
   * 获取硬盘信息
   * @returns {string} 硬盘标识信息
   */
  getDiskInfo() {
    try {
      if (process.platform === 'win32') {
        // Windows系统
        const diskInfo = execSync('wmic diskdrive get SerialNumber').toString().trim();
        const lines = diskInfo.split('\n').filter(line => line.trim() !== 'SerialNumber');
        return lines[0]?.trim() || '';
      } else if (process.platform === 'darwin') {
        // macOS
        const diskInfo = execSync('diskutil info disk0 | grep "Disk / Partition UUID"').toString().trim();
        const match = diskInfo.match(/UUID: (.+)/);
        return match ? match[1] : '';
      } else {
        // Linux系统
        const diskInfo = execSync('lsblk -o NAME,SERIAL | grep sda').toString().trim();
        return diskInfo || '';
      }
    } catch (error) {
      console.error('获取硬盘信息失败:', error.message);
      return '';
    }
  }

  /**
   * 获取网卡MAC地址
   * @returns {string} 第一个非回环网卡的MAC地址
   */
  getMacAddress() {
    try {
      const networkInterfaces = os.networkInterfaces();
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName];
        for (const iface of interfaces) {
          // 查找第一个非内部的IPv4地址的MAC
          if (!iface.internal && iface.family === 'IPv4') {
            return iface.mac;
          }
        }
      }
      return '';
    } catch (error) {
      console.error('获取MAC地址失败:', error.message);
      return '';
    }
  }

  /**
   * 获取操作系统信息
   * @returns {string} 操作系统唯一标识
   */
  getOsInfo() {
    try {
      if (process.platform === 'win32') {
        // Windows系统，尝试获取ProductId
        const productId = execSync('wmic os get SerialNumber').toString().trim();
        const lines = productId.split('\n').filter(line => line.trim() !== 'SerialNumber');
        return lines[0]?.trim() || '';
      }
      // 不同系统的通用信息
      return `${os.platform()}-${os.release()}-${os.hostname()}`;
    } catch (error) {
      console.error('获取操作系统信息失败:', error.message);
      return `${os.platform()}-${os.release()}-${os.hostname()}`;
    }
  }

  /**
   * 生成机器码
   * @returns {string} 生成的机器码
   */
  generateMachineCode() {
    // 获取各种硬件信息
    const cpuInfo = this.getCpuInfo();
    const motherboardInfo = this.getMotherboardInfo();
    const diskInfo = this.getDiskInfo();
    const macAddress = this.getMacAddress();
    const osInfo = this.getOsInfo();

    // 合并所有硬件信息
    const hardwareString = `${cpuInfo}|${motherboardInfo}|${diskInfo}|${macAddress}|${osInfo}`;
    
    // 使用SHA256算法生成哈希
    const hash = crypto.createHash('sha256').update(hardwareString).digest('hex');
    
    // 格式化为更易读的形式: XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
    const formattedCode = [];
    for (let i = 0; i < 8; i++) {
      formattedCode.push(hash.substring(i * 4, (i + 1) * 4));
    }
    
    return formattedCode.join('-');
  }

  /**
   * 获取当前机器码
   * @returns {Promise<{code: string, createdAt: string, version: number}>} 机器码信息
   */
  async getMachineCode() {
    // 确保configStore已初始化
    await configStore.waitForInit();

    // 从配置中获取机器码
    const machineCode = configStore._getConfigSync(this.MACHINE_CODE_KEY);
    const createdAt = configStore._getConfigSync(this.MACHINE_CODE_CREATED_AT);
    const version = configStore._getConfigSync(this.MACHINE_CODE_VERSION) || '1';

    // 如果没有机器码，则生成并保存
    if (!machineCode) {
      return await this.refreshMachineCode();
    }

    return {
      code: machineCode,
      createdAt: createdAt || new Date().toISOString(),
      version: parseInt(version, 10)
    };
  }

  /**
   * 刷新机器码
   * @returns {Promise<{code: string, createdAt: string, version: number}>} 新的机器码信息
   */
  async refreshMachineCode() {
    // 确保configStore已初始化
    await configStore.waitForInit();

    // 获取当前版本号
    let currentVersion = configStore._getConfigSync(this.MACHINE_CODE_VERSION) || '0';
    currentVersion = parseInt(currentVersion, 10);

    // 生成新的机器码
    const newCode = this.generateMachineCode();
    const now = new Date().toISOString();
    const newVersion = currentVersion + 1;

    // 保存到数据库
    await configStore._setConfig(this.MACHINE_CODE_KEY, newCode);
    await configStore._setConfig(this.MACHINE_CODE_CREATED_AT, now);
    await configStore._setConfig(this.MACHINE_CODE_VERSION, newVersion.toString());

    return {
      code: newCode,
      createdAt: now,
      version: newVersion
    };
  }

  /**
   * 验证机器码是否有效（当前机器是否匹配）
   * @param {string} code - 要验证的机器码
   * @returns {boolean} 是否有效
   */
  async verifyMachineCode(code) {
    // 生成当前机器的机器码
    const currentCode = this.generateMachineCode();
    
    // 比较传入的机器码和当前机器生成的机器码是否一致
    return code === currentCode;
  }
}

// 创建单例实例
const machineCodeGenerator = new MachineCodeGenerator();

module.exports = machineCodeGenerator; 