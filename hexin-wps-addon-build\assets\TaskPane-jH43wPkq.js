import{U as nn,r as pe,h as wt,v as Ne,i as Q,j as Pt,k as Rs,m as Ht,_ as Us,n as an,o as K,c as X,a as f,p as rn,t as de,f as ne,q as Ie,w as Be,s as Wt,e as Kt,F as Mt,u as Dt,x as yt,y as on,z as ge,A as Xt,B as fs,C as dt,D as ln,E as cn}from"./index-DmqtQTOu.js";function un(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=nn.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const pn={onbuttonclick:un};var dn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function fn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function hn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var r=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,r.get?r:{enumerable:!0,get:function(){return e[a]}})}),t}var Ls={exports:{}};const vn={},gn=Object.freeze(Object.defineProperty({__proto__:null,default:vn},Symbol.toStringTag,{value:"Module"})),hs=hn(gn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",r=a?window:{};r.JS_SHA1_NO_WINDOW&&(a=!1);var g=!a&&typeof self=="object",T=!r.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;T?r=dn:g&&(r=self);var S=!r.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,y=!r.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",C="0123456789abcdef".split(""),D=[-**********,8388608,32768,128],L=[24,16,8,0],W=["hex","array","digest","arrayBuffer"],I=[],q=Array.isArray;(r.JS_SHA1_NO_NODE_JS||!q)&&(q=function(p){return Object.prototype.toString.call(p)==="[object Array]"});var G=ArrayBuffer.isView;y&&(r.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!G)&&(G=function(p){return typeof p=="object"&&p.buffer&&p.buffer.constructor===ArrayBuffer});var N=function(p){var b=typeof p;if(b==="string")return[p,!0];if(b!=="object"||p===null)throw new Error(s);if(y&&p.constructor===ArrayBuffer)return[new Uint8Array(p),!1];if(!q(p)&&!G(p))throw new Error(s);return[p,!1]},te=function(p){return function(b){return new O(!0).update(b)[p]()}},se=function(){var p=te("hex");T&&(p=J(p)),p.create=function(){return new O},p.update=function(k){return p.create().update(k)};for(var b=0;b<W.length;++b){var m=W[b];p[m]=te(m)}return p},J=function(p){var b=hs,m=hs.Buffer,k;m.from&&!r.JS_SHA1_NO_BUFFER_FROM?k=m.from:k=function(M){return new m(M)};var $=function(M){if(typeof M=="string")return b.createHash("sha1").update(M,"utf8").digest("hex");if(M==null)throw new Error(s);return M.constructor===ArrayBuffer&&(M=new Uint8Array(M)),q(M)||G(M)||M.constructor===m?b.createHash("sha1").update(k(M)).digest("hex"):p(M)};return $},d=function(p){return function(b,m){return new re(b,!0).update(m)[p]()}},B=function(){var p=d("hex");p.create=function(k){return new re(k)},p.update=function(k,$){return p.create(k).update($)};for(var b=0;b<W.length;++b){var m=W[b];p[m]=d(m)}return p};function O(p){p?(I[0]=I[16]=I[1]=I[2]=I[3]=I[4]=I[5]=I[6]=I[7]=I[8]=I[9]=I[10]=I[11]=I[12]=I[13]=I[14]=I[15]=0,this.blocks=I):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}O.prototype.update=function(p){if(this.finalized)throw new Error(t);var b=N(p);p=b[0];for(var m=b[1],k,$=0,M,U=p.length||0,E=this.blocks;$<U;){if(this.hashed&&(this.hashed=!1,E[0]=this.block,this.block=E[16]=E[1]=E[2]=E[3]=E[4]=E[5]=E[6]=E[7]=E[8]=E[9]=E[10]=E[11]=E[12]=E[13]=E[14]=E[15]=0),m)for(M=this.start;$<U&&M<64;++$)k=p.charCodeAt($),k<128?E[M>>>2]|=k<<L[M++&3]:k<2048?(E[M>>>2]|=(192|k>>>6)<<L[M++&3],E[M>>>2]|=(128|k&63)<<L[M++&3]):k<55296||k>=57344?(E[M>>>2]|=(224|k>>>12)<<L[M++&3],E[M>>>2]|=(128|k>>>6&63)<<L[M++&3],E[M>>>2]|=(128|k&63)<<L[M++&3]):(k=65536+((k&1023)<<10|p.charCodeAt(++$)&1023),E[M>>>2]|=(240|k>>>18)<<L[M++&3],E[M>>>2]|=(128|k>>>12&63)<<L[M++&3],E[M>>>2]|=(128|k>>>6&63)<<L[M++&3],E[M>>>2]|=(128|k&63)<<L[M++&3]);else for(M=this.start;$<U&&M<64;++$)E[M>>>2]|=p[$]<<L[M++&3];this.lastByteIndex=M,this.bytes+=M-this.start,M>=64?(this.block=E[16],this.start=M-64,this.hash(),this.hashed=!0):this.start=M}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},O.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var p=this.blocks,b=this.lastByteIndex;p[16]=this.block,p[b>>>2]|=D[b&3],this.block=p[16],b>=56&&(this.hashed||this.hash(),p[0]=this.block,p[16]=p[1]=p[2]=p[3]=p[4]=p[5]=p[6]=p[7]=p[8]=p[9]=p[10]=p[11]=p[12]=p[13]=p[14]=p[15]=0),p[14]=this.hBytes<<3|this.bytes>>>29,p[15]=this.bytes<<3,this.hash()}},O.prototype.hash=function(){var p=this.h0,b=this.h1,m=this.h2,k=this.h3,$=this.h4,M,U,E,Y=this.blocks;for(U=16;U<80;++U)E=Y[U-3]^Y[U-8]^Y[U-14]^Y[U-16],Y[U]=E<<1|E>>>31;for(U=0;U<20;U+=5)M=b&m|~b&k,E=p<<5|p>>>27,$=E+M+$+1518500249+Y[U]<<0,b=b<<30|b>>>2,M=p&b|~p&m,E=$<<5|$>>>27,k=E+M+k+1518500249+Y[U+1]<<0,p=p<<30|p>>>2,M=$&p|~$&b,E=k<<5|k>>>27,m=E+M+m+1518500249+Y[U+2]<<0,$=$<<30|$>>>2,M=k&$|~k&p,E=m<<5|m>>>27,b=E+M+b+1518500249+Y[U+3]<<0,k=k<<30|k>>>2,M=m&k|~m&$,E=b<<5|b>>>27,p=E+M+p+1518500249+Y[U+4]<<0,m=m<<30|m>>>2;for(;U<40;U+=5)M=b^m^k,E=p<<5|p>>>27,$=E+M+$+1859775393+Y[U]<<0,b=b<<30|b>>>2,M=p^b^m,E=$<<5|$>>>27,k=E+M+k+1859775393+Y[U+1]<<0,p=p<<30|p>>>2,M=$^p^b,E=k<<5|k>>>27,m=E+M+m+1859775393+Y[U+2]<<0,$=$<<30|$>>>2,M=k^$^p,E=m<<5|m>>>27,b=E+M+b+1859775393+Y[U+3]<<0,k=k<<30|k>>>2,M=m^k^$,E=b<<5|b>>>27,p=E+M+p+1859775393+Y[U+4]<<0,m=m<<30|m>>>2;for(;U<60;U+=5)M=b&m|b&k|m&k,E=p<<5|p>>>27,$=E+M+$-1894007588+Y[U]<<0,b=b<<30|b>>>2,M=p&b|p&m|b&m,E=$<<5|$>>>27,k=E+M+k-1894007588+Y[U+1]<<0,p=p<<30|p>>>2,M=$&p|$&b|p&b,E=k<<5|k>>>27,m=E+M+m-1894007588+Y[U+2]<<0,$=$<<30|$>>>2,M=k&$|k&p|$&p,E=m<<5|m>>>27,b=E+M+b-1894007588+Y[U+3]<<0,k=k<<30|k>>>2,M=m&k|m&$|k&$,E=b<<5|b>>>27,p=E+M+p-1894007588+Y[U+4]<<0,m=m<<30|m>>>2;for(;U<80;U+=5)M=b^m^k,E=p<<5|p>>>27,$=E+M+$-899497514+Y[U]<<0,b=b<<30|b>>>2,M=p^b^m,E=$<<5|$>>>27,k=E+M+k-899497514+Y[U+1]<<0,p=p<<30|p>>>2,M=$^p^b,E=k<<5|k>>>27,m=E+M+m-899497514+Y[U+2]<<0,$=$<<30|$>>>2,M=k^$^p,E=m<<5|m>>>27,b=E+M+b-899497514+Y[U+3]<<0,k=k<<30|k>>>2,M=m^k^$,E=b<<5|b>>>27,p=E+M+p-899497514+Y[U+4]<<0,m=m<<30|m>>>2;this.h0=this.h0+p<<0,this.h1=this.h1+b<<0,this.h2=this.h2+m<<0,this.h3=this.h3+k<<0,this.h4=this.h4+$<<0},O.prototype.hex=function(){this.finalize();var p=this.h0,b=this.h1,m=this.h2,k=this.h3,$=this.h4;return C[p>>>28&15]+C[p>>>24&15]+C[p>>>20&15]+C[p>>>16&15]+C[p>>>12&15]+C[p>>>8&15]+C[p>>>4&15]+C[p&15]+C[b>>>28&15]+C[b>>>24&15]+C[b>>>20&15]+C[b>>>16&15]+C[b>>>12&15]+C[b>>>8&15]+C[b>>>4&15]+C[b&15]+C[m>>>28&15]+C[m>>>24&15]+C[m>>>20&15]+C[m>>>16&15]+C[m>>>12&15]+C[m>>>8&15]+C[m>>>4&15]+C[m&15]+C[k>>>28&15]+C[k>>>24&15]+C[k>>>20&15]+C[k>>>16&15]+C[k>>>12&15]+C[k>>>8&15]+C[k>>>4&15]+C[k&15]+C[$>>>28&15]+C[$>>>24&15]+C[$>>>20&15]+C[$>>>16&15]+C[$>>>12&15]+C[$>>>8&15]+C[$>>>4&15]+C[$&15]},O.prototype.toString=O.prototype.hex,O.prototype.digest=function(){this.finalize();var p=this.h0,b=this.h1,m=this.h2,k=this.h3,$=this.h4;return[p>>>24&255,p>>>16&255,p>>>8&255,p&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,m>>>24&255,m>>>16&255,m>>>8&255,m&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,$>>>24&255,$>>>16&255,$>>>8&255,$&255]},O.prototype.array=O.prototype.digest,O.prototype.arrayBuffer=function(){this.finalize();var p=new ArrayBuffer(20),b=new DataView(p);return b.setUint32(0,this.h0),b.setUint32(4,this.h1),b.setUint32(8,this.h2),b.setUint32(12,this.h3),b.setUint32(16,this.h4),p};function re(p,b){var m,k=N(p);if(p=k[0],k[1]){var $=[],M=p.length,U=0,E;for(m=0;m<M;++m)E=p.charCodeAt(m),E<128?$[U++]=E:E<2048?($[U++]=192|E>>>6,$[U++]=128|E&63):E<55296||E>=57344?($[U++]=224|E>>>12,$[U++]=128|E>>>6&63,$[U++]=128|E&63):(E=65536+((E&1023)<<10|p.charCodeAt(++m)&1023),$[U++]=240|E>>>18,$[U++]=128|E>>>12&63,$[U++]=128|E>>>6&63,$[U++]=128|E&63);p=$}p.length>64&&(p=new O(!0).update(p).array());var Y=[],he=[];for(m=0;m<64;++m){var me=p[m]||0;Y[m]=92^me,he[m]=54^me}O.call(this,b),this.update(he),this.oKeyPad=Y,this.inner=!0,this.sharedMemory=b}re.prototype=new O,re.prototype.finalize=function(){if(O.prototype.finalize.call(this),this.inner){this.inner=!1;var p=this.array();O.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(p),O.prototype.finalize.call(this)}};var oe=se();oe.sha1=oe,oe.sha1.hmac=B(),S?e.exports=oe:r.sha1=oe})()})(Ls);var mn=Ls.exports;const bn=fn(mn);function vs(){return"http://worksheet.hexinedu.com"}function xt(){return"http://127.0.0.1:3000"}function gs(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const Tt=async(e,s,t,a={},r=8e3)=>{try{return await Promise.race([e(),new Promise((g,T)=>setTimeout(()=>T(new Error("WebSocket请求超时，切换到HTTP")),r))])}catch{try{let T;return s==="get"?T=await Pt.get(t,{params:a}):s==="post"?T=await Pt.post(t,a):s==="delete"&&(T=await Pt.delete(t)),T.data}catch(T){throw new Error(`请求失败: ${T.message||"未知错误"}`)}}};function wn(e,s,t,a){const r=[e,s,t,a].join(":");return bn(r)}function yn(){const e=pe(""),s=pe(""),t=pe(""),a=wt({}),r=pe(""),g=pe("");let T="",S=null;const y=pe("c:\\Temp"),C=wt({appKey:"",appSecret:""}),D=wt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),L=pe(""),W=pe("junior"),I=pe(null),q=pe(!1),G=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],N=()=>Ne.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],te=wt(N()),se=async()=>{try{const n=await Q.getWatcherStatus();n.data&&n.data.watchDir&&(y.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${y.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},J=async()=>{var n,o,l;try{if(!C.appKey||!C.appSecret)throw new Error("未初始化app信息");const v=60,i=Date.now();let h;try{const z=window.Application.PluginStorage.getItem("token_info");z&&(h=JSON.parse(z))}catch(z){h=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${z.message}</span><br/>`}if(h&&h.access_token&&h.expired_time>i+v*1e3)return h.access_token;const c=C.appKey,P="1234567",A=Math.floor(Date.now()/1e3),_=wn(c,P,C.appSecret,A),R=await Pt.get(vs()+"/api/open/account/v1/auth/token",{params:{app_key:c,app_nonstr:P,app_timestamp:A,app_signature:_}});if((o=(n=R.data)==null?void 0:n.data)!=null&&o.access_token){const z=R.data.data.access_token;let ee;if(R.data.data.expired_time){const ce=parseInt(R.data.data.expired_time);ee=i+ce*1e3,t.value+=`<span class="log-item info">token已更新，有效期${ce}秒</span><br/>`}else ee=i+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const ae={access_token:z,expired_time:ee};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(ae))}catch(ce){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${ce.message}</span><br/>`}return z}else throw new Error(((l=R.data)==null?void 0:l.message)||"获取access_token失败")}catch(v){throw t.value+=`<span class="log-item error">获取access_token失败: ${v.message}</span><br/>`,v}},d=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},B=()=>{const n=window.Application,o=n.Documents.Count;for(let l=1;l<=o;l++){const v=n.Documents.Item(l);if(v.DocID===r.value)return v}return null},O=()=>{try{const n=B();if(!n)return{isValid:!1,message:"未找到当前文档"};let o="";try{o=n.Name||""}catch{try{o=$("getDocName")||""}catch{o=""}}if(o){const l=o.toLowerCase();return l.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:l.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},re=(n,o="",l=0,v=null)=>{try{const i=window.Application,h=B();let c;if(v)c=v;else{const P=h.ActiveWindow.Selection;if(!P||P.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;c=P.Range}if(o){const P=c.Text,A=c.Find;A.ClearFormatting(),A.Text=o,A.Forward=!0,A.Wrap=1;let _=0,R=[];for(;A.Execute()&&(A.Found&&c.Start<=A.Parent.Start&&A.Parent.End<=c.End);){const z=A.Parent.Start,ee=A.Parent.End;if(R.some(ce=>z===ce.start&&ee===ce.end))A.Parent.Start=ee;else{if(R.push({start:z,end:ee}),l===-1||_===l){const ce=h.Comments.Add(A.Parent,n);if(l!==-1&&_===l)return t.value+=`<span class="log-item success">已为第${l+1}个"${o}"添加批注: "${n}"</span><br/>`,!0}_++,A.Parent.Start=ee}}return l!==-1&&_<=l?(t.value+=`<span class="log-item error">在${v?"指定范围":"选中内容"}中未找到第${l+1}个"${o}"</span><br/>`,!1):l===-1&&_>0?(t.value+=`<span class="log-item success">已为${_}处"${o}"添加批注: "${n}"</span><br/>`,!0):l===-1&&_===0?(t.value+=`<span class="log-item error">在${v?"指定范围":"选中内容"}中未找到关键字"${o}"</span><br/>`,!1):!0}else{const P=h.Comments.Add(c,n);return t.value+=`<span class="log-item success">已为${v?"指定范围":"选中内容"}添加批注: "${n}"</span><br/>`,!0}}catch(i){return t.value+=`<span class="log-item error">添加批注失败: ${i.message}</span><br/>`,!1}},oe=n=>n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",p=n=>n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"进行中",b=n=>{const o=Date.now()-n,l=Math.floor(o/1e3);return l<60?`${l}秒`:l<3600?`${Math.floor(l/60)}分${l%60}秒`:`${Math.floor(l/3600)}时${Math.floor(l%3600/60)}分`},m=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const l=B();if(l&&l.ContentControls)for(let v=1;v<=l.ContentControls.Count;v++)try{const i=l.ContentControls.Item(v);if(i&&i.Title&&(i.Title===`任务_${n}`||i.Title===`任务增强_${n}`||i.Title===`校对_${n}`)){const h=i.Title===`任务增强_${n}`||a[n].isEnhanced,c=i.Title===`校对_${n}`||a[n].isCheckTask;c?i.Title=`已停止校对_${n}`:h?i.Title=`已停止增强_${n}`:i.Title=`已停止_${n}`;const P=c?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${P}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(l){t.value+=`<span class="log-item warning">更新控件标题失败: ${l.message}</span><br/>`}let o=null;if(Z[n]&&Z[n].urlId){o=Z[n].urlId;try{try{const l=await Tt(async()=>await Q.getUrlMonitorStatus(),"get",`${xt()}/api/url/status`),i=(Array.isArray(l)?l:l.data?Array.isArray(l.data)?l.data:[]:[]).find(h=>h.urlId===o);i&&i.downloadedPath&&(a[n].resultFile=i.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${i.downloadedPath}</span><br/>`)}catch(l){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${l.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await xe(o),delete Z[n]}catch(l){t.value+=`<span class="log-item warning">停止URL监控出错: ${l.message}，将重试</span><br/>`,delete Z[n],setTimeout(async()=>{try{o&&await Tt(async()=>await Q.stopUrlMonitoring(o),"delete",`${xt()}/api/url/monitor/${o}`)}catch(v){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${v.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(o){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${o.message}</span><br/>`,Z[n]&&delete Z[n]}},k=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const o=B();if(o&&o.ContentControls)for(let v=1;v<=o.ContentControls.Count;v++)try{const i=o.ContentControls.Item(v);if(i&&i.Title&&(i.Title===`任务_${n}`||i.Title===`任务增强_${n}`||i.Title===`校对_${n}`)){const h=i.Title===`任务增强_${n}`||a[n].isEnhanced,c=i.Title===`校对_${n}`||a[n].isCheckTask;c?i.Title=`已停止校对_${n}`:h?i.Title=`已停止增强_${n}`:i.Title=`已停止_${n}`,i.LockContents=!1;const P=c?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${P}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let l=null;if(Z[n]&&Z[n].urlId){l=Z[n].urlId;try{const v=await Q.getUrlMonitorStatus(),h=(Array.isArray(v)?v:v.data?Array.isArray(v.data)?v.data:[]:[]).find(c=>c.urlId===l);h&&h.downloadedPath&&(a[n].resultFile=h.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${h.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await xe(l),delete Z[n]}catch(v){t.value+=`<span class="log-item warning">停止URL监控出错: ${v.message}，将重试</span><br/>`,delete Z[n]}}},$=n=>pn.onbuttonclick(n),M=()=>{try{e.value=$("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},U=()=>{B().ActiveWindow.Selection.Copy()},E=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${y.value}\\${n}`,12,"","",!1),o.Close(),B().ActiveWindow.Activate()},Y=n=>{const o=window.Application.Documents.Add("",!1,0,!1);o.Content.Paste(),o.SaveAs2(`${y.value}\\${n}`,12,"","",!1),o.Close()},he=n=>{try{const l=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,v=window.Application.Documents.Add("",!1,0,!1);v.Content.Paste();const i=`${l}\\${n}`;v.SaveAs2(i,12,"","",!1),v.Close(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${i}.docx</span><br/>`}catch(o){throw t.value+=`<span class="log-item error">方式三保存失败: ${o.message}</span><br/>`,o}},me=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${y.value}\\${n}`,12,"","",!1),B().ActiveWindow.Activate()},Ce=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let o="method2";try{const l=await Q.getSaveMethod();if(l.success&&l.saveMethod){o=l.saveMethod;const v=o==="method1"?"方式一":o==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${v}</span><br/>`}}catch(l){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${l.message}</span><br/>`}o==="method1"?(E(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${y.value}\\${n}.docx</span><br/>`):o==="method2"?(Y(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${y.value}\\${n}.docx</span><br/>`):o==="method3"?(he(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):o==="method4"&&(me(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${y.value}\\${n}.docx</span><br/>`),Q.associateFileWithClient(`${n}.docx`).then(l=>{l.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${l.message||"未知错误"}</span><br/>`}).catch(l=>{t.value+=`<span class="log-item warning">关联文件时出错: ${l.message}</span><br/>`})}catch(o){t.value+=`<span class="log-item error">保存文件失败: ${o.message}</span><br/>`}},Fe=pe(null),we=wt([]),ke=new Set,_e=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),ye=async n=>{try{const o=n.slice(T.length);if(o.trim()){const l=Q.getClientId();if(!l){console.warn("无法获取客户端ID，跳过日志同步");return}const v=_e(o);if(!v.trim()){T=n;return}await Q.sendRequest("logger","syncLog",{content:v,timestamp:new Date().toISOString(),clientId:l}),T=n}}catch(o){console.error("同步日志到服务端失败:",o)}},$e=()=>{Q.connect().then(()=>{se()}).catch(o=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${o.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const o=[];for(const l in a)if(a.hasOwnProperty(l)){const v=a[l];v.status===1&&!v.terminated&&(o.includes(l)||o.push(l))}if(o.length>0){let l=!1;try{const v=B();if(v){const h=`taskpane_id_${v.DocID}`,c=window.Application.PluginStorage.getItem(h);if(c){const P=window.Application.GetTaskPane(c);P&&(l=P.Visible)}}}catch(v){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${v.message}</span><br/>`,l=!0}setTimeout(()=>{if(l){const v=`检测到 ${o.length} 个未完成的任务，是否继续？`;w(v).then(i=>{i?(t.value+=`<span class="log-item info">用户选择继续 ${o.length} 个进行中的任务 (by taskId)...</span><br/>`,Q.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:o}).then(h=>{h&&h.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${h.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(h==null?void 0:h.message)||"未知错误"}</span><br/>`}).catch(h=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${h.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',o.forEach(h=>{m(h)}),t.value+=`<span class="log-item success">${o.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(i=>{t.value+=`<span class="log-item error">弹窗错误: ${i.message}，默认停止任务（保留控件）</span><br/>`,o.forEach(h=>{m(h)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Q.setConnectionSuccessHandler(n),Q.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),Q.addEventListener("connection",o=>{o.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${o.reason||"未知"}, 代码: ${o.code||"N/A"}，将自动重连</span><br/>`)}),Q.addEventListener("watcher",o=>{var l,v;if(we.push(o),o.type,o.eventType==="uploadSuccess"){const i=(l=o.data)==null?void 0:l.file,h=i==null?void 0:i.replace(/\.docx$/,""),c=`${o.eventType}_${i}_${Date.now()}`;if(ke.has(c)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${i}</span><br/>`;return}if(ke.add(c),ke.size>100){const A=ke.values();ke.delete(A.next().value)}const P=h&&((v=a[h])==null?void 0:v.wordType);He(o,P)}else o.eventType&&He(o,null)}),Q.addEventListener("urlMonitor",o=>{we.push(o),o.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${o.eventType||o.action}</span><br/>`),o.eventType&&He(o,null)}),Q.addEventListener("health",o=>{}),Q.addEventListener("error",o=>{const l=o.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${l}</span><br/>`,console.error("WebSocket错误:",o)})},Z=wt({}),Ae=async(n,o,l=!1,v=5e3,i={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const h=await Q.startUrlMonitoring(n,v,{downloadOnSuccess:i.downloadOnSuccess!==void 0?i.downloadOnSuccess:!0,appKey:i.appKey,filename:i.filename,taskId:o}),c=h.success||(h==null?void 0:h.success),P=h.urlId||(h==null?void 0:h.urlId);if(c&&P){Z[o]={urlId:P,url:n,isResultUrl:l,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${P}</span><br/>`;try{await Q.startUrlChecking(P)}catch{}return P}else throw new Error("服务器返回失败")}catch(h){return t.value+=`<span class="log-item error">启动URL监控失败: ${h.message}</span><br/>`,null}},xe=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(Z).forEach(l=>{Z[l].urlId===n&&delete Z[l]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const o=await Tt(async()=>await Q.stopUrlMonitoring(n),"delete",`${xt()}/api/url/monitor/${n}`);return o&&(o.success||o!=null&&o.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(o){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${o.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await Tt(async()=>await Q.stopUrlMonitoring(n),"delete",`${xt()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},Pe=async()=>{try{const n=await Tt(async()=>await Q.getUrlMonitorStatus(),"get",`${xt()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},le=async n=>{try{return await Q.forceUrlCheck(n)}catch{return!1}},He=async(n,o)=>{var l;if(n.eventType==="uploadSuccess"){const v=n.data.file,i=v.replace(/\.docx$/,"");if(a[i]){if(a[i].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${i.substring(0,8)} 的上传通知</span><br/>`;return}if(a[i].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${i.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${v} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${o||a[i].wordType||"wps-analysis"}</span><br/>`,a[i].status=1,a[i].uploadSuccess=!0,await ze(i,o||a[i].wordType||"wps-analysis")}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:v,url:i,taskId:h,downloadedPath:c}=n.data,P=Object.keys(Z).filter(A=>Z[A].urlId===v);P.length>0&&P.forEach(A=>{c&&a[A]&&(a[A].resultFile=c,a[A].resultDownloaded=!0),delete Z[A]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:v,url:i,filePath:h,taskId:c}=n.data;if(!Object.values(Z).some(A=>A.urlId===v)&&c&&((l=a[c])!=null&&l.terminated))return;if(c&&a[c]&&a[c].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${v} 的文件下载通知</span><br/>`,v)try{await Q.stopUrlMonitoring(v),Z[c]&&delete Z[c]}catch{}return}if(c&&a[c]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${h}</span><br/>`,a[c].isCheckTask&&h.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${h}</span><br/>`;try{const _=window.Application.FileSystem.ReadFile(h),R=JSON.parse(_);if(await ve(R,c)){a[c].status=2,t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}已完成批注处理</span><br/>`;const ee=b(a[c].startTime);t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}完成，总耗时${ee}</span><br/>`}}catch(A){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${A.message}</span><br/>`,A.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${h}</span><br/>`),a[c].status=-1,a[c].errorMessage=`JSON处理失败: ${A.message}`,t.value+=`<span class="log-item error">校对任务${c.substring(0,8)}处理失败</span><br/>`}}else{a[c].resultFile=h,a[c].resultDownloaded=!0;const A=b(a[c].startTime);t.value+=`<span class="log-item success">任务${c.substring(0,8)}完成，总耗时${A}</span><br/>`,await qe(c)}Z[c]&&Z[c].urlId&&(xe(Z[c].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete Z[c])}else if(v){t.value+=`<span class="log-item info">URL文件已下载: ${h}</span><br/>`;const A=Object.keys(Z).filter(_=>{var R;return Z[_].urlId===v&&!((R=a[_])!=null&&R.terminated)});A.length>0&&A.forEach(async _=>{if(a[_]){if(t.value+=`<span class="log-item info">关联到任务: ${_.substring(0,8)}</span><br/>`,a[_].resultFile=h,a[_].resultDownloaded=!0,a[_].isCheckTask&&h.endsWith(".wps.json"))try{const z=window.Application.FileSystem.ReadFile(h),ee=JSON.parse(z);if(await ve(ee,_)&&a[_].status===1){a[_].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const ce=b(a[_].startTime);t.value+=`<span class="log-item success">校对任务${_.substring(0,8)}完成，总耗时${ce}</span><br/>`}}catch(R){t.value+=`<span class="log-item error">处理校对JSON失败: ${R.message}</span><br/>`,a[_].status===1&&(a[_].status=-1,a[_].errorMessage=`JSON处理失败: ${R.message}`,t.value+=`<span class="log-item error">校对任务${_.substring(0,8)}处理失败</span><br/>`)}else if(a[_].status===1){a[_].status=2;const R=b(a[_].startTime);t.value+=`<span class="log-item success">任务${_.substring(0,8)}完成，总耗时${R}</span><br/>`}}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:v,url:i,error:h,taskId:c}=n.data;if(c&&a[c]&&a[c].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${c.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${h}</span><br/>`,c&&a[c]){a[c].status=-1,a[c].errorMessage=`下载失败: ${h}`;try{const P=B();if(P&&P.ContentControls)for(let A=1;A<=P.ContentControls.Count;A++)try{const _=P.ContentControls.Item(A);if(_&&_.Title&&(_.Title===`任务_${c}`||_.Title===`任务增强_${c}`||_.Title===`校对_${c}`)){const R=_.Title===`任务增强_${c}`||a[c].isEnhanced,z=_.Title===`校对_${c}`||a[c].isCheckTask;z?_.Title=`异常校对_${c}`:R?_.Title=`异常增强_${c}`:_.Title=`异常_${c}`;const ee=z?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${ee}任务${c.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(P){t.value+=`<span class="log-item warning">更新控件标题失败: ${P.message}</span><br/>`}be(c),Z[c]&&delete Z[c]}if(v)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${v}</span><br/>`,await Tt(async()=>await Q.stopUrlMonitoring(v),"delete",`${xt()}/api/url/monitor/${v}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},ze=async(n,o="wps-analysis")=>{try{if(!C.appKey)throw new Error("未初始化appKey信息");const l=await J(),v=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,i=await Pt.post(vs()+"/api/open/ticket/v1/ai_comment/create",{access_token:l,data:{app_key:C.appKey,subject:L.value,stage:W.value,file_name:`${n}`,word_url:v,word_type:o,is_ai_auto:!0,is_ai_edit:!0,create_user_id:C.userId,create_username:C.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),h=i.data.data.ticket_id;if(!h)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",be(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${h}，开始监控结果文件</span><br/>`;let c,P;o==="wps-check"?(c=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${C.appKey}/ai/${h}.wps.json`,P=`${h}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${P}</span><br/>`):(c=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${h}.wps.docx`,P=`${h}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${P}</span><br/>`);const A={downloadOnSuccess:!0,filename:P,appKey:C.appKey,ticketId:h,taskId:n},_=await Ae(c,n,!0,3e3,A);return a[n]&&(a[n].ticketId=h,a[n].resultUrl=c,a[n].urlMonitorId=_,a[n].status=1),i.data}catch(l){return t.value+=`<span class="log-item error">API调用失败: ${l.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${l.message}`,be(n)),!1}},Qe=async(n,o="wps-analysis")=>new Promise((l,v)=>{try{t.value+=`<span class="log-item info">监控目录: ${y.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${o}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=o,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const i=1e3,h=30;let c=0;const P=setInterval(()=>{if(c++,a[n]&&a[n].uploadSuccess){clearInterval(P),l(!0);return}c>=h&&(clearInterval(P),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',v(new Error("上传超时，未收到完成通知")))},i)}catch(i){t.value+=`<span class="log-item error">任务处理异常: ${i.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${i.message}`),be(n),v(i)}}),et=async()=>{var l;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,o=n.ActiveDocument;if(r.value=o.DocID,g.value=n.ActiveWindow.Index,o!=null&&o.ContentControls)for(let v=1;v<=o.ContentControls.Count;v++){const i=o.ContentControls.Item(v);if(i&&i.Title){let h=null,c=1,P=!1,A=!1;if(i.Title.startsWith("任务增强_")?(h=i.Title.substring(5),c=1,P=!0):i.Title.startsWith("任务_")?(h=i.Title.substring(3),c=1):i.Title.startsWith("校对_")?(h=i.Title.substring(3),c=1,A=!0):i.Title.startsWith("已完成增强_")?(h=i.Title.substring(6),c=2,P=!0):i.Title.startsWith("已完成校对_")?(h=i.Title.substring(6),c=2,A=!0):i.Title.startsWith("已完成_")?(h=i.Title.substring(4),c=2):i.Title.startsWith("异常增强_")?(h=i.Title.substring(5),c=-1,P=!0):i.Title.startsWith("异常校对_")?(h=i.Title.substring(5),c=-1,A=!0):i.Title.startsWith("异常_")?(h=i.Title.substring(3),c=-1):i.Title.startsWith("已停止增强_")?(h=i.Title.substring(6),c=4,P=!0):i.Title.startsWith("已停止校对_")?(h=i.Title.substring(6),c=4,A=!0):i.Title.startsWith("已停止_")&&(h=i.Title.substring(4),c=4),h&&!a[h]){let _="";try{_=((l=i.Range)==null?void 0:l.Text)||""}catch{}let R=Date.now()-24*60*60*1e3;try{if(h.length===24){const ae=h.substring(0,8),ce=parseInt(ae,16);!isNaN(ce)&&ce>0&&ce<2147483647&&(R=ce*1e3)}else{const ae=Date.now()-864e5;c===2?R=ae-60*60*1e3:c===-1?R=ae-30*60*1e3:c===4?R=ae-45*60*1e3:R=ae}}catch{}a[h]={status:c,startTime:R,contentControlId:i.ID,isEnhanced:P,isCheckTask:A,selectedText:_};const z=c===1?"进行中":c===2?"已完成":c===-1?"异常":c===4?"已停止":"未知",ee=A?"校对":P?"增强":"普通";t.value+=`<span class="log-item info">发现已有${ee}任务: ${h.substring(0,8)}, 状态: ${z}</span><br/>`}}}},be=(n,o=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}o&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const l=B();if(!l){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let v=!1;const i=a[n].isEnhanced;if(l.ContentControls&&l.ContentControls.Count>0)for(let h=l.ContentControls.Count;h>=1;h--)try{const c=l.ContentControls.Item(h);c&&c.Title&&c.Title.includes(n)&&(c.LockContents=!1,c.Delete(!1),v=!0,console.log(c.Title),t.value+=`<span class="log-item success">已解锁并删除${i?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(c){t.value+=`<span class="log-item warning">访问第${h}个控件时出错: ${c.message}</span><br/>`;continue}v?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${i?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(l){t.value+=`<span class="log-item error">删除内容控件失败: ${l.message}</span><br/>`}},je=n=>{var o;try{const l=window.wps,v=B();if(!v||!v.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const i=(o=a[n])==null?void 0:o.isEnhanced;let h=null;for(let P=1;P<=v.ContentControls.Count;P++)try{const A=v.ContentControls.Item(P);if(A&&A.Title&&A.Title.includes(n)){h=A;break}}catch{continue}if(!h){const P=a[n];P&&(P.status===2||P.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}h.Range.Select();const c=l.Windows.Item(g.value);c&&c.Selection&&c.Selection.Range&&c.ScrollIntoView(c.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${i?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(l){t.value+=`<span class="log-item error">跳转到任务控件失败: ${l.message}</span><br/>`}},qe=async n=>{var c,P,A;const o=B(),l=a[n];if(!l){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(l.terminated||l.status!==1)return;if(l.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const v=B();let i=null;if(v&&v.ContentControls)for(let _=1;_<=v.ContentControls.Count;_++){const R=v.ContentControls.Item(_);if(R&&R.Title&&R.Title.includes(n)){i=R;break}}if(!i){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,l.errorMessage&&l.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${l.errorMessage}</span><br/>`;return}return}if(l.errorMessage&&l.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${l.errorMessage}</span><br/>`,be(n);return}if(!l.resultFile)return;a[n].documentInserted=!0;const h=i.Range;i.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${l.resultFile}...</span><br/>`;const _=o.Range(h.End,h.End);_.InsertParagraphAfter(),h.Text.includes("\v")&&_.InsertAfter("\v");let R=_.End;const z=(c=h.Tables)==null?void 0:c.Count;if(z){const ue=h.Tables.Item(z);((P=ue==null?void 0:ue.Range)==null?void 0:P.End)>R&&(ue.Range.InsertParagraphAfter(),R=(A=ue==null?void 0:ue.Range)==null?void 0:A.End)}o.Range(R,R).InsertFile(l.resultFile);for(let ue=1;ue<=v.ContentControls.Count;ue++){const at=v.ContentControls.Item(ue);if(at&&at.Title&&(at.Title===`任务_${n}`||at.Title===`任务增强_${n}`)){i=at;break}}const ae=o.Range(i.Range.End-1,i.Range.End);ae.Text.includes("\r")&&ae.Delete(),a[n].status=2,Z[n]&&Z[n].urlId&&Q.sendRequest("urlMonitor","updateTaskStatus",{urlId:Z[n].urlId,status:"completed",taskId:n}).then(ue=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(ue=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${ue.message}</span><br/>`});const Ke=b(l.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${Ke}</span><br/>`;const ut=i.Title===`任务增强_${n}`||l.isEnhanced,Te=i.Title===`校对_${n}`||l.isCheckTask;if(i){Te?i.Title=`已完成校对_${n}`:ut?i.Title=`已完成增强_${n}`:i.Title=`已完成_${n}`;const ue=Te?"校对":ut?"增强":"普通";t.value+=`<span class="log-item info">已将${ue}任务${n.substring(0,8)}控件标记为已完成</span><br/>`}}catch(_){a[n].documentInserted=!1,a[n].status=-1;const R=i.Title===`任务增强_${n}`||l.isEnhanced,z=i.Title===`校对_${n}`||l.isCheckTask;if(i){z?i.Title=`异常校对_${n}`:R?i.Title=`异常增强_${n}`:i.Title=`异常_${n}`;const ee=z?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${ee}任务${n.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${_.message}</span><br/>`}},tt=async()=>{const n=(v=1e3)=>new Promise(i=>{setTimeout(()=>i(),v)}),o=new Map,l=Object.keys(a).filter(v=>a[v].status===1&&!a[v].terminated);for(l.length?(l.forEach(v=>{o.has(v)||o.set(v,Date.now())}),await Promise.all(l.map(v=>qe(v)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const v=Object.keys(a).filter(i=>a[i].status===1&&!a[i].terminated);v.forEach(i=>{o.has(i)||o.set(i,Date.now());const h=o.get(i);(Date.now()-h)/1e3/60>=5e4&&a[i]&&!a[i].terminated&&(a[i].terminated=!0,a[i].status=-1,t.value+=`<span class="log-item warning">任务 ${i} 执行超过5分钟，已自动终止</span><br/>`,o.delete(i))}),v.length&&await Promise.all(v.map(i=>qe(i)))}},Me=async(n="wps-analysis")=>{const o=ct();if(o){const{primary:v,all:i}=o,{taskId:h,control:c,task:P,isEnhanced:A,overlapType:_}=v,R=i.filter(z=>z.task&&z.task.status===1);if(R.length>0){const z=R.map(ee=>ee.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${z})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${i.length}个已有任务重叠，重叠类型: ${_}</span><br/>`,x();try{for(const z of i){const{taskId:ee,control:ae,task:ce,isEnhanced:Ke}=z;t.value+=`<span class="log-item info">删除重叠的${Ke?"增强":"普通"}任务 ${ee.substring(0,8)}，状态为 ${p((ce==null?void 0:ce.status)||0)}</span><br/>`,ce&&(ce.status=3,ce.terminated=!0,ce.errorMessage="用户重新创建任务时删除");try{ae.LockContents=!1,ae.Delete(!1),a[ee]&&(a[ee].placeholderRemoved=!0)}catch(ut){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ut.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${i.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(z){return F(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${z.message}</span><br/>`,Promise.reject(z)}F()}const l=gs().replace(/-/g,"").substring(0,8);return new Promise(async(v,i)=>{try{const h=window.wps,c=B(),P=c.ActiveWindow.Selection,A=P.Range,_=P.Text||"";if(s.value=_,U(),A){const R=A.Paragraphs,z=R.Item(1),ee=R.Item(R.Count),ae=z.Range.Start,ce=ee.Range.End,Ke=A.Start,ut=A.End,Te=c.Range(Math.min(ae,Ke),Math.max(ce,ut));try{let ue=c.ContentControls.Add(h.Enum.wdContentControlRichText,Te);if(!ue){if(console.log("创建内容控件失败"),ue=c.ContentControls.Add(h.Enum.wdContentControlRichText),!ue){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',i(new Error("创建内容控件失败"));return}A.Cut(),ue.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const at=n==="wps-enhance_analysis";ue.Title=at?`任务增强_${l}`:`任务_${l}`,ue.LockContents=!0,a[l]||(a[l]={}),a[l].contentControlId=ue.ID,a[l].isEnhanced=at,t.value+=`<span class="log-item info">已创建${at?"增强":"普通"}内容控件并锁定选区</span><br/>`;const sn=ue.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${sn.length}</span><br/>`}catch(ue){t.value+=`<span class="log-item error">创建内容控件失败: ${ue.message}</span><br/>`,i(ue);return}}await Ce(l),a[l]={status:1,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:_},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${l}，类型: ${n}</span><br/>`;try{await Qe(l,n)?v():(a[l]&&a[l].status===1&&(a[l].status=-1,a[l].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${l.substring(0,8)}失败</span><br/>`,V(l)),i(new Error("API调用失败或超时")))}catch(R){a[l]&&(a[l].status=-1,a[l].errorMessage=`执行错误: ${R.message}`,t.value+=`<span class="log-item error">任务${l.substring(0,8)}执行出错: ${R.message}</span><br/>`,V(l)),i(R)}}catch(h){i(h)}})},Oe=async()=>{const n=ct();if(n){const{primary:l,all:v}=n,{taskId:i,control:h,task:c,isEnhanced:P,overlapType:A}=l,_=v.filter(R=>R.task&&R.task.status===1);if(_.length>0){const R=_.map(z=>z.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${R})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${v.length}个已有任务重叠，重叠类型: ${A}</span><br/>`,x();try{for(const R of v){const{taskId:z,control:ee,task:ae,isEnhanced:ce}=R;t.value+=`<span class="log-item info">删除重叠的校对任务 ${z.substring(0,8)}，状态为 ${p((ae==null?void 0:ae.status)||0)}</span><br/>`,ae&&(ae.status=3,ae.terminated=!0,ae.errorMessage="用户重新创建校对任务时删除");try{ee.LockContents=!1,ee.Delete(!1),a[z]&&(a[z].placeholderRemoved=!0)}catch(Ke){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${Ke.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${v.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(R){return F(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${R.message}</span><br/>`,Promise.reject(R)}F()}const o=gs().replace(/-/g,"").substring(0,8);return console.log("uuid",o),new Promise(async(l,v)=>{try{const i=window.wps,h=B(),c=h.ActiveWindow.Selection,P=c.Range,A=c.Text||"";if(s.value=A,U(),P){const _=P.Paragraphs,R=_.Item(1),z=_.Item(_.Count),ee=R.Range.Start,ae=z.Range.End,ce=P.Start,Ke=P.End,ut=h.Range(Math.min(ee,ce),Math.max(ae,Ke));try{let Te=h.ContentControls.Add(i.Enum.wdContentControlRichText,ut);if(!Te){if(console.log("创建校对内容控件失败"),Te=h.ContentControls.Add(i.Enum.wdContentControlRichText),!Te){t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',v(new Error("创建校对内容控件失败"));return}P.Cut(),Te.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',Te.Title=`校对_${o}`,Te.LockContents=!0,a[o]||(a[o]={}),a[o].contentControlId=Te.ID,a[o].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const ue=Te.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${ue.length}</span><br/>`}catch(Te){t.value+=`<span class="log-item error">创建校对内容控件失败: ${Te.message}</span><br/>`,v(Te);return}}await Ce(o),a[o]={status:1,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:A},t.value+=`<span class="log-item success">创建校对任务: ${o}，类型: wps-check</span><br/>`,console.log("uuid",o);try{await Qe(o,"wps-check")?l():(a[o]&&a[o].status===1&&(a[o].status=-1,a[o].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}失败</span><br/>`,V(o)),v(new Error("校对API调用失败或超时")))}catch(_){a[o]&&(a[o].status=-1,a[o].errorMessage=`校对执行错误: ${_.message}`,t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}执行出错: ${_.message}</span><br/>`,V(o)),v(_)}}catch(i){v(i)}})},st=async()=>{},We=()=>Ye()>0?"status-error":Je()>0?"status-running":nt()>0?"status-completed":"",Je=()=>Object.values(a).filter(n=>n.status===1).length,nt=()=>Object.values(a).filter(n=>n.status===2).length,ot=()=>Object.values(a).filter(n=>n.status===2||n.status===4).length,Ye=()=>Object.values(a).filter(n=>n.status===-1).length,it=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,o=B();if(!o){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let l=0;if(Object.keys(a).forEach(v=>{try{a[v].status===1&&(a[v].status=3,a[v].terminated=!0,a[v].errorMessage="强制清除"),be(v),l++}catch(i){t.value+=`<span class="log-item warning">清除任务${v.substring(0,8)}失败: ${i.message}</span><br/>`}}),o.ContentControls&&o.ContentControls.Count>0)for(let v=o.ContentControls.Count;v>=1;v--)try{const i=o.ContentControls.Item(v);if(i&&i.Title&&(i.Title.startsWith("任务_")||i.Title.startsWith("任务增强_")||i.Title.startsWith("已完成_")||i.Title.startsWith("已完成增强_")||i.Title.startsWith("异常_")||i.Title.startsWith("异常增强_")||i.Title.startsWith("已停止_")||i.Title.startsWith("已停止增强_")))try{i.LockContents=!1,i.Delete(!1);let h;i.Title.startsWith("任务增强_")?h=i.Title.substring(5):i.Title.startsWith("任务_")?h=i.Title.substring(3):i.Title.startsWith("已完成增强_")?h=i.Title.substring(6):i.Title.startsWith("已完成_")?h=i.Title.substring(4):i.Title.startsWith("异常增强_")?h=i.Title.substring(5):i.Title.startsWith("异常_")?h=i.Title.substring(3):i.Title.startsWith("已停止增强_")?h=i.Title.substring(6):i.Title.startsWith("已停止_")&&(h=i.Title.substring(4)),a[h]?(a[h].placeholderRemoved=!0,a[h].status=3):a[h]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},l++,t.value+=`<span class="log-item success">已删除任务${h.substring(0,8)}的内容控件</span><br/>`}catch(h){t.value+=`<span class="log-item error">删除控件失败: ${h.message}</span><br/>`}}catch(i){t.value+=`<span class="log-item warning">访问控件时出错: ${i.message}</span><br/>`}l>0?t.value+=`<span class="log-item success">已清除${l}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},mt=async()=>{try{const n=Object.values(Z).map(o=>o.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const o of n)await xe(o)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},_t=()=>{Rs(async()=>{try{const o=window.Application.PluginStorage.getItem("user_info");if(!o)throw new Error("未找到用户信息");const l=JSON.parse(o);if(!l.orgs||!l.orgs[0])throw new Error("未找到组织信息");C.appKey=l.appKey,C.userName=l.nickname,C.userId=l.userId,C.appSecret=l.appSecret}catch(o){t.value+=`<span class="log-item error">初始化appKey失败: ${o.message}</span><br/>`}await se(),Ht([L,W],async()=>{await H()},{immediate:!1}),await j();const n=Ne.onVersionChange(()=>{const o=N();te.splice(0,te.length,...o),Ne.isSeniorEdition()&&W.value==="junior"?W.value="senior":!Ne.isSeniorEdition()&&W.value==="senior"&&(W.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Ne.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',M(),await et(),$e(),bt(),tt(),window.addEventListener("beforeunload",mt),()=>{S&&clearTimeout(S),n&&n()}}),Ht(t,n=>{S&&clearTimeout(S),S=setTimeout(()=>{ye(n)},10)},{immediate:!1})},bt=()=>{Q.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==L.value&&(L.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${L.value}</span><br/>`),n.data.stage!==W.value&&(W.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${W.value}</span><br/>`))})},lt=pe(!1),ct=()=>{try{const n=B(),o=n.ActiveWindow.Selection;if(!o||!n||!n.ContentControls)return null;const l=o.Range,v=[];for(let i=1;i<=n.ContentControls.Count;i++)try{const h=n.ContentControls.Item(i);if(!h)continue;const c=(h.Title||"").trim(),P=h.Range;if(l.Start<P.End&&l.End>P.Start){let A=null,_=!1,R=!1;if(!c)R=!0,A=`empty_${h.ID||Date.now()}`;else if(c.startsWith("任务_")||c.startsWith("任务增强_")||c.startsWith("校对_")||c.startsWith("已完成_")||c.startsWith("已完成增强_")||c.startsWith("已完成校对_")||c.startsWith("异常_")||c.startsWith("异常增强_")||c.startsWith("异常校对_")||c.startsWith("已停止_")||c.startsWith("已停止增强_")||c.startsWith("已停止校对_"))c.startsWith("任务增强_")?(A=c.substring(5),_=!0):c.startsWith("任务_")||c.startsWith("校对_")?A=c.substring(3):c.startsWith("已完成增强_")?(A=c.substring(6),_=!0):c.startsWith("已完成校对_")?A=c.substring(6):c.startsWith("已完成_")?A=c.substring(4):c.startsWith("异常增强_")?(A=c.substring(5),_=!0):c.startsWith("异常校对_")?A=c.substring(5):c.startsWith("异常_")?A=c.substring(3):c.startsWith("已停止增强_")?(A=c.substring(6),_=!0):c.startsWith("已停止校对_")?A=c.substring(6):c.startsWith("已停止_")&&(A=c.substring(4));else continue;if(A){let z;l.Start>=P.Start&&l.End<=P.End?z="completely_within":l.Start<=P.Start&&l.End>=P.End?z="completely_contains":z="partial_overlap",v.push({taskId:A,control:h,task:R?null:a[A]||null,isEnhanced:_,isEmptyTitle:R,overlapType:z,controlRange:{start:P.Start,end:P.End},selectionRange:{start:l.Start,end:l.End}})}}}catch{continue}return v.length===0?null:{primary:v[0],all:v}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},x=()=>{lt.value=!0},F=()=>{lt.value=!1},V=async(n,o=!1)=>{x();try{await be(n,o)}finally{F()}},w=n=>new Promise((o,l)=>{D.show=!0,D.message=n,D.resolveCallback=o,D.rejectCallback=l}),u=n=>{D.show=!1,n&&D.resolveCallback?D.resolveCallback(!0):D.resolveCallback&&D.resolveCallback(!1),D.resolveCallback=null,D.rejectCallback=null},j=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await Q.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(L.value=n.data.subject),n.data.stage&&(W.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${L.value})和年级(${W.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},H=async()=>{try{if(L.value||W.value){t.value+=`<span class="log-item info">正在保存学科(${L.value})和年级(${W.value})设置到服务器...</span><br/>`;const n=await Q.setSubjectAndStage(L.value,W.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}},fe=async()=>{try{I.value=2,q.value=I.value===2,q.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${I.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${I.value}）</span><br/>`}catch(n){console.error("检查企业ID失败:",n),t.value+=`<span class="log-item error">检查企业ID失败: ${n.message}</span><br/>`,q.value=!1}},ie=(n,o)=>{try{const l=B();if(!l||!n)return!1;console.log("批注",n,o);const v=l.Comments.Add(n,o);return!0}catch(l){return t.value+=`<span class="log-item error">为范围添加批注失败: ${l.message}</span><br/>`,!1}},ve=async(n,o)=>{try{if(!n||!Array.isArray(n))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const l=B();let v=null,i=null;if(l&&l.ContentControls)for(let A=1;A<=l.ContentControls.Count;A++)try{const _=l.ContentControls.Item(A);if((_==null?void 0:_.Title)===`校对_${o}`){v=_,i=_.Range,t.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!i){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const A=a[o];if(A&&A.selectedText)try{const _=l.Range().Find;if(_.ClearFormatting(),_.Text=A.selectedText.substring(0,Math.min(A.selectedText.length,100)),_.Forward=!0,_.Wrap=1,_.Execute()){const R=_.Parent;if(A.selectedText.length>100){const z=R.Start+A.selectedText.length;i=l.Range(R.Start,Math.min(z,l.Range().End))}else i=R;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(_){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${_.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let h=0,c=0,P=[];for(const A of n){if(!A.mode1||!Array.isArray(A.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const _ of A.mode1){if(!_.error_info||!Array.isArray(_.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const R=_.quest_html||"",z=_.quest_type||"";t.value+=`<span class="log-item info">处理${z}题目，发现${_.error_info.length}个错误信息</span><br/>`;for(const ee of _.error_info)try{let ae="";if(ee.error_info&&(ae+=`【错误类型】${ee.error_info}`),ee.fix_info&&(ae+=`\r
【修正建议】${ee.fix_info}`),ee.keywords&&ee.keywords.trim()){let ce=v?v.Range:i;re(ae,ee.keywords.trim(),-1,v.Range)?(h++,t.value+=`<span class="log-item success">已为关键词"${ee.keywords.trim()}"添加批注</span><br/>`):(P.push({comment:ae,keyword:ee.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${ee.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else P.push({comment:ae,keyword:null})}catch(ae){c++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${ae.message}</span><br/>`}}}if(P.length>0){t.value+=`<span class="log-item info">为整个控件范围添加${P.length}个批注</span><br/>`;for(const A of P)try{let _=A.comment,R=v?v.Range:i;v.LockContents=!1,ie(v.Range,_)?(h++,t.value+=`<span class="log-item success">已为整个范围添加批注${A.keyword?`（关键词：${A.keyword}）`:""}</span><br/>`):c++}catch(_){c++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${_.message}</span><br/>`}}return h>0?(t.value+=`<span class="log-item success">校对任务${o.substring(0,8)}处理完成：成功添加${h}个批注</span><br/>`,c>0&&(t.value+=`<span class="log-item warning">校对任务${o.substring(0,8)}：${c}个批注添加失败</span><br/>`),!0):(t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(l){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${l.message}</span><br/>`,!1}};return{docName:e,selected:s,logger:t,map:a,watchedDir:y,subject:L,stage:W,subjectOptions:G,stageOptions:te,fetchWatchedDir:se,clearLog:d,getCurrentDocument:B,checkDocumentFormat:O,getTaskStatusClass:oe,getTaskStatusText:p,getElapsedTime:b,terminateTask:k,stopTaskWithoutRemovingControl:m,run1:Me,run2:st,runCheck:Oe,getHeaderStatusClass:We,getRunningTasksCount:Je,getCompletedTasksCount:nt,getReleasableTasksCount:ot,getErrorTasksCount:Ye,setupLifecycle:_t,navigateToTaskControl:je,forceCleanAllTasks:it,ws:Fe,wsMessages:we,initWebSocket:$e,handleWatcherEvent:He,urlMonitorTasks:Z,monitorUrlForTask:Ae,stopUrlMonitoring:xe,getUrlMonitorStatus:Pe,forceUrlCheck:le,cleanupUrlMonitoringTasks:mt,tryRemoveTaskPlaceholder:be,isLoading:lt,isSelectionInTaskControl:ct,tryRemoveTaskPlaceholderWithLoading:V,showConfirm:w,handleConfirm:u,confirmDialog:D,loadSubjectAndStage:j,saveSubjectAndStage:H,enterpriseId:I,isCheckingVisible:q,checkEnterpriseAndSetCheckingVisibility:fe,processCheckingJson:ve}}const xn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Ne.getVersionConfig(),selectedEdition:Ne.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),r=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${r}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Q.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Q[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Q.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Q.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Q.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Q.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Q.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Q.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Q.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Ne.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await an()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Q.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Q.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Q.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Q.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Q.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Q.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Q.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Ne.onVersionChange(e=>{this.versionConfig=Ne.getVersionConfig(),this.selectedEdition=Ne.getEdition()}),await Q.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},Tn={class:"file-watcher"},Cn={class:"settings-modal"},kn={class:"modal-content"},$n={class:"modal-header"},Sn={class:"version-tag"},En={key:0,class:"user-info"},_n={class:"header-actions"},An={class:"modal-body"},Mn={class:"status-section"},Dn={class:"status-item"},Pn={key:0,class:"status-item"},On={class:"directory-section"},In={class:"directory-form"},Rn={class:"radio-group"},Un={class:"radio-item"},Ln={class:"radio-item"},Fn={class:"radio-item"},jn={key:0,class:"radio-item"},Wn={key:1,class:"directory-section"},Bn={class:"directory-form"},Nn={class:"form-group"},Vn=["placeholder"],Hn=["disabled"],zn={key:2,class:"directory-section"},qn={class:"directory-form"},Jn={class:"form-group"},Yn=["placeholder"],Kn=["disabled"],Xn={key:3,class:"directory-section"},Gn={class:"directory-form"},Zn={class:"form-group"},Qn=["placeholder"],ea=["disabled"],ta={key:4,class:"events-section"},sa={class:"events-list"},na={class:"event-time"},aa={class:"event-message"},ra={key:1,class:"modal-footer"};function oa(e,s,t,a,r,g){var T,S,y,C,D,L,W,I,q,G,N,te,se,J,d,B,O,re,oe,p,b;return K(),X("div",Tn,[f("div",Cn,[f("div",kn,[f("div",$n,[f("h3",null,[rn(de(r.versionConfig.shortName)+"设置 ",1),f("span",Sn,de(r.versionConfig.appVersion),1),g.userInfo?(K(),X("span",En,"欢迎您，"+de(g.userInfo.nickname),1)):ne("",!0)]),f("div",_n,[f("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>g.handleLogout&&g.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[f("span",{class:"icon-logout"},null,-1)])),f("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),f("div",An,[f("div",Mn,[f("div",Dn,[s[22]||(s[22]=f("span",{class:"label"},"状态：",-1)),f("span",{class:Ie(["status-badge",r.status.status])},de(r.status.status==="running"?"运行中":"已停止"),3)]),((y=(S=(T=g.userInfo)==null?void 0:T.orgs)==null?void 0:S[0])==null?void 0:y.orgId)===2?(K(),X("div",Pn,[s[23]||(s[23]=f("span",{class:"label"},"本次上传：",-1)),f("span",null,de(r.status.processedFiles||0)+" 个文件",1)])):ne("",!0),f("div",On,[s[28]||(s[28]=f("h4",null,"保存方式设置",-1)),f("div",In,[f("div",Rn,[f("label",Un,[Be(f("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>r.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Wt,r.currentSaveMethod]]),s[24]||(s[24]=f("span",{class:"radio-label"},"方式一",-1))]),f("label",Ln,[Be(f("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>r.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Wt,r.currentSaveMethod]]),s[25]||(s[25]=f("span",{class:"radio-label"},"方式二 (默认)",-1))]),f("label",Fn,[Be(f("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>r.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Wt,r.currentSaveMethod]]),s[26]||(s[26]=f("span",{class:"radio-label"},"方式三",-1))]),((L=(D=(C=g.userInfo)==null?void 0:C.orgs)==null?void 0:D[0])==null?void 0:L.orgId)===2?(K(),X("label",jn,[Be(f("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>r.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Wt,r.currentSaveMethod]]),s[27]||(s[27]=f("span",{class:"radio-label"},"方式四",-1))])):ne("",!0)]),r.saveMethodUpdateMessage?(K(),X("div",{key:0,class:Ie(["update-message",r.saveMethodUpdateSuccess?"success":"error"])},de(r.saveMethodUpdateMessage),3)):ne("",!0)])])]),ne("",!0),((q=(I=(W=g.userInfo)==null?void 0:W.orgs)==null?void 0:I[0])==null?void 0:q.orgId)===2?(K(),X("div",Wn,[s[32]||(s[32]=f("h4",null,"上传目录设置",-1)),f("div",Bn,[f("div",Nn,[s[31]||(s[31]=f("span",{class:"label"},"路径：",-1)),Be(f("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>r.newWatchDir=m),placeholder:r.status.watchDir||"C:\\Temp"},null,8,Vn),[[Kt,r.newWatchDir]]),f("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>g.updateWatchDir&&g.updateWatchDir(...m)),disabled:r.isUpdating||!r.newWatchDir},de(r.isUpdating?"更新中...":"更新目录"),9,Hn)]),r.updateMessage?(K(),X("div",{key:0,class:Ie(["update-message",r.updateSuccess?"success":"error"])},de(r.updateMessage),3)):ne("",!0)])])):ne("",!0),((te=(N=(G=g.userInfo)==null?void 0:G.orgs)==null?void 0:N[0])==null?void 0:te.orgId)===2?(K(),X("div",zn,[s[34]||(s[34]=f("h4",null,"下载目录设置",-1)),f("div",qn,[f("div",Jn,[s[33]||(s[33]=f("span",{class:"label"},"路径：",-1)),Be(f("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>r.newDownloadPath=m),placeholder:r.downloadPath||"C:\\Temp\\Downloads"},null,8,Yn),[[Kt,r.newDownloadPath]]),f("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>g.updateDownloadPath&&g.updateDownloadPath(...m)),disabled:r.isUpdatingDownloadPath||!r.newDownloadPath},de(r.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Kn)]),r.downloadPathUpdateMessage?(K(),X("div",{key:0,class:Ie(["update-message",r.downloadPathUpdateSuccess?"success":"error"])},de(r.downloadPathUpdateMessage),3)):ne("",!0)])])):ne("",!0),((d=(J=(se=g.userInfo)==null?void 0:se.orgs)==null?void 0:J[0])==null?void 0:d.orgId)===2?(K(),X("div",Xn,[s[36]||(s[36]=f("h4",null,"配置目录设置",-1)),f("div",Gn,[f("div",Zn,[s[35]||(s[35]=f("span",{class:"label"},"路径：",-1)),Be(f("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>r.newAddonConfigPath=m),placeholder:r.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,Qn),[[Kt,r.newAddonConfigPath]]),f("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...m)),disabled:r.isUpdatingAddonConfigPath||!r.newAddonConfigPath},de(r.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,ea)]),r.addonConfigPathUpdateMessage?(K(),X("div",{key:0,class:Ie(["update-message",r.addonConfigPathUpdateSuccess?"success":"error"])},de(r.addonConfigPathUpdateMessage),3)):ne("",!0)])])):ne("",!0),((re=(O=(B=g.userInfo)==null?void 0:B.orgs)==null?void 0:O[0])==null?void 0:re.orgId)===2?(K(),X("div",ta,[s[37]||(s[37]=f("h4",null,"最近事件",-1)),f("div",sa,[(K(!0),X(Mt,null,Dt(r.recentEvents,(m,k)=>(K(),X("div",{key:k,class:"event-item"},[f("span",na,de(g.formatTime(m.timestamp)),1),f("span",{class:Ie(["event-type",m.eventType])},de(g.getEventTypeText(m.eventType)),3),f("span",aa,de(g.getEventMessage(m)),1)]))),128))])])):ne("",!0)]),ne("",!0),((b=(p=(oe=g.userInfo)==null?void 0:oe.orgs)==null?void 0:p[0])==null?void 0:b.orgId)===2?(K(),X("div",ra,[f("button",{class:Ie(["control-btn",r.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>g.controlService&&g.controlService(...m))},de(r.status.status==="running"?"停止服务":"启动服务"),3)])):ne("",!0)])])])}const ia=Us(xn,[["render",oa],["__scopeId","data-v-48242008"]]);var Se="top",Ue="bottom",Le="right",Ee="left",rs="auto",Ft=[Se,Ue,Le,Ee],kt="start",Ut="end",la="clippingParents",Fs="viewport",At="popper",ca="reference",ms=Ft.reduce(function(e,s){return e.concat([s+"-"+kt,s+"-"+Ut])},[]),js=[].concat(Ft,[rs]).reduce(function(e,s){return e.concat([s,s+"-"+kt,s+"-"+Ut])},[]),ua="beforeRead",pa="read",da="afterRead",fa="beforeMain",ha="main",va="afterMain",ga="beforeWrite",ma="write",ba="afterWrite",wa=[ua,pa,da,fa,ha,va,ga,ma,ba];function Ze(e){return e?(e.nodeName||"").toLowerCase():null}function De(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function gt(e){var s=De(e).Element;return e instanceof s||e instanceof Element}function Re(e){var s=De(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function os(e){if(typeof ShadowRoot>"u")return!1;var s=De(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function ya(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},r=s.attributes[t]||{},g=s.elements[t];!Re(g)||!Ze(g)||(Object.assign(g.style,a),Object.keys(r).forEach(function(T){var S=r[T];S===!1?g.removeAttribute(T):g.setAttribute(T,S===!0?"":S)}))})}function xa(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var r=s.elements[a],g=s.attributes[a]||{},T=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),S=T.reduce(function(y,C){return y[C]="",y},{});!Re(r)||!Ze(r)||(Object.assign(r.style,S),Object.keys(g).forEach(function(y){r.removeAttribute(y)}))})}}const Ws={name:"applyStyles",enabled:!0,phase:"write",fn:ya,effect:xa,requires:["computeStyles"]};function Ge(e){return e.split("-")[0]}var ht=Math.max,zt=Math.min,$t=Math.round;function ts(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Bs(){return!/^((?!chrome|android).)*safari/i.test(ts())}function St(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),r=1,g=1;s&&Re(e)&&(r=e.offsetWidth>0&&$t(a.width)/e.offsetWidth||1,g=e.offsetHeight>0&&$t(a.height)/e.offsetHeight||1);var T=gt(e)?De(e):window,S=T.visualViewport,y=!Bs()&&t,C=(a.left+(y&&S?S.offsetLeft:0))/r,D=(a.top+(y&&S?S.offsetTop:0))/g,L=a.width/r,W=a.height/g;return{width:L,height:W,top:D,right:C+L,bottom:D+W,left:C,x:C,y:D}}function is(e){var s=St(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function Ns(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&os(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function rt(e){return De(e).getComputedStyle(e)}function Ta(e){return["table","td","th"].indexOf(Ze(e))>=0}function pt(e){return((gt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Jt(e){return Ze(e)==="html"?e:e.assignedSlot||e.parentNode||(os(e)?e.host:null)||pt(e)}function bs(e){return!Re(e)||rt(e).position==="fixed"?null:e.offsetParent}function Ca(e){var s=/firefox/i.test(ts()),t=/Trident/i.test(ts());if(t&&Re(e)){var a=rt(e);if(a.position==="fixed")return null}var r=Jt(e);for(os(r)&&(r=r.host);Re(r)&&["html","body"].indexOf(Ze(r))<0;){var g=rt(r);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return r;r=r.parentNode}return null}function jt(e){for(var s=De(e),t=bs(e);t&&Ta(t)&&rt(t).position==="static";)t=bs(t);return t&&(Ze(t)==="html"||Ze(t)==="body"&&rt(t).position==="static")?s:t||Ca(e)||s}function ls(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ot(e,s,t){return ht(e,zt(s,t))}function ka(e,s,t){var a=Ot(e,s,t);return a>t?t:a}function Vs(){return{top:0,right:0,bottom:0,left:0}}function Hs(e){return Object.assign({},Vs(),e)}function zs(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var $a=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Hs(typeof s!="number"?s:zs(s,Ft))};function Sa(e){var s,t=e.state,a=e.name,r=e.options,g=t.elements.arrow,T=t.modifiersData.popperOffsets,S=Ge(t.placement),y=ls(S),C=[Ee,Le].indexOf(S)>=0,D=C?"height":"width";if(!(!g||!T)){var L=$a(r.padding,t),W=is(g),I=y==="y"?Se:Ee,q=y==="y"?Ue:Le,G=t.rects.reference[D]+t.rects.reference[y]-T[y]-t.rects.popper[D],N=T[y]-t.rects.reference[y],te=jt(g),se=te?y==="y"?te.clientHeight||0:te.clientWidth||0:0,J=G/2-N/2,d=L[I],B=se-W[D]-L[q],O=se/2-W[D]/2+J,re=Ot(d,O,B),oe=y;t.modifiersData[a]=(s={},s[oe]=re,s.centerOffset=re-O,s)}}function Ea(e){var s=e.state,t=e.options,a=t.element,r=a===void 0?"[data-popper-arrow]":a;r!=null&&(typeof r=="string"&&(r=s.elements.popper.querySelector(r),!r)||Ns(s.elements.popper,r)&&(s.elements.arrow=r))}const _a={name:"arrow",enabled:!0,phase:"main",fn:Sa,effect:Ea,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Et(e){return e.split("-")[1]}var Aa={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ma(e,s){var t=e.x,a=e.y,r=s.devicePixelRatio||1;return{x:$t(t*r)/r||0,y:$t(a*r)/r||0}}function ws(e){var s,t=e.popper,a=e.popperRect,r=e.placement,g=e.variation,T=e.offsets,S=e.position,y=e.gpuAcceleration,C=e.adaptive,D=e.roundOffsets,L=e.isFixed,W=T.x,I=W===void 0?0:W,q=T.y,G=q===void 0?0:q,N=typeof D=="function"?D({x:I,y:G}):{x:I,y:G};I=N.x,G=N.y;var te=T.hasOwnProperty("x"),se=T.hasOwnProperty("y"),J=Ee,d=Se,B=window;if(C){var O=jt(t),re="clientHeight",oe="clientWidth";if(O===De(t)&&(O=pt(t),rt(O).position!=="static"&&S==="absolute"&&(re="scrollHeight",oe="scrollWidth")),O=O,r===Se||(r===Ee||r===Le)&&g===Ut){d=Ue;var p=L&&O===B&&B.visualViewport?B.visualViewport.height:O[re];G-=p-a.height,G*=y?1:-1}if(r===Ee||(r===Se||r===Ue)&&g===Ut){J=Le;var b=L&&O===B&&B.visualViewport?B.visualViewport.width:O[oe];I-=b-a.width,I*=y?1:-1}}var m=Object.assign({position:S},C&&Aa),k=D===!0?Ma({x:I,y:G},De(t)):{x:I,y:G};if(I=k.x,G=k.y,y){var $;return Object.assign({},m,($={},$[d]=se?"0":"",$[J]=te?"0":"",$.transform=(B.devicePixelRatio||1)<=1?"translate("+I+"px, "+G+"px)":"translate3d("+I+"px, "+G+"px, 0)",$))}return Object.assign({},m,(s={},s[d]=se?G+"px":"",s[J]=te?I+"px":"",s.transform="",s))}function Da(e){var s=e.state,t=e.options,a=t.gpuAcceleration,r=a===void 0?!0:a,g=t.adaptive,T=g===void 0?!0:g,S=t.roundOffsets,y=S===void 0?!0:S,C={placement:Ge(s.placement),variation:Et(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:r,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,ws(Object.assign({},C,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:T,roundOffsets:y})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,ws(Object.assign({},C,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:y})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Pa={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Da,data:{}};var Bt={passive:!0};function Oa(e){var s=e.state,t=e.instance,a=e.options,r=a.scroll,g=r===void 0?!0:r,T=a.resize,S=T===void 0?!0:T,y=De(s.elements.popper),C=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&C.forEach(function(D){D.addEventListener("scroll",t.update,Bt)}),S&&y.addEventListener("resize",t.update,Bt),function(){g&&C.forEach(function(D){D.removeEventListener("scroll",t.update,Bt)}),S&&y.removeEventListener("resize",t.update,Bt)}}const Ia={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Oa,data:{}};var Ra={left:"right",right:"left",bottom:"top",top:"bottom"};function Vt(e){return e.replace(/left|right|bottom|top/g,function(s){return Ra[s]})}var Ua={start:"end",end:"start"};function ys(e){return e.replace(/start|end/g,function(s){return Ua[s]})}function cs(e){var s=De(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function us(e){return St(pt(e)).left+cs(e).scrollLeft}function La(e,s){var t=De(e),a=pt(e),r=t.visualViewport,g=a.clientWidth,T=a.clientHeight,S=0,y=0;if(r){g=r.width,T=r.height;var C=Bs();(C||!C&&s==="fixed")&&(S=r.offsetLeft,y=r.offsetTop)}return{width:g,height:T,x:S+us(e),y}}function Fa(e){var s,t=pt(e),a=cs(e),r=(s=e.ownerDocument)==null?void 0:s.body,g=ht(t.scrollWidth,t.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),T=ht(t.scrollHeight,t.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),S=-a.scrollLeft+us(e),y=-a.scrollTop;return rt(r||t).direction==="rtl"&&(S+=ht(t.clientWidth,r?r.clientWidth:0)-g),{width:g,height:T,x:S,y}}function ps(e){var s=rt(e),t=s.overflow,a=s.overflowX,r=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+r+a)}function qs(e){return["html","body","#document"].indexOf(Ze(e))>=0?e.ownerDocument.body:Re(e)&&ps(e)?e:qs(Jt(e))}function It(e,s){var t;s===void 0&&(s=[]);var a=qs(e),r=a===((t=e.ownerDocument)==null?void 0:t.body),g=De(a),T=r?[g].concat(g.visualViewport||[],ps(a)?a:[]):a,S=s.concat(T);return r?S:S.concat(It(Jt(T)))}function ss(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ja(e,s){var t=St(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function xs(e,s,t){return s===Fs?ss(La(e,t)):gt(s)?ja(s,t):ss(Fa(pt(e)))}function Wa(e){var s=It(Jt(e)),t=["absolute","fixed"].indexOf(rt(e).position)>=0,a=t&&Re(e)?jt(e):e;return gt(a)?s.filter(function(r){return gt(r)&&Ns(r,a)&&Ze(r)!=="body"}):[]}function Ba(e,s,t,a){var r=s==="clippingParents"?Wa(e):[].concat(s),g=[].concat(r,[t]),T=g[0],S=g.reduce(function(y,C){var D=xs(e,C,a);return y.top=ht(D.top,y.top),y.right=zt(D.right,y.right),y.bottom=zt(D.bottom,y.bottom),y.left=ht(D.left,y.left),y},xs(e,T,a));return S.width=S.right-S.left,S.height=S.bottom-S.top,S.x=S.left,S.y=S.top,S}function Js(e){var s=e.reference,t=e.element,a=e.placement,r=a?Ge(a):null,g=a?Et(a):null,T=s.x+s.width/2-t.width/2,S=s.y+s.height/2-t.height/2,y;switch(r){case Se:y={x:T,y:s.y-t.height};break;case Ue:y={x:T,y:s.y+s.height};break;case Le:y={x:s.x+s.width,y:S};break;case Ee:y={x:s.x-t.width,y:S};break;default:y={x:s.x,y:s.y}}var C=r?ls(r):null;if(C!=null){var D=C==="y"?"height":"width";switch(g){case kt:y[C]=y[C]-(s[D]/2-t[D]/2);break;case Ut:y[C]=y[C]+(s[D]/2-t[D]/2);break}}return y}function Lt(e,s){s===void 0&&(s={});var t=s,a=t.placement,r=a===void 0?e.placement:a,g=t.strategy,T=g===void 0?e.strategy:g,S=t.boundary,y=S===void 0?la:S,C=t.rootBoundary,D=C===void 0?Fs:C,L=t.elementContext,W=L===void 0?At:L,I=t.altBoundary,q=I===void 0?!1:I,G=t.padding,N=G===void 0?0:G,te=Hs(typeof N!="number"?N:zs(N,Ft)),se=W===At?ca:At,J=e.rects.popper,d=e.elements[q?se:W],B=Ba(gt(d)?d:d.contextElement||pt(e.elements.popper),y,D,T),O=St(e.elements.reference),re=Js({reference:O,element:J,strategy:"absolute",placement:r}),oe=ss(Object.assign({},J,re)),p=W===At?oe:O,b={top:B.top-p.top+te.top,bottom:p.bottom-B.bottom+te.bottom,left:B.left-p.left+te.left,right:p.right-B.right+te.right},m=e.modifiersData.offset;if(W===At&&m){var k=m[r];Object.keys(b).forEach(function($){var M=[Le,Ue].indexOf($)>=0?1:-1,U=[Se,Ue].indexOf($)>=0?"y":"x";b[$]+=k[U]*M})}return b}function Na(e,s){s===void 0&&(s={});var t=s,a=t.placement,r=t.boundary,g=t.rootBoundary,T=t.padding,S=t.flipVariations,y=t.allowedAutoPlacements,C=y===void 0?js:y,D=Et(a),L=D?S?ms:ms.filter(function(q){return Et(q)===D}):Ft,W=L.filter(function(q){return C.indexOf(q)>=0});W.length===0&&(W=L);var I=W.reduce(function(q,G){return q[G]=Lt(e,{placement:G,boundary:r,rootBoundary:g,padding:T})[Ge(G)],q},{});return Object.keys(I).sort(function(q,G){return I[q]-I[G]})}function Va(e){if(Ge(e)===rs)return[];var s=Vt(e);return[ys(e),s,ys(s)]}function Ha(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var r=t.mainAxis,g=r===void 0?!0:r,T=t.altAxis,S=T===void 0?!0:T,y=t.fallbackPlacements,C=t.padding,D=t.boundary,L=t.rootBoundary,W=t.altBoundary,I=t.flipVariations,q=I===void 0?!0:I,G=t.allowedAutoPlacements,N=s.options.placement,te=Ge(N),se=te===N,J=y||(se||!q?[Vt(N)]:Va(N)),d=[N].concat(J).reduce(function(_e,ye){return _e.concat(Ge(ye)===rs?Na(s,{placement:ye,boundary:D,rootBoundary:L,padding:C,flipVariations:q,allowedAutoPlacements:G}):ye)},[]),B=s.rects.reference,O=s.rects.popper,re=new Map,oe=!0,p=d[0],b=0;b<d.length;b++){var m=d[b],k=Ge(m),$=Et(m)===kt,M=[Se,Ue].indexOf(k)>=0,U=M?"width":"height",E=Lt(s,{placement:m,boundary:D,rootBoundary:L,altBoundary:W,padding:C}),Y=M?$?Le:Ee:$?Ue:Se;B[U]>O[U]&&(Y=Vt(Y));var he=Vt(Y),me=[];if(g&&me.push(E[k]<=0),S&&me.push(E[Y]<=0,E[he]<=0),me.every(function(_e){return _e})){p=m,oe=!1;break}re.set(m,me)}if(oe)for(var Ce=q?3:1,Fe=function(ye){var $e=d.find(function(Z){var Ae=re.get(Z);if(Ae)return Ae.slice(0,ye).every(function(xe){return xe})});if($e)return p=$e,"break"},we=Ce;we>0;we--){var ke=Fe(we);if(ke==="break")break}s.placement!==p&&(s.modifiersData[a]._skip=!0,s.placement=p,s.reset=!0)}}const za={name:"flip",enabled:!0,phase:"main",fn:Ha,requiresIfExists:["offset"],data:{_skip:!1}};function Ts(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function Cs(e){return[Se,Le,Ue,Ee].some(function(s){return e[s]>=0})}function qa(e){var s=e.state,t=e.name,a=s.rects.reference,r=s.rects.popper,g=s.modifiersData.preventOverflow,T=Lt(s,{elementContext:"reference"}),S=Lt(s,{altBoundary:!0}),y=Ts(T,a),C=Ts(S,r,g),D=Cs(y),L=Cs(C);s.modifiersData[t]={referenceClippingOffsets:y,popperEscapeOffsets:C,isReferenceHidden:D,hasPopperEscaped:L},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":D,"data-popper-escaped":L})}const Ja={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:qa};function Ya(e,s,t){var a=Ge(e),r=[Ee,Se].indexOf(a)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,T=g[0],S=g[1];return T=T||0,S=(S||0)*r,[Ee,Le].indexOf(a)>=0?{x:S,y:T}:{x:T,y:S}}function Ka(e){var s=e.state,t=e.options,a=e.name,r=t.offset,g=r===void 0?[0,0]:r,T=js.reduce(function(D,L){return D[L]=Ya(L,s.rects,g),D},{}),S=T[s.placement],y=S.x,C=S.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=y,s.modifiersData.popperOffsets.y+=C),s.modifiersData[a]=T}const Xa={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Ka};function Ga(e){var s=e.state,t=e.name;s.modifiersData[t]=Js({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const Za={name:"popperOffsets",enabled:!0,phase:"read",fn:Ga,data:{}};function Qa(e){return e==="x"?"y":"x"}function er(e){var s=e.state,t=e.options,a=e.name,r=t.mainAxis,g=r===void 0?!0:r,T=t.altAxis,S=T===void 0?!1:T,y=t.boundary,C=t.rootBoundary,D=t.altBoundary,L=t.padding,W=t.tether,I=W===void 0?!0:W,q=t.tetherOffset,G=q===void 0?0:q,N=Lt(s,{boundary:y,rootBoundary:C,padding:L,altBoundary:D}),te=Ge(s.placement),se=Et(s.placement),J=!se,d=ls(te),B=Qa(d),O=s.modifiersData.popperOffsets,re=s.rects.reference,oe=s.rects.popper,p=typeof G=="function"?G(Object.assign({},s.rects,{placement:s.placement})):G,b=typeof p=="number"?{mainAxis:p,altAxis:p}:Object.assign({mainAxis:0,altAxis:0},p),m=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,k={x:0,y:0};if(O){if(g){var $,M=d==="y"?Se:Ee,U=d==="y"?Ue:Le,E=d==="y"?"height":"width",Y=O[d],he=Y+N[M],me=Y-N[U],Ce=I?-oe[E]/2:0,Fe=se===kt?re[E]:oe[E],we=se===kt?-oe[E]:-re[E],ke=s.elements.arrow,_e=I&&ke?is(ke):{width:0,height:0},ye=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Vs(),$e=ye[M],Z=ye[U],Ae=Ot(0,re[E],_e[E]),xe=J?re[E]/2-Ce-Ae-$e-b.mainAxis:Fe-Ae-$e-b.mainAxis,Pe=J?-re[E]/2+Ce+Ae+Z+b.mainAxis:we+Ae+Z+b.mainAxis,le=s.elements.arrow&&jt(s.elements.arrow),He=le?d==="y"?le.clientTop||0:le.clientLeft||0:0,ze=($=m==null?void 0:m[d])!=null?$:0,Qe=Y+xe-ze-He,et=Y+Pe-ze,be=Ot(I?zt(he,Qe):he,Y,I?ht(me,et):me);O[d]=be,k[d]=be-Y}if(S){var je,qe=d==="x"?Se:Ee,tt=d==="x"?Ue:Le,Me=O[B],Oe=B==="y"?"height":"width",st=Me+N[qe],We=Me-N[tt],Je=[Se,Ee].indexOf(te)!==-1,nt=(je=m==null?void 0:m[B])!=null?je:0,ot=Je?st:Me-re[Oe]-oe[Oe]-nt+b.altAxis,Ye=Je?Me+re[Oe]+oe[Oe]-nt-b.altAxis:We,it=I&&Je?ka(ot,Me,Ye):Ot(I?ot:st,Me,I?Ye:We);O[B]=it,k[B]=it-Me}s.modifiersData[a]=k}}const tr={name:"preventOverflow",enabled:!0,phase:"main",fn:er,requiresIfExists:["offset"]};function sr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function nr(e){return e===De(e)||!Re(e)?cs(e):sr(e)}function ar(e){var s=e.getBoundingClientRect(),t=$t(s.width)/e.offsetWidth||1,a=$t(s.height)/e.offsetHeight||1;return t!==1||a!==1}function rr(e,s,t){t===void 0&&(t=!1);var a=Re(s),r=Re(s)&&ar(s),g=pt(s),T=St(e,r,t),S={scrollLeft:0,scrollTop:0},y={x:0,y:0};return(a||!a&&!t)&&((Ze(s)!=="body"||ps(g))&&(S=nr(s)),Re(s)?(y=St(s,!0),y.x+=s.clientLeft,y.y+=s.clientTop):g&&(y.x=us(g))),{x:T.left+S.scrollLeft-y.x,y:T.top+S.scrollTop-y.y,width:T.width,height:T.height}}function or(e){var s=new Map,t=new Set,a=[];e.forEach(function(g){s.set(g.name,g)});function r(g){t.add(g.name);var T=[].concat(g.requires||[],g.requiresIfExists||[]);T.forEach(function(S){if(!t.has(S)){var y=s.get(S);y&&r(y)}}),a.push(g)}return e.forEach(function(g){t.has(g.name)||r(g)}),a}function ir(e){var s=or(e);return wa.reduce(function(t,a){return t.concat(s.filter(function(r){return r.phase===a}))},[])}function lr(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function cr(e){var s=e.reduce(function(t,a){var r=t[a.name];return t[a.name]=r?Object.assign({},r,a,{options:Object.assign({},r.options,a.options),data:Object.assign({},r.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var ks={placement:"bottom",modifiers:[],strategy:"absolute"};function $s(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function ur(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,r=s.defaultOptions,g=r===void 0?ks:r;return function(S,y,C){C===void 0&&(C=g);var D={placement:"bottom",orderedModifiers:[],options:Object.assign({},ks,g),modifiersData:{},elements:{reference:S,popper:y},attributes:{},styles:{}},L=[],W=!1,I={state:D,setOptions:function(te){var se=typeof te=="function"?te(D.options):te;G(),D.options=Object.assign({},g,D.options,se),D.scrollParents={reference:gt(S)?It(S):S.contextElement?It(S.contextElement):[],popper:It(y)};var J=ir(cr([].concat(a,D.options.modifiers)));return D.orderedModifiers=J.filter(function(d){return d.enabled}),q(),I.update()},forceUpdate:function(){if(!W){var te=D.elements,se=te.reference,J=te.popper;if($s(se,J)){D.rects={reference:rr(se,jt(J),D.options.strategy==="fixed"),popper:is(J)},D.reset=!1,D.placement=D.options.placement,D.orderedModifiers.forEach(function(b){return D.modifiersData[b.name]=Object.assign({},b.data)});for(var d=0;d<D.orderedModifiers.length;d++){if(D.reset===!0){D.reset=!1,d=-1;continue}var B=D.orderedModifiers[d],O=B.fn,re=B.options,oe=re===void 0?{}:re,p=B.name;typeof O=="function"&&(D=O({state:D,options:oe,name:p,instance:I})||D)}}}},update:lr(function(){return new Promise(function(N){I.forceUpdate(),N(D)})}),destroy:function(){G(),W=!0}};if(!$s(S,y))return I;I.setOptions(C).then(function(N){!W&&C.onFirstUpdate&&C.onFirstUpdate(N)});function q(){D.orderedModifiers.forEach(function(N){var te=N.name,se=N.options,J=se===void 0?{}:se,d=N.effect;if(typeof d=="function"){var B=d({state:D,name:te,instance:I,options:J}),O=function(){};L.push(B||O)}})}function G(){L.forEach(function(N){return N()}),L=[]}return I}}var pr=[Ia,Za,Pa,Ws,Xa,za,tr,_a,Ja],dr=ur({defaultModifiers:pr}),fr="tippy-box",Ys="tippy-content",hr="tippy-backdrop",Ks="tippy-arrow",Xs="tippy-svg-arrow",ft={passive:!0,capture:!0},Gs=function(){return document.body};function Gt(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function ds(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function Zs(e,s){return typeof e=="function"?e.apply(void 0,s):e}function Ss(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function vr(e){return e.split(/\s+/).filter(Boolean)}function Ct(e){return[].concat(e)}function Es(e,s){e.indexOf(s)===-1&&e.push(s)}function gr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function mr(e){return e.split("-")[0]}function qt(e){return[].slice.call(e)}function _s(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Rt(){return document.createElement("div")}function Yt(e){return["Element","Fragment"].some(function(s){return ds(e,s)})}function br(e){return ds(e,"NodeList")}function wr(e){return ds(e,"MouseEvent")}function yr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function xr(e){return Yt(e)?[e]:br(e)?qt(e):Array.isArray(e)?e:qt(document.querySelectorAll(e))}function Zt(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function As(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function Tr(e){var s,t=Ct(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function Cr(e,s){var t=s.clientX,a=s.clientY;return e.every(function(r){var g=r.popperRect,T=r.popperState,S=r.props,y=S.interactiveBorder,C=mr(T.placement),D=T.modifiersData.offset;if(!D)return!0;var L=C==="bottom"?D.top.y:0,W=C==="top"?D.bottom.y:0,I=C==="right"?D.left.x:0,q=C==="left"?D.right.x:0,G=g.top-a+L>y,N=a-g.bottom-W>y,te=g.left-t+I>y,se=t-g.right-q>y;return G||N||te||se})}function Qt(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(r){e[a](r,t)})}function Ms(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var Xe={isTouch:!1},Ds=0;function kr(){Xe.isTouch||(Xe.isTouch=!0,window.performance&&document.addEventListener("mousemove",Qs))}function Qs(){var e=performance.now();e-Ds<20&&(Xe.isTouch=!1,document.removeEventListener("mousemove",Qs)),Ds=e}function $r(){var e=document.activeElement;if(yr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function Sr(){document.addEventListener("touchstart",kr,ft),window.addEventListener("blur",$r)}var Er=typeof window<"u"&&typeof document<"u",_r=Er?!!window.msCrypto:!1,Ar={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Mr={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Ve=Object.assign({appendTo:Gs,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Ar,Mr),Dr=Object.keys(Ve),Pr=function(s){var t=Object.keys(s);t.forEach(function(a){Ve[a]=s[a]})};function en(e){var s=e.plugins||[],t=s.reduce(function(a,r){var g=r.name,T=r.defaultValue;if(g){var S;a[g]=e[g]!==void 0?e[g]:(S=Ve[g])!=null?S:T}return a},{});return Object.assign({},e,t)}function Or(e,s){var t=s?Object.keys(en(Object.assign({},Ve,{plugins:s}))):Dr,a=t.reduce(function(r,g){var T=(e.getAttribute("data-tippy-"+g)||"").trim();if(!T)return r;if(g==="content")r[g]=T;else try{r[g]=JSON.parse(T)}catch{r[g]=T}return r},{});return a}function Ps(e,s){var t=Object.assign({},s,{content:Zs(s.content,[e])},s.ignoreAttributes?{}:Or(e,s.plugins));return t.aria=Object.assign({},Ve.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Ir=function(){return"innerHTML"};function ns(e,s){e[Ir()]=s}function Os(e){var s=Rt();return e===!0?s.className=Ks:(s.className=Xs,Yt(e)?s.appendChild(e):ns(s,e)),s}function Is(e,s){Yt(s.content)?(ns(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?ns(e,s.content):e.textContent=s.content)}function as(e){var s=e.firstElementChild,t=qt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(Ys)}),arrow:t.find(function(a){return a.classList.contains(Ks)||a.classList.contains(Xs)}),backdrop:t.find(function(a){return a.classList.contains(hr)})}}function tn(e){var s=Rt(),t=Rt();t.className=fr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=Rt();a.className=Ys,a.setAttribute("data-state","hidden"),Is(a,e.props),s.appendChild(t),t.appendChild(a),r(e.props,e.props);function r(g,T){var S=as(s),y=S.box,C=S.content,D=S.arrow;T.theme?y.setAttribute("data-theme",T.theme):y.removeAttribute("data-theme"),typeof T.animation=="string"?y.setAttribute("data-animation",T.animation):y.removeAttribute("data-animation"),T.inertia?y.setAttribute("data-inertia",""):y.removeAttribute("data-inertia"),y.style.maxWidth=typeof T.maxWidth=="number"?T.maxWidth+"px":T.maxWidth,T.role?y.setAttribute("role",T.role):y.removeAttribute("role"),(g.content!==T.content||g.allowHTML!==T.allowHTML)&&Is(C,e.props),T.arrow?D?g.arrow!==T.arrow&&(y.removeChild(D),y.appendChild(Os(T.arrow))):y.appendChild(Os(T.arrow)):D&&y.removeChild(D)}return{popper:s,onUpdate:r}}tn.$$tippy=!0;var Rr=1,Nt=[],es=[];function Ur(e,s){var t=Ps(e,Object.assign({},Ve,en(_s(s)))),a,r,g,T=!1,S=!1,y=!1,C=!1,D,L,W,I=[],q=Ss(Qe,t.interactiveDebounce),G,N=Rr++,te=null,se=gr(t.plugins),J={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},d={id:N,reference:e,popper:Rt(),popperInstance:te,props:t,state:J,plugins:se,clearDelayTimeouts:ot,setProps:Ye,setContent:it,show:mt,hide:_t,hideWithInteractivity:bt,enable:Je,disable:nt,unmount:lt,destroy:ct};if(!t.render)return d;var B=t.render(d),O=B.popper,re=B.onUpdate;O.setAttribute("data-tippy-root",""),O.id="tippy-"+d.id,d.popper=O,e._tippy=d,O._tippy=d;var oe=se.map(function(x){return x.fn(d)}),p=e.hasAttribute("aria-expanded");return le(),Ce(),Y(),he("onCreate",[d]),t.showOnCreate&&st(),O.addEventListener("mouseenter",function(){d.props.interactive&&d.state.isVisible&&d.clearDelayTimeouts()}),O.addEventListener("mouseleave",function(){d.props.interactive&&d.props.trigger.indexOf("mouseenter")>=0&&M().addEventListener("mousemove",q)}),d;function b(){var x=d.props.touch;return Array.isArray(x)?x:[x,0]}function m(){return b()[0]==="hold"}function k(){var x;return!!((x=d.props.render)!=null&&x.$$tippy)}function $(){return G||e}function M(){var x=$().parentNode;return x?Tr(x):document}function U(){return as(O)}function E(x){return d.state.isMounted&&!d.state.isVisible||Xe.isTouch||D&&D.type==="focus"?0:Gt(d.props.delay,x?0:1,Ve.delay)}function Y(x){x===void 0&&(x=!1),O.style.pointerEvents=d.props.interactive&&!x?"":"none",O.style.zIndex=""+d.props.zIndex}function he(x,F,V){if(V===void 0&&(V=!0),oe.forEach(function(u){u[x]&&u[x].apply(u,F)}),V){var w;(w=d.props)[x].apply(w,F)}}function me(){var x=d.props.aria;if(x.content){var F="aria-"+x.content,V=O.id,w=Ct(d.props.triggerTarget||e);w.forEach(function(u){var j=u.getAttribute(F);if(d.state.isVisible)u.setAttribute(F,j?j+" "+V:V);else{var H=j&&j.replace(V,"").trim();H?u.setAttribute(F,H):u.removeAttribute(F)}})}}function Ce(){if(!(p||!d.props.aria.expanded)){var x=Ct(d.props.triggerTarget||e);x.forEach(function(F){d.props.interactive?F.setAttribute("aria-expanded",d.state.isVisible&&F===$()?"true":"false"):F.removeAttribute("aria-expanded")})}}function Fe(){M().removeEventListener("mousemove",q),Nt=Nt.filter(function(x){return x!==q})}function we(x){if(!(Xe.isTouch&&(y||x.type==="mousedown"))){var F=x.composedPath&&x.composedPath()[0]||x.target;if(!(d.props.interactive&&Ms(O,F))){if(Ct(d.props.triggerTarget||e).some(function(V){return Ms(V,F)})){if(Xe.isTouch||d.state.isVisible&&d.props.trigger.indexOf("click")>=0)return}else he("onClickOutside",[d,x]);d.props.hideOnClick===!0&&(d.clearDelayTimeouts(),d.hide(),S=!0,setTimeout(function(){S=!1}),d.state.isMounted||$e())}}}function ke(){y=!0}function _e(){y=!1}function ye(){var x=M();x.addEventListener("mousedown",we,!0),x.addEventListener("touchend",we,ft),x.addEventListener("touchstart",_e,ft),x.addEventListener("touchmove",ke,ft)}function $e(){var x=M();x.removeEventListener("mousedown",we,!0),x.removeEventListener("touchend",we,ft),x.removeEventListener("touchstart",_e,ft),x.removeEventListener("touchmove",ke,ft)}function Z(x,F){xe(x,function(){!d.state.isVisible&&O.parentNode&&O.parentNode.contains(O)&&F()})}function Ae(x,F){xe(x,F)}function xe(x,F){var V=U().box;function w(u){u.target===V&&(Qt(V,"remove",w),F())}if(x===0)return F();Qt(V,"remove",L),Qt(V,"add",w),L=w}function Pe(x,F,V){V===void 0&&(V=!1);var w=Ct(d.props.triggerTarget||e);w.forEach(function(u){u.addEventListener(x,F,V),I.push({node:u,eventType:x,handler:F,options:V})})}function le(){m()&&(Pe("touchstart",ze,{passive:!0}),Pe("touchend",et,{passive:!0})),vr(d.props.trigger).forEach(function(x){if(x!=="manual")switch(Pe(x,ze),x){case"mouseenter":Pe("mouseleave",et);break;case"focus":Pe(_r?"focusout":"blur",be);break;case"focusin":Pe("focusout",be);break}})}function He(){I.forEach(function(x){var F=x.node,V=x.eventType,w=x.handler,u=x.options;F.removeEventListener(V,w,u)}),I=[]}function ze(x){var F,V=!1;if(!(!d.state.isEnabled||je(x)||S)){var w=((F=D)==null?void 0:F.type)==="focus";D=x,G=x.currentTarget,Ce(),!d.state.isVisible&&wr(x)&&Nt.forEach(function(u){return u(x)}),x.type==="click"&&(d.props.trigger.indexOf("mouseenter")<0||T)&&d.props.hideOnClick!==!1&&d.state.isVisible?V=!0:st(x),x.type==="click"&&(T=!V),V&&!w&&We(x)}}function Qe(x){var F=x.target,V=$().contains(F)||O.contains(F);if(!(x.type==="mousemove"&&V)){var w=Oe().concat(O).map(function(u){var j,H=u._tippy,fe=(j=H.popperInstance)==null?void 0:j.state;return fe?{popperRect:u.getBoundingClientRect(),popperState:fe,props:t}:null}).filter(Boolean);Cr(w,x)&&(Fe(),We(x))}}function et(x){var F=je(x)||d.props.trigger.indexOf("click")>=0&&T;if(!F){if(d.props.interactive){d.hideWithInteractivity(x);return}We(x)}}function be(x){d.props.trigger.indexOf("focusin")<0&&x.target!==$()||d.props.interactive&&x.relatedTarget&&O.contains(x.relatedTarget)||We(x)}function je(x){return Xe.isTouch?m()!==x.type.indexOf("touch")>=0:!1}function qe(){tt();var x=d.props,F=x.popperOptions,V=x.placement,w=x.offset,u=x.getReferenceClientRect,j=x.moveTransition,H=k()?as(O).arrow:null,fe=u?{getBoundingClientRect:u,contextElement:u.contextElement||$()}:e,ie={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(o){var l=o.state;if(k()){var v=U(),i=v.box;["placement","reference-hidden","escaped"].forEach(function(h){h==="placement"?i.setAttribute("data-placement",l.placement):l.attributes.popper["data-popper-"+h]?i.setAttribute("data-"+h,""):i.removeAttribute("data-"+h)}),l.attributes.popper={}}}},ve=[{name:"offset",options:{offset:w}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!j}},ie];k()&&H&&ve.push({name:"arrow",options:{element:H,padding:3}}),ve.push.apply(ve,(F==null?void 0:F.modifiers)||[]),d.popperInstance=dr(fe,O,Object.assign({},F,{placement:V,onFirstUpdate:W,modifiers:ve}))}function tt(){d.popperInstance&&(d.popperInstance.destroy(),d.popperInstance=null)}function Me(){var x=d.props.appendTo,F,V=$();d.props.interactive&&x===Gs||x==="parent"?F=V.parentNode:F=Zs(x,[V]),F.contains(O)||F.appendChild(O),d.state.isMounted=!0,qe()}function Oe(){return qt(O.querySelectorAll("[data-tippy-root]"))}function st(x){d.clearDelayTimeouts(),x&&he("onTrigger",[d,x]),ye();var F=E(!0),V=b(),w=V[0],u=V[1];Xe.isTouch&&w==="hold"&&u&&(F=u),F?a=setTimeout(function(){d.show()},F):d.show()}function We(x){if(d.clearDelayTimeouts(),he("onUntrigger",[d,x]),!d.state.isVisible){$e();return}if(!(d.props.trigger.indexOf("mouseenter")>=0&&d.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(x.type)>=0&&T)){var F=E(!1);F?r=setTimeout(function(){d.state.isVisible&&d.hide()},F):g=requestAnimationFrame(function(){d.hide()})}}function Je(){d.state.isEnabled=!0}function nt(){d.hide(),d.state.isEnabled=!1}function ot(){clearTimeout(a),clearTimeout(r),cancelAnimationFrame(g)}function Ye(x){if(!d.state.isDestroyed){he("onBeforeUpdate",[d,x]),He();var F=d.props,V=Ps(e,Object.assign({},F,_s(x),{ignoreAttributes:!0}));d.props=V,le(),F.interactiveDebounce!==V.interactiveDebounce&&(Fe(),q=Ss(Qe,V.interactiveDebounce)),F.triggerTarget&&!V.triggerTarget?Ct(F.triggerTarget).forEach(function(w){w.removeAttribute("aria-expanded")}):V.triggerTarget&&e.removeAttribute("aria-expanded"),Ce(),Y(),re&&re(F,V),d.popperInstance&&(qe(),Oe().forEach(function(w){requestAnimationFrame(w._tippy.popperInstance.forceUpdate)})),he("onAfterUpdate",[d,x])}}function it(x){d.setProps({content:x})}function mt(){var x=d.state.isVisible,F=d.state.isDestroyed,V=!d.state.isEnabled,w=Xe.isTouch&&!d.props.touch,u=Gt(d.props.duration,0,Ve.duration);if(!(x||F||V||w)&&!$().hasAttribute("disabled")&&(he("onShow",[d],!1),d.props.onShow(d)!==!1)){if(d.state.isVisible=!0,k()&&(O.style.visibility="visible"),Y(),ye(),d.state.isMounted||(O.style.transition="none"),k()){var j=U(),H=j.box,fe=j.content;Zt([H,fe],0)}W=function(){var ve;if(!(!d.state.isVisible||C)){if(C=!0,O.offsetHeight,O.style.transition=d.props.moveTransition,k()&&d.props.animation){var n=U(),o=n.box,l=n.content;Zt([o,l],u),As([o,l],"visible")}me(),Ce(),Es(es,d),(ve=d.popperInstance)==null||ve.forceUpdate(),he("onMount",[d]),d.props.animation&&k()&&Ae(u,function(){d.state.isShown=!0,he("onShown",[d])})}},Me()}}function _t(){var x=!d.state.isVisible,F=d.state.isDestroyed,V=!d.state.isEnabled,w=Gt(d.props.duration,1,Ve.duration);if(!(x||F||V)&&(he("onHide",[d],!1),d.props.onHide(d)!==!1)){if(d.state.isVisible=!1,d.state.isShown=!1,C=!1,T=!1,k()&&(O.style.visibility="hidden"),Fe(),$e(),Y(!0),k()){var u=U(),j=u.box,H=u.content;d.props.animation&&(Zt([j,H],w),As([j,H],"hidden"))}me(),Ce(),d.props.animation?k()&&Z(w,d.unmount):d.unmount()}}function bt(x){M().addEventListener("mousemove",q),Es(Nt,q),q(x)}function lt(){d.state.isVisible&&d.hide(),d.state.isMounted&&(tt(),Oe().forEach(function(x){x._tippy.unmount()}),O.parentNode&&O.parentNode.removeChild(O),es=es.filter(function(x){return x!==d}),d.state.isMounted=!1,he("onHidden",[d]))}function ct(){d.state.isDestroyed||(d.clearDelayTimeouts(),d.unmount(),He(),delete e._tippy,d.state.isDestroyed=!0,he("onDestroy",[d]))}}function vt(e,s){s===void 0&&(s={});var t=Ve.plugins.concat(s.plugins||[]);Sr();var a=Object.assign({},s,{plugins:t}),r=xr(e),g=r.reduce(function(T,S){var y=S&&Ur(S,a);return y&&T.push(y),T},[]);return Yt(e)?g[0]:g}vt.defaultProps=Ve;vt.setDefaultProps=Pr;vt.currentInput=Xe;Object.assign({},Ws,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});vt.setDefaultProps({render:tn});const Lr={class:"task-pane"},Fr={key:0,class:"loading-overlay"},jr={key:1,class:"format-error-overlay"},Wr={class:"format-error-content"},Br={class:"format-error-message"},Nr={class:"format-error-actions"},Vr={class:"doc-header"},Hr={class:"doc-title"},zr={class:"action-area"},qr={class:"select-container"},Jr={class:"select-group"},Yr=["disabled"],Kr=["value"],Xr={class:"select-group"},Gr=["disabled"],Zr=["value"],Qr=["title"],eo={key:0,class:"science-warning"},to={class:"action-buttons"},so=["disabled"],no={class:"btn-content"},ao={key:0,class:"button-loader"},ro=["disabled"],oo={class:"btn-content"},io={key:0,class:"button-loader"},lo=["disabled"],co={class:"btn-content"},uo={key:0,class:"button-loader"},po={class:"content-area"},fo={class:"modal-header"},ho={class:"modal-body"},vo={class:"selection-content"},go={class:"modal-header"},mo={class:"modal-body"},bo={class:"alert-message"},wo={class:"alert-actions"},yo={key:2,class:"modal-overlay"},xo={class:"modal-header"},To={class:"modal-body"},Co={class:"confirm-message"},ko={class:"confirm-actions"},$o={class:"task-queue"},So={class:"queue-header"},Eo={class:"queue-status-filter"},_o=["value"],Ao={class:"queue-actions"},Mo=["disabled","title"],Do={class:"task-count"},Po={key:0,class:"queue-table-container"},Oo={class:"col-id"},Io={class:"id-header"},Ro={key:0,class:"col-subject"},Uo={class:"subject-header"},Lo={class:"switch"},Fo=["title"],jo={key:1,class:"col-status"},Wo=["onClick"],Bo={class:"col-id"},No={class:"id-content"},Vo={class:"task-id"},Ho={key:0,class:"enhance-svg-icon"},zo={key:0,class:"status-in-id"},qo={key:0,class:"col-subject"},Jo=["onMouseenter"],Yo={key:1,class:"col-status"},Ko={class:"status-cell"},Xo={class:"col-actions"},Go={class:"task-actions"},Zo=["onClick"],Qo=["onClick"],ei={key:2,class:"no-action-icon",title:"无可用操作"},ti={key:1,class:"empty-queue"},si={key:3,class:"log-container"},ni={class:"log-actions"},ai={class:"toggle-icon"},ri=["innerHTML"],oi={__name:"TaskPane",setup(e){const s=pe(!1),t=pe(!1),a=pe(!1),r=pe(""),g=pe(!1),T=pe(!1),S=pe(!1),y=pe(!1),C=pe(!0),D=pe(""),L=pe(!1),W=pe(window.innerWidth),I=yt(()=>W.value<750),q=yt(()=>W.value<380),G=()=>{W.value=window.innerWidth},N=pe(null),te=pe(!1),se=pe(""),J={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},d=pe(!1),B=pe([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"}]),{docName:O,selected:re,logger:oe,map:p,subject:b,stage:m,subjectOptions:k,stageOptions:$,appConfig:M,clearLog:U,checkDocumentFormat:E,getTaskStatusClass:Y,getTaskStatusText:he,terminateTask:me,run1:Ce,runCheck:Fe,setupLifecycle:we,navigateToTaskControl:ke,isLoading:_e,tryRemoveTaskPlaceholderWithLoading:ye,confirmDialog:$e,handleConfirm:Z,getCompletedTasksCount:Ae,getReleasableTasksCount:xe,showConfirm:Pe}=yn(),le=pe(null);(()=>{var w;try{if((w=window.Application)!=null&&w.PluginStorage){const u=window.Application.PluginStorage.getItem("user_info");u?(le.value=JSON.parse(u),console.log("用户信息已加载:",le.value)):console.log("未找到用户信息")}}catch(u){console.error("解析用户信息时出错:",u)}})(),Ht(le,w=>{w&&w.orgs&&w.orgs[0]&&console.log(`用户企业ID: ${w.orgs[0].orgId}, 校对功能${w.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const ze=yt(()=>!le.value||le.value.isAdmin||le.value.isOwner?k:le.value.subject?k.filter(w=>w.value===le.value.subject):k),Qe=()=>{le.value&&!le.value.isAdmin&&!le.value.isOwner&&le.value.subject&&(b.value=le.value.subject)},et=yt(()=>["physics","chemistry","biology","math"].includes(b.value));Ht(M,w=>{w&&(console.log("TaskPane组件收到应用配置更新:",w),console.log("当前版本类型:",w.EDITION),console.log("当前年级选项:",$.value))},{deep:!0,immediate:!0});const be=()=>{try{const w=E();C.value=w.isValid,D.value=w.message,L.value=!w.isValid,w.isValid||console.warn("文档格式检查失败:",w.message)}catch(w){console.error("执行文档格式检查时出错:",w),C.value=!1,D.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",L.value=!0}},je=yt(()=>{let w={};const u=p;if(se.value==="")w={...u};else for(const j in u)if(Object.prototype.hasOwnProperty.call(u,j)){const H=u[j];H.status===se.value&&(w[j]=H)}return te.value&&N.value?w[N.value]?{[N.value]:w[N.value]}:{}:w}),qe=yt(()=>{const w=je.value;return Object.entries(w).map(([j,H])=>({tid:j,...H})).sort((j,H)=>{const fe=j.startTime||0;return(H.startTime||0)-fe}).reduce((j,H)=>{const{tid:fe,...ie}=H;return j[fe]=ie,j},{})}),tt=(w="wps-analysis")=>{b.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(r.value="未选中内容",t.value=!0):(w==="wps-analysis"?T.value=!0:w==="wps-enhance_analysis"&&(S.value=!0),Ce(w).catch(u=>{console.log(u),u.message.includes("重叠")?(r.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",u),r.value=u.message,t.value=!0)}).finally(()=>{w==="wps-analysis"?T.value=!1:w==="wps-enhance_analysis"&&(S.value=!1)})):(r.value="请选择学科",t.value=!0)},Me=()=>{b.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(r.value="未选中内容",t.value=!0):(y.value=!0,Fe().catch(w=>{console.log(w),w.message.includes("重叠")?(r.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",w),r.value=w.message,t.value=!0)}).finally(()=>{y.value=!1})):(r.value="请选择学科",t.value=!0)},Oe=(w,u)=>{N.value=w,ke(w)},st=w=>{p[w]&&(p[w].status=3),N.value===w&&(N.value=null),ye(w,!0)},We=async()=>{const w=Object.entries(p).filter(([u,j])=>j.status===2||j.status===4);if(w.length===0){r.value="没有可释放的任务",t.value=!0;return}try{if(await Pe(`确定要释放所有 ${w.length} 个可释放的任务吗？
此操作不可撤销。`)){let j=0;w.forEach(([H,fe])=>{p[H]&&(p[H].status=3,N.value===H&&(N.value=null),ye(H,!0),j++)}),r.value=`已成功释放 ${j} 个任务`,t.value=!0}}catch(u){console.error("释放任务时出错:",u),r.value="释放任务时出现错误",t.value=!0}},Je=()=>{g.value=!g.value},nt=w=>w?w.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",ot=w=>{const u=Ye(w);return u?nt(u):"无题目内容"},Ye=w=>{if(!w.selectedText)return"";const u=w.selectedText.split("\r").filter(H=>H.trim());if(u.length===1){const H=w.selectedText.split(`
`).filter(fe=>fe.trim());H.length>1&&u.splice(0,1,...H)}const j=u.map((H,fe)=>{const ie=H.trim();return ie.length>200,ie});return j.length===1?u[0].trim():j.join(`
`)},it=(w,u)=>{if(!d.value)return;const j=w.target,H=Ye(u).toString();if(!H||H.trim()===""){console.log("题目内容为空，不显示tooltip");return}const fe=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${H.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,ie=w.clientX,ve=w.clientY;if(J.subjects.has(j)){const o=J.subjects.get(j);o.setContent(fe),o.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ve,bottom:ve,left:ie,right:ie})}),o.show();return}const n=vt(j,{content:fe,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:ve,bottom:ve,left:ie,right:ie}),onHidden:()=>{n.setProps({getReferenceClientRect:null})}});J.subjects.set(j,n),n.show()},mt=w=>{const u=w.currentTarget,j=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,H=w.clientX,fe=w.clientY;if(J.enhance.has(u)){const ve=J.enhance.get(u);ve.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:H,right:H})}),ve.show();return}const ie=vt(u,{content:j,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});J.enhance.set(u,ie),ie.show()},_t=()=>{J.subjects.forEach(w=>{w.destroy()}),J.subjects.clear(),J.enhance.forEach(w=>{w.destroy()}),J.enhance.clear(),J.softBreak.forEach(w=>{w.destroy()}),J.softBreak.clear(),document.removeEventListener("click",bt),document.removeEventListener("mousemove",ct)},bt=w=>{const u=document.querySelector(".tippy-box");u&&!u.contains(w.target)&&(J.subjects.forEach(j=>j.hide()),J.enhance.forEach(j=>j.hide()),J.softBreak.forEach(j=>j.hide()))};let lt=0;const ct=w=>{const u=Date.now();if(u-lt<100)return;lt=u;const j=document.querySelector(".tippy-box");if(!j)return;const H=j.getBoundingClientRect();!(w.clientX>=H.left-20&&w.clientX<=H.right+20&&w.clientY>=H.top-20&&w.clientY<=H.bottom+20)&&!j.matches(":hover")&&(J.subjects.forEach(ie=>ie.hide()),J.enhance.forEach(ie=>ie.hide()),J.softBreak.forEach(ie=>ie.hide()))},x=()=>{document.addEventListener("click",bt),document.addEventListener("mousemove",ct)};Rs(()=>{window.addEventListener("resize",G),x(),Qe(),setTimeout(()=>{be()},500);const w=document.createElement("style");w.id="tippy-custom-styles",w.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(w)}),on(()=>{window.removeEventListener("resize",G),_t();const w=document.getElementById("tippy-custom-styles");w&&w.remove()}),we();const F=w=>w.selectedText?w.selectedText.includes("\v")||w.selectedText.includes("\v"):!1,V=w=>{const u=w.currentTarget,j=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,H=w.clientX,fe=w.clientY;if(J.softBreak.has(u)){const ve=J.softBreak.get(u);ve.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:H,right:H})}),ve.show();return}const ie=vt(u,{content:j,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});J.softBreak.set(u,ie),ie.show()};return(w,u)=>{var j,H,fe,ie,ve;return K(),X("div",Lr,[ge(_e)?(K(),X("div",Fr,u[26]||(u[26]=[f("div",{class:"loading-spinner"},null,-1),f("div",{class:"loading-text"},"处理中...",-1)]))):ne("",!0),L.value?(K(),X("div",jr,[f("div",Wr,[u[27]||(u[27]=f("div",{class:"format-error-icon"},"⚠️",-1)),u[28]||(u[28]=f("div",{class:"format-error-title"},"文档格式不支持",-1)),f("div",Br,de(D.value),1),f("div",Nr,[f("button",{class:"retry-check-btn",onClick:u[0]||(u[0]=n=>be())},"重新检查")])])])):ne("",!0),f("div",Vr,[f("div",Hr,de(ge(O)||"未选择文档"),1),f("button",{class:"settings-btn",onClick:u[1]||(u[1]=n=>a.value=!0)},u[29]||(u[29]=[f("i",{class:"icon-settings"},null,-1)]))]),f("div",zr,[f("div",qr,[f("div",Jr,[u[30]||(u[30]=f("label",{for:"stage-select"},"年级:",-1)),Be(f("select",{id:"stage-select","onUpdate:modelValue":u[2]||(u[2]=n=>fs(m)?m.value=n:null),class:"select-input",disabled:L.value},[(K(!0),X(Mt,null,Dt(ge($),n=>(K(),X("option",{key:n.value,value:n.value},de(n.label),9,Kr))),128))],8,Yr),[[Xt,ge(m)]])]),f("div",Xr,[u[31]||(u[31]=f("label",{for:"subject-select"},"学科:",-1)),Be(f("select",{id:"subject-select","onUpdate:modelValue":u[3]||(u[3]=n=>fs(b)?b.value=n:null),class:"select-input",disabled:L.value},[(K(!0),X(Mt,null,Dt(ze.value,n=>(K(),X("option",{key:n.value,value:n.value},de(n.label),9,Zr))),128))],8,Gr),[[Xt,ge(b)]]),le.value&&!le.value.isAdmin&&!le.value.isOwner&&le.value.subject?(K(),X("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((j=ze.value.find(n=>n.value===le.value.subject))==null?void 0:j.label)||le.value.subject} 学科`}," 🔒 ",8,Qr)):ne("",!0)])]),et.value?(K(),X("div",eo," 理科可使用增强模式以获取更精准的解析 ")):ne("",!0),f("div",to,[f("button",{class:"action-btn primary",onClick:u[4]||(u[4]=n=>tt("wps-analysis")),disabled:T.value||L.value},[f("div",no,[T.value?(K(),X("span",ao)):ne("",!0),u[32]||(u[32]=f("span",{class:"btn-text"},"解析",-1))])],8,so),et.value?(K(),X("button",{key:0,class:"action-btn enhance",onClick:u[5]||(u[5]=n=>tt("wps-enhance_analysis")),disabled:S.value||L.value},[f("div",oo,[S.value?(K(),X("span",io)):ne("",!0),u[33]||(u[33]=f("span",{class:"btn-text"},"增强解析",-1))])],8,ro)):ne("",!0),((fe=(H=le.value)==null?void 0:H.orgs[0])==null?void 0:fe.orgId)===2?(K(),X("button",{key:1,class:"action-btn secondary",onClick:u[6]||(u[6]=n=>Me()),disabled:y.value||L.value},[f("div",co,[y.value?(K(),X("span",uo)):ne("",!0),u[34]||(u[34]=f("span",{class:"btn-text"},"校对",-1))])],8,lo)):ne("",!0)])]),f("div",po,[s.value?(K(),X("div",{key:0,class:"modal-overlay",onClick:u[9]||(u[9]=n=>s.value=!1)},[f("div",{class:"modal-content",onClick:u[8]||(u[8]=dt(()=>{},["stop"]))},[f("div",fo,[u[35]||(u[35]=f("div",{class:"modal-title"},"选中内容",-1)),f("button",{class:"modal-close",onClick:u[7]||(u[7]=n=>s.value=!1)},"×")]),f("div",ho,[f("pre",vo,de(ge(re)||"未选中内容"),1)])])])):ne("",!0),t.value?(K(),X("div",{key:1,class:"modal-overlay",onClick:u[13]||(u[13]=n=>t.value=!1)},[f("div",{class:"modal-content alert-modal",onClick:u[12]||(u[12]=dt(()=>{},["stop"]))},[f("div",go,[u[36]||(u[36]=f("div",{class:"modal-title"},"提示",-1)),f("button",{class:"modal-close",onClick:u[10]||(u[10]=n=>t.value=!1)},"×")]),f("div",mo,[f("div",bo,de(r.value),1),f("div",wo,[f("button",{class:"action-btn primary",onClick:u[11]||(u[11]=n=>t.value=!1)},"确定")])])])])):ne("",!0),ge($e).show?(K(),X("div",yo,[f("div",{class:"modal-content confirm-modal",onClick:u[17]||(u[17]=dt(()=>{},["stop"]))},[f("div",xo,[u[37]||(u[37]=f("div",{class:"modal-title"},"确认",-1)),f("button",{class:"modal-close",onClick:u[14]||(u[14]=n=>ge(Z)(!1))},"×")]),f("div",To,[f("div",Co,de(ge($e).message),1),f("div",ko,[f("button",{class:"action-btn secondary",onClick:u[15]||(u[15]=n=>ge(Z)(!1))},"取消"),f("button",{class:"action-btn primary",onClick:u[16]||(u[16]=n=>ge(Z)(!0))},"确定")])])])])):ne("",!0),f("div",$o,[f("div",So,[u[38]||(u[38]=f("div",{class:"queue-title"},"任务队列",-1)),f("div",Eo,[Be(f("select",{id:"status-filter-select","onUpdate:modelValue":u[18]||(u[18]=n=>se.value=n),class:"status-filter-select-input"},[(K(!0),X(Mt,null,Dt(B.value,n=>(K(),X("option",{key:n.value,value:n.value},de(n.label),9,_o))),128))],512),[[Xt,se.value]])]),f("div",Ao,[f("button",{class:"release-all-btn",onClick:We,disabled:ge(xe)()===0,title:ge(xe)()===0?"无可释放任务":`释放所有${ge(xe)()}个可释放任务`}," 一键释放 ",8,Mo)]),f("div",Do,de(Object.keys(je.value).length)+"个任务",1)]),Object.keys(je.value).length>0?(K(),X("div",Po,[f("table",{class:Ie(["queue-table",{"narrow-view":I.value,"ultra-narrow-view":q.value}])},[f("thead",null,[f("tr",null,[f("th",Oo,[f("div",Io,[u[40]||(u[40]=f("span",null,"任务ID",-1)),f("span",{class:"help-icon",onMouseenter:u[19]||(u[19]=n=>mt(n))},u[39]||(u[39]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),f("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),I.value?ne("",!0):(K(),X("th",Ro,[f("div",Uo,[u[41]||(u[41]=f("span",null,"题目内容",-1)),f("label",Lo,[Be(f("input",{type:"checkbox","onUpdate:modelValue":u[20]||(u[20]=n=>d.value=n)},null,512),[[ln,d.value]]),f("span",{class:"slider round",title:d.value?"关闭题目预览":"开启题目预览"},null,8,Fo)])])])),q.value?ne("",!0):(K(),X("th",jo,"状态")),u[42]||(u[42]=f("th",{class:"col-actions"},"操作",-1))])]),f("tbody",null,[(K(!0),X(Mt,null,Dt(qe.value,(n,o)=>(K(),X("tr",{key:o,class:Ie(["task-row",[ge(Y)(n.status),{"selected-task-row":o===N.value}]]),onClick:l=>Oe(o)},[f("td",Bo,[f("div",{class:Ie(["id-cell",{"id-with-status":q.value}])},[f("div",No,[f("span",Vo,de(o.substring(0,8)),1),n.wordType==="wps-enhance_analysis"||n.isEnhanced?(K(),X("span",Ho,u[43]||(u[43]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[f("title",null,"增强模式"),f("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):ne("",!0),F(n)?(K(),X("span",{key:1,class:"soft-break-warning-icon",onMouseenter:u[21]||(u[21]=l=>V(l))},u[44]||(u[44]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[f("title",null,"提示"),f("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),f("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),f("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):ne("",!0)]),q.value?(K(),X("div",zo,[f("span",{class:Ie(["task-tag compact",ge(Y)(n.status)])},de(ge(he)(n.status)),3)])):ne("",!0)],2)]),I.value?ne("",!0):(K(),X("td",qo,[f("div",{class:"subject-cell",onMouseenter:l=>it(l,n)},de(ot(n)),41,Jo)])),q.value?ne("",!0):(K(),X("td",Yo,[f("div",Ko,[f("span",{class:Ie(["task-tag",ge(Y)(n.status)])},de(ge(he)(n.status)),3)])])),f("td",Xo,[f("div",Go,[n.status===1?(K(),X("button",{key:0,onClick:dt(l=>ge(me)(o),["stop"]),class:"terminate-btn"}," 终止 ",8,Zo)):ne("",!0),n.status===2||n.status===4?(K(),X("button",{key:1,onClick:dt(l=>st(o),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Qo)):ne("",!0),n.status!==1&&n.status!==2&&n.status!==4?(K(),X("span",ei,u[45]||(u[45]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):ne("",!0)])])],10,Wo))),128))])],2)])):(K(),X("div",ti,u[46]||(u[46]=[f("div",{class:"empty-text"},"暂无任务",-1)])))]),((ve=(ie=le.value)==null?void 0:ie.orgs[0])==null?void 0:ve.orgId)===2?(K(),X("div",si,[f("div",{class:"log-header",onClick:Je},[u[47]||(u[47]=f("div",{class:"log-title"},"执行日志",-1)),f("div",ni,[f("button",{class:"clear-btn",onClick:u[22]||(u[22]=dt(n=>ge(U)(),["stop"]))},"清空日志"),f("span",ai,de(g.value?"▼":"▶"),1)])]),g.value?(K(),X("div",{key:0,class:"log-content",innerHTML:ge(oe)},null,8,ri)):ne("",!0)])):ne("",!0)]),a.value?(K(),X("div",{key:2,class:"modal-overlay",onClick:u[25]||(u[25]=n=>a.value=!1)},[f("div",{class:"modal-content",onClick:u[24]||(u[24]=dt(()=>{},["stop"]))},[cn(ia,{onClose:u[23]||(u[23]=n=>a.value=!1)})])])):ne("",!0)])}}},li=Us(oi,[["__scopeId","data-v-2cebf37d"]]);export{li as default};
