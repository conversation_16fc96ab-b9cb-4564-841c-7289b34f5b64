import logger from './logger';
import wsClient from './wsClient';

class VersionManager {
  constructor() {
    this.versionInfo = {
      edition: 'wanwei', // 默认万唯版本
      appVersion: '1.0.0',
      buildDate: new Date().toISOString(),
      environment: 'development',
      bypassEncryption: false
    };
    
    this.versionCallbacks = new Set();
    this.isInitialized = false;
    
    this.setupWebSocketListener();
  }

  /**
   * 设置WebSocket版本信息监听器
   */
  setupWebSocketListener() {
    wsClient.addEventListener('version', (data) => {
      if (data.action === 'info' && data.success && data.data) {
        this.updateVersionInfo(data.data);
      }
    });
  }

  /**
   * 更新版本信息
   * @param {Object} newVersionInfo - 新的版本信息
   */
  updateVersionInfo(newVersionInfo) {
    const oldVersionInfo = { ...this.versionInfo };
    
    // 更新版本信息
    Object.assign(this.versionInfo, newVersionInfo);
    
    this.isInitialized = true;
    
    logger.info('版本信息已更新', {
      old: oldVersionInfo,
      new: this.versionInfo
    });

    // 通知所有回调函数
    this.versionCallbacks.forEach(callback => {
      try {
        callback(this.versionInfo, oldVersionInfo);
      } catch (error) {
        logger.error('版本信息回调执行失败', error);
      }
    });
  }

  /**
   * 获取当前版本信息
   * @returns {Object} 版本信息对象
   */
  getVersionInfo() {
    return { ...this.versionInfo };
  }

  /**
   * 获取版本类型
   * @returns {string} 版本类型：'wanwei', 'hexin' 或 'wwsenior'
   */
  getEdition() {
    return this.versionInfo.edition;
  }

  /**
   * 检查是否为万唯版本
   * @returns {boolean}
   */
  isWanweiEdition() {
    return this.versionInfo.edition === 'wanwei';
  }

  /**
   * 检查是否为合心版本
   * @returns {boolean}
   */
  isHexinEdition() {
    return this.versionInfo.edition === 'hexin';
  }

  /**
   * 检查是否为万唯高中版本
   * @returns {boolean}
   */
  isSeniorEdition() {
    return this.versionInfo.edition === 'wwsenior';
  }

  /**
   * 获取版本相关的配置
   * @returns {Object} 版本配置对象
   */
  getVersionConfig() {
    const baseConfig = {
      edition: this.versionInfo.edition,
      appVersion: this.versionInfo.appVersion,
      environment: this.versionInfo.environment
    };

    if (this.isWanweiEdition()) {
      return {
        ...baseConfig,
        appName: '万唯AI编辑WPS插件服务',
        shortName: 'WPS插件',
        iconName: 'ww.png',
        primaryColor: '#1890ff',
      };
    } else if (this.isHexinEdition()) {
      return {
        ...baseConfig,
        appName: '合心AI编辑WPS插件服务',
        shortName: 'WPS插件',
        iconName: 'hexin.png',
        primaryColor: '#1890ff',
      };
    } else if (this.isSeniorEdition()) {
      return {
        ...baseConfig,
        appName: '万唯AI编辑WPS插件服务',
        shortName: 'WPS插件(高中)',
        iconName: 'ww.png',
        primaryColor: '#1890ff',
      };
    }

    // 默认返回万唯版本配置
    return baseConfig;
  }

  /**
   * 添加版本信息变更回调
   * @param {Function} callback - 回调函数，接收 (newVersionInfo, oldVersionInfo) 参数
   * @returns {Function} 用于移除回调的函数
   */
  onVersionChange(callback) {
    this.versionCallbacks.add(callback);

    // 如果已经初始化，立即执行一次回调
    if (this.isInitialized) {
      try {
        callback(this.versionInfo, null);
      } catch (error) {
        logger.error('版本信息回调执行失败', error);
      }
    }

    return () => {
      this.versionCallbacks.delete(callback);
    };
  }

  /**
   * 等待版本信息初始化完成
   * @param {number} timeout - 超时时间（毫秒），默认5000ms
   * @returns {Promise<Object>} 版本信息对象
   */
  waitForInitialization(timeout = 5000) {
    return new Promise((resolve, reject) => {
      if (this.isInitialized) {
        resolve(this.versionInfo);
        return;
      }

      let timeoutId = null;
      
      const removeCallback = this.onVersionChange((versionInfo) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        removeCallback();
        resolve(versionInfo);
      });

      timeoutId = setTimeout(() => {
        removeCallback();
        reject(new Error('版本信息初始化超时'));
      }, timeout);
    });
  }

  /**
   * 请求最新版本信息
   */
  requestVersionInfo() {
    if (wsClient.isConnected) {
      wsClient.sendMessage('version', 'getInfo', {});
    } else {
      logger.warn('WebSocket未连接，无法请求版本信息');
    }
  }

  /**
   * 设置版本类型（仅用于测试）
   * @param {string} edition - 版本类型：'wanwei', 'hexin' 或 'wwsenior'
   * @returns {Promise<Object>} 设置结果
   */
  setEdition(edition) {
    return new Promise((resolve, reject) => {
      if (!wsClient.isConnected) {
        reject(new Error('WebSocket未连接'));
        return;
      }

      if (!edition || (edition !== 'wanwei' && edition !== 'hexin' && edition !== 'wwsenior')) {
        reject(new Error('无效的版本类型，必须是 wanwei, hexin 或 wwsenior'));
        return;
      }

      wsClient.sendMessage('version', 'setEdition', { edition }, (response) => {
        if (response.success) {
          logger.success('版本类型设置成功', response);
          resolve(response);
        } else {
          logger.error('版本类型设置失败', response);
          reject(new Error(response.message || '设置失败'));
        }
      });
    });
  }
}

// 创建单例实例
const versionManager = new VersionManager();

export default versionManager; 