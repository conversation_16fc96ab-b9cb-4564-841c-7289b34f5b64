const { ipc<PERSON>enderer, contextBridge } = require('electron');

console.log('Preload script initialized');

// 暴露安全的API到window对象
contextBridge.exposeInMainWorld('ipcRenderer', {
  send: (channel, data) => {
    // 白名单channels
    const validChannels = [
      'login-attempt',
      'logout',
      'show-main-window',
      'minimize-window',
      'hide-window',
      'get-user-info',
      'start-wps',
      'open-devtools',
      'app-version',
      'app-config',
      'check-update',
      // 网络文件夹选择相关事件
      'select-network-folder',
      'confirm-network-folder',
      'skip-network-folder-config',
      // 网络连接相关事件
      'retry-network-connection',
      'skip-network-connection',
      // 机器码相关事件
      'get-machine-code',
      'refresh-machine-code',
      'verify-machine-code',
      // 新增：监控文件夹重设相关事件
      'get-current-monitor-dir',
      // 帮助弹窗相关事件
      'get-guest-login-help',
      'enable-guest-login-policy',
      'check-guest-login-policy',
      // 更新相关事件
      'download-update',
      'install-update',
      'cancel-update',
      'auto-check-update' // 添加自动检查更新的通道
    ];
    if (validChannels.includes(channel)) {
      console.log(`Sending IPC message: ${channel}`);
      ipcRenderer.send(channel, data);
    } else {
      console.error(`无效的IPC通道: ${channel}`);
    }
  },
  on: (channel, listener) => {
    // 白名单channels
    const validChannels = [
      'show-login',
      'login-success',
      'login-failure',
      'logout-success',
      'logout-failure',
      'app-version',
      'app-config',
      'user-info-response',
      // 权限检查相关事件
      'show-permission-check',
      'permission-check-progress',
      'permission-check-complete',
      'permission-check-failed',
      // 网络文件夹选择相关事件
      'need-network-folder-selection',
      'network-folder-selected',
      'network-folder-confirmed',
      // 网络连接相关事件
      'network-connection-required',
      'network-connection-retry-result',
      'network-connection-skip-result',
      // 机器码相关事件
      'machine-code-response',
      'machine-code-refresh-response',
      'machine-code-verify-response',
      // 新增：监控文件夹重设相关响应事件
      'current-monitor-dir-response',
      'monitor-folder-reset-response',
      // 帮助弹窗相关响应事件
      'guest-login-help-response',
      'guest-login-policy-enable-response',
      'guest-login-policy-response',
      // 更新相关响应事件
      'update-check-result',
      'update-download-progress',
      'update-download-complete',
      'update-error',
      // 启动失败相关事件
      'wps-start-error',
      'app-startup-error',
      'server-start-error'
    ];
    if (validChannels.includes(channel)) {
      console.log(`Registering IPC listener: ${channel}`);
      // 包装函数确保没有远程模块被注入
      const wrappedListener = (event, ...args) => listener(event, ...args);
      ipcRenderer.on(channel, wrappedListener);

      // 返回一个清理函数以允许删除监听器
      return () => {
        ipcRenderer.removeListener(channel, wrappedListener);
      };
    } else {
      console.error(`无效的IPC通道: ${channel}`);
    }
  },
  // 添加获取用户信息的同步方法
  sendSync: (channel, data) => {
    const validChannels = ['get-user-info-sync'];
    if (validChannels.includes(channel)) {
      console.log(`Sending Sync IPC message: ${channel}`);
      return ipcRenderer.sendSync(channel, data);
    } else {
      console.error(`无效的同步IPC通道: ${channel}`);
      return null;
    }
  }
});

// 记录preload加载完成
console.log('Preload script completed');
