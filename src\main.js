import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { refreshSession } from './components/js/auth'
import { notificationPlugin } from './plugins/notificationPlugin'

// 应用程序启动时刷新会话
refreshSession()
  .then((r) => {
    if (!r) {
      // 未登录
    }
  })
  .catch((error) => {
    console.error('Initial session refresh error:', error)
  })

const app = createApp(App)

app.use(router)

// 使用通知插件
app.use(notificationPlugin)

app.mount('#app')
