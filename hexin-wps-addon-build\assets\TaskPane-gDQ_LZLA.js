import{U as rn,r as ce,h as gt,v as Je,i as G,j as Rt,k as Fs,m as Jt,_ as js,n as ln,o as F,c as j,a as d,p as cn,t as Q,f as q,q as ke,w as qe,s as Vt,e as Zt,F as mt,u as _t,x as lt,y as un,z as ie,A as Qt,B as gs,C as ft,D as dn,E as pn}from"./index-jghZXNfg.js";function fn(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=rn.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const hn={onbuttonclick:fn};var vn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function gn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function mn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var c=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,c.get?c:{enumerable:!0,get:function(){return e[a]}})}),t}var Ws={exports:{}};const bn={},wn=Object.freeze(Object.defineProperty({__proto__:null,default:bn},Symbol.toStringTag,{value:"Module"})),ms=mn(wn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",c=a?window:{};c.JS_SHA1_NO_WINDOW&&(a=!1);var g=!a&&typeof self=="object",T=!c.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;T?c=vn:g&&(c=self);var M=!c.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,w=!c.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",C="0123456789abcdef".split(""),P=[-**********,8388608,32768,128],L=[24,16,8,0],V=["hex","array","digest","arrayBuffer"],I=[],B=Array.isArray;(c.JS_SHA1_NO_NODE_JS||!B)&&(B=function(v){return Object.prototype.toString.call(v)==="[object Array]"});var J=ArrayBuffer.isView;w&&(c.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!J)&&(J=function(v){return typeof v=="object"&&v.buffer&&v.buffer.constructor===ArrayBuffer});var N=function(v){var b=typeof v;if(b==="string")return[v,!0];if(b!=="object"||v===null)throw new Error(s);if(w&&v.constructor===ArrayBuffer)return[new Uint8Array(v),!1];if(!B(v)&&!J(v))throw new Error(s);return[v,!1]},ee=function(v){return function(b){return new D(!0).update(b)[v]()}},te=function(){var v=ee("hex");T&&(v=se(v)),v.create=function(){return new D},v.update=function(k){return v.create().update(k)};for(var b=0;b<V.length;++b){var x=V[b];v[x]=ee(x)}return v},se=function(v){var b=ms,x=ms.Buffer,k;x.from&&!c.JS_SHA1_NO_BUFFER_FROM?k=x.from:k=function(m){return new x(m)};var $=function(m){if(typeof m=="string")return b.createHash("sha1").update(m,"utf8").digest("hex");if(m==null)throw new Error(s);return m.constructor===ArrayBuffer&&(m=new Uint8Array(m)),B(m)||J(m)||m.constructor===x?b.createHash("sha1").update(k(m)).digest("hex"):v(m)};return $},f=function(v){return function(b,x){return new re(b,!0).update(x)[v]()}},Z=function(){var v=f("hex");v.create=function(k){return new re(k)},v.update=function(k,$){return v.create(k).update($)};for(var b=0;b<V.length;++b){var x=V[b];v[x]=f(x)}return v};function D(v){v?(I[0]=I[16]=I[1]=I[2]=I[3]=I[4]=I[5]=I[6]=I[7]=I[8]=I[9]=I[10]=I[11]=I[12]=I[13]=I[14]=I[15]=0,this.blocks=I):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}D.prototype.update=function(v){if(this.finalized)throw new Error(t);var b=N(v);v=b[0];for(var x=b[1],k,$=0,m,U=v.length||0,E=this.blocks;$<U;){if(this.hashed&&(this.hashed=!1,E[0]=this.block,this.block=E[16]=E[1]=E[2]=E[3]=E[4]=E[5]=E[6]=E[7]=E[8]=E[9]=E[10]=E[11]=E[12]=E[13]=E[14]=E[15]=0),x)for(m=this.start;$<U&&m<64;++$)k=v.charCodeAt($),k<128?E[m>>>2]|=k<<L[m++&3]:k<2048?(E[m>>>2]|=(192|k>>>6)<<L[m++&3],E[m>>>2]|=(128|k&63)<<L[m++&3]):k<55296||k>=57344?(E[m>>>2]|=(224|k>>>12)<<L[m++&3],E[m>>>2]|=(128|k>>>6&63)<<L[m++&3],E[m>>>2]|=(128|k&63)<<L[m++&3]):(k=65536+((k&1023)<<10|v.charCodeAt(++$)&1023),E[m>>>2]|=(240|k>>>18)<<L[m++&3],E[m>>>2]|=(128|k>>>12&63)<<L[m++&3],E[m>>>2]|=(128|k>>>6&63)<<L[m++&3],E[m>>>2]|=(128|k&63)<<L[m++&3]);else for(m=this.start;$<U&&m<64;++$)E[m>>>2]|=v[$]<<L[m++&3];this.lastByteIndex=m,this.bytes+=m-this.start,m>=64?(this.block=E[16],this.start=m-64,this.hash(),this.hashed=!0):this.start=m}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},D.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var v=this.blocks,b=this.lastByteIndex;v[16]=this.block,v[b>>>2]|=P[b&3],this.block=v[16],b>=56&&(this.hashed||this.hash(),v[0]=this.block,v[16]=v[1]=v[2]=v[3]=v[4]=v[5]=v[6]=v[7]=v[8]=v[9]=v[10]=v[11]=v[12]=v[13]=v[14]=v[15]=0),v[14]=this.hBytes<<3|this.bytes>>>29,v[15]=this.bytes<<3,this.hash()}},D.prototype.hash=function(){var v=this.h0,b=this.h1,x=this.h2,k=this.h3,$=this.h4,m,U,E,Y=this.blocks;for(U=16;U<80;++U)E=Y[U-3]^Y[U-8]^Y[U-14]^Y[U-16],Y[U]=E<<1|E>>>31;for(U=0;U<20;U+=5)m=b&x|~b&k,E=v<<5|v>>>27,$=E+m+$+1518500249+Y[U]<<0,b=b<<30|b>>>2,m=v&b|~v&x,E=$<<5|$>>>27,k=E+m+k+1518500249+Y[U+1]<<0,v=v<<30|v>>>2,m=$&v|~$&b,E=k<<5|k>>>27,x=E+m+x+1518500249+Y[U+2]<<0,$=$<<30|$>>>2,m=k&$|~k&v,E=x<<5|x>>>27,b=E+m+b+1518500249+Y[U+3]<<0,k=k<<30|k>>>2,m=x&k|~x&$,E=b<<5|b>>>27,v=E+m+v+1518500249+Y[U+4]<<0,x=x<<30|x>>>2;for(;U<40;U+=5)m=b^x^k,E=v<<5|v>>>27,$=E+m+$+1859775393+Y[U]<<0,b=b<<30|b>>>2,m=v^b^x,E=$<<5|$>>>27,k=E+m+k+1859775393+Y[U+1]<<0,v=v<<30|v>>>2,m=$^v^b,E=k<<5|k>>>27,x=E+m+x+1859775393+Y[U+2]<<0,$=$<<30|$>>>2,m=k^$^v,E=x<<5|x>>>27,b=E+m+b+1859775393+Y[U+3]<<0,k=k<<30|k>>>2,m=x^k^$,E=b<<5|b>>>27,v=E+m+v+1859775393+Y[U+4]<<0,x=x<<30|x>>>2;for(;U<60;U+=5)m=b&x|b&k|x&k,E=v<<5|v>>>27,$=E+m+$-1894007588+Y[U]<<0,b=b<<30|b>>>2,m=v&b|v&x|b&x,E=$<<5|$>>>27,k=E+m+k-1894007588+Y[U+1]<<0,v=v<<30|v>>>2,m=$&v|$&b|v&b,E=k<<5|k>>>27,x=E+m+x-1894007588+Y[U+2]<<0,$=$<<30|$>>>2,m=k&$|k&v|$&v,E=x<<5|x>>>27,b=E+m+b-1894007588+Y[U+3]<<0,k=k<<30|k>>>2,m=x&k|x&$|k&$,E=b<<5|b>>>27,v=E+m+v-1894007588+Y[U+4]<<0,x=x<<30|x>>>2;for(;U<80;U+=5)m=b^x^k,E=v<<5|v>>>27,$=E+m+$-899497514+Y[U]<<0,b=b<<30|b>>>2,m=v^b^x,E=$<<5|$>>>27,k=E+m+k-899497514+Y[U+1]<<0,v=v<<30|v>>>2,m=$^v^b,E=k<<5|k>>>27,x=E+m+x-899497514+Y[U+2]<<0,$=$<<30|$>>>2,m=k^$^v,E=x<<5|x>>>27,b=E+m+b-899497514+Y[U+3]<<0,k=k<<30|k>>>2,m=x^k^$,E=b<<5|b>>>27,v=E+m+v-899497514+Y[U+4]<<0,x=x<<30|x>>>2;this.h0=this.h0+v<<0,this.h1=this.h1+b<<0,this.h2=this.h2+x<<0,this.h3=this.h3+k<<0,this.h4=this.h4+$<<0},D.prototype.hex=function(){this.finalize();var v=this.h0,b=this.h1,x=this.h2,k=this.h3,$=this.h4;return C[v>>>28&15]+C[v>>>24&15]+C[v>>>20&15]+C[v>>>16&15]+C[v>>>12&15]+C[v>>>8&15]+C[v>>>4&15]+C[v&15]+C[b>>>28&15]+C[b>>>24&15]+C[b>>>20&15]+C[b>>>16&15]+C[b>>>12&15]+C[b>>>8&15]+C[b>>>4&15]+C[b&15]+C[x>>>28&15]+C[x>>>24&15]+C[x>>>20&15]+C[x>>>16&15]+C[x>>>12&15]+C[x>>>8&15]+C[x>>>4&15]+C[x&15]+C[k>>>28&15]+C[k>>>24&15]+C[k>>>20&15]+C[k>>>16&15]+C[k>>>12&15]+C[k>>>8&15]+C[k>>>4&15]+C[k&15]+C[$>>>28&15]+C[$>>>24&15]+C[$>>>20&15]+C[$>>>16&15]+C[$>>>12&15]+C[$>>>8&15]+C[$>>>4&15]+C[$&15]},D.prototype.toString=D.prototype.hex,D.prototype.digest=function(){this.finalize();var v=this.h0,b=this.h1,x=this.h2,k=this.h3,$=this.h4;return[v>>>24&255,v>>>16&255,v>>>8&255,v&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,x>>>24&255,x>>>16&255,x>>>8&255,x&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,$>>>24&255,$>>>16&255,$>>>8&255,$&255]},D.prototype.array=D.prototype.digest,D.prototype.arrayBuffer=function(){this.finalize();var v=new ArrayBuffer(20),b=new DataView(v);return b.setUint32(0,this.h0),b.setUint32(4,this.h1),b.setUint32(8,this.h2),b.setUint32(12,this.h3),b.setUint32(16,this.h4),v};function re(v,b){var x,k=N(v);if(v=k[0],k[1]){var $=[],m=v.length,U=0,E;for(x=0;x<m;++x)E=v.charCodeAt(x),E<128?$[U++]=E:E<2048?($[U++]=192|E>>>6,$[U++]=128|E&63):E<55296||E>=57344?($[U++]=224|E>>>12,$[U++]=128|E>>>6&63,$[U++]=128|E&63):(E=65536+((E&1023)<<10|v.charCodeAt(++x)&1023),$[U++]=240|E>>>18,$[U++]=128|E>>>12&63,$[U++]=128|E>>>6&63,$[U++]=128|E&63);v=$}v.length>64&&(v=new D(!0).update(v).array());var Y=[],pe=[];for(x=0;x<64;++x){var he=v[x]||0;Y[x]=92^he,pe[x]=54^he}D.call(this,b),this.update(pe),this.oKeyPad=Y,this.inner=!0,this.sharedMemory=b}re.prototype=new D,re.prototype.finalize=function(){if(D.prototype.finalize.call(this),this.inner){this.inner=!1;var v=this.array();D.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(v),D.prototype.finalize.call(this)}};var le=te();le.sha1=le,le.sha1.hmac=Z(),M?e.exports=le:c.sha1=le})()})(Ws);var yn=Ws.exports;const xn=gn(yn);function bs(){return"http://worksheet.hexinedu.com"}function $t(){return"http://127.0.0.1:3000"}function ws(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const St=async(e,s,t,a={},c=8e3)=>{try{return await Promise.race([e(),new Promise((g,T)=>setTimeout(()=>T(new Error("WebSocket请求超时，切换到HTTP")),c))])}catch{try{let T;return s==="get"?T=await Rt.get(t,{params:a}):s==="post"?T=await Rt.post(t,a):s==="delete"&&(T=await Rt.delete(t)),T.data}catch(T){throw new Error(`请求失败: ${T.message||"未知错误"}`)}}};function kn(e,s,t,a){const c=[e,s,t,a].join(":");return xn(c)}function Tn(){const e=ce(""),s=ce(""),t=ce(""),a=gt({}),c=ce(""),g=ce("");let T="",M=null;const w=ce("c:\\Temp"),C=gt({appKey:"",appSecret:""}),P=gt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),L=gt({show:!1,title:"",message:"",type:"error"}),V=ce(""),I=ce("junior"),B=ce(null),J=ce(!1),N=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],ee=()=>Je.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],te=gt(ee()),se=async()=>{try{const n=await G.getWatcherStatus();n.data&&n.data.watchDir&&(w.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${w.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},f=async()=>{var n,o,l;try{if(!C.appKey||!C.appSecret)throw new Error("未初始化app信息");const p=60,i=Date.now();let r;try{const z=window.Application.PluginStorage.getItem("token_info");z&&(r=JSON.parse(z))}catch(z){r=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${z.message}</span><br/>`}if(r&&r.access_token&&r.expired_time>i+p*1e3)return r.access_token;const u=C.appKey,O="1234567",_=Math.floor(Date.now()/1e3),A=kn(u,O,C.appSecret,_),R=await Rt.get(bs()+"/api/open/account/v1/auth/token",{params:{app_key:u,app_nonstr:O,app_timestamp:_,app_signature:A}});if((o=(n=R.data)==null?void 0:n.data)!=null&&o.access_token){const z=R.data.data.access_token;let X;if(R.data.data.expired_time){const ue=parseInt(R.data.data.expired_time);X=i+ue*1e3,t.value+=`<span class="log-item info">token已更新，有效期${ue}秒</span><br/>`}else X=i+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const ne={access_token:z,expired_time:X};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(ne))}catch(ue){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${ue.message}</span><br/>`}return z}else throw new Error(((l=R.data)==null?void 0:l.message)||"获取access_token失败")}catch(p){throw t.value+=`<span class="log-item error">获取access_token失败: ${p.message}</span><br/>`,p}},Z=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},D=()=>{const n=window.Application,o=n.Documents.Count;for(let l=1;l<=o;l++){const p=n.Documents.Item(l);if(p.DocID===c.value)return p}return null},re=()=>{try{const n=D();if(!n)return{isValid:!1,message:"未找到当前文档"};let o="";try{o=n.Name||""}catch{try{o=m("getDocName")||""}catch{o=""}}if(o){const l=o.toLowerCase();return l.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:l.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},le=(n,o="",l=0,p=null)=>{try{const i=window.Application,r=D();let u;if(p)u=p;else{const O=r.ActiveWindow.Selection;if(!O||O.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;u=O.Range}if(o){const O=u.Text,_=u.Find;console.log("find",_),_.ClearFormatting(),_.Text=o,_.Forward=!0,_.Wrap=0;let A=0,R=[];const z=u.Start,X=u.End;for(;_.Execute()&&(_.Found&&_.Parent.Start>=z&&_.Parent.End<=X);){const ne=_.Parent.Start,ue=_.Parent.End;if(R.some(be=>ne===be.start&&ue===be.end)){const be=Math.min(ue,X);if(be>=X)break;_.Parent.SetRange(be,X)}else{if(R.push({start:ne,end:ue}),l===-1||A===l){const de=r.Comments.Add(_.Parent,n);if(l!==-1&&A===l)return t.value+=`<span class="log-item success">已为第${l+1}个"${o}"添加批注: "${n}"</span><br/>`,!0}A++;const be=Math.min(ue,X);if(be>=X)break;const xe=r.Range(be,X);_.Parent.SetRange(be,X)}}return l!==-1&&A<=l?(t.value+=`<span class="log-item error">在${p?"指定范围":"选中内容"}中未找到第${l+1}个"${o}"</span><br/>`,!1):l===-1&&A>0?(t.value+=`<span class="log-item success">已为${A}处"${o}"添加批注: "${n}"</span><br/>`,!0):l===-1&&A===0?(t.value+=`<span class="log-item error">在${p?"指定范围":"选中内容"}中未找到关键字"${o}"</span><br/>`,!1):!0}else{const O=r.Comments.Add(u,n);return t.value+=`<span class="log-item success">已为${p?"指定范围":"选中内容"}添加批注: "${n}"</span><br/>`,!0}}catch(i){return t.value+=`<span class="log-item error">添加批注失败: ${i.message}</span><br/>`,!1}},v=n=>n===0?"status-preparing":n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",b=n=>n===0?"准备中":n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"准备中",x=n=>{const o=Date.now()-n,l=Math.floor(o/1e3);return l<60?`${l}秒`:l<3600?`${Math.floor(l/60)}分${l%60}秒`:`${Math.floor(l/3600)}时${Math.floor(l%3600/60)}分`},k=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const l=D();if(l&&l.ContentControls)for(let p=1;p<=l.ContentControls.Count;p++)try{const i=l.ContentControls.Item(p);if(i&&i.Title&&(i.Title===`任务_${n}`||i.Title===`任务增强_${n}`||i.Title===`校对_${n}`)){const r=i.Title===`任务增强_${n}`||a[n].isEnhanced,u=i.Title===`校对_${n}`||a[n].isCheckTask;u?i.Title=`已停止校对_${n}`:r?i.Title=`已停止增强_${n}`:i.Title=`已停止_${n}`;const O=u?"校对":r?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(l){t.value+=`<span class="log-item warning">更新控件标题失败: ${l.message}</span><br/>`}let o=null;if(K[n]&&K[n].urlId){o=K[n].urlId;try{try{const l=await St(async()=>await G.getUrlMonitorStatus(),"get",`${$t()}/api/url/status`),i=(Array.isArray(l)?l:l.data?Array.isArray(l.data)?l.data:[]:[]).find(r=>r.urlId===o);i&&i.downloadedPath&&(a[n].resultFile=i.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${i.downloadedPath}</span><br/>`)}catch(l){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${l.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ge(o),delete K[n]}catch(l){t.value+=`<span class="log-item warning">停止URL监控出错: ${l.message}，将重试</span><br/>`,delete K[n],setTimeout(async()=>{try{o&&await St(async()=>await G.stopUrlMonitoring(o),"delete",`${$t()}/api/url/monitor/${o}`)}catch(p){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${p.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(o){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${o.message}</span><br/>`,K[n]&&delete K[n]}},$=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const o=D();if(o&&o.ContentControls)for(let p=1;p<=o.ContentControls.Count;p++)try{const i=o.ContentControls.Item(p);if(i&&i.Title&&(i.Title===`任务_${n}`||i.Title===`任务增强_${n}`||i.Title===`校对_${n}`)){const r=i.Title===`任务增强_${n}`||a[n].isEnhanced,u=i.Title===`校对_${n}`||a[n].isCheckTask;u?i.Title=`已停止校对_${n}`:r?i.Title=`已停止增强_${n}`:i.Title=`已停止_${n}`,i.LockContents=!1;const O=u?"校对":r?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let l=null;if(K[n]&&K[n].urlId){l=K[n].urlId;try{const p=await G.getUrlMonitorStatus(),r=(Array.isArray(p)?p:p.data?Array.isArray(p.data)?p.data:[]:[]).find(u=>u.urlId===l);r&&r.downloadedPath&&(a[n].resultFile=r.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${r.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ge(l),delete K[n]}catch(p){t.value+=`<span class="log-item warning">停止URL监控出错: ${p.message}，将重试</span><br/>`,delete K[n]}}},m=n=>hn.onbuttonclick(n),U=()=>{try{e.value=m("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},E=()=>{D().ActiveWindow.Selection.Copy()},Y=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${w.value}\\${n}`,12,"","",!1),o.Close(),D().ActiveWindow.Activate()},pe=n=>{const o=window.Application.Documents.Add("",!1,0,!1);o.Content.Paste(),o.SaveAs2(`${w.value}\\${n}`,12,"","",!1),o.Close(),D().ActiveWindow.Activate()},he=n=>{try{const l=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,p=window.Application.Documents.Add("",!1,0,!1);p.Content.Paste();const i=`${l}\\${n}`;p.SaveAs2(i,12,"","",!1),p.Close(),D().ActiveWindow.Activate(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${i}.docx</span><br/>`}catch(o){throw t.value+=`<span class="log-item error">方式三保存失败: ${o.message}</span><br/>`,o}},$e=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${w.value}\\${n}`,12,"","",!1),D().ActiveWindow.Activate()},Oe=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let o="method2";try{const l=await G.getSaveMethod();if(l.success&&l.saveMethod){o=l.saveMethod;const p=o==="method1"?"方式一":o==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${p}</span><br/>`}}catch(l){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${l.message}</span><br/>`}o==="method1"?(Y(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):o==="method2"?(pe(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):o==="method3"?(he(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):o==="method4"&&($e(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${w.value}\\${n}.docx</span><br/>`),G.associateFileWithClient(`${n}.docx`).then(l=>{l.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${l.message||"未知错误"}</span><br/>`}).catch(l=>{t.value+=`<span class="log-item warning">关联文件时出错: ${l.message}</span><br/>`})}catch(o){t.value+=`<span class="log-item error">保存文件失败: ${o.message}</span><br/>`}},Se=ce(null),Me=gt([]),we=new Set,Te=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),_e=async n=>{try{const o=n.slice(T.length);if(o.trim()){const l=G.getClientId();if(!l){console.warn("无法获取客户端ID，跳过日志同步");return}const p=Te(o);if(!p.trim()){T=n;return}await G.sendRequest("logger","syncLog",{content:p,timestamp:new Date().toISOString(),clientId:l}),T=n}}catch(o){console.error("同步日志到服务端失败:",o)}},Ie=()=>{G.connect().then(()=>{se()}).catch(o=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${o.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const o=[];for(const l in a)if(a.hasOwnProperty(l)){const p=a[l];(p.status===0||p.status===1)&&!p.terminated&&(o.includes(l)||o.push(l))}if(o.length>0){let l=!1;try{const p=D();if(p){const r=`taskpane_id_${p.DocID}`,u=window.Application.PluginStorage.getItem(r);if(u){const O=window.Application.GetTaskPane(u);O&&(l=O.Visible)}}}catch(p){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${p.message}</span><br/>`,l=!0}setTimeout(()=>{if(l){const p=`检测到 ${o.length} 个未完成的任务，是否继续？`;ae(p).then(i=>{i?(t.value+=`<span class="log-item info">用户选择继续 ${o.length} 个进行中的任务 (by taskId)...</span><br/>`,G.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:o}).then(r=>{r&&r.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${r.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(r==null?void 0:r.message)||"未知错误"}</span><br/>`}).catch(r=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${r.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',o.forEach(r=>{k(r)}),t.value+=`<span class="log-item success">${o.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(i=>{t.value+=`<span class="log-item error">弹窗错误: ${i.message}，默认停止任务（保留控件）</span><br/>`,o.forEach(r=>{k(r)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};G.setConnectionSuccessHandler(n),G.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),G.addEventListener("connection",o=>{o.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${o.reason||"未知"}, 代码: ${o.code||"N/A"}，将自动重连</span><br/>`)}),G.addEventListener("watcher",o=>{var l,p;if(Me.push(o),o.type,o.eventType==="uploadSuccess"){const i=(l=o.data)==null?void 0:l.file,r=i==null?void 0:i.replace(/\.docx$/,""),u=`${o.eventType}_${i}_${Date.now()}`;if(we.has(u)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${i}</span><br/>`;return}if(we.add(u),we.size>100){const _=we.values();we.delete(_.next().value)}const O=r&&((p=a[r])==null?void 0:p.wordType);Re(o,O)}else o.eventType&&Re(o,null)}),G.addEventListener("urlMonitor",o=>{Me.push(o),o.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${o.eventType||o.action}</span><br/>`),o.eventType&&Re(o,null)}),G.addEventListener("health",o=>{}),G.addEventListener("error",o=>{const l=o.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${l}</span><br/>`,console.error("WebSocket错误:",o)})},K=gt({}),De=async(n,o,l=!1,p=5e3,i={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const r=await G.startUrlMonitoring(n,p,{downloadOnSuccess:i.downloadOnSuccess!==void 0?i.downloadOnSuccess:!0,appKey:i.appKey,filename:i.filename,taskId:o}),u=r.success||(r==null?void 0:r.success),O=r.urlId||(r==null?void 0:r.urlId);if(u&&O){K[o]={urlId:O,url:n,isResultUrl:l,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${O}</span><br/>`;try{await G.startUrlChecking(O)}catch{}return O}else throw new Error("服务器返回失败")}catch(r){return t.value+=`<span class="log-item error">启动URL监控失败: ${r.message}</span><br/>`,null}},ge=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(K).forEach(l=>{K[l].urlId===n&&delete K[l]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const o=await St(async()=>await G.stopUrlMonitoring(n),"delete",`${$t()}/api/url/monitor/${n}`);return o&&(o.success||o!=null&&o.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(o){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${o.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await St(async()=>await G.stopUrlMonitoring(n),"delete",`${$t()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},st=async()=>{try{const n=await St(async()=>await G.getUrlMonitorStatus(),"get",`${$t()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Ke=async n=>{try{return await G.forceUrlCheck(n)}catch{return!1}},Re=async(n,o)=>{var l;if(n.eventType==="uploadSuccess"){const p=n.data.file,i=p.replace(/\.docx$/,"");if(a[i]){if(a[i].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${i.substring(0,8)} 的上传通知</span><br/>`;return}if(a[i].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${i.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${p} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${o||a[i].wordType||"wps-analysis"}</span><br/>`,a[i].status=1,a[i].uploadSuccess=!0,await fe(i,o||a[i].wordType||"wps-analysis")}}else if(n.eventType==="encryptedFileError"){const p=n.data.file,i=p.replace(/\.docx$/,"");if(a[i]){t.value+=`<span class="log-item error">文件加密错误: ${p}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[i].status=-1,a[i].errorMessage=n.data.message||"文件已加密，无法处理";try{const r=D();if(r&&r.ContentControls)for(let u=1;u<=r.ContentControls.Count;u++)try{const O=r.ContentControls.Item(u);if(O&&O.Title&&(O.Title===`任务_${i}`||O.Title===`任务增强_${i}`||O.Title===`校对_${i}`)){const _=O.Title===`任务增强_${i}`||a[i].isEnhanced,A=O.Title===`校对_${i}`||a[i].isCheckTask;A?O.Title=`异常校对_${i}`:_?O.Title=`异常增强_${i}`:O.Title=`异常_${i}`;const R=A?"校对":_?"增强":"普通";t.value+=`<span class="log-item info">已将${R}任务${i.substring(0,8)}控件标记为异常（文件加密）</span><br/>`;break}}catch{continue}}catch(r){t.value+=`<span class="log-item warning">更新控件标题失败: ${r.message}</span><br/>`}me(i)}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:p,url:i,taskId:r,downloadedPath:u}=n.data,O=Object.keys(K).filter(_=>K[_].urlId===p);O.length>0&&O.forEach(_=>{u&&a[_]&&(a[_].resultFile=u,a[_].resultDownloaded=!0),delete K[_]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:p,url:i,filePath:r,taskId:u}=n.data;if(!Object.values(K).some(_=>_.urlId===p)&&u&&((l=a[u])!=null&&l.terminated))return;if(u&&a[u]&&a[u].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${p} 的文件下载通知</span><br/>`,p)try{await G.stopUrlMonitoring(p),K[u]&&delete K[u]}catch{}return}if(u&&a[u]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${r}</span><br/>`,a[u].isCheckTask&&r.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${r}</span><br/>`;try{const A=window.Application.FileSystem.ReadFile(r),R=JSON.parse(A);if(await h(R,u)){a[u].status=2,t.value+=`<span class="log-item success">校对任务${u.substring(0,8)}已完成批注处理</span><br/>`;const X=x(a[u].startTime);t.value+=`<span class="log-item success">校对任务${u.substring(0,8)}完成，总耗时${X}</span><br/>`}}catch(_){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${_.message}</span><br/>`,_.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${r}</span><br/>`),a[u].status=-1,a[u].errorMessage=`JSON处理失败: ${_.message}`,t.value+=`<span class="log-item error">校对任务${u.substring(0,8)}处理失败</span><br/>`}}else{a[u].resultFile=r,a[u].resultDownloaded=!0;const _=x(a[u].startTime);t.value+=`<span class="log-item success">任务${u.substring(0,8)}完成，总耗时${_}</span><br/>`,await Be(u)}K[u]&&K[u].urlId&&(ge(K[u].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete K[u])}else if(p){t.value+=`<span class="log-item info">URL文件已下载: ${r}</span><br/>`;const _=Object.keys(K).filter(A=>{var R;return K[A].urlId===p&&!((R=a[A])!=null&&R.terminated)});_.length>0&&_.forEach(async A=>{if(a[A]){if(t.value+=`<span class="log-item info">关联到任务: ${A.substring(0,8)}</span><br/>`,a[A].resultFile=r,a[A].resultDownloaded=!0,a[A].isCheckTask&&r.endsWith(".wps.json"))try{const z=window.Application.FileSystem.ReadFile(r),X=JSON.parse(z);if(await h(X,A)&&a[A].status===1){a[A].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const ue=x(a[A].startTime);t.value+=`<span class="log-item success">校对任务${A.substring(0,8)}完成，总耗时${ue}</span><br/>`}}catch(R){t.value+=`<span class="log-item error">处理校对JSON失败: ${R.message}</span><br/>`,a[A].status===1&&(a[A].status=-1,a[A].errorMessage=`JSON处理失败: ${R.message}`,t.value+=`<span class="log-item error">校对任务${A.substring(0,8)}处理失败</span><br/>`)}else if(a[A].status===1){a[A].status=2;const R=x(a[A].startTime);t.value+=`<span class="log-item success">任务${A.substring(0,8)}完成，总耗时${R}</span><br/>`}}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:p,url:i,error:r,taskId:u}=n.data;if(u&&a[u]&&a[u].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${u.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${r}</span><br/>`,u&&a[u]){a[u].status=-1,a[u].errorMessage=`下载失败: ${r}`;try{const O=D();if(O&&O.ContentControls)for(let _=1;_<=O.ContentControls.Count;_++)try{const A=O.ContentControls.Item(_);if(A&&A.Title&&(A.Title===`任务_${u}`||A.Title===`任务增强_${u}`||A.Title===`校对_${u}`)){const R=A.Title===`任务增强_${u}`||a[u].isEnhanced,z=A.Title===`校对_${u}`||a[u].isCheckTask;z?A.Title=`异常校对_${u}`:R?A.Title=`异常增强_${u}`:A.Title=`异常_${u}`;const X=z?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${X}任务${u.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(O){t.value+=`<span class="log-item warning">更新控件标题失败: ${O.message}</span><br/>`}me(u),K[u]&&delete K[u]}if(p)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${p}</span><br/>`,await St(async()=>await G.stopUrlMonitoring(p),"delete",`${$t()}/api/url/monitor/${p}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},fe=async(n,o="wps-analysis")=>{try{if(!C.appKey)throw new Error("未初始化appKey信息");const l=await f(),p=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,i=await Rt.post(bs()+"/api/open/ticket/v1/ai_comment/create",{access_token:l,data:{app_key:C.appKey,subject:V.value,stage:I.value,file_name:`${n}`,word_url:p,word_type:o,is_ai_auto:!0,is_ai_edit:!0,create_user_id:C.userId,create_username:C.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),r=i.data.data.ticket_id;if(!r)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",me(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${r}，开始监控结果文件</span><br/>`;let u,O;o==="wps-check"?(u=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${C.appKey}/ai/${r}.wps.json`,O=`${r}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${O}</span><br/>`):(u=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${r}.wps.docx`,O=`${r}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${O}</span><br/>`);const _={downloadOnSuccess:!0,filename:O,appKey:C.appKey,ticketId:r,taskId:n},A=await De(u,n,!0,3e3,_);return a[n]&&(a[n].ticketId=r,a[n].resultUrl=u,a[n].urlMonitorId=A,a[n].status=1),i.data}catch(l){return t.value+=`<span class="log-item error">API调用失败: ${l.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${l.message}`,me(n)),!1}},ut=async(n,o="wps-analysis")=>new Promise((l,p)=>{try{t.value+=`<span class="log-item info">监控目录: ${w.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${o}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=o,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const i=1e3,r=10;let u=0;const O=setInterval(()=>{if(u++,a[n]&&a[n].uploadSuccess){clearInterval(O),l(!0);return}if(a[n]&&a[n].status===-1&&(clearInterval(O),t.value+=`<span class="log-item error">任务${n.substring(0,8)}已异常，停止等待上传完成</span><br/>`,p(new Error(a[n].errorMessage||"任务已异常"))),u>=r){clearInterval(O),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',me(n);return}},i)}catch(i){t.value+=`<span class="log-item error">任务处理异常: ${i.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${i.message}`),me(n),p(i)}}),Xe=async()=>{var l;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,o=n.ActiveDocument;if(c.value=o.DocID,g.value=n.ActiveWindow.Index,o!=null&&o.ContentControls)for(let p=1;p<=o.ContentControls.Count;p++){const i=o.ContentControls.Item(p);if(i&&i.Title){let r=null,u=1,O=!1,_=!1;if(i.Title.startsWith("任务增强_")?(r=i.Title.substring(5),u=1,O=!0):i.Title.startsWith("任务_")?(r=i.Title.substring(3),u=1):i.Title.startsWith("校对_")?(r=i.Title.substring(3),u=1,_=!0):i.Title.startsWith("已完成增强_")?(r=i.Title.substring(6),u=2,O=!0):i.Title.startsWith("已完成校对_")?(r=i.Title.substring(6),u=2,_=!0):i.Title.startsWith("已完成_")?(r=i.Title.substring(4),u=2):i.Title.startsWith("异常增强_")?(r=i.Title.substring(5),u=-1,O=!0):i.Title.startsWith("异常校对_")?(r=i.Title.substring(5),u=-1,_=!0):i.Title.startsWith("异常_")?(r=i.Title.substring(3),u=-1):i.Title.startsWith("已停止增强_")?(r=i.Title.substring(6),u=4,O=!0):i.Title.startsWith("已停止校对_")?(r=i.Title.substring(6),u=4,_=!0):i.Title.startsWith("已停止_")&&(r=i.Title.substring(4),u=4),r&&!a[r]){let A="";try{A=((l=i.Range)==null?void 0:l.Text)||""}catch{}let R=Date.now()-24*60*60*1e3;try{if(r.length===24){const ne=r.substring(0,8),ue=parseInt(ne,16);!isNaN(ue)&&ue>0&&ue<2147483647&&(R=ue*1e3)}else{const ne=Date.now()-864e5;u===2?R=ne-60*60*1e3:u===-1?R=ne-30*60*1e3:u===4?R=ne-45*60*1e3:R=ne}}catch{}a[r]={status:u,startTime:R,contentControlId:i.ID,isEnhanced:O,isCheckTask:_,selectedText:A};const z=u===1?"进行中":u===2?"已完成":u===-1?"异常":u===4?"已停止":"未知",X=_?"校对":O?"增强":"普通";t.value+=`<span class="log-item info">发现已有${X}任务: ${r.substring(0,8)}, 状态: ${z}</span><br/>`}}}},me=(n,o=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}o&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const l=D();if(!l){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let p=!1;const i=a[n].isEnhanced;if(l.ContentControls&&l.ContentControls.Count>0)for(let r=l.ContentControls.Count;r>=1;r--)try{const u=l.ContentControls.Item(r);u&&u.Title&&u.Title.includes(n)&&(u.LockContents=!1,u.Delete(!1),p=!0,console.log(u.Title),t.value+=`<span class="log-item success">已解锁并删除${i?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(u){t.value+=`<span class="log-item warning">访问第${r}个控件时出错: ${u.message}</span><br/>`;continue}p?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${i?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(l){t.value+=`<span class="log-item error">删除内容控件失败: ${l.message}</span><br/>`}},nt=n=>{var o;try{const l=window.wps,p=D();if(!p||!p.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const i=(o=a[n])==null?void 0:o.isEnhanced;let r=null;for(let O=1;O<=p.ContentControls.Count;O++)try{const _=p.ContentControls.Item(O);if(_&&_.Title&&_.Title.includes(n)){r=_;break}}catch{continue}if(!r){const O=a[n];O&&(O.status===2||O.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}r.Range.Select();const u=l.Windows.Item(g.value);u&&u.Selection&&u.Selection.Range&&u.ScrollIntoView(u.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${i?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(l){t.value+=`<span class="log-item error">跳转到任务控件失败: ${l.message}</span><br/>`}},Be=async n=>{var u,O,_;const o=D(),l=a[n];if(!l){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(l.terminated||l.status!==1)return;if(l.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const p=D();let i=null;if(p&&p.ContentControls)for(let A=1;A<=p.ContentControls.Count;A++){const R=p.ContentControls.Item(A);if(R&&R.Title&&R.Title.includes(n)){i=R;break}}if(!i){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,l.errorMessage&&l.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${l.errorMessage}</span><br/>`;return}return}if(l.errorMessage&&l.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${l.errorMessage}</span><br/>`,me(n);return}if(!l.resultFile)return;a[n].documentInserted=!0;const r=i.Range;i.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${l.resultFile}...</span><br/>`;const A=o.Range(r.End,r.End);A.InsertParagraphAfter(),r.Text.includes("\v")&&A.InsertAfter("\v");let R=A.End;const z=(u=r.Tables)==null?void 0:u.Count;if(z){const de=r.Tables.Item(z);((O=de==null?void 0:de.Range)==null?void 0:O.End)>R&&(de.Range.InsertParagraphAfter(),R=(_=de==null?void 0:de.Range)==null?void 0:_.End)}o.Range(R,R).InsertFile(l.resultFile);for(let de=1;de<=p.ContentControls.Count;de++){const it=p.ContentControls.Item(de);if(it&&it.Title&&(it.Title===`任务_${n}`||it.Title===`任务增强_${n}`)){i=it;break}}const ne=o.Range(i.Range.End-1,i.Range.End);ne.Text.includes("\r")&&ne.Delete(),a[n].status=2,K[n]&&K[n].urlId&&G.sendRequest("urlMonitor","updateTaskStatus",{urlId:K[n].urlId,status:"completed",taskId:n}).then(de=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(de=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${de.message}</span><br/>`});const ze=x(l.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${ze}</span><br/>`;const be=i.Title===`任务增强_${n}`||l.isEnhanced,xe=i.Title===`校对_${n}`||l.isCheckTask;if(i){xe?i.Title=`已完成校对_${n}`:be?i.Title=`已完成增强_${n}`:i.Title=`已完成_${n}`;const de=xe?"校对":be?"增强":"普通";t.value+=`<span class="log-item info">已将${de}任务${n.substring(0,8)}控件标记为已完成</span><br/>`}}catch(A){a[n].documentInserted=!1,a[n].status=-1;const R=i.Title===`任务增强_${n}`||l.isEnhanced,z=i.Title===`校对_${n}`||l.isCheckTask;if(i){z?i.Title=`异常校对_${n}`:R?i.Title=`异常增强_${n}`:i.Title=`异常_${n}`;const X=z?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${X}任务${n.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${A.message}</span><br/>`}},Ce=async()=>{const n=(p=1e3)=>new Promise(i=>{setTimeout(()=>i(),p)}),o=new Map,l=Object.keys(a).filter(p=>a[p].status===1&&!a[p].terminated);for(l.length?(l.forEach(p=>{o.has(p)||o.set(p,Date.now())}),await Promise.all(l.map(p=>Be(p)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const p=Object.keys(a).filter(i=>a[i].status===1&&!a[i].terminated);p.forEach(i=>{o.has(i)||o.set(i,Date.now());const r=o.get(i);(Date.now()-r)/1e3/60>=5e4&&a[i]&&!a[i].terminated&&(a[i].terminated=!0,a[i].status=-1,t.value+=`<span class="log-item warning">任务 ${i} 执行超过5分钟，已自动终止</span><br/>`,o.delete(i))}),p.length&&await Promise.all(p.map(i=>Be(i)))}},Ue=async(n="wps-analysis")=>{const o=y();if(o){const{primary:p,all:i}=o,{taskId:r,control:u,task:O,isEnhanced:_,overlapType:A}=p,R=i.filter(z=>z.task&&(z.task.status===0||z.task.status===1));if(R.length>0){const z=R.map(X=>X.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${z})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${i.length}个已有任务重叠，重叠类型: ${A}</span><br/>`,W();try{for(const z of i){const{taskId:X,control:ne,task:ue,isEnhanced:ze}=z;t.value+=`<span class="log-item info">删除重叠的${ze?"增强":"普通"}任务 ${X.substring(0,8)}，状态为 ${b((ue==null?void 0:ue.status)||0)}</span><br/>`,ue&&(ue.status=3,ue.terminated=!0,ue.errorMessage="用户重新创建任务时删除");try{ne.LockContents=!1,ne.Delete(!1),a[X]&&(a[X].placeholderRemoved=!0)}catch(be){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${be.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${i.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(z){return H(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${z.message}</span><br/>`,Promise.reject(z)}H()}const l=ws().replace(/-/g,"").substring(0,8);return new Promise(async(p,i)=>{try{const r=window.wps,u=D(),O=u.ActiveWindow.Selection,_=O.Range,A=O.Text||"";if(s.value=A,E(),_){const R=_.Paragraphs,z=R.Item(1),X=R.Item(R.Count),ne=z.Range.Start,ue=X.Range.End,ze=_.Start,be=_.End,xe=u.Range(Math.min(ne,ze),Math.max(ue,be));try{let de=u.ContentControls.Add(r.Enum.wdContentControlRichText,xe);if(!de){if(console.log("创建内容控件失败"),de=u.ContentControls.Add(r.Enum.wdContentControlRichText),!de){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',i(new Error("创建内容控件失败"));return}_.Cut(),de.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const it=n==="wps-enhance_analysis";de.Title=it?`任务增强_${l}`:`任务_${l}`,de.LockContents=!0,a[l]||(a[l]={}),a[l].contentControlId=de.ID,a[l].isEnhanced=it,t.value+=`<span class="log-item info">已创建${it?"增强":"普通"}内容控件并锁定选区</span><br/>`;const on=de.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${on.length}</span><br/>`}catch(de){t.value+=`<span class="log-item error">创建内容控件失败: ${de.message}</span><br/>`,i(de);return}}a[l]={status:0,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:A},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${l}，类型: ${n}</span><br/>`,await Oe(l),a[l]&&(a[l].status=1);try{await ut(l,n)?p():(a[l]&&a[l].status===1&&(a[l].status=-1,a[l].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${l.substring(0,8)}失败</span><br/>`,oe(l)),i(new Error("API调用失败或超时")))}catch(R){a[l]&&(a[l].status=-1,a[l].errorMessage=`执行错误: ${R.message}`,t.value+=`<span class="log-item error">任务${l.substring(0,8)}执行出错: ${R.message}</span><br/>`,oe(l)),i(R)}}catch(r){i(r)}})},Ne=async()=>{const n=y();if(n){const{primary:l,all:p}=n,{taskId:i,control:r,task:u,isEnhanced:O,overlapType:_}=l,A=p.filter(R=>R.task&&(R.task.status===0||R.task.status===1));if(A.length>0){const R=A.map(z=>z.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${R})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${p.length}个已有任务重叠，重叠类型: ${_}</span><br/>`,W();try{for(const R of p){const{taskId:z,control:X,task:ne,isEnhanced:ue}=R;t.value+=`<span class="log-item info">删除重叠的校对任务 ${z.substring(0,8)}，状态为 ${b((ne==null?void 0:ne.status)||0)}</span><br/>`,ne&&(ne.status=3,ne.terminated=!0,ne.errorMessage="用户重新创建校对任务时删除");try{X.LockContents=!1,X.Delete(!1),a[z]&&(a[z].placeholderRemoved=!0)}catch(ze){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${ze.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${p.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(R){return H(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${R.message}</span><br/>`,Promise.reject(R)}H()}const o=ws().replace(/-/g,"").substring(0,8);return new Promise(async(l,p)=>{try{const i=window.wps,r=D(),u=r.ActiveWindow.Selection,O=u.Range,_=u.Text||"";if(s.value=_,E(),O){const A=O.Paragraphs,R=A.Item(1),z=A.Item(A.Count),X=R.Range.Start,ne=z.Range.End,ue=O.Start,ze=O.End,be=r.Range(Math.min(X,ue),Math.max(ne,ze));try{let xe=r.ContentControls.Add(i.Enum.wdContentControlRichText,be);if(!xe){if(console.log("创建校对内容控件失败"),xe=r.ContentControls.Add(i.Enum.wdContentControlRichText),!xe){t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',p(new Error("创建校对内容控件失败"));return}O.Cut(),xe.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',xe.Title=`校对_${o}`,xe.LockContents=!0,a[o]||(a[o]={}),a[o].contentControlId=xe.ID,a[o].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const de=xe.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${de.length}</span><br/>`}catch(xe){t.value+=`<span class="log-item error">创建校对内容控件失败: ${xe.message}</span><br/>`,p(xe);return}}a[o]={status:0,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:_},t.value+=`<span class="log-item success">创建校对任务: ${o}，类型: wps-check</span><br/>`,await Oe(o),a[o]&&(a[o].status=1);try{await ut(o,"wps-check")?l():(a[o]&&a[o].status===1&&(a[o].status=-1,a[o].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}失败</span><br/>`,oe(o)),p(new Error("校对API调用失败或超时")))}catch(A){a[o]&&(a[o].status=-1,a[o].errorMessage=`校对执行错误: ${A.message}`,t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}执行出错: ${A.message}</span><br/>`,oe(o)),p(A)}}catch(i){p(i)}})},Ve=async()=>{},Ge=()=>ot()>0?"status-error":at()>0?"status-running":Ze()>0?"status-completed":"",at=()=>Object.values(a).filter(n=>n.status===0||n.status===1).length,Ze=()=>Object.values(a).filter(n=>n.status===2).length,dt=()=>Object.values(a).filter(n=>n.status===2||n.status===4).length,ot=()=>Object.values(a).filter(n=>n.status===-1).length,Ot=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,o=D();if(!o){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let l=0;if(Object.keys(a).forEach(p=>{try{a[p].status===1&&(a[p].status=3,a[p].terminated=!0,a[p].errorMessage="强制清除"),me(p),l++}catch(i){t.value+=`<span class="log-item warning">清除任务${p.substring(0,8)}失败: ${i.message}</span><br/>`}}),o.ContentControls&&o.ContentControls.Count>0)for(let p=o.ContentControls.Count;p>=1;p--)try{const i=o.ContentControls.Item(p);if(i&&i.Title&&(i.Title.startsWith("任务_")||i.Title.startsWith("任务增强_")||i.Title.startsWith("已完成_")||i.Title.startsWith("已完成增强_")||i.Title.startsWith("异常_")||i.Title.startsWith("异常增强_")||i.Title.startsWith("已停止_")||i.Title.startsWith("已停止增强_")))try{i.LockContents=!1,i.Delete(!1);let r;i.Title.startsWith("任务增强_")?r=i.Title.substring(5):i.Title.startsWith("任务_")?r=i.Title.substring(3):i.Title.startsWith("已完成增强_")?r=i.Title.substring(6):i.Title.startsWith("已完成_")?r=i.Title.substring(4):i.Title.startsWith("异常增强_")?r=i.Title.substring(5):i.Title.startsWith("异常_")?r=i.Title.substring(3):i.Title.startsWith("已停止增强_")?r=i.Title.substring(6):i.Title.startsWith("已停止_")&&(r=i.Title.substring(4)),a[r]?(a[r].placeholderRemoved=!0,a[r].status=3):a[r]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},l++,t.value+=`<span class="log-item success">已删除任务${r.substring(0,8)}的内容控件</span><br/>`}catch(r){t.value+=`<span class="log-item error">删除控件失败: ${r.message}</span><br/>`}}catch(i){t.value+=`<span class="log-item warning">访问控件时出错: ${i.message}</span><br/>`}l>0?t.value+=`<span class="log-item success">已清除${l}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},kt=async()=>{try{const n=Object.values(K).map(o=>o.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const o of n)await ge(o)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Tt=()=>{Fs(async()=>{try{const o=window.Application.PluginStorage.getItem("user_info");if(!o)throw new Error("未找到用户信息");const l=JSON.parse(o);if(!l.orgs||!l.orgs[0])throw new Error("未找到组织信息");C.appKey=l.appKey,C.userName=l.nickname,C.userId=l.userId,C.appSecret=l.appSecret}catch(o){t.value+=`<span class="log-item error">初始化appKey失败: ${o.message}</span><br/>`}await se(),Jt([V,I],async()=>{await Le()},{immediate:!1}),await vt();const n=Je.onVersionChange(()=>{const o=ee();te.splice(0,te.length,...o),Je.isSeniorEdition()&&I.value==="junior"?I.value="senior":!Je.isSeniorEdition()&&I.value==="senior"&&(I.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Je.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',U(),await Xe(),Ie(),Ct(),Ce(),window.addEventListener("beforeunload",kt),()=>{M&&clearTimeout(M),n&&n()}}),Jt(t,n=>{M&&clearTimeout(M),M=setTimeout(()=>{_e(n)},10)},{immediate:!1})},Ct=()=>{G.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==V.value&&(V.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${V.value}</span><br/>`),n.data.stage!==I.value&&(I.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${I.value}</span><br/>`))})},pt=ce(!1),y=()=>{try{const n=D(),o=n.ActiveWindow.Selection;if(!o||!n||!n.ContentControls)return null;const l=o.Range,p=[];for(let i=1;i<=n.ContentControls.Count;i++)try{const r=n.ContentControls.Item(i);if(!r)continue;const u=(r.Title||"").trim(),O=r.Range;if(l.Start<O.End&&l.End>O.Start){let _=null,A=!1,R=!1;if(!u)R=!0,_=`empty_${r.ID||Date.now()}`;else if(u.startsWith("任务_")||u.startsWith("任务增强_")||u.startsWith("校对_")||u.startsWith("已完成_")||u.startsWith("已完成增强_")||u.startsWith("已完成校对_")||u.startsWith("异常_")||u.startsWith("异常增强_")||u.startsWith("异常校对_")||u.startsWith("已停止_")||u.startsWith("已停止增强_")||u.startsWith("已停止校对_"))u.startsWith("任务增强_")?(_=u.substring(5),A=!0):u.startsWith("任务_")||u.startsWith("校对_")?_=u.substring(3):u.startsWith("已完成增强_")?(_=u.substring(6),A=!0):u.startsWith("已完成校对_")?_=u.substring(6):u.startsWith("已完成_")?_=u.substring(4):u.startsWith("异常增强_")?(_=u.substring(5),A=!0):u.startsWith("异常校对_")?_=u.substring(5):u.startsWith("异常_")?_=u.substring(3):u.startsWith("已停止增强_")?(_=u.substring(6),A=!0):u.startsWith("已停止校对_")?_=u.substring(6):u.startsWith("已停止_")&&(_=u.substring(4));else continue;if(_){let z;l.Start>=O.Start&&l.End<=O.End?z="completely_within":l.Start<=O.Start&&l.End>=O.End?z="completely_contains":z="partial_overlap",p.push({taskId:_,control:r,task:R?null:a[_]||null,isEnhanced:A,isEmptyTitle:R,overlapType:z,controlRange:{start:O.Start,end:O.End},selectionRange:{start:l.Start,end:l.End}})}}}catch{continue}return p.length===0?null:{primary:p[0],all:p}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},W=()=>{pt.value=!0},H=()=>{pt.value=!1},oe=async(n,o=!1)=>{W();try{await me(n,o)}finally{H()}},ae=n=>new Promise((o,l)=>{P.show=!0,P.message=n,P.resolveCallback=o,P.rejectCallback=l}),ve=n=>{P.show=!1,n&&P.resolveCallback?P.resolveCallback(!0):P.resolveCallback&&P.resolveCallback(!1),P.resolveCallback=null,P.rejectCallback=null},ye=(n,o,l="error")=>{L.show=!0,L.title=n,L.message=o,L.type=l},He=()=>{L.show=!1,L.title="",L.message="",L.type="error"},vt=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await G.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(V.value=n.data.subject),n.data.stage&&(I.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${V.value})和年级(${I.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},Le=async()=>{try{if(V.value||I.value){t.value+=`<span class="log-item info">正在保存学科(${V.value})和年级(${I.value})设置到服务器...</span><br/>`;const n=await G.setSubjectAndStage(V.value,I.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}},rt=async()=>{try{B.value=2,J.value=B.value===2,J.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${B.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${B.value}）</span><br/>`}catch(n){console.error("检查企业ID失败:",n),t.value+=`<span class="log-item error">检查企业ID失败: ${n.message}</span><br/>`,J.value=!1}},S=(n,o)=>{try{const l=D();if(!l||!n)return!1;const p=l.Comments.Add(n,o);return!0}catch(l){return t.value+=`<span class="log-item error">为范围添加批注失败: ${l.message}</span><br/>`,!1}},h=async(n,o)=>{try{if(!n||!Array.isArray(n))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const l=D();let p=null,i=null;if(l&&l.ContentControls)for(let _=1;_<=l.ContentControls.Count;_++)try{const A=l.ContentControls.Item(_);if((A==null?void 0:A.Title)===`校对_${o}`){p=A,i=A.Range,t.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!i){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const _=a[o];if(_&&_.selectedText)try{const A=l.Range().Find;if(A.ClearFormatting(),A.Text=_.selectedText.substring(0,Math.min(_.selectedText.length,100)),A.Forward=!0,A.Wrap=1,A.Execute()){const R=A.Parent;if(_.selectedText.length>100){const z=R.Start+_.selectedText.length;i=l.Range(R.Start,Math.min(z,l.Range().End))}else i=R;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(A){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${A.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let r=0,u=0,O=[];for(const _ of n){if(!_.mode1||!Array.isArray(_.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const A of _.mode1){if(!A.error_info||!Array.isArray(A.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const R=A.quest_html||"",z=A.quest_type||"";t.value+=`<span class="log-item info">处理${z}题目，发现${A.error_info.length}个错误信息</span><br/>`;for(const X of A.error_info)try{let ne="";if(X.error_info&&(ne+=`【错误类型】${X.error_info}`),X.fix_info&&(ne+=`\r
【修正建议】${X.fix_info}`),X.keywords&&X.keywords.trim()){let ue=p?p.Range:i;le(ne,X.keywords.trim(),-1,p.Range)?(r++,t.value+=`<span class="log-item success">已为关键词"${X.keywords.trim()}"添加批注</span><br/>`):(O.push({comment:ne,keyword:X.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${X.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else O.push({comment:ne,keyword:null})}catch(ne){u++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${ne.message}</span><br/>`}}}if(O.length>0){t.value+=`<span class="log-item info">为整个控件范围添加${O.length}个批注</span><br/>`;for(const _ of O)try{let A=_.comment,R=p?p.Range:i;p.LockContents=!1,S(p.Range,A)?(r++,t.value+=`<span class="log-item success">已为整个范围添加批注${_.keyword?`（关键词：${_.keyword}）`:""}</span><br/>`):u++}catch(A){u++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${A.message}</span><br/>`}}return r>0?(t.value+=`<span class="log-item success">校对任务${o.substring(0,8)}处理完成：成功添加${r}个批注</span><br/>`,u>0&&(t.value+=`<span class="log-item warning">校对任务${o.substring(0,8)}：${u}个批注添加失败</span><br/>`),p.Title=`已完成校对_${o}`,!0):(t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(l){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${l.message}</span><br/>`,!1}};return{docName:e,selected:s,logger:t,map:a,watchedDir:w,subject:V,stage:I,subjectOptions:N,stageOptions:te,fetchWatchedDir:se,clearLog:Z,getCurrentDocument:D,checkDocumentFormat:re,getTaskStatusClass:v,getTaskStatusText:b,getElapsedTime:x,terminateTask:$,stopTaskWithoutRemovingControl:k,run1:Ue,run2:Ve,runCheck:Ne,getHeaderStatusClass:Ge,getRunningTasksCount:at,getCompletedTasksCount:Ze,getReleasableTasksCount:dt,getErrorTasksCount:ot,setupLifecycle:Tt,navigateToTaskControl:nt,forceCleanAllTasks:Ot,ws:Se,wsMessages:Me,initWebSocket:Ie,handleWatcherEvent:Re,urlMonitorTasks:K,monitorUrlForTask:De,stopUrlMonitoring:ge,getUrlMonitorStatus:st,forceUrlCheck:Ke,cleanupUrlMonitoringTasks:kt,tryRemoveTaskPlaceholder:me,isLoading:pt,isSelectionInTaskControl:y,tryRemoveTaskPlaceholderWithLoading:oe,showConfirm:ae,handleConfirm:ve,confirmDialog:P,errorDialog:L,showErrorDialog:ye,hideErrorDialog:He,loadSubjectAndStage:vt,saveSubjectAndStage:Le,enterpriseId:B,isCheckingVisible:J,checkEnterpriseAndSetCheckingVisibility:rt,processCheckingJson:h}}const Cn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Je.getVersionConfig(),selectedEdition:Je.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),c=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${c}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await G.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await G[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await G.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await G.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await G.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await G.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await G.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await G.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await G.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Je.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await ln()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await G.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await G.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await G.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await G.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=G.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=G.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=G.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Je.onVersionChange(e=>{this.versionConfig=Je.getVersionConfig(),this.selectedEdition=Je.getEdition()}),await G.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},$n={class:"file-watcher"},Sn={class:"settings-modal"},_n={class:"modal-content"},En={class:"modal-header"},An={class:"version-tag"},Mn={key:0,class:"user-info"},Dn={key:1,class:"user-info inner-tag"},Pn={class:"header-actions"},On={class:"modal-body"},In={class:"status-section"},Rn={class:"status-item"},Un={key:0,class:"status-item"},Ln={class:"directory-section"},Fn={class:"directory-form"},jn={class:"radio-group"},Wn={class:"radio-item"},Bn={class:"radio-item"},Nn={class:"radio-item"},Vn={key:0,class:"radio-item"},Hn={key:1,class:"directory-section"},zn={class:"directory-form"},qn={class:"form-group"},Jn=["placeholder"],Yn=["disabled"],Kn={key:2,class:"directory-section"},Xn={class:"directory-form"},Gn={class:"form-group"},Zn=["placeholder"],Qn=["disabled"],ea={key:3,class:"directory-section"},ta={class:"directory-form"},sa={class:"form-group"},na=["placeholder"],aa=["disabled"],oa={key:4,class:"events-section"},ra={class:"events-list"},ia={class:"event-time"},la={class:"event-message"},ca={key:1,class:"modal-footer"};function ua(e,s,t,a,c,g){var T,M,w,C,P,L,V,I,B,J,N,ee,te,se,f,Z,D,re,le,v,b,x,k,$;return F(),j("div",$n,[d("div",Sn,[d("div",_n,[d("div",En,[d("h3",null,[cn(Q(c.versionConfig.shortName)+"设置 ",1),d("span",An,Q(c.versionConfig.appVersion),1),g.userInfo?(F(),j("span",Mn,"欢迎您，"+Q(g.userInfo.nickname),1)):q("",!0),((w=(M=(T=g.userInfo)==null?void 0:T.orgs)==null?void 0:M[0])==null?void 0:w.orgId)===2?(F(),j("span",Dn,"内部版本号：1.1.23")):q("",!0)]),d("div",Pn,[d("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>g.handleLogout&&g.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[d("span",{class:"icon-logout"},null,-1)])),d("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),d("div",On,[d("div",In,[d("div",Rn,[s[22]||(s[22]=d("span",{class:"label"},"状态：",-1)),d("span",{class:ke(["status-badge",c.status.status])},Q(c.status.status==="running"?"运行中":"已停止"),3)]),((L=(P=(C=g.userInfo)==null?void 0:C.orgs)==null?void 0:P[0])==null?void 0:L.orgId)===2?(F(),j("div",Un,[s[23]||(s[23]=d("span",{class:"label"},"本次上传：",-1)),d("span",null,Q(c.status.processedFiles||0)+" 个文件",1)])):q("",!0),d("div",Ln,[s[28]||(s[28]=d("h4",null,"保存方式设置",-1)),d("div",Fn,[d("div",jn,[d("label",Wn,[qe(d("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>c.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,c.currentSaveMethod]]),s[24]||(s[24]=d("span",{class:"radio-label"},"方式一",-1))]),d("label",Bn,[qe(d("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>c.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,c.currentSaveMethod]]),s[25]||(s[25]=d("span",{class:"radio-label"},"方式二 (默认)",-1))]),d("label",Nn,[qe(d("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>c.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,c.currentSaveMethod]]),s[26]||(s[26]=d("span",{class:"radio-label"},"方式三",-1))]),((B=(I=(V=g.userInfo)==null?void 0:V.orgs)==null?void 0:I[0])==null?void 0:B.orgId)===2?(F(),j("label",Vn,[qe(d("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>c.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,c.currentSaveMethod]]),s[27]||(s[27]=d("span",{class:"radio-label"},"方式四",-1))])):q("",!0)]),c.saveMethodUpdateMessage?(F(),j("div",{key:0,class:ke(["update-message",c.saveMethodUpdateSuccess?"success":"error"])},Q(c.saveMethodUpdateMessage),3)):q("",!0)])])]),q("",!0),((ee=(N=(J=g.userInfo)==null?void 0:J.orgs)==null?void 0:N[0])==null?void 0:ee.orgId)===2?(F(),j("div",Hn,[s[32]||(s[32]=d("h4",null,"上传目录设置",-1)),d("div",zn,[d("div",qn,[s[31]||(s[31]=d("span",{class:"label"},"路径：",-1)),qe(d("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>c.newWatchDir=m),placeholder:c.status.watchDir||"C:\\Temp"},null,8,Jn),[[Zt,c.newWatchDir]]),d("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>g.updateWatchDir&&g.updateWatchDir(...m)),disabled:c.isUpdating||!c.newWatchDir},Q(c.isUpdating?"更新中...":"更新目录"),9,Yn)]),c.updateMessage?(F(),j("div",{key:0,class:ke(["update-message",c.updateSuccess?"success":"error"])},Q(c.updateMessage),3)):q("",!0)])])):q("",!0),((f=(se=(te=g.userInfo)==null?void 0:te.orgs)==null?void 0:se[0])==null?void 0:f.orgId)===2?(F(),j("div",Kn,[s[34]||(s[34]=d("h4",null,"下载目录设置",-1)),d("div",Xn,[d("div",Gn,[s[33]||(s[33]=d("span",{class:"label"},"路径：",-1)),qe(d("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>c.newDownloadPath=m),placeholder:c.downloadPath||"C:\\Temp\\Downloads"},null,8,Zn),[[Zt,c.newDownloadPath]]),d("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>g.updateDownloadPath&&g.updateDownloadPath(...m)),disabled:c.isUpdatingDownloadPath||!c.newDownloadPath},Q(c.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Qn)]),c.downloadPathUpdateMessage?(F(),j("div",{key:0,class:ke(["update-message",c.downloadPathUpdateSuccess?"success":"error"])},Q(c.downloadPathUpdateMessage),3)):q("",!0)])])):q("",!0),((re=(D=(Z=g.userInfo)==null?void 0:Z.orgs)==null?void 0:D[0])==null?void 0:re.orgId)===2?(F(),j("div",ea,[s[36]||(s[36]=d("h4",null,"配置目录设置",-1)),d("div",ta,[d("div",sa,[s[35]||(s[35]=d("span",{class:"label"},"路径：",-1)),qe(d("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>c.newAddonConfigPath=m),placeholder:c.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,na),[[Zt,c.newAddonConfigPath]]),d("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...m)),disabled:c.isUpdatingAddonConfigPath||!c.newAddonConfigPath},Q(c.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,aa)]),c.addonConfigPathUpdateMessage?(F(),j("div",{key:0,class:ke(["update-message",c.addonConfigPathUpdateSuccess?"success":"error"])},Q(c.addonConfigPathUpdateMessage),3)):q("",!0)])])):q("",!0),((b=(v=(le=g.userInfo)==null?void 0:le.orgs)==null?void 0:v[0])==null?void 0:b.orgId)===2?(F(),j("div",oa,[s[37]||(s[37]=d("h4",null,"最近事件",-1)),d("div",ra,[(F(!0),j(mt,null,_t(c.recentEvents,(m,U)=>(F(),j("div",{key:U,class:"event-item"},[d("span",ia,Q(g.formatTime(m.timestamp)),1),d("span",{class:ke(["event-type",m.eventType])},Q(g.getEventTypeText(m.eventType)),3),d("span",la,Q(g.getEventMessage(m)),1)]))),128))])])):q("",!0)]),q("",!0),(($=(k=(x=g.userInfo)==null?void 0:x.orgs)==null?void 0:k[0])==null?void 0:$.orgId)===2?(F(),j("div",ca,[d("button",{class:ke(["control-btn",c.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>g.controlService&&g.controlService(...m))},Q(c.status.status==="running"?"停止服务":"启动服务"),3)])):q("",!0)])])])}const da=js(Cn,[["render",ua],["__scopeId","data-v-24bf5b08"]]);var Ee="top",je="bottom",We="right",Ae="left",ls="auto",Bt=[Ee,je,We,Ae],At="start",jt="end",pa="clippingParents",Bs="viewport",It="popper",fa="reference",ys=Bt.reduce(function(e,s){return e.concat([s+"-"+At,s+"-"+jt])},[]),Ns=[].concat(Bt,[ls]).reduce(function(e,s){return e.concat([s,s+"-"+At,s+"-"+jt])},[]),ha="beforeRead",va="read",ga="afterRead",ma="beforeMain",ba="main",wa="afterMain",ya="beforeWrite",xa="write",ka="afterWrite",Ta=[ha,va,ga,ma,ba,wa,ya,xa,ka];function tt(e){return e?(e.nodeName||"").toLowerCase():null}function Pe(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function xt(e){var s=Pe(e).Element;return e instanceof s||e instanceof Element}function Fe(e){var s=Pe(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function cs(e){if(typeof ShadowRoot>"u")return!1;var s=Pe(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function Ca(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},c=s.attributes[t]||{},g=s.elements[t];!Fe(g)||!tt(g)||(Object.assign(g.style,a),Object.keys(c).forEach(function(T){var M=c[T];M===!1?g.removeAttribute(T):g.setAttribute(T,M===!0?"":M)}))})}function $a(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var c=s.elements[a],g=s.attributes[a]||{},T=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),M=T.reduce(function(w,C){return w[C]="",w},{});!Fe(c)||!tt(c)||(Object.assign(c.style,M),Object.keys(g).forEach(function(w){c.removeAttribute(w)}))})}}const Vs={name:"applyStyles",enabled:!0,phase:"write",fn:Ca,effect:$a,requires:["computeStyles"]};function et(e){return e.split("-")[0]}var wt=Math.max,Yt=Math.min,Mt=Math.round;function as(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Hs(){return!/^((?!chrome|android).)*safari/i.test(as())}function Dt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),c=1,g=1;s&&Fe(e)&&(c=e.offsetWidth>0&&Mt(a.width)/e.offsetWidth||1,g=e.offsetHeight>0&&Mt(a.height)/e.offsetHeight||1);var T=xt(e)?Pe(e):window,M=T.visualViewport,w=!Hs()&&t,C=(a.left+(w&&M?M.offsetLeft:0))/c,P=(a.top+(w&&M?M.offsetTop:0))/g,L=a.width/c,V=a.height/g;return{width:L,height:V,top:P,right:C+L,bottom:P+V,left:C,x:C,y:P}}function us(e){var s=Dt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function zs(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&cs(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function ct(e){return Pe(e).getComputedStyle(e)}function Sa(e){return["table","td","th"].indexOf(tt(e))>=0}function ht(e){return((xt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Xt(e){return tt(e)==="html"?e:e.assignedSlot||e.parentNode||(cs(e)?e.host:null)||ht(e)}function xs(e){return!Fe(e)||ct(e).position==="fixed"?null:e.offsetParent}function _a(e){var s=/firefox/i.test(as()),t=/Trident/i.test(as());if(t&&Fe(e)){var a=ct(e);if(a.position==="fixed")return null}var c=Xt(e);for(cs(c)&&(c=c.host);Fe(c)&&["html","body"].indexOf(tt(c))<0;){var g=ct(c);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return c;c=c.parentNode}return null}function Nt(e){for(var s=Pe(e),t=xs(e);t&&Sa(t)&&ct(t).position==="static";)t=xs(t);return t&&(tt(t)==="html"||tt(t)==="body"&&ct(t).position==="static")?s:t||_a(e)||s}function ds(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ut(e,s,t){return wt(e,Yt(s,t))}function Ea(e,s,t){var a=Ut(e,s,t);return a>t?t:a}function qs(){return{top:0,right:0,bottom:0,left:0}}function Js(e){return Object.assign({},qs(),e)}function Ys(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var Aa=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Js(typeof s!="number"?s:Ys(s,Bt))};function Ma(e){var s,t=e.state,a=e.name,c=e.options,g=t.elements.arrow,T=t.modifiersData.popperOffsets,M=et(t.placement),w=ds(M),C=[Ae,We].indexOf(M)>=0,P=C?"height":"width";if(!(!g||!T)){var L=Aa(c.padding,t),V=us(g),I=w==="y"?Ee:Ae,B=w==="y"?je:We,J=t.rects.reference[P]+t.rects.reference[w]-T[w]-t.rects.popper[P],N=T[w]-t.rects.reference[w],ee=Nt(g),te=ee?w==="y"?ee.clientHeight||0:ee.clientWidth||0:0,se=J/2-N/2,f=L[I],Z=te-V[P]-L[B],D=te/2-V[P]/2+se,re=Ut(f,D,Z),le=w;t.modifiersData[a]=(s={},s[le]=re,s.centerOffset=re-D,s)}}function Da(e){var s=e.state,t=e.options,a=t.element,c=a===void 0?"[data-popper-arrow]":a;c!=null&&(typeof c=="string"&&(c=s.elements.popper.querySelector(c),!c)||zs(s.elements.popper,c)&&(s.elements.arrow=c))}const Pa={name:"arrow",enabled:!0,phase:"main",fn:Ma,effect:Da,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Pt(e){return e.split("-")[1]}var Oa={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ia(e,s){var t=e.x,a=e.y,c=s.devicePixelRatio||1;return{x:Mt(t*c)/c||0,y:Mt(a*c)/c||0}}function ks(e){var s,t=e.popper,a=e.popperRect,c=e.placement,g=e.variation,T=e.offsets,M=e.position,w=e.gpuAcceleration,C=e.adaptive,P=e.roundOffsets,L=e.isFixed,V=T.x,I=V===void 0?0:V,B=T.y,J=B===void 0?0:B,N=typeof P=="function"?P({x:I,y:J}):{x:I,y:J};I=N.x,J=N.y;var ee=T.hasOwnProperty("x"),te=T.hasOwnProperty("y"),se=Ae,f=Ee,Z=window;if(C){var D=Nt(t),re="clientHeight",le="clientWidth";if(D===Pe(t)&&(D=ht(t),ct(D).position!=="static"&&M==="absolute"&&(re="scrollHeight",le="scrollWidth")),D=D,c===Ee||(c===Ae||c===We)&&g===jt){f=je;var v=L&&D===Z&&Z.visualViewport?Z.visualViewport.height:D[re];J-=v-a.height,J*=w?1:-1}if(c===Ae||(c===Ee||c===je)&&g===jt){se=We;var b=L&&D===Z&&Z.visualViewport?Z.visualViewport.width:D[le];I-=b-a.width,I*=w?1:-1}}var x=Object.assign({position:M},C&&Oa),k=P===!0?Ia({x:I,y:J},Pe(t)):{x:I,y:J};if(I=k.x,J=k.y,w){var $;return Object.assign({},x,($={},$[f]=te?"0":"",$[se]=ee?"0":"",$.transform=(Z.devicePixelRatio||1)<=1?"translate("+I+"px, "+J+"px)":"translate3d("+I+"px, "+J+"px, 0)",$))}return Object.assign({},x,(s={},s[f]=te?J+"px":"",s[se]=ee?I+"px":"",s.transform="",s))}function Ra(e){var s=e.state,t=e.options,a=t.gpuAcceleration,c=a===void 0?!0:a,g=t.adaptive,T=g===void 0?!0:g,M=t.roundOffsets,w=M===void 0?!0:M,C={placement:et(s.placement),variation:Pt(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:c,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,ks(Object.assign({},C,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:T,roundOffsets:w})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,ks(Object.assign({},C,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:w})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Ua={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ra,data:{}};var Ht={passive:!0};function La(e){var s=e.state,t=e.instance,a=e.options,c=a.scroll,g=c===void 0?!0:c,T=a.resize,M=T===void 0?!0:T,w=Pe(s.elements.popper),C=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&C.forEach(function(P){P.addEventListener("scroll",t.update,Ht)}),M&&w.addEventListener("resize",t.update,Ht),function(){g&&C.forEach(function(P){P.removeEventListener("scroll",t.update,Ht)}),M&&w.removeEventListener("resize",t.update,Ht)}}const Fa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:La,data:{}};var ja={left:"right",right:"left",bottom:"top",top:"bottom"};function qt(e){return e.replace(/left|right|bottom|top/g,function(s){return ja[s]})}var Wa={start:"end",end:"start"};function Ts(e){return e.replace(/start|end/g,function(s){return Wa[s]})}function ps(e){var s=Pe(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function fs(e){return Dt(ht(e)).left+ps(e).scrollLeft}function Ba(e,s){var t=Pe(e),a=ht(e),c=t.visualViewport,g=a.clientWidth,T=a.clientHeight,M=0,w=0;if(c){g=c.width,T=c.height;var C=Hs();(C||!C&&s==="fixed")&&(M=c.offsetLeft,w=c.offsetTop)}return{width:g,height:T,x:M+fs(e),y:w}}function Na(e){var s,t=ht(e),a=ps(e),c=(s=e.ownerDocument)==null?void 0:s.body,g=wt(t.scrollWidth,t.clientWidth,c?c.scrollWidth:0,c?c.clientWidth:0),T=wt(t.scrollHeight,t.clientHeight,c?c.scrollHeight:0,c?c.clientHeight:0),M=-a.scrollLeft+fs(e),w=-a.scrollTop;return ct(c||t).direction==="rtl"&&(M+=wt(t.clientWidth,c?c.clientWidth:0)-g),{width:g,height:T,x:M,y:w}}function hs(e){var s=ct(e),t=s.overflow,a=s.overflowX,c=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+c+a)}function Ks(e){return["html","body","#document"].indexOf(tt(e))>=0?e.ownerDocument.body:Fe(e)&&hs(e)?e:Ks(Xt(e))}function Lt(e,s){var t;s===void 0&&(s=[]);var a=Ks(e),c=a===((t=e.ownerDocument)==null?void 0:t.body),g=Pe(a),T=c?[g].concat(g.visualViewport||[],hs(a)?a:[]):a,M=s.concat(T);return c?M:M.concat(Lt(Xt(T)))}function os(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Va(e,s){var t=Dt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Cs(e,s,t){return s===Bs?os(Ba(e,t)):xt(s)?Va(s,t):os(Na(ht(e)))}function Ha(e){var s=Lt(Xt(e)),t=["absolute","fixed"].indexOf(ct(e).position)>=0,a=t&&Fe(e)?Nt(e):e;return xt(a)?s.filter(function(c){return xt(c)&&zs(c,a)&&tt(c)!=="body"}):[]}function za(e,s,t,a){var c=s==="clippingParents"?Ha(e):[].concat(s),g=[].concat(c,[t]),T=g[0],M=g.reduce(function(w,C){var P=Cs(e,C,a);return w.top=wt(P.top,w.top),w.right=Yt(P.right,w.right),w.bottom=Yt(P.bottom,w.bottom),w.left=wt(P.left,w.left),w},Cs(e,T,a));return M.width=M.right-M.left,M.height=M.bottom-M.top,M.x=M.left,M.y=M.top,M}function Xs(e){var s=e.reference,t=e.element,a=e.placement,c=a?et(a):null,g=a?Pt(a):null,T=s.x+s.width/2-t.width/2,M=s.y+s.height/2-t.height/2,w;switch(c){case Ee:w={x:T,y:s.y-t.height};break;case je:w={x:T,y:s.y+s.height};break;case We:w={x:s.x+s.width,y:M};break;case Ae:w={x:s.x-t.width,y:M};break;default:w={x:s.x,y:s.y}}var C=c?ds(c):null;if(C!=null){var P=C==="y"?"height":"width";switch(g){case At:w[C]=w[C]-(s[P]/2-t[P]/2);break;case jt:w[C]=w[C]+(s[P]/2-t[P]/2);break}}return w}function Wt(e,s){s===void 0&&(s={});var t=s,a=t.placement,c=a===void 0?e.placement:a,g=t.strategy,T=g===void 0?e.strategy:g,M=t.boundary,w=M===void 0?pa:M,C=t.rootBoundary,P=C===void 0?Bs:C,L=t.elementContext,V=L===void 0?It:L,I=t.altBoundary,B=I===void 0?!1:I,J=t.padding,N=J===void 0?0:J,ee=Js(typeof N!="number"?N:Ys(N,Bt)),te=V===It?fa:It,se=e.rects.popper,f=e.elements[B?te:V],Z=za(xt(f)?f:f.contextElement||ht(e.elements.popper),w,P,T),D=Dt(e.elements.reference),re=Xs({reference:D,element:se,strategy:"absolute",placement:c}),le=os(Object.assign({},se,re)),v=V===It?le:D,b={top:Z.top-v.top+ee.top,bottom:v.bottom-Z.bottom+ee.bottom,left:Z.left-v.left+ee.left,right:v.right-Z.right+ee.right},x=e.modifiersData.offset;if(V===It&&x){var k=x[c];Object.keys(b).forEach(function($){var m=[We,je].indexOf($)>=0?1:-1,U=[Ee,je].indexOf($)>=0?"y":"x";b[$]+=k[U]*m})}return b}function qa(e,s){s===void 0&&(s={});var t=s,a=t.placement,c=t.boundary,g=t.rootBoundary,T=t.padding,M=t.flipVariations,w=t.allowedAutoPlacements,C=w===void 0?Ns:w,P=Pt(a),L=P?M?ys:ys.filter(function(B){return Pt(B)===P}):Bt,V=L.filter(function(B){return C.indexOf(B)>=0});V.length===0&&(V=L);var I=V.reduce(function(B,J){return B[J]=Wt(e,{placement:J,boundary:c,rootBoundary:g,padding:T})[et(J)],B},{});return Object.keys(I).sort(function(B,J){return I[B]-I[J]})}function Ja(e){if(et(e)===ls)return[];var s=qt(e);return[Ts(e),s,Ts(s)]}function Ya(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var c=t.mainAxis,g=c===void 0?!0:c,T=t.altAxis,M=T===void 0?!0:T,w=t.fallbackPlacements,C=t.padding,P=t.boundary,L=t.rootBoundary,V=t.altBoundary,I=t.flipVariations,B=I===void 0?!0:I,J=t.allowedAutoPlacements,N=s.options.placement,ee=et(N),te=ee===N,se=w||(te||!B?[qt(N)]:Ja(N)),f=[N].concat(se).reduce(function(we,Te){return we.concat(et(Te)===ls?qa(s,{placement:Te,boundary:P,rootBoundary:L,padding:C,flipVariations:B,allowedAutoPlacements:J}):Te)},[]),Z=s.rects.reference,D=s.rects.popper,re=new Map,le=!0,v=f[0],b=0;b<f.length;b++){var x=f[b],k=et(x),$=Pt(x)===At,m=[Ee,je].indexOf(k)>=0,U=m?"width":"height",E=Wt(s,{placement:x,boundary:P,rootBoundary:L,altBoundary:V,padding:C}),Y=m?$?We:Ae:$?je:Ee;Z[U]>D[U]&&(Y=qt(Y));var pe=qt(Y),he=[];if(g&&he.push(E[k]<=0),M&&he.push(E[Y]<=0,E[pe]<=0),he.every(function(we){return we})){v=x,le=!1;break}re.set(x,he)}if(le)for(var $e=B?3:1,Oe=function(Te){var _e=f.find(function(Ie){var K=re.get(Ie);if(K)return K.slice(0,Te).every(function(De){return De})});if(_e)return v=_e,"break"},Se=$e;Se>0;Se--){var Me=Oe(Se);if(Me==="break")break}s.placement!==v&&(s.modifiersData[a]._skip=!0,s.placement=v,s.reset=!0)}}const Ka={name:"flip",enabled:!0,phase:"main",fn:Ya,requiresIfExists:["offset"],data:{_skip:!1}};function $s(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function Ss(e){return[Ee,We,je,Ae].some(function(s){return e[s]>=0})}function Xa(e){var s=e.state,t=e.name,a=s.rects.reference,c=s.rects.popper,g=s.modifiersData.preventOverflow,T=Wt(s,{elementContext:"reference"}),M=Wt(s,{altBoundary:!0}),w=$s(T,a),C=$s(M,c,g),P=Ss(w),L=Ss(C);s.modifiersData[t]={referenceClippingOffsets:w,popperEscapeOffsets:C,isReferenceHidden:P,hasPopperEscaped:L},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":P,"data-popper-escaped":L})}const Ga={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Xa};function Za(e,s,t){var a=et(e),c=[Ae,Ee].indexOf(a)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,T=g[0],M=g[1];return T=T||0,M=(M||0)*c,[Ae,We].indexOf(a)>=0?{x:M,y:T}:{x:T,y:M}}function Qa(e){var s=e.state,t=e.options,a=e.name,c=t.offset,g=c===void 0?[0,0]:c,T=Ns.reduce(function(P,L){return P[L]=Za(L,s.rects,g),P},{}),M=T[s.placement],w=M.x,C=M.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=w,s.modifiersData.popperOffsets.y+=C),s.modifiersData[a]=T}const eo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Qa};function to(e){var s=e.state,t=e.name;s.modifiersData[t]=Xs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const so={name:"popperOffsets",enabled:!0,phase:"read",fn:to,data:{}};function no(e){return e==="x"?"y":"x"}function ao(e){var s=e.state,t=e.options,a=e.name,c=t.mainAxis,g=c===void 0?!0:c,T=t.altAxis,M=T===void 0?!1:T,w=t.boundary,C=t.rootBoundary,P=t.altBoundary,L=t.padding,V=t.tether,I=V===void 0?!0:V,B=t.tetherOffset,J=B===void 0?0:B,N=Wt(s,{boundary:w,rootBoundary:C,padding:L,altBoundary:P}),ee=et(s.placement),te=Pt(s.placement),se=!te,f=ds(ee),Z=no(f),D=s.modifiersData.popperOffsets,re=s.rects.reference,le=s.rects.popper,v=typeof J=="function"?J(Object.assign({},s.rects,{placement:s.placement})):J,b=typeof v=="number"?{mainAxis:v,altAxis:v}:Object.assign({mainAxis:0,altAxis:0},v),x=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,k={x:0,y:0};if(D){if(g){var $,m=f==="y"?Ee:Ae,U=f==="y"?je:We,E=f==="y"?"height":"width",Y=D[f],pe=Y+N[m],he=Y-N[U],$e=I?-le[E]/2:0,Oe=te===At?re[E]:le[E],Se=te===At?-le[E]:-re[E],Me=s.elements.arrow,we=I&&Me?us(Me):{width:0,height:0},Te=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:qs(),_e=Te[m],Ie=Te[U],K=Ut(0,re[E],we[E]),De=se?re[E]/2-$e-K-_e-b.mainAxis:Oe-K-_e-b.mainAxis,ge=se?-re[E]/2+$e+K+Ie+b.mainAxis:Se+K+Ie+b.mainAxis,st=s.elements.arrow&&Nt(s.elements.arrow),Ke=st?f==="y"?st.clientTop||0:st.clientLeft||0:0,Re=($=x==null?void 0:x[f])!=null?$:0,fe=Y+De-Re-Ke,ut=Y+ge-Re,Xe=Ut(I?Yt(pe,fe):pe,Y,I?wt(he,ut):he);D[f]=Xe,k[f]=Xe-Y}if(M){var me,nt=f==="x"?Ee:Ae,Be=f==="x"?je:We,Ce=D[Z],Ue=Z==="y"?"height":"width",Ne=Ce+N[nt],Ve=Ce-N[Be],Ge=[Ee,Ae].indexOf(ee)!==-1,at=(me=x==null?void 0:x[Z])!=null?me:0,Ze=Ge?Ne:Ce-re[Ue]-le[Ue]-at+b.altAxis,dt=Ge?Ce+re[Ue]+le[Ue]-at-b.altAxis:Ve,ot=I&&Ge?Ea(Ze,Ce,dt):Ut(I?Ze:Ne,Ce,I?dt:Ve);D[Z]=ot,k[Z]=ot-Ce}s.modifiersData[a]=k}}const oo={name:"preventOverflow",enabled:!0,phase:"main",fn:ao,requiresIfExists:["offset"]};function ro(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function io(e){return e===Pe(e)||!Fe(e)?ps(e):ro(e)}function lo(e){var s=e.getBoundingClientRect(),t=Mt(s.width)/e.offsetWidth||1,a=Mt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function co(e,s,t){t===void 0&&(t=!1);var a=Fe(s),c=Fe(s)&&lo(s),g=ht(s),T=Dt(e,c,t),M={scrollLeft:0,scrollTop:0},w={x:0,y:0};return(a||!a&&!t)&&((tt(s)!=="body"||hs(g))&&(M=io(s)),Fe(s)?(w=Dt(s,!0),w.x+=s.clientLeft,w.y+=s.clientTop):g&&(w.x=fs(g))),{x:T.left+M.scrollLeft-w.x,y:T.top+M.scrollTop-w.y,width:T.width,height:T.height}}function uo(e){var s=new Map,t=new Set,a=[];e.forEach(function(g){s.set(g.name,g)});function c(g){t.add(g.name);var T=[].concat(g.requires||[],g.requiresIfExists||[]);T.forEach(function(M){if(!t.has(M)){var w=s.get(M);w&&c(w)}}),a.push(g)}return e.forEach(function(g){t.has(g.name)||c(g)}),a}function po(e){var s=uo(e);return Ta.reduce(function(t,a){return t.concat(s.filter(function(c){return c.phase===a}))},[])}function fo(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function ho(e){var s=e.reduce(function(t,a){var c=t[a.name];return t[a.name]=c?Object.assign({},c,a,{options:Object.assign({},c.options,a.options),data:Object.assign({},c.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var _s={placement:"bottom",modifiers:[],strategy:"absolute"};function Es(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function vo(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,c=s.defaultOptions,g=c===void 0?_s:c;return function(M,w,C){C===void 0&&(C=g);var P={placement:"bottom",orderedModifiers:[],options:Object.assign({},_s,g),modifiersData:{},elements:{reference:M,popper:w},attributes:{},styles:{}},L=[],V=!1,I={state:P,setOptions:function(ee){var te=typeof ee=="function"?ee(P.options):ee;J(),P.options=Object.assign({},g,P.options,te),P.scrollParents={reference:xt(M)?Lt(M):M.contextElement?Lt(M.contextElement):[],popper:Lt(w)};var se=po(ho([].concat(a,P.options.modifiers)));return P.orderedModifiers=se.filter(function(f){return f.enabled}),B(),I.update()},forceUpdate:function(){if(!V){var ee=P.elements,te=ee.reference,se=ee.popper;if(Es(te,se)){P.rects={reference:co(te,Nt(se),P.options.strategy==="fixed"),popper:us(se)},P.reset=!1,P.placement=P.options.placement,P.orderedModifiers.forEach(function(b){return P.modifiersData[b.name]=Object.assign({},b.data)});for(var f=0;f<P.orderedModifiers.length;f++){if(P.reset===!0){P.reset=!1,f=-1;continue}var Z=P.orderedModifiers[f],D=Z.fn,re=Z.options,le=re===void 0?{}:re,v=Z.name;typeof D=="function"&&(P=D({state:P,options:le,name:v,instance:I})||P)}}}},update:fo(function(){return new Promise(function(N){I.forceUpdate(),N(P)})}),destroy:function(){J(),V=!0}};if(!Es(M,w))return I;I.setOptions(C).then(function(N){!V&&C.onFirstUpdate&&C.onFirstUpdate(N)});function B(){P.orderedModifiers.forEach(function(N){var ee=N.name,te=N.options,se=te===void 0?{}:te,f=N.effect;if(typeof f=="function"){var Z=f({state:P,name:ee,instance:I,options:se}),D=function(){};L.push(Z||D)}})}function J(){L.forEach(function(N){return N()}),L=[]}return I}}var go=[Fa,so,Ua,Vs,eo,Ka,oo,Pa,Ga],mo=vo({defaultModifiers:go}),bo="tippy-box",Gs="tippy-content",wo="tippy-backdrop",Zs="tippy-arrow",Qs="tippy-svg-arrow",bt={passive:!0,capture:!0},en=function(){return document.body};function es(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function vs(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function tn(e,s){return typeof e=="function"?e.apply(void 0,s):e}function As(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function yo(e){return e.split(/\s+/).filter(Boolean)}function Et(e){return[].concat(e)}function Ms(e,s){e.indexOf(s)===-1&&e.push(s)}function xo(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function ko(e){return e.split("-")[0]}function Kt(e){return[].slice.call(e)}function Ds(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Ft(){return document.createElement("div")}function Gt(e){return["Element","Fragment"].some(function(s){return vs(e,s)})}function To(e){return vs(e,"NodeList")}function Co(e){return vs(e,"MouseEvent")}function $o(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function So(e){return Gt(e)?[e]:To(e)?Kt(e):Array.isArray(e)?e:Kt(document.querySelectorAll(e))}function ts(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function Ps(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function _o(e){var s,t=Et(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function Eo(e,s){var t=s.clientX,a=s.clientY;return e.every(function(c){var g=c.popperRect,T=c.popperState,M=c.props,w=M.interactiveBorder,C=ko(T.placement),P=T.modifiersData.offset;if(!P)return!0;var L=C==="bottom"?P.top.y:0,V=C==="top"?P.bottom.y:0,I=C==="right"?P.left.x:0,B=C==="left"?P.right.x:0,J=g.top-a+L>w,N=a-g.bottom-V>w,ee=g.left-t+I>w,te=t-g.right-B>w;return J||N||ee||te})}function ss(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(c){e[a](c,t)})}function Os(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var Qe={isTouch:!1},Is=0;function Ao(){Qe.isTouch||(Qe.isTouch=!0,window.performance&&document.addEventListener("mousemove",sn))}function sn(){var e=performance.now();e-Is<20&&(Qe.isTouch=!1,document.removeEventListener("mousemove",sn)),Is=e}function Mo(){var e=document.activeElement;if($o(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function Do(){document.addEventListener("touchstart",Ao,bt),window.addEventListener("blur",Mo)}var Po=typeof window<"u"&&typeof document<"u",Oo=Po?!!window.msCrypto:!1,Io={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ro={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Ye=Object.assign({appendTo:en,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Io,Ro),Uo=Object.keys(Ye),Lo=function(s){var t=Object.keys(s);t.forEach(function(a){Ye[a]=s[a]})};function nn(e){var s=e.plugins||[],t=s.reduce(function(a,c){var g=c.name,T=c.defaultValue;if(g){var M;a[g]=e[g]!==void 0?e[g]:(M=Ye[g])!=null?M:T}return a},{});return Object.assign({},e,t)}function Fo(e,s){var t=s?Object.keys(nn(Object.assign({},Ye,{plugins:s}))):Uo,a=t.reduce(function(c,g){var T=(e.getAttribute("data-tippy-"+g)||"").trim();if(!T)return c;if(g==="content")c[g]=T;else try{c[g]=JSON.parse(T)}catch{c[g]=T}return c},{});return a}function Rs(e,s){var t=Object.assign({},s,{content:tn(s.content,[e])},s.ignoreAttributes?{}:Fo(e,s.plugins));return t.aria=Object.assign({},Ye.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var jo=function(){return"innerHTML"};function rs(e,s){e[jo()]=s}function Us(e){var s=Ft();return e===!0?s.className=Zs:(s.className=Qs,Gt(e)?s.appendChild(e):rs(s,e)),s}function Ls(e,s){Gt(s.content)?(rs(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?rs(e,s.content):e.textContent=s.content)}function is(e){var s=e.firstElementChild,t=Kt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(Gs)}),arrow:t.find(function(a){return a.classList.contains(Zs)||a.classList.contains(Qs)}),backdrop:t.find(function(a){return a.classList.contains(wo)})}}function an(e){var s=Ft(),t=Ft();t.className=bo,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=Ft();a.className=Gs,a.setAttribute("data-state","hidden"),Ls(a,e.props),s.appendChild(t),t.appendChild(a),c(e.props,e.props);function c(g,T){var M=is(s),w=M.box,C=M.content,P=M.arrow;T.theme?w.setAttribute("data-theme",T.theme):w.removeAttribute("data-theme"),typeof T.animation=="string"?w.setAttribute("data-animation",T.animation):w.removeAttribute("data-animation"),T.inertia?w.setAttribute("data-inertia",""):w.removeAttribute("data-inertia"),w.style.maxWidth=typeof T.maxWidth=="number"?T.maxWidth+"px":T.maxWidth,T.role?w.setAttribute("role",T.role):w.removeAttribute("role"),(g.content!==T.content||g.allowHTML!==T.allowHTML)&&Ls(C,e.props),T.arrow?P?g.arrow!==T.arrow&&(w.removeChild(P),w.appendChild(Us(T.arrow))):w.appendChild(Us(T.arrow)):P&&w.removeChild(P)}return{popper:s,onUpdate:c}}an.$$tippy=!0;var Wo=1,zt=[],ns=[];function Bo(e,s){var t=Rs(e,Object.assign({},Ye,nn(Ds(s)))),a,c,g,T=!1,M=!1,w=!1,C=!1,P,L,V,I=[],B=As(fe,t.interactiveDebounce),J,N=Wo++,ee=null,te=xo(t.plugins),se={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},f={id:N,reference:e,popper:Ft(),popperInstance:ee,props:t,state:se,plugins:te,clearDelayTimeouts:Ze,setProps:dt,setContent:ot,show:Ot,hide:kt,hideWithInteractivity:Tt,enable:Ge,disable:at,unmount:Ct,destroy:pt};if(!t.render)return f;var Z=t.render(f),D=Z.popper,re=Z.onUpdate;D.setAttribute("data-tippy-root",""),D.id="tippy-"+f.id,f.popper=D,e._tippy=f,D._tippy=f;var le=te.map(function(y){return y.fn(f)}),v=e.hasAttribute("aria-expanded");return st(),$e(),Y(),pe("onCreate",[f]),t.showOnCreate&&Ne(),D.addEventListener("mouseenter",function(){f.props.interactive&&f.state.isVisible&&f.clearDelayTimeouts()}),D.addEventListener("mouseleave",function(){f.props.interactive&&f.props.trigger.indexOf("mouseenter")>=0&&m().addEventListener("mousemove",B)}),f;function b(){var y=f.props.touch;return Array.isArray(y)?y:[y,0]}function x(){return b()[0]==="hold"}function k(){var y;return!!((y=f.props.render)!=null&&y.$$tippy)}function $(){return J||e}function m(){var y=$().parentNode;return y?_o(y):document}function U(){return is(D)}function E(y){return f.state.isMounted&&!f.state.isVisible||Qe.isTouch||P&&P.type==="focus"?0:es(f.props.delay,y?0:1,Ye.delay)}function Y(y){y===void 0&&(y=!1),D.style.pointerEvents=f.props.interactive&&!y?"":"none",D.style.zIndex=""+f.props.zIndex}function pe(y,W,H){if(H===void 0&&(H=!0),le.forEach(function(ae){ae[y]&&ae[y].apply(ae,W)}),H){var oe;(oe=f.props)[y].apply(oe,W)}}function he(){var y=f.props.aria;if(y.content){var W="aria-"+y.content,H=D.id,oe=Et(f.props.triggerTarget||e);oe.forEach(function(ae){var ve=ae.getAttribute(W);if(f.state.isVisible)ae.setAttribute(W,ve?ve+" "+H:H);else{var ye=ve&&ve.replace(H,"").trim();ye?ae.setAttribute(W,ye):ae.removeAttribute(W)}})}}function $e(){if(!(v||!f.props.aria.expanded)){var y=Et(f.props.triggerTarget||e);y.forEach(function(W){f.props.interactive?W.setAttribute("aria-expanded",f.state.isVisible&&W===$()?"true":"false"):W.removeAttribute("aria-expanded")})}}function Oe(){m().removeEventListener("mousemove",B),zt=zt.filter(function(y){return y!==B})}function Se(y){if(!(Qe.isTouch&&(w||y.type==="mousedown"))){var W=y.composedPath&&y.composedPath()[0]||y.target;if(!(f.props.interactive&&Os(D,W))){if(Et(f.props.triggerTarget||e).some(function(H){return Os(H,W)})){if(Qe.isTouch||f.state.isVisible&&f.props.trigger.indexOf("click")>=0)return}else pe("onClickOutside",[f,y]);f.props.hideOnClick===!0&&(f.clearDelayTimeouts(),f.hide(),M=!0,setTimeout(function(){M=!1}),f.state.isMounted||_e())}}}function Me(){w=!0}function we(){w=!1}function Te(){var y=m();y.addEventListener("mousedown",Se,!0),y.addEventListener("touchend",Se,bt),y.addEventListener("touchstart",we,bt),y.addEventListener("touchmove",Me,bt)}function _e(){var y=m();y.removeEventListener("mousedown",Se,!0),y.removeEventListener("touchend",Se,bt),y.removeEventListener("touchstart",we,bt),y.removeEventListener("touchmove",Me,bt)}function Ie(y,W){De(y,function(){!f.state.isVisible&&D.parentNode&&D.parentNode.contains(D)&&W()})}function K(y,W){De(y,W)}function De(y,W){var H=U().box;function oe(ae){ae.target===H&&(ss(H,"remove",oe),W())}if(y===0)return W();ss(H,"remove",L),ss(H,"add",oe),L=oe}function ge(y,W,H){H===void 0&&(H=!1);var oe=Et(f.props.triggerTarget||e);oe.forEach(function(ae){ae.addEventListener(y,W,H),I.push({node:ae,eventType:y,handler:W,options:H})})}function st(){x()&&(ge("touchstart",Re,{passive:!0}),ge("touchend",ut,{passive:!0})),yo(f.props.trigger).forEach(function(y){if(y!=="manual")switch(ge(y,Re),y){case"mouseenter":ge("mouseleave",ut);break;case"focus":ge(Oo?"focusout":"blur",Xe);break;case"focusin":ge("focusout",Xe);break}})}function Ke(){I.forEach(function(y){var W=y.node,H=y.eventType,oe=y.handler,ae=y.options;W.removeEventListener(H,oe,ae)}),I=[]}function Re(y){var W,H=!1;if(!(!f.state.isEnabled||me(y)||M)){var oe=((W=P)==null?void 0:W.type)==="focus";P=y,J=y.currentTarget,$e(),!f.state.isVisible&&Co(y)&&zt.forEach(function(ae){return ae(y)}),y.type==="click"&&(f.props.trigger.indexOf("mouseenter")<0||T)&&f.props.hideOnClick!==!1&&f.state.isVisible?H=!0:Ne(y),y.type==="click"&&(T=!H),H&&!oe&&Ve(y)}}function fe(y){var W=y.target,H=$().contains(W)||D.contains(W);if(!(y.type==="mousemove"&&H)){var oe=Ue().concat(D).map(function(ae){var ve,ye=ae._tippy,He=(ve=ye.popperInstance)==null?void 0:ve.state;return He?{popperRect:ae.getBoundingClientRect(),popperState:He,props:t}:null}).filter(Boolean);Eo(oe,y)&&(Oe(),Ve(y))}}function ut(y){var W=me(y)||f.props.trigger.indexOf("click")>=0&&T;if(!W){if(f.props.interactive){f.hideWithInteractivity(y);return}Ve(y)}}function Xe(y){f.props.trigger.indexOf("focusin")<0&&y.target!==$()||f.props.interactive&&y.relatedTarget&&D.contains(y.relatedTarget)||Ve(y)}function me(y){return Qe.isTouch?x()!==y.type.indexOf("touch")>=0:!1}function nt(){Be();var y=f.props,W=y.popperOptions,H=y.placement,oe=y.offset,ae=y.getReferenceClientRect,ve=y.moveTransition,ye=k()?is(D).arrow:null,He=ae?{getBoundingClientRect:ae,contextElement:ae.contextElement||$()}:e,vt={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(S){var h=S.state;if(k()){var n=U(),o=n.box;["placement","reference-hidden","escaped"].forEach(function(l){l==="placement"?o.setAttribute("data-placement",h.placement):h.attributes.popper["data-popper-"+l]?o.setAttribute("data-"+l,""):o.removeAttribute("data-"+l)}),h.attributes.popper={}}}},Le=[{name:"offset",options:{offset:oe}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!ve}},vt];k()&&ye&&Le.push({name:"arrow",options:{element:ye,padding:3}}),Le.push.apply(Le,(W==null?void 0:W.modifiers)||[]),f.popperInstance=mo(He,D,Object.assign({},W,{placement:H,onFirstUpdate:V,modifiers:Le}))}function Be(){f.popperInstance&&(f.popperInstance.destroy(),f.popperInstance=null)}function Ce(){var y=f.props.appendTo,W,H=$();f.props.interactive&&y===en||y==="parent"?W=H.parentNode:W=tn(y,[H]),W.contains(D)||W.appendChild(D),f.state.isMounted=!0,nt()}function Ue(){return Kt(D.querySelectorAll("[data-tippy-root]"))}function Ne(y){f.clearDelayTimeouts(),y&&pe("onTrigger",[f,y]),Te();var W=E(!0),H=b(),oe=H[0],ae=H[1];Qe.isTouch&&oe==="hold"&&ae&&(W=ae),W?a=setTimeout(function(){f.show()},W):f.show()}function Ve(y){if(f.clearDelayTimeouts(),pe("onUntrigger",[f,y]),!f.state.isVisible){_e();return}if(!(f.props.trigger.indexOf("mouseenter")>=0&&f.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(y.type)>=0&&T)){var W=E(!1);W?c=setTimeout(function(){f.state.isVisible&&f.hide()},W):g=requestAnimationFrame(function(){f.hide()})}}function Ge(){f.state.isEnabled=!0}function at(){f.hide(),f.state.isEnabled=!1}function Ze(){clearTimeout(a),clearTimeout(c),cancelAnimationFrame(g)}function dt(y){if(!f.state.isDestroyed){pe("onBeforeUpdate",[f,y]),Ke();var W=f.props,H=Rs(e,Object.assign({},W,Ds(y),{ignoreAttributes:!0}));f.props=H,st(),W.interactiveDebounce!==H.interactiveDebounce&&(Oe(),B=As(fe,H.interactiveDebounce)),W.triggerTarget&&!H.triggerTarget?Et(W.triggerTarget).forEach(function(oe){oe.removeAttribute("aria-expanded")}):H.triggerTarget&&e.removeAttribute("aria-expanded"),$e(),Y(),re&&re(W,H),f.popperInstance&&(nt(),Ue().forEach(function(oe){requestAnimationFrame(oe._tippy.popperInstance.forceUpdate)})),pe("onAfterUpdate",[f,y])}}function ot(y){f.setProps({content:y})}function Ot(){var y=f.state.isVisible,W=f.state.isDestroyed,H=!f.state.isEnabled,oe=Qe.isTouch&&!f.props.touch,ae=es(f.props.duration,0,Ye.duration);if(!(y||W||H||oe)&&!$().hasAttribute("disabled")&&(pe("onShow",[f],!1),f.props.onShow(f)!==!1)){if(f.state.isVisible=!0,k()&&(D.style.visibility="visible"),Y(),Te(),f.state.isMounted||(D.style.transition="none"),k()){var ve=U(),ye=ve.box,He=ve.content;ts([ye,He],0)}V=function(){var Le;if(!(!f.state.isVisible||C)){if(C=!0,D.offsetHeight,D.style.transition=f.props.moveTransition,k()&&f.props.animation){var rt=U(),S=rt.box,h=rt.content;ts([S,h],ae),Ps([S,h],"visible")}he(),$e(),Ms(ns,f),(Le=f.popperInstance)==null||Le.forceUpdate(),pe("onMount",[f]),f.props.animation&&k()&&K(ae,function(){f.state.isShown=!0,pe("onShown",[f])})}},Ce()}}function kt(){var y=!f.state.isVisible,W=f.state.isDestroyed,H=!f.state.isEnabled,oe=es(f.props.duration,1,Ye.duration);if(!(y||W||H)&&(pe("onHide",[f],!1),f.props.onHide(f)!==!1)){if(f.state.isVisible=!1,f.state.isShown=!1,C=!1,T=!1,k()&&(D.style.visibility="hidden"),Oe(),_e(),Y(!0),k()){var ae=U(),ve=ae.box,ye=ae.content;f.props.animation&&(ts([ve,ye],oe),Ps([ve,ye],"hidden"))}he(),$e(),f.props.animation?k()&&Ie(oe,f.unmount):f.unmount()}}function Tt(y){m().addEventListener("mousemove",B),Ms(zt,B),B(y)}function Ct(){f.state.isVisible&&f.hide(),f.state.isMounted&&(Be(),Ue().forEach(function(y){y._tippy.unmount()}),D.parentNode&&D.parentNode.removeChild(D),ns=ns.filter(function(y){return y!==f}),f.state.isMounted=!1,pe("onHidden",[f]))}function pt(){f.state.isDestroyed||(f.clearDelayTimeouts(),f.unmount(),Ke(),delete e._tippy,f.state.isDestroyed=!0,pe("onDestroy",[f]))}}function yt(e,s){s===void 0&&(s={});var t=Ye.plugins.concat(s.plugins||[]);Do();var a=Object.assign({},s,{plugins:t}),c=So(e),g=c.reduce(function(T,M){var w=M&&Bo(M,a);return w&&T.push(w),T},[]);return Gt(e)?g[0]:g}yt.defaultProps=Ye;yt.setDefaultProps=Lo;yt.currentInput=Qe;Object.assign({},Vs,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});yt.setDefaultProps({render:an});const No={class:"task-pane"},Vo={key:0,class:"loading-overlay"},Ho={key:1,class:"format-error-overlay"},zo={class:"format-error-content"},qo={class:"format-error-message"},Jo={class:"format-error-actions"},Yo={class:"doc-header"},Ko={class:"doc-title"},Xo={class:"action-area"},Go={class:"select-container"},Zo={class:"select-group"},Qo=["disabled"],er=["value"],tr={class:"select-group"},sr=["disabled"],nr=["value"],ar=["title"],or={key:0,class:"science-warning"},rr={class:"action-buttons"},ir=["disabled"],lr={class:"btn-content"},cr={key:0,class:"button-loader"},ur=["disabled"],dr={class:"btn-content"},pr={key:0,class:"button-loader"},fr=["disabled"],hr={class:"btn-content"},vr={key:0,class:"button-loader"},gr={class:"content-area"},mr={class:"modal-header"},br={class:"modal-body"},wr={class:"selection-content"},yr={class:"modal-header"},xr={class:"modal-body"},kr={class:"alert-message"},Tr={class:"alert-actions"},Cr={key:2,class:"modal-overlay"},$r={class:"modal-header"},Sr={class:"modal-body"},_r={class:"confirm-message"},Er={class:"confirm-actions"},Ar={class:"modal-header"},Mr={class:"modal-title"},Dr={class:"modal-body"},Pr={class:"alert-message"},Or={class:"alert-actions"},Ir={class:"task-queue"},Rr={class:"queue-header"},Ur={class:"queue-status-filter"},Lr=["value"],Fr={class:"queue-actions"},jr=["disabled","title"],Wr=["title"],Br={class:"task-count"},Nr={key:0,class:"queue-table-container"},Vr={class:"col-id"},Hr={class:"id-header"},zr={key:0,class:"col-subject"},qr={class:"subject-header"},Jr={class:"switch"},Yr=["title"],Kr={key:1,class:"col-status"},Xr=["onClick"],Gr={class:"col-id"},Zr={class:"id-cell group-cell"},Qr={class:"group-toggle-icon"},ei={class:"group-label"},ti={key:0,class:"col-subject"},si={class:"subject-cell group-subject"},ni={key:1,class:"col-status"},ai={class:"col-actions"},oi={class:"task-actions"},ri={class:"group-action-text"},ii=["onClick"],li={class:"col-id"},ci={class:"id-content"},ui={class:"task-id"},di={key:0,class:"enhance-svg-icon"},pi={key:1,class:"check-task-icon",title:"校对任务"},fi={key:0,class:"status-in-id"},hi={key:0,class:"col-subject"},vi=["onMouseenter"],gi={key:1,class:"col-status"},mi={class:"status-cell"},bi=["onClick"],wi={class:"col-id"},yi={class:"id-content"},xi={class:"task-id"},ki={key:0,class:"enhance-svg-icon"},Ti={key:1,class:"check-task-icon",title:"校对任务"},Ci={key:0,class:"status-in-id"},$i={key:0,class:"col-subject"},Si=["onMouseenter"],_i={key:1,class:"col-status"},Ei={class:"status-cell"},Ai={class:"col-actions"},Mi={class:"task-actions"},Di=["onClick"],Pi=["onClick"],Oi={key:2,class:"no-action-icon",title:"无可用操作"},Ii={key:1,class:"empty-queue"},Ri={key:4,class:"log-container"},Ui={class:"log-actions"},Li={class:"toggle-icon"},Fi=["innerHTML"],ji={__name:"TaskPane",setup(e){const s=ce(!1),t=ce(!1),a=ce(!1),c=ce(""),g=ce(!1),T=ce(!1),M=ce(!1),w=ce(!1),C=ce(!0),P=ce(""),L=ce(!1),V=ce(window.innerWidth),I=lt(()=>V.value<750),B=lt(()=>V.value<380),J=()=>{V.value=window.innerWidth},N=ce(null),ee=ce(!1),te=ce(""),se=ce(new Set);ce(!1);const f={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},Z=ce(!1),D=ce([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"}]),{docName:re,selected:le,logger:v,map:b,subject:x,stage:k,subjectOptions:$,stageOptions:m,appConfig:U,clearLog:E,checkDocumentFormat:Y,getTaskStatusClass:pe,getTaskStatusText:he,terminateTask:$e,run1:Oe,runCheck:Se,setupLifecycle:Me,navigateToTaskControl:we,isLoading:Te,tryRemoveTaskPlaceholderWithLoading:_e,confirmDialog:Ie,handleConfirm:K,errorDialog:De,hideErrorDialog:ge,getCompletedTasksCount:st,getReleasableTasksCount:Ke,showConfirm:Re}=Tn(),fe=ce(null);(()=>{var S;try{if((S=window.Application)!=null&&S.PluginStorage){const h=window.Application.PluginStorage.getItem("user_info");h?(fe.value=JSON.parse(h),console.log("用户信息已加载:",fe.value)):console.log("未找到用户信息")}}catch(h){console.error("解析用户信息时出错:",h)}})(),Jt(fe,S=>{S&&S.orgs&&S.orgs[0]&&console.log(`用户企业ID: ${S.orgs[0].orgId}, 校对功能${S.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Xe=lt(()=>!fe.value||fe.value.isAdmin||fe.value.isOwner?$:fe.value.subject?$.filter(S=>S.value===fe.value.subject):$),me=()=>{fe.value&&!fe.value.isAdmin&&!fe.value.isOwner&&fe.value.subject&&(x.value=fe.value.subject)},nt=lt(()=>["physics","chemistry","biology","math"].includes(x.value));Jt(U,S=>{S&&(console.log("TaskPane组件收到应用配置更新:",S),console.log("当前版本类型:",S.EDITION),console.log("当前年级选项:",m.value))},{deep:!0,immediate:!0});const Be=()=>{try{const S=Y();C.value=S.isValid,P.value=S.message,L.value=!S.isValid,S.isValid||console.warn("文档格式检查失败:",S.message)}catch(S){console.error("执行文档格式检查时出错:",S),C.value=!1,P.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",L.value=!0}},Ce=lt(()=>{let S={};const h=b;if(te.value==="")S={...h};else for(const n in h)if(Object.prototype.hasOwnProperty.call(h,n)){const o=h[n];o.status===te.value&&(S[n]=o)}return ee.value&&N.value?S[N.value]?{[N.value]:S[N.value]}:{}:S}),Ue=lt(()=>{const S=Ce.value;return Object.entries(S).map(([n,o])=>({tid:n,...o})).sort((n,o)=>{const l=n.startTime||0;return(o.startTime||0)-l}).reduce((n,o)=>{const{tid:l,...p}=o;return n[l]=p,n},{})}),Ne=lt(()=>{const S=Ue.value,h=Object.entries(S).map(([p,i])=>({tid:p,...i})),n=h.filter(p=>p.status===3),o=h.filter(p=>p.status!==3),l=[];if(o.forEach(p=>{l.push({type:"task",...p})}),n.length>=2){const p="released-group-all",i=se.value.has(p);l.push({type:"group",groupId:p,isCollapsed:i,tasks:n,count:n.length})}else n.forEach(p=>{l.push({type:"task",...p})});return l}),Ve=S=>{se.value.has(S)?se.value.delete(S):se.value.add(S)},Ge=(S="wps-analysis")=>{x.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(c.value="未选中内容",t.value=!0):(S==="wps-analysis"?T.value=!0:S==="wps-enhance_analysis"&&(M.value=!0),Oe(S).catch(h=>{console.log(h),h.message.includes("重叠")?(c.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",h),c.value=h.message,t.value=!0)}).finally(()=>{S==="wps-analysis"?T.value=!1:S==="wps-enhance_analysis"&&(M.value=!1)})):(c.value="请选择学科",t.value=!0)},at=()=>{x.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(c.value="未选中内容",t.value=!0):(w.value=!0,Se().catch(S=>{console.log(S),S.message.includes("重叠")?(c.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",S),c.value=S.message,t.value=!0)}).finally(()=>{w.value=!1})):(c.value="请选择学科",t.value=!0)},Ze=(S,h)=>{N.value=S,we(S)},dt=S=>{b[S]&&(b[S].status=3),N.value===S&&(N.value=null),_e(S,!0)},ot=async()=>{const S=Object.entries(b).filter(([h,n])=>n.status===2||n.status===4);if(S.length===0){c.value="没有可释放的任务",t.value=!0;return}try{if(await Re(`确定要释放所有 ${S.length} 个可释放的任务吗？
此操作不可撤销。`)){let n=0;S.forEach(([o,l])=>{b[o]&&(b[o].status=3,N.value===o&&(N.value=null),_e(o,!0),n++)}),c.value=`已成功释放 ${n} 个任务`,t.value=!0}}catch(h){console.error("释放任务时出错:",h),c.value="释放任务时出现错误",t.value=!0}},Ot=()=>{g.value=!g.value},kt=S=>S?S.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Tt=S=>{const h=Ct(S);return h?kt(h):"无题目内容"},Ct=S=>{if(!S.selectedText)return"";const h=S.selectedText.split("\r").filter(o=>o.trim());if(h.length===1){const o=S.selectedText.split(`
`).filter(l=>l.trim());o.length>1&&h.splice(0,1,...o)}const n=h.map((o,l)=>{const p=o.trim();return p.length>200,p});return n.length===1?h[0].trim():n.join(`
`)},pt=(S,h)=>{if(!Z.value)return;const n=S.target,o=Ct(h).toString();if(!o||o.trim()===""){console.log("题目内容为空，不显示tooltip");return}const l=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${o.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,p=S.clientX,i=S.clientY;if(f.subjects.has(n)){const u=f.subjects.get(n);u.setContent(l),u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:p,right:p})}),u.show();return}const r=yt(n,{content:l,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:p,right:p}),onHidden:()=>{r.setProps({getReferenceClientRect:null})}});f.subjects.set(n,r),r.show()},y=S=>{const h=S.currentTarget,n=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,o=S.clientX,l=S.clientY;if(f.enhance.has(h)){const i=f.enhance.get(h);i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:l,bottom:l,left:o,right:o})}),i.show();return}const p=yt(h,{content:n,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});f.enhance.set(h,p),p.show()},W=()=>{f.subjects.forEach(S=>{S.destroy()}),f.subjects.clear(),f.enhance.forEach(S=>{S.destroy()}),f.enhance.clear(),f.softBreak.forEach(S=>{S.destroy()}),f.softBreak.clear(),document.removeEventListener("click",H),document.removeEventListener("mousemove",ae)},H=S=>{const h=document.querySelector(".tippy-box");h&&!h.contains(S.target)&&(f.subjects.forEach(n=>n.hide()),f.enhance.forEach(n=>n.hide()),f.softBreak.forEach(n=>n.hide()))};let oe=0;const ae=S=>{const h=Date.now();if(h-oe<100)return;oe=h;const n=document.querySelector(".tippy-box");if(!n)return;const o=n.getBoundingClientRect();!(S.clientX>=o.left-20&&S.clientX<=o.right+20&&S.clientY>=o.top-20&&S.clientY<=o.bottom+20)&&!n.matches(":hover")&&(f.subjects.forEach(p=>p.hide()),f.enhance.forEach(p=>p.hide()),f.softBreak.forEach(p=>p.hide()))},ve=()=>{document.addEventListener("click",H),document.addEventListener("mousemove",ae)};Fs(()=>{window.addEventListener("resize",J),ve(),me(),setTimeout(()=>{Be()},500);const S=document.createElement("style");S.id="tippy-custom-styles",S.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(S)}),un(()=>{window.removeEventListener("resize",J),W();const S=document.getElementById("tippy-custom-styles");S&&S.remove()}),Me();const ye=S=>S.selectedText?S.selectedText.includes("\v")||S.selectedText.includes("\v"):!1,He=S=>{const h=S.currentTarget,n=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,o=S.clientX,l=S.clientY;if(f.softBreak.has(h)){const i=f.softBreak.get(h);i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:l,bottom:l,left:o,right:o})}),i.show();return}const p=yt(h,{content:n,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});f.softBreak.set(h,p),p.show()},vt=()=>{const S=Ne.value.filter(h=>h.type==="group");rt.value?se.value.clear():S.forEach(h=>{se.value.add(h.groupId)})},Le=lt(()=>Ne.value.some(S=>S.type==="group")),rt=lt(()=>se.value.size>0);return(S,h)=>{var n,o,l,p,i;return F(),j("div",No,[ie(Te)?(F(),j("div",Vo,h[31]||(h[31]=[d("div",{class:"loading-spinner"},null,-1),d("div",{class:"loading-text"},"处理中...",-1)]))):q("",!0),L.value?(F(),j("div",Ho,[d("div",zo,[h[32]||(h[32]=d("div",{class:"format-error-icon"},"⚠️",-1)),h[33]||(h[33]=d("div",{class:"format-error-title"},"文档格式不支持",-1)),d("div",qo,Q(P.value),1),d("div",Jo,[d("button",{class:"retry-check-btn",onClick:h[0]||(h[0]=r=>Be())},"重新检查")])])])):q("",!0),d("div",Yo,[d("div",Ko,Q(ie(re)||"未选择文档"),1),d("button",{class:"settings-btn",onClick:h[1]||(h[1]=r=>a.value=!0)},h[34]||(h[34]=[d("i",{class:"icon-settings"},null,-1)]))]),d("div",Xo,[d("div",Go,[d("div",Zo,[h[35]||(h[35]=d("label",{for:"stage-select"},"年级:",-1)),qe(d("select",{id:"stage-select","onUpdate:modelValue":h[2]||(h[2]=r=>gs(k)?k.value=r:null),class:"select-input",disabled:L.value},[(F(!0),j(mt,null,_t(ie(m),r=>(F(),j("option",{key:r.value,value:r.value},Q(r.label),9,er))),128))],8,Qo),[[Qt,ie(k)]])]),d("div",tr,[h[36]||(h[36]=d("label",{for:"subject-select"},"学科:",-1)),qe(d("select",{id:"subject-select","onUpdate:modelValue":h[3]||(h[3]=r=>gs(x)?x.value=r:null),class:"select-input",disabled:L.value},[(F(!0),j(mt,null,_t(Xe.value,r=>(F(),j("option",{key:r.value,value:r.value},Q(r.label),9,nr))),128))],8,sr),[[Qt,ie(x)]]),fe.value&&!fe.value.isAdmin&&!fe.value.isOwner&&fe.value.subject?(F(),j("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((n=Xe.value.find(r=>r.value===fe.value.subject))==null?void 0:n.label)||fe.value.subject} 学科`}," 🔒 ",8,ar)):q("",!0)])]),nt.value?(F(),j("div",or," 理科可使用增强模式以获取更精准的解析 ")):q("",!0),d("div",rr,[d("button",{class:"action-btn primary",onClick:h[4]||(h[4]=r=>Ge("wps-analysis")),disabled:T.value||L.value},[d("div",lr,[T.value?(F(),j("span",cr)):q("",!0),h[37]||(h[37]=d("span",{class:"btn-text"},"解析",-1))])],8,ir),nt.value?(F(),j("button",{key:0,class:"action-btn enhance",onClick:h[5]||(h[5]=r=>Ge("wps-enhance_analysis")),disabled:M.value||L.value},[d("div",dr,[M.value?(F(),j("span",pr)):q("",!0),h[38]||(h[38]=d("span",{class:"btn-text"},"增强解析",-1))])],8,ur)):q("",!0),((l=(o=fe.value)==null?void 0:o.orgs[0])==null?void 0:l.orgId)===2?(F(),j("button",{key:1,class:"action-btn secondary",onClick:h[6]||(h[6]=r=>at()),disabled:w.value||L.value},[d("div",hr,[w.value?(F(),j("span",vr)):q("",!0),h[39]||(h[39]=d("span",{class:"btn-text"},"校对",-1))])],8,fr)):q("",!0)])]),d("div",gr,[s.value?(F(),j("div",{key:0,class:"modal-overlay",onClick:h[9]||(h[9]=r=>s.value=!1)},[d("div",{class:"modal-content",onClick:h[8]||(h[8]=ft(()=>{},["stop"]))},[d("div",mr,[h[40]||(h[40]=d("div",{class:"modal-title"},"选中内容",-1)),d("button",{class:"modal-close",onClick:h[7]||(h[7]=r=>s.value=!1)},"×")]),d("div",br,[d("pre",wr,Q(ie(le)||"未选中内容"),1)])])])):q("",!0),t.value?(F(),j("div",{key:1,class:"modal-overlay",onClick:h[13]||(h[13]=r=>t.value=!1)},[d("div",{class:"modal-content alert-modal",onClick:h[12]||(h[12]=ft(()=>{},["stop"]))},[d("div",yr,[h[41]||(h[41]=d("div",{class:"modal-title"},"提示",-1)),d("button",{class:"modal-close",onClick:h[10]||(h[10]=r=>t.value=!1)},"×")]),d("div",xr,[d("div",kr,Q(c.value),1),d("div",Tr,[d("button",{class:"action-btn primary",onClick:h[11]||(h[11]=r=>t.value=!1)},"确定")])])])])):q("",!0),ie(Ie).show?(F(),j("div",Cr,[d("div",{class:"modal-content confirm-modal",onClick:h[17]||(h[17]=ft(()=>{},["stop"]))},[d("div",$r,[h[42]||(h[42]=d("div",{class:"modal-title"},"确认",-1)),d("button",{class:"modal-close",onClick:h[14]||(h[14]=r=>ie(K)(!1))},"×")]),d("div",Sr,[d("div",_r,Q(ie(Ie).message),1),d("div",Er,[d("button",{class:"action-btn secondary",onClick:h[15]||(h[15]=r=>ie(K)(!1))},"取消"),d("button",{class:"action-btn primary",onClick:h[16]||(h[16]=r=>ie(K)(!0))},"确定")])])])])):q("",!0),ie(De).show?(F(),j("div",{key:3,class:"modal-overlay",onClick:h[21]||(h[21]=r=>ie(ge)())},[d("div",{class:"modal-content alert-modal",onClick:h[20]||(h[20]=ft(()=>{},["stop"]))},[d("div",Ar,[d("div",Mr,Q(ie(De).title),1),d("button",{class:"modal-close",onClick:h[18]||(h[18]=r=>ie(ge)())},"×")]),d("div",Dr,[d("div",Pr,Q(ie(De).message),1),d("div",Or,[d("button",{class:"action-btn primary",onClick:h[19]||(h[19]=r=>ie(ge)())},"确定")])])])])):q("",!0),d("div",Ir,[d("div",Rr,[h[43]||(h[43]=d("div",{class:"queue-title"},"任务队列",-1)),d("div",Ur,[qe(d("select",{id:"status-filter-select","onUpdate:modelValue":h[22]||(h[22]=r=>te.value=r),class:"status-filter-select-input"},[(F(!0),j(mt,null,_t(D.value,r=>(F(),j("option",{key:r.value,value:r.value},Q(r.label),9,Lr))),128))],512),[[Qt,te.value]])]),d("div",Fr,[d("button",{class:"release-all-btn",onClick:ot,disabled:ie(Ke)()===0,title:ie(Ke)()===0?"无可释放任务":`释放所有${ie(Ke)()}个可释放任务`}," 一键释放 ",8,jr),Le.value?(F(),j("button",{key:0,class:"collapse-toggle-btn",onClick:vt,title:rt.value?"展开所有折叠组":"折叠所有已释放任务组"},Q(rt.value?"全部展开":"全部折叠"),9,Wr)):q("",!0)]),d("div",Br,Q(Object.keys(Ce.value).length)+"个任务",1)]),Object.keys(Ce.value).length>0?(F(),j("div",Nr,[d("table",{class:ke(["queue-table",{"narrow-view":I.value,"ultra-narrow-view":B.value}])},[d("thead",null,[d("tr",null,[d("th",Vr,[d("div",Hr,[h[45]||(h[45]=d("span",null,"任务ID",-1)),d("span",{class:"help-icon",onMouseenter:h[23]||(h[23]=r=>y(r))},h[44]||(h[44]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("circle",{cx:"12",cy:"12",r:"10"}),d("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),d("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),I.value?q("",!0):(F(),j("th",zr,[d("div",qr,[h[46]||(h[46]=d("span",null,"题目内容",-1)),d("label",Jr,[qe(d("input",{type:"checkbox","onUpdate:modelValue":h[24]||(h[24]=r=>Z.value=r)},null,512),[[dn,Z.value]]),d("span",{class:"slider round",title:Z.value?"关闭题目预览":"开启题目预览"},null,8,Yr)])])])),B.value?q("",!0):(F(),j("th",Kr,"状态")),h[47]||(h[47]=d("th",{class:"col-actions"},"操作",-1))])]),d("tbody",null,[(F(!0),j(mt,null,_t(Ne.value,r=>(F(),j(mt,{key:r.type==="group"?r.groupId:r.tid},[r.type==="group"?(F(),j("tr",{key:0,class:"group-row",onClick:u=>Ve(r.groupId)},[d("td",Gr,[d("div",Zr,[d("span",Qr,Q(r.isCollapsed?"▶":"▼"),1),d("span",ei,"已释放任务组 ("+Q(r.count)+"个)",1)])]),I.value?q("",!0):(F(),j("td",ti,[d("div",si,Q(r.isCollapsed?"点击展开查看详情":"点击折叠隐藏详情"),1)])),B.value?q("",!0):(F(),j("td",ni,h[48]||(h[48]=[d("div",{class:"status-cell"},[d("span",{class:"task-tag status-released"},"已释放")],-1)]))),d("td",ai,[d("div",oi,[d("span",ri,Q(r.isCollapsed?"展开":"折叠"),1)])])],8,Xr)):q("",!0),r.type==="group"&&!r.isCollapsed?(F(!0),j(mt,{key:1},_t(r.tasks,(u,O)=>(F(),j("tr",{key:u.tid,class:ke(["task-row group-task-row",[ie(pe)(u.status),{"selected-task-row":u.tid===N.value}]]),onClick:_=>Ze(u.tid)},[d("td",li,[d("div",{class:ke(["id-cell",{"id-with-status":B.value}])},[d("div",ci,[h[51]||(h[51]=d("span",{class:"group-indent"},"└─",-1)),d("span",ui,Q(u.tid.substring(0,8)),1),u.wordType==="wps-enhance_analysis"||u.isEnhanced?(F(),j("span",di,h[49]||(h[49]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("title",null,"增强模式"),d("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):q("",!0),u.wordType==="wps-check"||u.isCheckTask?(F(),j("span",pi," 校 ")):q("",!0),ye(u)?(F(),j("span",{key:2,class:"soft-break-warning-icon",onMouseenter:h[25]||(h[25]=_=>He(_))},h[50]||(h[50]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("title",null,"提示"),d("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),d("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),d("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):q("",!0)]),B.value?(F(),j("div",fi,[d("span",{class:ke(["task-tag compact",ie(pe)(u.status)])},Q(ie(he)(u.status)),3)])):q("",!0)],2)]),I.value?q("",!0):(F(),j("td",hi,[d("div",{class:"subject-cell",onMouseenter:_=>pt(_,u)},Q(Tt(u)),41,vi)])),B.value?q("",!0):(F(),j("td",gi,[d("div",mi,[d("span",{class:ke(["task-tag",ie(pe)(u.status)])},Q(ie(he)(u.status)),3)])])),h[52]||(h[52]=d("td",{class:"col-actions"},[d("div",{class:"task-actions"},[d("span",{class:"no-action-icon",title:"无可用操作"},[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[d("circle",{cx:"12",cy:"12",r:"10"}),d("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),d("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})])])])],-1))],10,ii))),128)):q("",!0),r.type==="task"?(F(),j("tr",{key:2,class:ke(["task-row",[ie(pe)(r.status),{"selected-task-row":r.tid===N.value}]]),onClick:u=>Ze(r.tid)},[d("td",wi,[d("div",{class:ke(["id-cell",{"id-with-status":B.value}])},[d("div",yi,[d("span",xi,Q(r.tid.substring(0,8)),1),r.wordType==="wps-enhance_analysis"||r.isEnhanced?(F(),j("span",ki,h[53]||(h[53]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("title",null,"增强模式"),d("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):q("",!0),r.wordType==="wps-check"||r.isCheckTask?(F(),j("span",Ti," 校 ")):q("",!0),ye(r)?(F(),j("span",{key:2,class:"soft-break-warning-icon",onMouseenter:h[26]||(h[26]=u=>He(u))},h[54]||(h[54]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("title",null,"提示"),d("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),d("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),d("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):q("",!0)]),B.value?(F(),j("div",Ci,[d("span",{class:ke(["task-tag compact",ie(pe)(r.status)])},Q(ie(he)(r.status)),3)])):q("",!0)],2)]),I.value?q("",!0):(F(),j("td",$i,[d("div",{class:"subject-cell",onMouseenter:u=>pt(u,r)},Q(Tt(r)),41,Si)])),B.value?q("",!0):(F(),j("td",_i,[d("div",Ei,[d("span",{class:ke(["task-tag",ie(pe)(r.status)])},Q(ie(he)(r.status)),3)])])),d("td",Ai,[d("div",Mi,[r.status===1?(F(),j("button",{key:0,onClick:ft(u=>ie($e)(r.tid),["stop"]),class:"terminate-btn"}," 终止 ",8,Di)):q("",!0),r.status===2||r.status===4?(F(),j("button",{key:1,onClick:ft(u=>dt(r.tid),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Pi)):q("",!0),r.status!==1&&r.status!==2&&r.status!==4?(F(),j("span",Oi,h[55]||(h[55]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[d("circle",{cx:"12",cy:"12",r:"10"}),d("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),d("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):q("",!0)])])],10,bi)):q("",!0)],64))),128))])],2)])):(F(),j("div",Ii,h[56]||(h[56]=[d("div",{class:"empty-text"},"暂无任务",-1)])))]),((i=(p=fe.value)==null?void 0:p.orgs[0])==null?void 0:i.orgId)===2?(F(),j("div",Ri,[d("div",{class:"log-header",onClick:Ot},[h[57]||(h[57]=d("div",{class:"log-title"},"执行日志",-1)),d("div",Ui,[d("button",{class:"clear-btn",onClick:h[27]||(h[27]=ft(r=>ie(E)(),["stop"]))},"清空日志"),d("span",Li,Q(g.value?"▼":"▶"),1)])]),g.value?(F(),j("div",{key:0,class:"log-content",innerHTML:ie(v)},null,8,Fi)):q("",!0)])):q("",!0)]),a.value?(F(),j("div",{key:2,class:"modal-overlay",onClick:h[30]||(h[30]=r=>a.value=!1)},[d("div",{class:"modal-content",onClick:h[29]||(h[29]=ft(()=>{},["stop"]))},[pn(da,{onClose:h[28]||(h[28]=r=>a.value=!1)})])])):q("",!0)])}}},Bi=js(ji,[["__scopeId","data-v-1e73d900"]]);export{Bi as default};
