const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ray, <PERSON>u, ipc<PERSON>ain, dialog } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');
const url = require('url');
const authProxy = require('./services/authProxy');
const fs = require('fs');
const os = require('os');
const cp = require('child_process');
const configStore = require('./services/configStore');
const { isProcessRunning, createEmptyWordDocument } = require('./utils/checkProcess');
const { defaultLogger } = require('./utils/logger')
const machineCodeGenerator = require('./utils/machineCode'); // 添加机器码生成器导入

// 创建日志目录和文件
const logDir = path.join(app.getPath('userData'), 'logs');
const logFile = path.join(logDir, 'app-debug.log');
try {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
} catch (err) {
  console.error('创建日志目录失败:', err);
}
const uiDirName = 'ui';

// 添加环境配置
const ENV = {
  // 基本环境判断
  isDev: !app.isPackaged,
  get isProd() { return !this.isDev; },

  // 环境配置
  config: {
    // 默认配置
    ENV: 'development',
    APP_VERSION: app.getVersion(),
    BUILD_DATE: new Date().toISOString(),
    // 版本配置
    EDITION: 'wanwei', // 默认为万唯版本，可以是 'wanwei'、'hexin' 或 'wwsenior'
    BYPASS_ENCRYPTION: false // 是否绕过加密软件检测
  },

  // 加载环境配置
  loadConfig() {
    try {
      if (this.isProd) {
        // 在生产环境中加载env-config.json
        const configPath = path.join(process.resourcesPath, 'env-config.json');
        logDebug(`尝试加载环境配置: ${configPath}`);

        if (fs.existsSync(configPath)) {
          const configData = fs.readFileSync(configPath, 'utf8');
          const config = JSON.parse(configData);
          Object.assign(this.config, config);
          logDebug('已加载生产环境配置');
        } else {
          logDebug('环境配置文件不存在，使用默认配置');
        }
      } else {
        // 开发环境加载开发配置文件
        const devConfigPath = path.join(__dirname, 'env-config/development.json');
        logDebug(`尝试加载开发环境配置: ${devConfigPath}`);

        if (fs.existsSync(devConfigPath)) {
          const configData = fs.readFileSync(devConfigPath, 'utf8');
          const config = JSON.parse(configData);
          Object.assign(this.config, config);
          logDebug('已加载开发环境配置');
        } else {
          this.config.ENV = 'development';
          logDebug('开发环境配置文件不存在，使用默认配置');
        }
      }
    } catch (err) {
      logDebug(`加载环境配置出错: ${err.message}`);
    }

    // 优先从命令行环境变量读取版本信息（用于开发时的版本切换）
    if (process.env.APP_EDITION) {
      this.config.EDITION = process.env.APP_EDITION;
      logDebug(`从环境变量读取版本类型: ${process.env.APP_EDITION}`);
    }

    // 输出最终环境配置
    logDebug(`环境: ${this.config.ENV}`);
    logDebug(`版本: ${this.config.APP_VERSION}`);
    logDebug(`版本类型: ${this.config.EDITION}`);
    logDebug(`绕过加密检测: ${this.config.BYPASS_ENCRYPTION}`);

    return this.config;
  },

  paths: {
    // 开发环境路径
    dev: {
      appPath: __dirname,
      assets: path.join(__dirname, 'assets'),
      ui: path.join(__dirname, uiDirName),
      preload: path.join(__dirname, 'preload.js')
    },
    // 生产环境路径
    prod: {
      appPath: app.getAppPath(),
      // 在生产环境，assets和ui在resources目录(extraResources)
      assets: path.join(process.resourcesPath, 'assets'),
      ui: path.join(process.resourcesPath, uiDirName),
      // preload在app.asar内
      preload: path.join(app.getAppPath(), 'preload.js')
    },
    // 根据当前环境获取路径
    get(key) {
      return ENV.isDev ? ENV.paths.dev[key] : ENV.paths.prod[key];
    }
  },
  // 获取资源路径的辅助函数
  getPath(type, filename) {
    const basePath = this.paths.get(type);
    return filename ? path.join(basePath, filename) : basePath;
  }
};

// 输出当前环境信息
logDebug(`应用环境: ${ENV.isDev ? '开发' : '生产'}`);
logDebug(`应用路径: ${ENV.paths.get('appPath')}`);
logDebug(`资源路径: ${ENV.paths.get('assets')}`);
logDebug(`UI路径: ${ENV.paths.get(uiDirName)}`);
logDebug(`Preload路径: ${ENV.paths.get('preload')}`);

// 日志函数
function logDebug(message) {
  // Skip non-critical logging in production
  if (process.env.NODE_ENV === 'production' && !message.includes('错误') && !message.includes('失败')) {
    return;
  }

  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  try {
    fs.appendFileSync(logFile, logMessage);
  } catch (err) {
    console.error('写入日志失败:', err);
  }
}

// 添加未捕获异常处理
process.on('uncaughtException', (error) => {
  logDebug(`未捕获的异常: ${error.stack || error}`);
  dialog.showErrorBox('应用错误', `发生未捕获的异常:\n${error.message}\n\n请查看日志文件: ${logFile}`);
});

// 确保应用只有一个实例运行
const gotSingleInstanceLock = app.requestSingleInstanceLock();

if (!gotSingleInstanceLock) {
  logDebug('应用已经在运行中，退出当前实例');
  app.quit();
} else {
  // 监听第二个实例启动
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    logDebug('检测到第二个实例启动，聚焦当前窗口');

    // 如果已经有窗口，则显示并聚焦它
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.show();
      mainWindow.focus();
    }
  });

  // 修复Windows上的中文显示问题
  if (process.platform === 'win32') {
    logDebug('Windows环境检测到，设置语言环境');
    process.env.LANG = 'zh-CN.UTF-8';
    process.env.ELECTRON_FORCE_IS_PACKAGED = "true";
    process.env.ELECTRON_NO_ASAR = "true";

    try {
      cp.execSync('chcp 65001', { stdio: 'ignore' });
    } catch (e) {
      logDebug(`无法设置控制台代码页: ${e}`);
    }
  }
}

// 获取图标路径
function getIconPath() {
  // 根据版本类型选择不同的图标
  const iconName = ENV.config.EDITION === 'hexin' ? 'hexin.png' : 'ww.png';
  const iconPath = ENV.getPath('assets', iconName);
  logDebug(`尝试获取图标: ${iconPath} (版本: ${ENV.config.EDITION})`);

  try {
    fs.accessSync(iconPath, fs.constants.F_OK);
    logDebug(`图标存在: ${iconPath}`);
    return iconPath;
  } catch (err) {
    logDebug(`图标不存在: ${err.message}`);
  }
}

// Keep a global reference of the window and tray objects
let mainWindow = null;
let tray = null;
let serverInstance = null;
let isQuitting = false;

// 添加网络文件夹配置状态跟踪
let networkFolderConfigured = false;
let permissionCheckCompleted = false;
// 添加权限检查Promise的resolver
let permissionCheckResolver = null;
let loginStatusCheckResolver = null; // 新增：登录状态检查Promise resolver
// 新增：跟踪是否是从机器码弹窗触发的重设操作
let isMonitorFolderReset = false;

// 重置权限检查状态（应用启动时调用）
function resetPermissionCheckStatus() {
  networkFolderConfigured = false;
  permissionCheckCompleted = false;
  permissionCheckResolver = null;
  loginStatusCheckResolver = null; // 重置登录状态检查resolver
  isMonitorFolderReset = false; // 重置监控文件夹重设标志
  logDebug('权限检查状态已重置');
}

// 启动服务器实例
function startServer() {
  logDebug('准备启动服务器');
  logDebug(`当前目录: ${__dirname}`);
  logDebug(`app.getAppPath(): ${app.getAppPath()}`);
  logDebug(`process.resourcesPath: ${process.resourcesPath}`);
  logDebug(`当前工作目录: ${process.cwd()}`);

  // 检查端口是否被占用
  checkAndFreePort(3000)
    .then(() => {
      try {
        // 直接引入server.js模块
        const serverModule = require('./server');

        if (!serverModule || !serverModule.startServer) {
          throw new Error('服务器模块没有导出startServer函数');
        }

        // 设置环境变量
        process.env.LANG = 'zh-CN.UTF-8';
        process.env.NODE_OPTIONS = '--no-warnings --max-http-header-size=16384';
        process.env.DEBUG = '*'; // 启用所有调试输出

        // 启动服务器
        logDebug('正在启动内置服务器...');
        serverInstance = serverModule.startServer({
          port: 3000
        });

        // 监听认证事件
        if (serverInstance.authEvents) {
          logDebug('注册认证事件监听器');

          // 监听登录成功事件
          serverInstance.authEvents.on('login-success', (userData) => {
            logDebug('收到外部登录成功事件，更新桌面应用状态');

            // 如果主窗口存在，发送登录成功事件
            if (mainWindow && mainWindow.webContents) {
              mainWindow.webContents.send('login-success', userData);
            }
          });

          // 监听登出事件
          serverInstance.authEvents.on('logout', () => {
            logDebug('收到外部登出事件，更新桌面应用状态');

            // 如果主窗口存在，发送登出成功事件并显示登录界面
            if (mainWindow && mainWindow.webContents) {
              mainWindow.webContents.send('logout-success');
              mainWindow.show();
              mainWindow.webContents.send('show-login');
            }
          });
        } else {
          logDebug('服务器实例未提供认证事件接口');
        }

        logDebug(`服务器已启动，监听端口: ${serverInstance.port}`);

      } catch (err) {
        logDebug(`启动服务器失败: ${err.stack || err.message}`);
        dialog.showErrorBox('服务器错误', `启动服务器失败: ${err.message}\n\n请查看日志: ${logFile}`);
        app.quit();
      }
    })
    .catch(err => {
      logDebug(`处理端口占用出错: ${err.message}`);
      dialog.showErrorBox('服务器错误', `无法使用必要的端口(3000): ${err.message}\n\n请检查是否有其他程序占用此端口。`);
      app.quit();
    });
}

// 检查端口并释放（如已被占用）
async function checkAndFreePort(port) {
  const tcpPortUsed = require('tcp-port-used');

  try {
    // 检查端口是否在使用中
    const inUse = await tcpPortUsed.check(port);

    if (inUse) {
      logDebug(`端口 ${port} 已被占用，尝试释放...`);

      if (process.platform === 'win32') {
        // Windows平台
        // 查找使用端口的进程PID
        const findPidCommand = `netstat -ano | findstr :${port}`;
        const output = cp.execSync(findPidCommand, { encoding: 'utf8' });

        // 提取PID
        const lines = output.split('\n').filter(line => line.includes(`LISTENING`));

        if (lines.length > 0) {
          // 获取最后一列的PID
          const pidMatch = lines[0].trim().match(/(\d+)$/);

          if (pidMatch && pidMatch[1]) {
            const pid = pidMatch[1];
            logDebug(`找到使用端口 ${port} 的进程，PID: ${pid}`);

            // 确认不是当前进程
            if (pid !== process.pid.toString()) {
              // 终止进程
              logDebug(`尝试终止进程 PID: ${pid}`);
              cp.execSync(`taskkill /F /PID ${pid}`);
              logDebug(`已终止进程 PID: ${pid}`);

              // 等待端口释放
              await new Promise(resolve => setTimeout(resolve, 1000));

              // 再次检查端口
              const stillInUse = await tcpPortUsed.check(port);
              if (stillInUse) {
                throw new Error(`无法释放端口 ${port}，即使已尝试终止进程`);
              }
            } else {
              logDebug(`端口被当前进程占用，这是不可能的情况`);
            }
          } else {
            logDebug(`无法从输出中提取PID: ${output}`);
          }
        } else {
          logDebug(`找不到使用端口 ${port} 的进程`);
        }
      } else {
        // macOS/Linux平台
        const findPidCommand = `lsof -i :${port} -t`;
        const pid = cp.execSync(findPidCommand, { encoding: 'utf8' }).trim();

        if (pid) {
          logDebug(`找到使用端口 ${port} 的进程，PID: ${pid}`);

          // 确认不是当前进程
          if (pid !== process.pid.toString()) {
            // 终止进程
            logDebug(`尝试终止进程 PID: ${pid}`);
            cp.execSync(`kill -9 ${pid}`);
            logDebug(`已终止进程 PID: ${pid}`);

            // 等待端口释放
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 再次检查端口
            const stillInUse = await tcpPortUsed.check(port);
            if (stillInUse) {
              throw new Error(`无法释放端口 ${port}，即使已尝试终止进程`);
            }
          } else {
            logDebug(`端口被当前进程占用，这是不可能的情况`);
          }
        } else {
          logDebug(`找不到使用端口 ${port} 的进程`);
        }
      }

      // 最终检查
      const finalCheck = await tcpPortUsed.check(port);
      if (finalCheck) {
        throw new Error(`无法释放端口 ${port}，请手动关闭使用该端口的程序`);
      }

      logDebug(`端口 ${port} 已成功释放`);
    } else {
      logDebug(`端口 ${port} 未被占用，可以使用`);
    }
  } catch (err) {
    logDebug(`检查或释放端口时出错: ${err.message}`);
    throw err;
  }
}

async function createWindow() {
  return new Promise((resolve, reject) => {
    logDebug('创建主窗口');
    try {
      const iconPath = getIconPath();

      // 从环境配置获取preload路径
      const preloadPath = ENV.paths.get('preload');
      Menu.setApplicationMenu(null);

      mainWindow = new BrowserWindow({
        width: 360,
        height: 360,
        frame: false,
        resizable: false,
        transparent: false,
        show: false, // 先隐藏，页面加载完成后再显示
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          enableRemoteModule: false,
          preload: preloadPath
        },
        icon: iconPath,
        // 添加应用标识配置
        title: 'WPS Addon',
        // 设置skipTaskbar为false确保可以在任务栏显示
        skipTaskbar: false
      });
      logDebug('主窗口已创建');

      // 从环境配置获取UI路径
      const uiPath = path.join(ENV.paths.get('ui'), 'index.html');

      logDebug(`加载UI: ${uiPath}`);
      if (!fs.existsSync(uiPath)) {
        logDebug('UI文件不存在');
        dialog.showErrorBox('UI错误', '无法找到UI文件，请检查应用安装是否完整');
        reject(new Error('UI文件不存在'));
        return;
      }
      // Load the application UI
      mainWindow.loadURL(url.format({
        pathname: uiPath,
        protocol: 'file:',
        slashes: true
      }));

      // 添加页面加载事件监听
      mainWindow.webContents.on('did-start-loading', () => {
        logDebug('页面开始加载');
      });

      mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        logDebug(`页面加载失败: ${errorDescription} (${errorCode})`);
        dialog.showErrorBox('UI加载错误', `页面加载失败: ${errorDescription}`);
        reject(new Error(`页面加载失败: ${errorDescription}`));
      });

      // 页面加载完成后立即显示窗口，然后resolve Promise
      mainWindow.webContents.on('did-finish-load', () => {
        logDebug('页面加载完成，显示窗口');
        logDebug(`当前状态 - permissionCheckCompleted: ${permissionCheckCompleted}, networkFolderConfigured: ${networkFolderConfigured}`);

        // 立即显示窗口
        mainWindow.show();
        mainWindow.focus();

        // 发送版本号和配置信息到渲染进程
        mainWindow.webContents.send('app-version', app.getVersion());
        mainWindow.webContents.send('app-config', ENV.config);

        // 窗口创建完成，resolve Promise
        // 注意：权限检查将在app.on('ready')中的checkLoginStatus()调用中进行
        resolve();
      });

      // Hide window on close instead of quitting
      mainWindow.on('close', (event) => {
        if (!isQuitting) {
          event.preventDefault();
          mainWindow.hide();
          return false;
        }
      });
      createTray();
    } catch (err) {
      logDebug(`创建主窗口失败: ${err}`);
      dialog.showErrorBox('窗口错误', `创建主窗口失败: ${err.message}`);
      reject(err);
    }
  });
}

function createTray() {
  try {
    const iconPath = getIconPath();

    try {
      tray = new Tray(iconPath);
    } catch (err) {
      logDebug(`创建托盘图标失败: ${err}`);
    }

    if (!tray) {
      logDebug('无法创建托盘图标，应用将继续但没有托盘图标');
      return;
    }

    const contextMenu = Menu.buildFromTemplate([
      { label: '显示页面', click: () => mainWindow.show() },
      { type: 'separator' },
      {
        label: '退出', click: () => {
          isQuitting = true;
          app.quit();
        }
      }
    ]);

    tray.setToolTip('万唯AI编辑WPS插件服务');
    tray.setContextMenu(contextMenu);

    // Show main window on double click
    tray.on('double-click', () => {
      mainWindow.show();
    });
  } catch (err) {
    logDebug(`创建托盘完全失败: ${err}`);
    // 继续运行应用，但没有托盘图标
  }
}

// 添加网络路径检测函数
function isNetworkPath(dirPath) {
  if (!dirPath) return false;

  // UNC路径检测 (\\server\share 或 \\************)
  if (dirPath.startsWith('\\\\')) {
    return true;
  }

  // 映射网络驱动器检测（通过执行 net use 命令）
  if (process.platform === 'win32') {
    try {
      const output = cp.execSync('net use', { encoding: 'utf8', timeout: 5000 });
      const driveLetter = dirPath.substring(0, 2).toUpperCase();

      // 检查输出中是否包含该驱动器号的网络映射
      return output.includes(driveLetter) && output.includes('Microsoft Windows Network');
    } catch (err) {
      logDebug(`检查网络驱动器失败: ${err.message}`);
      return false;
    }
  }

  return false;
}

// 检查路径是否为网络设备（比 isNetworkPath 更宽松，允许直接选择网络主机）
function isNetworkDevice(dirPath) {
  if (!dirPath) return false;

  // UNC 网络设备路径 (\\server 或 \\************)
  if (dirPath.startsWith('\\\\')) {
    // 移除开头的 \\
    const devicePath = dirPath.substring(2);
    // 检查是否只包含主机名或IP，没有共享路径
    const parts = devicePath.split('\\');
    return parts.length >= 1; // 至少有主机名
  }
  // 也检查是否为映射的网络驱动器
  return isNetworkPath(dirPath);
}

// 检测加密软件是否运行
function isEncryptionSoftwareRunning() {
  return isProcessRunning('CDGRegedit') || isProcessRunning('watchctrl');
}

// 检查Windows组策略中的"启用不安全的来宾登录"设置
function checkGuestLoginPolicy() {
  if (process.platform !== 'win32') {
    return { checked: false, reason: '非Windows系统' };
  }

  try {
    logDebug('检查组策略：启用不安全的来宾登录');

    // 检查注册表项：HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\LanmanWorkstation\Parameters\AllowInsecureGuestAuth
    const queryCommand = 'reg query "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\LanmanWorkstation\\Parameters" /v AllowInsecureGuestAuth';
    const fullQueryCommand = `chcp 65001 >nul 2>&1 && ${queryCommand}`;

    const regOutput = cp.execSync(fullQueryCommand, {
      encoding: 'utf8',
      timeout: 5000,
      stdio: 'pipe'
    });

    // 解析注册表输出，查找值
    const match = regOutput.match(/AllowInsecureGuestAuth\s+REG_DWORD\s+0x([0-9a-fA-F]+)/);

    if (match) {
      const value = parseInt(match[1], 16);
      const isEnabled = value === 1;

      logDebug(`组策略检查结果 - AllowInsecureGuestAuth: ${value} (${isEnabled ? '已启用' : '未启用'})`);

      return {
        checked: true,
        enabled: isEnabled,
        value: value,
        suggestion: isEnabled ? null : '需要启用"不安全的来宾登录"'
      };
    } else {
      logDebug('未找到AllowInsecureGuestAuth注册表项，可能需要手动配置');
      return {
        checked: true,
        enabled: false,
        suggestion: '未找到相关配置，建议手动启用"不安全的来宾登录"'
      };
    }
  } catch (error) {
    logDebug(`检查组策略失败: ${error.message}`);
    return {
      checked: false,
      error: error.message,
      suggestion: '无法检查组策略设置，建议手动检查'
    };
  }
}

// 检查网络连接是否可用
async function checkNetworkConnection(networkHost) {
  try {
    console.log(`正在检查网络连接: ${networkHost}`);

    // 先尝试建立网络连接（Windows）
    if (process.platform === 'win32') {
      try {
        console.log(`尝试建立网络连接: ${networkHost}`);

        // 使用 net use 命令建立连接，使用空密码的来宾访问
        // 完整命令示例: net use "\\***********" /user:guest ""
        // 参数说明:
        //   - "\\***********": 网络主机路径
        //   - /user:guest: 使用来宾账户
        //   - "": 空密码
        const netUseCommand = `net use "${networkHost}" /user:guest ""`;

        try {
          const netUseOutput = cp.execSync(netUseCommand, {
            encoding: 'utf8',
            timeout: 10000,
            stdio: 'pipe'
          });
          console.log(`网络连接建立成功: ${networkHost}`);
          logDebug(`net use 输出: ${netUseOutput}`);
        } catch (netUseError) {
          // net use 失败不一定意味着连接不可用，可能连接已存在
          console.log(`net use 命令执行结果: ${netUseError.message}`);

          // 检查是否是因为连接已存在而失败
          if (netUseError.message.includes('Multiple connections') ||
            netUseError.message.includes('已存在') ||
            netUseError.message.includes('already exists')) {
            console.log('网络连接已存在，继续检查可访问性');
          } else {
            logDebug(`建立网络连接失败，但继续尝试检查: ${netUseError.message}`);
          }
        }
      } catch (setupError) {
        console.log(`设置网络连接时出错: ${setupError.message}`);
      }
    }

    // 尝试访问网络主机根目录
    const testPath = networkHost;

    // 检查路径是否可访问
    await fs.promises.access(testPath, fs.constants.F_OK);

    console.log(`网络连接检查成功: ${networkHost}`);
    return { success: true, host: networkHost };
  } catch (error) {
    console.log(`网络连接检查失败: ${networkHost}, 错误: ${error.message}`);

    let additionalInfo = {};

    // 尝试使用 net view 命令检查网络连接（Windows）
    if (process.platform === 'win32') {
      try {
        // 使用 net view 命令查看网络主机的共享资源
        // 完整命令示例: net view \\***********
        // 用途: 列出指定网络主机上的共享资源，验证主机可访问性
        const netOutput = cp.execSync(`net view ${networkHost}`, {
          encoding: 'utf8',
          timeout: 5000,
          stdio: 'pipe'
        });

        if (netOutput && !netOutput.includes('System error')) {
          return { success: true, host: networkHost, method: 'net view' };
        }
      } catch (netError) {
        // 尝试更积极的连接建立方式
        try {
          // 尝试直接访问网络主机的 IPC$ 共享来触发连接
          // 完整命令示例: net use "\\***********\IPC$" /user:guest ""
          // IPC$ 是Windows默认的管理共享，用于进程间通信
          // 连接到 IPC$ 可以触发网络身份验证和连接建立
          const ipcShareCommand = `net use "${networkHost}\\IPC$" /user:guest ""`;
          const ipcOutput = cp.execSync(ipcShareCommand, {
            encoding: 'utf8',
            timeout: 8000,
            stdio: 'pipe'
          });
          logDebug(`IPC$ 连接输出: ${ipcOutput}`);

          // 重新检查路径可访问性
          try {
            await fs.promises.access(networkHost, fs.constants.F_OK);
            return { success: true, host: networkHost, method: 'ipc connection' };
          } catch (recheckError) {
          }

        } catch (ipcError) {
          console.log(`IPC$ 连接也失败: ${ipcError.message}`);
        }

        // 网络连接失败时，检查组策略设置
        const policyCheck = checkGuestLoginPolicy();
        additionalInfo.policyCheck = policyCheck;

        // 生成详细的错误信息和建议
        let suggestions = ['请检查网络连接是否正常'];

        if (policyCheck.checked && !policyCheck.enabled) {
          suggestions.push('启用Windows组策略：计算机配置 → 管理模板 → 网络 → Lanman工作站 → "启用不安全的来宾登录"');
        } else if (policyCheck.suggestion) {
          suggestions.push(policyCheck.suggestion);
        }

        suggestions.push('确认目标主机地址是否正确');
        suggestions.push('检查防火墙是否阻止了网络访问');
        suggestions.push('尝试在文件管理器地址栏手动输入网络地址进行访问');

        additionalInfo.suggestions = suggestions;
      }
    }

    return {
      success: false,
      host: networkHost,
      error: error.message,
      ...additionalInfo
    };
  }
}

// 添加新函数：检验并修正网络文件夹结构
async function validateAndFixNetworkFolder(watchDir) {
  try {
    logDebug(`检验网络文件夹结构: ${watchDir}`);

    // 判断是否为网络路径
    const isNetworkDir = isNetworkPath(watchDir);
    if (!isNetworkDir) {
      logDebug('当前路径不是网络路径，无需修正');
      return { success: true, path: watchDir, changed: false };
    }

    // 获取机器码
    const machineCodeInfo = await machineCodeGenerator.getMachineCode();
    const machineCode = machineCodeInfo.code;

    // 分析当前路径
    // 期望的结构: 主机名\图片\机器码\ww-wps-addon\Temp
    let hostPart = '';
    if (watchDir.startsWith('\\\\')) {
      const pathParts = watchDir.split('\\').filter(Boolean);
      hostPart = `\\\\${pathParts[0]}`;
    } else {
      // 映射的网络驱动器，获取UNC路径
      try {
        const driveLetter = watchDir.substring(0, 2); // 例如 "Z:"
        const netUseOutput = cp.execSync(`net use ${driveLetter}`).toString();
        const remoteMatch = netUseOutput.match(/Remote name\s+\\\\([^\s\\]+)/i);
        if (remoteMatch && remoteMatch[1]) {
          hostPart = `\\\\${remoteMatch[1]}`;
        } else {
          logDebug(`无法确定驱动器 ${driveLetter} 的网络路径，保持原路径`);
          return { success: true, path: watchDir, changed: false };
        }
      } catch (err) {
        logDebug(`获取网络驱动器映射失败: ${err.message}，保持原路径`);
        return { success: true, path: watchDir, changed: false };
      }
    }

    // 检查路径是否符合预期格式：主机名\图片\机器码\ww-wps-addon\Temp
    const expectedPattern = new RegExp(`${hostPart.replace(/\\/g, '\\\\')}\\\\图片\\\\${machineCode}\\\\ww-wps-addon\\\\Temp$`, 'i');

    if (expectedPattern.test(watchDir)) {
      logDebug('网络文件夹结构符合要求，无需修改');
      return { success: true, path: watchDir, changed: false };
    }

    // 构建新路径
    const newPath = path.join(hostPart, '图片', machineCode, 'ww-wps-addon', 'Temp');
    logDebug(`网络文件夹结构不符合要求，将修改为: ${newPath}`);

    // 创建新目录
    if (!fs.existsSync(newPath)) {
      fs.mkdirSync(newPath, { recursive: true });
      logDebug(`已创建新文件夹: ${newPath}`);
    }

    return { success: true, path: newPath, changed: true };
  } catch (error) {
    console.log(`验证网络文件夹结构失败`, error);
    logDebug(`验证网络文件夹结构失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// 权限检查函数，在登录前执行
async function checkPermissions() {
  return new Promise((resolve) => {
    logDebug('开始检查权限...');
    console.info(`权限检查状态 - permissionCheckCompleted: ${permissionCheckCompleted}, networkFolderConfigured: ${networkFolderConfigured}`);

    // 保存resolver以便后续使用
    permissionCheckResolver = resolve;

    // 如果权限检查已经完成，直接返回成功
    if (permissionCheckCompleted) {
      logDebug('权限检查已完成，跳过重复检查');
      resolve({
        success: true,
        permissions: [],
        encryptionDetected: networkFolderConfigured
      });
      return;
    }

    // 通知渲染进程显示权限检查界面
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('show-permission-check');
    }

    // 模拟检查过程，实际应用中替换为真实的权限检查逻辑
    const totalSteps = 5;
    let currentStep = 0;
    const permissions = [
      { name: '文件系统权限', id: 'file_system' },
      { name: '网络连接权限', id: 'network' },
      { name: '进程管理权限', id: 'process' },
      { name: '系统资源权限', id: 'system' },
      { name: '用户数据访问权限', id: 'user_data' }
    ];

    let encryptionSoftwareDetected = false;
    let permissionCheckStopped = false; // 添加停止标志

    const checkInterval = setInterval(async () => {
      // 如果权限检查已被停止，不继续执行
      if (permissionCheckStopped) {
        clearInterval(checkInterval);
        return;
      }
      // 更新当前步骤
      currentStep++;
      const permission = permissions[currentStep - 1];

      // 发送进度更新到渲染进程
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('permission-check-progress', {
          current: currentStep,
          total: totalSteps,
          percent: Math.floor((currentStep / totalSteps) * 100),
          currentPermission: permission
        });
      }

      switch (permission.id) {
        case 'file_system': {
          // 检测加密软件（根据配置决定是否绕过）
          if (ENV.config.BYPASS_ENCRYPTION) {
            logDebug('合心版本 - 绕过加密软件检测');
            encryptionSoftwareDetected = false;
          } else {
            encryptionSoftwareDetected = isEncryptionSoftwareRunning();
          }

          if (encryptionSoftwareDetected) {
            console.log('检测到加密软件正在运行，自动设置网络监控文件夹')

            try {
              // 获取机器码
              const machineCodeInfo = await machineCodeGenerator.getMachineCode()
              const machineCode = machineCodeInfo.code

              // 固定的网络主机地址
              const networkHost = '\\\\***********'

              console.log(`检查网络连接: ${networkHost}`)

              // 检查网络连接
              const connectionResult = await checkNetworkConnection(networkHost)

              if (!connectionResult.success) {
                console.log(`无法连接到网络主机: ${networkHost}`)

                // 构建详细的错误信息
                let message = `无法连接到网络主机 ${networkHost}。`;

                // 如果有组策略检查结果和建议，包含在消息中
                if (connectionResult.suggestions && connectionResult.suggestions.length > 0) {
                  message += '\n\n解决建议：\n' + connectionResult.suggestions.map((suggestion, index) => `${index + 1}. ${suggestion}`).join('\n');
                }

                // 发送事件到UI，提示用户需要建立网络连接
                if (mainWindow && mainWindow.webContents) {
                  mainWindow.webContents.send('network-connection-required', {
                    networkHost: networkHost,
                    message: message,
                    policyCheck: connectionResult.policyCheck,
                    suggestions: connectionResult.suggestions
                  })
                }

                // 设置停止标志并清除定时器，停止权限检查流程
                permissionCheckStopped = true
                clearInterval(checkInterval)
                logDebug('网络连接失败，停止权限检查流程，等待用户处理')
                // 不resolve Promise，等待用户重试或跳过后再继续
                return
              }

              // 直接使用固定的网络路径：\\***********\图片\机器码\ww-wps-addon\Temp
              const fixedNetworkPath = `${networkHost}\\图片\\${machineCode}\\ww-wps-addon\\Temp`

              console.log(`自动设置网络文件夹路径: ${fixedNetworkPath}`)

              // 创建目录结构（如果不存在）
              if (!fs.existsSync(fixedNetworkPath)) {
                fs.mkdirSync(fixedNetworkPath, { recursive: true })
                console.log(`已创建网络文件夹: ${fixedNetworkPath}`)
              } else {
                console.log(`网络文件夹已存在: ${fixedNetworkPath}`)
              }

              // 更新配置
              await configStore.setWatchDir(fixedNetworkPath)
              networkFolderConfigured = true

            } catch (error) {
              console.log(`自动设置网络文件夹失败: ${error.message}`)

              // 如果是网络连接相关的错误，提示用户
              if (error.message.includes('network') || error.message.includes('连接') ||
                error.message.includes('access') || error.message.includes('permission')) {
                logDebug(`网络连接错误: ${error.message}，提示用户建立连接`)

                if (mainWindow && mainWindow.webContents) {
                  mainWindow.webContents.send('network-connection-required', {
                    networkHost: '\\\\***********',
                    message: `创建网络文件夹时出错: ${error.message}，请确保能够正常访问网络主机。`
                  })
                }

                // 设置停止标志并清除定时器，停止权限检查流程
                permissionCheckStopped = true
                clearInterval(checkInterval)
                logDebug('网络连接错误，停止权限检查流程，等待用户处理')
                // 不resolve Promise，等待用户重试或跳过后再继续
                return
              }

              // 其他错误，标记为已配置（使用默认路径）
              networkFolderConfigured = true
              console.log('自动设置失败，但继续使用默认路径')
            }
          } else {
            logDebug('未检测到加密软件')
          }
          break
        }
        case 'network':
          break;
        case 'process':
          break;
        case 'system':
          break;
        case 'user_data':
          break;
        default:
          break;
      }

      // 检查完成
      if (currentStep >= totalSteps) {
        clearInterval(checkInterval);
        logDebug('权限检查完成');

        // 完成权限检查
        completePermissionCheck(encryptionSoftwareDetected, permissions);
      }
    }, 500); // 每500ms检查一个权限
  });
}

// 完成权限检查的辅助函数
function completePermissionCheck(encryptionDetected, permissions = []) {
  logDebug('完成权限检查');

  // 标记权限检查完成
  permissionCheckCompleted = true;

  // 发送检查完成事件
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('permission-check-complete', {
      success: true,
      message: '所有权限检查通过',
      encryptionDetected: encryptionDetected
    });
  }

  // 解决权限检查Promise
  if (permissionCheckResolver) {
    permissionCheckResolver({
      success: true,
      permissions: permissions.map(p => ({ ...p, granted: true })),
      encryptionDetected: encryptionDetected
    });
    permissionCheckResolver = null;
  }

  // 权限检查完成后，启动登录状态检查
  setTimeout(async () => {
    logDebug('权限检查完成，开始检查登录状态...');
    try {
      const result = await authProxy.proxyGetOwn();
      logDebug(`登录状态检查结果: ${result.success ? '已登录' : '未登录'}`);

      if (!result.success) {
        // 未登录时显示登录表单（窗口已经显示，无需再次调用show）
        logDebug('用户未登录，显示登录表单');
        mainWindow.webContents.send('show-login');
      } else {
        // 已登录时发送登录成功事件（窗口已经显示，无需再次调用show）
        logDebug('用户已登录，发送登录成功事件');
        mainWindow.webContents.send('login-success', result.data);
      }

      // 登录状态检查完成，解决登录状态检查Promise
      if (loginStatusCheckResolver) {
        loginStatusCheckResolver({
          success: true,
          loginStatus: result
        });
        loginStatusCheckResolver = null;
      }
    } catch (error) {
      logDebug(`登录状态检查失败: ${error}`);
      console.error('Login status check failed:', error);
      // 登录检查失败时显示登录表单（窗口已经显示，无需再次调用show）
      mainWindow.webContents.send('show-login');

      // 即使登录状态检查失败，也解决Promise（因为权限检查已完成）
      if (loginStatusCheckResolver) {
        loginStatusCheckResolver({
          success: false,
          error: error.message
        });
        loginStatusCheckResolver = null;
      }
    }
  }, 1000);
}

// 仅检查登录状态的函数（不包含权限检查）
async function checkLoginStatusOnly() {
  try {
    logDebug('开始检查登录状态...');
    const result = await authProxy.proxyGetOwn();
    logDebug(`登录状态检查结果: ${result.success ? '已登录' : '未登录'}`);

    if (!result.success) {
      // 未登录时显示登录表单（窗口已经显示，无需再次调用show）
      logDebug('用户未登录，显示登录表单');
      mainWindow.webContents.send('show-login');
    } else {
      // 已登录时发送登录成功事件（窗口已经显示，无需再次调用show）
      logDebug('用户已登录，发送登录成功事件');
      mainWindow.webContents.send('login-success', result.data);
    }
  } catch (error) {
    logDebug(`登录状态检查失败: ${error}`);
    console.error('Login status check failed:', error);
    // 登录检查失败时显示登录表单（窗口已经显示，无需再次调用show）
    mainWindow.webContents.send('show-login');
  }
}

// Check if user is logged in
async function checkLoginStatus() {
  return new Promise(async (resolve, reject) => {
    try {
      // 保存resolver以便后续使用
      loginStatusCheckResolver = resolve;

      // 仅执行权限检查，登录检查将在权限检查完成后自动执行
      logDebug('开始权限检查流程...');
      const permissionResult = await checkPermissions();

      if (!permissionResult.success) {
        logDebug(`权限检查未通过: ${permissionResult.message || '未知错误'}`);
        // 权限检查失败，显示错误信息（窗口已经显示，无需再次调用show）
        mainWindow.webContents.send('permission-check-failed', permissionResult);
        reject(new Error(permissionResult.message || '权限检查失败'));
        return;
      }

      // 注意：登录状态检查现在在 completePermissionCheck 中自动执行
      // 这里不再执行登录检查，因为可能权限检查还需要用户交互
      // Promise将在completePermissionCheck中的登录状态检查完成后resolve
    } catch (error) {
      logDebug(`权限检查流程失败: ${error}`);
      console.error('Permission check failed:', error);
      // 权限检查失败时显示登录表单（窗口已经显示，无需再次调用show）
      mainWindow.webContents.send('show-login');
      reject(error);
    }
  });
}

function startWps() {
  const GetExePath = (callback) => {
    if (os.platform() == 'win32') {
      let type = "KWPS.Document.12"
      cp.exec(`REG QUERY HKEY_CLASSES_ROOT\\${type}\\shell\\new\\command /ve`, function (error, stdout, stderr) {
        var strList = stdout.split("    ")
        var val = strList.length > 2 ? strList[3] : undefined;
        if (typeof (val) == "undefined" || val == null) {
          throw new Error("WPS未安装，请安装WPS最新版本。")
        }
        var pos = val.indexOf(".exe");
        if (pos < 0) {
          throw new Error("wps安装异常，请确认有没有正确的安装 WPS 最新版本！")
        }
        val = val.trim()
        if (!val.endsWith("\"%1\"")) {
          console.log("获取 WPS 启动路径异常，继续尝试启动")
        }
        let cmdString = val.replace("\"%1\"", "")
        let cmds = cmdString.split("\"")
        let exePath = cmds[0] ? cmds[0] : cmds[1]
        let rawArgs = []
        if (cmds.length == 1) {
          let data = cmds[0].split(" ")
          exePath = data[0]
          rawArgs = data.splice(1)
        } else if (cmds.length > 1) {
          let idx = cmds[0] ? 1 : 2;
          if (cmds[idx]) {
            rawArgs = cmds[idx].split(" ")
          }
        }
        let args = []
        rawArgs.forEach(function (item) {
          if (item) args.push(item)
        })
        callback(exePath, args)
      });
    } else if (os.platform() == 'darwin') {
      let exePath = `/Applications/wpsoffice.app`
      callback(exePath, [])
    } else {
      let exePath = `/opt/kingsoft/wps-office/office6/wps`
      if (!fs.existsSync(exePath))
        exePath = `/opt/apps/cn.wps.wps-office-pro/files/kingsoft/wps-office/office6/wps`
      callback(exePath, [])
    }
  }
  GetExePath((cmd, args) => {
    //cmd = "f:\\work\\one\\debug\\WPSOffice\\office6\\wps.exe /prometheus /wps /t"
    if (os.platform() == 'win32') {
      cp.spawn(cmd, { detached: true, stdio: ['ignore'] })
    } else if (os.platform() == 'darwin') {
      args = ['-a', `${cmd}`]
      cmd = 'open'
      cp.spawn(cmd, args, { detached: true, stdio: ['ignore'] })
    } else {
      cp.spawn(cmd, { detached: true, stdio: ['ignore'] })
    }
  })
}

// App lifecycle events
app.on('ready', async () => {
  logDebug('应用准备就绪');
  logDebug(`应用版本: ${app.getVersion()}`);
  logDebug(`Electron版本: ${process.versions.electron}`);
  logDebug(`Node版本: ${process.versions.node}`);
  logDebug(`Chrome版本: ${process.versions.chrome}`);
  logDebug(`应用路径: ${app.getAppPath()}`);
  logDebug(`用户数据路径: ${app.getPath('userData')}`);

  // 重置权限检查状态
  resetPermissionCheckStatus();

  // 移除Windows任务栏右键菜单中的Electron选项
  if (process.platform === 'win32') {
    // 设置应用程序名称和AppUserModelId
    app.setAppUserModelId('com.hexin.wps.addon');
    app.name = '万唯AI编辑WPS插件服务';

    // 清空跳转列表和用户任务
    app.setJumpList([]);
    app.setUserTasks([]);
    logDebug('已设置应用标识并移除Windows任务栏右键菜单项');
  }
  // 先初始化配置数据库
  await configStore.waitForInit();


  // 加载环境配置
ENV.loadConfig();

// 将ENV配置暴露为全局变量，供其他模块使用
global.ENV = ENV;

  // 初始化 autoUpdater 诊断
  if (ENV.isProd) {
    logDebug('生产环境 - 初始化 autoUpdater');
    try {
      logDebug(`autoUpdater 对象存在: ${typeof autoUpdater !== 'undefined'}`);
      logDebug(`autoUpdater.downloadUpdate 方法存在: ${typeof autoUpdater.downloadUpdate === 'function'}`);
    } catch (diagError) {
      logDebug(`autoUpdater 诊断失败: ${diagError.message}`);
    }
  } else {
    logDebug('开发环境 - 跳过 autoUpdater 初始化');
  }

  try {
    // 先创建窗口
    await createWindow();
    logDebug('窗口创建完成，开始权限检查和登录状态检查');

    // 执行权限检查和登录状态检查
    await checkLoginStatus();
    logDebug('权限检查和登录状态检查完成，准备启动服务器');

    // 权限检查完成后启动服务器
    await startServer();
    logDebug('服务器启动完成');

    // 添加快捷键支持
    const { globalShortcut } = require('electron');

    // 注册Ctrl+Shift+I快捷键（标准开发者工具快捷键）
    // globalShortcut.register('CommandOrControl+Shift+I', () => {
    //   if (mainWindow) {
    //     logDebug('通过Ctrl+Shift+I快捷键打开开发者工具');
    //     mainWindow.webContents.openDevTools({ mode: 'detach' });
    //   }
    // });
  } catch (error) {
    logDebug(`应用启动流程失败: ${error.message}`);
    console.error('App startup failed:', error);
    dialog.showErrorBox('启动错误', `应用启动失败: ${error.message}`);
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

app.on('before-quit', () => {
  isQuitting = true;
  if (serverInstance) {
    try {
      logDebug('正在关闭服务器...');
      serverInstance.stop();
      logDebug('服务器已关闭');
    } catch (err) {
      logDebug(`关闭服务器时出错: ${err}`);
    }
  }
});

// 更新事件监听
autoUpdater.on('update-available', (info) => {
  logDebug(`发现新版本: ${info.version}`);

  // 发送更新检查结果到UI
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('update-check-result', {
      success: true,
      hasUpdate: true,
      currentVersion: app.getVersion(),
      newVersion: info.version,
      releaseDate: info.releaseDate,
      releaseNotes: info.releaseNotes,
      autoCheck: autoUpdater.autoCheck // 传递自动检查标志
    });
  }
});

autoUpdater.on('update-not-available', (info) => {
  logDebug('当前已是最新版本');

  // 发送更新检查结果到UI
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('update-check-result', {
      success: true,
      hasUpdate: false,
      currentVersion: app.getVersion(),
      message: '您使用的已是最新版本',
      autoCheck: autoUpdater.autoCheck // 传递自动检查标志
    });
  }
});

autoUpdater.on('error', (err) => {
  logDebug(`更新过程中出现错误: ${err.message}`);
  logDebug(`错误详情: ${JSON.stringify(err, null, 2)}`);

  // 发送更新错误到UI
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('update-error', {
      message: `更新过程中出现问题: ${err.message}`,
      error: err
    });
  }
});

autoUpdater.on('download-progress', (progressObj) => {
  const percent = Math.round(progressObj.percent || 0);
  const transferred = progressObj.transferred || 0;
  const total = progressObj.total || 0;
  const speed = progressObj.bytesPerSecond || 0;

  logDebug(`下载进度: ${percent}% (${transferred}/${total}) 速度: ${speed} B/s`);

  // 发送下载进度到UI
  if (mainWindow && mainWindow.webContents) {
    try {
      mainWindow.webContents.send('update-download-progress', {
        percent: progressObj.percent || 0,
        transferred: transferred,
        total: total,
        bytesPerSecond: speed
      });
      logDebug(`已发送进度到UI: ${percent}%`);
    } catch (error) {
      logDebug(`发送进度到UI时出错: ${error.message}`);
    }
  }
});

// 添加下载取消事件监听
autoUpdater.on('update-cancelled', (info) => {
  logDebug('更新下载被取消');

  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('update-error', {
      message: '更新下载已取消'
    });
  }
});

autoUpdater.on('update-downloaded', (info) => {
  logDebug(`更新下载完成: ${info.version}`);

  // 发送下载完成到UI
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.send('update-download-complete', {
      success: true,
      version: info.version,
      message: '下载完成，准备安装'
    });
  }
});

ipcMain.on('login-attempt', async (event, credentials) => {
  try {
    logDebug(`桌面应用发起登录请求: ${credentials.username}`);
    const result = await authProxy.proxyLogin(credentials);

    if (result.success) {
      logDebug('登录成功，开始权限检查');

      // 登录成功后，调用 proxyGetOwn 检查权限
      try {
        const ownResult = await authProxy.proxyGetOwn();

        if (ownResult.success) {
          logDebug('权限检查通过，发送登录成功事件到桌面应用');
          event.sender.send('login-success', ownResult.data);

          if (serverInstance && serverInstance.authEvents) {
            logDebug('触发authEvents登录事件');
            serverInstance.authEvents.emit('login-success', ownResult.data);
          }

          if (serverInstance && serverInstance.wsServer) {
            logDebug('通过WebSocket广播桌面应用登录事件');
            serverInstance.wsServer.broadcast({
              type: 'auth',
              eventType: 'login-success',
              data: ownResult.data,
              timestamp: new Date().toISOString()
            });
          }
        } else {
          logDebug(`权限检查失败: ${ownResult.message}, code: ${ownResult.code}`);
          // 传递权限检查失败的详细信息，包括错误码
          event.sender.send('login-failure', {
            message: ownResult.message,
            code: ownResult.code,
            data: ownResult.data
          });
        }
      } catch (permissionError) {
        logDebug(`权限检查过程中发生错误: ${permissionError}`);
        console.error('Permission check failed:', permissionError);
        event.sender.send('login-failure', { message: '权限检查失败，请稍后重试' });
      }
    } else {
      logDebug(`登录失败: ${result.message}`);
      event.sender.send('login-failure', { message: result.message });
    }
  } catch (error) {
    console.error('Login attempt failed:', error);
    logDebug(`登录尝试失败: ${error}`);
    event.sender.send('login-failure', { message: 'Login attempt failed' });
  }
});

ipcMain.on('logout', async (event) => {
  try {
    logDebug('桌面应用发起登出请求');
    await authProxy.proxyLogout();
    event.sender.send('logout-success');

    // 通过WebSocket广播登出事件
    if (serverInstance && serverInstance.wsServer) {
      logDebug('通过WebSocket广播桌面应用登出事件');
      serverInstance.wsServer.broadcast({
        type: 'auth',
        eventType: 'logout',
        timestamp: new Date().toISOString()
      });
    }

    // 触发authEvents事件，确保一致性
    if (serverInstance && serverInstance.authEvents) {
      logDebug('触发authEvents登出事件');
      serverInstance.authEvents.emit('logout');
    }

    // 登出后显示登录表单（窗口已经显示，无需再次调用show）
    mainWindow.webContents.send('show-login');
  } catch (error) {
    console.error('Logout failed:', error);
    logDebug(`登出失败: ${error}`);
    event.sender.send('logout-failure', { message: 'Logout attempt failed' });
  }
});

ipcMain.on('show-main-window', () => {
  if (mainWindow) {
    mainWindow.show();
    mainWindow.focus();
  }
});

// 添加对窗口控制的IPC消息处理
ipcMain.on('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.on('hide-window', () => {
  if (mainWindow) {
    mainWindow.hide();
  }
});

// 添加获取用户信息的IPC消息处理
ipcMain.on('get-user-info', async (event) => {
  try {
    const result = await authProxy.proxyGetOwn();

    if (result.success) {
      event.sender.send('user-info-response', result);
    } else {
      logDebug('获取用户信息失败');
      event.sender.send('user-info-response', { success: false, message: '获取用户信息失败' });
    }
  } catch (error) {
    logDebug(`获取用户信息发生错误: ${error}`);
    console.error('Get user info failed:', error);
    event.sender.send('user-info-response', { success: false, message: '获取用户信息失败' });
  }
});

// 增强开发者工具处理程序
ipcMain.on('open-devtools', (event) => {
  logDebug('收到打开开发者工具的IPC消息');
  if (mainWindow) {
    logDebug('打开开发者工具');
    try {
      mainWindow.webContents.openDevTools({ mode: 'detach' });
      logDebug('开发者工具已打开');
    } catch (err) {
      logDebug(`打开开发者工具失败: ${err.message}`);
    }
  } else {
    logDebug('mainWindow不存在，无法打开开发者工具');
  }
});


ipcMain.on('start-wps', () => {
  try {
    startWps();
  } catch (error) {
    logDebug(`启动 WPS 时发生错误: ${error.message}`);
    // 可以选择向渲染进程发送错误信息
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('wps-start-error', {
        message: `启动 WPS 失败: ${error.message}`
      });
    }
  }
});

// 处理更新检查的通用函数
function checkForUpdates(isAutoCheck = false) {
  logDebug(`开始${isAutoCheck ? '自动' : '手动'}检查更新`);

  try {
    if (ENV.isProd) {
      // 仅在生产环境中检查更新
      logDebug('生产环境，开始检查更新');
      // 根据版本类型设置不同的更新URL
      const updateUrl = ENV.config.EDITION === 'hexin'
        ? 'https://dl.hexinedu.com/exe/hexin-wps-addon/'
        : 'https://dl.hexinedu.com/exe/wps-addon/';
      logDebug(`设置更新URL: ${updateUrl} (版本: ${ENV.config.EDITION})`);
      autoUpdater.setFeedURL(updateUrl);

      // 保存当前检查模式，以便在事件中区分
      autoUpdater.autoCheck = isAutoCheck;

      autoUpdater.checkForUpdates();
    } else {
      // 开发环境模拟更新检查
      logDebug('开发环境，模拟更新检查');
      setTimeout(() => {
        if (mainWindow && mainWindow.webContents) {
          // 开发环境模拟更新行为
          // 设置为true表示始终模拟有更新，便于测试
          // const hasUpdate = !isAutoCheck;  // 自动检查时无更新，手动检查时显示有更新
          const hasUpdate = true; // 开发环境测试模式：自动检查和手动检查都显示有更新

          if (hasUpdate) {
            // 模拟发现新版本
            const currentVersionParts = app.getVersion().split('.').map(Number);
            const newVersionParts = [...currentVersionParts];
            newVersionParts[2] += 1; // 增加补丁版本号
            const newVersion = newVersionParts.join('.');

            mainWindow.webContents.send('update-check-result', {
              success: true,
              hasUpdate: true,
              currentVersion: app.getVersion(),
              newVersion: newVersion,
              releaseDate: new Date().toISOString(),
              releaseNotes: `## v${newVersion} 更新内容

**新功能：**
- 添加了更新日志显示功能
- 优化了界面响应速度
- 增强了网络连接稳定性

**修复：**
- 修复了某些情况下的内存泄漏问题
- 解决了权限检查时的偶发错误
- 改进了文件夹选择的用户体验

**改进：**
- 更新了用户界面样式
- 优化了错误提示信息
- 提升了整体性能

**开发模式提示：** 这是开发环境下的模拟更新日志，用于测试更新日志显示功能。`,
              autoCheck: isAutoCheck // 添加自动检查标志
            });
          } else {
            // 模拟已是最新版本的情况
            mainWindow.webContents.send('update-check-result', {
              success: true,
              hasUpdate: false,
              currentVersion: app.getVersion(),
              message: '您使用的已是最新版本（开发模式）',
              autoCheck: isAutoCheck // 添加自动检查标志
            });
          }
        }
      }, 1000);
    }
  } catch (error) {
    logDebug(`检查更新时发生错误: ${error.message}`);
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('update-error', {
        message: `检查更新失败: ${error.message}`,
        autoCheck: isAutoCheck
      });
    }
  }
}

// 手动检查更新
ipcMain.on('check-update', () => {
  logDebug('收到手动检查更新请求');
  checkForUpdates(false);
});

// 自动检查更新
ipcMain.on('auto-check-update', () => {
  logDebug('收到自动检查更新请求');
  checkForUpdates(true);
});

// 添加下载更新的IPC处理
ipcMain.on('download-update', () => {
  logDebug('收到下载更新请求');

  try {
    if (ENV.isProd) {
      logDebug('开始下载更新');

      // 发送初始进度事件
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('update-download-progress', {
          percent: 0,
          transferred: 0,
          total: 0,
          bytesPerSecond: 0
        });
      }

      // 调用下载更新
      autoUpdater.downloadUpdate();
      logDebug('已调用 autoUpdater.downloadUpdate()');

      // 设置超时检查，如果10秒内没有收到进度事件，发送警告
      setTimeout(() => {
        logDebug('检查下载进度超时 - 如果没有看到进度更新，可能存在问题');
      }, 10000);

    } else {
      // 开发环境模拟下载过程
      logDebug('开发环境，模拟下载过程');
      let progress = 0;
      let updateCount = 0;

      // 立即发送第一个进度事件
      if (mainWindow && mainWindow.webContents) {
        const total = 50 * 1024 * 1024; // 50MB
        const transferred = 0;
        const bytesPerSecond = 1024 * 1024 * 2; // 2MB/s

        mainWindow.webContents.send('update-download-progress', {
          percent: 0,
          transferred: transferred,
          total: total,
          bytesPerSecond: bytesPerSecond
        });
      }

      const downloadInterval = setInterval(() => {
        updateCount++;
        // 前几次更新确保有明显进度
        if (updateCount <= 3) {
          progress += 8 + Math.random() * 7; // 8-15% 的增长
        } else {
          progress += 3 + Math.random() * 12; // 3-15% 的增长
        }

        if (progress >= 100) {
          progress = 100;
          clearInterval(downloadInterval);

          // 发送最终进度
          if (mainWindow && mainWindow.webContents) {
            const total = 50 * 1024 * 1024;
            mainWindow.webContents.send('update-download-progress', {
              percent: 100,
              transferred: total,
              total: total,
              bytesPerSecond: 1024 * 1024 * 2
            });
          }

          // 延迟一下再发送完成事件，让用户看到100%
          setTimeout(() => {
            if (mainWindow && mainWindow.webContents) {
              mainWindow.webContents.send('update-download-complete', {
                success: true,
                version: `${app.getVersion()}-dev`,
                message: '下载完成，准备安装（开发模式）'
              });
            }
          }, 500);
        } else {
          // 模拟下载进度
          if (mainWindow && mainWindow.webContents) {
            const total = 50 * 1024 * 1024; // 50MB
            const transferred = Math.floor((progress / 100) * total);
            const bytesPerSecond = (1024 * 1024 * 1.5) + Math.random() * (1024 * 1024); // 1.5-2.5MB/s

            logDebug(`发送下载进度: ${progress.toFixed(1)}% (${transferred}/${total})`);

            mainWindow.webContents.send('update-download-progress', {
              percent: progress,
              transferred: transferred,
              total: total,
              bytesPerSecond: bytesPerSecond
            });
          }
        }
      }, 800);
    }
  } catch (error) {
    logDebug(`下载更新时发生错误: ${error.message}`);
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('update-error', {
        message: `下载更新失败: ${error.message}`
      });
    }
  }
});

// 添加安装更新的IPC处理
ipcMain.on('install-update', () => {
  logDebug('收到安装更新请求');

  try {
    if (ENV.isProd) {
      logDebug('开始安装更新，应用即将重启');
      // 设置退出标志，避免阻止退出
      isQuitting = true;

      // 安装更新并重启应用
      setImmediate(() => {
        autoUpdater.quitAndInstall();
      });
    } else {
      // 开发环境提示
      logDebug('开发环境，不执行实际安装');
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('update-error', {
          message: '开发模式下不支持安装更新'
        });
      }
    }
  } catch (error) {
    logDebug(`安装更新时发生错误: ${error.message}`);
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('update-error', {
        message: `安装更新失败: ${error.message}`
      });
    }
  }
});

// 添加取消更新的IPC处理（预留功能）
ipcMain.on('cancel-update', () => {
  logDebug('收到取消更新请求');

  try {
    // electron-updater 没有提供直接取消下载的API
    // 这里只是记录日志，实际取消需要重启应用
    logDebug('更新取消功能暂不支持，需要重启应用');

    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('update-error', {
        message: '取消更新功能暂不支持，请重启应用'
      });
    }
  } catch (error) {
    logDebug(`取消更新时发生错误: ${error.message}`);
  }
});

// 处理选择网络文件夹的请求
ipcMain.on('select-network-folder', async (event) => {
  try {
    logDebug('收到选择网络文件夹请求');

    // 新增：检查是否是从机器码弹窗触发的（通过检查当前是否已完成权限检查）
    const isFromMachineDialog = permissionCheckCompleted;
    isMonitorFolderReset = isFromMachineDialog;

    logDebug(`文件夹选择来源: ${isFromMachineDialog ? '机器码弹窗' : '权限检查流程'}`);

    const result = await dialog.showOpenDialog(mainWindow, {
      title: '选择网络设备',
      properties: ['openDirectory'],
      message: '请选择一个网络设备（如 \\\\************），程序将自动创建所需的文件夹'
    });

    if (!result.canceled && result.filePaths.length > 0) {
      const selectedPath = result.filePaths[0];
      logDebug(`用户选择的路径: ${selectedPath}`);

      // 验证所选路径是否为网络路径或网络设备
      const isNetwork = isNetworkDevice(selectedPath);

      if (isNetwork) {
        logDebug('所选路径是网络设备，准备创建应用文件夹');

        try {
          // 获取机器码，确保在创建文件夹前已生成
          const machineCodeInfo = await machineCodeGenerator.getMachineCode();
          const machineCode = machineCodeInfo.code;
          logDebug(`当前机器码: ${machineCode}`);

          // 从选择的网络路径中提取主机名部分
          let hostPart = selectedPath;
          if (selectedPath.startsWith('\\\\')) {
            // 提取主机名部分 (\\server 或 \\IP)
            const pathParts = selectedPath.split('\\').filter(Boolean);
            hostPart = `\\\\${pathParts[0]}`;
          }

          // 创建新格式的目录路径: 主机名\图片\机器码\ww-wps-addon\Temp
          const appFolderPath = path.join(hostPart, '图片', machineCode, 'ww-wps-addon', 'Temp');

          logDebug(`准备创建文件夹: ${appFolderPath}`);

          // 创建目录结构
          if (!fs.existsSync(appFolderPath)) {
            fs.mkdirSync(appFolderPath, { recursive: true });
            logDebug(`已创建文件夹: ${appFolderPath}`);
          } else {
            logDebug(`文件夹已存在: ${appFolderPath}`);
          }

          // 验证文件夹是否创建成功
          if (fs.existsSync(appFolderPath)) {
            logDebug('文件夹创建成功，发送选择结果');

            // 根据来源发送不同的响应事件
            const responseEvent = isMonitorFolderReset ? 'monitor-folder-reset-response' : 'network-folder-selected';
            const responseData = {
              success: true,
              path: appFolderPath,
              isNetwork: true,
              networkDevice: hostPart,
              machineCode: machineCode,
              autoCreated: true
            };

            // 如果是监控文件夹重设，添加新路径信息并更新配置
            if (isMonitorFolderReset) {
              responseData.newPath = appFolderPath;

              // 更新配置存储
              configStore.setWatchDir(appFolderPath).then((success) => {
                if (success) {
                  logDebug(`监控文件夹配置已更新: ${appFolderPath}`);
                } else {
                  logDebug('监控文件夹配置更新失败');
                }
              }).catch((err) => {
                logDebug(`监控文件夹配置更新出错: ${err.message}`);
              });
            }

            event.sender.send(responseEvent, responseData);
          } else {
            logDebug('文件夹创建失败');
            const responseEvent = isMonitorFolderReset ? 'monitor-folder-reset-response' : 'network-folder-selected';
            event.sender.send(responseEvent, {
              success: false,
              path: selectedPath,
              isNetwork: true,
              message: '无法在所选网络设备上创建文件夹，请检查网络连接和权限'
            });
          }

        } catch (createError) {
          logDebug(`创建文件夹时出错: ${createError.message}`);
          const responseEvent = isMonitorFolderReset ? 'monitor-folder-reset-response' : 'network-folder-selected';
          event.sender.send(responseEvent, {
            success: false,
            path: selectedPath,
            isNetwork: true,
            message: `创建文件夹失败: ${createError.message}。请确保对网络设备有写入权限。`
          });
        }

      } else {
        logDebug('所选路径不是网络设备');
        const responseEvent = isMonitorFolderReset ? 'monitor-folder-reset-response' : 'network-folder-selected';
        event.sender.send(responseEvent, {
          success: false,
          path: selectedPath,
          isNetwork: false,
          message: '所选路径不是网络设备，请选择网络路径（如 \\\\服务器IP 或映射的网络驱动器）'
        });
      }
    } else {
      logDebug('用户取消选择文件夹');
      const responseEvent = isMonitorFolderReset ? 'monitor-folder-reset-response' : 'network-folder-selected';
      event.sender.send(responseEvent, {
        success: false,
        canceled: true,
        message: '用户取消选择'
      });
    }
  } catch (error) {
    logDebug(`选择网络文件夹时出错: ${error.message}`);
    const responseEvent = isMonitorFolderReset ? 'monitor-folder-reset-response' : 'network-folder-selected';
    event.sender.send(responseEvent, {
      success: false,
      message: `选择文件夹时出错: ${error.message}`
    });
  }
});

// 处理确认使用网络文件夹的请求
ipcMain.on('confirm-network-folder', async (event, data) => {
  try {
    logDebug(`确认使用网络文件夹: ${data.path}`);

    // 获取机器码
    const machineCodeInfo = await machineCodeGenerator.getMachineCode();
    const machineCode = machineCodeInfo.code;

    // 检验路径是否符合要求的结构
    const validateResult = await validateAndFixNetworkFolder(data.path);
    const finalPath = validateResult.success ? validateResult.path : data.path;

    // 更新配置
    const success = await configStore.setWatchDir(finalPath);

    if (success) {
      logDebug(`网络文件夹配置更新成功: ${finalPath}`);

      // 标记网络文件夹已配置
      networkFolderConfigured = true;

      // 发送配置更新成功事件
      event.sender.send('network-folder-confirmed', {
        success: true,
        newWatchDir: finalPath,
        machineCode: machineCode,
        pathChanged: validateResult.changed
      });

      // 完成权限检查流程
      setTimeout(() => {
        completePermissionCheck(true); // encryptionDetected = true
      }, 1000);
    } else {
      logDebug('网络文件夹配置更新失败');
      event.sender.send('network-folder-confirmed', {
        success: false,
        message: '配置更新失败，请重试'
      });
    }
  } catch (error) {
    logDebug(`确认网络文件夹时出错: ${error.message}`);
    event.sender.send('network-folder-confirmed', {
      success: false,
      message: `配置更新时出错: ${error.message}`
    });
  }
});

// 处理跳过网络文件夹配置的请求
ipcMain.on('skip-network-folder-config', async (event) => {
  try {
    logDebug('用户选择跳过网络文件夹配置，继续权限检查流程');

    // 完成权限检查流程
    setTimeout(() => {
      completePermissionCheck(true); // encryptionDetected = true，但跳过了网络文件夹配置
    }, 500);

  } catch (error) {
    logDebug(`跳过网络文件夹配置时出错: ${error.message}`);
  }
});

// 处理网络连接重试请求
ipcMain.on('retry-network-connection', async (event) => {
  try {
    logDebug('用户请求重试网络连接');

    // 重新执行权限检查的网络部分
    const machineCodeInfo = await machineCodeGenerator.getMachineCode();
    const machineCode = machineCodeInfo.code;
    const networkHost = '\\\\***********';

    console.log(`重试网络连接检查: ${networkHost}`);

    // 重新检查网络连接
    const connectionResult = await checkNetworkConnection(networkHost);

    if (connectionResult.success) {
      console.log('网络连接重试成功，继续创建文件夹');

      try {
        const fixedNetworkPath = `${networkHost}\\图片\\${machineCode}\\ww-wps-addon\\Temp`;

        console.log(`重试创建网络文件夹: ${fixedNetworkPath}`);

        // 创建目录结构（如果不存在）
        if (!fs.existsSync(fixedNetworkPath)) {
          fs.mkdirSync(fixedNetworkPath, { recursive: true });
          console.log(`重试成功，已创建网络文件夹: ${fixedNetworkPath}`);
        } else {
          console.log(`重试检查，网络文件夹已存在: ${fixedNetworkPath}`);
        }

        // 更新配置
        await configStore.setWatchDir(fixedNetworkPath);
        networkFolderConfigured = true;

        // 发送成功事件
        event.sender.send('network-connection-retry-result', {
          success: true,
          path: fixedNetworkPath,
          message: '网络连接成功，文件夹创建完成'
        });

        // 继续权限检查流程
        setTimeout(() => {
          completePermissionCheck(true);
        }, 1000);

      } catch (createError) {
        console.log(`重试创建文件夹失败: ${createError.message}`);
        event.sender.send('network-connection-retry-result', {
          success: false,
          message: `创建文件夹失败: ${createError.message}`
        });
      }
    } else {
      console.log('网络连接重试仍然失败');

      // 构建详细的失败信息
      let message = '网络连接重试失败。';

      // 如果有组策略检查结果和建议，包含在消息中
      message += '<p>请联系管理员获取支持。</p>';

      event.sender.send('network-connection-retry-result', {
        success: false,
        message: message,
        policyCheck: connectionResult.policyCheck,
        suggestions: connectionResult.suggestions
      });
    }

  } catch (error) {
    logDebug(`网络连接重试时出错: ${error.message}`);
    event.sender.send('network-connection-retry-result', {
      success: false,
      message: `重试时出错: ${error.message}`
    });
  }
});

// 处理跳过网络连接的请求
ipcMain.on('skip-network-connection', async (event) => {
  try {
    logDebug('用户选择跳过网络连接，使用默认路径');

    // 标记网络文件夹已配置（使用默认路径）
    networkFolderConfigured = true;

    // 发送跳过结果
    event.sender.send('network-connection-skip-result', {
      success: true,
      message: '已跳过网络连接，使用默认路径'
    });

    // 继续权限检查流程
    setTimeout(() => {
      completePermissionCheck(true);
    }, 500);

  } catch (error) {
    logDebug(`跳过网络连接时出错: ${error.message}`);
    event.sender.send('network-connection-skip-result', {
      success: false,
      message: `跳过网络连接时出错: ${error.message}`
    });
  }
});

// 添加获取机器码的IPC消息处理
ipcMain.on('get-machine-code', async (event) => {
  try {
    logDebug('获取机器码');
    const machineCodeInfo = await machineCodeGenerator.getMachineCode();
    event.sender.send('machine-code-response', {
      success: true,
      ...machineCodeInfo
    });
  } catch (error) {
    logDebug(`获取机器码失败: ${error.message}`);
    event.sender.send('machine-code-response', {
      success: false,
      message: `获取机器码失败: ${error.message}`
    });
  }
});

// 新增：添加获取当前监控文件夹的IPC消息处理
ipcMain.on('get-current-monitor-dir', async (event) => {
  try {
    logDebug('获取当前监控文件夹');
    const currentWatchDir = configStore.getWatchDir();
    event.sender.send('current-monitor-dir-response', {
      success: true,
      path: currentWatchDir
    });
  } catch (error) {
    logDebug(`获取当前监控文件夹失败: ${error.message}`);
    event.sender.send('current-monitor-dir-response', {
      success: false,
      message: `获取当前监控文件夹失败: ${error.message}`
    });
  }
});

// 添加刷新机器码的IPC消息处理
ipcMain.on('refresh-machine-code', async (event) => {
  try {
    logDebug('刷新机器码');
    const machineCodeInfo = await machineCodeGenerator.refreshMachineCode();
    event.sender.send('machine-code-refresh-response', {
      success: true,
      ...machineCodeInfo
    });
  } catch (error) {
    logDebug(`刷新机器码失败: ${error.message}`);
    event.sender.send('machine-code-refresh-response', {
      success: false,
      message: `刷新机器码失败: ${error.message}`
    });
  }
});

// 添加验证机器码的IPC消息处理
ipcMain.on('verify-machine-code', async (event, { code }) => {
  try {
    logDebug(`验证机器码: ${code}`);
    const isValid = await machineCodeGenerator.verifyMachineCode(code);
    event.sender.send('machine-code-verify-response', {
      success: true,
      isValid
    });
  } catch (error) {
    logDebug(`验证机器码失败: ${error.message}`);
    event.sender.send('machine-code-verify-response', {
      success: false,
      message: `验证机器码失败: ${error.message}`
    });
  }
});

// 添加检查组策略设置的IPC消息处理
ipcMain.on('check-guest-login-policy', async (event) => {
  try {
    logDebug('手动检查组策略设置');
    const policyCheck = checkGuestLoginPolicy();
    event.sender.send('guest-login-policy-response', {
      success: true,
      ...policyCheck
    });
  } catch (error) {
    logDebug(`检查组策略失败: ${error.message}`);
    event.sender.send('guest-login-policy-response', {
      success: false,
      message: `检查组策略失败: ${error.message}`
    });
  }
});

// 添加启用组策略设置的IPC消息处理
ipcMain.on('enable-guest-login-policy', async (event) => {
  try {
    logDebug('尝试启用不安全的来宾登录组策略');

    if (process.platform !== 'win32') {
      event.sender.send('guest-login-policy-enable-response', {
        success: false,
        message: '此功能仅在Windows系统上可用'
      });
      return;
    }

    // 尝试通过注册表启用设置
    const regCommand = 'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\LanmanWorkstation\\Parameters" /v AllowInsecureGuestAuth /t REG_DWORD /d 1 /f';

    // 使用chcp 65001设置UTF-8编码，避免中文乱码
    const fullCommand = process.platform === 'win32'
      ? `chcp 65001 >nul 2>&1 && ${regCommand}`
      : regCommand;

    logDebug(`执行命令: ${fullCommand}`);

    try {
      const result = cp.execSync(fullCommand, {
        encoding: 'utf8',
        timeout: 10000,
        stdio: 'pipe' // 避免输出到控制台
      });

      logDebug(`命令执行结果: ${result}`);

      // 验证设置是否成功
      const verifyResult = checkGuestLoginPolicy();

      if (verifyResult.enabled) {
        logDebug('组策略设置启用成功');
        event.sender.send('guest-login-policy-enable-response', {
          success: true,
          message: '不安全的来宾登录已启用，建议重启系统以确保生效',
          needReboot: true
        });
      } else {
        logDebug('组策略设置启用失败 - 验证结果为未启用');
        event.sender.send('guest-login-policy-enable-response', {
          success: false,
          message: '设置可能未完全生效，请手动通过组策略编辑器进行配置，或尝试重启后再次检查'
        });
      }

    } catch (execError) {
      logDebug(`执行注册表命令失败: ${execError.message}`);

      // 分析具体的错误类型
      let message = '启用组策略失败';
      let suggestions = [];

      const errorMsg = execError.message.toLowerCase();

      if (errorMsg.includes('access') || errorMsg.includes('denied') ||
        errorMsg.includes('拒绝') || errorMsg.includes('权限') ||
        execError.status === 1) {
        // 权限问题
        message = '<p><b>权限不足，无法修改注册表</b></p>';
        suggestions = [
          '请以管理员身份运行此程序'
        ];
      } else if (errorMsg.includes('timeout')) {
        message = '操作超时';
        suggestions = ['请重试，如果持续失败请手动配置组策略'];
      } else {
        message = '注册表操作失败';
        suggestions = [
          '请确保Windows系统正常',
          '尝试手动配置组策略',
          '如果问题持续，请联系系统管理员'
        ];
      }

      const fullMessage = suggestions.length > 0
        ? message + '<p>解决方案：</p>' + suggestions.join('\n')
        : message;

      event.sender.send('guest-login-policy-enable-response', {
        success: false,
        message: fullMessage
      });
    }

  } catch (error) {
    logDebug(`启用组策略外层异常: ${error.message}`);

    event.sender.send('guest-login-policy-enable-response', {
      success: false,
      message: `操作失败: ${error.message}\n\n请尝试手动配置组策略或以管理员身份运行程序。`
    });
  }
});

// 添加获取组策略配置帮助信息的IPC消息处理
ipcMain.on('get-guest-login-help', async (event) => {
  try {
    logDebug('获取组策略配置帮助信息');

    const helpInfo = {
      title: '启用不安全的来宾登录',
      description: '此设置允许访问网络共享文件夹，是连接网络驱动器的必要条件',
      steps: [
        '按 Win + R 打开"运行"对话框',
        '输入 gpedit.msc 并按回车键，打开本地组策略编辑器',
        '在左侧导航栏中展开：计算机配置',
        '继续展开：管理模板 → 网络 → Lanman工作站',
        '在右侧找到"启用不安全的来宾登录"策略',
        '双击该策略，选择"已启用"',
        '点击"确定"保存设置',
        '建议重启计算机以确保设置生效'
      ],
      alternativeMethod: {
        title: '注册表方法（需要管理员权限）',
        warning: '修改注册表可能影响系统稳定性，请谨慎操作',
        steps: [
          '以管理员身份运行命令提示符',
          '执行以下命令：',
          'reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\LanmanWorkstation\\Parameters" /v AllowInsecureGuestAuth /t REG_DWORD /d 1 /f',
          '重启计算机'
        ]
      },
      troubleshooting: [
        '如果组策略编辑器无法打开，可能是因为您使用的是Windows Home版本',
        'Windows Home版本默认不包含组策略编辑器，只能通过注册表方法修改',
        '如果修改后仍无法连接，请检查网络防火墙设置',
        '确认目标网络设备允许来宾访问'
      ]
    };

    event.sender.send('guest-login-help-response', {
      success: true,
      helpInfo: helpInfo
    });

  } catch (error) {
    logDebug(`获取组策略帮助信息失败: ${error.message}`);
    event.sender.send('guest-login-help-response', {
      success: false,
      message: `获取帮助信息失败: ${error.message}`
    });
  }
});

