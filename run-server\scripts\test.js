const cp = require('child_process');

function isProcessRunning(processNamePattern) {
  try {
    let command;
    let output;

    if (process.platform === 'win32') {
      // Windows 平台
      command = 'tasklist /FO CSV';
      output = cp.execSync(command, { encoding: 'utf8' });
      // 创建正则表达式模式
      const regex = new RegExp(`${processNamePattern}`, 'i');
      return regex.test(output);

    } else if (process.platform === 'darwin') {
      // macOS 平台
      command = 'ps -ax';
      output = cp.execSync(command, { encoding: 'utf8' });

      // 创建正则表达式模式，排除 grep 本身
      const regex = new RegExp(`[^(grep)].*${processNamePattern}`, 'i');
      return regex.test(output);

    } else {
      // Linux 平台
      command = 'ps -A';
      output = cp.execSync(command, { encoding: 'utf8' });

      // 创建正则表达式模式，排除 grep 本身
      const regex = new RegExp(`[^(grep)].*${processNamePattern}`, 'i');
      return regex.test(output);
    }
  } catch (error) {
    console.error(`检测进程运行状态出错: ${error}`);
    return false;
  }
}

// 使用示例
// 可以模糊匹配 "wps"、"wpsoffice" 等包含 "wps" 的进程
const isWpsRunning = isProcessRunning('CDGRegedit') || isProcessRunning('watchctrl');
console.log(`WPS 是否运行: ${isWpsRunning}`);
