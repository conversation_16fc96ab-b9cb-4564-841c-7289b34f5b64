<template>
  <div class="image-split-container">
    <div class="header">
      <h2>图片分割</h2>
      <div class="status-indicator" :class="{ 'has-image': hasSelectedImage }">
        <span v-if="hasSelectedImage" class="status-text success">✓ 已选中图片</span>
        <span v-else class="status-text warning">⚠ 未选中图片</span>
      </div>
    </div>

    <!-- 开始分割按钮放在顶部 -->
    <div class="top-action-section">
      <button class="start-split-btn" :class="{ 'disabled': !hasSelectedImage || isProcessing }"
        :disabled="!hasSelectedImage || isProcessing" @click="openSplitModal">
        <span v-if="isProcessing" class="loading-spinner"></span>
        <span>{{ isProcessing ? '处理中...' : '开始分割' }}</span>
      </button>
    </div>

    <div class="content">
      <div class="instruction-section">
        <h3>使用说明</h3>
        <ol>
          <li>在文档中选择要分割的图片</li>
          <li>点击上方的"开始分割"按钮</li>
          <li>在弹窗中手动设置分割线或矩形</li>
          <li>确认分割并插入到文档</li>
        </ol>
      </div>

      <!-- <div class="selection-info">
        <h3>当前选择</h3>
        <div class="selection-details">
          <p><strong>选择类型:</strong> {{ selectionType }}</p>
          <p><strong>图片数量:</strong> {{ imageCount }}</p>
          <p v-if="selectionText"><strong>文本内容:</strong> {{ selectionText.substring(0, 50) }}{{ selectionText.length > 50 ? '...' : '' }}</p>
        </div>
      </div> -->



      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="success-message">
        {{ successMessage }}
      </div>
    </div>

    <!-- 分割弹窗 -->
    <div v-if="showSplitModal" class="modal-overlay" @click="closeSplitModal">
      <div class="split-modal" @click.stop>
        <div class="modal-header">
          <h3>图片分割工具</h3>
          <button class="close-btn" @click="closeSplitModal">×</button>
        </div>

        <div class="modal-content">
          <!-- 工具栏 -->
          <div class="toolbar">
            <div class="tool-group">
              <button class="tool-btn" :class="{
                active: currentTool === 'horizontal',
                disabled: isToolDisabled('horizontal')
              }" :disabled="isToolDisabled('horizontal')" @click="setTool('horizontal')"
                :title="getToolTooltip('horizontal')">
                <span class="tool-icon">➖</span>
                <span class="tool-text">横线</span>
              </button>
              <button class="tool-btn" :class="{
                active: currentTool === 'vertical',
                disabled: isToolDisabled('vertical')
              }" :disabled="isToolDisabled('vertical')" @click="setTool('vertical')"
                :title="getToolTooltip('vertical')">
                <span class="tool-icon">|</span>
                <span class="tool-text">竖线</span>
              </button>
              <button class="tool-btn" :class="{
                active: currentTool === 'rectangle',
                disabled: isToolDisabled('rectangle')
              }" :disabled="isToolDisabled('rectangle')" @click="setTool('rectangle')"
                :title="getToolTooltip('rectangle')">
                <span class="tool-icon">⬜</span>
                <span class="tool-text">矩形</span>
              </button>
            </div>
            <div class="tool-actions">
              <button class="clear-btn" @click="clearSplits">清除所有</button>
              <button class="undo-btn" @click="undoLastSplit">撤销</button>
            </div>
          </div>

          <!-- 图片编辑区域 -->
          <div class="image-editor">
            <canvas ref="imageCanvas" class="image-canvas" @mousedown="onCanvasMouseDown" @mousemove="onCanvasMouseMove"
              @mouseup="onCanvasMouseUp" @mouseleave="onCanvasMouseLeave"></canvas>

            <!-- Loading 覆盖层 -->
            <div v-if="isLoadingImage" class="loading-overlay">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载真实图片...</div>
            </div>
          </div>


        </div>

        <div class="modal-footer">
          <button class="cancel-btn" @click="closeSplitModal">取消</button>
          <button class="confirm-btn" :disabled="splitRegions.length === 0 || isProcessing" @click="confirmSplit">
            <span v-if="isProcessing" class="loading-spinner"></span>
            <span>{{ isProcessing ? '分割中...' : `确认分割 (${getExpectedSplitCount()}个片段)` }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import wsClient from './js/wsClient'

// 响应式数据
const hasSelectedImage = ref(false)
const selectionType = ref('无选择')
const imageCount = ref(0)
const selectionText = ref('')
const isProcessing = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const isLoadingImage = ref(false) // 图片加载状态
const currentImageInfo = ref(null) // 当前图片信息

// 分割弹窗相关
const showSplitModal = ref(false)
const currentTool = ref('horizontal') // 'horizontal', 'vertical', 'rectangle'
const splitRegions = ref([])
const selectedRegion = ref(-1)

// Canvas相关
const imageCanvas = ref(null)
const isDrawing = ref(false)
const startPoint = ref({ x: 0, y: 0 })
const currentImage = ref(null)
const watchedDir = ref('C:\\ww-wps-addon\\Temp') // 监控目录

// 检查选择状态的定时器
let selectionCheckTimer = null

// 计算当前已使用的工具类型
const usedToolType = computed(() => {
  if (splitRegions.value.length === 0) {
    return null
  }
  return splitRegions.value[0].type
})

// 检查工具是否应该被禁用
const isToolDisabled = (toolType) => {
  const used = usedToolType.value
  if (!used) {
    return false // 没有使用任何工具时，所有工具都可用
  }
  return used !== toolType // 如果已使用的工具类型与当前工具不同，则禁用
}

// 计算预期的分割片段数量
const getExpectedSplitCount = () => {
  // 统计线条数量（横线和竖线）
  const lineCount = splitRegions.value.filter(region =>
    region.type === 'horizontal' || region.type === 'vertical'
  ).length

  // 统计矩形数量
  const rectangleCount = splitRegions.value.filter(region =>
    region.type === 'rectangle'
  ).length

  // n条线分割产生n+1个区域，矩形分割产生独立的区域
  return (lineCount > 0 ? lineCount + 1 : 0) + rectangleCount
}

// 获取工具提示文本
const getToolTooltip = (toolType) => {
  const used = usedToolType.value
  if (!used) {
    // 没有使用任何工具时的默认提示
    switch (toolType) {
      case 'horizontal': return '横向分割线'
      case 'vertical': return '竖向分割线'
      case 'rectangle': return '矩形选择'
      default: return ''
    }
  }

  if (used === toolType) {
    // 当前工具可用
    switch (toolType) {
      case 'horizontal': return '横向分割线 (当前工具)'
      case 'vertical': return '竖向分割线 (当前工具)'
      case 'rectangle': return '矩形选择 (当前工具)'
      default: return ''
    }
  } else {
    // 其他工具被禁用
    const usedToolName = used === 'horizontal' ? '横线' : used === 'vertical' ? '竖线' : '矩形'
    return `已使用${usedToolName}工具，请先清除所有分割线`
  }
}

// 获取图片分割监控目录
const getImageSplitWatchDir = async () => {
  try {
    // 从WebSocket客户端获取图片分割监控目录
    if (wsClient) {
      const response = await wsClient.sendRequest('imageSplit', 'getWatchDir', {})
      if (response && response.watchDir) {
        return response.watchDir
      }
    }
    return 'C:\\ww-wps-addon\\Temp\\ImageSplit' // 默认图片分割监控目录
  } catch (error) {
    console.warn('获取图片分割监控目录失败，使用默认目录:', error)
    return 'C:\\ww-wps-addon\\Temp\\ImageSplit'
  }
}

// 打开分割弹窗
const openSplitModal = async () => {
  if (!hasSelectedImage.value || isProcessing.value) {
    return
  }

  try {
    isProcessing.value = true
    errorMessage.value = ''

    // 获取图片分割监控目录
    watchedDir.value = await getImageSplitWatchDir()

    // 提取并保存图片到监控目录
    const imageInfo = await saveSelectedImageToWatchDir()

    if (imageInfo) {
      // 保存图片信息并显示分割弹窗
      currentImageInfo.value = imageInfo
      showSplitModal.value = true

      // 重置分割状态
      splitRegions.value = []
      selectedRegion.value = -1
      currentTool.value = 'horizontal'

      // 等待弹窗渲染完成后开始加载图片
      await nextTick() // 等待Vue更新DOM
      setTimeout(async () => {
        try {
          console.log('开始等待图片上传到OSS...')
          isLoadingImage.value = true

          // 先显示loading状态的演示图片
          await loadDemoImageToCanvas(imageInfo.width, imageInfo.height)
          console.log('Loading演示图片显示成功，等待OSS上传完成...')

          // 等待WebSocket事件通知OSS上传成功
          // 实际的图片加载将在WebSocket事件处理器中完成

        } catch (loadError) {
          console.error('加载Loading图片失败:', loadError)
          errorMessage.value = '加载图片失败: ' + loadError.message
          isLoadingImage.value = false
        }
      }, 300) // 等待300ms确保弹窗完全渲染
    }

  } catch (error) {
    console.error('打开分割弹窗失败:', error)
    errorMessage.value = '打开分割工具失败: ' + error.message
  } finally {
    isProcessing.value = false
  }
}

// 保存选中图片到监控目录
const saveSelectedImageToWatchDir = async () => {
  try {
    const selection = window.Application.Selection
    const range = selection.Range

    if (!range || !range.InlineShapes || range.InlineShapes.Count === 0) {
      throw new Error('未找到选中的图片')
    }

    // 获取第一张图片
    const shape = range.InlineShapes.Item(1)

    if (!shape || (shape.Type !== 3 && shape.Type !== window.Application?.Enum?.wdInlineShapePicture)) {
      throw new Error('选中的不是图片')
    }

    // 生成唯一文件名
    const timestamp = Date.now()
    const fileName = `image_split_${timestamp}.png`
    const fullPath = `${watchedDir.value}\\${fileName}`

    // 确保监控目录存在
    const fileSystem = window.Application.FileSystem
    // 保存图片到监控目录
    shape.SaveAsPicture(fullPath)

    // 验证文件是否保存成功
    if (!fileSystem.Exists(fullPath)) {
      throw new Error('图片保存失败')
    }

    console.log('图片已保存到:', fullPath)

    // 通知WebSocket服务器关联文件与客户端
    try {
      if (wsClient) {
        console.log('[DEBUG] 准备关联文件:', fileName, '客户端ID:', wsClient.getClientId())
        const result = await wsClient.sendRequest('imageSplit', 'associateFile', {
          fileName: fileName
        })
        console.log('[DEBUG] 关联文件响应:', result)
        console.log('已通知服务器关联图片文件:', fileName)
      }
    } catch (wsError) {
      console.warn('通知服务器关联文件失败:', wsError)
    }

    // 返回包含图片信息的对象，等待服务端上传通知
    return {
      path: fullPath,
      fileName: fileName,
      width: shape.Width || 400,
      height: shape.Height || 300,
      needsDemo: true // 初始状态，等待服务端上传成功通知
    }

  } catch (error) {
    console.error('保存图片到监控目录失败:', error)
    throw error
  }
}

// 加载演示图片到Canvas
const loadDemoImageToCanvas = async (width, height) => {
  return new Promise((resolve, reject) => {
    try {
      setTimeout(() => {
        let canvas = imageCanvas.value

        // 如果ref没有找到，尝试直接查询DOM
        if (!canvas) {
          console.log('通过ref未找到Canvas，尝试直接查询DOM...')
          canvas = document.querySelector('.image-canvas')
        }

        if (!canvas) {
          reject(new Error('Canvas元素未找到'))
          return
        }

        console.log('找到Canvas元素，开始创建演示图片')

        const ctx = canvas.getContext('2d')

        // 设置Canvas尺寸
        const maxWidth = 800
        const maxHeight = 600

        let displayWidth = width
        let displayHeight = height

        // 按比例缩放
        if (displayWidth > maxWidth) {
          displayHeight = (displayHeight * maxWidth) / displayWidth
          displayWidth = maxWidth
        }

        if (displayHeight > maxHeight) {
          displayWidth = (displayWidth * maxHeight) / displayHeight
          displayHeight = maxHeight
        }

        canvas.width = displayWidth
        canvas.height = displayHeight

        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, displayWidth, displayHeight)
        gradient.addColorStop(0, '#4CAF50')
        gradient.addColorStop(0.5, '#2196F3')
        gradient.addColorStop(1, '#FF9800')

        ctx.fillStyle = gradient
        ctx.fillRect(0, 0, displayWidth, displayHeight)

        // 添加网格线
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
        ctx.lineWidth = 1
        const gridSize = 50

        for (let x = 0; x <= displayWidth; x += gridSize) {
          ctx.beginPath()
          ctx.moveTo(x, 0)
          ctx.lineTo(x, displayHeight)
          ctx.stroke()
        }

        for (let y = 0; y <= displayHeight; y += gridSize) {
          ctx.beginPath()
          ctx.moveTo(0, y)
          ctx.lineTo(displayWidth, y)
          ctx.stroke()
        }

        // 添加文字
        ctx.fillStyle = 'white'
        ctx.font = 'bold 24px Arial'
        ctx.textAlign = 'center'
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
        ctx.shadowBlur = 4
        ctx.fillText('演示图片', displayWidth / 2, displayHeight / 2 - 30)

        ctx.font = '16px Arial'
        ctx.fillText('(用于分割工具演示)', displayWidth / 2, displayHeight / 2)
        ctx.fillText(`原始尺寸: ${width} × ${height}`, displayWidth / 2, displayHeight / 2 + 25)
        ctx.fillText(`显示尺寸: ${Math.round(displayWidth)} × ${Math.round(displayHeight)}`, displayWidth / 2, displayHeight / 2 + 50)

        // 保存图片引用
        currentImage.value = {
          element: null, // 演示图片没有img元素
          originalPath: null,
          displayWidth: displayWidth,
          displayHeight: displayHeight,
          originalWidth: width,
          originalHeight: height,
          isDemo: true
        }

        console.log('演示图片创建成功:', {
          originalSize: `${width}×${height}`,
          displaySize: `${Math.round(displayWidth)}×${Math.round(displayHeight)}`
        })

        resolve()
      }, 100)

    } catch (error) {
      reject(error)
    }
  })
}

// 加载OSS图片到Canvas
const loadImageFromOSS = async (ossUrl, originalWidth, originalHeight) => {
  return new Promise((resolve, reject) => {
    try {
      setTimeout(() => {
        let canvas = imageCanvas.value

        // 如果ref没有找到，尝试直接查询DOM
        if (!canvas) {
          console.log('通过ref未找到Canvas，尝试直接查询DOM...')
          canvas = document.querySelector('.image-canvas')
        }

        if (!canvas) {
          reject(new Error('Canvas元素未找到'))
          return
        }

        console.log('找到Canvas元素，开始加载OSS图片:', ossUrl)

        const ctx = canvas.getContext('2d')
        const img = new Image()

        // 设置跨域属性以支持OSS图片
        img.crossOrigin = 'anonymous'

        img.onload = () => {
          try {
            // 设置Canvas尺寸
            const maxWidth = 800
            const maxHeight = 600

            let { width, height } = img

            // 按比例缩放
            if (width > maxWidth) {
              height = (height * maxWidth) / width
              width = maxWidth
            }

            if (height > maxHeight) {
              width = (width * maxHeight) / height
              height = maxHeight
            }

            canvas.width = width
            canvas.height = height

            // 绘制图片
            ctx.clearRect(0, 0, width, height)
            ctx.drawImage(img, 0, 0, width, height)

            // 保存图片引用
            currentImage.value = {
              element: img,
              originalPath: ossUrl,
              displayWidth: width,
              displayHeight: height,
              originalWidth: originalWidth || img.width,
              originalHeight: originalHeight || img.height,
              isDemo: false, // 标记为真实图片
              ossUrl: ossUrl
            }

            console.log('OSS图片加载成功:', {
              originalSize: `${originalWidth || img.width}×${originalHeight || img.height}`,
              displaySize: `${width}×${height}`,
              ossUrl: ossUrl
            })

            resolve()
          } catch (canvasError) {
            reject(new Error('Canvas操作失败: ' + canvasError.message))
          }
        }

        img.onerror = () => {
          reject(new Error('无法加载OSS图片: ' + ossUrl))
        }

        // 直接使用OSS URL加载图片
        img.src = ossUrl

      }, 100) // 等待100ms确保DOM更新

    } catch (error) {
      reject(error)
    }
  })
}







// 关闭分割弹窗
const closeSplitModal = () => {
  showSplitModal.value = false
  splitRegions.value = []
  selectedRegion.value = -1
  currentImage.value = null
}

// 设置当前工具
const setTool = (tool) => {
  // 检查工具是否被禁用
  if (isToolDisabled(tool)) {
    console.warn(`工具 ${tool} 被禁用，当前已使用工具: ${usedToolType.value}`)
    return
  }
  currentTool.value = tool
}

// Canvas鼠标事件处理
const onCanvasMouseDown = (event) => {
  if (!currentImage.value) return

  const canvas = imageCanvas.value
  const rect = canvas.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  isDrawing.value = true
  startPoint.value = { x, y }

  if (currentTool.value === 'horizontal') {
    // 横向分割线：立即添加
    addHorizontalSplit(y)
    isDrawing.value = false
  } else if (currentTool.value === 'vertical') {
    // 竖向分割线：立即添加
    addVerticalSplit(x)
    isDrawing.value = false
  }
  // 矩形工具需要在mouseup时处理
}

const onCanvasMouseMove = (event) => {
  if (!isDrawing.value || currentTool.value !== 'rectangle') return

  const canvas = imageCanvas.value
  const rect = canvas.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 绘制临时矩形
  redrawCanvas()
  drawTemporaryRectangle(startPoint.value.x, startPoint.value.y, x, y)
}

const onCanvasMouseUp = (event) => {
  if (!isDrawing.value) return

  if (currentTool.value === 'rectangle') {
    const canvas = imageCanvas.value
    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 添加矩形分割区域
    addRectangleSplit(startPoint.value.x, startPoint.value.y, x, y)
  }

  isDrawing.value = false
}

const onCanvasMouseLeave = () => {
  isDrawing.value = false
  redrawCanvas()
}

// 添加横向分割线
const addHorizontalSplit = (y) => {
  const region = {
    type: 'horizontal',
    y: y,
    order: splitRegions.value.length + 1
  }
  splitRegions.value.push(region)
  redrawCanvas()
}

// 添加竖向分割线
const addVerticalSplit = (x) => {
  const region = {
    type: 'vertical',
    x: x,
    order: splitRegions.value.length + 1
  }
  splitRegions.value.push(region)
  redrawCanvas()
}

// 添加矩形分割区域
const addRectangleSplit = (x1, y1, x2, y2) => {
  const left = Math.min(x1, x2)
  const top = Math.min(y1, y2)
  const width = Math.abs(x2 - x1)
  const height = Math.abs(y2 - y1)

  // 最小尺寸检查
  if (width < 20 || height < 20) {
    return
  }

  const region = {
    type: 'rectangle',
    x: left,
    y: top,
    width: width,
    height: height,
    order: splitRegions.value.length + 1
  }
  splitRegions.value.push(region)
  redrawCanvas()
}

// 重绘Canvas
const redrawCanvas = () => {
  if (!currentImage.value || !imageCanvas.value) return

  const canvas = imageCanvas.value
  const ctx = canvas.getContext('2d')

  // 清除Canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 重绘背景图片
  if (currentImage.value.isDemo) {
    // 如果是演示图片，重新绘制演示背景
    redrawDemoBackground(ctx, currentImage.value.displayWidth, currentImage.value.displayHeight)
  } else if (currentImage.value.element) {
    // 如果有真实图片元素，绘制图片
    ctx.drawImage(
      currentImage.value.element,
      0, 0,
      currentImage.value.displayWidth,
      currentImage.value.displayHeight
    )
  }

  // 绘制所有分割线和矩形
  splitRegions.value.forEach((region) => {
    drawSplitRegion(ctx, region)
  })
}

// 重绘演示背景
const redrawDemoBackground = (ctx, width, height) => {
  // 创建渐变背景
  const gradient = ctx.createLinearGradient(0, 0, width, height)
  gradient.addColorStop(0, '#4CAF50')
  gradient.addColorStop(0.5, '#2196F3')
  gradient.addColorStop(1, '#FF9800')

  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)

  // 添加网格线
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
  ctx.lineWidth = 1
  const gridSize = 50

  for (let x = 0; x <= width; x += gridSize) {
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, height)
    ctx.stroke()
  }

  for (let y = 0; y <= height; y += gridSize) {
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }

  // 添加文字
  ctx.fillStyle = 'white'
  ctx.font = 'bold 24px Arial'
  ctx.textAlign = 'center'
  ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
  ctx.shadowBlur = 4
  ctx.fillText('演示图片', width / 2, height / 2 - 30)

  ctx.font = '16px Arial'
  ctx.fillText('(用于分割工具演示)', width / 2, height / 2)
  ctx.fillText(`原始尺寸: ${currentImage.value.originalWidth} × ${currentImage.value.originalHeight}`, width / 2, height / 2 + 25)
  ctx.fillText(`显示尺寸: ${Math.round(width)} × ${Math.round(height)}`, width / 2, height / 2 + 50)
}

// 绘制分割区域
const drawSplitRegion = (ctx, region) => {
  ctx.save()

  if (region.type === 'horizontal') {
    // 绘制横向分割线
    ctx.strokeStyle = '#ff4444'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    ctx.beginPath()
    ctx.moveTo(0, region.y)
    ctx.lineTo(currentImage.value.displayWidth, region.y)
    ctx.stroke()

    // 绘制序号 - 更大更清楚的圆形标识
    ctx.fillStyle = '#ff4444'
    ctx.beginPath()
    ctx.arc(30, region.y, 15, 0, 2 * Math.PI)
    ctx.fill()

    ctx.fillStyle = 'white'
    ctx.font = 'bold 14px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(region.order.toString(), 30, region.y)

  } else if (region.type === 'vertical') {
    // 绘制竖向分割线
    ctx.strokeStyle = '#4444ff'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    ctx.beginPath()
    ctx.moveTo(region.x, 0)
    ctx.lineTo(region.x, currentImage.value.displayHeight)
    ctx.stroke()

    // 绘制序号 - 更大更清楚的圆形标识
    ctx.fillStyle = '#4444ff'
    ctx.beginPath()
    ctx.arc(region.x, 30, 15, 0, 2 * Math.PI)
    ctx.fill()

    ctx.fillStyle = 'white'
    ctx.font = 'bold 14px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(region.order.toString(), region.x, 30)

  } else if (region.type === 'rectangle') {
    // 绘制矩形选择区域
    ctx.strokeStyle = '#44ff44'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    ctx.strokeRect(region.x, region.y, region.width, region.height)

    // 绘制序号 - 更大更清楚的圆形标识
    ctx.fillStyle = '#44ff44'
    const centerX = region.x + region.width / 2
    const centerY = region.y + region.height / 2
    ctx.beginPath()
    ctx.arc(centerX, centerY, 18, 0, 2 * Math.PI)
    ctx.fill()

    ctx.fillStyle = 'white'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(region.order.toString(), centerX, centerY)
  }

  ctx.restore()
}

// 绘制临时矩形
const drawTemporaryRectangle = (x1, y1, x2, y2) => {
  const canvas = imageCanvas.value
  const ctx = canvas.getContext('2d')

  ctx.save()
  ctx.strokeStyle = '#888888'
  ctx.lineWidth = 1
  ctx.setLineDash([3, 3])

  const left = Math.min(x1, x2)
  const top = Math.min(y1, y2)
  const width = Math.abs(x2 - x1)
  const height = Math.abs(y2 - y1)

  ctx.strokeRect(left, top, width, height)
  ctx.restore()
}



// 清除所有分割
const clearSplits = () => {
  splitRegions.value = []
  selectedRegion.value = -1
  // 清除后重置为默认工具，所有工具重新可用
  currentTool.value = 'horizontal'
  redrawCanvas()
}

// 撤销最后一个分割
const undoLastSplit = () => {
  if (splitRegions.value.length > 0) {
    splitRegions.value.pop()
    selectedRegion.value = -1
    redrawCanvas()
  }
}

// 确认分割
const confirmSplit = async () => {
  if (splitRegions.value.length === 0 || isProcessing.value) {
    return
  }

  try {
    isProcessing.value = true
    errorMessage.value = ''

    // 使用OSS图片处理参数生成分割后的图片URL
    const splitImages = await generateSplitImagesWithOSS()

    if (splitImages.length > 0) {
      // 按顺序插入到文档
      await insertSplitImagesToDocument(splitImages)

      successMessage.value = `成功分割并插入 ${splitImages.length} 个图片片段！`

      // 关闭弹窗
      closeSplitModal()

      // 3秒后清除成功消息
      setTimeout(() => {
        successMessage.value = ''
      }, 3000)
    }

  } catch (error) {
    console.error('分割确认失败:', error)
    errorMessage.value = '分割失败: ' + error.message
  } finally {
    isProcessing.value = false
  }
}

// 使用OSS图片处理参数生成分割后的图片URL
const generateSplitImagesWithOSS = async () => {
  if (!currentImage.value) {
    throw new Error('没有加载的图片')
  }

  // 检查是否有OSS URL
  if (!currentImage.value.ossUrl) {
    if (currentImage.value.isDemo) {
      throw new Error('演示图片无法进行真实分割，请等待真实图片加载完成后再试')
    } else {
      throw new Error('没有OSS图片URL，无法进行服务端分割')
    }
  }

  if (splitRegions.value.length === 0) {
    throw new Error('没有分割区域')
  }

  const splitImages = []

  // 计算缩放比例（Canvas显示坐标转换为原图坐标）
  // 使用真实的原图尺寸和Canvas实际显示尺寸
  const scaleX = currentImage.value.originalWidth / currentImage.value.displayWidth
  const scaleY = currentImage.value.originalHeight / currentImage.value.displayHeight

  console.log('开始生成OSS分割URL，缩放比例:', { scaleX, scaleY })
  console.log('原图尺寸:', currentImage.value.originalWidth, 'x', currentImage.value.originalHeight)
  console.log('显示尺寸:', currentImage.value.displayWidth, 'x', currentImage.value.displayHeight)

  // 获取分割类型（每次只使用一种分割方式）
  const splitType = splitRegions.value[0].type

  // 辅助函数：构造完整的OSS裁剪URL
  const buildOSSCropUrl = (x, y, width, height) => {
    const cropParams = `image/crop,x_${Math.round(x)},y_${Math.round(y)},w_${Math.round(width)},h_${Math.round(height)}`
    const baseUrl = currentImage.value.ossUrl.split('?')[0] // 移除已有的查询参数
    return `${baseUrl}?x-oss-process=${cropParams}`
  }

  // 辅助函数：构造横线分割的OSS URL（只需要y和h参数）
  const buildHorizontalCropUrl = (y, height) => {
    const cropParams = `image/crop,y_${Math.round(y)},h_${Math.round(height)}`
    const baseUrl = currentImage.value.ossUrl.split('?')[0]
    return `${baseUrl}?x-oss-process=${cropParams}`
  }

  // 辅助函数：构造竖线分割的OSS URL（只需要x和w参数）
  const buildVerticalCropUrl = (x, width) => {
    const cropParams = `image/crop,x_${Math.round(x)},w_${Math.round(width)}`
    const baseUrl = currentImage.value.ossUrl.split('?')[0]
    return `${baseUrl}?x-oss-process=${cropParams}`
  }

  let order = 1

  // 根据分割类型处理
  if (splitType === 'rectangle') {
    // 矩形分割：每个矩形都是独立的分割区域
    for (const region of splitRegions.value) {
      const sourceX = region.x * scaleX
      const sourceY = region.y * scaleY
      const sourceWidth = region.width * scaleX
      const sourceHeight = region.height * scaleY

      const ossUrl = buildOSSCropUrl(sourceX, sourceY, sourceWidth, sourceHeight)

      console.log('矩形分割OSS URL:', ossUrl)

      splitImages.push({
        ossUrl: ossUrl,
        order: order++,
        type: 'rectangle',
        region: region,
        cropParams: ossUrl.split('?x-oss-process=')[1]
      })
    }

  } else if (splitType === 'horizontal') {
    // 横线分割：按Y坐标分割成水平条带
    const yLines = splitRegions.value.map(r => Math.round(r.y * scaleY)).sort((a, b) => a - b)
    const yPoints = [0, ...yLines, Math.round(currentImage.value.originalHeight)]

    console.log('横线分割Y坐标点:', yPoints)

    for (let i = 0; i < yPoints.length - 1; i++) {
      const y = yPoints[i]
      const height = yPoints[i + 1] - y

      if (height > 0) {
        const ossUrl = buildHorizontalCropUrl(y, height)

        console.log(`横线分割区域${order} (y=${y}, h=${height}) OSS URL:`, ossUrl)

        splitImages.push({
          ossUrl: ossUrl,
          order: order++,
          type: 'horizontal_strip',
          cropParams: ossUrl.split('?x-oss-process=')[1]
        })
      }
    }

  } else if (splitType === 'vertical') {
    // 竖线分割：按X坐标分割成垂直条带
    const xLines = splitRegions.value.map(r => Math.round(r.x * scaleX)).sort((a, b) => a - b)
    const xPoints = [0, ...xLines, Math.round(currentImage.value.originalWidth)]

    console.log('竖线分割X坐标点:', xPoints)

    for (let i = 0; i < xPoints.length - 1; i++) {
      const x = xPoints[i]
      const width = xPoints[i + 1] - x

      if (width > 0) {
        const ossUrl = buildVerticalCropUrl(x, width)

        console.log(`竖线分割区域${order} (x=${x}, w=${width}) OSS URL:`, ossUrl)

        splitImages.push({
          ossUrl: ossUrl,
          order: order++,
          type: 'vertical_strip',
          cropParams: ossUrl.split('?x-oss-process=')[1]
        })
      }
    }
  }

  // 按顺序排序
  splitImages.sort((a, b) => a.order - b.order)

  return splitImages
}

// 通过服务端下载OSS分割图片到缓存目录
const downloadSplitImageToCache = async (ossUrl, fileName) => {
  try {
    if (!wsClient) {
      throw new Error('WebSocket客户端不可用')
    }

    console.log('请求服务端下载分割图片:', ossUrl, fileName)

    const downloadResult = await wsClient.sendRequest('imageSplit', 'downloadSplitImage', {
      ossUrl: ossUrl,
      fileName: fileName
    })

    if (downloadResult && downloadResult.success) {
      console.log('分割图片下载到缓存成功:', downloadResult.localPath)
      return downloadResult.localPath
    } else {
      throw new Error(`下载失败: ${downloadResult.error || '未知错误'}`)
    }

  } catch (error) {
    console.error('下载分割图片失败:', error)
    throw error
  }
}

// 将分割后的图片按顺序插入到文档
const insertSplitImagesToDocument = async (splitImages) => {
  try {
    const selection = window.Application.Selection
    const document = window.Application.ActiveDocument

    if (!selection || !document) {
      throw new Error('无法访问文档或选择区域')
    }

    // 获取当前选中的图片范围
    const originalRange = selection.Range

    // 找到选中图片的位置，在其下方插入分割结果
    let insertPosition = originalRange.End

    // 添加标题
    const titleRange = document.Range(insertPosition, insertPosition)
    insertPosition = titleRange.End

    // 按顺序插入每个分割后的图片
    for (let i = 0; i < splitImages.length; i++) {
      const splitImage = splitImages[i]

      try {
        // 生成唯一的文件名
        const fileName = `split_${splitImage.type}_${splitImage.order}_${Date.now()}.jpg`

        // 先下载OSS图片到缓存目录
        console.log('下载分割图片到缓存:', splitImage.ossUrl)
        const localPath = await downloadSplitImageToCache(splitImage.ossUrl, fileName)

        // 插入图片标题
        const labelRange = document.Range(insertPosition, insertPosition)
        labelRange.Text = `片段${i + 1}: `
        insertPosition = labelRange.End

        // 使用本地路径插入图片
        const imageRange = document.Range(insertPosition, insertPosition)
        const inlineShape = imageRange.InlineShapes.AddPicture(localPath)

        // 设置图片尺寸（可选）
        if (inlineShape) {
          const maxWidth = 300
          const maxHeight = 300

          if (inlineShape.Width > maxWidth) {
            const ratio = maxWidth / inlineShape.Width
            inlineShape.Width = maxWidth
            inlineShape.Height = inlineShape.Height * ratio
          }

          if (inlineShape.Height > maxHeight) {
            const ratio = maxHeight / inlineShape.Height
            inlineShape.Height = maxHeight
            inlineShape.Width = inlineShape.Width * ratio
          }
        }

        insertPosition += 1 // 为插入的图片预留位置

        // 换行
        const lineBreakRange = document.Range(insertPosition, insertPosition)
        lineBreakRange.Text = '\n'
        insertPosition = lineBreakRange.End

      } catch (error) {
        console.error(`插入第${i + 1}个分割图片失败:`, error)

        // 如果插入图片失败，插入文本占位符
        const errorRange = document.Range(insertPosition, insertPosition)
        errorRange.Text = `[分割图片${i + 1}插入失败: ${splitImage.ossUrl}]\n`
        insertPosition = errorRange.End
      }
    }

    // 所有分割图片插入完成后，删除原始选中的图片
    try {
      console.log('删除原始选中的图片')
      // 重新获取选择，因为插入操作可能改变了选择状态
      const currentSelection = window.Application.Selection
      if (currentSelection && currentSelection.Range && currentSelection.Range.InlineShapes && currentSelection.Range.InlineShapes.Count > 0) {
        // 删除选中范围内的所有图片
        for (let i = currentSelection.Range.InlineShapes.Count; i >= 1; i--) {
          const shape = currentSelection.Range.InlineShapes.Item(i)
          if (shape && shape.Type === 3) { // 图片类型
            shape.Delete()
            console.log(`已删除原图片 ${i}`)
          }
        }
      }
    } catch (deleteError) {
      console.warn('删除原图片失败:', deleteError)
      // 删除失败不影响整体流程，只记录警告
    }

  } catch (error) {
    console.error('插入分割图片到文档失败:', error)
    throw error
  }
}

// 检查当前选择是否包含图片
const checkImageSelection = () => {
  try {
    if (!window.Application || !window.Application.Selection) {
      hasSelectedImage.value = false
      selectionType.value = '无选择'
      imageCount.value = 0
      selectionText.value = ''
      return
    }

    const selection = window.Application.Selection
    const range = selection.Range

    if (!range) {
      hasSelectedImage.value = false
      selectionType.value = '无选择'
      imageCount.value = 0
      selectionText.value = ''
      return
    }

    // 获取选择的文本
    selectionText.value = selection.Text || ''

    // 检查是否有内联形状（图片）
    let inlineShapeCount = 0
    try {
      if (range.InlineShapes && range.InlineShapes.Count) {
        inlineShapeCount = range.InlineShapes.Count

        // 检查是否有图片类型的形状
        let imageShapeCount = 0
        for (let i = 1; i <= inlineShapeCount; i++) {
          const shape = range.InlineShapes.Item(i)
          // 检查形状类型是否为图片 (wdInlineShapePicture = 3)
          if (shape && shape.Type === 3) {
            imageShapeCount++
          }
        }
        imageCount.value = imageShapeCount
        hasSelectedImage.value = imageShapeCount > 0
      } else {
        imageCount.value = 0
        hasSelectedImage.value = false
      }
    } catch (e) {
      console.warn('检查内联形状时出错:', e)
      imageCount.value = 0
      hasSelectedImage.value = false
    }

    // 更新选择类型
    if (hasSelectedImage.value) {
      selectionType.value = `图片选择 (${imageCount.value}张)`
    } else if (selectionText.value.trim()) {
      selectionType.value = '文本选择'
    } else {
      selectionType.value = '空选择'
    }

  } catch (error) {
    console.error('检查图片选择时出错:', error)
    hasSelectedImage.value = false
    selectionType.value = '检查出错'
    imageCount.value = 0
    selectionText.value = ''
  }
}

// 旧的图片提取代码已删除，现在直接在saveSelectedImageToWatchDir中处理

// 旧的Base64读取代码已删除

// 旧的插入方法（已被新的insertSplitImagesToDocument替代）
// const insertSplitImages = async (splitResults) => {
//   // 这个方法已经被新的手动分割功能替代
// }

// 旧的插入方法已删除，使用新的insertSplitImagesToDocument方法

// 旧的单张图片插入方法已删除

// 旧的Base64文件保存方法已删除

// 旧的自动分割算法已删除，现在使用手动分割工具

// 旧的自动分割和预览代码已删除

// 设置WebSocket事件监听器
const setupWebSocketListeners = () => {
  if (!wsClient) {
    console.warn('WebSocket客户端不可用')
    return
  }

  // 监听imageSplit专用事件类型
  console.log('设置imageSplit事件监听器, wsClient:', wsClient)
  wsClient.addEventListener('imageSplit', (message) => {
    const { eventType, data } = message

    // 处理图片分割上传开始事件
    if (eventType === 'imageSplitUploadStart') {
      console.log('图片开始上传:', data.file)
      // 可以在这里显示上传进度
    }

    // 处理图片分割上传成功事件
    else if (eventType === 'imageSplitUploadSuccess') {
      handleImageUploadSuccess(data)
    }

    // 处理图片分割上传失败事件
    else if (eventType === 'imageSplitUploadError') {
      handleImageUploadError(data)
    }

    // 处理图片分割服务错误事件
    else if (eventType === 'imageSplitError') {
      handleImageSplitError(data)
    }
  })
}

// 处理图片上传成功事件
const handleImageUploadSuccess = async (data) => {
  console.log('图片上传成功:', data.file, data.ossUrl)

  // 检查是否是当前等待的原图文件
  if (isLoadingImage.value && currentImageInfo.value && data.file === currentImageInfo.value.fileName) {
    try {
      console.log('开始加载真实OSS图片到Canvas:', data.ossUrl)
      await loadImageFromOSS(data.ossUrl, currentImageInfo.value.width, currentImageInfo.value.height)
      console.log('真实OSS图片加载到Canvas成功')
      isLoadingImage.value = false
      successMessage.value = '图片加载成功！'
      setTimeout(() => {
        successMessage.value = ''
      }, 2000)
    } catch (loadError) {
      console.error('加载OSS图片到Canvas失败:', loadError)
      // 保持演示图片，但停止loading状态
      isLoadingImage.value = false
      errorMessage.value = '加载真实图片失败，使用演示图片'
      setTimeout(() => {
        errorMessage.value = ''
      }, 3000)
    }
  } else {
    // 其他分割图片上传成功的通知
    successMessage.value = `图片 ${data.file} 上传到OSS成功！`
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)
  }
}

// 处理图片上传失败事件
const handleImageUploadError = (data) => {
  console.error('图片上传失败:', data.file, data.error)

  // 检查是否是当前等待的原图文件
  if (isLoadingImage.value && currentImageInfo.value && data.file === currentImageInfo.value.fileName) {
    console.log('原图上传失败，保持使用演示图片')
    isLoadingImage.value = false
    errorMessage.value = '图片上传失败，使用演示图片进行分割'
    setTimeout(() => {
      errorMessage.value = ''
    }, 3000)
  } else {
    // 其他分割图片上传失败的通知
    errorMessage.value = `图片 ${data.file} 上传失败: ${data.error}`
    setTimeout(() => {
      errorMessage.value = ''
    }, 5000)
  }
}

// 处理图片分割服务错误事件
const handleImageSplitError = (data) => {
  console.error('图片分割服务错误:', data.error, data.message)
  errorMessage.value = `图片分割服务错误: ${data.message}`
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

// 组件挂载时开始检查选择状态
onMounted(() => {
  checkImageSelection()
  // 每500ms检查一次选择状态
  selectionCheckTimer = setInterval(checkImageSelection, 500)

  // 设置WebSocket事件监听器
  setupWebSocketListeners()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (selectionCheckTimer) {
    clearInterval(selectionCheckTimer)
  }
})
</script>

<style scoped>
.image-split-container {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  height: 100vh;
  overflow-y: auto;
}

.header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 15px;
}

.header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.status-indicator {
  padding: 8px 12px;
  border-radius: 4px;
  display: inline-block;
}

.status-indicator.has-image {
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
}

.status-indicator:not(.has-image) {
  background-color: #fff3cd;
  border: 1px solid #ffc107;
}

.status-text.success {
  color: #2e7d32;
  font-weight: 500;
}

.status-text.warning {
  color: #f57c00;
  font-weight: 500;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.instruction-section,
.selection-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.instruction-section h3,
.selection-info h3 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 16px;
}

.instruction-section ol {
  margin: 0;
  padding-left: 20px;
}

.instruction-section li {
  margin-bottom: 5px;
  color: #6c757d;
}

.selection-details p {
  margin: 5px 0;
  color: #495057;
}

.action-section {
  text-align: center;
  margin: 20px 0;
}

.split-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.split-btn:hover:not(.disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.split-btn.disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin-top: 10px;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #c3e6cb;
  margin-top: 10px;
}

/* 分割选项样式 */
.split-options {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.split-options h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
}

.option-group {
  margin-bottom: 15px;
}

.option-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.radio-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.radio-item input[type="radio"] {
  margin: 0;
}

.grid-params,
.smart-params,
.custom-params {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.param-item label {
  min-width: 60px;
  font-size: 14px;
  color: #495057;
}

.param-item input[type="number"] {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.param-item input[type="range"] {
  flex: 1;
  max-width: 150px;
}

.param-value {
  min-width: 40px;
  font-size: 14px;
  color: #6c757d;
}

.param-info {
  margin-top: 8px;
  padding: 8px;
  background: #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  color: #6c757d;
  font-style: italic;
}

/* 预览区域样式 */
.preview-section {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-top: 20px;
}

.preview-section h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
}

.preview-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.preview-grid.grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  min-width: 120px;
}

.preview-image {
  max-width: 100px;
  max-height: 100px;
  object-fit: contain;
  border: 1px solid #e9ecef;
  border-radius: 2px;
}

.preview-info {
  margin-top: 5px;
  text-align: center;
  font-size: 12px;
  color: #6c757d;
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #545b62;
}

/* 顶部操作区域样式 */
.top-action-section {
  text-align: center;
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.start-split-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.start-split-btn:hover:not(.disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.start-split-btn.disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 分割弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.split-modal {
  background: white;
  border-radius: 8px;
  width: 90vw;
  max-width: 1200px;
  height: 90vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #495057;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.tool-group {
  display: flex;
  gap: 8px;
}

.tool-btn {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 60px;
  height: 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.tool-icon {
  font-size: 14px;
  line-height: 1;
}

.tool-text {
  font-size: 11px;
  line-height: 1;
  white-space: nowrap;
}

.tool-btn:hover {
  background: #e9ecef;
}

.tool-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.tool-btn:disabled,
.tool-btn.disabled {
  background: #f8f9fa;
  color: #6c757d;
  border-color: #e9ecef;
  cursor: not-allowed;
  opacity: 0.6;
}

.tool-btn:disabled:hover,
.tool-btn.disabled:hover {
  background: #f8f9fa;
  color: #6c757d;
  transform: none;
}

.tool-actions {
  display: flex;
  gap: 10px;
}

.clear-btn,
.undo-btn {
  padding: 6px 12px;
  border: 1px solid #dc3545;
  background: white;
  color: #dc3545;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
  height: 36px;
}

.clear-btn:hover,
.undo-btn:hover {
  background: #dc3545;
  color: white;
}

/* 图片编辑区域样式 */
.image-editor {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  overflow: auto;
  position: relative;
  /* 为loading覆盖层提供定位基准 */
}

.image-canvas {
  border: 2px solid #dee2e6;
  border-radius: 4px;
  cursor: crosshair;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}



/* 弹窗底部样式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.cancel-btn {
  padding: 10px 20px;
  border: 1px solid #6c757d;
  background: white;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #6c757d;
  color: white;
}

.confirm-btn {
  padding: 10px 20px;
  border: none;
  background: #28a745;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.confirm-btn:hover:not(:disabled) {
  background: #218838;
}

/* Loading 覆盖层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #007bff;
  font-size: 14px;
  font-weight: 500;
}

.confirm-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
</style>
