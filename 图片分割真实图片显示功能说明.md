# 图片分割真实图片显示功能说明

## 问题描述
之前图片分割工具在Canvas中显示的是演示图片，而不是用户选中的真实图片，导致用户无法准确预览分割效果。

## 解决方案
修改图片分割工具，使其在Canvas中显示真实的OSS图片，而不是演示图片。

## 实现细节

### 1. 修改 `saveSelectedImageToWatchDir` 函数

#### 原有逻辑
- 保存图片到监控目录
- 通知服务器关联文件
- 返回本地路径信息，标记 `needsDemo: true`

#### 新逻辑
- 保存图片到监控目录
- 通知服务器关联文件
- **立即触发OSS上传**
- 如果上传成功，返回OSS URL，标记 `needsDemo: false`
- 如果上传失败，返回本地路径，标记 `needsDemo: true`

```javascript
// 立即触发上传到OSS
const uploadResult = await wsClient.sendMessage('imageSplit', 'uploadFile', {
  fileName: fileName
})

if (uploadResult && uploadResult.success) {
  return {
    path: fullPath,
    fileName: fileName,
    width: shape.Width || 400,
    height: shape.Height || 300,
    ossUrl: uploadResult.ossUrl, // OSS链接
    needsDemo: false // 有真实OSS图片
  }
}
```

### 2. 新增 `loadImageFromOSS` 函数

#### 功能
- 从OSS URL加载真实图片到Canvas
- 处理跨域问题（设置 `crossOrigin = "anonymous"`）
- 按比例缩放图片适应Canvas尺寸
- 更新 `currentImage` 对象，标记为真实图片

#### 关键特性
```javascript
// 设置跨域属性以支持OSS图片
img.crossOrigin = 'anonymous'

// 保存图片引用
currentImage.value = {
  element: img,
  originalPath: ossUrl,
  displayWidth: width,
  displayHeight: height,
  originalWidth: originalWidth || img.width,
  originalHeight: originalHeight || img.height,
  isDemo: false, // 标记为真实图片
  ossUrl: ossUrl
}
```

### 3. 修改 `openSplitModal` 函数

#### 智能加载逻辑
1. **优先使用OSS图片**: 如果有OSS URL且上传成功
2. **备用演示图片**: 如果OSS不可用或上传失败
3. **双重备用**: 如果OSS图片加载失败，再尝试演示图片

```javascript
// 优先使用OSS图片，如果没有则使用演示图片
if (imageInfo.ossUrl && !imageInfo.needsDemo) {
  console.log('使用OSS图片:', imageInfo.ossUrl)
  await loadImageFromOSS(imageInfo.ossUrl, imageInfo.width, imageInfo.height)
} else {
  console.log('使用演示图片 (OSS不可用或上传失败)')
  await loadDemoImageToCanvas(imageInfo.width, imageInfo.height)
}
```

## 工作流程

### 1. 图片选择和上传阶段
```
用户选择图片 → 保存到监控目录 → 关联文件 → 立即上传OSS → 获取OSS URL
```

### 2. Canvas显示阶段
```
检查OSS URL → 加载OSS图片 → 显示真实图片
     ↓ (失败时)
   加载演示图片 → 显示备用图片
```

### 3. 分割处理阶段
```
检查图片类型 → 使用真实图片数据分割 → 生成分割图片 → 上传到OSS
```

## 技术优势

### 1. 真实预览
- 用户可以看到真实的图片内容
- 分割线位置更加准确
- 提供更好的用户体验

### 2. 跨域支持
- 正确设置 `crossOrigin` 属性
- 支持OSS图片的Canvas操作
- 避免跨域安全限制

### 3. 容错机制
- OSS上传失败时使用演示图片
- OSS图片加载失败时使用演示图片
- 确保功能始终可用

### 4. 性能优化
- 立即上传原图到OSS
- 避免重复上传
- 提供清晰的加载状态

## 兼容性

### 向后兼容
- 保留演示图片功能作为备用
- 现有的分割逻辑无需修改
- 支持 `isDemo` 标记的判断逻辑

### 错误处理
- OSS服务不可用时自动降级
- 网络问题时使用本地备用方案
- 提供详细的错误日志

## 测试建议

1. **正常流程测试**
   - 选择图片 → 检查Canvas显示真实图片
   - 进行分割 → 验证分割结果正确

2. **异常情况测试**
   - OSS服务不可用 → 验证演示图片显示
   - 网络中断 → 验证备用机制工作

3. **跨域测试**
   - 验证OSS图片正确加载
   - 验证Canvas操作无跨域错误

## 注意事项

1. **OSS配置**: 确保OSS bucket允许跨域访问
2. **网络依赖**: 功能依赖网络连接和OSS服务
3. **图片格式**: 支持常见的图片格式（PNG、JPG等）
4. **性能考虑**: 大图片可能需要更长的加载时间
