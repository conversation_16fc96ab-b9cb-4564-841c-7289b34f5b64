const fs = require('node:fs');
const path = require('node:path');
const Oss = require('ali-oss');
const chalk = require('chalk');

// OSS配置信息
const ossInfo = {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe',
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
    bucket: 'hexin-worksheet',
};

// 初始化OSS客户端
const ossClient = new Oss({
    region: ossInfo.region,
    accessKeyId: ossInfo.accessKeyId,
    accessKeySecret: ossInfo.accessKeySecret,
    bucket: ossInfo.bucket,
    timeout: '120s'
});

// 递归获取目录下所有文件
function getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath).filter(file => file !== 'win-unpacked');

    files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        if (fs.statSync(fullPath).isDirectory()) {
            getAllFiles(fullPath, arrayOfFiles);
        } else {
            arrayOfFiles.push(fullPath);
        }
    });

    return arrayOfFiles;
}

// 从package.json获取当前版本号
function getCurrentVersion() {
    const packageJsonPath = path.resolve(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return packageJson.version;
}

// 创建软连接指向最新版本
async function createSymlinksToLatest() {
    try {
        console.log(chalk.cyan('=== 创建软连接指向最新版本 ==='));
        
        // 使用项目根目录
        const projectRoot = path.resolve(__dirname, '..');
        const currentVersion = getCurrentVersion();
        
        console.log(chalk.blue(`当前版本: ${currentVersion}`));
        
        // 定义版本配置
        const versions = [
            {
                name: '万唯版本',
                distPath: path.join(projectRoot, 'dist'),
                ossPrefix: 'exe/wps-addon'
            },
            {
                name: '合心版本', 
                distPath: path.join(projectRoot, 'dist-hexin'),
                ossPrefix: 'exe/hexin-wps-addon'
            },
            {
                name: '万唯高中版本',
                distPath: path.join(projectRoot, 'dist-wwsenior'),
                ossPrefix: 'exe/wwsenior-wps-addon'
            }
        ];

        let totalSymlinks = 0;

        for (const version of versions) {
            if (!fs.existsSync(version.distPath)) {
                console.log(chalk.yellow(`跳过 ${version.name}: 构建产物不存在`));
                continue;
            }
            
            try {
                // 为每个版本创建软连接
                const sourcePrefix = `${version.ossPrefix}`;  // 源文件路径（不带版本号）
                
                console.log(chalk.blue(`\n${version.name}: 创建固定名称的 exe 软连接`));
                
                // 获取exe文件并创建固定名称的软连接
                const allFiles = getAllFiles(version.distPath);
                const exeFiles = allFiles.filter(file => path.extname(file).toLowerCase() === '.exe');
                let symlinkCount = 0;
                
                console.log(chalk.gray(`  找到 ${exeFiles.length} 个 exe 文件`));
                
                // 定义固定的exe文件名映射
                const fixedExeNames = {
                    '万唯版本': '万唯AI编辑WPS插件服务-Setup.exe',
                    '合心版本': '合心科技AI编辑WPS插件服务-Setup.exe',
                    '万唯高中版本': '万唯高中AI编辑WPS插件服务-Setup.exe'
                };
                
                const fixedExeName = fixedExeNames[version.name];
                if (!fixedExeName) {
                    console.log(chalk.yellow(`未找到 ${version.name} 对应的固定exe文件名`));
                    continue;
                }
                
                for (const filePath of exeFiles) {
                    const relativePath = path.relative(version.distPath, filePath);
                    const sourceOssKey = `${sourcePrefix}/${relativePath}`.replace(/\\/g, '/');
                    // 使用固定的文件名作为软连接
                    const fixedOssKey = `${sourcePrefix}/${fixedExeName}`.replace(/\\/g, '/');
                    
                    try {
                        // 检查源文件是否存在
                        try {
                            await ossClient.head(sourceOssKey);
                        } catch (headError) {
                            console.log(chalk.yellow(`源文件不存在，跳过: ${relativePath}`));
                            continue;
                        }
                        
                        // 复制源文件到固定名称（作为软连接的效果）
                        await ossClient.copy(fixedOssKey, sourceOssKey);
                        
                        symlinkCount++;
                        console.log(chalk.gray(`    ${relativePath} → ${fixedExeName}`));
                        
                    } catch (err) {
                        console.error(chalk.red(`\n创建软连接失败: ${relativePath} → ${fixedExeName}`), err.message);
                    }
                }
                
                console.log(chalk.green(`\n${version.name}: 成功创建 ${symlinkCount} 个 exe 软连接`));
                totalSymlinks += symlinkCount;
                
            } catch (error) {
                console.error(chalk.red(`${version.name} 软连接创建失败:`), error.message);
            }
        }
        
        console.log(chalk.green('\n=== exe 软连接创建完成! ==='));
        console.log(chalk.blue(`总计创建 exe 软连接: ${totalSymlinks} 个`));
        console.log(chalk.blue(`版本: ${currentVersion}`));
        
        // 显示固定名称的访问路径
        console.log(chalk.cyan('\n固定名称访问路径:'));
        const fixedExeNames = {
            '万唯版本': '万唯AI编辑WPS插件服务-Setup.exe',
            '合心版本': '合心科技AI编辑WPS插件服务-Setup.exe',
            '万唯高中版本': '万唯高中AI编辑WPS插件服务-Setup.exe'
        };
        
        for (const version of versions) {
            if (fs.existsSync(version.distPath)) {
                const fixedExeName = fixedExeNames[version.name];
                if (fixedExeName) {
                    console.log(chalk.white(`  ${version.name}: https://dl.hexinedu.com/${version.ossPrefix}/${fixedExeName}`));
                }
            }
        }
        
    } catch (error) {
        console.error(chalk.red('\nError during symlink creation:'), error);
        process.exit(1);
    }
}

// 如果直接运行这个脚本
if (require.main === module) {
    createSymlinksToLatest();
}

module.exports = { createSymlinksToLatest }; 