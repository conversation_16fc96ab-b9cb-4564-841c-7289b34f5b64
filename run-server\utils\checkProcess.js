const cp = require('child_process');

function isProcessRunning(processNamePattern) {
  try {
    let command;
    let output;

    if (process.platform === 'win32') {
      // Windows 平台
      command = 'tasklist /FO CSV';
      output = cp.execSync(command, { encoding: 'utf8' });
      // 创建正则表达式模式
      const regex = new RegExp(`${processNamePattern}`, 'i');
      return regex.test(output);

    } else if (process.platform === 'darwin') {
      // macOS 平台
      command = 'ps -ax';
      output = cp.execSync(command, { encoding: 'utf8' });

      // 创建正则表达式模式，排除 grep 本身
      const regex = new RegExp(`[^(grep)].*${processNamePattern}`, 'i');
      return regex.test(output);

    } else {
      // Linux 平台
      command = 'ps -A';
      output = cp.execSync(command, { encoding: 'utf8' });

      // 创建正则表达式模式，排除 grep 本身
      const regex = new RegExp(`[^(grep)].*${processNamePattern}`, 'i');
      return regex.test(output);
    }
  } catch (error) {
    console.error(`检测进程运行状态出错: ${error}`);
    return false;
  }
}

/**
 * 创建空 Word 文档
 * @param {string} filePath - 文件保存路径，包括文件名但不包括扩展名
 * @returns {Promise<string>} - 创建的文件路径
 */
function createEmptyWordDocument(filePath) {
  return new Promise((resolve, reject) => {
    const fs = require('fs');
    const path = require('path');

    // 确保目录存在
    const directory = path.dirname(filePath);
    if (!fs.existsSync(directory)) {
      try {
        fs.mkdirSync(directory, { recursive: true });
        console.log(`目录已创建: ${directory}`);
      } catch (err) {
        console.error(`创建目录失败: ${err.message}`);
        reject(`创建目录失败: ${err.message}`);
        return;
      }
    }

    const fullPath = filePath;
    try {
      const powershellCmd = `
      $word = New-Object -ComObject Word.Application
      $word.Visible = $false
      $doc = $word.Documents.Add()
      $doc.SaveAs("${filePath.replace(/\\/g, '\\\\')}")
      $doc.Close()
      $word.Quit()
    `;
      cp.execSync(`powershell -Command "${powershellCmd}"`, { windowsHide: true });
      resolve(true);
      console.log(`空白 Word 文档已创建: ${fullPath}`);
      resolve(fullPath);
    } catch (err) {
      console.error(`创建空白 Word 文档失败: ${err.message}`);
      reject(`创建空白 Word 文档失败: ${err.message}`);
    }
  });
}

module.exports = { isProcessRunning, createEmptyWordDocument }
