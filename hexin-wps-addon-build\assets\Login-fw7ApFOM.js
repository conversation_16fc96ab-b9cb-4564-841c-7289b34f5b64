import{_ as p,v as a,b as c,l as n,d as w,c as d,a as o,t as l,w as h,e as f,f as u,g as v,o as g}from"./index-CaDjZEpv.js";const L={name:"Login",data(){return{form:{username:"",password:""},errors:{},loginError:"",isLoading:!1,loginTitle:"欢迎使用，请登录",versionConfig:a.getVersionConfig()}},mounted(){this.removeVersionListener=a.onVersionChange(i=>{this.versionConfig=a.getVersionConfig(),this.updateLoginTitle()}),this.updateLoginTitle()},beforeUnmount(){this.removeVersionListener&&this.removeVersionListener()},methods:{updateLoginTitle(){a.isHexinEdition()?this.loginTitle="欢迎使用合心科技WPS插件，请登录":this.loginTitle="欢迎使用万唯WPS插件，请登录"},clearError(i){this.errors[i]&&(this.errors={...this.errors,[i]:""}),this.loginError=""},async handleLogin(){this.errors={},this.loginError="";const i=c(this.form.username,this.form.password);if(!i.isValid){n.warn("登录表单验证失败",i.errors),this.errors=i.errors;return}this.isLoading=!0;try{const e=await w(this.form.username,this.form.password);console.log("login result:",e),n.info("登录请求返回结果",{success:e.success,message:e.message}),e.success?(n.success("登录成功，准备跳转到taskpane页面"),n.info("Router对象信息",{currentRoute:this.$router.currentRoute.value,routes:this.$router.getRoutes().map(m=>m.path)}),window.location.hash="#/taskpane",setTimeout(()=>{n.info("路由跳转完成后状态",{hash:window.location.hash,href:window.location.href})},100),window.location.hash="#/taskpane"):e.code==="AI_EDIT_DISABLED"?(this.loginError="该企业尚未开启 AI 编辑功能，请联系您的企业管理员。",n.warn("AI编辑功能未开启",{reason:e.message,code:e.code})):(this.loginError=e.message,n.error("登录失败",{reason:e.message,code:e.code}))}catch(e){this.loginError="登录失败，请稍后重试",n.error("登录过程发生异常",e),console.error("Login error:",e)}finally{this.isLoading=!1}}}},E={class:"login-container"},_={class:"login-form"},y={class:"form-group"},V={key:0,class:"error-message"},T={class:"form-group"},b={key:0,class:"error-message"},k={key:0,class:"login-error"},I=["disabled"];function C(i,e,m,x,r,t){return g(),d("div",E,[o("div",_,[o("h2",null,l(r.loginTitle),1),o("div",y,[e[6]||(e[6]=o("label",{for:"username"},"用户名",-1)),h(o("input",{type:"text",id:"username","onUpdate:modelValue":e[0]||(e[0]=s=>r.form.username=s),placeholder:"请输入手机号或邮箱",onInput:e[1]||(e[1]=s=>t.clearError("username"))},null,544),[[f,r.form.username]]),r.errors.username?(g(),d("div",V,l(r.errors.username),1)):u("",!0)]),o("div",T,[e[7]||(e[7]=o("label",{for:"password"},"密码",-1)),h(o("input",{type:"password",id:"password","onUpdate:modelValue":e[2]||(e[2]=s=>r.form.password=s),placeholder:"请输入密码",onInput:e[3]||(e[3]=s=>t.clearError("password")),onKeyup:e[4]||(e[4]=v((...s)=>t.handleLogin&&t.handleLogin(...s),["enter"]))},null,544),[[f,r.form.password]]),r.errors.password?(g(),d("div",b,l(r.errors.password),1)):u("",!0)]),r.loginError?(g(),d("div",k,l(r.loginError),1)):u("",!0),o("button",{class:"login-btn",onClick:e[5]||(e[5]=(...s)=>t.handleLogin&&t.handleLogin(...s)),disabled:r.isLoading},l(r.isLoading?"登录中...":"登录"),9,I)])])}const A=p(L,[["render",C],["__scopeId","data-v-958bd87f"]]);export{A as default};
