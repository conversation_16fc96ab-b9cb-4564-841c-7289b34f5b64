import{U as Ks,r as se,h as ct,v as Oe,i as z,j as Ct,k as Es,m as qt,_ as $s,n as Xs,o as q,c as Y,a as v,p as Js,t as te,f as Z,q as ke,w as De,s as Dt,e as Wt,F as xt,u as Tt,x as ut,y as Gs,z as oe,A as Bt,B as os,C as st,D as Zs,E as Qs}from"./index-C3Lt4xGo.js";function en(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=Ks.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const tn={onbuttonclick:en};var sn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function nn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function an(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var i=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,i.get?i:{enumerable:!0,get:function(){return e[a]}})}),t}var As={exports:{}};const on={},rn=Object.freeze(Object.defineProperty({__proto__:null,default:on},Symbol.toStringTag,{value:"Module"})),rs=an(rn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",i=a?window:{};i.JS_SHA1_NO_WINDOW&&(a=!1);var g=!a&&typeof self=="object",y=!i.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;y?i=sn:g&&(i=self);var S=!i.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,b=!i.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",x="0123456789abcdef".split(""),$=[-**********,8388608,32768,128],I=[24,16,8,0],U=["hex","array","digest","arrayBuffer"],D=[],j=Array.isArray;(i.JS_SHA1_NO_NODE_JS||!j)&&(j=function(p){return Object.prototype.toString.call(p)==="[object Array]"});var M=ArrayBuffer.isView;b&&(i.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!M)&&(M=function(p){return typeof p=="object"&&p.buffer&&p.buffer.constructor===ArrayBuffer});var V=function(p){var w=typeof p;if(w==="string")return[p,!0];if(w!=="object"||p===null)throw new Error(s);if(b&&p.constructor===ArrayBuffer)return[new Uint8Array(p),!1];if(!j(p)&&!M(p))throw new Error(s);return[p,!1]},K=function(p){return function(w){return new _(!0).update(w)[p]()}},L=function(){var p=K("hex");y&&(p=N(p)),p.create=function(){return new _},p.update=function(T){return p.create().update(T)};for(var w=0;w<U.length;++w){var k=U[w];p[k]=K(k)}return p},N=function(p){var w=rs,k=rs.Buffer,T;k.from&&!i.JS_SHA1_NO_BUFFER_FROM?T=k.from:T=function(A){return new k(A)};var E=function(A){if(typeof A=="string")return w.createHash("sha1").update(A,"utf8").digest("hex");if(A==null)throw new Error(s);return A.constructor===ArrayBuffer&&(A=new Uint8Array(A)),j(A)||M(A)||A.constructor===k?w.createHash("sha1").update(T(A)).digest("hex"):p(A)};return E},h=function(p){return function(w,k){return new J(w,!0).update(k)[p]()}},X=function(){var p=h("hex");p.create=function(T){return new J(T)},p.update=function(T,E){return p.create(T).update(E)};for(var w=0;w<U.length;++w){var k=U[w];p[k]=h(k)}return p};function _(p){p?(D[0]=D[16]=D[1]=D[2]=D[3]=D[4]=D[5]=D[6]=D[7]=D[8]=D[9]=D[10]=D[11]=D[12]=D[13]=D[14]=D[15]=0,this.blocks=D):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}_.prototype.update=function(p){if(this.finalized)throw new Error(t);var w=V(p);p=w[0];for(var k=w[1],T,E=0,A,O=p.length||0,C=this.blocks;E<O;){if(this.hashed&&(this.hashed=!1,C[0]=this.block,this.block=C[16]=C[1]=C[2]=C[3]=C[4]=C[5]=C[6]=C[7]=C[8]=C[9]=C[10]=C[11]=C[12]=C[13]=C[14]=C[15]=0),k)for(A=this.start;E<O&&A<64;++E)T=p.charCodeAt(E),T<128?C[A>>>2]|=T<<I[A++&3]:T<2048?(C[A>>>2]|=(192|T>>>6)<<I[A++&3],C[A>>>2]|=(128|T&63)<<I[A++&3]):T<55296||T>=57344?(C[A>>>2]|=(224|T>>>12)<<I[A++&3],C[A>>>2]|=(128|T>>>6&63)<<I[A++&3],C[A>>>2]|=(128|T&63)<<I[A++&3]):(T=65536+((T&1023)<<10|p.charCodeAt(++E)&1023),C[A>>>2]|=(240|T>>>18)<<I[A++&3],C[A>>>2]|=(128|T>>>12&63)<<I[A++&3],C[A>>>2]|=(128|T>>>6&63)<<I[A++&3],C[A>>>2]|=(128|T&63)<<I[A++&3]);else for(A=this.start;E<O&&A<64;++E)C[A>>>2]|=p[E]<<I[A++&3];this.lastByteIndex=A,this.bytes+=A-this.start,A>=64?(this.block=C[16],this.start=A-64,this.hash(),this.hashed=!0):this.start=A}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},_.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var p=this.blocks,w=this.lastByteIndex;p[16]=this.block,p[w>>>2]|=$[w&3],this.block=p[16],w>=56&&(this.hashed||this.hash(),p[0]=this.block,p[16]=p[1]=p[2]=p[3]=p[4]=p[5]=p[6]=p[7]=p[8]=p[9]=p[10]=p[11]=p[12]=p[13]=p[14]=p[15]=0),p[14]=this.hBytes<<3|this.bytes>>>29,p[15]=this.bytes<<3,this.hash()}},_.prototype.hash=function(){var p=this.h0,w=this.h1,k=this.h2,T=this.h3,E=this.h4,A,O,C,F=this.blocks;for(O=16;O<80;++O)C=F[O-3]^F[O-8]^F[O-14]^F[O-16],F[O]=C<<1|C>>>31;for(O=0;O<20;O+=5)A=w&k|~w&T,C=p<<5|p>>>27,E=C+A+E+1518500249+F[O]<<0,w=w<<30|w>>>2,A=p&w|~p&k,C=E<<5|E>>>27,T=C+A+T+1518500249+F[O+1]<<0,p=p<<30|p>>>2,A=E&p|~E&w,C=T<<5|T>>>27,k=C+A+k+1518500249+F[O+2]<<0,E=E<<30|E>>>2,A=T&E|~T&p,C=k<<5|k>>>27,w=C+A+w+1518500249+F[O+3]<<0,T=T<<30|T>>>2,A=k&T|~k&E,C=w<<5|w>>>27,p=C+A+p+1518500249+F[O+4]<<0,k=k<<30|k>>>2;for(;O<40;O+=5)A=w^k^T,C=p<<5|p>>>27,E=C+A+E+1859775393+F[O]<<0,w=w<<30|w>>>2,A=p^w^k,C=E<<5|E>>>27,T=C+A+T+1859775393+F[O+1]<<0,p=p<<30|p>>>2,A=E^p^w,C=T<<5|T>>>27,k=C+A+k+1859775393+F[O+2]<<0,E=E<<30|E>>>2,A=T^E^p,C=k<<5|k>>>27,w=C+A+w+1859775393+F[O+3]<<0,T=T<<30|T>>>2,A=k^T^E,C=w<<5|w>>>27,p=C+A+p+1859775393+F[O+4]<<0,k=k<<30|k>>>2;for(;O<60;O+=5)A=w&k|w&T|k&T,C=p<<5|p>>>27,E=C+A+E-1894007588+F[O]<<0,w=w<<30|w>>>2,A=p&w|p&k|w&k,C=E<<5|E>>>27,T=C+A+T-1894007588+F[O+1]<<0,p=p<<30|p>>>2,A=E&p|E&w|p&w,C=T<<5|T>>>27,k=C+A+k-1894007588+F[O+2]<<0,E=E<<30|E>>>2,A=T&E|T&p|E&p,C=k<<5|k>>>27,w=C+A+w-1894007588+F[O+3]<<0,T=T<<30|T>>>2,A=k&T|k&E|T&E,C=w<<5|w>>>27,p=C+A+p-1894007588+F[O+4]<<0,k=k<<30|k>>>2;for(;O<80;O+=5)A=w^k^T,C=p<<5|p>>>27,E=C+A+E-899497514+F[O]<<0,w=w<<30|w>>>2,A=p^w^k,C=E<<5|E>>>27,T=C+A+T-899497514+F[O+1]<<0,p=p<<30|p>>>2,A=E^p^w,C=T<<5|T>>>27,k=C+A+k-899497514+F[O+2]<<0,E=E<<30|E>>>2,A=T^E^p,C=k<<5|k>>>27,w=C+A+w-899497514+F[O+3]<<0,T=T<<30|T>>>2,A=k^T^E,C=w<<5|w>>>27,p=C+A+p-899497514+F[O+4]<<0,k=k<<30|k>>>2;this.h0=this.h0+p<<0,this.h1=this.h1+w<<0,this.h2=this.h2+k<<0,this.h3=this.h3+T<<0,this.h4=this.h4+E<<0},_.prototype.hex=function(){this.finalize();var p=this.h0,w=this.h1,k=this.h2,T=this.h3,E=this.h4;return x[p>>>28&15]+x[p>>>24&15]+x[p>>>20&15]+x[p>>>16&15]+x[p>>>12&15]+x[p>>>8&15]+x[p>>>4&15]+x[p&15]+x[w>>>28&15]+x[w>>>24&15]+x[w>>>20&15]+x[w>>>16&15]+x[w>>>12&15]+x[w>>>8&15]+x[w>>>4&15]+x[w&15]+x[k>>>28&15]+x[k>>>24&15]+x[k>>>20&15]+x[k>>>16&15]+x[k>>>12&15]+x[k>>>8&15]+x[k>>>4&15]+x[k&15]+x[T>>>28&15]+x[T>>>24&15]+x[T>>>20&15]+x[T>>>16&15]+x[T>>>12&15]+x[T>>>8&15]+x[T>>>4&15]+x[T&15]+x[E>>>28&15]+x[E>>>24&15]+x[E>>>20&15]+x[E>>>16&15]+x[E>>>12&15]+x[E>>>8&15]+x[E>>>4&15]+x[E&15]},_.prototype.toString=_.prototype.hex,_.prototype.digest=function(){this.finalize();var p=this.h0,w=this.h1,k=this.h2,T=this.h3,E=this.h4;return[p>>>24&255,p>>>16&255,p>>>8&255,p&255,w>>>24&255,w>>>16&255,w>>>8&255,w&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,T>>>24&255,T>>>16&255,T>>>8&255,T&255,E>>>24&255,E>>>16&255,E>>>8&255,E&255]},_.prototype.array=_.prototype.digest,_.prototype.arrayBuffer=function(){this.finalize();var p=new ArrayBuffer(20),w=new DataView(p);return w.setUint32(0,this.h0),w.setUint32(4,this.h1),w.setUint32(8,this.h2),w.setUint32(12,this.h3),w.setUint32(16,this.h4),p};function J(p,w){var k,T=V(p);if(p=T[0],T[1]){var E=[],A=p.length,O=0,C;for(k=0;k<A;++k)C=p.charCodeAt(k),C<128?E[O++]=C:C<2048?(E[O++]=192|C>>>6,E[O++]=128|C&63):C<55296||C>=57344?(E[O++]=224|C>>>12,E[O++]=128|C>>>6&63,E[O++]=128|C&63):(C=65536+((C&1023)<<10|p.charCodeAt(++k)&1023),E[O++]=240|C>>>18,E[O++]=128|C>>>12&63,E[O++]=128|C>>>6&63,E[O++]=128|C&63);p=E}p.length>64&&(p=new _(!0).update(p).array());var F=[],ae=[];for(k=0;k<64;++k){var re=p[k]||0;F[k]=92^re,ae[k]=54^re}_.call(this,w),this.update(ae),this.oKeyPad=F,this.inner=!0,this.sharedMemory=w}J.prototype=new _,J.prototype.finalize=function(){if(_.prototype.finalize.call(this),this.inner){this.inner=!1;var p=this.array();_.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(p),_.prototype.finalize.call(this)}};var G=L();G.sha1=G,G.sha1.hmac=X(),S?e.exports=G:i.sha1=G})()})(As);var ln=As.exports;const cn=nn(ln);function is(){return"http://worksheet.hexinedu.com"}function dt(){return"http://127.0.0.1:3000"}function un(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const pt=async(e,s,t,a={},i=8e3)=>{try{return await Promise.race([e(),new Promise((g,y)=>setTimeout(()=>y(new Error("WebSocket请求超时，切换到HTTP")),i))])}catch{try{let y;return s==="get"?y=await Ct.get(t,{params:a}):s==="post"?y=await Ct.post(t,a):s==="delete"&&(y=await Ct.delete(t)),y.data}catch(y){throw new Error(`请求失败: ${y.message||"未知错误"}`)}}};function dn(e,s,t,a){const i=[e,s,t,a].join(":");return cn(i)}function pn(){const e=se(""),s=se(""),t=se(""),a=ct({}),i=se(""),g=se("");let y="",S=null;const b=se("c:\\Temp"),x=ct({appKey:"",appSecret:""}),$=ct({show:!1,message:"",resolveCallback:null,rejectCallback:null}),I=se(""),U=se("junior"),D=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],j=()=>Oe.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],M=ct(j()),V=async()=>{try{const n=await z.getWatcherStatus();n.data&&n.data.watchDir&&(b.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${b.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},K=async()=>{var n,o,r;try{if(!x.appKey||!x.appSecret)throw new Error("未初始化app信息");const f=60,u=Date.now();let d;try{const ee=window.Application.PluginStorage.getItem("token_info");ee&&(d=JSON.parse(ee))}catch(ee){d=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${ee.message}</span><br/>`}if(d&&d.access_token&&d.expired_time>u+f*1e3)return d.access_token;const m=x.appKey,P="1234567",R=Math.floor(Date.now()/1e3),H=dn(m,P,x.appSecret,R),B=await Ct.get(is()+"/api/open/account/v1/auth/token",{params:{app_key:m,app_nonstr:P,app_timestamp:R,app_signature:H}});if((o=(n=B.data)==null?void 0:n.data)!=null&&o.access_token){const ee=B.data.data.access_token;let ne;if(B.data.data.expired_time){const he=parseInt(B.data.data.expired_time);ne=u+he*1e3,t.value+=`<span class="log-item info">token已更新，有效期${he}秒</span><br/>`}else ne=u+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const le={access_token:ee,expired_time:ne};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(le))}catch(he){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${he.message}</span><br/>`}return ee}else throw new Error(((r=B.data)==null?void 0:r.message)||"获取access_token失败")}catch(f){throw t.value+=`<span class="log-item error">获取access_token失败: ${f.message}</span><br/>`,f}},L=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},N=()=>{const n=window.Application,o=n.Documents.Count;for(let r=1;r<=o;r++){const f=n.Documents.Item(r);if(f.DocID===i.value)return f}return null},h=()=>{try{const n=N();if(!n)return{isValid:!1,message:"未找到当前文档"};let o="";try{o=n.Name||""}catch{try{o=w("getDocName")||""}catch{o=""}}if(o){const r=o.toLowerCase();return r.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:r.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},X=n=>n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",_=n=>n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"进行中",J=n=>{const o=Date.now()-n,r=Math.floor(o/1e3);return r<60?`${r}秒`:r<3600?`${Math.floor(r/60)}分${r%60}秒`:`${Math.floor(r/3600)}时${Math.floor(r%3600/60)}分`},G=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const r=N();if(r&&r.ContentControls)for(let f=1;f<=r.ContentControls.Count;f++)try{const u=r.ContentControls.Item(f);if(u&&u.Title&&(u.Title===`任务_${n}`||u.Title===`任务增强_${n}`)){const d=u.Title===`任务增强_${n}`||a[n].isEnhanced;u.Title=d?`已停止增强_${n}`:`已停止_${n}`,t.value+=`<span class="log-item info">已将${d?"增强":"普通"}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(r){t.value+=`<span class="log-item warning">更新控件标题失败: ${r.message}</span><br/>`}let o=null;if(W[n]&&W[n].urlId){o=W[n].urlId;try{try{const r=await pt(async()=>await z.getUrlMonitorStatus(),"get",`${dt()}/api/url/status`),u=(Array.isArray(r)?r:r.data?Array.isArray(r.data)?r.data:[]:[]).find(d=>d.urlId===o);u&&u.downloadedPath&&(a[n].resultFile=u.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${u.downloadedPath}</span><br/>`)}catch(r){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${r.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ie(o),delete W[n]}catch(r){t.value+=`<span class="log-item warning">停止URL监控出错: ${r.message}，将重试</span><br/>`,delete W[n],setTimeout(async()=>{try{o&&await pt(async()=>await z.stopUrlMonitoring(o),"delete",`${dt()}/api/url/monitor/${o}`)}catch(f){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${f.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(o){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${o.message}</span><br/>`,W[n]&&delete W[n]}},p=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const o=N();if(o&&o.ContentControls)for(let f=1;f<=o.ContentControls.Count;f++)try{const u=o.ContentControls.Item(f);if(u&&u.Title&&(u.Title===`任务_${n}`||u.Title===`任务增强_${n}`)){const d=u.Title===`任务增强_${n}`||a[n].isEnhanced;u.Title=d?`已停止增强_${n}`:`已停止_${n}`,u.LockContents=!1,t.value+=`<span class="log-item info">已将${d?"增强":"普通"}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let r=null;if(W[n]&&W[n].urlId){r=W[n].urlId;try{const f=await z.getUrlMonitorStatus(),d=(Array.isArray(f)?f:f.data?Array.isArray(f.data)?f.data:[]:[]).find(m=>m.urlId===r);d&&d.downloadedPath&&(a[n].resultFile=d.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${d.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ie(r),delete W[n]}catch(f){t.value+=`<span class="log-item warning">停止URL监控出错: ${f.message}，将重试</span><br/>`,delete W[n]}}},w=n=>tn.onbuttonclick(n),k=()=>{try{e.value=w("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},T=()=>{N().ActiveWindow.Selection.Copy()},E=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${b.value}\\${n}`,12,"","",!1),o.Close(),N().ActiveWindow.Activate()},A=n=>{const o=window.Application.Documents.Add("",!1,0,!1);o.Content.Paste(),o.SaveAs2(`${b.value}\\${n}`,12,"","",!1),o.Close()},O=n=>{try{const r=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,f=window.Application.Documents.Add("",!1,0,!1);f.Content.Paste();const u=`${r}\\${n}`;f.SaveAs2(u,12,"","",!1),f.Close(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${u}.docx</span><br/>`}catch(o){throw t.value+=`<span class="log-item error">方式三保存失败: ${o.message}</span><br/>`,o}},C=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${b.value}\\${n}`,12,"","",!1),N().ActiveWindow.Activate()},F=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let o="method2";try{const r=await z.getSaveMethod();if(r.success&&r.saveMethod){o=r.saveMethod;const f=o==="method1"?"方式一":o==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${f}</span><br/>`}}catch(r){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${r.message}</span><br/>`}o==="method1"?(E(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${b.value}\\${n}.docx</span><br/>`):o==="method2"?(A(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${b.value}\\${n}.docx</span><br/>`):o==="method3"?(O(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):o==="method4"&&(C(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${b.value}\\${n}.docx</span><br/>`),z.associateFileWithClient(`${n}.docx`).then(r=>{r.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${r.message||"未知错误"}</span><br/>`}).catch(r=>{t.value+=`<span class="log-item warning">关联文件时出错: ${r.message}</span><br/>`})}catch(o){t.value+=`<span class="log-item error">保存文件失败: ${o.message}</span><br/>`}},ae=se(null),re=ct([]),ce=new Set,Ae=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),ve=async n=>{try{const o=n.slice(y.length);if(o.trim()){const r=z.getClientId();if(!r){console.warn("无法获取客户端ID，跳过日志同步");return}const f=Ae(o);if(!f.trim()){y=n;return}await z.sendRequest("logger","syncLog",{content:f,timestamp:new Date().toISOString(),clientId:r}),y=n}}catch(o){console.error("同步日志到服务端失败:",o)}},we=()=>{z.connect().then(()=>{V()}).catch(o=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${o.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const o=[];for(const r in a)if(a.hasOwnProperty(r)){const f=a[r];f.status===1&&!f.terminated&&(o.includes(r)||o.push(r))}if(o.length>0){let r=!1;try{const f=N();if(f){const d=`taskpane_id_${f.DocID}`,m=window.Application.PluginStorage.getItem(d);if(m){const P=window.Application.GetTaskPane(m);P&&(r=P.Visible)}}}catch(f){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${f.message}</span><br/>`,r=!0}setTimeout(()=>{if(r){const f=`检测到 ${o.length} 个未完成的任务，是否继续？`;it(f).then(u=>{u?(t.value+=`<span class="log-item info">用户选择继续 ${o.length} 个进行中的任务 (by taskId)...</span><br/>`,z.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:o}).then(d=>{d&&d.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${d.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(d==null?void 0:d.message)||"未知错误"}</span><br/>`}).catch(d=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${d.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',o.forEach(d=>{G(d)}),t.value+=`<span class="log-item success">${o.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(u=>{t.value+=`<span class="log-item error">弹窗错误: ${u.message}，默认停止任务（保留控件）</span><br/>`,o.forEach(d=>{G(d)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};z.setConnectionSuccessHandler(n),z.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),z.addEventListener("connection",o=>{o.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${o.reason||"未知"}, 代码: ${o.code||"N/A"}，将自动重连</span><br/>`)}),z.addEventListener("watcher",o=>{var r,f;if(re.push(o),o.type,o.eventType==="uploadSuccess"){const u=(r=o.data)==null?void 0:r.file,d=u==null?void 0:u.replace(/\.docx$/,""),m=`${o.eventType}_${u}_${Date.now()}`;if(ce.has(m)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${u}</span><br/>`;return}if(ce.add(m),ce.size>100){const R=ce.values();ce.delete(R.next().value)}const P=d&&((f=a[d])==null?void 0:f.wordType);Ce(o,P)}else o.eventType&&Ce(o,null)}),z.addEventListener("urlMonitor",o=>{re.push(o),o.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${o.eventType||o.action}</span><br/>`),o.eventType&&Ce(o,null)}),z.addEventListener("health",o=>{}),z.addEventListener("error",o=>{const r=o.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${r}</span><br/>`,console.error("WebSocket错误:",o)})},W=ct({}),ue=async(n,o,r=!1,f=5e3,u={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const d=await z.startUrlMonitoring(n,f,{downloadOnSuccess:u.downloadOnSuccess!==void 0?u.downloadOnSuccess:!0,appKey:u.appKey,filename:u.filename,taskId:o});t.value+=`<span class="log-item info">URL监控请求数据: ${JSON.stringify(d)}</span><br/>`;const m=d.success||(d==null?void 0:d.success),P=d.urlId||(d==null?void 0:d.urlId);if(m&&P){W[o]={urlId:P,url:n,isResultUrl:r,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${P}</span><br/>`;try{await z.startUrlChecking(P)}catch{}return P}else throw new Error("服务器返回失败")}catch(d){return t.value+=`<span class="log-item error">启动URL监控失败: ${d.message}</span><br/>`,null}},ie=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(W).forEach(r=>{W[r].urlId===n&&delete W[r]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const o=await pt(async()=>await z.stopUrlMonitoring(n),"delete",`${dt()}/api/url/monitor/${n}`);return o&&(o.success||o!=null&&o.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(o){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${o.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await pt(async()=>await z.stopUrlMonitoring(n),"delete",`${dt()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},Ie=async()=>{try{const n=await pt(async()=>await z.getUrlMonitorStatus(),"get",`${dt()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Q=async n=>{try{return await z.forceUrlCheck(n)}catch{return!1}},Ce=async(n,o)=>{var r;if(n.eventType==="uploadSuccess"){const f=n.data.file,u=f.replace(/\.docx$/,"");if(a[u]){if(a[u].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${u.substring(0,8)} 的上传通知</span><br/>`;return}if(a[u].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${u.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${f} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${o||a[u].wordType||"wps-analysis"}</span><br/>`,a[u].status=1,a[u].uploadSuccess=!0,await ye(u,o||a[u].wordType||"wps-analysis")}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:f,url:u,taskId:d,downloadedPath:m}=n.data,P=Object.keys(W).filter(R=>W[R].urlId===f);P.length>0&&P.forEach(R=>{m&&a[R]&&(a[R].resultFile=m,a[R].resultDownloaded=!0),delete W[R]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:f,url:u,filePath:d,taskId:m}=n.data;if(!Object.values(W).some(R=>R.urlId===f)&&m&&((r=a[m])!=null&&r.terminated))return;if(m&&a[m]&&a[m].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${f} 的文件下载通知</span><br/>`,f)try{await z.stopUrlMonitoring(f),W[m]&&delete W[m]}catch{}return}if(m&&a[m]){t.value+=`<span class="log-item success">收到结果文件通知: ${d}</span><br/>`,a[m].resultFile=d,a[m].resultDownloaded=!0;const R=J(a[m].startTime);t.value+=`<span class="log-item success">任务${m.substring(0,8)}完成，总耗时${R}</span><br/>`,await Le(m),W[m]&&W[m].urlId&&(ie(W[m].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete W[m])}else if(f){t.value+=`<span class="log-item info">URL文件已下载: ${d}</span><br/>`;const R=Object.keys(W).filter(H=>{var B;return W[H].urlId===f&&!((B=a[H])!=null&&B.terminated)});R.length>0&&R.forEach(H=>{if(a[H]&&(t.value+=`<span class="log-item info">关联到任务: ${H.substring(0,8)}</span><br/>`,a[H].resultFile=d,a[H].resultDownloaded=!0,a[H].status===1)){a[H].status=2;const B=J(a[H].startTime);t.value+=`<span class="log-item success">任务${H.substring(0,8)}完成，总耗时${B}</span><br/>`}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:f,url:u,error:d,taskId:m}=n.data;if(m&&a[m]&&a[m].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${m.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${d}</span><br/>`,m&&a[m]&&(a[m].status=-1,a[m].errorMessage=`下载失败: ${d}`,de(m),W[m]&&delete W[m]),f)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${f}</span><br/>`,await pt(async()=>await z.stopUrlMonitoring(f),"delete",`${dt()}/api/url/monitor/${f}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},ye=async(n,o="wps-analysis")=>{try{if(!x.appKey)throw new Error("未初始化appKey信息");const r=await K(),f=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,u=await Ct.post(is()+"/api/open/ticket/v1/ai_comment/create",{access_token:r,data:{app_key:x.appKey,subject:I.value,stage:U.value,file_name:`${n}`,word_url:f,word_type:o,is_ai_auto:!0,is_ai_edit:!0,create_user_id:x.userId,create_username:x.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),d=u.data.data.ticket_id;if(!d)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",de(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${d}，开始监控结果文件</span><br/>`;const m=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${d}.wps.docx`,P={downloadOnSuccess:!0,filename:`${d}.wps.docx`,appKey:x.appKey,ticketId:d,taskId:n},R=await ue(m,n,!0,3e3,P);return a[n]&&(a[n].ticketId=d,a[n].resultUrl=m,a[n].urlMonitorId=R,a[n].status=1),u.data}catch(r){return t.value+=`<span class="log-item error">API调用失败: ${r.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${r.message}`,de(n)),!1}},Ue=async(n,o="wps-analysis")=>new Promise((r,f)=>{try{t.value+=`<span class="log-item info">监控目录: ${b.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${o}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=o,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const u=1e3,d=30;let m=0;const P=setInterval(()=>{if(m++,a[n]&&a[n].uploadSuccess){clearInterval(P),r(!0);return}m>=d&&(clearInterval(P),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',f(new Error("上传超时，未收到完成通知")))},u)}catch(u){t.value+=`<span class="log-item error">任务处理异常: ${u.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${u.message}`),de(n),f(u)}}),ze=async()=>{var r;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,o=n.ActiveDocument;if(i.value=o.DocID,g.value=n.ActiveWindow.Index,o!=null&&o.ContentControls)for(let f=1;f<=o.ContentControls.Count;f++){const u=o.ContentControls.Item(f);if(u&&u.Title){let d=null,m=1,P=!1;if(u.Title.startsWith("任务增强_")?(d=u.Title.substring(5),m=1,P=!0):u.Title.startsWith("任务_")?(d=u.Title.substring(3),m=1):u.Title.startsWith("已完成增强_")?(d=u.Title.substring(6),m=2,P=!0):u.Title.startsWith("已完成_")?(d=u.Title.substring(4),m=2):u.Title.startsWith("异常增强_")?(d=u.Title.substring(5),m=-1,P=!0):u.Title.startsWith("异常_")?(d=u.Title.substring(3),m=-1):u.Title.startsWith("已停止增强_")?(d=u.Title.substring(6),m=4,P=!0):u.Title.startsWith("已停止_")&&(d=u.Title.substring(4),m=4),d&&!a[d]){let R="";try{R=((r=u.Range)==null?void 0:r.Text)||""}catch{}let H=Date.now()-24*60*60*1e3;try{if(d.length===24){const ne=d.substring(0,8),le=parseInt(ne,16);!isNaN(le)&&le>0&&le<2147483647&&(H=le*1e3)}else{const ne=Date.now()-864e5;m===2?H=ne-60*60*1e3:m===-1?H=ne-30*60*1e3:m===4?H=ne-45*60*1e3:H=ne}}catch{}a[d]={status:m,startTime:H,contentControlId:u.ID,isEnhanced:P,selectedText:R};const B=m===1?"进行中":m===2?"已完成":m===-1?"异常":m===4?"已停止":"未知",ee=P?"增强":"普通";t.value+=`<span class="log-item info">发现已有${ee}任务: ${d.substring(0,8)}, 状态: ${B}</span><br/>`}}}},de=(n,o=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}o&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const r=N();if(!r){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let f=!1;const u=a[n].isEnhanced;if(r.ContentControls&&r.ContentControls.Count>0)for(let d=r.ContentControls.Count;d>=1;d--)try{const m=r.ContentControls.Item(d);m&&m.Title&&m.Title.includes(n)&&(m.LockContents=!1,m.Delete(!1),f=!0,console.log(m.Title),t.value+=`<span class="log-item success">已解锁并删除${u?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(m){t.value+=`<span class="log-item warning">访问第${d}个控件时出错: ${m.message}</span><br/>`;continue}f?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${u?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(r){t.value+=`<span class="log-item error">删除内容控件失败: ${r.message}</span><br/>`}},Re=n=>{var o;try{const r=window.wps,f=N();if(!f||!f.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const u=(o=a[n])==null?void 0:o.isEnhanced;let d=null;for(let P=1;P<=f.ContentControls.Count;P++)try{const R=f.ContentControls.Item(P);if(R&&R.Title&&R.Title.includes(n)){d=R;break}}catch{continue}if(!d){const P=a[n];P&&(P.status===2||P.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}d.Range.Select();const m=r.Windows.Item(g.value);m&&m.Selection&&m.Selection.Range&&m.ScrollIntoView(m.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${u?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(r){t.value+=`<span class="log-item error">跳转到任务控件失败: ${r.message}</span><br/>`}},Le=async n=>{var m,P,R;const o=N(),r=a[n];if(!r){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(r.terminated||r.status!==1)return;if(r.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const f=N();let u=null;if(f&&f.ContentControls)for(let H=1;H<=f.ContentControls.Count;H++){const B=f.ContentControls.Item(H);if(B&&B.Title&&B.Title.includes(n)){u=B;break}}if(!u){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,r.errorMessage&&r.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${r.errorMessage}</span><br/>`;return}return}if(r.errorMessage&&r.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${r.errorMessage}</span><br/>`,de(n);return}if(!r.resultFile)return;a[n].documentInserted=!0;const d=u.Range;u.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${r.resultFile}...</span><br/>`;const H=o.Range(d.End,d.End);H.InsertParagraphAfter(),d.Text.includes("\v")&&H.InsertAfter("\v");let B=H.End;const ee=(m=d.Tables)==null?void 0:m.Count;if(ee){const fe=d.Tables.Item(ee);((P=fe==null?void 0:fe.Range)==null?void 0:P.End)>B&&(fe.Range.InsertParagraphAfter(),B=(R=fe==null?void 0:fe.Range)==null?void 0:R.End)}o.Range(B,B).InsertFile(r.resultFile);for(let fe=1;fe<=f.ContentControls.Count;fe++){const pe=f.ContentControls.Item(fe);if(pe&&pe.Title&&(pe.Title===`任务_${n}`||pe.Title===`任务增强_${n}`)){u=pe;break}}const le=o.Range(u.Range.End-1,u.Range.End);le.Text.includes("\r")&&le.Delete(),a[n].status=2,W[n]&&W[n].urlId&&z.sendRequest("urlMonitor","updateTaskStatus",{urlId:W[n].urlId,status:"completed",taskId:n}).then(fe=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(fe=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${fe.message}</span><br/>`});const wt=J(r.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${wt}</span><br/>`;const lt=u.Title===`任务增强_${n}`||r.isEnhanced;u&&(u.Title=lt?`已完成增强_${n}`:`已完成_${n}`,t.value+=`<span class="log-item info">已将${lt?"增强":"普通"}任务${n.substring(0,8)}控件标记为已完成</span><br/>`)}catch(H){a[n].documentInserted=!1,a[n].status=-1;const B=u.Title===`任务增强_${n}`||r.isEnhanced;u&&(u.Title=B?`异常增强_${n}`:`异常_${n}`,t.value+=`<span class="log-item info">已将${B?"增强":"普通"}任务${n.substring(0,8)}控件标记为异常</span><br/>`),t.value+=`<span class="log-item error">插入文档失败: ${H.message}</span><br/>`}},Fe=async()=>{const n=(f=1e3)=>new Promise(u=>{setTimeout(()=>u(),f)}),o=new Map,r=Object.keys(a).filter(f=>a[f].status===1&&!a[f].terminated);for(r.length?(r.forEach(f=>{o.has(f)||o.set(f,Date.now())}),await Promise.all(r.map(f=>Le(f)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const f=Object.keys(a).filter(u=>a[u].status===1&&!a[u].terminated);f.forEach(u=>{o.has(u)||o.set(u,Date.now());const d=o.get(u);(Date.now()-d)/1e3/60>=5e4&&a[u]&&!a[u].terminated&&(a[u].terminated=!0,a[u].status=-1,t.value+=`<span class="log-item warning">任务 ${u} 执行超过5分钟，已自动终止</span><br/>`,o.delete(u))}),f.length&&await Promise.all(f.map(u=>Le(u)))}},qe=async(n="wps-analysis")=>{const o=Be();if(o){const{primary:f,all:u}=o,{taskId:d,control:m,task:P,isEnhanced:R,overlapType:H}=f,B=u.filter(ee=>ee.task&&ee.task.status===1);if(B.length>0){const ee=B.map(ne=>ne.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${ee})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${u.length}个已有任务重叠，重叠类型: ${H}</span><br/>`,et();try{for(const ee of u){const{taskId:ne,control:le,task:he,isEnhanced:wt}=ee;t.value+=`<span class="log-item info">删除重叠的${wt?"增强":"普通"}任务 ${ne.substring(0,8)}，状态为 ${_((he==null?void 0:he.status)||0)}</span><br/>`,he&&(he.status=3,he.terminated=!0,he.errorMessage="用户重新创建任务时删除");try{le.LockContents=!1,le.Delete(!1),a[ne]&&(a[ne].placeholderRemoved=!0)}catch(lt){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${lt.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${u.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(ee){return Ze(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ee.message}</span><br/>`,Promise.reject(ee)}Ze()}const r=un().replace(/-/g,"").substring(0,8);return new Promise(async(f,u)=>{try{const d=window.wps,m=N(),P=m.ActiveWindow.Selection,R=P.Range,H=P.Text||"";if(s.value=H,T(),R){const B=R.Paragraphs,ee=B.Item(1),ne=B.Item(B.Count),le=ee.Range.Start,he=ne.Range.End,wt=R.Start,lt=R.End,fe=m.Range(Math.min(le,wt),Math.max(he,lt));try{let pe=m.ContentControls.Add(d.Enum.wdContentControlRichText,fe);if(!pe){if(console.log("创建内容控件失败"),pe=m.ContentControls.Add(d.Enum.wdContentControlRichText),!pe){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',u(new Error("创建内容控件失败"));return}R.Cut(),pe.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const jt=n==="wps-enhance_analysis";pe.Title=jt?`任务增强_${r}`:`任务_${r}`,pe.LockContents=!0,a[r]||(a[r]={}),a[r].contentControlId=pe.ID,a[r].isEnhanced=jt,t.value+=`<span class="log-item info">已创建${jt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const Ys=pe.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${Ys.length}</span><br/>`}catch(pe){t.value+=`<span class="log-item error">创建内容控件失败: ${pe.message}</span><br/>`,u(pe);return}}await F(r),a[r]={status:1,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:H},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${r}，类型: ${n}</span><br/>`;try{await Ue(r,n)?f():(a[r]&&a[r].status===1&&(a[r].status=-1,a[r].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${r.substring(0,8)}失败</span><br/>`,tt(r)),u(new Error("API调用失败或超时")))}catch(B){a[r]&&(a[r].status=-1,a[r].errorMessage=`执行错误: ${B.message}`,t.value+=`<span class="log-item error">任务${r.substring(0,8)}执行出错: ${B.message}</span><br/>`,tt(r)),u(B)}}catch(d){u(d)}})},Ke=async()=>{},Xe=()=>je()>0?"status-error":ge()>0?"status-running":xe()>0?"status-completed":"",ge=()=>Object.values(a).filter(n=>n.status===1).length,xe=()=>Object.values(a).filter(n=>n.status===2).length,je=()=>Object.values(a).filter(n=>n.status===-1).length,Me=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,o=N();if(!o){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let r=0;if(Object.keys(a).forEach(f=>{try{a[f].status===1&&(a[f].status=3,a[f].terminated=!0,a[f].errorMessage="强制清除"),de(f),r++}catch(u){t.value+=`<span class="log-item warning">清除任务${f.substring(0,8)}失败: ${u.message}</span><br/>`}}),o.ContentControls&&o.ContentControls.Count>0)for(let f=o.ContentControls.Count;f>=1;f--)try{const u=o.ContentControls.Item(f);if(u&&u.Title&&(u.Title.startsWith("任务_")||u.Title.startsWith("任务增强_")||u.Title.startsWith("已完成_")||u.Title.startsWith("已完成增强_")||u.Title.startsWith("异常_")||u.Title.startsWith("异常增强_")||u.Title.startsWith("已停止_")||u.Title.startsWith("已停止增强_")))try{u.LockContents=!1,u.Delete(!1);let d;u.Title.startsWith("任务增强_")?d=u.Title.substring(5):u.Title.startsWith("任务_")?d=u.Title.substring(3):u.Title.startsWith("已完成增强_")?d=u.Title.substring(6):u.Title.startsWith("已完成_")?d=u.Title.substring(4):u.Title.startsWith("异常增强_")?d=u.Title.substring(5):u.Title.startsWith("异常_")?d=u.Title.substring(3):u.Title.startsWith("已停止增强_")?d=u.Title.substring(6):u.Title.startsWith("已停止_")&&(d=u.Title.substring(4)),a[d]?(a[d].placeholderRemoved=!0,a[d].status=3):a[d]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},r++,t.value+=`<span class="log-item success">已删除任务${d.substring(0,8)}的内容控件</span><br/>`}catch(d){t.value+=`<span class="log-item error">删除控件失败: ${d.message}</span><br/>`}}catch(u){t.value+=`<span class="log-item warning">访问控件时出错: ${u.message}</span><br/>`}r>0?t.value+=`<span class="log-item success">已清除${r}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},_e=async()=>{try{const n=Object.values(W).map(o=>o.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const o of n)await ie(o)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Je=()=>{Es(async()=>{try{const o=window.Application.PluginStorage.getItem("user_info");if(!o)throw new Error("未找到用户信息");const r=JSON.parse(o);if(!r.orgs||!r.orgs[0])throw new Error("未找到组织信息");x.appKey=r.appKey,x.userName=r.nickname,x.userId=r.userId,x.appSecret=r.appSecret}catch(o){t.value+=`<span class="log-item error">初始化appKey失败: ${o.message}</span><br/>`}await V(),qt([I,U],async()=>{await c()},{immediate:!1}),await l();const n=Oe.onVersionChange(()=>{const o=j();M.splice(0,M.length,...o),Oe.isSeniorEdition()&&U.value==="junior"?U.value="senior":!Oe.isSeniorEdition()&&U.value==="senior"&&(U.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Oe.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',k(),await ze(),we(),Ge(),Fe(),window.addEventListener("beforeunload",_e),()=>{S&&clearTimeout(S),n&&n()}}),qt(t,n=>{S&&clearTimeout(S),S=setTimeout(()=>{ve(n)},10)},{immediate:!1})},Ge=()=>{z.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==I.value&&(I.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${I.value}</span><br/>`),n.data.stage!==U.value&&(U.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${U.value}</span><br/>`))})},We=se(!1),Be=()=>{try{const n=N(),o=n.ActiveWindow.Selection;if(!o||!n||!n.ContentControls)return null;const r=o.Range,f=[];for(let u=1;u<=n.ContentControls.Count;u++)try{const d=n.ContentControls.Item(u);if(!d)continue;const m=(d.Title||"").trim(),P=d.Range;if(r.Start<P.End&&r.End>P.Start){let R=null,H=!1,B=!1;if(!m)B=!0,R=`empty_${d.ID||Date.now()}`;else if(m.startsWith("任务_")||m.startsWith("任务增强_")||m.startsWith("已完成_")||m.startsWith("已完成增强_")||m.startsWith("异常_")||m.startsWith("异常增强_")||m.startsWith("已停止_")||m.startsWith("已停止增强_"))m.startsWith("任务增强_")?(R=m.substring(5),H=!0):m.startsWith("任务_")?R=m.substring(3):m.startsWith("已完成增强_")?(R=m.substring(6),H=!0):m.startsWith("已完成_")?R=m.substring(4):m.startsWith("异常增强_")?(R=m.substring(5),H=!0):m.startsWith("异常_")?R=m.substring(3):m.startsWith("已停止增强_")?(R=m.substring(6),H=!0):m.startsWith("已停止_")&&(R=m.substring(4));else continue;if(R){let ee;r.Start>=P.Start&&r.End<=P.End?ee="completely_within":r.Start<=P.Start&&r.End>=P.End?ee="completely_contains":ee="partial_overlap",f.push({taskId:R,control:d,task:B?null:a[R]||null,isEnhanced:H,isEmptyTitle:B,overlapType:ee,controlRange:{start:P.Start,end:P.End},selectionRange:{start:r.Start,end:r.End}})}}}catch{continue}return f.length===0?null:{primary:f[0],all:f}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},et=()=>{We.value=!0},Ze=()=>{We.value=!1},tt=async(n,o=!1)=>{et();try{await de(n,o)}finally{Ze()}},it=n=>new Promise((o,r)=>{$.show=!0,$.message=n,$.resolveCallback=o,$.rejectCallback=r}),bt=n=>{$.show=!1,n&&$.resolveCallback?$.resolveCallback(!0):$.resolveCallback&&$.resolveCallback(!1),$.resolveCallback=null,$.rejectCallback=null},l=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await z.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(I.value=n.data.subject),n.data.stage&&(U.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${I.value})和年级(${U.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},c=async()=>{try{if(I.value||U.value){t.value+=`<span class="log-item info">正在保存学科(${I.value})和年级(${U.value})设置到服务器...</span><br/>`;const n=await z.setSubjectAndStage(I.value,U.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}};return{docName:e,selected:s,logger:t,map:a,watchedDir:b,subject:I,stage:U,subjectOptions:D,stageOptions:M,fetchWatchedDir:V,clearLog:L,getCurrentDocument:N,checkDocumentFormat:h,getTaskStatusClass:X,getTaskStatusText:_,getElapsedTime:J,terminateTask:p,stopTaskWithoutRemovingControl:G,run1:qe,run2:Ke,getHeaderStatusClass:Xe,getRunningTasksCount:ge,getCompletedTasksCount:xe,getErrorTasksCount:je,setupLifecycle:Je,navigateToTaskControl:Re,forceCleanAllTasks:Me,ws:ae,wsMessages:re,initWebSocket:we,handleWatcherEvent:Ce,urlMonitorTasks:W,monitorUrlForTask:ue,stopUrlMonitoring:ie,getUrlMonitorStatus:Ie,forceUrlCheck:Q,cleanupUrlMonitoringTasks:_e,tryRemoveTaskPlaceholder:de,isLoading:We,isSelectionInTaskControl:Be,tryRemoveTaskPlaceholderWithLoading:tt,showConfirm:it,handleConfirm:bt,confirmDialog:$,loadSubjectAndStage:l,saveSubjectAndStage:c}}const fn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Oe.getVersionConfig(),selectedEdition:Oe.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),i=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${i}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await z.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await z[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await z.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await z.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await z.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await z.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await z.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await z.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await z.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Oe.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await Xs()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await z.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await z.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await z.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await z.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=z.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=z.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=z.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Oe.onVersionChange(e=>{this.versionConfig=Oe.getVersionConfig(),this.selectedEdition=Oe.getEdition()}),await z.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},hn={class:"file-watcher"},vn={class:"settings-modal"},gn={class:"modal-content"},mn={class:"modal-header"},bn={class:"version-tag"},wn={key:0,class:"user-info"},yn={class:"header-actions"},xn={key:0,class:"modal-body"},Tn={class:"status-section"},Cn={class:"status-item"},kn={class:"status-item"},Sn={class:"directory-section"},En={class:"directory-form"},$n={class:"radio-group"},An={class:"radio-item"},Mn={class:"radio-item"},_n={class:"radio-item"},Dn={key:0,class:"radio-item"},On={class:"directory-section"},Pn={class:"directory-form"},In={class:"form-group"},Un=["placeholder"],Rn=["disabled"],Ln={class:"directory-section"},Fn={class:"directory-form"},jn={class:"form-group"},Wn=["placeholder"],Bn=["disabled"],Vn={class:"directory-section"},Nn={class:"directory-form"},Hn={class:"form-group"},zn=["placeholder"],qn=["disabled"],Yn={class:"events-section"},Kn={class:"events-list"},Xn={class:"event-time"},Jn={class:"event-message"},Gn={class:"modal-footer"},Zn=["disabled"];function Qn(e,s,t,a,i,g){var y,S,b,x,$,I,U,D,j;return q(),Y("div",hn,[v("div",vn,[v("div",gn,[v("div",mn,[v("h3",null,[Js(te(i.versionConfig.shortName)+"设置 ",1),v("span",bn,te(i.versionConfig.appVersion),1),g.userInfo?(q(),Y("span",wn,"欢迎您，"+te(g.userInfo.nickname),1)):Z("",!0)]),v("div",yn,[v("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...M)=>g.handleLogout&&g.handleLogout(...M)),title:"退出登录"},s[21]||(s[21]=[v("span",{class:"icon-logout"},null,-1)])),v("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),((b=(S=(y=g.userInfo)==null?void 0:y.orgs)==null?void 0:S[0])==null?void 0:b.orgId)===2?(q(),Y("div",xn,[v("div",Tn,[v("div",Cn,[s[22]||(s[22]=v("span",{class:"label"},"状态：",-1)),v("span",{class:ke(["status-badge",i.status.status])},te(i.status.status==="running"?"运行中":"已停止"),3)]),v("div",kn,[s[23]||(s[23]=v("span",{class:"label"},"本次上传：",-1)),v("span",null,te(i.status.processedFiles||0)+" 个文件",1)]),v("div",Sn,[s[28]||(s[28]=v("h4",null,"保存方式设置",-1)),v("div",En,[v("div",$n,[v("label",An,[De(v("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=M=>i.currentSaveMethod=M),value:"method1",onChange:s[3]||(s[3]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Dt,i.currentSaveMethod]]),s[24]||(s[24]=v("span",{class:"radio-label"},"方式一",-1))]),v("label",Mn,[De(v("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=M=>i.currentSaveMethod=M),value:"method2",onChange:s[5]||(s[5]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Dt,i.currentSaveMethod]]),s[25]||(s[25]=v("span",{class:"radio-label"},"方式二 (默认)",-1))]),v("label",_n,[De(v("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=M=>i.currentSaveMethod=M),value:"method3",onChange:s[7]||(s[7]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Dt,i.currentSaveMethod]]),s[26]||(s[26]=v("span",{class:"radio-label"},"方式三",-1))]),((I=($=(x=g.userInfo)==null?void 0:x.orgs)==null?void 0:$[0])==null?void 0:I.orgId)===2?(q(),Y("label",Dn,[De(v("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=M=>i.currentSaveMethod=M),value:"method4",onChange:s[9]||(s[9]=(...M)=>g.updateSaveMethod&&g.updateSaveMethod(...M))},null,544),[[Dt,i.currentSaveMethod]]),s[27]||(s[27]=v("span",{class:"radio-label"},"方式四",-1))])):Z("",!0)]),i.saveMethodUpdateMessage?(q(),Y("div",{key:0,class:ke(["update-message",i.saveMethodUpdateSuccess?"success":"error"])},te(i.saveMethodUpdateMessage),3)):Z("",!0)])])]),Z("",!0),v("div",On,[s[32]||(s[32]=v("h4",null,"上传目录设置",-1)),v("div",Pn,[v("div",In,[s[31]||(s[31]=v("span",{class:"label"},"路径：",-1)),De(v("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=M=>i.newWatchDir=M),placeholder:i.status.watchDir||"C:\\Temp"},null,8,Un),[[Wt,i.newWatchDir]]),v("button",{class:"action-btn",onClick:s[13]||(s[13]=(...M)=>g.updateWatchDir&&g.updateWatchDir(...M)),disabled:i.isUpdating||!i.newWatchDir},te(i.isUpdating?"更新中...":"更新目录"),9,Rn)]),i.updateMessage?(q(),Y("div",{key:0,class:ke(["update-message",i.updateSuccess?"success":"error"])},te(i.updateMessage),3)):Z("",!0)])]),v("div",Ln,[s[34]||(s[34]=v("h4",null,"下载目录设置",-1)),v("div",Fn,[v("div",jn,[s[33]||(s[33]=v("span",{class:"label"},"路径：",-1)),De(v("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=M=>i.newDownloadPath=M),placeholder:i.downloadPath||"C:\\Temp\\Downloads"},null,8,Wn),[[Wt,i.newDownloadPath]]),v("button",{class:"action-btn",onClick:s[15]||(s[15]=(...M)=>g.updateDownloadPath&&g.updateDownloadPath(...M)),disabled:i.isUpdatingDownloadPath||!i.newDownloadPath},te(i.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Bn)]),i.downloadPathUpdateMessage?(q(),Y("div",{key:0,class:ke(["update-message",i.downloadPathUpdateSuccess?"success":"error"])},te(i.downloadPathUpdateMessage),3)):Z("",!0)])]),v("div",Vn,[s[36]||(s[36]=v("h4",null,"配置目录设置",-1)),v("div",Nn,[v("div",Hn,[s[35]||(s[35]=v("span",{class:"label"},"路径：",-1)),De(v("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=M=>i.newAddonConfigPath=M),placeholder:i.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,zn),[[Wt,i.newAddonConfigPath]]),v("button",{class:"action-btn",onClick:s[17]||(s[17]=(...M)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...M)),disabled:i.isUpdatingAddonConfigPath||!i.newAddonConfigPath},te(i.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,qn)]),i.addonConfigPathUpdateMessage?(q(),Y("div",{key:0,class:ke(["update-message",i.addonConfigPathUpdateSuccess?"success":"error"])},te(i.addonConfigPathUpdateMessage),3)):Z("",!0)])]),v("div",Yn,[s[37]||(s[37]=v("h4",null,"最近事件",-1)),v("div",Kn,[(q(!0),Y(xt,null,Tt(i.recentEvents,(M,V)=>(q(),Y("div",{key:V,class:"event-item"},[v("span",Xn,te(g.formatTime(M.timestamp)),1),v("span",{class:ke(["event-type",M.eventType])},te(g.getEventTypeText(M.eventType)),3),v("span",Jn,te(g.getEventMessage(M)),1)]))),128))])])])):Z("",!0),Z("",!0),v("div",Gn,[v("button",{class:ke(["control-btn",i.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...M)=>g.controlService&&g.controlService(...M)),disabled:!((j=(D=(U=g.userInfo)==null?void 0:U.orgs)==null?void 0:D[0])!=null&&j.orgId)===2},te(i.status.status==="running"?"停止服务":"启动服务"),11,Zn)])])])])}const ea=$s(fn,[["render",Qn],["__scopeId","data-v-4c5a00d8"]]);var me="top",Ee="bottom",$e="right",be="left",Gt="auto",Mt=[me,Ee,$e,be],ht="start",$t="end",ta="clippingParents",Ms="viewport",yt="popper",sa="reference",ls=Mt.reduce(function(e,s){return e.concat([s+"-"+ht,s+"-"+$t])},[]),_s=[].concat(Mt,[Gt]).reduce(function(e,s){return e.concat([s,s+"-"+ht,s+"-"+$t])},[]),na="beforeRead",aa="read",oa="afterRead",ra="beforeMain",ia="main",la="afterMain",ca="beforeWrite",ua="write",da="afterWrite",pa=[na,aa,oa,ra,ia,la,ca,ua,da];function He(e){return e?(e.nodeName||"").toLowerCase():null}function Te(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function rt(e){var s=Te(e).Element;return e instanceof s||e instanceof Element}function Se(e){var s=Te(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function Zt(e){if(typeof ShadowRoot>"u")return!1;var s=Te(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function fa(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},i=s.attributes[t]||{},g=s.elements[t];!Se(g)||!He(g)||(Object.assign(g.style,a),Object.keys(i).forEach(function(y){var S=i[y];S===!1?g.removeAttribute(y):g.setAttribute(y,S===!0?"":S)}))})}function ha(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var i=s.elements[a],g=s.attributes[a]||{},y=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),S=y.reduce(function(b,x){return b[x]="",b},{});!Se(i)||!He(i)||(Object.assign(i.style,S),Object.keys(g).forEach(function(b){i.removeAttribute(b)}))})}}const Ds={name:"applyStyles",enabled:!0,phase:"write",fn:fa,effect:ha,requires:["computeStyles"]};function Ne(e){return e.split("-")[0]}var at=Math.max,Ut=Math.min,vt=Math.round;function Yt(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Os(){return!/^((?!chrome|android).)*safari/i.test(Yt())}function gt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),i=1,g=1;s&&Se(e)&&(i=e.offsetWidth>0&&vt(a.width)/e.offsetWidth||1,g=e.offsetHeight>0&&vt(a.height)/e.offsetHeight||1);var y=rt(e)?Te(e):window,S=y.visualViewport,b=!Os()&&t,x=(a.left+(b&&S?S.offsetLeft:0))/i,$=(a.top+(b&&S?S.offsetTop:0))/g,I=a.width/i,U=a.height/g;return{width:I,height:U,top:$,right:x+I,bottom:$+U,left:x,x,y:$}}function Qt(e){var s=gt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function Ps(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&Zt(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function Ye(e){return Te(e).getComputedStyle(e)}function va(e){return["table","td","th"].indexOf(He(e))>=0}function Qe(e){return((rt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Lt(e){return He(e)==="html"?e:e.assignedSlot||e.parentNode||(Zt(e)?e.host:null)||Qe(e)}function cs(e){return!Se(e)||Ye(e).position==="fixed"?null:e.offsetParent}function ga(e){var s=/firefox/i.test(Yt()),t=/Trident/i.test(Yt());if(t&&Se(e)){var a=Ye(e);if(a.position==="fixed")return null}var i=Lt(e);for(Zt(i)&&(i=i.host);Se(i)&&["html","body"].indexOf(He(i))<0;){var g=Ye(i);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return i;i=i.parentNode}return null}function _t(e){for(var s=Te(e),t=cs(e);t&&va(t)&&Ye(t).position==="static";)t=cs(t);return t&&(He(t)==="html"||He(t)==="body"&&Ye(t).position==="static")?s:t||ga(e)||s}function es(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function kt(e,s,t){return at(e,Ut(s,t))}function ma(e,s,t){var a=kt(e,s,t);return a>t?t:a}function Is(){return{top:0,right:0,bottom:0,left:0}}function Us(e){return Object.assign({},Is(),e)}function Rs(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var ba=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Us(typeof s!="number"?s:Rs(s,Mt))};function wa(e){var s,t=e.state,a=e.name,i=e.options,g=t.elements.arrow,y=t.modifiersData.popperOffsets,S=Ne(t.placement),b=es(S),x=[be,$e].indexOf(S)>=0,$=x?"height":"width";if(!(!g||!y)){var I=ba(i.padding,t),U=Qt(g),D=b==="y"?me:be,j=b==="y"?Ee:$e,M=t.rects.reference[$]+t.rects.reference[b]-y[b]-t.rects.popper[$],V=y[b]-t.rects.reference[b],K=_t(g),L=K?b==="y"?K.clientHeight||0:K.clientWidth||0:0,N=M/2-V/2,h=I[D],X=L-U[$]-I[j],_=L/2-U[$]/2+N,J=kt(h,_,X),G=b;t.modifiersData[a]=(s={},s[G]=J,s.centerOffset=J-_,s)}}function ya(e){var s=e.state,t=e.options,a=t.element,i=a===void 0?"[data-popper-arrow]":a;i!=null&&(typeof i=="string"&&(i=s.elements.popper.querySelector(i),!i)||Ps(s.elements.popper,i)&&(s.elements.arrow=i))}const xa={name:"arrow",enabled:!0,phase:"main",fn:wa,effect:ya,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function mt(e){return e.split("-")[1]}var Ta={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ca(e,s){var t=e.x,a=e.y,i=s.devicePixelRatio||1;return{x:vt(t*i)/i||0,y:vt(a*i)/i||0}}function us(e){var s,t=e.popper,a=e.popperRect,i=e.placement,g=e.variation,y=e.offsets,S=e.position,b=e.gpuAcceleration,x=e.adaptive,$=e.roundOffsets,I=e.isFixed,U=y.x,D=U===void 0?0:U,j=y.y,M=j===void 0?0:j,V=typeof $=="function"?$({x:D,y:M}):{x:D,y:M};D=V.x,M=V.y;var K=y.hasOwnProperty("x"),L=y.hasOwnProperty("y"),N=be,h=me,X=window;if(x){var _=_t(t),J="clientHeight",G="clientWidth";if(_===Te(t)&&(_=Qe(t),Ye(_).position!=="static"&&S==="absolute"&&(J="scrollHeight",G="scrollWidth")),_=_,i===me||(i===be||i===$e)&&g===$t){h=Ee;var p=I&&_===X&&X.visualViewport?X.visualViewport.height:_[J];M-=p-a.height,M*=b?1:-1}if(i===be||(i===me||i===Ee)&&g===$t){N=$e;var w=I&&_===X&&X.visualViewport?X.visualViewport.width:_[G];D-=w-a.width,D*=b?1:-1}}var k=Object.assign({position:S},x&&Ta),T=$===!0?Ca({x:D,y:M},Te(t)):{x:D,y:M};if(D=T.x,M=T.y,b){var E;return Object.assign({},k,(E={},E[h]=L?"0":"",E[N]=K?"0":"",E.transform=(X.devicePixelRatio||1)<=1?"translate("+D+"px, "+M+"px)":"translate3d("+D+"px, "+M+"px, 0)",E))}return Object.assign({},k,(s={},s[h]=L?M+"px":"",s[N]=K?D+"px":"",s.transform="",s))}function ka(e){var s=e.state,t=e.options,a=t.gpuAcceleration,i=a===void 0?!0:a,g=t.adaptive,y=g===void 0?!0:g,S=t.roundOffsets,b=S===void 0?!0:S,x={placement:Ne(s.placement),variation:mt(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:i,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,us(Object.assign({},x,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:y,roundOffsets:b})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,us(Object.assign({},x,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:b})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Sa={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:ka,data:{}};var Ot={passive:!0};function Ea(e){var s=e.state,t=e.instance,a=e.options,i=a.scroll,g=i===void 0?!0:i,y=a.resize,S=y===void 0?!0:y,b=Te(s.elements.popper),x=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&x.forEach(function($){$.addEventListener("scroll",t.update,Ot)}),S&&b.addEventListener("resize",t.update,Ot),function(){g&&x.forEach(function($){$.removeEventListener("scroll",t.update,Ot)}),S&&b.removeEventListener("resize",t.update,Ot)}}const $a={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Ea,data:{}};var Aa={left:"right",right:"left",bottom:"top",top:"bottom"};function It(e){return e.replace(/left|right|bottom|top/g,function(s){return Aa[s]})}var Ma={start:"end",end:"start"};function ds(e){return e.replace(/start|end/g,function(s){return Ma[s]})}function ts(e){var s=Te(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function ss(e){return gt(Qe(e)).left+ts(e).scrollLeft}function _a(e,s){var t=Te(e),a=Qe(e),i=t.visualViewport,g=a.clientWidth,y=a.clientHeight,S=0,b=0;if(i){g=i.width,y=i.height;var x=Os();(x||!x&&s==="fixed")&&(S=i.offsetLeft,b=i.offsetTop)}return{width:g,height:y,x:S+ss(e),y:b}}function Da(e){var s,t=Qe(e),a=ts(e),i=(s=e.ownerDocument)==null?void 0:s.body,g=at(t.scrollWidth,t.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),y=at(t.scrollHeight,t.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),S=-a.scrollLeft+ss(e),b=-a.scrollTop;return Ye(i||t).direction==="rtl"&&(S+=at(t.clientWidth,i?i.clientWidth:0)-g),{width:g,height:y,x:S,y:b}}function ns(e){var s=Ye(e),t=s.overflow,a=s.overflowX,i=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+i+a)}function Ls(e){return["html","body","#document"].indexOf(He(e))>=0?e.ownerDocument.body:Se(e)&&ns(e)?e:Ls(Lt(e))}function St(e,s){var t;s===void 0&&(s=[]);var a=Ls(e),i=a===((t=e.ownerDocument)==null?void 0:t.body),g=Te(a),y=i?[g].concat(g.visualViewport||[],ns(a)?a:[]):a,S=s.concat(y);return i?S:S.concat(St(Lt(y)))}function Kt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Oa(e,s){var t=gt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function ps(e,s,t){return s===Ms?Kt(_a(e,t)):rt(s)?Oa(s,t):Kt(Da(Qe(e)))}function Pa(e){var s=St(Lt(e)),t=["absolute","fixed"].indexOf(Ye(e).position)>=0,a=t&&Se(e)?_t(e):e;return rt(a)?s.filter(function(i){return rt(i)&&Ps(i,a)&&He(i)!=="body"}):[]}function Ia(e,s,t,a){var i=s==="clippingParents"?Pa(e):[].concat(s),g=[].concat(i,[t]),y=g[0],S=g.reduce(function(b,x){var $=ps(e,x,a);return b.top=at($.top,b.top),b.right=Ut($.right,b.right),b.bottom=Ut($.bottom,b.bottom),b.left=at($.left,b.left),b},ps(e,y,a));return S.width=S.right-S.left,S.height=S.bottom-S.top,S.x=S.left,S.y=S.top,S}function Fs(e){var s=e.reference,t=e.element,a=e.placement,i=a?Ne(a):null,g=a?mt(a):null,y=s.x+s.width/2-t.width/2,S=s.y+s.height/2-t.height/2,b;switch(i){case me:b={x:y,y:s.y-t.height};break;case Ee:b={x:y,y:s.y+s.height};break;case $e:b={x:s.x+s.width,y:S};break;case be:b={x:s.x-t.width,y:S};break;default:b={x:s.x,y:s.y}}var x=i?es(i):null;if(x!=null){var $=x==="y"?"height":"width";switch(g){case ht:b[x]=b[x]-(s[$]/2-t[$]/2);break;case $t:b[x]=b[x]+(s[$]/2-t[$]/2);break}}return b}function At(e,s){s===void 0&&(s={});var t=s,a=t.placement,i=a===void 0?e.placement:a,g=t.strategy,y=g===void 0?e.strategy:g,S=t.boundary,b=S===void 0?ta:S,x=t.rootBoundary,$=x===void 0?Ms:x,I=t.elementContext,U=I===void 0?yt:I,D=t.altBoundary,j=D===void 0?!1:D,M=t.padding,V=M===void 0?0:M,K=Us(typeof V!="number"?V:Rs(V,Mt)),L=U===yt?sa:yt,N=e.rects.popper,h=e.elements[j?L:U],X=Ia(rt(h)?h:h.contextElement||Qe(e.elements.popper),b,$,y),_=gt(e.elements.reference),J=Fs({reference:_,element:N,strategy:"absolute",placement:i}),G=Kt(Object.assign({},N,J)),p=U===yt?G:_,w={top:X.top-p.top+K.top,bottom:p.bottom-X.bottom+K.bottom,left:X.left-p.left+K.left,right:p.right-X.right+K.right},k=e.modifiersData.offset;if(U===yt&&k){var T=k[i];Object.keys(w).forEach(function(E){var A=[$e,Ee].indexOf(E)>=0?1:-1,O=[me,Ee].indexOf(E)>=0?"y":"x";w[E]+=T[O]*A})}return w}function Ua(e,s){s===void 0&&(s={});var t=s,a=t.placement,i=t.boundary,g=t.rootBoundary,y=t.padding,S=t.flipVariations,b=t.allowedAutoPlacements,x=b===void 0?_s:b,$=mt(a),I=$?S?ls:ls.filter(function(j){return mt(j)===$}):Mt,U=I.filter(function(j){return x.indexOf(j)>=0});U.length===0&&(U=I);var D=U.reduce(function(j,M){return j[M]=At(e,{placement:M,boundary:i,rootBoundary:g,padding:y})[Ne(M)],j},{});return Object.keys(D).sort(function(j,M){return D[j]-D[M]})}function Ra(e){if(Ne(e)===Gt)return[];var s=It(e);return[ds(e),s,ds(s)]}function La(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var i=t.mainAxis,g=i===void 0?!0:i,y=t.altAxis,S=y===void 0?!0:y,b=t.fallbackPlacements,x=t.padding,$=t.boundary,I=t.rootBoundary,U=t.altBoundary,D=t.flipVariations,j=D===void 0?!0:D,M=t.allowedAutoPlacements,V=s.options.placement,K=Ne(V),L=K===V,N=b||(L||!j?[It(V)]:Ra(V)),h=[V].concat(N).reduce(function(W,ue){return W.concat(Ne(ue)===Gt?Ua(s,{placement:ue,boundary:$,rootBoundary:I,padding:x,flipVariations:j,allowedAutoPlacements:M}):ue)},[]),X=s.rects.reference,_=s.rects.popper,J=new Map,G=!0,p=h[0],w=0;w<h.length;w++){var k=h[w],T=Ne(k),E=mt(k)===ht,A=[me,Ee].indexOf(T)>=0,O=A?"width":"height",C=At(s,{placement:k,boundary:$,rootBoundary:I,altBoundary:U,padding:x}),F=A?E?$e:be:E?Ee:me;X[O]>_[O]&&(F=It(F));var ae=It(F),re=[];if(g&&re.push(C[T]<=0),S&&re.push(C[F]<=0,C[ae]<=0),re.every(function(W){return W})){p=k,G=!1;break}J.set(k,re)}if(G)for(var ce=j?3:1,Ae=function(ue){var ie=h.find(function(Ie){var Q=J.get(Ie);if(Q)return Q.slice(0,ue).every(function(Ce){return Ce})});if(ie)return p=ie,"break"},ve=ce;ve>0;ve--){var we=Ae(ve);if(we==="break")break}s.placement!==p&&(s.modifiersData[a]._skip=!0,s.placement=p,s.reset=!0)}}const Fa={name:"flip",enabled:!0,phase:"main",fn:La,requiresIfExists:["offset"],data:{_skip:!1}};function fs(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function hs(e){return[me,$e,Ee,be].some(function(s){return e[s]>=0})}function ja(e){var s=e.state,t=e.name,a=s.rects.reference,i=s.rects.popper,g=s.modifiersData.preventOverflow,y=At(s,{elementContext:"reference"}),S=At(s,{altBoundary:!0}),b=fs(y,a),x=fs(S,i,g),$=hs(b),I=hs(x);s.modifiersData[t]={referenceClippingOffsets:b,popperEscapeOffsets:x,isReferenceHidden:$,hasPopperEscaped:I},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":$,"data-popper-escaped":I})}const Wa={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:ja};function Ba(e,s,t){var a=Ne(e),i=[be,me].indexOf(a)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,y=g[0],S=g[1];return y=y||0,S=(S||0)*i,[be,$e].indexOf(a)>=0?{x:S,y}:{x:y,y:S}}function Va(e){var s=e.state,t=e.options,a=e.name,i=t.offset,g=i===void 0?[0,0]:i,y=_s.reduce(function($,I){return $[I]=Ba(I,s.rects,g),$},{}),S=y[s.placement],b=S.x,x=S.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=b,s.modifiersData.popperOffsets.y+=x),s.modifiersData[a]=y}const Na={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Va};function Ha(e){var s=e.state,t=e.name;s.modifiersData[t]=Fs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const za={name:"popperOffsets",enabled:!0,phase:"read",fn:Ha,data:{}};function qa(e){return e==="x"?"y":"x"}function Ya(e){var s=e.state,t=e.options,a=e.name,i=t.mainAxis,g=i===void 0?!0:i,y=t.altAxis,S=y===void 0?!1:y,b=t.boundary,x=t.rootBoundary,$=t.altBoundary,I=t.padding,U=t.tether,D=U===void 0?!0:U,j=t.tetherOffset,M=j===void 0?0:j,V=At(s,{boundary:b,rootBoundary:x,padding:I,altBoundary:$}),K=Ne(s.placement),L=mt(s.placement),N=!L,h=es(K),X=qa(h),_=s.modifiersData.popperOffsets,J=s.rects.reference,G=s.rects.popper,p=typeof M=="function"?M(Object.assign({},s.rects,{placement:s.placement})):M,w=typeof p=="number"?{mainAxis:p,altAxis:p}:Object.assign({mainAxis:0,altAxis:0},p),k=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,T={x:0,y:0};if(_){if(g){var E,A=h==="y"?me:be,O=h==="y"?Ee:$e,C=h==="y"?"height":"width",F=_[h],ae=F+V[A],re=F-V[O],ce=D?-G[C]/2:0,Ae=L===ht?J[C]:G[C],ve=L===ht?-G[C]:-J[C],we=s.elements.arrow,W=D&&we?Qt(we):{width:0,height:0},ue=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Is(),ie=ue[A],Ie=ue[O],Q=kt(0,J[C],W[C]),Ce=N?J[C]/2-ce-Q-ie-w.mainAxis:Ae-Q-ie-w.mainAxis,ye=N?-J[C]/2+ce+Q+Ie+w.mainAxis:ve+Q+Ie+w.mainAxis,Ue=s.elements.arrow&&_t(s.elements.arrow),ze=Ue?h==="y"?Ue.clientTop||0:Ue.clientLeft||0:0,de=(E=k==null?void 0:k[h])!=null?E:0,Re=F+Ce-de-ze,Le=F+ye-de,Fe=kt(D?Ut(ae,Re):ae,F,D?at(re,Le):re);_[h]=Fe,T[h]=Fe-F}if(S){var qe,Ke=h==="x"?me:be,Xe=h==="x"?Ee:$e,ge=_[X],xe=X==="y"?"height":"width",je=ge+V[Ke],Me=ge-V[Xe],_e=[me,be].indexOf(K)!==-1,Je=(qe=k==null?void 0:k[X])!=null?qe:0,Ge=_e?je:ge-J[xe]-G[xe]-Je+w.altAxis,We=_e?ge+J[xe]+G[xe]-Je-w.altAxis:Me,Be=D&&_e?ma(Ge,ge,We):kt(D?Ge:je,ge,D?We:Me);_[X]=Be,T[X]=Be-ge}s.modifiersData[a]=T}}const Ka={name:"preventOverflow",enabled:!0,phase:"main",fn:Ya,requiresIfExists:["offset"]};function Xa(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Ja(e){return e===Te(e)||!Se(e)?ts(e):Xa(e)}function Ga(e){var s=e.getBoundingClientRect(),t=vt(s.width)/e.offsetWidth||1,a=vt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function Za(e,s,t){t===void 0&&(t=!1);var a=Se(s),i=Se(s)&&Ga(s),g=Qe(s),y=gt(e,i,t),S={scrollLeft:0,scrollTop:0},b={x:0,y:0};return(a||!a&&!t)&&((He(s)!=="body"||ns(g))&&(S=Ja(s)),Se(s)?(b=gt(s,!0),b.x+=s.clientLeft,b.y+=s.clientTop):g&&(b.x=ss(g))),{x:y.left+S.scrollLeft-b.x,y:y.top+S.scrollTop-b.y,width:y.width,height:y.height}}function Qa(e){var s=new Map,t=new Set,a=[];e.forEach(function(g){s.set(g.name,g)});function i(g){t.add(g.name);var y=[].concat(g.requires||[],g.requiresIfExists||[]);y.forEach(function(S){if(!t.has(S)){var b=s.get(S);b&&i(b)}}),a.push(g)}return e.forEach(function(g){t.has(g.name)||i(g)}),a}function eo(e){var s=Qa(e);return pa.reduce(function(t,a){return t.concat(s.filter(function(i){return i.phase===a}))},[])}function to(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function so(e){var s=e.reduce(function(t,a){var i=t[a.name];return t[a.name]=i?Object.assign({},i,a,{options:Object.assign({},i.options,a.options),data:Object.assign({},i.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var vs={placement:"bottom",modifiers:[],strategy:"absolute"};function gs(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function no(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,i=s.defaultOptions,g=i===void 0?vs:i;return function(S,b,x){x===void 0&&(x=g);var $={placement:"bottom",orderedModifiers:[],options:Object.assign({},vs,g),modifiersData:{},elements:{reference:S,popper:b},attributes:{},styles:{}},I=[],U=!1,D={state:$,setOptions:function(K){var L=typeof K=="function"?K($.options):K;M(),$.options=Object.assign({},g,$.options,L),$.scrollParents={reference:rt(S)?St(S):S.contextElement?St(S.contextElement):[],popper:St(b)};var N=eo(so([].concat(a,$.options.modifiers)));return $.orderedModifiers=N.filter(function(h){return h.enabled}),j(),D.update()},forceUpdate:function(){if(!U){var K=$.elements,L=K.reference,N=K.popper;if(gs(L,N)){$.rects={reference:Za(L,_t(N),$.options.strategy==="fixed"),popper:Qt(N)},$.reset=!1,$.placement=$.options.placement,$.orderedModifiers.forEach(function(w){return $.modifiersData[w.name]=Object.assign({},w.data)});for(var h=0;h<$.orderedModifiers.length;h++){if($.reset===!0){$.reset=!1,h=-1;continue}var X=$.orderedModifiers[h],_=X.fn,J=X.options,G=J===void 0?{}:J,p=X.name;typeof _=="function"&&($=_({state:$,options:G,name:p,instance:D})||$)}}}},update:to(function(){return new Promise(function(V){D.forceUpdate(),V($)})}),destroy:function(){M(),U=!0}};if(!gs(S,b))return D;D.setOptions(x).then(function(V){!U&&x.onFirstUpdate&&x.onFirstUpdate(V)});function j(){$.orderedModifiers.forEach(function(V){var K=V.name,L=V.options,N=L===void 0?{}:L,h=V.effect;if(typeof h=="function"){var X=h({state:$,name:K,instance:D,options:N}),_=function(){};I.push(X||_)}})}function M(){I.forEach(function(V){return V()}),I=[]}return D}}var ao=[$a,za,Sa,Ds,Na,Fa,Ka,xa,Wa],oo=no({defaultModifiers:ao}),ro="tippy-box",js="tippy-content",io="tippy-backdrop",Ws="tippy-arrow",Bs="tippy-svg-arrow",nt={passive:!0,capture:!0},Vs=function(){return document.body};function Vt(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function as(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function Ns(e,s){return typeof e=="function"?e.apply(void 0,s):e}function ms(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function lo(e){return e.split(/\s+/).filter(Boolean)}function ft(e){return[].concat(e)}function bs(e,s){e.indexOf(s)===-1&&e.push(s)}function co(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function uo(e){return e.split("-")[0]}function Rt(e){return[].slice.call(e)}function ws(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Et(){return document.createElement("div")}function Ft(e){return["Element","Fragment"].some(function(s){return as(e,s)})}function po(e){return as(e,"NodeList")}function fo(e){return as(e,"MouseEvent")}function ho(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function vo(e){return Ft(e)?[e]:po(e)?Rt(e):Array.isArray(e)?e:Rt(document.querySelectorAll(e))}function Nt(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function ys(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function go(e){var s,t=ft(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function mo(e,s){var t=s.clientX,a=s.clientY;return e.every(function(i){var g=i.popperRect,y=i.popperState,S=i.props,b=S.interactiveBorder,x=uo(y.placement),$=y.modifiersData.offset;if(!$)return!0;var I=x==="bottom"?$.top.y:0,U=x==="top"?$.bottom.y:0,D=x==="right"?$.left.x:0,j=x==="left"?$.right.x:0,M=g.top-a+I>b,V=a-g.bottom-U>b,K=g.left-t+D>b,L=t-g.right-j>b;return M||V||K||L})}function Ht(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(i){e[a](i,t)})}function xs(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var Ve={isTouch:!1},Ts=0;function bo(){Ve.isTouch||(Ve.isTouch=!0,window.performance&&document.addEventListener("mousemove",Hs))}function Hs(){var e=performance.now();e-Ts<20&&(Ve.isTouch=!1,document.removeEventListener("mousemove",Hs)),Ts=e}function wo(){var e=document.activeElement;if(ho(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function yo(){document.addEventListener("touchstart",bo,nt),window.addEventListener("blur",wo)}var xo=typeof window<"u"&&typeof document<"u",To=xo?!!window.msCrypto:!1,Co={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},ko={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Pe=Object.assign({appendTo:Vs,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Co,ko),So=Object.keys(Pe),Eo=function(s){var t=Object.keys(s);t.forEach(function(a){Pe[a]=s[a]})};function zs(e){var s=e.plugins||[],t=s.reduce(function(a,i){var g=i.name,y=i.defaultValue;if(g){var S;a[g]=e[g]!==void 0?e[g]:(S=Pe[g])!=null?S:y}return a},{});return Object.assign({},e,t)}function $o(e,s){var t=s?Object.keys(zs(Object.assign({},Pe,{plugins:s}))):So,a=t.reduce(function(i,g){var y=(e.getAttribute("data-tippy-"+g)||"").trim();if(!y)return i;if(g==="content")i[g]=y;else try{i[g]=JSON.parse(y)}catch{i[g]=y}return i},{});return a}function Cs(e,s){var t=Object.assign({},s,{content:Ns(s.content,[e])},s.ignoreAttributes?{}:$o(e,s.plugins));return t.aria=Object.assign({},Pe.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Ao=function(){return"innerHTML"};function Xt(e,s){e[Ao()]=s}function ks(e){var s=Et();return e===!0?s.className=Ws:(s.className=Bs,Ft(e)?s.appendChild(e):Xt(s,e)),s}function Ss(e,s){Ft(s.content)?(Xt(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?Xt(e,s.content):e.textContent=s.content)}function Jt(e){var s=e.firstElementChild,t=Rt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(js)}),arrow:t.find(function(a){return a.classList.contains(Ws)||a.classList.contains(Bs)}),backdrop:t.find(function(a){return a.classList.contains(io)})}}function qs(e){var s=Et(),t=Et();t.className=ro,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=Et();a.className=js,a.setAttribute("data-state","hidden"),Ss(a,e.props),s.appendChild(t),t.appendChild(a),i(e.props,e.props);function i(g,y){var S=Jt(s),b=S.box,x=S.content,$=S.arrow;y.theme?b.setAttribute("data-theme",y.theme):b.removeAttribute("data-theme"),typeof y.animation=="string"?b.setAttribute("data-animation",y.animation):b.removeAttribute("data-animation"),y.inertia?b.setAttribute("data-inertia",""):b.removeAttribute("data-inertia"),b.style.maxWidth=typeof y.maxWidth=="number"?y.maxWidth+"px":y.maxWidth,y.role?b.setAttribute("role",y.role):b.removeAttribute("role"),(g.content!==y.content||g.allowHTML!==y.allowHTML)&&Ss(x,e.props),y.arrow?$?g.arrow!==y.arrow&&(b.removeChild($),b.appendChild(ks(y.arrow))):b.appendChild(ks(y.arrow)):$&&b.removeChild($)}return{popper:s,onUpdate:i}}qs.$$tippy=!0;var Mo=1,Pt=[],zt=[];function _o(e,s){var t=Cs(e,Object.assign({},Pe,zs(ws(s)))),a,i,g,y=!1,S=!1,b=!1,x=!1,$,I,U,D=[],j=ms(Re,t.interactiveDebounce),M,V=Mo++,K=null,L=co(t.plugins),N={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},h={id:V,reference:e,popper:Et(),popperInstance:K,props:t,state:N,plugins:L,clearDelayTimeouts:Ge,setProps:We,setContent:Be,show:et,hide:Ze,hideWithInteractivity:tt,enable:_e,disable:Je,unmount:it,destroy:bt};if(!t.render)return h;var X=t.render(h),_=X.popper,J=X.onUpdate;_.setAttribute("data-tippy-root",""),_.id="tippy-"+h.id,h.popper=_,e._tippy=h,_._tippy=h;var G=L.map(function(l){return l.fn(h)}),p=e.hasAttribute("aria-expanded");return Ue(),ce(),F(),ae("onCreate",[h]),t.showOnCreate&&je(),_.addEventListener("mouseenter",function(){h.props.interactive&&h.state.isVisible&&h.clearDelayTimeouts()}),_.addEventListener("mouseleave",function(){h.props.interactive&&h.props.trigger.indexOf("mouseenter")>=0&&A().addEventListener("mousemove",j)}),h;function w(){var l=h.props.touch;return Array.isArray(l)?l:[l,0]}function k(){return w()[0]==="hold"}function T(){var l;return!!((l=h.props.render)!=null&&l.$$tippy)}function E(){return M||e}function A(){var l=E().parentNode;return l?go(l):document}function O(){return Jt(_)}function C(l){return h.state.isMounted&&!h.state.isVisible||Ve.isTouch||$&&$.type==="focus"?0:Vt(h.props.delay,l?0:1,Pe.delay)}function F(l){l===void 0&&(l=!1),_.style.pointerEvents=h.props.interactive&&!l?"":"none",_.style.zIndex=""+h.props.zIndex}function ae(l,c,n){if(n===void 0&&(n=!0),G.forEach(function(r){r[l]&&r[l].apply(r,c)}),n){var o;(o=h.props)[l].apply(o,c)}}function re(){var l=h.props.aria;if(l.content){var c="aria-"+l.content,n=_.id,o=ft(h.props.triggerTarget||e);o.forEach(function(r){var f=r.getAttribute(c);if(h.state.isVisible)r.setAttribute(c,f?f+" "+n:n);else{var u=f&&f.replace(n,"").trim();u?r.setAttribute(c,u):r.removeAttribute(c)}})}}function ce(){if(!(p||!h.props.aria.expanded)){var l=ft(h.props.triggerTarget||e);l.forEach(function(c){h.props.interactive?c.setAttribute("aria-expanded",h.state.isVisible&&c===E()?"true":"false"):c.removeAttribute("aria-expanded")})}}function Ae(){A().removeEventListener("mousemove",j),Pt=Pt.filter(function(l){return l!==j})}function ve(l){if(!(Ve.isTouch&&(b||l.type==="mousedown"))){var c=l.composedPath&&l.composedPath()[0]||l.target;if(!(h.props.interactive&&xs(_,c))){if(ft(h.props.triggerTarget||e).some(function(n){return xs(n,c)})){if(Ve.isTouch||h.state.isVisible&&h.props.trigger.indexOf("click")>=0)return}else ae("onClickOutside",[h,l]);h.props.hideOnClick===!0&&(h.clearDelayTimeouts(),h.hide(),S=!0,setTimeout(function(){S=!1}),h.state.isMounted||ie())}}}function we(){b=!0}function W(){b=!1}function ue(){var l=A();l.addEventListener("mousedown",ve,!0),l.addEventListener("touchend",ve,nt),l.addEventListener("touchstart",W,nt),l.addEventListener("touchmove",we,nt)}function ie(){var l=A();l.removeEventListener("mousedown",ve,!0),l.removeEventListener("touchend",ve,nt),l.removeEventListener("touchstart",W,nt),l.removeEventListener("touchmove",we,nt)}function Ie(l,c){Ce(l,function(){!h.state.isVisible&&_.parentNode&&_.parentNode.contains(_)&&c()})}function Q(l,c){Ce(l,c)}function Ce(l,c){var n=O().box;function o(r){r.target===n&&(Ht(n,"remove",o),c())}if(l===0)return c();Ht(n,"remove",I),Ht(n,"add",o),I=o}function ye(l,c,n){n===void 0&&(n=!1);var o=ft(h.props.triggerTarget||e);o.forEach(function(r){r.addEventListener(l,c,n),D.push({node:r,eventType:l,handler:c,options:n})})}function Ue(){k()&&(ye("touchstart",de,{passive:!0}),ye("touchend",Le,{passive:!0})),lo(h.props.trigger).forEach(function(l){if(l!=="manual")switch(ye(l,de),l){case"mouseenter":ye("mouseleave",Le);break;case"focus":ye(To?"focusout":"blur",Fe);break;case"focusin":ye("focusout",Fe);break}})}function ze(){D.forEach(function(l){var c=l.node,n=l.eventType,o=l.handler,r=l.options;c.removeEventListener(n,o,r)}),D=[]}function de(l){var c,n=!1;if(!(!h.state.isEnabled||qe(l)||S)){var o=((c=$)==null?void 0:c.type)==="focus";$=l,M=l.currentTarget,ce(),!h.state.isVisible&&fo(l)&&Pt.forEach(function(r){return r(l)}),l.type==="click"&&(h.props.trigger.indexOf("mouseenter")<0||y)&&h.props.hideOnClick!==!1&&h.state.isVisible?n=!0:je(l),l.type==="click"&&(y=!n),n&&!o&&Me(l)}}function Re(l){var c=l.target,n=E().contains(c)||_.contains(c);if(!(l.type==="mousemove"&&n)){var o=xe().concat(_).map(function(r){var f,u=r._tippy,d=(f=u.popperInstance)==null?void 0:f.state;return d?{popperRect:r.getBoundingClientRect(),popperState:d,props:t}:null}).filter(Boolean);mo(o,l)&&(Ae(),Me(l))}}function Le(l){var c=qe(l)||h.props.trigger.indexOf("click")>=0&&y;if(!c){if(h.props.interactive){h.hideWithInteractivity(l);return}Me(l)}}function Fe(l){h.props.trigger.indexOf("focusin")<0&&l.target!==E()||h.props.interactive&&l.relatedTarget&&_.contains(l.relatedTarget)||Me(l)}function qe(l){return Ve.isTouch?k()!==l.type.indexOf("touch")>=0:!1}function Ke(){Xe();var l=h.props,c=l.popperOptions,n=l.placement,o=l.offset,r=l.getReferenceClientRect,f=l.moveTransition,u=T()?Jt(_).arrow:null,d=r?{getBoundingClientRect:r,contextElement:r.contextElement||E()}:e,m={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(H){var B=H.state;if(T()){var ee=O(),ne=ee.box;["placement","reference-hidden","escaped"].forEach(function(le){le==="placement"?ne.setAttribute("data-placement",B.placement):B.attributes.popper["data-popper-"+le]?ne.setAttribute("data-"+le,""):ne.removeAttribute("data-"+le)}),B.attributes.popper={}}}},P=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!f}},m];T()&&u&&P.push({name:"arrow",options:{element:u,padding:3}}),P.push.apply(P,(c==null?void 0:c.modifiers)||[]),h.popperInstance=oo(d,_,Object.assign({},c,{placement:n,onFirstUpdate:U,modifiers:P}))}function Xe(){h.popperInstance&&(h.popperInstance.destroy(),h.popperInstance=null)}function ge(){var l=h.props.appendTo,c,n=E();h.props.interactive&&l===Vs||l==="parent"?c=n.parentNode:c=Ns(l,[n]),c.contains(_)||c.appendChild(_),h.state.isMounted=!0,Ke()}function xe(){return Rt(_.querySelectorAll("[data-tippy-root]"))}function je(l){h.clearDelayTimeouts(),l&&ae("onTrigger",[h,l]),ue();var c=C(!0),n=w(),o=n[0],r=n[1];Ve.isTouch&&o==="hold"&&r&&(c=r),c?a=setTimeout(function(){h.show()},c):h.show()}function Me(l){if(h.clearDelayTimeouts(),ae("onUntrigger",[h,l]),!h.state.isVisible){ie();return}if(!(h.props.trigger.indexOf("mouseenter")>=0&&h.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(l.type)>=0&&y)){var c=C(!1);c?i=setTimeout(function(){h.state.isVisible&&h.hide()},c):g=requestAnimationFrame(function(){h.hide()})}}function _e(){h.state.isEnabled=!0}function Je(){h.hide(),h.state.isEnabled=!1}function Ge(){clearTimeout(a),clearTimeout(i),cancelAnimationFrame(g)}function We(l){if(!h.state.isDestroyed){ae("onBeforeUpdate",[h,l]),ze();var c=h.props,n=Cs(e,Object.assign({},c,ws(l),{ignoreAttributes:!0}));h.props=n,Ue(),c.interactiveDebounce!==n.interactiveDebounce&&(Ae(),j=ms(Re,n.interactiveDebounce)),c.triggerTarget&&!n.triggerTarget?ft(c.triggerTarget).forEach(function(o){o.removeAttribute("aria-expanded")}):n.triggerTarget&&e.removeAttribute("aria-expanded"),ce(),F(),J&&J(c,n),h.popperInstance&&(Ke(),xe().forEach(function(o){requestAnimationFrame(o._tippy.popperInstance.forceUpdate)})),ae("onAfterUpdate",[h,l])}}function Be(l){h.setProps({content:l})}function et(){var l=h.state.isVisible,c=h.state.isDestroyed,n=!h.state.isEnabled,o=Ve.isTouch&&!h.props.touch,r=Vt(h.props.duration,0,Pe.duration);if(!(l||c||n||o)&&!E().hasAttribute("disabled")&&(ae("onShow",[h],!1),h.props.onShow(h)!==!1)){if(h.state.isVisible=!0,T()&&(_.style.visibility="visible"),F(),ue(),h.state.isMounted||(_.style.transition="none"),T()){var f=O(),u=f.box,d=f.content;Nt([u,d],0)}U=function(){var P;if(!(!h.state.isVisible||x)){if(x=!0,_.offsetHeight,_.style.transition=h.props.moveTransition,T()&&h.props.animation){var R=O(),H=R.box,B=R.content;Nt([H,B],r),ys([H,B],"visible")}re(),ce(),bs(zt,h),(P=h.popperInstance)==null||P.forceUpdate(),ae("onMount",[h]),h.props.animation&&T()&&Q(r,function(){h.state.isShown=!0,ae("onShown",[h])})}},ge()}}function Ze(){var l=!h.state.isVisible,c=h.state.isDestroyed,n=!h.state.isEnabled,o=Vt(h.props.duration,1,Pe.duration);if(!(l||c||n)&&(ae("onHide",[h],!1),h.props.onHide(h)!==!1)){if(h.state.isVisible=!1,h.state.isShown=!1,x=!1,y=!1,T()&&(_.style.visibility="hidden"),Ae(),ie(),F(!0),T()){var r=O(),f=r.box,u=r.content;h.props.animation&&(Nt([f,u],o),ys([f,u],"hidden"))}re(),ce(),h.props.animation?T()&&Ie(o,h.unmount):h.unmount()}}function tt(l){A().addEventListener("mousemove",j),bs(Pt,j),j(l)}function it(){h.state.isVisible&&h.hide(),h.state.isMounted&&(Xe(),xe().forEach(function(l){l._tippy.unmount()}),_.parentNode&&_.parentNode.removeChild(_),zt=zt.filter(function(l){return l!==h}),h.state.isMounted=!1,ae("onHidden",[h]))}function bt(){h.state.isDestroyed||(h.clearDelayTimeouts(),h.unmount(),ze(),delete e._tippy,h.state.isDestroyed=!0,ae("onDestroy",[h]))}}function ot(e,s){s===void 0&&(s={});var t=Pe.plugins.concat(s.plugins||[]);yo();var a=Object.assign({},s,{plugins:t}),i=vo(e),g=i.reduce(function(y,S){var b=S&&_o(S,a);return b&&y.push(b),y},[]);return Ft(e)?g[0]:g}ot.defaultProps=Pe;ot.setDefaultProps=Eo;ot.currentInput=Ve;Object.assign({},Ds,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});ot.setDefaultProps({render:qs});const Do={class:"task-pane"},Oo={key:0,class:"loading-overlay"},Po={key:1,class:"format-error-overlay"},Io={class:"format-error-content"},Uo={class:"format-error-message"},Ro={class:"format-error-actions"},Lo={class:"doc-header"},Fo={class:"doc-title"},jo={class:"action-area"},Wo={class:"select-container"},Bo={class:"select-group"},Vo=["disabled"],No=["value"],Ho={class:"select-group"},zo=["disabled"],qo=["value"],Yo=["title"],Ko={key:0,class:"science-warning"},Xo={class:"action-buttons"},Jo=["disabled"],Go={class:"btn-content"},Zo={key:0,class:"button-loader"},Qo=["disabled"],er={class:"btn-content"},tr={key:0,class:"button-loader"},sr=["disabled"],nr={class:"content-area"},ar={class:"modal-header"},or={class:"modal-body"},rr={class:"selection-content"},ir={class:"modal-header"},lr={class:"modal-body"},cr={class:"alert-message"},ur={class:"alert-actions"},dr={key:2,class:"modal-overlay"},pr={class:"modal-header"},fr={class:"modal-body"},hr={class:"confirm-message"},vr={class:"confirm-actions"},gr={class:"task-queue"},mr={class:"queue-header"},br={class:"queue-status-filter"},wr=["value"],yr={class:"queue-actions"},xr=["disabled","title"],Tr={class:"task-count"},Cr={key:0,class:"queue-table-container"},kr={class:"col-id"},Sr={class:"id-header"},Er={key:0,class:"col-subject"},$r={class:"subject-header"},Ar={class:"switch"},Mr=["title"],_r={key:1,class:"col-status"},Dr=["onClick"],Or={class:"col-id"},Pr={class:"id-content"},Ir={class:"task-id"},Ur={key:0,class:"enhance-svg-icon"},Rr={key:0,class:"status-in-id"},Lr={key:0,class:"col-subject"},Fr=["onMouseenter"],jr={key:1,class:"col-status"},Wr={class:"status-cell"},Br={class:"col-actions"},Vr={class:"task-actions"},Nr=["onClick"],Hr=["onClick"],zr={key:2,class:"no-action-icon",title:"无可用操作"},qr={key:1,class:"empty-queue"},Yr={key:3,class:"log-container"},Kr={class:"log-actions"},Xr={class:"toggle-icon"},Jr=["innerHTML"],Gr={__name:"TaskPane",setup(e){const s=se(!1),t=se(!1),a=se(!1),i=se(""),g=se(!1),y=se(!1),S=se(!1),b=se(!0),x=se(""),$=se(!1),I=se(window.innerWidth),U=ut(()=>I.value<750),D=ut(()=>I.value<380),j=()=>{I.value=window.innerWidth},M=se(null),V=se(!1),K=se(""),L={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},N=se(!1),h=se([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"}]),{docName:X,selected:_,logger:J,map:G,subject:p,stage:w,subjectOptions:k,stageOptions:T,appConfig:E,clearLog:A,checkDocumentFormat:O,getTaskStatusClass:C,getTaskStatusText:F,terminateTask:ae,run1:re,setupLifecycle:ce,navigateToTaskControl:Ae,isLoading:ve,tryRemoveTaskPlaceholderWithLoading:we,confirmDialog:W,handleConfirm:ue,getCompletedTasksCount:ie,showConfirm:Ie}=pn(),Q=se(null);(()=>{var l;try{if((l=window.Application)!=null&&l.PluginStorage){const c=window.Application.PluginStorage.getItem("user_info");c?(Q.value=JSON.parse(c),console.log("用户信息已加载:",Q.value)):console.log("未找到用户信息")}}catch(c){console.error("解析用户信息时出错:",c)}})();const ye=ut(()=>!Q.value||Q.value.isAdmin||Q.value.isOwner?k:Q.value.subject?k.filter(l=>l.value===Q.value.subject):k),Ue=()=>{Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject&&(p.value=Q.value.subject)},ze=ut(()=>["physics","chemistry","biology","math"].includes(p.value));qt(E,l=>{l&&(console.log("TaskPane组件收到应用配置更新:",l),console.log("当前版本类型:",l.EDITION),console.log("当前年级选项:",T.value))},{deep:!0,immediate:!0});const de=()=>{try{const l=O();b.value=l.isValid,x.value=l.message,$.value=!l.isValid,l.isValid||console.warn("文档格式检查失败:",l.message)}catch(l){console.error("执行文档格式检查时出错:",l),b.value=!1,x.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",$.value=!0}},Re=ut(()=>{let l={};const c=G;if(K.value==="")l={...c};else for(const n in c)if(Object.prototype.hasOwnProperty.call(c,n)){const o=c[n];o.status===K.value&&(l[n]=o)}return V.value&&M.value?l[M.value]?{[M.value]:l[M.value]}:{}:l}),Le=ut(()=>{const l=Re.value;return Object.entries(l).map(([n,o])=>({tid:n,...o})).sort((n,o)=>{const r=n.startTime||0;return(o.startTime||0)-r}).reduce((n,o)=>{const{tid:r,...f}=o;return n[r]=f,n},{})}),Fe=(l="wps-analysis")=>{p.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(i.value="未选中内容",t.value=!0):(l==="wps-analysis"?y.value=!0:l==="wps-enhance_analysis"&&(S.value=!0),re(l).catch(c=>{console.log(c),c.message.includes("重叠")?(i.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",c),i.value=c.message,t.value=!0)}).finally(()=>{l==="wps-analysis"?y.value=!1:l==="wps-enhance_analysis"&&(S.value=!1)})):(i.value="请选择学科",t.value=!0)},qe=()=>{i.value="功能开发中……敬请期待",t.value=!0},Ke=(l,c)=>{M.value=l,Ae(l)},Xe=l=>{G[l]&&(G[l].status=3),M.value===l&&(M.value=null),we(l,!0)},ge=async()=>{const l=Object.entries(G).filter(([c,n])=>n.status===2);if(l.length===0){i.value="没有已完成的任务可释放",t.value=!0;return}try{if(await Ie(`确定要释放所有 ${l.length} 个已完成的任务吗？
此操作不可撤销。`)){let n=0;l.forEach(([o,r])=>{G[o]&&(G[o].status=3,M.value===o&&(M.value=null),we(o,!0),n++)}),i.value=`已成功释放 ${n} 个任务`,t.value=!0}}catch(c){console.error("释放任务时出错:",c),i.value="释放任务时出现错误",t.value=!0}},xe=()=>{g.value=!g.value},je=l=>l?l.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Me=l=>{const c=_e(l);return c?je(c):"无题目内容"},_e=l=>{if(!l.selectedText)return"";const c=l.selectedText.split("\r").filter(o=>o.trim());if(c.length===1){const o=l.selectedText.split(`
`).filter(r=>r.trim());o.length>1&&c.splice(0,1,...o)}const n=c.map((o,r)=>{const f=o.trim();return f.length>200,f});return n.length===1?c[0].trim():n.join(`
`)},Je=(l,c)=>{if(!N.value)return;const n=l.target,o=_e(c).toString();if(!o||o.trim()===""){console.log("题目内容为空，不显示tooltip");return}const r=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${o.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,f=l.clientX,u=l.clientY;if(L.subjects.has(n)){const m=L.subjects.get(n);m.setContent(r),m.setProps({getReferenceClientRect:()=>({width:0,height:0,top:u,bottom:u,left:f,right:f})}),m.show();return}const d=ot(n,{content:r,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:u,bottom:u,left:f,right:f}),onHidden:()=>{d.setProps({getReferenceClientRect:null})}});L.subjects.set(n,d),d.show()},Ge=l=>{const c=l.currentTarget,n=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,o=l.clientX,r=l.clientY;if(L.enhance.has(c)){const u=L.enhance.get(c);u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:r,bottom:r,left:o,right:o})}),u.show();return}const f=ot(c,{content:n,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});L.enhance.set(c,f),f.show()},We=()=>{L.subjects.forEach(l=>{l.destroy()}),L.subjects.clear(),L.enhance.forEach(l=>{l.destroy()}),L.enhance.clear(),L.softBreak.forEach(l=>{l.destroy()}),L.softBreak.clear(),document.removeEventListener("click",Be),document.removeEventListener("mousemove",Ze)},Be=l=>{const c=document.querySelector(".tippy-box");c&&!c.contains(l.target)&&(L.subjects.forEach(n=>n.hide()),L.enhance.forEach(n=>n.hide()),L.softBreak.forEach(n=>n.hide()))};let et=0;const Ze=l=>{const c=Date.now();if(c-et<100)return;et=c;const n=document.querySelector(".tippy-box");if(!n)return;const o=n.getBoundingClientRect();!(l.clientX>=o.left-20&&l.clientX<=o.right+20&&l.clientY>=o.top-20&&l.clientY<=o.bottom+20)&&!n.matches(":hover")&&(L.subjects.forEach(f=>f.hide()),L.enhance.forEach(f=>f.hide()),L.softBreak.forEach(f=>f.hide()))},tt=()=>{document.addEventListener("click",Be),document.addEventListener("mousemove",Ze)};Es(()=>{window.addEventListener("resize",j),tt(),Ue(),setTimeout(()=>{de()},500);const l=document.createElement("style");l.id="tippy-custom-styles",l.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(l)}),Gs(()=>{window.removeEventListener("resize",j),We();const l=document.getElementById("tippy-custom-styles");l&&l.remove()}),ce();const it=l=>l.selectedText?l.selectedText.includes("\v")||l.selectedText.includes("\v"):!1,bt=l=>{const c=l.currentTarget,n=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,o=l.clientX,r=l.clientY;if(L.softBreak.has(c)){const u=L.softBreak.get(c);u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:r,bottom:r,left:o,right:o})}),u.show();return}const f=ot(c,{content:n,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});L.softBreak.set(c,f),f.show()};return(l,c)=>{var n,o,r,f,u;return q(),Y("div",Do,[oe(ve)?(q(),Y("div",Oo,c[26]||(c[26]=[v("div",{class:"loading-spinner"},null,-1),v("div",{class:"loading-text"},"处理中...",-1)]))):Z("",!0),$.value?(q(),Y("div",Po,[v("div",Io,[c[27]||(c[27]=v("div",{class:"format-error-icon"},"⚠️",-1)),c[28]||(c[28]=v("div",{class:"format-error-title"},"文档格式不支持",-1)),v("div",Uo,te(x.value),1),v("div",Ro,[v("button",{class:"retry-check-btn",onClick:c[0]||(c[0]=d=>de())},"重新检查")])])])):Z("",!0),v("div",Lo,[v("div",Fo,te(oe(X)||"未选择文档"),1),v("button",{class:"settings-btn",onClick:c[1]||(c[1]=d=>a.value=!0)},c[29]||(c[29]=[v("i",{class:"icon-settings"},null,-1)]))]),v("div",jo,[v("div",Wo,[v("div",Bo,[c[30]||(c[30]=v("label",{for:"stage-select"},"年级:",-1)),De(v("select",{id:"stage-select","onUpdate:modelValue":c[2]||(c[2]=d=>os(w)?w.value=d:null),class:"select-input",disabled:$.value},[(q(!0),Y(xt,null,Tt(oe(T),d=>(q(),Y("option",{key:d.value,value:d.value},te(d.label),9,No))),128))],8,Vo),[[Bt,oe(w)]])]),v("div",Ho,[c[31]||(c[31]=v("label",{for:"subject-select"},"学科:",-1)),De(v("select",{id:"subject-select","onUpdate:modelValue":c[3]||(c[3]=d=>os(p)?p.value=d:null),class:"select-input",disabled:$.value},[(q(!0),Y(xt,null,Tt(ye.value,d=>(q(),Y("option",{key:d.value,value:d.value},te(d.label),9,qo))),128))],8,zo),[[Bt,oe(p)]]),Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject?(q(),Y("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((n=ye.value.find(d=>d.value===Q.value.subject))==null?void 0:n.label)||Q.value.subject} 学科`}," 🔒 ",8,Yo)):Z("",!0)])]),ze.value?(q(),Y("div",Ko," 理科可使用增强模式以获取更精准的解析 ")):Z("",!0),v("div",Xo,[v("button",{class:"action-btn primary",onClick:c[4]||(c[4]=d=>Fe("wps-analysis")),disabled:y.value||$.value},[v("div",Go,[y.value?(q(),Y("span",Zo)):Z("",!0),c[32]||(c[32]=v("span",{class:"btn-text"},"解析",-1))])],8,Jo),ze.value?(q(),Y("button",{key:0,class:"action-btn enhance",onClick:c[5]||(c[5]=d=>Fe("wps-enhance_analysis")),disabled:S.value||$.value},[v("div",er,[S.value?(q(),Y("span",tr)):Z("",!0),c[33]||(c[33]=v("span",{class:"btn-text"},"增强解析",-1))])],8,Qo)):Z("",!0),((r=(o=Q.value)==null?void 0:o.orgs[0])==null?void 0:r.orgId)===2?(q(),Y("button",{key:1,class:"action-btn secondary",onClick:c[6]||(c[6]=d=>qe()),disabled:$.value},c[34]||(c[34]=[v("div",{class:"btn-content"},[v("span",{class:"btn-text"},"校对")],-1)]),8,sr)):Z("",!0)])]),v("div",nr,[s.value?(q(),Y("div",{key:0,class:"modal-overlay",onClick:c[9]||(c[9]=d=>s.value=!1)},[v("div",{class:"modal-content",onClick:c[8]||(c[8]=st(()=>{},["stop"]))},[v("div",ar,[c[35]||(c[35]=v("div",{class:"modal-title"},"选中内容",-1)),v("button",{class:"modal-close",onClick:c[7]||(c[7]=d=>s.value=!1)},"×")]),v("div",or,[v("pre",rr,te(oe(_)||"未选中内容"),1)])])])):Z("",!0),t.value?(q(),Y("div",{key:1,class:"modal-overlay",onClick:c[13]||(c[13]=d=>t.value=!1)},[v("div",{class:"modal-content alert-modal",onClick:c[12]||(c[12]=st(()=>{},["stop"]))},[v("div",ir,[c[36]||(c[36]=v("div",{class:"modal-title"},"提示",-1)),v("button",{class:"modal-close",onClick:c[10]||(c[10]=d=>t.value=!1)},"×")]),v("div",lr,[v("div",cr,te(i.value),1),v("div",ur,[v("button",{class:"action-btn primary",onClick:c[11]||(c[11]=d=>t.value=!1)},"确定")])])])])):Z("",!0),oe(W).show?(q(),Y("div",dr,[v("div",{class:"modal-content confirm-modal",onClick:c[17]||(c[17]=st(()=>{},["stop"]))},[v("div",pr,[c[37]||(c[37]=v("div",{class:"modal-title"},"确认",-1)),v("button",{class:"modal-close",onClick:c[14]||(c[14]=d=>oe(ue)(!1))},"×")]),v("div",fr,[v("div",hr,te(oe(W).message),1),v("div",vr,[v("button",{class:"action-btn secondary",onClick:c[15]||(c[15]=d=>oe(ue)(!1))},"取消"),v("button",{class:"action-btn primary",onClick:c[16]||(c[16]=d=>oe(ue)(!0))},"确定")])])])])):Z("",!0),v("div",gr,[v("div",mr,[c[38]||(c[38]=v("div",{class:"queue-title"},"任务队列",-1)),v("div",br,[De(v("select",{id:"status-filter-select","onUpdate:modelValue":c[18]||(c[18]=d=>K.value=d),class:"status-filter-select-input"},[(q(!0),Y(xt,null,Tt(h.value,d=>(q(),Y("option",{key:d.value,value:d.value},te(d.label),9,wr))),128))],512),[[Bt,K.value]])]),v("div",yr,[v("button",{class:"release-all-btn",onClick:ge,disabled:oe(ie)()===0,title:oe(ie)()===0?"无已完成任务可释放":`释放所有${oe(ie)()}个已完成任务`}," 一键释放 ",8,xr)]),v("div",Tr,te(Object.keys(Re.value).length)+"个任务",1)]),Object.keys(Re.value).length>0?(q(),Y("div",Cr,[v("table",{class:ke(["queue-table",{"narrow-view":U.value,"ultra-narrow-view":D.value}])},[v("thead",null,[v("tr",null,[v("th",kr,[v("div",Sr,[c[40]||(c[40]=v("span",null,"任务ID",-1)),v("span",{class:"help-icon",onMouseenter:c[19]||(c[19]=d=>Ge(d))},c[39]||(c[39]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[v("circle",{cx:"12",cy:"12",r:"10"}),v("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),v("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),U.value?Z("",!0):(q(),Y("th",Er,[v("div",$r,[c[41]||(c[41]=v("span",null,"题目内容",-1)),v("label",Ar,[De(v("input",{type:"checkbox","onUpdate:modelValue":c[20]||(c[20]=d=>N.value=d)},null,512),[[Zs,N.value]]),v("span",{class:"slider round",title:N.value?"关闭题目预览":"开启题目预览"},null,8,Mr)])])])),D.value?Z("",!0):(q(),Y("th",_r,"状态")),c[42]||(c[42]=v("th",{class:"col-actions"},"操作",-1))])]),v("tbody",null,[(q(!0),Y(xt,null,Tt(Le.value,(d,m)=>(q(),Y("tr",{key:m,class:ke(["task-row",[oe(C)(d.status),{"selected-task-row":m===M.value}]]),onClick:P=>Ke(m)},[v("td",Or,[v("div",{class:ke(["id-cell",{"id-with-status":D.value}])},[v("div",Pr,[v("span",Ir,te(m.substring(0,8)),1),d.wordType==="wps-enhance_analysis"||d.isEnhanced?(q(),Y("span",Ur,c[43]||(c[43]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[v("title",null,"增强模式"),v("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):Z("",!0),it(d)?(q(),Y("span",{key:1,class:"soft-break-warning-icon",onMouseenter:c[21]||(c[21]=P=>bt(P))},c[44]||(c[44]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[v("title",null,"提示"),v("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),v("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),v("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):Z("",!0)]),D.value?(q(),Y("div",Rr,[v("span",{class:ke(["task-tag compact",oe(C)(d.status)])},te(oe(F)(d.status)),3)])):Z("",!0)],2)]),U.value?Z("",!0):(q(),Y("td",Lr,[v("div",{class:"subject-cell",onMouseenter:P=>Je(P,d)},te(Me(d)),41,Fr)])),D.value?Z("",!0):(q(),Y("td",jr,[v("div",Wr,[v("span",{class:ke(["task-tag",oe(C)(d.status)])},te(oe(F)(d.status)),3)])])),v("td",Br,[v("div",Vr,[d.status===1?(q(),Y("button",{key:0,onClick:st(P=>oe(ae)(m),["stop"]),class:"terminate-btn"}," 终止 ",8,Nr)):Z("",!0),d.status===2?(q(),Y("button",{key:1,onClick:st(P=>Xe(m),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Hr)):Z("",!0),d.status!==1&&d.status!==2?(q(),Y("span",zr,c[45]||(c[45]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[v("circle",{cx:"12",cy:"12",r:"10"}),v("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),v("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):Z("",!0)])])],10,Dr))),128))])],2)])):(q(),Y("div",qr,c[46]||(c[46]=[v("div",{class:"empty-text"},"暂无任务",-1)])))]),((u=(f=Q.value)==null?void 0:f.orgs[0])==null?void 0:u.orgId)===2?(q(),Y("div",Yr,[v("div",{class:"log-header",onClick:xe},[c[47]||(c[47]=v("div",{class:"log-title"},"执行日志",-1)),v("div",Kr,[v("button",{class:"clear-btn",onClick:c[22]||(c[22]=st(d=>oe(A)(),["stop"]))},"清空日志"),v("span",Xr,te(g.value?"▼":"▶"),1)])]),g.value?(q(),Y("div",{key:0,class:"log-content",innerHTML:oe(J)},null,8,Jr)):Z("",!0)])):Z("",!0)]),a.value?(q(),Y("div",{key:2,class:"modal-overlay",onClick:c[25]||(c[25]=d=>a.value=!1)},[v("div",{class:"modal-content",onClick:c[24]||(c[24]=st(()=>{},["stop"]))},[Qs(ea,{onClose:c[23]||(c[23]=d=>a.value=!1)})])])):Z("",!0)])}}},Qr=$s(Gr,[["__scopeId","data-v-e69ce6c3"]]);export{Qr as default};
