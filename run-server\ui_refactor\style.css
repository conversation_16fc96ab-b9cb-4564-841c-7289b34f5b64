* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

body {
    background-color: #f0f2f5;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    color: #333;
}

/* 自定义标题栏样式 */
.title-bar {
    height: 36px;
    background-color: #229a52;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    -webkit-app-region: drag; /* Allows dragging the window */
    color: white;
    font-size: 13px;
}

.title-bar .title {
    font-weight: 600;
}

.title-bar .controls {
    display: flex;
    -webkit-app-region: no-drag; /* Prevents dragging on controls */
}

.title-bar .controls button {
    width: 28px;
    height: 28px;
    background-color: transparent;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    margin-left: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.title-bar .controls button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.title-bar .controls .close-button:hover {
    background-color: #e81123; /* Red for close button hover */
}

.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.header { /* This class seems unused in the provided HTML, but kept for potential future use */
    text-align: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.header h1 { /* This class seems unused in the provided HTML */
    font-size: 18px;
    color: #333;
    margin: 0;
}

.panel-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 15px; /* Adjusted padding */
}

.login-form,
.status-panel {
    display: none; /* Initially hidden, controlled by JS */
    flex-direction: column;
}

.login-form {
    gap: 15px;
}

.status-panel {
    flex: 1;
    justify-content: center;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

label {
    font-size: 13px;
    color: #444;
    font-weight: 500;
}

input {
    height: 36px;
    padding: 0 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

input:focus {
    border-color: #229a52; /* Highlight color on focus */
    outline: none;
}

.button-container {
    margin-top: 5px;
}

button {
    height: 36px;
    width: 100%;
    background-color: #229a52; /* Primary button color */
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, opacity 0.2s;
}

button:disabled {
    background-color: #9fd8b8; /* Lighter green when disabled */
    cursor: not-allowed;
    opacity: 0.7;
}

button:hover:not(:disabled) {
    background-color: #1cc660; /* Darker green on hover */
}

button.secondary {
    background-color: #6c757d; /* Secondary button color */
    margin-top: 10px;
}

button.secondary:hover:not(:disabled) {
    background-color: #5a6268; /* Darker secondary on hover */
}

button.button-danger {
    background-color: #dc3545; /* Danger button color */
    margin-top: 10px;
}

button.button-danger:hover:not(:disabled) {
    background-color: #c82333; /* Darker danger on hover */
}

.error-message {
    color: #dc3545; /* Error message color */
    font-size: 13px;
    text-align: center;
    display: none; /* Initially hidden */
    margin-top: 10px;
    min-height: 18px;
}

/* 加载指示器样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 15px;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(34, 154, 82, 0.2); /* Light green border */
    border-top-color: #229a52; /* Dark green for spinner animation */
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: #666;
    font-size: 14px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* WPS插件面板样式 */
.wps-addon-panel {
    display: flex;
    flex-direction: column;
    margin: 15px 0;
    background-color: #f8f9fa; /* Light background for panel */
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e0e0; /* Light border */
    gap: 10px;
}

.wps-addon-title { /* Unused in current HTML, kept for potential use */
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.wps-addon-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.wps-addon-name {
    font-weight: 500;
}

.wps-addon-status {
    padding: 4px 8px;
    border-radius: 50px; /* Pill shape */
    font-size: 12px;
    min-width: 60px;
    text-align: center;
}

.status-installed {
    color: #34a853; /* Green for installed */
    background-color: rgba(52, 168, 83, 0.1); /* Light green background */
    border: 1px solid rgba(52, 168, 83, 0.2); /* Light green border */
}

.status-not-installed {
    color: #ea4335; /* Red for not installed */
    background-color: rgba(234, 67, 53, 0.1); /* Light red background */
    border: 1px solid rgba(234, 67, 53, 0.2); /* Light red border */
}

.wps-addon-buttons {
    display: flex;
    gap: 10px;
}

.wps-addon-buttons button {
    flex: 1; /* Buttons share space equally */
}

/* 修复用户界面以匹配截图 */
html,
body {
    height: 360px; /* Fixed height */
    max-height: 360px;
    overflow: hidden; /* Prevent scrolling */
}

#user-greeting {
    margin-bottom: 10px !important; /* Ensure margin is applied */
}

#logout-button {
    margin-top: 15px;
}

/* 权限检查进度条面板样式 */
.permission-check-container {
    /* styles already defined inline in HTML, but can be moved here if preferred */
}
.permission-progress-info {
    /* styles already defined inline in HTML */
}
.permission-progress-bar {
    /* styles already defined inline in HTML */
}
.permission-check-status {
    /* styles already defined inline in HTML */
}


/* 网络文件夹选择面板样式 */
.network-folder-container {
    display: none; /* Initially hidden */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 15px;
    padding: 5px; /* Adjusted padding */
}

.network-folder-header {
    text-align: center;
    margin-bottom: 15px;
}

.network-folder-header h3 {
    color: #e67e22; /* Orange color for warning */
    margin-bottom: 10px;
}

.network-folder-header p {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.current-path-info {
    width: 100%;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.current-path-info div:first-child {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.current-path-info div:last-child {
    font-size: 13px;
    color: #333;
    word-break: break-all; /* Prevent long paths from breaking layout */
}

.network-folder-buttons {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.network-folder-buttons button {
    background-color: #e67e22; /* Orange for select button */
}

/* Confirm button has specific inline style for green, can be a class too */
/* #confirm-network-folder-btn { background-color: #28a745; } */


.network-folder-message {
    display: none; /* Initially hidden */
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
}

.network-folder-help { /* Unused in current HTML, kept for potential use */
    margin-top: 15px;
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 1.3;
}
