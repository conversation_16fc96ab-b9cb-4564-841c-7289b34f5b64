<template>
  <div class="app">
    <div v-if="showWSWarning" class="ws-warning-overlay">
      <div class="ws-warning-content">
        <h3>连接失败</h3>
        <p v-if="connectionErrors.ws">WebSocket 连接失败</p>
        <div class="error-details" v-if="connectionErrors.http || connectionErrors.ws">
          <p v-if="connectionErrors.ws">{{ connectionErrors.wsMessage }}</p>
        </div>
        <p>请确保 {{ appDisplayName }} 已启动并正常运行。</p>
        <button @click="tryReconnect" class="reconnect-btn" :disabled="isReconnecting">
          <span v-if="isReconnecting" class="loading-icon"></span>
          {{ isReconnecting ? '连接中...' : '重新连接' }}
        </button>
      </div>
    </div>

    <NotificationContainer />

    <RouterView />

    <DevToolsButton />
  </div>
</template>

<script setup>
import { inject, onMounted, onUnmounted, reactive, ref } from 'vue'
import ribbon from './components/ribbon.js'
import { checkLoginStatus, checkServerConnectivity, setupAuthEvents } from './components/js/auth'
import NotificationContainer from './components/NotificationContainer.vue'
import DevToolsButton from './components/DevToolsButton.vue'
import logger from './components/js/logger'
import wsClient from './components/js/wsClient'
import versionManager from './components/js/versionManager'
import Util from './components/js/util'

const message = ref('当你看到这句话的时候，说明你已经成功安装了wps加载项，但是你访问的路径不对。')
const showWSWarning = ref(false)
const reconnectTimer = ref(null)
const isReconnecting = ref(false)

// 添加连接错误详情
const connectionErrors = reactive({
  http: false,
  ws: false,
  httpMessage: '',
  wsMessage: ''
})

// 从依赖注入系统获取通知功能
const notification = inject('notification')

// 设置认证事件监听
let removeAuthListener = null
let removeVersionListener = null

// 添加文档相关的响应式变量
const docName = ref('')
const currentDocId = ref(null)

// 版本相关的响应式变量
const appDisplayName = ref('万唯AI编辑WPS插件服务') // 默认万唯版本
const versionConfig = ref(versionManager.getVersionConfig())

// 更新文档信息的方法
const updateDocInfo = () => {
  try {
    const app = window.Application
    if (app && app.ActiveDocument) {
      docName.value = app.ActiveDocument.Name
      currentDocId.value = app.ActiveDocument.DocID
      logger.info('文档已切换', { docId: currentDocId.value, name: docName.value })
    } else {
      docName.value = ''
      currentDocId.value = null
    }
  } catch (error) {
    logger.error('获取文档信息失败', error)
    docName.value = ''
    currentDocId.value = null
  }
}

// 尝试建立WebSocket连接
const connectWebSocket = async () => {
  if (wsClient.isConnected) {
    return
  }

  // 清除之前的重连定时器
  if (reconnectTimer.value) {
    clearTimeout(reconnectTimer.value)
  }

  try {
    // 使用 wsClient 连接
    const connected = await wsClient.connect()

    if (connected) {
      logger.success('WebSocket连接成功')

      // 清除WebSocket错误状态
      connectionErrors.ws = false
      connectionErrors.wsMessage = ''

      // 只有当HTTP也没有错误时才隐藏警告
      if (!connectionErrors.http) {
        showWSWarning.value = false
      }

      // 重连后检查登录状态
      try {
        const isLoggedIn = await checkLoginStatus()

        // 如果未登录且不在登录页面，则重定向到登录页面
        if (!isLoggedIn && window.location.hash !== '#/login') {
          window.location.hash = '#/login'
        }
        // 如果已登录，智能跳转到对应的窗格
        else if (isLoggedIn) {
          Util.redirectToCurrentPane('/taskpane')
          // 显示登录成功通知
          notification.showNotify('登录成功', 'success')
        }
      } catch (error) {
        // 处理 AI 编辑功能未开启的错误
        if (error.message && error.message.startsWith('AI_EDIT_DISABLED:')) {
          const message = error.message.replace('AI_EDIT_DISABLED:', '')
          logger.warn('AI编辑功能未开启', message)
          notification.showNotify(message, 'warning')
          // 重定向到登录页面
          window.location.hash = '#/login'
        } else {
          logger.error('检查登录状态时出错', error)
          // 其他错误也重定向到登录页面
          window.location.hash = '#/login'
        }
      }
    } else {
      // 连接失败
      logger.error('WebSocket连接失败')

      // 设置WebSocket错误状态
      connectionErrors.ws = true
      connectionErrors.wsMessage = 'WebSocket连接失败'
      showWSWarning.value = true

      // 5秒后尝试重连
      reconnectTimer.value = setTimeout(() => {
        connectWebSocket()
      }, 5000)
    }
  } catch (error) {
    logger.error('创建WebSocket连接时出错', error)

    // 设置WebSocket错误状态
    connectionErrors.ws = true
    connectionErrors.wsMessage = error.message || '创建WebSocket连接时出错'
    showWSWarning.value = true

    // 发生错误时也尝试重连
    reconnectTimer.value = setTimeout(() => {
      connectWebSocket()
    }, 5000)
  }
}

// 尝试重新连接
const tryReconnect = async () => {
  isReconnecting.value = true

  // 确保loading状态至少持续300ms
  const startTime = Date.now()

  // 测试连接
  try {
    const connectivityResult = await checkServerConnectivity()

    // 更新HTTP状态
    connectionErrors.http = !connectivityResult.httpCheck.success
    connectionErrors.httpMessage = connectivityResult.httpCheck.error || ''

    // 更新WebSocket状态
    connectionErrors.ws = !connectivityResult.wsCheck.success
    connectionErrors.wsMessage = connectivityResult.wsCheck.error || ''

    // 只有当两者都成功时，才隐藏警告
    showWSWarning.value = connectionErrors.http || connectionErrors.ws

    if (connectivityResult.success) {
      logger.success('连接测试成功', connectivityResult)
    } else {
      logger.error('连接测试失败', connectivityResult)
    }
  } catch (error) {
    logger.error('连接测试出错', error)
    showWSWarning.value = true
  }

  // 使用setTimeout确保loading状态至少持续300ms
  setTimeout(() => {
    // 计算已经过去的时间，如果不足300ms，则等待剩余时间
    const elapsedTime = Date.now() - startTime
    const remainingTime = Math.max(0, 300 - elapsedTime)

    setTimeout(() => {
      isReconnecting.value = false
    }, remainingTime)
  }, 0)
}

const setupDocumentWatch = () => {
  // console.log(window.Application)
  // window.Application.ApiEvent.AddApiEventListener("DocumentOpen", function(doc) {
  //   // 新增一个 taksPane
  //   // Application.CreateTaskpane
  //   const isProd = import.meta.env.PROD
  //   taskpane = window.Application.CreateTaskPane((isProd ? 'http://wwwps.hexinedu.com/wps-addon-build' : Util.GetUrlPath())  + Util.GetRouterHash() + '/taskpane')
  // })
}

onMounted(async () => {
  window.ribbon = ribbon

  // 设置版本信息监听
  removeVersionListener = versionManager.onVersionChange((versionInfo) => {
    logger.info('版本信息已更新，更新UI显示', versionInfo);
    const config = versionManager.getVersionConfig();
    versionConfig.value = config;
    appDisplayName.value = config.appName;
  });

  // 设置WebSocket认证事件监听
  removeAuthListener = setupAuthEvents()

  // 添加WebSocket连接状态监听
  wsClient.addEventListener('connection', (data) => {
    if (data.status === 'connected') {
      // 清除WebSocket错误状态
      connectionErrors.ws = false
      connectionErrors.wsMessage = ''

      // 只有当HTTP也没有错误时才隐藏警告
      if (!connectionErrors.http) {
        showWSWarning.value = false
      }
    } else if (data.status === 'disconnected') {
      // 设置WebSocket错误状态
      connectionErrors.ws = true
      connectionErrors.wsMessage = `连接关闭 (代码: ${data.code}${data.reason ? ', 原因: ' + data.reason : ''})`
      showWSWarning.value = true
    }
  })

  // 添加WebSocket错误监听
  wsClient.addEventListener('error', (data) => {
    // 设置WebSocket错误状态
    connectionErrors.ws = true
    connectionErrors.wsMessage = data.error || 'WebSocket错误'
    showWSWarning.value = true
  })

  // 测试服务器连接
  try {
    const connectivityResult = await checkServerConnectivity()

    // 更新HTTP状态
    connectionErrors.http = !connectivityResult.httpCheck.success
    connectionErrors.httpMessage = connectivityResult.httpCheck.error || ''

    // 更新WebSocket状态
    connectionErrors.ws = !connectivityResult.wsCheck.success
    connectionErrors.wsMessage = connectivityResult.wsCheck.error || ''

    // 只有当两者都成功时，才隐藏警告
    showWSWarning.value = connectionErrors.http || connectionErrors.ws

    if (connectivityResult.success) {
      logger.success('初始连接测试成功', connectivityResult)
    } else {
      logger.error('初始连接测试失败', connectivityResult)
    }
  } catch (error) {
    logger.error('初始连接测试出错', error)
    connectionErrors.http = true
    connectionErrors.httpMessage = error.message || '连接测试出错'
    connectionErrors.ws = true
    connectionErrors.wsMessage = error.message || '连接测试出错'
    showWSWarning.value = true
  }

  try {
    const isLoggedIn = await checkLoginStatus()

    // If not on login page and not logged in, redirect to login
    if (!isLoggedIn && window.location.hash !== '#/login') {
      window.location.hash = '#/login'
    } else if (isLoggedIn) {
      // 如果已登录显示通知
      logger.success('已检测到登录状态', { currentHash: window.location.hash })
      Util.redirectToCurrentPane('/taskpane')
      notification.showNotify('登录成功', 'success')
    }
  } catch (error) {
    // 处理 AI 编辑功能未开启的错误
    if (error.message && error.message.startsWith('AI_EDIT_DISABLED:')) {
      const message = error.message.replace('AI_EDIT_DISABLED:', '')
      logger.warn('AI编辑功能未开启', message)
      notification.showNotify(message, 'warning')
      // 重定向到登录页面
      window.location.hash = '#/login'
    } else {
      logger.error('初始登录状态检查时出错', error)
      // 其他错误也重定向到登录页面
      window.location.hash = '#/login'
    }
  }
})

onUnmounted(() => {
  // 清理WebSocket连接
  if (removeAuthListener) {
    removeAuthListener()
  }

  // 清理版本监听器
  if (removeVersionListener) {
    removeVersionListener()
  }

  if (reconnectTimer.value) {
    clearTimeout(reconnectTimer.value)
  }

  // 清理通知定时器
  notification.cleanupNotification()
  // 清理文档检查定时器
  if (window._docCheckInterval) {
    clearInterval(window._docCheckInterval)
    window._docCheckInterval = null
  }
})
</script>

<style scoped>
.app {
  position: relative;
  height: 100%;
}

.ws-warning-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999999;
}

.ws-warning-content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  text-align: center;
  max-width: 500px;
  color: #000;
}

.error-details {
  margin: 10px 0;
  padding: 10px;
  background-color: #fff8f8;
  border-left: 3px solid #ff6b6b;
  text-align: left;
  font-size: 0.9em;
  color: #000;
}

.reconnect-btn {
  margin-top: 15px;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.reconnect-btn:hover {
  background-color: #40a9ff;
}

.reconnect-btn:disabled {
  background-color: #b7b7b7;
  cursor: not-allowed;
}

.loading-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.doc-name {
  padding: 8px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
  color: #333;
}
</style>
