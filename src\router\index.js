import { createRouter, createWebHashHistory } from 'vue-router'
import { checkLoginStatus } from '../components/js/auth'

const router = createRouter({
  history:  createWebHashHistory(''),
  routes: [
    {
      path: '/',
      name: 'u9ed8u8ba4u9875',
      component: () => import('../components/Root.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/login',
      name: 'u767bu5f55',
      component: () => import('../components/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/dashboard',
      name: 'u4eeau8868u76d8',
      component: () => import('../components/TaskPane.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/dialog',
      name: 'u5bf9u8bddu6846',
      component: () => import('../components/Dialog.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/taskpane',
      name: 'u4efbu52a1u7a97u683c',
      component: () => import('../components/TaskPane.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/image-split',
      name: 'u56feu7247u5206u5272',
      component: () => import('../components/ImageSplit.vue'),
      meta: { requiresAuth: true }
    }
  ]
})

// Navigation guard to check authentication
router.beforeEach(async (to, from, next) => {
  // Check if the route requires authentication
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);

  if (requiresAuth) {
    // Check if user is logged in
    const isLoggedIn = await checkLoginStatus();

    if (!isLoggedIn) {
      // Redirect to login page if not logged in
      next({ path: '/login' });
    } else {
      // Continue to the route
      next();
    }
  } else {
    // For routes that don't require auth, just proceed
    next();
  }
});

export default router
