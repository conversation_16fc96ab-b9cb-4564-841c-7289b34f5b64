const fs = require('node:fs');
const path = require('node:path');
const { app } = require('electron');
const configStore = require('../services/configStore');

class Logger {
    constructor(options = {}) {
        // 日志配置
        this.logDir = options.logDir || 'c:\\Temp';
        this.logFileName = options.logFileName || 'app_debug.log';
        this.logFilePath = path.join(this.logDir, this.logFileName);
        this.emergencyLogFilePath = path.join(process.cwd(), `app_emergency_${this.logFileName}`);
        this.initialLogWritten = false;

        // 确保日志目录存在
        this.initLogDirectory();

        // 设置全局错误处理
        this.setupGlobalErrorHandlers();
    }

    initLogDirectory() {
        try {
            if (!fs.existsSync(this.logDir)) {
                fs.mkdirSync(this.logDir, { recursive: true });
                console.log(`日志目录已创建：${this.logDir}`);
            }
        } catch (e) {
            console.warn(`警告：无法创建日志目录 ${this.logDir}。错误：${e.message}。尝试使用当前工作目录作为紧急日志。`);
            this.logFilePath = this.emergencyLogFilePath;
        }
    }

    writeInitialLog() {
        // 如果已经写入过初始日志，则不再重复写入
        if (this.initialLogWritten) {
            return;
        }

        const startTime = new Date();
        const formattedStartTime = `${startTime.getFullYear()}-${(startTime.getMonth() + 1).toString().padStart(2, '0')}-${startTime.getDate().toString().padStart(2, '0')} ${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}:${startTime.getSeconds().toString().padStart(2, '0')}`;

        const initialLogMessages = [
            `\n`,
            `═════════════════════════════════════════════════════════════════════════════════════════════════════════`,
            `                                应用程序已启动                                                            `,
            `═════════════════════════════════════════════════════════════════════════════════════════════════════════`,
            ` 启动时间           : ${formattedStartTime}`,
            ` 日志文件路径       : ${this.logFilePath}`,
            ` 平台               : ${process.platform}`,
            `════════════════════════════════════════════════════════════════════════════════════════════════════════\n`
        ];

        try {
            initialLogMessages.forEach(msg => fs.appendFileSync(this.logFilePath, msg + '\n', { encoding: 'utf8' }));
            console.log(initialLogMessages.join('\n').trim());
            // 标记初始日志已写入
            this.initialLogWritten = true;
        } catch (e) {
            console.error("致命错误：无法将初始日志条目写入指定的日志路径:", this.logFilePath, e);
            try {
                const emergencyHeader = `紧急日志：无法写入 ${this.logFilePath}。使用当前工作目录下的紧急日志。\n原始错误：${e.stack}\n${new Date().toISOString()} - 应用程序已启动（紧急日志）\n`;
                fs.appendFileSync(this.emergencyLogFilePath, emergencyHeader + initialLogMessages.join('\n'));
                console.error("使用当前工作目录中的紧急日志文件:", this.emergencyLogFilePath);
                this.logFilePath = this.emergencyLogFilePath;
                // 标记初始日志已写入(即使是紧急日志)
                this.initialLogWritten = true;
            } catch (emergencyError) {
                console.error("致命错误：同样无法写入当前工作目录中的紧急日志。仅记录到控制台。", emergencyError);
            }
        }
    }

    log(level, message, data = '') {
        const now = new Date();
        const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}.${now.getMilliseconds().toString().padStart(3, '0')}`;

        const levelStr = `[${level.toUpperCase()}]`.padEnd(7);
        let logMessage = `${timestamp} ${levelStr} | ${message}\n`;

        if (data) {
            let dataString;
            if (data instanceof Error) {
                dataString = `    错误详情：${data.message}\n    堆栈跟踪：\n${data.stack ? data.stack.split('\n').map(line => `      ${line.trim()}`).join('\n') : '      不适用'}\n`;
            } else if (typeof data === 'object') {
                try {
                    dataString = `    数据：${JSON.stringify(data, null, 2).split('\n').map(line => `      ${line}`).join('\n')}\n`;
                } catch (jsonError) {
                    dataString = `    数据：（无法序列化对象 - ${jsonError.message}）\n      ${String(data)}\n`;
                }
            } else {
                dataString = `    详情：${String(data)}\n`;
            }
            logMessage += dataString;
        }
        logMessage += `----------------------------------------------------------------------------------------------------------\n`;

        try {
            // 确保以 UTF-8 编码写入文件
            fs.appendFileSync(this.logFilePath, logMessage, { encoding: 'utf8' });
            const consoleColor = {
                ERROR: '\x1b[31m',
                WARN: '\x1b[33m',
                INFO: '\x1b[32m',
                DEBUG: '\x1b[34m',
                RESET: '\x1b[0m'
            };
            const color = consoleColor[level.toUpperCase()] || consoleColor.RESET;
            const resetColor = consoleColor.RESET;

            // 使用Buffer确保UTF-8编码输出，特别是在Windows环境下
            const consoleMessage = `${color}${timestamp} ${levelStr}| ${message}${resetColor}`;
            const dataStr = data && typeof data === 'object' ?
                            JSON.stringify(data, null, 2) :
                            (data ? String(data) : '');

            if (level.toLowerCase() === 'error' || level.toLowerCase() === 'warn') {
                process.stderr.write(Buffer.from(consoleMessage + (dataStr ? ' ' + dataStr : '') + '\n', 'utf8'));
            } else {
                process.stdout.write(Buffer.from(consoleMessage + (dataStr ? ' ' + dataStr : '') + '\n', 'utf8'));
            }
        } catch (logErr) {
            console.error(`!!!!!!!!!! 致命错误：无法写入日志文件 (${this.logFilePath}) !!!!!!!!!!`, logErr);
            console.error('原始日志尝试内容：\n', logMessage);
        }
    }

    error(message, data) {
        this.log('error', message, data);
    }

    warn(message, data) {
        this.log('warn', message, data);
    }

    info(message, data) {
        this.log('info', message, data);
    }

    debug(message, data) {
        this.log('debug', message, data);
    }

    setupGlobalErrorHandlers() {
        // 设置全局错误处理
        process.on('uncaughtException', (err, origin) => {
            console.log('未捕获的异常！来源：', origin, err);
            this.error(`未捕获的异常！来源：${origin}`, err);
            setTimeout(() => process.exit(1), 1000).unref();
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error('未处理的Promise拒绝！', reason);
            this.error('未处理的Promise拒绝！', reason);
            this.debug('被拒绝的Promise:', promise);
            setTimeout(() => process.exit(1), 1000).unref();
        });
    }
}


const logDir = path.join(app.getPath('userData'), 'logs');
// 创建默认实例
const defaultLogger = new Logger({
    logDir: logDir,
    logFileName: 'app-server-debug.log',
    logLevel: 'debug'
});

module.exports = {
    Logger,
    defaultLogger,
    // 向后兼容旧的接口
    logToFile: (level, message, data) => defaultLogger.log(level, message, data),
    writeInitialLog: () => defaultLogger.writeInitialLog()
};
