const axios = require('axios')
const fs = require('fs')
const path = require('path')
const { defaultLogger } = require('../utils/logger')
const wsServer = require('./wsServer')
const { getUrlMonitorStore } = require('./urlMonitorStore')
const configStore = require('./configStore')

// label=1
const preHandleHtmlUrl = (appKey, ticketId) => `https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${appKey}/task/${ticketId}.html`

// label=2
const fixedHtmlUrl = (appKey, ticketId) => `https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${appKey}/ai/${ticketId}.fixed.html`

// label=3
const fixedJsonUrl = (appKey, ticketId) => `https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${appKey}/ai/${ticketId}.fixed.json`

// label=4
const aiEditJsonUrl = (appKey, ticketId) => `https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${appKey}/ai/${ticketId}.wps.json`

/**
 * URL监控器 - 带持久化存储支持
 * 这个类在原始URL监控器的基础上添加了数据库持久化功能
 */
class UrlMonitorWithDB {
  constructor() {
    try {
      this.monitoredUrls = new Map() // Map of URL -> {status, lastChecked, interval, urlId}
      // defaultLogger.info('URL监控器(DB版)初始化，Map已创建');
    } catch (error) {
      defaultLogger.error(`创建Map对象失败: ${error.message}`)
      // 回退到使用对象
      this.monitoredUrls = {}
      this._isUsingObject = true
      defaultLogger.info('回退到使用普通对象存储URL数据')
    }

    this.urlStore = null // urlStore is initialized below
    this.clientUrlMap = new Map() // 存储客户端ID与URL监控ID的关联关系
    this.taskUrlMap = new Map() // Initialize taskUrlMap for URL to TaskID associations

    this.activeClientIds = new Set() // Track active client IDs

    // 从configStore加载下载路径设置
    this.downloadPath = configStore.getUrlDownloadPath()

    // 确保下载目录存在
    this.ensureDownloadDirectory()

    // 从数据库加载现有的URL任务
    this._loadTasksFromDB()
  }

  /**
   * 从数据库加载任务
   * @private
   */
  async _loadTasksFromDB() {
    try {
      this.urlStore = await getUrlMonitorStore()
    } catch (error) {
      defaultLogger.error(`从数据库加载URL任务失败: ${error.message} \nStack: ${error.stack}`)
    }
  }

  // 辅助方法，确保即使回退到对象也能正常工作
  _getUrlData(urlId) {
    return this.monitoredUrls.get(urlId)
  }

  // 辅助方法，设置URL数据，兼容对象和Map
  async _setUrlData(urlId, data) {
    const existingData = this._getUrlData(urlId) || {}
    const dataToSave = { ...existingData, ...data }
    if (data.timerId === undefined && existingData.timerId)
      dataToSave.timerId = existingData.timerId
    if (data.timeoutId === undefined && existingData.timeoutId)
      dataToSave.timeoutId = existingData.timeoutId
    this.monitoredUrls.set(urlId, dataToSave)
    const dbData = { ...dataToSave }
    delete dbData.timerId // Not stored in DB
    delete dbData.timeoutId // Not stored in DB
    // 'checkStarted' IS stored in DB

    await this.urlStore?.saveTask(urlId, dbData)
  }

  // 辅助方法，删除URL数据，兼容对象和Map
  async _deleteUrlData(urlId) {
    this.monitoredUrls.delete(urlId)
    // 同步从数据库删除
    await this.urlStore.deleteTask(urlId)
  }

  /**
   * 确保下载目录存在
   */
  ensureDownloadDirectory() {
    try {
      if (!fs.existsSync(this.downloadPath)) {
        fs.mkdirSync(this.downloadPath, { recursive: true })
        // defaultLogger.info(`创建下载目录: ${this.downloadPath}`);
      }
    } catch (error) {
      defaultLogger.error(`创建下载目录失败: ${error.message}`)
    }
  }

  /**
   * 设置下载路径
   * @param {string} dirPath - 新的下载目录路径
   * @param {string} clientId - 客户端ID
   * @returns {boolean} 是否成功设置
   */
  setDownloadPath(dirPath, clientId) {
    if (!dirPath) {
      defaultLogger.error('无效的下载目录路径')
      return false
    }

    try {
      // 保存设置到configStore和urlStore
      configStore.setUrlDownloadPath(dirPath)
      this.urlStore.setDownloadPath(dirPath)
      this.downloadPath = dirPath

      // 确保目录存在
      this.ensureDownloadDirectory()

      // defaultLogger.info(`URL下载路径已设置为: ${dirPath}`);

      // 仅通知请求更改的客户端
      if (clientId) {
        this._sendEventToClient(
          'urlDownloadPathChanged',
          {
            path: dirPath
          },
          clientId
        )
      }

      return true
    } catch (error) {
      defaultLogger.error(`设置下载路径失败: ${error.message}`)
      return false
    }
  }

  /**
   * 获取当前下载路径
   * @returns {string} 当前下载路径
   */
  getDownloadPath() {
    // 直接获取configStore中的下载路径
    return configStore.getUrlDownloadPath()
  }

  /**
   * 将客户端ID与URL监控关联起来
   * @param {string} clientId - 客户端ID
   * @param {string} urlId - URL监控ID
   * @private
   */
  async _associateClientWithUrl(clientId, urlId) {
    if (!clientId || !urlId) return

    try {
      if (!this.clientUrlMap.has(clientId)) {
        this.clientUrlMap.set(clientId, new Set())
      }

      const urlSet = this.clientUrlMap.get(clientId)
      urlSet.add(urlId)
    } catch (error) {
      defaultLogger.error(`关联客户端与URL监控失败 (内存中): ${error.message}`)
    }
  }

  /**
   * 解除客户端ID与URL监控的关联
   * @param {string} urlId - URL监控ID
   * @private
   */
  _dissociateUrlFromClients(urlId) {
    if (!urlId) return

    try {
      for (const [clientId, urlSet] of this.clientUrlMap.entries()) {
        if (urlSet.has(urlId)) {
          urlSet.delete(urlId)
        }

        if (urlSet.size === 0) {
          this.clientUrlMap.delete(clientId)
        }
      }
    } catch (error) {
      defaultLogger.error(`解除URL监控 ${urlId} 的客户端关联失败 (内存中): ${error.message}`)
    }
  }

  /**
   * 获取与URL监控关联的客户端
   * @param {string} urlId - URL监控ID
   * @returns {Array<string>} 关联的客户端ID数组
   * @private
   */
  _getClientsForUrl(urlId) {
    const associatedClients = []
    for (const [clientId, urlSet] of this.clientUrlMap.entries()) {
      if (urlSet.has(urlId)) {
        associatedClients.push(clientId)
      }
    }
    return associatedClients
  }

  /**
   * 向特定客户端发送事件
   * @param {string} eventType - 事件类型
   * @param {Object} data - 事件数据
   * @param {string} clientId - 客户端ID
   * @private
   */
  _sendEventToClient(eventType, data, clientId) {
    if (!clientId) return

    try {
      wsServer.sendWatcherEventToClient(eventType, data, clientId)
    } catch (error) {
      defaultLogger.error(`向客户端 ${clientId} 发送事件失败: ${error.message}`)
    }
  }

  /**
   * 向与业务任务关联的URL发送事件 (通过WebSocket)
   * @param {string} eventType - 事件类型
   * @param {Object} data - 事件数据
   * @param {string} taskId - 业务任务ID
   * @param {string} clientId - 特定客户端ID (可选，指定时只发送给该客户端)
   * @private
   */
  _sendEventForTask(eventType, data, taskId, clientId) {
    if (!taskId) return

    if (clientId) {
      // 如果指定了客户端ID，只发送给该客户端
      this._sendEventToClient(
        eventType,
        {
          ...data,
          taskId
        },
        clientId
      )
      return
    }

    // 向所有当前连接的客户端广播任务相关事件
    try {
      wsServer.broadcastWatcherEvent(eventType, {
        ...data,
        taskId
      })
      // defaultLogger.info(`已广播业务任务 ${taskId} 的 ${eventType} 事件`);
    } catch (error) {
      defaultLogger.error(`广播任务事件失败: ${error.message}`)
    }
  }

  /**
   * 开始监控URL
   * @param {string} url - 要监控的URL
   * @param {number} interval - 检查间隔，默认1000毫秒
   * @param {Object} options - 附加选项
   * @param {boolean} options.downloadOnSuccess - 是否在可访问时下载文件
   * @param {string} options.filename - 自定义下载文件名
   * @param {string} options.taskId - 关联的任务ID
   * @param {string} options.clientId - 发起监控的客户端ID，仅用于通知
   * @returns {string} urlId - 监控URL的ID
   */
  async startMonitoring(url, interval = 1000, options = {}) {
    if (!url) {
      defaultLogger.error('无效的URL')
      return null
    }
    const clientId = options.clientId // Extract clientId

    let urlId
    try {
      urlId = await this.urlStore.getNextTaskId()
    } catch (error) {
      defaultLogger.error(`获取任务ID失败: ${error.message}`)
      urlId = `url_${Date.now()}`
    }
    defaultLogger.info(
      `开始监控URL: ${url} (ID: ${urlId}), 客户端: ${clientId}, 选项: ${JSON.stringify(options)}`
    )
    const urlData = {
      url,
      status: 'pending',
      lastChecked: null,
      interval,
      timerId: null,
      timeoutId: null,
      downloadOnSuccess: true,
      filename: options.filename || this._generateFilenameFromUrl(url),
      taskId: options.taskId || null,
      downloadedPath: null,
      checkStarted: 0,
      appKey: options.appKey || null, // 将appKey保存到URL数据中
      ticketId: options.ticketId || null // 将ticketId保存到URL数据中
    }
    await this._setUrlData(urlId, urlData)

    if (clientId) {
      await this._associateUrlWithTaskId(urlId, options.taskId)
      if (!this.activeClientIds.has(clientId)) {
        this.activeClientIds.add(clientId)
        defaultLogger.info(`客户端 ${clientId} (来自startMonitoring选项) 已添加到活动列表。`)
      }
      if (!this.clientUrlMap.get(clientId)) {
        this.clientUrlMap.set(clientId, new Set([urlId]))
      } else {
        this.clientUrlMap.get(clientId).add(urlId)
      }

    }
    await this._startOrResumePollingIfNeeded(urlId, clientId)
    const currentData = this._getUrlData(urlId)
    if (currentData && currentData.timerId && currentData.checkStarted) {
      await this._checkUrl(urlId, clientId)
    }
    this._sendEventToClient(
      'urlMonitoringStarted',
      {
        urlId,
        url,
        taskId: options.taskId,
        appKey: options.appKey || null, // 在事件中包含 appKey
        ticketId: options.ticketId || null // 在事件中包含 ticketId
      },
      clientId
    )
    return urlId
  }

  /**
   * 从URL生成文件名
   * @param {string} url - URL
   * @returns {string} 生成的文件名
   * @private
   */
  _generateFilenameFromUrl(url) {
    try {
      // 解析URL获取路径部分
      const urlObj = new URL(url)
      let pathname = urlObj.pathname

      // 获取路径的最后一部分作为文件名
      let filename = pathname.split('/').pop()

      // 如果文件名为空，使用时间戳
      if (!filename) {
        filename = `file_${Date.now()}`
      }

      // 确保文件名没有特殊字符
      filename = filename.replace(/[^a-zA-Z0-9\-_.]/g, '_')

      return filename
    } catch (error) {
      // 如果URL解析失败，使用时间戳作为文件名
      return `file_${Date.now()}`
    }
  }

  /**
   * 下载URL对应的文件
   * @param {string} urlId - URL监控ID
   * @returns {Promise<string|null>} 下载的文件路径，失败返回null
   * @private
   */
  async _downloadFile(urlId, clientId) {
    const urlData = this._getUrlData(urlId)
    if (!urlData) return null

    const url = urlData.url
    const filename = urlData.filename || this._generateFilenameFromUrl(url)

    try {
      // defaultLogger.info(`开始下载文件: ${url}`);

      // 确保下载目录存在
      this.ensureDownloadDirectory()

      // 构建完整的文件路径
      console.log(this.downloadPath, filename)
      const filePath = path.join(this.downloadPath, filename)

      // 创建写入流
      const writer = fs.createWriteStream(filePath)

      // 下载文件
      const response = await axios({
        method: 'get',
        url: url,
        responseType: 'stream',
        timeout: 60000 // 60秒超时
      })

      // 管道响应流到文件
      response.data.pipe(writer)

      // 返回一个Promise，当文件写入完成或出错时解析
      return new Promise((resolve, reject) => {
        writer.on('finish', async () => {
          // defaultLogger.info(`文件下载成功: ${filePath}`);

          // 更新URL数据以包含下载的文件路径
          try {
            // 重新获取可能已更新的URL数据
            const currentData = this._getUrlData(urlId)
            if (currentData) {
              // 创建包含所有当前数据的新对象，并更新文件路径
              const updatedData = {
                ...currentData,
                downloadedPath: filePath,
                localFilePath: filePath  // 同时也更新localFilePath字段
              }

              // 更新数据（也会同步到数据库）
              await this._setUrlData(urlId, updatedData)
              // defaultLogger.info(`成功更新URL ${urlId} 的下载路径: ${filePath}`);

              // 更新数据库中的localFilePath字段
              try {
                await this.urlStore.updateLocalFilePath(urlId, filePath)
                defaultLogger.info(`成功更新URL ${urlId} 的本地文件路径到数据库: ${filePath}`)
              } catch (dbError) {
                defaultLogger.error(`更新数据库中URL ${urlId} 的本地文件路径失败: ${dbError.message}`)
              }
            }
          } catch (updateError) {
            defaultLogger.error(`更新下载路径时出错: ${updateError.message}`)
          }

          // 发送下载成功通知到关联的客户端
          const eventData = {
            urlId,
            url,
            filePath,
            taskId: urlData.taskId,
            localFilePath: filePath
          }

          this._sendEventToClient('urlFileDownloaded', eventData, clientId)

          resolve(filePath)
        })

        writer.on('error', (error) => {
          defaultLogger.error(`文件下载失败: ${error.message}`)

          // 发送下载失败通知到关联的客户端
          const eventData = {
            urlId,
            url,
            error: error.message,
            taskId: urlData.taskId
          }

          this._sendEventToClient('urlFileDownloadError', eventData, clientId)

          reject(error)
        })
      })
    } catch (error) {
      defaultLogger.error(`下载文件失败: ${error.message}`)

      // 发送下载失败通知到关联的客户端
      const eventData = {
        urlId,
        url,
        error: error.message,
        taskId: urlData.taskId
      }

      this._sendEventToClient('urlFileDownloadError', eventData, clientId)

      return null
    }
  }

  /**
   * 停止监控特定URL
   * @param {string} urlId - 要停止监控的URL的ID
   * @param {string} clientId - 请求停止的客户端ID（可选）
   */
  async stopMonitoring(urlId, clientId) {
    const urlData = this._getUrlData(urlId)
    if (!urlData) {
      defaultLogger.warn(`未找到ID为 ${urlId} 的URL监控`)
      return false
    }

    defaultLogger.info(
      `停止监控URL: ${urlData.url} (ID: ${urlId}), 请求者: ${clientId || '服务器'}. 将更新状态为停止，但不会删除任务数据.`
    )

    if (urlData.timerId) {
      clearInterval(urlData.timerId)
    }
    if (urlData.timeoutId) {
      clearTimeout(urlData.timeoutId)
    }

    const downloadedPath = urlData.downloadedPath
    const url = urlData.url
    const taskId = urlData.taskId

    const updateForDBAndMemory = {
      timerId: null,
      timeoutId: null,
      checkStarted: 0,
      status:
        urlData.status === 'pending' ||
          urlData.status === 'accessible' ||
          urlData.status === 'inaccessible' ||
          urlData.status === 'error'
          ? 'stopped'
          : urlData.status,
      errorMessage:
        urlData.status === 'error'
          ? urlData.errorMessage
          : urlData.status !== 'completed' && urlData.status !== 'stopped'
            ? '主动停止'
            : urlData.errorMessage,
      timeoutReached: false // Clear any previous timeout flag
    }
    // This will update in-memory this.monitoredUrls and persist to url_tasks table via urlStore.saveTask
    await this._setUrlData(urlId, updateForDBAndMemory)

    if (downloadedPath && fs.existsSync(downloadedPath)) {
      try {
        fs.unlinkSync(downloadedPath)
        defaultLogger.info(`已删除下载的文件: ${downloadedPath} (因任务 ${urlId} 停止)`)
      } catch (deleteError) {
        defaultLogger.error(`删除下载文件失败: ${deleteError.message}`)
      }
    }

    const clientsToSendEvent = clientId ? [clientId] : this._getClientsForUrl(urlId)
    for (const client of clientsToSendEvent) {
      if (this.activeClientIds.has(client) || client === clientId) {
        this._sendEventToClient(
          'urlMonitorStopped',
          {
            urlId,
            url,
            taskId,
            downloadedPath,
            message: '监控已停止'
          },
          client
        )
      }
    }

    // Dissociate clients from this urlId in the in-memory clientUrlMap
    // This does not affect persistent task_associations
    const associatedClientsInMemory = this._getClientsForUrl(urlId)
    for (const associatedClientId of associatedClientsInMemory) {
      const clientUrls = this.clientUrlMap.get(associatedClientId)
      if (clientUrls) {
        clientUrls.delete(urlId)
        if (clientUrls.size === 0) {
          this.clientUrlMap.delete(associatedClientId)
          defaultLogger.info(
            `客户端 ${associatedClientId} 不再关联任何URL监控 (因 ${urlId} 停止)，已从内存映射中移除。`
          )
        }
      }
    }

    defaultLogger.info(`已"软"停止URL任务 (状态更新): ${url} (ID: ${urlId}). 数据保留在数据库中。`)
    return true
  }

  /**
   * 获取所有监控的URL的状态
   * @param {string} taskId - 可选的任务ID，用于筛选与该任务关联的URL
   * @returns {Array} URL监控状态对象数组
   */
  getStatus(taskId) {
    const statusList = []

    // 如果指定了任务ID，只返回与该任务关联的URL监控
    let urlIdsToInclude = null
    if (taskId) {
      try {
        // 获取与任务关联的URL
        urlIdsToInclude = this._getUrlsForTaskId(taskId)
      } catch (error) {
        defaultLogger.error(`获取任务关联URL失败: ${error.message}`)
        // 如果出错，返回空列表
        return statusList
      }
    }

    // 处理Map和Object存储
    try {
      // 如果使用Map存储
      try {
        this.monitoredUrls.forEach((data, urlId) => {
          try {
            // 如果指定了任务筛选，检查URL是否与该任务关联
            if (urlIdsToInclude && !urlIdsToInclude.includes(urlId)) {
              return
            }

            statusList.push({
              urlId,
              url: data.url,
              status: data.status,
              lastChecked: data.lastChecked,
              interval: data.interval,
              downloadOnSuccess: data.downloadOnSuccess,
              filename: data.filename,
              taskId: data.taskId,
              downloadedPath: data.downloadedPath,
              localFilePath: data.localFilePath,
              appKey: data.appKey || null, // 包含 appKey 字段
              ticketId: data.ticketId || null // 包含 ticketId 字段
            })
          } catch (itemError) {
            defaultLogger.error(`处理URL ${urlId} 状态时出错: ${itemError.message}`)
          }
        })
      } catch (mapError) {
        defaultLogger.error(`从Map获取状态失败: ${mapError.message}`)

        // 如果Map出错，尝试转换为数组
        try {
          Array.from(this.monitoredUrls.entries()).forEach(([urlId, data]) => {
            try {
              // 如果指定了任务筛选，检查URL是否与该任务关联
              if (urlIdsToInclude && !urlIdsToInclude.includes(urlId)) {
                return
              }

              if (data) {
                statusList.push({
                  urlId,
                  url: data.url,
                  status: data.status,
                  lastChecked: data.lastChecked,
                  interval: data.interval,
                  downloadOnSuccess: data.downloadOnSuccess,
                  filename: data.filename,
                  taskId: data.taskId,
                  downloadedPath: data.downloadedPath,
                  localFilePath: data.localFilePath,
                  appKey: data.appKey || null, // 包含 appKey 字段
                  ticketId: data.ticketId || null // 包含 ticketId 字段
                })
              }
            } catch (itemError) {
              defaultLogger.error(`处理URL ${urlId} 状态时出错: ${itemError.message}`)
            }
          })
        } catch (arrayError) {
          defaultLogger.error(`转换Map到数组失败: ${arrayError.message}`)
        }
      }
    } catch (error) {
      defaultLogger.error(`获取URL状态失败: ${error.message}`)
    }
    return statusList
  }

  /**
   * 内部方法，检查URL的可访问性
   * @param {string} urlId - 要检查的URL的ID
   * @returns {Promise<void>} 检查完成时解析的Promise
   * @private
   */
  async _checkUrl(urlId, clientId) {
    if (!urlId) {
      return
    }

    const urlData = this._getUrlData(urlId)
    if (!urlData) {
      return
    }

    if (!urlData.checkStarted || !(await this._isPollingCandidate(urlId))) {
      if (urlData.timerId) {
        clearInterval(urlData.timerId)
        urlData.timerId = null
      }
      if (urlData.timeoutId) {
        clearTimeout(urlData.timeoutId)
        urlData.timeoutId = null
      }
      await this._setUrlData(urlId, { timerId: null, timeoutId: null })
      return
    }

    const appKey = urlData.appKey
    const ticketId = urlData.ticketId
    let label = urlData.label || 0
    
    // 检查是否为校对任务（通过filename判断）
    if (urlData.filename && urlData.filename.endsWith('.wps.json')) {
      // 校对任务直接检查aiEditJsonUrl
      label = 3;
    }
    
    // 根据当前label选择检查的URL
    let url = urlData.url
    if (appKey && ticketId) {
      if (label === 0) {
        url = preHandleHtmlUrl(appKey, ticketId)
      } else if (label === 1) {
        url = fixedHtmlUrl(appKey, ticketId)
      } else if (label === 2) {
        url = fixedJsonUrl(appKey, ticketId)
      } else if (label === 3) {
        url = aiEditJsonUrl(appKey, ticketId)
      }
      // 如果label是4，则使用原始url(.docx)
    }
    if (!url) {
      defaultLogger.error(`ID ${urlId} 的URL缺失`)
      return
    }
    let previousStatus = urlData.status || 'pending'
    let newStatus = 'pending'
    let statusCode = null
    let responseTime = null
    const startTime = Date.now()
    let currentLabel = label
    console.log(`检查URL: ${urlData.url} (ID: ${urlId})， ${clientId}, ${this.activeClientIds.size} ,${!urlData.checkStarted || !(await this._isPollingCandidate(urlId))}`)
    const clientToNotify = clientId || (await this._getFirstActiveClientForUrl(urlId))

    try {
      const response = await axios({
        method: 'head',
        url,
        timeout: 10000,
        validateStatus: () => true
      })
      responseTime = Date.now() - startTime
      statusCode = response.status

      if (statusCode >= 200 && statusCode < 400) {
        newStatus = 'accessible'
        if (urlData.timeoutId) {
          clearTimeout(urlData.timeoutId)
          urlData.timeoutId = null // Important: update urlData in memory
        }

        // 校对任务不需要递增label，保持在label=3
        if (urlData.filename && urlData.filename.endsWith('.wps.json')) {
          currentLabel = 3; // 校对任务固定为label=3
        } else {
          // 如果当前URL可访问且label小于4，增加label
          if (currentLabel < 4) {
            currentLabel = currentLabel + 1
          }
        }
      } else {
        newStatus = 'inaccessible'
      }

      if (
        newStatus === 'accessible' &&
        !urlData.downloadedPath
        // (previousStatus !== 'accessible' || )
        // && urlData.downloadOnSuccess
      ) {
        await this._downloadFile(urlId, clientToNotify) // Pass clientToNotify
      }
    } catch (error) {
      responseTime = Date.now() - startTime
      newStatus = 'error'
      defaultLogger.error(`检查URL ${url} 出错: ${error.message}`)
    }

    const updatedDataForStore = {
      status: newStatus,
      lastChecked: new Date().toISOString(),
      responseTime: responseTime,
      statusCode: statusCode,
      timeoutId: newStatus === 'accessible' ? null : urlData.timeoutId,
      label: currentLabel
    }
    Object.assign(urlData, updatedDataForStore)
    await this._setUrlData(urlId, urlData)

    if (clientToNotify && this.activeClientIds.has(clientToNotify)) {
      const eventData = {
        urlId,
        url,
        status: newStatus,
        previousStatus,
        statusChanged: true,
        lastChecked: urlData.lastChecked,
        responseTime,
        statusCode,
        taskId: urlData.taskId,
        downloadedPath: urlData.downloadedPath,
        localFilePath: urlData.localFilePath,
        appKey: urlData.appKey || null,
        ticketId: urlData.ticketId || null,
        label: currentLabel // 添加label到事件数据中
      }
      this._sendEventToClient('urlMonitorUpdate', eventData, clientToNotify)
    } else if (newStatus !== previousStatus) {
      defaultLogger.info(`URL ${urlId} 状态变为 ${newStatus}，但此检查周期无特定活动客户端通知。`)
    }
  }

  /**
   * 强制立即检查URL
   * @param {string} urlId - 要检查的URL的ID
   * @param {string} clientId - 请求检查的客户端ID（可选）
   * @returns {Promise<boolean>} 如果检查开始，解析为true，否则为false
   */
  async forceCheck(urlId, clientId) {
    const urlData = this._getUrlData(urlId)
    if (!urlData) {
      defaultLogger.warn(`未找到ID为 ${urlId} 的URL监控`)
      return false
    }
    // Client permission check optional

    // Ensure the client requesting the check is associated or it's a general request
    let clientIsAssociated = false
    if (clientId) {
      const associatedClients = this._getClientsForUrl(urlId) // Use in-memory getter
      if (associatedClients.includes(clientId)) {
        clientIsAssociated = true
      }
    }

    if (!clientId || (clientId && clientIsAssociated && this.activeClientIds.has(clientId))) {
      // defaultLogger.info(`强制检查 URL: ${urlData.url} (ID: ${urlId}), 请求者: ${clientId || '服务器'}`);
      // Temporarily ensure checkStarted is true for this one check if it wasn't
      const originalCheckStarted = urlData.checkStarted
      if (!originalCheckStarted) {
        urlData.checkStarted = 1
      }
      try {
        await this._checkUrl(urlId, clientId)
        if (!originalCheckStarted) urlData.checkStarted = false // Revert if changed
        return true
      } catch (error) {
        defaultLogger.error(`强制检查失败: ${error.message}`)
        if (!originalCheckStarted) urlData.checkStarted = false // Revert
        return false
      }
    } else {
      defaultLogger.warn(`客户端 ${clientId} 尝试强制检查URL ${urlId} 但不是活动或未关联。`)
      return false
    }
  }

  /**
   * 开始检查已设置为监控的URL
   * @param {string} urlId - 要开始检查的URL的ID
   * @param {string} clientId - 请求开始检查的客户端ID（可选）
   * @returns {boolean} 操作是否成功
   */
  async startChecking(urlId, clientId) {
    // clientId is the client requesting to start
    const urlData = this._getUrlData(urlId)
    if (!urlData) {
      defaultLogger.error(`startChecking: 未找到ID ${urlId} 的URL数据`)
      return false
    }

    if (clientId && !this.activeClientIds.has(clientId)) {
      defaultLogger.warn(
        `客户端 ${clientId} 请求启动 ${urlId} 的检查，但不在 activeClientIds 集合中。`
      )
    }
    defaultLogger.info(
      `请求启动 URL 检查: ${urlData.url} (ID: ${urlId}), 客户端: ${clientId || 'N/A'}`
    )
    urlData.checkStarted = 1
    await this._setUrlData(urlId, { checkStarted: 1 }) // Persist checkStarted
    await this._startOrResumePollingIfNeeded(urlId, clientId)
    const currentData = this._getUrlData(urlId)
    if (currentData && currentData.timerId && currentData.checkStarted) {
      try {
        await this._checkUrl(urlId, clientId)
      } catch (err) { }
      return true
    } else {
      defaultLogger.warn(
        `URL ${urlId} 检查由 ${clientId} 请求，但轮询未启动 (例如无活动关联客户端或其他问题)。`
      )
      urlData.checkStarted = false
      await this._setUrlData(urlId, { checkStarted: 0 })
      return false
    }
  }

  /**
   * 当客户端断开连接时清理相关资源
   * @param {string} clientId - 断开连接的客户端ID
   * @param {boolean} actualDisconnect - True if this is from a real client disconnect event
   */
  async cleanupDisconnectedClient(clientId, actualDisconnect = false) {
    if (!clientId) return

    if (actualDisconnect) {
      // activeClientIds set is already updated by removeActiveClient
    } else {
      this.activeClientIds.delete(clientId)
    }

    defaultLogger.info(
      `清理客户端 ${clientId} 的关联资源。活动客户端剩余: ${this.activeClientIds.size}`
    )

    const associatedUrlIds = this.clientUrlMap.has(clientId)
      ? Array.from(this.clientUrlMap.get(clientId))
      : []
    // Old: const associatedUrlIds = await this.urlStore.getTasksForClient(clientId);

    if (!associatedUrlIds || associatedUrlIds.length === 0) {
      this.clientUrlMap.delete(clientId) // Clean local map too
      return
    }

    let pollingStoppedCount = 0
    for (const urlId of associatedUrlIds) {
      const urlData = this._getUrlData(urlId)
      if (!urlData) continue

      const stillHasActiveClient = await this._isPollingCandidate(urlId)

      if (!stillHasActiveClient && (urlData.timerId || urlData.checkStarted)) {
        // Check if it was polling or intended to poll
        defaultLogger.info(
          `客户端 ${clientId} 断开, URL ${urlId} (当前状态: ${urlData.status}, checkStarted: ${urlData.checkStarted}) 不再有活动客户端。停止轮询并将 checkStarted 设为 0。`
        )
        if (urlData.timerId) {
          clearInterval(urlData.timerId)
        }
        // urlData.timerId = null; // for memory - _setUrlData handles memory update based on what's passed
        if (urlData.timeoutId) {
          clearTimeout(urlData.timeoutId)
        }
        // urlData.timeoutId = null; // for memory - _setUrlData handles memory update

        const updateForPause = {
          timerId: null,
          timeoutId: null,
          checkStarted: 0 // Explicitly set to 0
          // Do NOT change urlData.status here. 'stopped' is for hard stops by stopMonitoring.
        }
        await this._setUrlData(urlId, updateForPause)
        pollingStoppedCount++
      } else if (stillHasActiveClient) {
        defaultLogger.info(`客户端 ${clientId} 断开, 但URL ${urlId} 仍有其他活动客户端。轮询继续。`)
      }
    }

    // Remove the client from the clientUrlMap entirely
    this.clientUrlMap.delete(clientId)
    // Old: await this.urlStore.cleanupClientAssociations(clientId); // REMOVED

    defaultLogger.info(
      `客户端 ${clientId} 清理完成。为 ${pollingStoppedCount} 个URL停止了轮询并更新了 checkStarted 状态。`
    )
  }

  async addActiveClient(clientId) {
    if (clientId) {
      this.activeClientIds.add(clientId)
      defaultLogger.info(
        `客户端 ${clientId} 已连接并标记为活动状态。当前活动客户端: ${this.activeClientIds.size}`
      )
      await this._checkAndResumePollingForClient(clientId)
    }
  }

  /**
   * Called by wsServer when a client disconnects.
   * @param {string} clientId
   */
  async removeActiveClient(clientId) {
    if (clientId) {
      this.activeClientIds.delete(clientId)
      // defaultLogger.info(`客户端 ${clientId} 已断开连接。当前活动客户端: ${this.activeClientIds.size}`);
      await this.cleanupDisconnectedClient(clientId, true) // Pass true to indicate actual disconnect
    }
  }

  /**
   * Checks if a URL should be actively polled based on its checkStarted status
   * and if any associated client is currently active.
   * @param {string} urlId
   * @returns {Promise<boolean>}
   * @private
   */
  async _isPollingCandidate(urlId) {
    const urlData = this._getUrlData(urlId)
    if (!urlData) {
      return false
    }
    if (!urlData.checkStarted) {
      return false
    }

    const associatedClientIds = this._getClientsForUrl(urlId)
    if (!associatedClientIds || associatedClientIds.length === 0) {
      return false
    }

    for (const clientId of associatedClientIds) {
      if (this.activeClientIds.has(clientId)) {
        return true
      }
    }
    return false
  }

  /**
   * Starts or resumes polling for a specific URL if it's a polling candidate.
   * @param {string} urlId
   * @param {string} [initiatingClientId] - Optional: client that triggered this action.
   * @private
   */
  async _startOrResumePollingIfNeeded(urlId, initiatingClientId) {
    const urlData = this._getUrlData(urlId)
    if (!urlData) {
      return
    }

    if (urlData.timerId && urlData.checkStarted) {
      return
    }
    const isCandidate = await this._isPollingCandidate(urlId)

    if (isCandidate) {
      if (!urlData.checkStarted) {
        urlData.checkStarted = 1
      }

      if (urlData.timerId) clearInterval(urlData.timerId)
      if (urlData.timeoutId) clearTimeout(urlData.timeoutId)
      urlData.timeoutId = null

      urlData.timerId = setInterval(async () => {
        const currentUrlData = this._getUrlData(urlId)
        if (
          currentUrlData &&
          currentUrlData.checkStarted &&
          (await this._isPollingCandidate(urlId))
        ) {
          await this._checkUrl(
            urlId,
            initiatingClientId || (await this._getFirstActiveClientForUrl(urlId))
          )
        } else if (currentUrlData && currentUrlData.timerId) {
          clearInterval(currentUrlData.timerId)
          currentUrlData.timerId = null
          if (currentUrlData.timeoutId) {
            clearTimeout(currentUrlData.timeoutId)
            currentUrlData.timeoutId = null
          }
          await this._setUrlData(urlId, {
            timerId: null,
            timeoutId: null,
            checkStarted: currentUrlData.checkStarted
          })
        }
      }, urlData.interval)

      if (urlData.status !== 'accessible') {
        urlData.timeoutId = setTimeout(async () => {
          const currentData = this._getUrlData(urlId)
          if (
            currentData &&
            currentData.checkStarted &&
            currentData.status !== 'accessible' &&
            (await this._isPollingCandidate(urlId))
          ) {
            currentData.status = 'error'
            currentData.timeoutReached = true
            currentData.errorMessage = '检查超时 (5分钟)'
            await this._setUrlData(urlId, currentData)
            this._sendEventToClient(
              'urlMonitorTimeout',
              {
                urlId,
                url: currentData.url,
                taskId: currentData.taskId,
                message: currentData.errorMessage
              },
              initiatingClientId || (await this._getFirstActiveClientForUrl(urlId))
            )
          } else if (currentData && currentData.timeoutId) {
            clearTimeout(currentData.timeoutId)
            currentData.timeoutId = null
          }
        }, 300000) // 5 minutes
      }
      await this._setUrlData(urlId, {
        ...urlData,
        timerId: urlData.timerId,
        timeoutId: urlData.timeoutId
      })
    } else {
      if (urlData.timerId) {
        clearInterval(urlData.timerId)
      }
      if (urlData.timeoutId) {
        clearTimeout(urlData.timeoutId)
      }
      await this._setUrlData(urlId, {
        timerId: null,
        timeoutId: null,
        checkStarted: urlData.checkStarted
      })
    }
  }

  /**
   * Helper to get the first active client for a URL, for sending events.
   * @param {string} urlId
   * @returns {Promise<string|null>}
   * @private
   */
  async _getFirstActiveClientForUrl(urlId) {
    // Get associated clients from in-memory clientUrlMap
    const associatedClientIds = this._getClientsForUrl(urlId)
    // Old: const associatedClientIds = await this.urlStore.getClientsForTask(urlId);

    if (associatedClientIds) {
      for (const clientId of associatedClientIds) {
        if (this.activeClientIds.has(clientId)) {
          return clientId
        }
      }
    }
    return null
  }

  /**
   * When a client connects, check its associated URLs and resume polling if needed.
   * @param {string} clientId
   * @private
   */
  async _checkAndResumePollingForClient(clientId) {
    // Get task IDs (urlIds) associated with this client from in-memory map
    const urlIds = this.clientUrlMap.get(clientId)
      ? Array.from(this.clientUrlMap.get(clientId))
      : []
    // Old: const taskIds = await this.urlStore.getTasksForClient(clientId);

    // defaultLogger.info(`客户端 ${clientId} 连接。检查 ${urlIds.length} 个关联URL任务以恢复轮询。`);
    for (const urlId of urlIds) {
      const urlData = this._getUrlData(urlId)
      if (urlData && urlData.checkStarted) {
        //  defaultLogger.info(`因客户端 ${clientId} 连接，重新评估 ${urlId} 的轮询。`);
        await this._startOrResumePollingIfNeeded(urlId, clientId)
      }
    }
  }

  async handleResumeUrlMonitors(data, clientId) {
    // this.addActiveClient(clientId);
    if (clientId) {
      this.activeClientIds.add(clientId)
    }
    defaultLogger.info(
      `客户端 ${clientId} 已连接并标记为活动状态。当前活动客户端: ${this.activeClientIds.size}`
    )
    const { taskIds } = data
    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      defaultLogger.warn(`[${clientId}] 无效的 resumeUrlMonitors 请求: taskIds 缺失或为空`)
      return { success: false, message: '未提供taskIds或taskIds数组为空' }
    }

    defaultLogger.info(
      `[${clientId}] 请求恢复 ${taskIds.length} 个业务任务的URL监控: ${taskIds.join(', ')}`
    )
    let resumedUrlMonitorCount = 0
    let totalUrlMonitorsFound = 0
    let alreadyActiveAndPollingCount = 0
    let notFoundOrNoUrlMonitorsCount = 0
    let nonRecoverableStateUrlMonitorCount = 0 // Renamed counter
    let errorProcessingBusinessTaskCount = 0

    for (const businessTaskId of taskIds) {
      try {
        const associatedUrlTasks =
          await this.urlStore?.findUrlMonitorsByBusinessTaskId(businessTaskId)

        if (!associatedUrlTasks || associatedUrlTasks.length === 0) {
          defaultLogger.info(`[${clientId}] 业务任务 ${businessTaskId} 没有找到关联的URL监控任务。`)
          notFoundOrNoUrlMonitorsCount++
          continue
        }
        totalUrlMonitorsFound += associatedUrlTasks.length

        for (const urlTaskDataFromDB of associatedUrlTasks) {
          const urlId = urlTaskDataFromDB.urlId

          await this._associateClientWithUrl(clientId, urlId)
          // await this._associateUrlWithTaskId(urlId, businessTaskId)

          const urlDataInDB = await this.urlStore.getTask(urlId)

          if (urlDataInDB && urlDataInDB.checkStarted && (await this._isPollingCandidate(urlId))) {
            defaultLogger.info(
              `[${clientId}] URL监控 ${urlId} (属于业务任务 ${businessTaskId}) 已在内存中并积极轮询。`
            )
            alreadyActiveAndPollingCount++
            continue
          }

          if (urlTaskDataFromDB.status === 'completed' || urlTaskDataFromDB.status === 'error') {
            defaultLogger.info(
              `[${clientId}] URL监控 ${urlId} (业务任务 ${businessTaskId}) 状态为 ${urlTaskDataFromDB.status}，不恢复轮询。`
            )
            nonRecoverableStateUrlMonitorCount++
            if (urlTaskDataFromDB.checkStarted) {
              await this.urlStore.updateTask(urlId, { checkStarted: 0 })
            }
            continue
          }

          const urlDataToResumeInMemory = {
            ...(urlDataInDB || {}),
            ...urlTaskDataFromDB,
            checkStarted: 1,
            timerId: null,
            timeoutId: null, // Ensure fresh timeout
            // If resuming from 'stopped', clear previous error messages that might have been related to stopping.
            // For other states like 'inaccessible', their existing errorMessage (if any) from DB is preserved.
            errorMessage:
              urlTaskDataFromDB.status === 'stopped' ? null : urlTaskDataFromDB.errorMessage,
            timeoutReached: false // Clear any previous timeout flag
          }
          this.monitoredUrls.set(urlId, urlDataToResumeInMemory)
          await this._setUrlData(urlId, {
            checkStarted: 1,
            status: urlTaskDataFromDB.status,
            errorMessage: urlDataToResumeInMemory.errorMessage,
            timeoutReached: false
          })

          await this._startOrResumePollingIfNeeded(urlId, clientId)

          const currentDataAfterResume = this._getUrlData(urlId)
          if (currentDataAfterResume && currentDataAfterResume.checkStarted) {
            resumedUrlMonitorCount++
            await this._checkUrl(urlId, clientId)
            defaultLogger.info(`[${clientId}] 成功恢复并开始检查 URL监控 ${urlId} (业务任务 ${businessTaskId}，原状态: ${urlTaskDataFromDB.status})。`)
          } else {
            defaultLogger.warn(
              `[${clientId}] URL监控 ${urlId} (业务任务 ${businessTaskId}，原状态: ${urlTaskDataFromDB.status}) 请求恢复，checkStarted已设置，但轮询未激活。`
            )
          }
        }
      } catch (error) {
        defaultLogger.error(
          `[${clientId}] 恢复业务任务 ${businessTaskId} 的URL监控时出错: ${error.message} \nStack: ${error.stack}`
        )
        errorProcessingBusinessTaskCount++
      }
    }

    const summaryMessage =
      `恢复轮询 ${resumedUrlMonitorCount} 个URL监控。` +
      `请求任务的总URL监控数: ${totalUrlMonitorsFound}. ` +
      `已激活: ${alreadyActiveAndPollingCount}. ` +
      `未找到监控的任务数: ${notFoundOrNoUrlMonitorsCount}. ` +
      `处于不可恢复状态(completed/error): ${nonRecoverableStateUrlMonitorCount}. ` +
      `处理出错的任务数: ${errorProcessingBusinessTaskCount}。`
    defaultLogger.info(`[${clientId}] 业务任务URL监控恢复完成。${summaryMessage}`)
    return {
      success: true,
      message: summaryMessage
    }
  }

  /**
   * 获取当前正在积极监控的URL数量。
   * 一个URL被认为是积极监控的，如果它有一个活动的timerId并且其checkStarted为true。
   * @returns {number} 正在积极监控的URL数量。
   */
  getMonitoringCount() {
    let count = 0
    const processUrlData = (urlData) => {
      if (urlData && urlData.timerId && urlData.checkStarted) {
        count++
      }
    }

    this.monitoredUrls.forEach(processUrlData)
    return count
  }

  /**
   * Associates a URL monitoring task (urlId) with a business task ID.
   * This involves calling the urlStore to persist this association
   * and updating the in-memory taskUrlMap.
   * @param {string} urlId - The ID of the URL monitoring task.
   * @param {string} businessTaskId - The ID of the business task.
   * @private
   */
  async _associateUrlWithTaskId(urlId, businessTaskId) {
    if (!urlId || !businessTaskId) {
      defaultLogger.warn(
        `_associateUrlWithTaskId 调用时 urlId ('${urlId}') 或 businessTaskId ('${businessTaskId}') 无效。`
      )
      return
    }
    try {
      await this.urlStore.associateUrlWithTask(urlId, businessTaskId)
      if (!this.taskUrlMap.has(businessTaskId)) {
        this.taskUrlMap.set(businessTaskId, new Set())
      }
      this.taskUrlMap.get(businessTaskId).add(urlId)
    } catch (error) {
      defaultLogger.error(
        `关联URL监控 ${urlId} 与业务任务 ${businessTaskId} 失败: ${error.message}`
      )
    }
  }

  /**
   * Retrieves all urlIds associated with a given businessTaskId from the in-memory map.
   * @param {string} businessTaskId - The business task ID.
   * @returns {Array<string>} An array of urlIds.
   * @private
   */
  _getUrlsForTaskId(businessTaskId) {
    if (this.taskUrlMap.has(businessTaskId)) {
      return Array.from(this.taskUrlMap.get(businessTaskId))
    }
    // defaultLogger.debug(`_getUrlsForTaskId: No urlIds found for businessTaskId ${businessTaskId} in taskUrlMap.`);
    return []
  }
}

const urlMonitor = new UrlMonitorWithDB()

module.exports = urlMonitor
