const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// 获取当前版本号
function getCurrentVersion() {
    const packagePath = path.join(__dirname, '..', 'package.json');
    const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    return packageData.version;
}

// 获取当前时间
function getCurrentDate() {
    return new Date().toISOString();
}

// 更新配置文件
function updateConfigFile(configPath, updates) {
    try {
        let config = {};
        
        // 如果文件存在，读取现有配置
        if (fs.existsSync(configPath)) {
            config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        }
        
        // 应用更新
        Object.assign(config, updates);
        
        // 写回文件
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2) + '\n', 'utf8');
        console.log(chalk.green(`✓ 已更新配置文件: ${configPath}`));
        
        return true;
    } catch (error) {
        console.error(chalk.red(`✗ 更新配置文件失败: ${configPath}`), error.message);
        return false;
    }
}

// 主函数
function updateVersionInfo() {
    console.log(chalk.cyan('=== 更新版本信息 ==='));
    
    const currentVersion = getCurrentVersion();
    const currentDate = getCurrentDate();
    
    console.log(chalk.blue(`当前版本: ${currentVersion}`));
    console.log(chalk.blue(`构建时间: ${currentDate}`));
    
    // 定义配置文件及其更新内容
    const configs = [
        {
            path: path.join(__dirname, '..', 'env-config', 'production.json'),
            updates: {
                ENV: 'production',
                EDITION: 'wanwei',
                BYPASS_ENCRYPTION: false,
                APP_NAME: '万唯AI编辑WPS插件服务',
                APP_VERSION: currentVersion,
                BUILD_DATE: currentDate
            }
        },
        {
            path: path.join(__dirname, '..', 'env-config', 'hexin-production.json'),
            updates: {
                ENV: 'production',
                EDITION: 'hexin',
                BYPASS_ENCRYPTION: true,
                APP_NAME: '合心科技AI编辑WPS插件服务',
                APP_VERSION: currentVersion,
                BUILD_DATE: currentDate
            }
        }
    ];
    
    // 确保目录存在
    const envConfigDir = path.join(__dirname, '..', 'env-config');
    if (!fs.existsSync(envConfigDir)) {
        fs.mkdirSync(envConfigDir, { recursive: true });
        console.log(chalk.yellow(`创建目录: ${envConfigDir}`));
    }
    
    // 更新所有配置文件
    let successCount = 0;
    for (const config of configs) {
        if (updateConfigFile(config.path, config.updates)) {
            successCount++;
        }
    }
    
    console.log(chalk.blue(`\n成功更新: ${successCount}/${configs.length} 个配置文件`));
    
    if (successCount === configs.length) {
        console.log(chalk.green('✓ 所有版本信息更新完成'));
        return true;
    } else {
        console.log(chalk.red('✗ 部分版本信息更新失败'));
        return false;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const success = updateVersionInfo();
    process.exit(success ? 0 : 1);
}

module.exports = { updateVersionInfo }; 