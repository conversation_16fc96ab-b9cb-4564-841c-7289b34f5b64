import{U as cn,r as he,h as bt,v as Ke,i as Q,j as Ut,k as Ws,m as Yt,_ as Bs,n as un,o as j,c as W,a as d,p as dn,t as ae,f as J,q as _e,w as Ye,s as Ht,e as es,F as wt,u as _t,x as ut,y as pn,z as de,A as ts,B as bs,C as ht,D as fn,E as hn}from"./index-BXZGzeH0.js";function gn(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=cn.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const vn={onbuttonclick:gn};var mn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function bn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function wn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var o=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,o.get?o:{enumerable:!0,get:function(){return e[a]}})}),t}var Ns={exports:{}};const yn={},xn=Object.freeze(Object.defineProperty({__proto__:null,default:yn},Symbol.toStringTag,{value:"Module"})),ws=wn(xn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",o=a?window:{};o.JS_SHA1_NO_WINDOW&&(a=!1);var v=!a&&typeof self=="object",C=!o.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;C?o=mn:v&&(o=self);var D=!o.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,w=!o.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",$="0123456789abcdef".split(""),O=[-**********,8388608,32768,128],U=[24,16,8,0],H=["hex","array","digest","arrayBuffer"],R=[],N=Array.isArray;(o.JS_SHA1_NO_NODE_JS||!N)&&(N=function(h){return Object.prototype.toString.call(h)==="[object Array]"});var K=ArrayBuffer.isView;w&&(o.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!K)&&(K=function(h){return typeof h=="object"&&h.buffer&&h.buffer.constructor===ArrayBuffer});var V=function(h){var b=typeof h;if(b==="string")return[h,!0];if(b!=="object"||h===null)throw new Error(s);if(w&&h.constructor===ArrayBuffer)return[new Uint8Array(h),!1];if(!N(h)&&!K(h))throw new Error(s);return[h,!1]},se=function(h){return function(b){return new P(!0).update(b)[h]()}},ne=function(){var h=se("hex");C&&(h=oe(h)),h.create=function(){return new P},h.update=function(k){return h.create().update(k)};for(var b=0;b<H.length;++b){var T=H[b];h[T]=se(T)}return h},oe=function(h){var b=ws,T=ws.Buffer,k;T.from&&!o.JS_SHA1_NO_BUFFER_FROM?k=T.from:k=function(m){return new T(m)};var S=function(m){if(typeof m=="string")return b.createHash("sha1").update(m,"utf8").digest("hex");if(m==null)throw new Error(s);return m.constructor===ArrayBuffer&&(m=new Uint8Array(m)),N(m)||K(m)||m.constructor===T?b.createHash("sha1").update(k(m)).digest("hex"):h(m)};return S},p=function(h){return function(b,T){return new ce(b,!0).update(T)[h]()}},te=function(){var h=p("hex");h.create=function(k){return new ce(k)},h.update=function(k,S){return h.create(k).update(S)};for(var b=0;b<H.length;++b){var T=H[b];h[T]=p(T)}return h};function P(h){h?(R[0]=R[16]=R[1]=R[2]=R[3]=R[4]=R[5]=R[6]=R[7]=R[8]=R[9]=R[10]=R[11]=R[12]=R[13]=R[14]=R[15]=0,this.blocks=R):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}P.prototype.update=function(h){if(this.finalized)throw new Error(t);var b=V(h);h=b[0];for(var T=b[1],k,S=0,m,I=h.length||0,A=this.blocks;S<I;){if(this.hashed&&(this.hashed=!1,A[0]=this.block,this.block=A[16]=A[1]=A[2]=A[3]=A[4]=A[5]=A[6]=A[7]=A[8]=A[9]=A[10]=A[11]=A[12]=A[13]=A[14]=A[15]=0),T)for(m=this.start;S<I&&m<64;++S)k=h.charCodeAt(S),k<128?A[m>>>2]|=k<<U[m++&3]:k<2048?(A[m>>>2]|=(192|k>>>6)<<U[m++&3],A[m>>>2]|=(128|k&63)<<U[m++&3]):k<55296||k>=57344?(A[m>>>2]|=(224|k>>>12)<<U[m++&3],A[m>>>2]|=(128|k>>>6&63)<<U[m++&3],A[m>>>2]|=(128|k&63)<<U[m++&3]):(k=65536+((k&1023)<<10|h.charCodeAt(++S)&1023),A[m>>>2]|=(240|k>>>18)<<U[m++&3],A[m>>>2]|=(128|k>>>12&63)<<U[m++&3],A[m>>>2]|=(128|k>>>6&63)<<U[m++&3],A[m>>>2]|=(128|k&63)<<U[m++&3]);else for(m=this.start;S<I&&m<64;++S)A[m>>>2]|=h[S]<<U[m++&3];this.lastByteIndex=m,this.bytes+=m-this.start,m>=64?(this.block=A[16],this.start=m-64,this.hash(),this.hashed=!0):this.start=m}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},P.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var h=this.blocks,b=this.lastByteIndex;h[16]=this.block,h[b>>>2]|=O[b&3],this.block=h[16],b>=56&&(this.hashed||this.hash(),h[0]=this.block,h[16]=h[1]=h[2]=h[3]=h[4]=h[5]=h[6]=h[7]=h[8]=h[9]=h[10]=h[11]=h[12]=h[13]=h[14]=h[15]=0),h[14]=this.hBytes<<3|this.bytes>>>29,h[15]=this.bytes<<3,this.hash()}},P.prototype.hash=function(){var h=this.h0,b=this.h1,T=this.h2,k=this.h3,S=this.h4,m,I,A,G=this.blocks;for(I=16;I<80;++I)A=G[I-3]^G[I-8]^G[I-14]^G[I-16],G[I]=A<<1|A>>>31;for(I=0;I<20;I+=5)m=b&T|~b&k,A=h<<5|h>>>27,S=A+m+S+1518500249+G[I]<<0,b=b<<30|b>>>2,m=h&b|~h&T,A=S<<5|S>>>27,k=A+m+k+1518500249+G[I+1]<<0,h=h<<30|h>>>2,m=S&h|~S&b,A=k<<5|k>>>27,T=A+m+T+1518500249+G[I+2]<<0,S=S<<30|S>>>2,m=k&S|~k&h,A=T<<5|T>>>27,b=A+m+b+1518500249+G[I+3]<<0,k=k<<30|k>>>2,m=T&k|~T&S,A=b<<5|b>>>27,h=A+m+h+1518500249+G[I+4]<<0,T=T<<30|T>>>2;for(;I<40;I+=5)m=b^T^k,A=h<<5|h>>>27,S=A+m+S+1859775393+G[I]<<0,b=b<<30|b>>>2,m=h^b^T,A=S<<5|S>>>27,k=A+m+k+1859775393+G[I+1]<<0,h=h<<30|h>>>2,m=S^h^b,A=k<<5|k>>>27,T=A+m+T+1859775393+G[I+2]<<0,S=S<<30|S>>>2,m=k^S^h,A=T<<5|T>>>27,b=A+m+b+1859775393+G[I+3]<<0,k=k<<30|k>>>2,m=T^k^S,A=b<<5|b>>>27,h=A+m+h+1859775393+G[I+4]<<0,T=T<<30|T>>>2;for(;I<60;I+=5)m=b&T|b&k|T&k,A=h<<5|h>>>27,S=A+m+S-1894007588+G[I]<<0,b=b<<30|b>>>2,m=h&b|h&T|b&T,A=S<<5|S>>>27,k=A+m+k-1894007588+G[I+1]<<0,h=h<<30|h>>>2,m=S&h|S&b|h&b,A=k<<5|k>>>27,T=A+m+T-1894007588+G[I+2]<<0,S=S<<30|S>>>2,m=k&S|k&h|S&h,A=T<<5|T>>>27,b=A+m+b-1894007588+G[I+3]<<0,k=k<<30|k>>>2,m=T&k|T&S|k&S,A=b<<5|b>>>27,h=A+m+h-1894007588+G[I+4]<<0,T=T<<30|T>>>2;for(;I<80;I+=5)m=b^T^k,A=h<<5|h>>>27,S=A+m+S-899497514+G[I]<<0,b=b<<30|b>>>2,m=h^b^T,A=S<<5|S>>>27,k=A+m+k-899497514+G[I+1]<<0,h=h<<30|h>>>2,m=S^h^b,A=k<<5|k>>>27,T=A+m+T-899497514+G[I+2]<<0,S=S<<30|S>>>2,m=k^S^h,A=T<<5|T>>>27,b=A+m+b-899497514+G[I+3]<<0,k=k<<30|k>>>2,m=T^k^S,A=b<<5|b>>>27,h=A+m+h-899497514+G[I+4]<<0,T=T<<30|T>>>2;this.h0=this.h0+h<<0,this.h1=this.h1+b<<0,this.h2=this.h2+T<<0,this.h3=this.h3+k<<0,this.h4=this.h4+S<<0},P.prototype.hex=function(){this.finalize();var h=this.h0,b=this.h1,T=this.h2,k=this.h3,S=this.h4;return $[h>>>28&15]+$[h>>>24&15]+$[h>>>20&15]+$[h>>>16&15]+$[h>>>12&15]+$[h>>>8&15]+$[h>>>4&15]+$[h&15]+$[b>>>28&15]+$[b>>>24&15]+$[b>>>20&15]+$[b>>>16&15]+$[b>>>12&15]+$[b>>>8&15]+$[b>>>4&15]+$[b&15]+$[T>>>28&15]+$[T>>>24&15]+$[T>>>20&15]+$[T>>>16&15]+$[T>>>12&15]+$[T>>>8&15]+$[T>>>4&15]+$[T&15]+$[k>>>28&15]+$[k>>>24&15]+$[k>>>20&15]+$[k>>>16&15]+$[k>>>12&15]+$[k>>>8&15]+$[k>>>4&15]+$[k&15]+$[S>>>28&15]+$[S>>>24&15]+$[S>>>20&15]+$[S>>>16&15]+$[S>>>12&15]+$[S>>>8&15]+$[S>>>4&15]+$[S&15]},P.prototype.toString=P.prototype.hex,P.prototype.digest=function(){this.finalize();var h=this.h0,b=this.h1,T=this.h2,k=this.h3,S=this.h4;return[h>>>24&255,h>>>16&255,h>>>8&255,h&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,T>>>24&255,T>>>16&255,T>>>8&255,T&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,S>>>24&255,S>>>16&255,S>>>8&255,S&255]},P.prototype.array=P.prototype.digest,P.prototype.arrayBuffer=function(){this.finalize();var h=new ArrayBuffer(20),b=new DataView(h);return b.setUint32(0,this.h0),b.setUint32(4,this.h1),b.setUint32(8,this.h2),b.setUint32(12,this.h3),b.setUint32(16,this.h4),h};function ce(h,b){var T,k=V(h);if(h=k[0],k[1]){var S=[],m=h.length,I=0,A;for(T=0;T<m;++T)A=h.charCodeAt(T),A<128?S[I++]=A:A<2048?(S[I++]=192|A>>>6,S[I++]=128|A&63):A<55296||A>=57344?(S[I++]=224|A>>>12,S[I++]=128|A>>>6&63,S[I++]=128|A&63):(A=65536+((A&1023)<<10|h.charCodeAt(++T)&1023),S[I++]=240|A>>>18,S[I++]=128|A>>>12&63,S[I++]=128|A>>>6&63,S[I++]=128|A&63);h=S}h.length>64&&(h=new P(!0).update(h).array());var G=[],ge=[];for(T=0;T<64;++T){var ye=h[T]||0;G[T]=92^ye,ge[T]=54^ye}P.call(this,b),this.update(ge),this.oKeyPad=G,this.inner=!0,this.sharedMemory=b}ce.prototype=new P,ce.prototype.finalize=function(){if(P.prototype.finalize.call(this),this.inner){this.inner=!1;var h=this.array();P.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(h),P.prototype.finalize.call(this)}};var pe=ne();pe.sha1=pe,pe.sha1.hmac=te(),D?e.exports=pe:o.sha1=pe})()})(Ns);var Tn=Ns.exports;const kn=bn(Tn);function ys(){return"http://worksheet.hexinedu.com"}function St(){return"http://127.0.0.1:3000"}function xs(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const Et=async(e,s,t,a={},o=8e3)=>{try{return await Promise.race([e(),new Promise((v,C)=>setTimeout(()=>C(new Error("WebSocket请求超时，切换到HTTP")),o))])}catch{try{let C;return s==="get"?C=await Ut.get(t,{params:a}):s==="post"?C=await Ut.post(t,a):s==="delete"&&(C=await Ut.delete(t)),C.data}catch(C){throw new Error(`请求失败: ${C.message||"未知错误"}`)}}};function Cn(e,s,t,a){const o=[e,s,t,a].join(":");return kn(o)}function $n(){const e=he(""),s=he(""),t=he(""),a=bt({}),o=he(""),v=he("");let C="",D=null;const w=he("c:\\Temp"),$=bt({appKey:"",appSecret:""}),O=bt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),U=bt({show:!1,title:"",message:"",type:"error"}),H=he(""),R=he("junior"),N=he(null),K=he(!1),V=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],se=()=>Ke.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],ne=bt(se()),oe=async()=>{try{const n=await Q.getWatcherStatus();n.data&&n.data.watchDir&&(w.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${w.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},p=async()=>{var n,i,l;try{if(!$.appKey||!$.appSecret)throw new Error("未初始化app信息");const c=60,r=Date.now();let f;try{const q=window.Application.PluginStorage.getItem("token_info");q&&(f=JSON.parse(q))}catch(q){f=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${q.message}</span><br/>`}if(f&&f.access_token&&f.expired_time>r+c*1e3)return f.access_token;const u=$.appKey,x="1234567",E=Math.floor(Date.now()/1e3),M=Cn(u,x,$.appSecret,E),F=await Ut.get(ys()+"/api/open/account/v1/auth/token",{params:{app_key:u,app_nonstr:x,app_timestamp:E,app_signature:M}});if((i=(n=F.data)==null?void 0:n.data)!=null&&i.access_token){const q=F.data.data.access_token;let Y;if(F.data.data.expired_time){const ue=parseInt(F.data.data.expired_time);Y=r+ue*1e3,t.value+=`<span class="log-item info">token已更新，有效期${ue}秒</span><br/>`}else Y=r+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const re={access_token:q,expired_time:Y};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(re))}catch(ue){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${ue.message}</span><br/>`}return q}else throw new Error(((l=F.data)==null?void 0:l.message)||"获取access_token失败")}catch(c){throw t.value+=`<span class="log-item error">获取access_token失败: ${c.message}</span><br/>`,c}},te=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},P=()=>{const n=window.Application,i=n.Documents.Count;for(let l=1;l<=i;l++){const c=n.Documents.Item(l);if(c.DocID===o.value)return c}return null},ce=()=>{try{const n=P();if(!n)return{isValid:!1,message:"未找到当前文档"};let i="";try{i=n.Name||""}catch{try{i=m("getDocName")||""}catch{i=""}}if(i){const l=i.toLowerCase();return l.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:l.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},pe=async(n,i="",l=0,c=null)=>await be("添加批注",async()=>{const r=window.Application,f=P();let u;if(c)u=c;else{const x=f.ActiveWindow.Selection;if(!x||x.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;u=x.Range}if(i){const x=u.Text,E=u.Find;E.ClearFormatting(),E.Text=i,E.Forward=!0,E.Wrap=0;let M=0,F=[];const q=u.Start,Y=u.End;for(;E.Execute()&&(E.Found&&E.Parent.Start>=q&&E.Parent.End<=Y);){const re=E.Parent.Start,ue=E.Parent.End;if(F.some(we=>re===we.start&&ue===we.end)){const we=Math.min(ue,Y);if(we>=Y)break;E.Parent.SetRange(we,Y)}else{if(F.push({start:re,end:ue}),l===-1||M===l){const ve=f.Comments.Add(E.Parent,n);try{ve&&ve.Range&&ve.Range.ParagraphFormat&&(ve.Range.ParagraphFormat.Reset(),ve.Range.ParagraphFormat.LineSpacingRule=3,ve.Range.ParagraphFormat.LineSpacing=10)}catch(Ce){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${Ce.message}</span><br/>`}if(l!==-1&&M===l)return!0}M++;const we=Math.min(ue,Y);if(console.log("nextStart",we),we>=Y)break;const ct=f.Range(we,Y);E.Parent.SetRange(we,Y)}}return l!==-1&&M<=l?!1:l===-1&&M>0?!0:!(l===-1&&M===0)}else{const x=f.Comments.Add(u,n);try{x&&x.Range&&x.Range.ParagraphFormat&&(x.Range.ParagraphFormat.Reset(),x.Range.ParagraphFormat.LineSpacingRule=3,x.Range.ParagraphFormat.LineSpacing=10)}catch(E){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${E.message}</span><br/>`}return t.value+=`<span class="log-item success">已为${c?"指定范围":"选中内容"}添加批注: "${n}"</span><br/>`,!0}}),h=n=>n===0?"status-preparing":n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",b=n=>n===0?"准备中":n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"准备中",T=n=>{const i=Date.now()-n,l=Math.floor(i/1e3);return l<60?`${l}秒`:l<3600?`${Math.floor(l/60)}分${l%60}秒`:`${Math.floor(l/3600)}时${Math.floor(l%3600/60)}分`},k=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const l=P();if(l&&l.ContentControls)for(let c=1;c<=l.ContentControls.Count;c++)try{const r=l.ContentControls.Item(c);if(r&&r.Title&&(r.Title===`任务_${n}`||r.Title===`任务增强_${n}`||r.Title===`校对_${n}`)){const f=r.Title===`任务增强_${n}`||a[n].isEnhanced,u=r.Title===`校对_${n}`||a[n].isCheckTask;u?r.Title=`已停止校对_${n}`:f?r.Title=`已停止增强_${n}`:r.Title=`已停止_${n}`;const x=u?"校对":f?"增强":"普通";t.value+=`<span class="log-item info">已将${x}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(l){t.value+=`<span class="log-item warning">更新控件标题失败: ${l.message}</span><br/>`}let i=null;if(Z[n]&&Z[n].urlId){i=Z[n].urlId;try{try{const l=await Et(async()=>await Q.getUrlMonitorStatus(),"get",`${St()}/api/url/status`),r=(Array.isArray(l)?l:l.data?Array.isArray(l.data)?l.data:[]:[]).find(f=>f.urlId===i);r&&r.downloadedPath&&(a[n].resultFile=r.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${r.downloadedPath}</span><br/>`)}catch(l){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${l.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ke(i),delete Z[n]}catch(l){t.value+=`<span class="log-item warning">停止URL监控出错: ${l.message}，将重试</span><br/>`,delete Z[n],setTimeout(async()=>{try{i&&await Et(async()=>await Q.stopUrlMonitoring(i),"delete",`${St()}/api/url/monitor/${i}`)}catch(c){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${c.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(i){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${i.message}</span><br/>`,Z[n]&&delete Z[n]}},S=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const i=P();if(i&&i.ContentControls)for(let c=1;c<=i.ContentControls.Count;c++)try{const r=i.ContentControls.Item(c);if(r&&r.Title&&(r.Title===`任务_${n}`||r.Title===`任务增强_${n}`||r.Title===`校对_${n}`)){const f=r.Title===`任务增强_${n}`||a[n].isEnhanced,u=r.Title===`校对_${n}`||a[n].isCheckTask;u?r.Title=`已停止校对_${n}`:f?r.Title=`已停止增强_${n}`:r.Title=`已停止_${n}`,r.LockContents=!1;const x=u?"校对":f?"增强":"普通";t.value+=`<span class="log-item info">已将${x}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let l=null;if(Z[n]&&Z[n].urlId){l=Z[n].urlId;try{const c=await Q.getUrlMonitorStatus(),f=(Array.isArray(c)?c:c.data?Array.isArray(c.data)?c.data:[]:[]).find(u=>u.urlId===l);f&&f.downloadedPath&&(a[n].resultFile=f.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${f.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ke(l),delete Z[n]}catch(c){t.value+=`<span class="log-item warning">停止URL监控出错: ${c.message}，将重试</span><br/>`,delete Z[n]}}},m=n=>vn.onbuttonclick(n),I=()=>{try{e.value=m("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},A=()=>{P().ActiveWindow.Selection.Copy()},G=n=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),i.Close(),P().ActiveWindow.Activate()},ge=n=>{const i=window.Application.Documents.Add("",!1,0,!1);i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),i.Close(),P().ActiveWindow.Activate()},ye=n=>{try{const l=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,c=window.Application.Documents.Add("",!1,0,!1);c.Content.Paste();const r=`${l}\\${n}`;c.SaveAs2(r,12,"","",!1),c.Close(),P().ActiveWindow.Activate(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${r}.docx</span><br/>`}catch(i){throw t.value+=`<span class="log-item error">方式三保存失败: ${i.message}</span><br/>`,i}},Me=n=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),P().ActiveWindow.Activate()},We=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let i="method3";try{const l=await Q.getSaveMethod();if(l.success&&l.saveMethod){i=l.saveMethod;const c=i==="method1"?"方式一":i==="method2"?"方式二":i==="method3"?"方式三":"方式四";t.value+=`<span class="log-item info">使用保存方式: ${c}</span><br/>`}}catch(l){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式三: ${l.message}</span><br/>`}i==="method1"?(G(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):i==="method2"?(ge(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):i==="method3"?(ye(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):i==="method4"&&(Me(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${w.value}\\${n}.docx</span><br/>`),Q.associateFileWithClient(`${n}.docx`).then(l=>{l.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${l.message||"未知错误"}</span><br/>`}).catch(l=>{t.value+=`<span class="log-item warning">关联文件时出错: ${l.message}</span><br/>`})}catch(i){t.value+=`<span class="log-item error">保存文件失败: ${i.message}</span><br/>`}},De=he(null),Ie=bt([]),$e=new Set,Ae=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),Pe=async n=>{try{const i=n.slice(C.length);if(i.trim()){const l=Q.getClientId();if(!l){console.warn("无法获取客户端ID，跳过日志同步");return}const c=Ae(i);if(!c.trim()){C=n;return}await Q.sendRequest("logger","syncLog",{content:c,timestamp:new Date().toISOString(),clientId:l}),C=n}}catch(i){console.error("同步日志到服务端失败:",i)}},Be=()=>{Q.connect().then(()=>{oe()}).catch(i=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${i.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const i=[];for(const l in a)if(a.hasOwnProperty(l)){const c=a[l];(c.status===0||c.status===1)&&!c.terminated&&(i.includes(l)||i.push(l))}if(i.length>0){let l=!1;try{const c=P();if(c){const f=`taskpane_id_${c.DocID}`,u=window.Application.PluginStorage.getItem(f);if(u){const x=window.Application.GetTaskPane(u);x&&(l=x.Visible)}}}catch(c){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${c.message}</span><br/>`,l=!0}setTimeout(()=>{if(l){const c=`检测到 ${i.length} 个未完成的任务，是否继续？`;le(c).then(r=>{r?(t.value+=`<span class="log-item info">用户选择继续 ${i.length} 个进行中的任务 (by taskId)...</span><br/>`,Q.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:i}).then(f=>{f&&f.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${f.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(f==null?void 0:f.message)||"未知错误"}</span><br/>`}).catch(f=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${f.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',i.forEach(f=>{k(f)}),t.value+=`<span class="log-item success">${i.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(r=>{t.value+=`<span class="log-item error">弹窗错误: ${r.message}，默认停止任务（保留控件）</span><br/>`,i.forEach(f=>{k(f)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Q.setConnectionSuccessHandler(n),Q.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),Q.addEventListener("connection",i=>{i.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${i.reason||"未知"}, 代码: ${i.code||"N/A"}，将自动重连</span><br/>`)}),Q.addEventListener("watcher",i=>{var l,c;if(Ie.push(i),i.type,i.eventType==="uploadSuccess"){const r=(l=i.data)==null?void 0:l.file,f=r==null?void 0:r.replace(/\.docx$/,""),u=`${i.eventType}_${r}_${Date.now()}`;if($e.has(u)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${r}</span><br/>`;return}if($e.add(u),$e.size>100){const E=$e.values();$e.delete(E.next().value)}const x=f&&((c=a[f])==null?void 0:c.wordType);Ne(i,x)}else i.eventType&&Ne(i,null)}),Q.addEventListener("urlMonitor",i=>{Ie.push(i),i.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${i.eventType||i.action}</span><br/>`),i.eventType&&Ne(i,null)}),Q.addEventListener("health",i=>{}),Q.addEventListener("error",i=>{const l=i.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${l}</span><br/>`,console.error("WebSocket错误:",i)})},Z=bt({}),Ue=async(n,i,l=!1,c=5e3,r={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const f=await Q.startUrlMonitoring(n,c,{downloadOnSuccess:r.downloadOnSuccess!==void 0?r.downloadOnSuccess:!0,appKey:r.appKey,filename:r.filename,taskId:i}),u=f.success||(f==null?void 0:f.success),x=f.urlId||(f==null?void 0:f.urlId);if(u&&x){Z[i]={urlId:x,url:n,isResultUrl:l,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${x}</span><br/>`;try{await Q.startUrlChecking(x)}catch{}return x}else throw new Error("服务器返回失败")}catch(f){return t.value+=`<span class="log-item error">启动URL监控失败: ${f.message}</span><br/>`,null}},ke=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(Z).forEach(l=>{Z[l].urlId===n&&delete Z[l]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const i=await Et(async()=>await Q.stopUrlMonitoring(n),"delete",`${St()}/api/url/monitor/${n}`);return i&&(i.success||i!=null&&i.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(i){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${i.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await Et(async()=>await Q.stopUrlMonitoring(n),"delete",`${St()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},nt=async()=>{try{const n=await Et(async()=>await Q.getUrlMonitorStatus(),"get",`${St()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Ge=async n=>{try{return await Q.forceUrlCheck(n)}catch{return!1}},Ne=async(n,i)=>{var l;if(n.eventType==="uploadSuccess"){const c=n.data.file,r=c.replace(/\.docx$/,"");if(a[r]){if(a[r].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${r.substring(0,8)} 的上传通知</span><br/>`;return}if(a[r].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${r.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${c} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${i||a[r].wordType||"wps-analysis"}</span><br/>`,a[r].status=1,a[r].uploadSuccess=!0,await me(r,i||a[r].wordType||"wps-analysis")}}else if(n.eventType==="encryptedFileError"){const c=n.data.file,r=c.replace(/\.docx$/,"");if(a[r]){t.value+=`<span class="log-item error">文件加密错误: ${c}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[r].status=-1,a[r].errorMessage=n.data.message||"文件已加密，无法处理";try{const f=P();if(f&&f.ContentControls)for(let u=1;u<=f.ContentControls.Count;u++)try{const x=f.ContentControls.Item(u);if(x&&x.Title&&(x.Title===`任务_${r}`||x.Title===`任务增强_${r}`||x.Title===`校对_${r}`)){const E=x.Title===`任务增强_${r}`||a[r].isEnhanced,M=x.Title===`校对_${r}`||a[r].isCheckTask;M?x.Title=`异常校对_${r}`:E?x.Title=`异常增强_${r}`:x.Title=`异常_${r}`;const F=M?"校对":E?"增强":"普通";t.value+=`<span class="log-item info">已将${F}任务${r.substring(0,8)}控件标记为异常（文件加密）</span><br/>`;break}}catch{continue}}catch(f){t.value+=`<span class="log-item warning">更新控件标题失败: ${f.message}</span><br/>`}Te(r)}}else if(n.eventType==="uploadError"){const c=n.data.file,r=c.replace(/\.docx$/,"");if(a[r]){t.value+=`<span class="log-item error">文件上传错误: ${c}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[r].status=-1,a[r].errorMessage=n.data.message||"文件上传失败";try{const f=P();if(f&&f.ContentControls)for(let u=1;u<=f.ContentControls.Count;u++)try{const x=f.ContentControls.Item(u);if(x&&x.Title&&(x.Title===`任务_${r}`||x.Title===`任务增强_${r}`||x.Title===`校对_${r}`)){const E=x.Title===`任务增强_${r}`||a[r].isEnhanced,M=x.Title===`校对_${r}`||a[r].isCheckTask;M?x.Title=`异常校对_${r}`:E?x.Title=`异常增强_${r}`:x.Title=`异常_${r}`;const F=M?"校对":E?"增强":"普通";t.value+=`<span class="log-item info">已将${F}任务${r.substring(0,8)}控件标记为异常（文件上传失败）</span><br/>`;break}}catch{continue}}catch(f){t.value+=`<span class="log-item warning">更新控件标题失败: ${f.message}</span><br/>`}Te(r)}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:c,url:r,taskId:f,downloadedPath:u}=n.data,x=Object.keys(Z).filter(E=>Z[E].urlId===c);x.length>0&&x.forEach(E=>{u&&a[E]&&(a[E].resultFile=u,a[E].resultDownloaded=!0),delete Z[E]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:c,url:r,filePath:f,taskId:u}=n.data;if(!Object.values(Z).some(E=>E.urlId===c)&&u&&((l=a[u])!=null&&l.terminated))return;if(u&&a[u]&&a[u].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${c} 的文件下载通知</span><br/>`,c)try{await Q.stopUrlMonitoring(c),Z[u]&&delete Z[u]}catch{}return}if(u&&a[u]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${f}</span><br/>`,a[u].isCheckTask&&f.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${f}</span><br/>`;try{const M=window.Application.FileSystem.ReadFile(f),F=JSON.parse(M);if(await X(F,u)){a[u].status=2,t.value+=`<span class="log-item success">校对任务${u.substring(0,8)}已完成批注处理</span><br/>`;const Y=T(a[u].startTime);t.value+=`<span class="log-item success">校对任务${u.substring(0,8)}完成，总耗时${Y}</span><br/>`}}catch(E){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${E.message}</span><br/>`,E.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${f}</span><br/>`),a[u].status=-1,a[u].errorMessage=`JSON处理失败: ${E.message}`,t.value+=`<span class="log-item error">校对任务${u.substring(0,8)}处理失败</span><br/>`}}else{a[u].resultFile=f,a[u].resultDownloaded=!0;const E=T(a[u].startTime);t.value+=`<span class="log-item success">任务${u.substring(0,8)}完成，总耗时${E}</span><br/>`,await Ve(u)}Z[u]&&Z[u].urlId&&(ke(Z[u].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete Z[u])}else if(c){t.value+=`<span class="log-item info">URL文件已下载: ${f}</span><br/>`;const E=Object.keys(Z).filter(M=>{var F;return Z[M].urlId===c&&!((F=a[M])!=null&&F.terminated)});E.length>0&&E.forEach(async M=>{if(a[M]){if(t.value+=`<span class="log-item info">关联到任务: ${M.substring(0,8)}</span><br/>`,a[M].resultFile=f,a[M].resultDownloaded=!0,a[M].isCheckTask&&f.endsWith(".wps.json"))try{const q=window.Application.FileSystem.ReadFile(f),Y=JSON.parse(q);if(await X(Y,M)&&a[M].status===1){a[M].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const ue=T(a[M].startTime);t.value+=`<span class="log-item success">校对任务${M.substring(0,8)}完成，总耗时${ue}</span><br/>`}}catch(F){t.value+=`<span class="log-item error">处理校对JSON失败: ${F.message}</span><br/>`,a[M].status===1&&(a[M].status=-1,a[M].errorMessage=`JSON处理失败: ${F.message}`,t.value+=`<span class="log-item error">校对任务${M.substring(0,8)}处理失败</span><br/>`)}else if(a[M].status===1){a[M].status=2;const F=T(a[M].startTime);t.value+=`<span class="log-item success">任务${M.substring(0,8)}完成，总耗时${F}</span><br/>`}}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:c,url:r,error:f,taskId:u}=n.data;if(u&&a[u]&&a[u].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${u.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${f}</span><br/>`,u&&a[u]){a[u].status=-1,a[u].errorMessage=`下载失败: ${f}`;try{const x=P();if(x&&x.ContentControls)for(let E=1;E<=x.ContentControls.Count;E++)try{const M=x.ContentControls.Item(E);if(M&&M.Title&&(M.Title===`任务_${u}`||M.Title===`任务增强_${u}`||M.Title===`校对_${u}`)){const F=M.Title===`任务增强_${u}`||a[u].isEnhanced,q=M.Title===`校对_${u}`||a[u].isCheckTask;q?M.Title=`异常校对_${u}`:F?M.Title=`异常增强_${u}`:M.Title=`异常_${u}`;const Y=q?"校对":F?"增强":"普通";t.value+=`<span class="log-item info">已将${Y}任务${u.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(x){t.value+=`<span class="log-item warning">更新控件标题失败: ${x.message}</span><br/>`}Te(u),Z[u]&&delete Z[u]}if(c)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${c}</span><br/>`,await Et(async()=>await Q.stopUrlMonitoring(c),"delete",`${St()}/api/url/monitor/${c}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},me=async(n,i="wps-analysis")=>{try{if(!$.appKey)throw new Error("未初始化appKey信息");const l=await p(),c=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,r=await Ut.post(ys()+"/api/open/ticket/v1/ai_comment/create",{access_token:l,data:{app_key:$.appKey,subject:H.value,stage:R.value,file_name:`${n}`,word_url:c,word_type:i,is_ai_auto:!0,is_ai_edit:!0,create_user_id:$.userId,create_username:$.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),f=r.data.data.ticket_id;if(!f)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",Te(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${f}，开始监控结果文件</span><br/>`;let u,x;i==="wps-check"?(u=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${$.appKey}/ai/${f}.wps.json`,x=`${f}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${x}</span><br/>`):(u=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${f}.wps.docx`,x=`${f}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${x}</span><br/>`);const E={downloadOnSuccess:!0,filename:x,appKey:$.appKey,ticketId:f,taskId:n},M=await Ue(u,n,!0,3e3,E);return a[n]&&(a[n].ticketId=f,a[n].resultUrl=u,a[n].urlMonitorId=M,a[n].status=1),r.data}catch(l){return t.value+=`<span class="log-item error">API调用失败: ${l.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${l.message}`,Te(n)),!1}},pt=async(n,i="wps-analysis")=>new Promise((l,c)=>{try{t.value+=`<span class="log-item info">监控目录: ${w.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${i}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=i,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const r=1e3,f=10;let u=0;const x=setInterval(()=>{if(u++,a[n]&&a[n].uploadSuccess){clearInterval(x),l(!0);return}if(a[n]&&a[n].status===-1&&(clearInterval(x),t.value+=`<span class="log-item error">任务${n.substring(0,8)}已异常，停止等待上传完成</span><br/>`,c(new Error(a[n].errorMessage||"任务已异常"))),u>=f){clearInterval(x),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',Te(n);return}},r)}catch(r){t.value+=`<span class="log-item error">任务处理异常: ${r.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${r.message}`),Te(n),c(r)}}),Ze=async()=>{var l;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,i=n.ActiveDocument;if(o.value=i.DocID,v.value=n.ActiveWindow.Index,i!=null&&i.ContentControls)for(let c=1;c<=i.ContentControls.Count;c++){const r=i.ContentControls.Item(c);if(r&&r.Title){let f=null,u=1,x=!1,E=!1;if(r.Title.startsWith("任务增强_")?(f=r.Title.substring(5),u=1,x=!0):r.Title.startsWith("任务_")?(f=r.Title.substring(3),u=1):r.Title.startsWith("校对_")?(f=r.Title.substring(3),u=1,E=!0):r.Title.startsWith("已完成增强_")?(f=r.Title.substring(6),u=2,x=!0):r.Title.startsWith("已完成校对_")?(f=r.Title.substring(6),u=2,E=!0):r.Title.startsWith("已完成_")?(f=r.Title.substring(4),u=2):r.Title.startsWith("异常增强_")?(f=r.Title.substring(5),u=-1,x=!0):r.Title.startsWith("异常校对_")?(f=r.Title.substring(5),u=-1,E=!0):r.Title.startsWith("异常_")?(f=r.Title.substring(3),u=-1):r.Title.startsWith("已停止增强_")?(f=r.Title.substring(6),u=4,x=!0):r.Title.startsWith("已停止校对_")?(f=r.Title.substring(6),u=4,E=!0):r.Title.startsWith("已停止_")&&(f=r.Title.substring(4),u=4),f&&!a[f]){let M="";try{M=((l=r.Range)==null?void 0:l.Text)||""}catch{}let F=Date.now()-24*60*60*1e3;try{if(f.length===24){const re=f.substring(0,8),ue=parseInt(re,16);!isNaN(ue)&&ue>0&&ue<2147483647&&(F=ue*1e3)}else{const re=Date.now()-864e5;u===2?F=re-60*60*1e3:u===-1?F=re-30*60*1e3:u===4?F=re-45*60*1e3:F=re}}catch{}a[f]={status:u,startTime:F,contentControlId:r.ID,isEnhanced:x,isCheckTask:E,selectedText:M};const q=u===1?"进行中":u===2?"已完成":u===-1?"异常":u===4?"已停止":"未知",Y=E?"校对":x?"增强":"普通";t.value+=`<span class="log-item info">发现已有${Y}任务: ${f.substring(0,8)}, 状态: ${q}</span><br/>`}}}},Te=async(n,i=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}i&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const l=P();if(!l){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let c=!1;const r=a[n].isEnhanced;await be("删除内容控件",async()=>{if(l.ContentControls&&l.ContentControls.Count>0)for(let f=l.ContentControls.Count;f>=1;f--)try{const u=l.ContentControls.Item(f);u&&u.Title&&u.Title.includes(n)&&(u.LockContents=!1,u.Delete(!1),c=!0,console.log(u.Title),t.value+=`<span class="log-item success">已解锁并删除${r?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(u){t.value+=`<span class="log-item warning">访问第${f}个控件时出错: ${u.message}</span><br/>`;continue}}),c?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${r?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(l){t.value+=`<span class="log-item error">删除内容控件失败: ${l.message}</span><br/>`}},at=n=>{var i;try{const l=window.wps,c=P();if(!c||!c.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const r=(i=a[n])==null?void 0:i.isEnhanced;let f=null;for(let x=1;x<=c.ContentControls.Count;x++)try{const E=c.ContentControls.Item(x);if(E&&E.Title&&E.Title.includes(n)){f=E;break}}catch{continue}if(!f){const x=a[n];x&&(x.status===2||x.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}f.Range.Select();const u=l.Windows.Item(v.value);u&&u.Selection&&u.Selection.Range&&u.ScrollIntoView(u.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${r?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(l){t.value+=`<span class="log-item error">跳转到任务控件失败: ${l.message}</span><br/>`}},Ve=async n=>{const i=P(),l=a[n];if(!l){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(l.terminated||l.status!==1)return;if(l.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const c=P();let r=null;if(c&&c.ContentControls)for(let u=1;u<=c.ContentControls.Count;u++){const x=c.ContentControls.Item(u);if(x&&x.Title&&x.Title.includes(n)){r=x;break}}if(!r){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,l.errorMessage&&l.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${l.errorMessage}</span><br/>`;return}return}if(l.errorMessage&&l.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${l.errorMessage}</span><br/>`,Te(n);return}if(!l.resultFile)return;a[n].documentInserted=!0;const f=r.Range;r.LockContents=!1;try{await be("插入结果文件",async()=>{var Ee,we,ct;t.value+=`<span class="log-item info">正在自动插入结果文件${l.resultFile}...</span><br/>`;const M=i.Range(f.End,f.End);M.InsertParagraphAfter(),f.Text.includes("\v")&&M.InsertAfter("\v");let F=M.End;const q=(Ee=f.Tables)==null?void 0:Ee.Count;if(q){const ve=f.Tables.Item(q);((we=ve==null?void 0:ve.Range)==null?void 0:we.End)>F&&(ve.Range.InsertParagraphAfter(),F=(ct=ve==null?void 0:ve.Range)==null?void 0:ct.End)}i.Range(F,F).InsertFile(l.resultFile);for(let ve=1;ve<=c.ContentControls.Count;ve++){const Ce=c.ContentControls.Item(ve);if(Ce&&Ce.Title&&(Ce.Title===`任务_${n}`||Ce.Title===`任务增强_${n}`)){r=Ce;break}}const re=i.Range(r.Range.End-1,r.Range.End);re.Text.includes("\r")&&re.Delete()}),a[n].status=2,Z[n]&&Z[n].urlId&&Q.sendRequest("urlMonitor","updateTaskStatus",{urlId:Z[n].urlId,status:"completed",taskId:n}).then(M=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(M=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${M.message}</span><br/>`});const u=T(l.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${u}</span><br/>`;const x=r.Title===`任务增强_${n}`||l.isEnhanced,E=r.Title===`校对_${n}`||l.isCheckTask;if(r){E?r.Title=`已完成校对_${n}`:x?r.Title=`已完成增强_${n}`:r.Title=`已完成_${n}`;const M=E?"校对":x?"增强":"普通";t.value+=`<span class="log-item info">已将${M}任务${n.substring(0,8)}控件标记为已完成</span><br/>`}}catch(u){a[n].documentInserted=!1,a[n].status=-1;const x=r.Title===`任务增强_${n}`||l.isEnhanced,E=r.Title===`校对_${n}`||l.isCheckTask;if(r){E?r.Title=`异常校对_${n}`:x?r.Title=`异常增强_${n}`:r.Title=`异常_${n}`;const M=E?"校对":x?"增强":"普通";t.value+=`<span class="log-item info">已将${M}任务${n.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${u.message}</span><br/>`}},Le=async()=>{const n=(c=1e3)=>new Promise(r=>{setTimeout(()=>r(),c)}),i=new Map,l=Object.keys(a).filter(c=>a[c].status===1&&!a[c].terminated);for(l.length?(l.forEach(c=>{i.has(c)||i.set(c,Date.now())}),await Promise.all(l.map(c=>Ve(c)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const c=Object.keys(a).filter(r=>a[r].status===1&&!a[r].terminated);c.forEach(r=>{i.has(r)||i.set(r,Date.now());const f=i.get(r);(Date.now()-f)/1e3/60>=5e4&&a[r]&&!a[r].terminated&&(a[r].terminated=!0,a[r].status=-1,t.value+=`<span class="log-item warning">任务 ${r} 执行超过5分钟，已自动终止</span><br/>`,i.delete(r))}),c.length&&await Promise.all(c.map(r=>Ve(r)))}},Fe=async(n="wps-analysis",i=null)=>{const l=y();if(l){const{primary:r,all:f}=l,{taskId:u,control:x,task:E,isEnhanced:M,overlapType:F}=r,q=f.filter(Y=>Y.task&&(Y.task.status===0||Y.task.status===1));if(q.length>0){const Y=q.map(re=>re.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${Y})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${f.length}个已有任务重叠，重叠类型: ${F}</span><br/>`,L();try{await be("删除重叠控件",async()=>{for(const Y of f){const{taskId:re,control:ue,task:Ee,isEnhanced:we}=Y;t.value+=`<span class="log-item info">删除重叠的${we?"增强":"普通"}任务 ${re.substring(0,8)}，状态为 ${b((Ee==null?void 0:Ee.status)||0)}</span><br/>`,Ee&&(Ee.status=3,Ee.terminated=!0,Ee.errorMessage="用户重新创建任务时删除");try{ue.LockContents=!1,ue.Delete(!1),a[re]&&(a[re].placeholderRemoved=!0)}catch(ct){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ct.message}</span><br/>`}}}),t.value+=`<span class="log-item success">已删除${f.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(Y){return z(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${Y.message}</span><br/>`,Promise.reject(Y)}z()}const c=xs().replace(/-/g,"").substring(0,8);return new Promise(async(r,f)=>{try{const u=window.wps,x=P(),E=x.ActiveWindow.Selection,M=E.Range,F=E.Text||"";if(s.value=F,A(),M){const q=M.Paragraphs,Y=q.Item(1),re=q.Item(q.Count),ue=Y.Range.Start,Ee=re.Range.End,we=M.Start,ct=M.End,ve=x.Range(Math.min(ue,we),Math.max(Ee,ct));await be("创建内容控件",async()=>{let Ce=x.ContentControls.Add(u.Enum.wdContentControlRichText,ve);if(!Ce){if(console.log("创建内容控件失败"),Ce=x.ContentControls.Add(u.Enum.wdContentControlRichText),!Ce)throw t.value+='<span class="log-item error">创建内容控件失败</span><br/>',new Error("创建内容控件失败");M.Cut(),Ce.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const Qt=n==="wps-enhance_analysis";Ce.Title=Qt?`任务增强_${c}`:`任务_${c}`,Ce.LockContents=!0,a[c]||(a[c]={}),a[c].contentControlId=Ce.ID,a[c].isEnhanced=Qt,t.value+=`<span class="log-item info">已创建${Qt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const ln=Ce.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${ln.length}</span><br/>`})}a[c]={status:0,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:F},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${c}，类型: ${n}</span><br/>`,await We(c),i&&i(),a[c]&&(a[c].status=1);try{await pt(c,n)?r():(a[c]&&a[c].status===1&&(a[c].status=-1,a[c].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${c.substring(0,8)}失败</span><br/>`,ie(c)),f(new Error("API调用失败或超时")))}catch(q){a[c]&&(a[c].status=-1,a[c].errorMessage=`执行错误: ${q.message}`,t.value+=`<span class="log-item error">任务${c.substring(0,8)}执行出错: ${q.message}</span><br/>`,ie(c)),f(q)}}catch(u){f(u)}})},rt=async(n=null)=>{const i=y();if(i){const{primary:c,all:r}=i,{taskId:f,control:u,task:x,isEnhanced:E,overlapType:M}=c,F=r.filter(q=>q.task&&(q.task.status===0||q.task.status===1));if(F.length>0){const q=F.map(Y=>Y.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${q})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${r.length}个已有任务重叠，重叠类型: ${M}</span><br/>`,L();try{await be("删除重叠校对控件",async()=>{for(const q of r){const{taskId:Y,control:re,task:ue,isEnhanced:Ee}=q;t.value+=`<span class="log-item info">删除重叠的校对任务 ${Y.substring(0,8)}，状态为 ${b((ue==null?void 0:ue.status)||0)}</span><br/>`,ue&&(ue.status=3,ue.terminated=!0,ue.errorMessage="用户重新创建校对任务时删除");try{re.LockContents=!1,re.Delete(!1),a[Y]&&(a[Y].placeholderRemoved=!0)}catch(we){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${we.message}</span><br/>`}}}),t.value+=`<span class="log-item success">已删除${r.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(q){return z(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${q.message}</span><br/>`,Promise.reject(q)}z()}const l=xs().replace(/-/g,"").substring(0,8);return new Promise(async(c,r)=>{try{const f=window.wps,u=P(),x=u.ActiveWindow.Selection,E=x.Range,M=x.Text||"";if(s.value=M,A(),E){const F=E.Paragraphs,q=F.Item(1),Y=F.Item(F.Count),re=q.Range.Start,ue=Y.Range.End,Ee=E.Start,we=E.End,ct=u.Range(Math.min(re,Ee),Math.max(ue,we));await be("创建校对控件",async()=>{let ve=u.ContentControls.Add(f.Enum.wdContentControlRichText,ct);if(!ve){if(console.log("创建校对内容控件失败"),ve=u.ContentControls.Add(f.Enum.wdContentControlRichText),!ve)throw t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',new Error("创建校对内容控件失败");E.Cut(),ve.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',ve.Title=`校对_${l}`,ve.LockContents=!0,a[l]||(a[l]={}),a[l].contentControlId=ve.ID,a[l].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const Ce=ve.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${Ce.length}</span><br/>`})}a[l]={status:0,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:M},t.value+=`<span class="log-item success">创建校对任务: ${l}，类型: wps-check</span><br/>`,await We(l),n&&n(),a[l]&&(a[l].status=1);try{await pt(l,"wps-check")?c():(a[l]&&a[l].status===1&&(a[l].status=-1,a[l].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${l.substring(0,8)}失败</span><br/>`,ie(l)),r(new Error("校对API调用失败或超时")))}catch(F){a[l]&&(a[l].status=-1,a[l].errorMessage=`校对执行错误: ${F.message}`,t.value+=`<span class="log-item error">校对任务${l.substring(0,8)}执行出错: ${F.message}</span><br/>`,ie(l)),r(F)}}catch(f){r(f)}})},Je=async()=>{},ot=()=>lt()>0?"status-error":Qe()>0?"status-running":it()>0?"status-completed":"",Qe=()=>Object.values(a).filter(n=>n.status===0||n.status===1).length,it=()=>Object.values(a).filter(n=>n.status===2).length,ft=()=>Object.values(a).filter(n=>n.status===2||n.status===4).length,lt=()=>Object.values(a).filter(n=>n.status===-1).length,Rt=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,i=P();if(!i){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let l=0;if(Object.keys(a).forEach(c=>{try{a[c].status===1&&(a[c].status=3,a[c].terminated=!0,a[c].errorMessage="强制清除"),Te(c),l++}catch(r){t.value+=`<span class="log-item warning">清除任务${c.substring(0,8)}失败: ${r.message}</span><br/>`}}),i.ContentControls&&i.ContentControls.Count>0)for(let c=i.ContentControls.Count;c>=1;c--)try{const r=i.ContentControls.Item(c);if(r&&r.Title&&(r.Title.startsWith("任务_")||r.Title.startsWith("任务增强_")||r.Title.startsWith("已完成_")||r.Title.startsWith("已完成增强_")||r.Title.startsWith("异常_")||r.Title.startsWith("异常增强_")||r.Title.startsWith("已停止_")||r.Title.startsWith("已停止增强_")))try{r.LockContents=!1,r.Delete(!1);let f;r.Title.startsWith("任务增强_")?f=r.Title.substring(5):r.Title.startsWith("任务_")?f=r.Title.substring(3):r.Title.startsWith("已完成增强_")?f=r.Title.substring(6):r.Title.startsWith("已完成_")?f=r.Title.substring(4):r.Title.startsWith("异常增强_")?f=r.Title.substring(5):r.Title.startsWith("异常_")?f=r.Title.substring(3):r.Title.startsWith("已停止增强_")?f=r.Title.substring(6):r.Title.startsWith("已停止_")&&(f=r.Title.substring(4)),a[f]?(a[f].placeholderRemoved=!0,a[f].status=3):a[f]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},l++,t.value+=`<span class="log-item success">已删除任务${f.substring(0,8)}的内容控件</span><br/>`}catch(f){t.value+=`<span class="log-item error">删除控件失败: ${f.message}</span><br/>`}}catch(r){t.value+=`<span class="log-item warning">访问控件时出错: ${r.message}</span><br/>`}l>0?t.value+=`<span class="log-item success">已清除${l}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},vt=async()=>{try{const n=Object.values(Z).map(i=>i.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const i of n)await ke(i)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Ct=()=>{Ws(async()=>{try{const i=window.Application.PluginStorage.getItem("user_info");if(!i)throw new Error("未找到用户信息");const l=JSON.parse(i);if(!l.orgs||!l.orgs[0])throw new Error("未找到组织信息");$.appKey=l.appKey,$.userName=l.nickname,$.userId=l.userId,$.appSecret=l.appSecret}catch(i){t.value+=`<span class="log-item error">初始化appKey失败: ${i.message}</span><br/>`}await oe(),Yt([H,R],async()=>{await B()},{immediate:!1}),await g();const n=Ke.onVersionChange(()=>{const i=se();ne.splice(0,ne.length,...i),Ke.isSeniorEdition()&&R.value==="junior"?R.value="senior":!Ke.isSeniorEdition()&&R.value==="senior"&&(R.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Ke.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',I(),await Ze(),Be(),$t(),Le(),window.addEventListener("beforeunload",vt),()=>{D&&clearTimeout(D),n&&n()}}),Yt(t,n=>{D&&clearTimeout(D),D=setTimeout(()=>{Pe(n)},10)},{immediate:!1})},$t=()=>{Q.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==H.value&&(H.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${H.value}</span><br/>`),n.data.stage!==R.value&&(R.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${R.value}</span><br/>`))})},mt=he(!1),y=()=>{try{const n=P(),i=n.ActiveWindow.Selection;if(!i||!n||!n.ContentControls)return null;const l=i.Range,c=[];for(let r=1;r<=n.ContentControls.Count;r++)try{const f=n.ContentControls.Item(r);if(!f)continue;const u=(f.Title||"").trim(),x=f.Range;if(l.Start<x.End&&l.End>x.Start){let E=null,M=!1,F=!1;if(!u)F=!0,E=`empty_${f.ID||Date.now()}`;else if(u.startsWith("任务_")||u.startsWith("任务增强_")||u.startsWith("校对_")||u.startsWith("已完成_")||u.startsWith("已完成增强_")||u.startsWith("已完成校对_")||u.startsWith("异常_")||u.startsWith("异常增强_")||u.startsWith("异常校对_")||u.startsWith("已停止_")||u.startsWith("已停止增强_")||u.startsWith("已停止校对_"))u.startsWith("任务增强_")?(E=u.substring(5),M=!0):u.startsWith("任务_")||u.startsWith("校对_")?E=u.substring(3):u.startsWith("已完成增强_")?(E=u.substring(6),M=!0):u.startsWith("已完成校对_")?E=u.substring(6):u.startsWith("已完成_")?E=u.substring(4):u.startsWith("异常增强_")?(E=u.substring(5),M=!0):u.startsWith("异常校对_")?E=u.substring(5):u.startsWith("异常_")?E=u.substring(3):u.startsWith("已停止增强_")?(E=u.substring(6),M=!0):u.startsWith("已停止校对_")?E=u.substring(6):u.startsWith("已停止_")&&(E=u.substring(4));else continue;if(E){let q;l.Start>=x.Start&&l.End<=x.End?q="completely_within":l.Start<=x.Start&&l.End>=x.End?q="completely_contains":q="partial_overlap",c.push({taskId:E,control:f,task:F?null:a[E]||null,isEnhanced:M,isEmptyTitle:F,overlapType:q,controlRange:{start:x.Start,end:x.End},selectionRange:{start:l.Start,end:l.End}})}}}catch{continue}return c.length===0?null:{primary:c[0],all:c}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},L=()=>{mt.value=!0},z=()=>{mt.value=!1},ie=async(n,i=!1)=>{L();try{await Te(n,i)}finally{z()}},le=n=>new Promise((i,l)=>{O.show=!0,O.message=n,O.resolveCallback=i,O.rejectCallback=l}),xe=n=>{O.show=!1,n&&O.resolveCallback?O.resolveCallback(!0):O.resolveCallback&&O.resolveCallback(!1),O.resolveCallback=null,O.rejectCallback=null},Se=(n,i,l="error")=>{U.show=!0,U.title=n,U.message=i,U.type=l},_=()=>{U.show=!1,U.title="",U.message="",U.type="error"},g=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await Q.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(H.value=n.data.subject),n.data.stage&&(R.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${H.value})和年级(${R.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},B=async()=>{try{if(H.value||R.value){t.value+=`<span class="log-item info">正在保存学科(${H.value})和年级(${R.value})设置到服务器...</span><br/>`;const n=await Q.setSubjectAndStage(H.value,R.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}},ee=async()=>{try{N.value=2,K.value=N.value===2,K.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${N.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${N.value}）</span><br/>`}catch(n){console.error("检查企业ID失败:",n),t.value+=`<span class="log-item error">检查企业ID失败: ${n.message}</span><br/>`,K.value=!1}},fe=async(n,i)=>await be("为范围添加批注",async()=>{const l=P();if(!l||!n)return!1;const c=l.Comments.Add(n,i);try{c!=null&&c.Range&&(c.Range.ParagraphFormat.Reset(),c.Range.ParagraphFormat.LineSpacingRule=3,c.Range.ParagraphFormat.LineSpacing=10,c.Edit())}catch(r){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${r.message}</span><br/>`}return!0}),X=async(n,i)=>{try{if(!n||!Array.isArray(n))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const l=P();let c=null,r=null;if(l&&l.ContentControls)for(let E=1;E<=l.ContentControls.Count;E++)try{const M=l.ContentControls.Item(E);if((M==null?void 0:M.Title)===`校对_${i}`){c=M,r=M.Range,t.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!r){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const E=a[i];if(E&&E.selectedText)try{const M=l.Range().Find;if(M.ClearFormatting(),M.Text=E.selectedText.substring(0,Math.min(E.selectedText.length,100)),M.Forward=!0,M.Wrap=1,M.Execute()){const F=M.Parent;if(E.selectedText.length>100){const q=F.Start+E.selectedText.length;r=l.Range(F.Start,Math.min(q,l.Range().End))}else r=F;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(M){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${M.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let f=0,u=0,x=[];for(const E of n){if(!E.mode1||!Array.isArray(E.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const M of E.mode1){if(!M.error_info||!Array.isArray(M.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const F=M.quest_html||"",q=M.quest_type||"";t.value+=`<span class="log-item info">处理${q}题目，发现${M.error_info.length}个错误信息</span><br/>`;for(const Y of M.error_info)try{let re="";if(Y.error_info&&(re+=`【错误类型】${Y.error_info}\r`),Y.fix_info&&(re+=`【建议】${Y.fix_info}`),Y.keywords&&Y.keywords.trim()){let ue=c?c.Range:r;c.LockContents=!1,pe(re,Y.keywords.trim(),0,c.Range)?(f++,t.value+=`<span class="log-item success">已为关键词"${Y.keywords.trim()}"添加批注: ${re}</span><br/>`):(x.push({comment:re,keyword:Y.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${Y.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else x.push({comment:re,keyword:null})}catch(re){u++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${re.message}</span><br/>`}}}if(x.length>0){t.value+=`<span class="log-item info">为整个控件范围添加${x.length}个批注</span><br/>`;for(const E of x)try{let M=E.comment,F=c?c.Range:r;c.LockContents=!1,fe(c.Range,M)?(f++,t.value+=`<span class="log-item success">已为整个范围添加批注${E.keyword?`（关键词：${E.keyword}）`:""}</span><br/>`):u++}catch(M){u++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${M.message}</span><br/>`}}return f>0?(t.value+=`<span class="log-item success">校对任务${i.substring(0,8)}处理完成：成功添加${f}个批注</span><br/>`,u>0&&(t.value+=`<span class="log-item warning">校对任务${i.substring(0,8)}：${u}个批注添加失败</span><br/>`),c.Title=`已完成校对_${i}`,!0):(t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(l){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${l.message}</span><br/>`,!1}},be=async(n,i)=>{const l=window.Application;if(!l)throw new Error("无法获取 Application 对象");l.ScreenUpdating;try{l.ScreenUpdating=!1;const c=P();return c&&c.UndoRecord&&c.UndoRecord.StartCustomRecord(n),await i()}catch(c){throw t.value+=`<span class="log-item error">${n}操作失败: ${c.message}</span><br/>`,c}finally{try{const c=P();c&&c.UndoRecord&&c.UndoRecord.EndCustomRecord()}catch(c){t.value+=`<span class="log-item warning">清理VBA操作状态时出错: ${c.message}</span><br/>`}}};return{docName:e,selected:s,logger:t,map:a,watchedDir:w,subject:H,stage:R,subjectOptions:V,stageOptions:ne,fetchWatchedDir:oe,clearLog:te,getCurrentDocument:P,checkDocumentFormat:ce,getTaskStatusClass:h,getTaskStatusText:b,getElapsedTime:T,terminateTask:S,stopTaskWithoutRemovingControl:k,run1:Fe,run2:Je,runCheck:rt,getHeaderStatusClass:ot,getRunningTasksCount:Qe,getCompletedTasksCount:it,getReleasableTasksCount:ft,getErrorTasksCount:lt,setupLifecycle:Ct,navigateToTaskControl:at,forceCleanAllTasks:Rt,ws:De,wsMessages:Ie,initWebSocket:Be,handleWatcherEvent:Ne,urlMonitorTasks:Z,monitorUrlForTask:Ue,stopUrlMonitoring:ke,getUrlMonitorStatus:nt,forceUrlCheck:Ge,cleanupUrlMonitoringTasks:vt,tryRemoveTaskPlaceholder:Te,isLoading:mt,isSelectionInTaskControl:y,tryRemoveTaskPlaceholderWithLoading:ie,showConfirm:le,handleConfirm:xe,confirmDialog:O,errorDialog:U,showErrorDialog:Se,hideErrorDialog:_,loadSubjectAndStage:g,saveSubjectAndStage:B,enterpriseId:N,isCheckingVisible:K,checkEnterpriseAndSetCheckingVisibility:ee,processCheckingJson:X}}const Sn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Ke.getVersionConfig(),selectedEdition:Ke.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method3",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),o=Math.floor(t%(1e3*60*60)/(1e3*60)),v=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${o}分 ${v}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Q.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Q[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Q.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Q.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Q.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Q.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Q.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Q.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Q.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Ke.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await un()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Q.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Q.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Q.sendRequest("config","getSaveMethod");e.success&&e.saveMethod?(this.currentSaveMethod=e.saveMethod,e.saveMethod!=="method3"&&(console.log(`当前保存方式为${e.saveMethod}，自动切换到方式三`),await this.setSaveMethodToThree())):(console.log("未检测到保存方式设置，自动设置为方式三"),await this.setSaveMethodToThree())}catch(e){console.error("获取保存方式设置失败:",e),await this.setSaveMethodToThree()}},async setSaveMethodToThree(){try{const e=await Q.sendRequest("config","setSaveMethod",{saveMethod:"method3"});e.success?(this.currentSaveMethod="method3",console.log("已自动设置保存方式为方式三")):console.error("自动设置保存方式为方式三失败:",e.message)}catch(e){console.error("自动设置保存方式失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Q.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Q.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Q.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Q.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Ke.onVersionChange(e=>{this.versionConfig=Ke.getVersionConfig(),this.selectedEdition=Ke.getEdition()}),await Q.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},En={class:"file-watcher"},_n={class:"settings-modal"},An={class:"modal-content"},Mn={class:"modal-header"},Dn={class:"version-tag"},Pn={key:0,class:"user-info"},On={key:1,class:"user-info inner-tag"},Rn={class:"header-actions"},In={class:"modal-body"},Un={class:"status-section"},Ln={class:"status-item"},Fn={key:0,class:"status-item"},jn={class:"directory-section"},Wn={class:"directory-form"},Bn={class:"radio-group"},Nn={class:"radio-item"},Vn={class:"radio-item"},Hn={class:"radio-item"},zn={key:0,class:"radio-item"},qn={key:1,class:"directory-section"},Jn={class:"directory-form"},Yn={class:"form-group"},Kn=["placeholder"],Xn=["disabled"],Gn={key:2,class:"directory-section"},Zn={class:"directory-form"},Qn={class:"form-group"},ea=["placeholder"],ta=["disabled"],sa={key:3,class:"directory-section"},na={class:"directory-form"},aa={class:"form-group"},ra=["placeholder"],oa=["disabled"],ia={key:4,class:"events-section"},la={class:"events-list"},ca={class:"event-time"},ua={class:"event-message"},da={key:1,class:"modal-footer"};function pa(e,s,t,a,o,v){var C,D,w,$,O,U,H,R,N,K,V,se,ne,oe,p,te,P,ce,pe,h,b,T,k,S;return j(),W("div",En,[d("div",_n,[d("div",An,[d("div",Mn,[d("h3",null,[dn(ae(o.versionConfig.shortName)+"设置 ",1),d("span",Dn,ae(o.versionConfig.appVersion),1),v.userInfo?(j(),W("span",Pn,"欢迎您，"+ae(v.userInfo.nickname),1)):J("",!0),((w=(D=(C=v.userInfo)==null?void 0:C.orgs)==null?void 0:D[0])==null?void 0:w.orgId)===2?(j(),W("span",On,"内部版本号：1.1.26")):J("",!0)]),d("div",Rn,[d("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>v.handleLogout&&v.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[d("span",{class:"icon-logout"},null,-1)])),d("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),d("div",In,[d("div",Un,[d("div",Ln,[s[22]||(s[22]=d("span",{class:"label"},"状态：",-1)),d("span",{class:_e(["status-badge",o.status.status])},ae(o.status.status==="running"?"运行中":"已停止"),3)]),((U=(O=($=v.userInfo)==null?void 0:$.orgs)==null?void 0:O[0])==null?void 0:U.orgId)===2?(j(),W("div",Fn,[s[23]||(s[23]=d("span",{class:"label"},"本次上传：",-1)),d("span",null,ae(o.status.processedFiles||0)+" 个文件",1)])):J("",!0),d("div",jn,[s[28]||(s[28]=d("h4",null,"保存方式设置",-1)),d("div",Wn,[d("div",Bn,[d("label",Nn,[Ye(d("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>o.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,o.currentSaveMethod]]),s[24]||(s[24]=d("span",{class:"radio-label"},"方式一",-1))]),d("label",Vn,[Ye(d("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>o.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,o.currentSaveMethod]]),s[25]||(s[25]=d("span",{class:"radio-label"},"方式二",-1))]),d("label",Hn,[Ye(d("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>o.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,o.currentSaveMethod]]),s[26]||(s[26]=d("span",{class:"radio-label"},"方式三 (默认)",-1))]),((N=(R=(H=v.userInfo)==null?void 0:H.orgs)==null?void 0:R[0])==null?void 0:N.orgId)===2?(j(),W("label",zn,[Ye(d("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>o.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,o.currentSaveMethod]]),s[27]||(s[27]=d("span",{class:"radio-label"},"方式四",-1))])):J("",!0)]),o.saveMethodUpdateMessage?(j(),W("div",{key:0,class:_e(["update-message",o.saveMethodUpdateSuccess?"success":"error"])},ae(o.saveMethodUpdateMessage),3)):J("",!0)])])]),J("",!0),((se=(V=(K=v.userInfo)==null?void 0:K.orgs)==null?void 0:V[0])==null?void 0:se.orgId)===2?(j(),W("div",qn,[s[32]||(s[32]=d("h4",null,"上传目录设置",-1)),d("div",Jn,[d("div",Yn,[s[31]||(s[31]=d("span",{class:"label"},"路径：",-1)),Ye(d("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>o.newWatchDir=m),placeholder:o.status.watchDir||"C:\\Temp"},null,8,Kn),[[es,o.newWatchDir]]),d("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>v.updateWatchDir&&v.updateWatchDir(...m)),disabled:o.isUpdating||!o.newWatchDir},ae(o.isUpdating?"更新中...":"更新目录"),9,Xn)]),o.updateMessage?(j(),W("div",{key:0,class:_e(["update-message",o.updateSuccess?"success":"error"])},ae(o.updateMessage),3)):J("",!0)])])):J("",!0),((p=(oe=(ne=v.userInfo)==null?void 0:ne.orgs)==null?void 0:oe[0])==null?void 0:p.orgId)===2?(j(),W("div",Gn,[s[34]||(s[34]=d("h4",null,"下载目录设置",-1)),d("div",Zn,[d("div",Qn,[s[33]||(s[33]=d("span",{class:"label"},"路径：",-1)),Ye(d("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>o.newDownloadPath=m),placeholder:o.downloadPath||"C:\\Temp\\Downloads"},null,8,ea),[[es,o.newDownloadPath]]),d("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>v.updateDownloadPath&&v.updateDownloadPath(...m)),disabled:o.isUpdatingDownloadPath||!o.newDownloadPath},ae(o.isUpdatingDownloadPath?"更新中...":"更新路径"),9,ta)]),o.downloadPathUpdateMessage?(j(),W("div",{key:0,class:_e(["update-message",o.downloadPathUpdateSuccess?"success":"error"])},ae(o.downloadPathUpdateMessage),3)):J("",!0)])])):J("",!0),((ce=(P=(te=v.userInfo)==null?void 0:te.orgs)==null?void 0:P[0])==null?void 0:ce.orgId)===2?(j(),W("div",sa,[s[36]||(s[36]=d("h4",null,"配置目录设置",-1)),d("div",na,[d("div",aa,[s[35]||(s[35]=d("span",{class:"label"},"路径：",-1)),Ye(d("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>o.newAddonConfigPath=m),placeholder:o.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,ra),[[es,o.newAddonConfigPath]]),d("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>v.updateAddonConfigPath&&v.updateAddonConfigPath(...m)),disabled:o.isUpdatingAddonConfigPath||!o.newAddonConfigPath},ae(o.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,oa)]),o.addonConfigPathUpdateMessage?(j(),W("div",{key:0,class:_e(["update-message",o.addonConfigPathUpdateSuccess?"success":"error"])},ae(o.addonConfigPathUpdateMessage),3)):J("",!0)])])):J("",!0),((b=(h=(pe=v.userInfo)==null?void 0:pe.orgs)==null?void 0:h[0])==null?void 0:b.orgId)===2?(j(),W("div",ia,[s[37]||(s[37]=d("h4",null,"最近事件",-1)),d("div",la,[(j(!0),W(wt,null,_t(o.recentEvents,(m,I)=>(j(),W("div",{key:I,class:"event-item"},[d("span",ca,ae(v.formatTime(m.timestamp)),1),d("span",{class:_e(["event-type",m.eventType])},ae(v.getEventTypeText(m.eventType)),3),d("span",ua,ae(v.getEventMessage(m)),1)]))),128))])])):J("",!0)]),J("",!0),((S=(k=(T=v.userInfo)==null?void 0:T.orgs)==null?void 0:k[0])==null?void 0:S.orgId)===2?(j(),W("div",da,[d("button",{class:_e(["control-btn",o.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>v.controlService&&v.controlService(...m))},ae(o.status.status==="running"?"停止服务":"启动服务"),3)])):J("",!0)])])])}const fa=Bs(Sn,[["render",pa],["__scopeId","data-v-1c71c50b"]]);var Oe="top",ze="bottom",qe="right",Re="left",us="auto",Nt=[Oe,ze,qe,Re],Mt="start",Wt="end",ha="clippingParents",Vs="viewport",It="popper",ga="reference",Ts=Nt.reduce(function(e,s){return e.concat([s+"-"+Mt,s+"-"+Wt])},[]),Hs=[].concat(Nt,[us]).reduce(function(e,s){return e.concat([s,s+"-"+Mt,s+"-"+Wt])},[]),va="beforeRead",ma="read",ba="afterRead",wa="beforeMain",ya="main",xa="afterMain",Ta="beforeWrite",ka="write",Ca="afterWrite",$a=[va,ma,ba,wa,ya,xa,Ta,ka,Ca];function st(e){return e?(e.nodeName||"").toLowerCase():null}function je(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function kt(e){var s=je(e).Element;return e instanceof s||e instanceof Element}function He(e){var s=je(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function ds(e){if(typeof ShadowRoot>"u")return!1;var s=je(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function Sa(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},o=s.attributes[t]||{},v=s.elements[t];!He(v)||!st(v)||(Object.assign(v.style,a),Object.keys(o).forEach(function(C){var D=o[C];D===!1?v.removeAttribute(C):v.setAttribute(C,D===!0?"":D)}))})}function Ea(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var o=s.elements[a],v=s.attributes[a]||{},C=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),D=C.reduce(function(w,$){return w[$]="",w},{});!He(o)||!st(o)||(Object.assign(o.style,D),Object.keys(v).forEach(function(w){o.removeAttribute(w)}))})}}const zs={name:"applyStyles",enabled:!0,phase:"write",fn:Sa,effect:Ea,requires:["computeStyles"]};function tt(e){return e.split("-")[0]}var xt=Math.max,Kt=Math.min,Dt=Math.round;function os(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function qs(){return!/^((?!chrome|android).)*safari/i.test(os())}function Pt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),o=1,v=1;s&&He(e)&&(o=e.offsetWidth>0&&Dt(a.width)/e.offsetWidth||1,v=e.offsetHeight>0&&Dt(a.height)/e.offsetHeight||1);var C=kt(e)?je(e):window,D=C.visualViewport,w=!qs()&&t,$=(a.left+(w&&D?D.offsetLeft:0))/o,O=(a.top+(w&&D?D.offsetTop:0))/v,U=a.width/o,H=a.height/v;return{width:U,height:H,top:O,right:$+U,bottom:O+H,left:$,x:$,y:O}}function ps(e){var s=Pt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function Js(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&ds(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function dt(e){return je(e).getComputedStyle(e)}function _a(e){return["table","td","th"].indexOf(st(e))>=0}function gt(e){return((kt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Gt(e){return st(e)==="html"?e:e.assignedSlot||e.parentNode||(ds(e)?e.host:null)||gt(e)}function ks(e){return!He(e)||dt(e).position==="fixed"?null:e.offsetParent}function Aa(e){var s=/firefox/i.test(os()),t=/Trident/i.test(os());if(t&&He(e)){var a=dt(e);if(a.position==="fixed")return null}var o=Gt(e);for(ds(o)&&(o=o.host);He(o)&&["html","body"].indexOf(st(o))<0;){var v=dt(o);if(v.transform!=="none"||v.perspective!=="none"||v.contain==="paint"||["transform","perspective"].indexOf(v.willChange)!==-1||s&&v.willChange==="filter"||s&&v.filter&&v.filter!=="none")return o;o=o.parentNode}return null}function Vt(e){for(var s=je(e),t=ks(e);t&&_a(t)&&dt(t).position==="static";)t=ks(t);return t&&(st(t)==="html"||st(t)==="body"&&dt(t).position==="static")?s:t||Aa(e)||s}function fs(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Lt(e,s,t){return xt(e,Kt(s,t))}function Ma(e,s,t){var a=Lt(e,s,t);return a>t?t:a}function Ys(){return{top:0,right:0,bottom:0,left:0}}function Ks(e){return Object.assign({},Ys(),e)}function Xs(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var Da=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Ks(typeof s!="number"?s:Xs(s,Nt))};function Pa(e){var s,t=e.state,a=e.name,o=e.options,v=t.elements.arrow,C=t.modifiersData.popperOffsets,D=tt(t.placement),w=fs(D),$=[Re,qe].indexOf(D)>=0,O=$?"height":"width";if(!(!v||!C)){var U=Da(o.padding,t),H=ps(v),R=w==="y"?Oe:Re,N=w==="y"?ze:qe,K=t.rects.reference[O]+t.rects.reference[w]-C[w]-t.rects.popper[O],V=C[w]-t.rects.reference[w],se=Vt(v),ne=se?w==="y"?se.clientHeight||0:se.clientWidth||0:0,oe=K/2-V/2,p=U[R],te=ne-H[O]-U[N],P=ne/2-H[O]/2+oe,ce=Lt(p,P,te),pe=w;t.modifiersData[a]=(s={},s[pe]=ce,s.centerOffset=ce-P,s)}}function Oa(e){var s=e.state,t=e.options,a=t.element,o=a===void 0?"[data-popper-arrow]":a;o!=null&&(typeof o=="string"&&(o=s.elements.popper.querySelector(o),!o)||Js(s.elements.popper,o)&&(s.elements.arrow=o))}const Ra={name:"arrow",enabled:!0,phase:"main",fn:Pa,effect:Oa,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ot(e){return e.split("-")[1]}var Ia={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ua(e,s){var t=e.x,a=e.y,o=s.devicePixelRatio||1;return{x:Dt(t*o)/o||0,y:Dt(a*o)/o||0}}function Cs(e){var s,t=e.popper,a=e.popperRect,o=e.placement,v=e.variation,C=e.offsets,D=e.position,w=e.gpuAcceleration,$=e.adaptive,O=e.roundOffsets,U=e.isFixed,H=C.x,R=H===void 0?0:H,N=C.y,K=N===void 0?0:N,V=typeof O=="function"?O({x:R,y:K}):{x:R,y:K};R=V.x,K=V.y;var se=C.hasOwnProperty("x"),ne=C.hasOwnProperty("y"),oe=Re,p=Oe,te=window;if($){var P=Vt(t),ce="clientHeight",pe="clientWidth";if(P===je(t)&&(P=gt(t),dt(P).position!=="static"&&D==="absolute"&&(ce="scrollHeight",pe="scrollWidth")),P=P,o===Oe||(o===Re||o===qe)&&v===Wt){p=ze;var h=U&&P===te&&te.visualViewport?te.visualViewport.height:P[ce];K-=h-a.height,K*=w?1:-1}if(o===Re||(o===Oe||o===ze)&&v===Wt){oe=qe;var b=U&&P===te&&te.visualViewport?te.visualViewport.width:P[pe];R-=b-a.width,R*=w?1:-1}}var T=Object.assign({position:D},$&&Ia),k=O===!0?Ua({x:R,y:K},je(t)):{x:R,y:K};if(R=k.x,K=k.y,w){var S;return Object.assign({},T,(S={},S[p]=ne?"0":"",S[oe]=se?"0":"",S.transform=(te.devicePixelRatio||1)<=1?"translate("+R+"px, "+K+"px)":"translate3d("+R+"px, "+K+"px, 0)",S))}return Object.assign({},T,(s={},s[p]=ne?K+"px":"",s[oe]=se?R+"px":"",s.transform="",s))}function La(e){var s=e.state,t=e.options,a=t.gpuAcceleration,o=a===void 0?!0:a,v=t.adaptive,C=v===void 0?!0:v,D=t.roundOffsets,w=D===void 0?!0:D,$={placement:tt(s.placement),variation:Ot(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:o,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,Cs(Object.assign({},$,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:C,roundOffsets:w})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,Cs(Object.assign({},$,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:w})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Fa={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:La,data:{}};var zt={passive:!0};function ja(e){var s=e.state,t=e.instance,a=e.options,o=a.scroll,v=o===void 0?!0:o,C=a.resize,D=C===void 0?!0:C,w=je(s.elements.popper),$=[].concat(s.scrollParents.reference,s.scrollParents.popper);return v&&$.forEach(function(O){O.addEventListener("scroll",t.update,zt)}),D&&w.addEventListener("resize",t.update,zt),function(){v&&$.forEach(function(O){O.removeEventListener("scroll",t.update,zt)}),D&&w.removeEventListener("resize",t.update,zt)}}const Wa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ja,data:{}};var Ba={left:"right",right:"left",bottom:"top",top:"bottom"};function Jt(e){return e.replace(/left|right|bottom|top/g,function(s){return Ba[s]})}var Na={start:"end",end:"start"};function $s(e){return e.replace(/start|end/g,function(s){return Na[s]})}function hs(e){var s=je(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function gs(e){return Pt(gt(e)).left+hs(e).scrollLeft}function Va(e,s){var t=je(e),a=gt(e),o=t.visualViewport,v=a.clientWidth,C=a.clientHeight,D=0,w=0;if(o){v=o.width,C=o.height;var $=qs();($||!$&&s==="fixed")&&(D=o.offsetLeft,w=o.offsetTop)}return{width:v,height:C,x:D+gs(e),y:w}}function Ha(e){var s,t=gt(e),a=hs(e),o=(s=e.ownerDocument)==null?void 0:s.body,v=xt(t.scrollWidth,t.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),C=xt(t.scrollHeight,t.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),D=-a.scrollLeft+gs(e),w=-a.scrollTop;return dt(o||t).direction==="rtl"&&(D+=xt(t.clientWidth,o?o.clientWidth:0)-v),{width:v,height:C,x:D,y:w}}function vs(e){var s=dt(e),t=s.overflow,a=s.overflowX,o=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+o+a)}function Gs(e){return["html","body","#document"].indexOf(st(e))>=0?e.ownerDocument.body:He(e)&&vs(e)?e:Gs(Gt(e))}function Ft(e,s){var t;s===void 0&&(s=[]);var a=Gs(e),o=a===((t=e.ownerDocument)==null?void 0:t.body),v=je(a),C=o?[v].concat(v.visualViewport||[],vs(a)?a:[]):a,D=s.concat(C);return o?D:D.concat(Ft(Gt(C)))}function is(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function za(e,s){var t=Pt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Ss(e,s,t){return s===Vs?is(Va(e,t)):kt(s)?za(s,t):is(Ha(gt(e)))}function qa(e){var s=Ft(Gt(e)),t=["absolute","fixed"].indexOf(dt(e).position)>=0,a=t&&He(e)?Vt(e):e;return kt(a)?s.filter(function(o){return kt(o)&&Js(o,a)&&st(o)!=="body"}):[]}function Ja(e,s,t,a){var o=s==="clippingParents"?qa(e):[].concat(s),v=[].concat(o,[t]),C=v[0],D=v.reduce(function(w,$){var O=Ss(e,$,a);return w.top=xt(O.top,w.top),w.right=Kt(O.right,w.right),w.bottom=Kt(O.bottom,w.bottom),w.left=xt(O.left,w.left),w},Ss(e,C,a));return D.width=D.right-D.left,D.height=D.bottom-D.top,D.x=D.left,D.y=D.top,D}function Zs(e){var s=e.reference,t=e.element,a=e.placement,o=a?tt(a):null,v=a?Ot(a):null,C=s.x+s.width/2-t.width/2,D=s.y+s.height/2-t.height/2,w;switch(o){case Oe:w={x:C,y:s.y-t.height};break;case ze:w={x:C,y:s.y+s.height};break;case qe:w={x:s.x+s.width,y:D};break;case Re:w={x:s.x-t.width,y:D};break;default:w={x:s.x,y:s.y}}var $=o?fs(o):null;if($!=null){var O=$==="y"?"height":"width";switch(v){case Mt:w[$]=w[$]-(s[O]/2-t[O]/2);break;case Wt:w[$]=w[$]+(s[O]/2-t[O]/2);break}}return w}function Bt(e,s){s===void 0&&(s={});var t=s,a=t.placement,o=a===void 0?e.placement:a,v=t.strategy,C=v===void 0?e.strategy:v,D=t.boundary,w=D===void 0?ha:D,$=t.rootBoundary,O=$===void 0?Vs:$,U=t.elementContext,H=U===void 0?It:U,R=t.altBoundary,N=R===void 0?!1:R,K=t.padding,V=K===void 0?0:K,se=Ks(typeof V!="number"?V:Xs(V,Nt)),ne=H===It?ga:It,oe=e.rects.popper,p=e.elements[N?ne:H],te=Ja(kt(p)?p:p.contextElement||gt(e.elements.popper),w,O,C),P=Pt(e.elements.reference),ce=Zs({reference:P,element:oe,strategy:"absolute",placement:o}),pe=is(Object.assign({},oe,ce)),h=H===It?pe:P,b={top:te.top-h.top+se.top,bottom:h.bottom-te.bottom+se.bottom,left:te.left-h.left+se.left,right:h.right-te.right+se.right},T=e.modifiersData.offset;if(H===It&&T){var k=T[o];Object.keys(b).forEach(function(S){var m=[qe,ze].indexOf(S)>=0?1:-1,I=[Oe,ze].indexOf(S)>=0?"y":"x";b[S]+=k[I]*m})}return b}function Ya(e,s){s===void 0&&(s={});var t=s,a=t.placement,o=t.boundary,v=t.rootBoundary,C=t.padding,D=t.flipVariations,w=t.allowedAutoPlacements,$=w===void 0?Hs:w,O=Ot(a),U=O?D?Ts:Ts.filter(function(N){return Ot(N)===O}):Nt,H=U.filter(function(N){return $.indexOf(N)>=0});H.length===0&&(H=U);var R=H.reduce(function(N,K){return N[K]=Bt(e,{placement:K,boundary:o,rootBoundary:v,padding:C})[tt(K)],N},{});return Object.keys(R).sort(function(N,K){return R[N]-R[K]})}function Ka(e){if(tt(e)===us)return[];var s=Jt(e);return[$s(e),s,$s(s)]}function Xa(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var o=t.mainAxis,v=o===void 0?!0:o,C=t.altAxis,D=C===void 0?!0:C,w=t.fallbackPlacements,$=t.padding,O=t.boundary,U=t.rootBoundary,H=t.altBoundary,R=t.flipVariations,N=R===void 0?!0:R,K=t.allowedAutoPlacements,V=s.options.placement,se=tt(V),ne=se===V,oe=w||(ne||!N?[Jt(V)]:Ka(V)),p=[V].concat(oe).reduce(function($e,Ae){return $e.concat(tt(Ae)===us?Ya(s,{placement:Ae,boundary:O,rootBoundary:U,padding:$,flipVariations:N,allowedAutoPlacements:K}):Ae)},[]),te=s.rects.reference,P=s.rects.popper,ce=new Map,pe=!0,h=p[0],b=0;b<p.length;b++){var T=p[b],k=tt(T),S=Ot(T)===Mt,m=[Oe,ze].indexOf(k)>=0,I=m?"width":"height",A=Bt(s,{placement:T,boundary:O,rootBoundary:U,altBoundary:H,padding:$}),G=m?S?qe:Re:S?ze:Oe;te[I]>P[I]&&(G=Jt(G));var ge=Jt(G),ye=[];if(v&&ye.push(A[k]<=0),D&&ye.push(A[G]<=0,A[ge]<=0),ye.every(function($e){return $e})){h=T,pe=!1;break}ce.set(T,ye)}if(pe)for(var Me=N?3:1,We=function(Ae){var Pe=p.find(function(Be){var Z=ce.get(Be);if(Z)return Z.slice(0,Ae).every(function(Ue){return Ue})});if(Pe)return h=Pe,"break"},De=Me;De>0;De--){var Ie=We(De);if(Ie==="break")break}s.placement!==h&&(s.modifiersData[a]._skip=!0,s.placement=h,s.reset=!0)}}const Ga={name:"flip",enabled:!0,phase:"main",fn:Xa,requiresIfExists:["offset"],data:{_skip:!1}};function Es(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function _s(e){return[Oe,qe,ze,Re].some(function(s){return e[s]>=0})}function Za(e){var s=e.state,t=e.name,a=s.rects.reference,o=s.rects.popper,v=s.modifiersData.preventOverflow,C=Bt(s,{elementContext:"reference"}),D=Bt(s,{altBoundary:!0}),w=Es(C,a),$=Es(D,o,v),O=_s(w),U=_s($);s.modifiersData[t]={referenceClippingOffsets:w,popperEscapeOffsets:$,isReferenceHidden:O,hasPopperEscaped:U},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":O,"data-popper-escaped":U})}const Qa={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Za};function er(e,s,t){var a=tt(e),o=[Re,Oe].indexOf(a)>=0?-1:1,v=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,C=v[0],D=v[1];return C=C||0,D=(D||0)*o,[Re,qe].indexOf(a)>=0?{x:D,y:C}:{x:C,y:D}}function tr(e){var s=e.state,t=e.options,a=e.name,o=t.offset,v=o===void 0?[0,0]:o,C=Hs.reduce(function(O,U){return O[U]=er(U,s.rects,v),O},{}),D=C[s.placement],w=D.x,$=D.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=w,s.modifiersData.popperOffsets.y+=$),s.modifiersData[a]=C}const sr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:tr};function nr(e){var s=e.state,t=e.name;s.modifiersData[t]=Zs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const ar={name:"popperOffsets",enabled:!0,phase:"read",fn:nr,data:{}};function rr(e){return e==="x"?"y":"x"}function or(e){var s=e.state,t=e.options,a=e.name,o=t.mainAxis,v=o===void 0?!0:o,C=t.altAxis,D=C===void 0?!1:C,w=t.boundary,$=t.rootBoundary,O=t.altBoundary,U=t.padding,H=t.tether,R=H===void 0?!0:H,N=t.tetherOffset,K=N===void 0?0:N,V=Bt(s,{boundary:w,rootBoundary:$,padding:U,altBoundary:O}),se=tt(s.placement),ne=Ot(s.placement),oe=!ne,p=fs(se),te=rr(p),P=s.modifiersData.popperOffsets,ce=s.rects.reference,pe=s.rects.popper,h=typeof K=="function"?K(Object.assign({},s.rects,{placement:s.placement})):K,b=typeof h=="number"?{mainAxis:h,altAxis:h}:Object.assign({mainAxis:0,altAxis:0},h),T=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,k={x:0,y:0};if(P){if(v){var S,m=p==="y"?Oe:Re,I=p==="y"?ze:qe,A=p==="y"?"height":"width",G=P[p],ge=G+V[m],ye=G-V[I],Me=R?-pe[A]/2:0,We=ne===Mt?ce[A]:pe[A],De=ne===Mt?-pe[A]:-ce[A],Ie=s.elements.arrow,$e=R&&Ie?ps(Ie):{width:0,height:0},Ae=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Ys(),Pe=Ae[m],Be=Ae[I],Z=Lt(0,ce[A],$e[A]),Ue=oe?ce[A]/2-Me-Z-Pe-b.mainAxis:We-Z-Pe-b.mainAxis,ke=oe?-ce[A]/2+Me+Z+Be+b.mainAxis:De+Z+Be+b.mainAxis,nt=s.elements.arrow&&Vt(s.elements.arrow),Ge=nt?p==="y"?nt.clientTop||0:nt.clientLeft||0:0,Ne=(S=T==null?void 0:T[p])!=null?S:0,me=G+Ue-Ne-Ge,pt=G+ke-Ne,Ze=Lt(R?Kt(ge,me):ge,G,R?xt(ye,pt):ye);P[p]=Ze,k[p]=Ze-G}if(D){var Te,at=p==="x"?Oe:Re,Ve=p==="x"?ze:qe,Le=P[te],Fe=te==="y"?"height":"width",rt=Le+V[at],Je=Le-V[Ve],ot=[Oe,Re].indexOf(se)!==-1,Qe=(Te=T==null?void 0:T[te])!=null?Te:0,it=ot?rt:Le-ce[Fe]-pe[Fe]-Qe+b.altAxis,ft=ot?Le+ce[Fe]+pe[Fe]-Qe-b.altAxis:Je,lt=R&&ot?Ma(it,Le,ft):Lt(R?it:rt,Le,R?ft:Je);P[te]=lt,k[te]=lt-Le}s.modifiersData[a]=k}}const ir={name:"preventOverflow",enabled:!0,phase:"main",fn:or,requiresIfExists:["offset"]};function lr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function cr(e){return e===je(e)||!He(e)?hs(e):lr(e)}function ur(e){var s=e.getBoundingClientRect(),t=Dt(s.width)/e.offsetWidth||1,a=Dt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function dr(e,s,t){t===void 0&&(t=!1);var a=He(s),o=He(s)&&ur(s),v=gt(s),C=Pt(e,o,t),D={scrollLeft:0,scrollTop:0},w={x:0,y:0};return(a||!a&&!t)&&((st(s)!=="body"||vs(v))&&(D=cr(s)),He(s)?(w=Pt(s,!0),w.x+=s.clientLeft,w.y+=s.clientTop):v&&(w.x=gs(v))),{x:C.left+D.scrollLeft-w.x,y:C.top+D.scrollTop-w.y,width:C.width,height:C.height}}function pr(e){var s=new Map,t=new Set,a=[];e.forEach(function(v){s.set(v.name,v)});function o(v){t.add(v.name);var C=[].concat(v.requires||[],v.requiresIfExists||[]);C.forEach(function(D){if(!t.has(D)){var w=s.get(D);w&&o(w)}}),a.push(v)}return e.forEach(function(v){t.has(v.name)||o(v)}),a}function fr(e){var s=pr(e);return $a.reduce(function(t,a){return t.concat(s.filter(function(o){return o.phase===a}))},[])}function hr(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function gr(e){var s=e.reduce(function(t,a){var o=t[a.name];return t[a.name]=o?Object.assign({},o,a,{options:Object.assign({},o.options,a.options),data:Object.assign({},o.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var As={placement:"bottom",modifiers:[],strategy:"absolute"};function Ms(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function vr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,o=s.defaultOptions,v=o===void 0?As:o;return function(D,w,$){$===void 0&&($=v);var O={placement:"bottom",orderedModifiers:[],options:Object.assign({},As,v),modifiersData:{},elements:{reference:D,popper:w},attributes:{},styles:{}},U=[],H=!1,R={state:O,setOptions:function(se){var ne=typeof se=="function"?se(O.options):se;K(),O.options=Object.assign({},v,O.options,ne),O.scrollParents={reference:kt(D)?Ft(D):D.contextElement?Ft(D.contextElement):[],popper:Ft(w)};var oe=fr(gr([].concat(a,O.options.modifiers)));return O.orderedModifiers=oe.filter(function(p){return p.enabled}),N(),R.update()},forceUpdate:function(){if(!H){var se=O.elements,ne=se.reference,oe=se.popper;if(Ms(ne,oe)){O.rects={reference:dr(ne,Vt(oe),O.options.strategy==="fixed"),popper:ps(oe)},O.reset=!1,O.placement=O.options.placement,O.orderedModifiers.forEach(function(b){return O.modifiersData[b.name]=Object.assign({},b.data)});for(var p=0;p<O.orderedModifiers.length;p++){if(O.reset===!0){O.reset=!1,p=-1;continue}var te=O.orderedModifiers[p],P=te.fn,ce=te.options,pe=ce===void 0?{}:ce,h=te.name;typeof P=="function"&&(O=P({state:O,options:pe,name:h,instance:R})||O)}}}},update:hr(function(){return new Promise(function(V){R.forceUpdate(),V(O)})}),destroy:function(){K(),H=!0}};if(!Ms(D,w))return R;R.setOptions($).then(function(V){!H&&$.onFirstUpdate&&$.onFirstUpdate(V)});function N(){O.orderedModifiers.forEach(function(V){var se=V.name,ne=V.options,oe=ne===void 0?{}:ne,p=V.effect;if(typeof p=="function"){var te=p({state:O,name:se,instance:R,options:oe}),P=function(){};U.push(te||P)}})}function K(){U.forEach(function(V){return V()}),U=[]}return R}}var mr=[Wa,ar,Fa,zs,sr,Ga,ir,Ra,Qa],br=vr({defaultModifiers:mr}),wr="tippy-box",Qs="tippy-content",yr="tippy-backdrop",en="tippy-arrow",tn="tippy-svg-arrow",yt={passive:!0,capture:!0},sn=function(){return document.body};function ss(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function ms(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function nn(e,s){return typeof e=="function"?e.apply(void 0,s):e}function Ds(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function xr(e){return e.split(/\s+/).filter(Boolean)}function At(e){return[].concat(e)}function Ps(e,s){e.indexOf(s)===-1&&e.push(s)}function Tr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function kr(e){return e.split("-")[0]}function Xt(e){return[].slice.call(e)}function Os(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function jt(){return document.createElement("div")}function Zt(e){return["Element","Fragment"].some(function(s){return ms(e,s)})}function Cr(e){return ms(e,"NodeList")}function $r(e){return ms(e,"MouseEvent")}function Sr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function Er(e){return Zt(e)?[e]:Cr(e)?Xt(e):Array.isArray(e)?e:Xt(document.querySelectorAll(e))}function ns(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function Rs(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function _r(e){var s,t=At(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function Ar(e,s){var t=s.clientX,a=s.clientY;return e.every(function(o){var v=o.popperRect,C=o.popperState,D=o.props,w=D.interactiveBorder,$=kr(C.placement),O=C.modifiersData.offset;if(!O)return!0;var U=$==="bottom"?O.top.y:0,H=$==="top"?O.bottom.y:0,R=$==="right"?O.left.x:0,N=$==="left"?O.right.x:0,K=v.top-a+U>w,V=a-v.bottom-H>w,se=v.left-t+R>w,ne=t-v.right-N>w;return K||V||se||ne})}function as(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(o){e[a](o,t)})}function Is(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var et={isTouch:!1},Us=0;function Mr(){et.isTouch||(et.isTouch=!0,window.performance&&document.addEventListener("mousemove",an))}function an(){var e=performance.now();e-Us<20&&(et.isTouch=!1,document.removeEventListener("mousemove",an)),Us=e}function Dr(){var e=document.activeElement;if(Sr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function Pr(){document.addEventListener("touchstart",Mr,yt),window.addEventListener("blur",Dr)}var Or=typeof window<"u"&&typeof document<"u",Rr=Or?!!window.msCrypto:!1,Ir={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ur={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Xe=Object.assign({appendTo:sn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Ir,Ur),Lr=Object.keys(Xe),Fr=function(s){var t=Object.keys(s);t.forEach(function(a){Xe[a]=s[a]})};function rn(e){var s=e.plugins||[],t=s.reduce(function(a,o){var v=o.name,C=o.defaultValue;if(v){var D;a[v]=e[v]!==void 0?e[v]:(D=Xe[v])!=null?D:C}return a},{});return Object.assign({},e,t)}function jr(e,s){var t=s?Object.keys(rn(Object.assign({},Xe,{plugins:s}))):Lr,a=t.reduce(function(o,v){var C=(e.getAttribute("data-tippy-"+v)||"").trim();if(!C)return o;if(v==="content")o[v]=C;else try{o[v]=JSON.parse(C)}catch{o[v]=C}return o},{});return a}function Ls(e,s){var t=Object.assign({},s,{content:nn(s.content,[e])},s.ignoreAttributes?{}:jr(e,s.plugins));return t.aria=Object.assign({},Xe.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Wr=function(){return"innerHTML"};function ls(e,s){e[Wr()]=s}function Fs(e){var s=jt();return e===!0?s.className=en:(s.className=tn,Zt(e)?s.appendChild(e):ls(s,e)),s}function js(e,s){Zt(s.content)?(ls(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?ls(e,s.content):e.textContent=s.content)}function cs(e){var s=e.firstElementChild,t=Xt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(Qs)}),arrow:t.find(function(a){return a.classList.contains(en)||a.classList.contains(tn)}),backdrop:t.find(function(a){return a.classList.contains(yr)})}}function on(e){var s=jt(),t=jt();t.className=wr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=jt();a.className=Qs,a.setAttribute("data-state","hidden"),js(a,e.props),s.appendChild(t),t.appendChild(a),o(e.props,e.props);function o(v,C){var D=cs(s),w=D.box,$=D.content,O=D.arrow;C.theme?w.setAttribute("data-theme",C.theme):w.removeAttribute("data-theme"),typeof C.animation=="string"?w.setAttribute("data-animation",C.animation):w.removeAttribute("data-animation"),C.inertia?w.setAttribute("data-inertia",""):w.removeAttribute("data-inertia"),w.style.maxWidth=typeof C.maxWidth=="number"?C.maxWidth+"px":C.maxWidth,C.role?w.setAttribute("role",C.role):w.removeAttribute("role"),(v.content!==C.content||v.allowHTML!==C.allowHTML)&&js($,e.props),C.arrow?O?v.arrow!==C.arrow&&(w.removeChild(O),w.appendChild(Fs(C.arrow))):w.appendChild(Fs(C.arrow)):O&&w.removeChild(O)}return{popper:s,onUpdate:o}}on.$$tippy=!0;var Br=1,qt=[],rs=[];function Nr(e,s){var t=Ls(e,Object.assign({},Xe,rn(Os(s)))),a,o,v,C=!1,D=!1,w=!1,$=!1,O,U,H,R=[],N=Ds(me,t.interactiveDebounce),K,V=Br++,se=null,ne=Tr(t.plugins),oe={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},p={id:V,reference:e,popper:jt(),popperInstance:se,props:t,state:oe,plugins:ne,clearDelayTimeouts:it,setProps:ft,setContent:lt,show:Rt,hide:vt,hideWithInteractivity:Ct,enable:ot,disable:Qe,unmount:$t,destroy:mt};if(!t.render)return p;var te=t.render(p),P=te.popper,ce=te.onUpdate;P.setAttribute("data-tippy-root",""),P.id="tippy-"+p.id,p.popper=P,e._tippy=p,P._tippy=p;var pe=ne.map(function(y){return y.fn(p)}),h=e.hasAttribute("aria-expanded");return nt(),Me(),G(),ge("onCreate",[p]),t.showOnCreate&&rt(),P.addEventListener("mouseenter",function(){p.props.interactive&&p.state.isVisible&&p.clearDelayTimeouts()}),P.addEventListener("mouseleave",function(){p.props.interactive&&p.props.trigger.indexOf("mouseenter")>=0&&m().addEventListener("mousemove",N)}),p;function b(){var y=p.props.touch;return Array.isArray(y)?y:[y,0]}function T(){return b()[0]==="hold"}function k(){var y;return!!((y=p.props.render)!=null&&y.$$tippy)}function S(){return K||e}function m(){var y=S().parentNode;return y?_r(y):document}function I(){return cs(P)}function A(y){return p.state.isMounted&&!p.state.isVisible||et.isTouch||O&&O.type==="focus"?0:ss(p.props.delay,y?0:1,Xe.delay)}function G(y){y===void 0&&(y=!1),P.style.pointerEvents=p.props.interactive&&!y?"":"none",P.style.zIndex=""+p.props.zIndex}function ge(y,L,z){if(z===void 0&&(z=!0),pe.forEach(function(le){le[y]&&le[y].apply(le,L)}),z){var ie;(ie=p.props)[y].apply(ie,L)}}function ye(){var y=p.props.aria;if(y.content){var L="aria-"+y.content,z=P.id,ie=At(p.props.triggerTarget||e);ie.forEach(function(le){var xe=le.getAttribute(L);if(p.state.isVisible)le.setAttribute(L,xe?xe+" "+z:z);else{var Se=xe&&xe.replace(z,"").trim();Se?le.setAttribute(L,Se):le.removeAttribute(L)}})}}function Me(){if(!(h||!p.props.aria.expanded)){var y=At(p.props.triggerTarget||e);y.forEach(function(L){p.props.interactive?L.setAttribute("aria-expanded",p.state.isVisible&&L===S()?"true":"false"):L.removeAttribute("aria-expanded")})}}function We(){m().removeEventListener("mousemove",N),qt=qt.filter(function(y){return y!==N})}function De(y){if(!(et.isTouch&&(w||y.type==="mousedown"))){var L=y.composedPath&&y.composedPath()[0]||y.target;if(!(p.props.interactive&&Is(P,L))){if(At(p.props.triggerTarget||e).some(function(z){return Is(z,L)})){if(et.isTouch||p.state.isVisible&&p.props.trigger.indexOf("click")>=0)return}else ge("onClickOutside",[p,y]);p.props.hideOnClick===!0&&(p.clearDelayTimeouts(),p.hide(),D=!0,setTimeout(function(){D=!1}),p.state.isMounted||Pe())}}}function Ie(){w=!0}function $e(){w=!1}function Ae(){var y=m();y.addEventListener("mousedown",De,!0),y.addEventListener("touchend",De,yt),y.addEventListener("touchstart",$e,yt),y.addEventListener("touchmove",Ie,yt)}function Pe(){var y=m();y.removeEventListener("mousedown",De,!0),y.removeEventListener("touchend",De,yt),y.removeEventListener("touchstart",$e,yt),y.removeEventListener("touchmove",Ie,yt)}function Be(y,L){Ue(y,function(){!p.state.isVisible&&P.parentNode&&P.parentNode.contains(P)&&L()})}function Z(y,L){Ue(y,L)}function Ue(y,L){var z=I().box;function ie(le){le.target===z&&(as(z,"remove",ie),L())}if(y===0)return L();as(z,"remove",U),as(z,"add",ie),U=ie}function ke(y,L,z){z===void 0&&(z=!1);var ie=At(p.props.triggerTarget||e);ie.forEach(function(le){le.addEventListener(y,L,z),R.push({node:le,eventType:y,handler:L,options:z})})}function nt(){T()&&(ke("touchstart",Ne,{passive:!0}),ke("touchend",pt,{passive:!0})),xr(p.props.trigger).forEach(function(y){if(y!=="manual")switch(ke(y,Ne),y){case"mouseenter":ke("mouseleave",pt);break;case"focus":ke(Rr?"focusout":"blur",Ze);break;case"focusin":ke("focusout",Ze);break}})}function Ge(){R.forEach(function(y){var L=y.node,z=y.eventType,ie=y.handler,le=y.options;L.removeEventListener(z,ie,le)}),R=[]}function Ne(y){var L,z=!1;if(!(!p.state.isEnabled||Te(y)||D)){var ie=((L=O)==null?void 0:L.type)==="focus";O=y,K=y.currentTarget,Me(),!p.state.isVisible&&$r(y)&&qt.forEach(function(le){return le(y)}),y.type==="click"&&(p.props.trigger.indexOf("mouseenter")<0||C)&&p.props.hideOnClick!==!1&&p.state.isVisible?z=!0:rt(y),y.type==="click"&&(C=!z),z&&!ie&&Je(y)}}function me(y){var L=y.target,z=S().contains(L)||P.contains(L);if(!(y.type==="mousemove"&&z)){var ie=Fe().concat(P).map(function(le){var xe,Se=le._tippy,_=(xe=Se.popperInstance)==null?void 0:xe.state;return _?{popperRect:le.getBoundingClientRect(),popperState:_,props:t}:null}).filter(Boolean);Ar(ie,y)&&(We(),Je(y))}}function pt(y){var L=Te(y)||p.props.trigger.indexOf("click")>=0&&C;if(!L){if(p.props.interactive){p.hideWithInteractivity(y);return}Je(y)}}function Ze(y){p.props.trigger.indexOf("focusin")<0&&y.target!==S()||p.props.interactive&&y.relatedTarget&&P.contains(y.relatedTarget)||Je(y)}function Te(y){return et.isTouch?T()!==y.type.indexOf("touch")>=0:!1}function at(){Ve();var y=p.props,L=y.popperOptions,z=y.placement,ie=y.offset,le=y.getReferenceClientRect,xe=y.moveTransition,Se=k()?cs(P).arrow:null,_=le?{getBoundingClientRect:le,contextElement:le.contextElement||S()}:e,g={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(fe){var X=fe.state;if(k()){var be=I(),n=be.box;["placement","reference-hidden","escaped"].forEach(function(i){i==="placement"?n.setAttribute("data-placement",X.placement):X.attributes.popper["data-popper-"+i]?n.setAttribute("data-"+i,""):n.removeAttribute("data-"+i)}),X.attributes.popper={}}}},B=[{name:"offset",options:{offset:ie}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!xe}},g];k()&&Se&&B.push({name:"arrow",options:{element:Se,padding:3}}),B.push.apply(B,(L==null?void 0:L.modifiers)||[]),p.popperInstance=br(_,P,Object.assign({},L,{placement:z,onFirstUpdate:H,modifiers:B}))}function Ve(){p.popperInstance&&(p.popperInstance.destroy(),p.popperInstance=null)}function Le(){var y=p.props.appendTo,L,z=S();p.props.interactive&&y===sn||y==="parent"?L=z.parentNode:L=nn(y,[z]),L.contains(P)||L.appendChild(P),p.state.isMounted=!0,at()}function Fe(){return Xt(P.querySelectorAll("[data-tippy-root]"))}function rt(y){p.clearDelayTimeouts(),y&&ge("onTrigger",[p,y]),Ae();var L=A(!0),z=b(),ie=z[0],le=z[1];et.isTouch&&ie==="hold"&&le&&(L=le),L?a=setTimeout(function(){p.show()},L):p.show()}function Je(y){if(p.clearDelayTimeouts(),ge("onUntrigger",[p,y]),!p.state.isVisible){Pe();return}if(!(p.props.trigger.indexOf("mouseenter")>=0&&p.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(y.type)>=0&&C)){var L=A(!1);L?o=setTimeout(function(){p.state.isVisible&&p.hide()},L):v=requestAnimationFrame(function(){p.hide()})}}function ot(){p.state.isEnabled=!0}function Qe(){p.hide(),p.state.isEnabled=!1}function it(){clearTimeout(a),clearTimeout(o),cancelAnimationFrame(v)}function ft(y){if(!p.state.isDestroyed){ge("onBeforeUpdate",[p,y]),Ge();var L=p.props,z=Ls(e,Object.assign({},L,Os(y),{ignoreAttributes:!0}));p.props=z,nt(),L.interactiveDebounce!==z.interactiveDebounce&&(We(),N=Ds(me,z.interactiveDebounce)),L.triggerTarget&&!z.triggerTarget?At(L.triggerTarget).forEach(function(ie){ie.removeAttribute("aria-expanded")}):z.triggerTarget&&e.removeAttribute("aria-expanded"),Me(),G(),ce&&ce(L,z),p.popperInstance&&(at(),Fe().forEach(function(ie){requestAnimationFrame(ie._tippy.popperInstance.forceUpdate)})),ge("onAfterUpdate",[p,y])}}function lt(y){p.setProps({content:y})}function Rt(){var y=p.state.isVisible,L=p.state.isDestroyed,z=!p.state.isEnabled,ie=et.isTouch&&!p.props.touch,le=ss(p.props.duration,0,Xe.duration);if(!(y||L||z||ie)&&!S().hasAttribute("disabled")&&(ge("onShow",[p],!1),p.props.onShow(p)!==!1)){if(p.state.isVisible=!0,k()&&(P.style.visibility="visible"),G(),Ae(),p.state.isMounted||(P.style.transition="none"),k()){var xe=I(),Se=xe.box,_=xe.content;ns([Se,_],0)}H=function(){var B;if(!(!p.state.isVisible||$)){if($=!0,P.offsetHeight,P.style.transition=p.props.moveTransition,k()&&p.props.animation){var ee=I(),fe=ee.box,X=ee.content;ns([fe,X],le),Rs([fe,X],"visible")}ye(),Me(),Ps(rs,p),(B=p.popperInstance)==null||B.forceUpdate(),ge("onMount",[p]),p.props.animation&&k()&&Z(le,function(){p.state.isShown=!0,ge("onShown",[p])})}},Le()}}function vt(){var y=!p.state.isVisible,L=p.state.isDestroyed,z=!p.state.isEnabled,ie=ss(p.props.duration,1,Xe.duration);if(!(y||L||z)&&(ge("onHide",[p],!1),p.props.onHide(p)!==!1)){if(p.state.isVisible=!1,p.state.isShown=!1,$=!1,C=!1,k()&&(P.style.visibility="hidden"),We(),Pe(),G(!0),k()){var le=I(),xe=le.box,Se=le.content;p.props.animation&&(ns([xe,Se],ie),Rs([xe,Se],"hidden"))}ye(),Me(),p.props.animation?k()&&Be(ie,p.unmount):p.unmount()}}function Ct(y){m().addEventListener("mousemove",N),Ps(qt,N),N(y)}function $t(){p.state.isVisible&&p.hide(),p.state.isMounted&&(Ve(),Fe().forEach(function(y){y._tippy.unmount()}),P.parentNode&&P.parentNode.removeChild(P),rs=rs.filter(function(y){return y!==p}),p.state.isMounted=!1,ge("onHidden",[p]))}function mt(){p.state.isDestroyed||(p.clearDelayTimeouts(),p.unmount(),Ge(),delete e._tippy,p.state.isDestroyed=!0,ge("onDestroy",[p]))}}function Tt(e,s){s===void 0&&(s={});var t=Xe.plugins.concat(s.plugins||[]);Pr();var a=Object.assign({},s,{plugins:t}),o=Er(e),v=o.reduce(function(C,D){var w=D&&Nr(D,a);return w&&C.push(w),C},[]);return Zt(e)?v[0]:v}Tt.defaultProps=Xe;Tt.setDefaultProps=Fr;Tt.currentInput=et;Object.assign({},zs,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});Tt.setDefaultProps({render:on});const Vr={class:"task-pane"},Hr={key:0,class:"loading-overlay"},zr={key:1,class:"format-error-overlay"},qr={class:"format-error-content"},Jr={class:"format-error-message"},Yr={class:"format-error-actions"},Kr={class:"doc-header"},Xr={class:"doc-title"},Gr={class:"action-area"},Zr={class:"select-container"},Qr={class:"select-group"},eo=["disabled"],to=["value"],so={class:"select-group"},no=["disabled"],ao=["value"],ro=["title"],oo={class:"action-buttons"},io=["disabled"],lo={class:"btn-content"},co={key:0,class:"button-loader"},uo=["disabled"],po={class:"btn-content"},fo={key:0,class:"button-loader"},ho={class:"content-area"},go={class:"modal-header"},vo={class:"modal-body"},mo={class:"selection-content"},bo={class:"modal-header"},wo={class:"modal-body"},yo={class:"alert-message"},xo={class:"alert-actions"},To={key:2,class:"modal-overlay"},ko={class:"modal-header"},Co={class:"modal-body"},$o={class:"confirm-message"},So={class:"confirm-actions"},Eo={class:"modal-header"},_o={class:"modal-title"},Ao={class:"modal-body"},Mo={class:"alert-message"},Do={class:"alert-actions"},Po={class:"task-queue"},Oo={class:"queue-header"},Ro={class:"queue-status-filter"},Io=["value"],Uo={class:"queue-actions"},Lo=["disabled","title"],Fo={class:"task-count"},jo={key:0,class:"queue-table-container"},Wo={class:"col-id"},Bo={class:"id-header"},No={key:0,class:"col-subject"},Vo={class:"subject-header"},Ho={class:"switch"},zo=["title"],qo={key:1,class:"col-status"},Jo=["onClick"],Yo={class:"col-id"},Ko={class:"id-cell group-cell"},Xo={class:"group-toggle-icon"},Go={class:"group-label"},Zo={key:0,class:"col-subject"},Qo={class:"subject-cell group-subject"},ei={key:1,class:"col-status"},ti={class:"col-actions"},si={class:"task-actions"},ni={class:"group-action-text"},ai=["onClick"],ri={class:"col-id"},oi={class:"id-content"},ii={class:"task-id"},li={key:0,class:"analysis-task-icon",title:"解析任务"},ci={key:1,class:"enhance-task-icon",title:"增强解析任务"},ui={key:2,class:"check-task-icon",title:"校对任务"},di={key:0,class:"status-in-id"},pi={key:0,class:"col-subject"},fi=["onMouseenter"],hi={key:1,class:"col-status"},gi={class:"status-cell"},vi=["onClick"],mi={class:"col-id"},bi={class:"id-content"},wi={class:"task-id"},yi={key:0,class:"analysis-task-icon",title:"解析任务"},xi={key:1,class:"enhance-task-icon",title:"增强解析任务"},Ti={key:2,class:"check-task-icon",title:"校对任务"},ki={key:0,class:"status-in-id"},Ci={key:0,class:"col-subject"},$i=["onMouseenter"],Si={key:1,class:"col-status"},Ei={class:"status-cell"},_i={class:"col-actions"},Ai={class:"task-actions"},Mi=["onClick"],Di=["onClick"],Pi={key:2,class:"no-action-icon",title:"无可用操作"},Oi={key:1,class:"empty-queue"},Ri={key:4,class:"log-container"},Ii={class:"log-actions"},Ui={class:"toggle-icon"},Li=["innerHTML"],Fi={__name:"TaskPane",setup(e){const s=he(!1),t=he(!1),a=he(!1),o=he(""),v=he(!1),C=he(!1),D=he(!1),w=he(!1),$=he(!0),O=he(""),U=he(!1),H=he(window.innerWidth),R=ut(()=>H.value<750),N=ut(()=>H.value<380),K=()=>{H.value=window.innerWidth},V=he(null),se=he(!1),ne=he(""),oe=he(new Set);he(!1);const p={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},te=he(!1),P=he([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"}]),{docName:ce,selected:pe,logger:h,map:b,subject:T,stage:k,subjectOptions:S,stageOptions:m,appConfig:I,clearLog:A,checkDocumentFormat:G,getTaskStatusClass:ge,getTaskStatusText:ye,terminateTask:Me,run1:We,runCheck:De,setupLifecycle:Ie,navigateToTaskControl:$e,isLoading:Ae,tryRemoveTaskPlaceholderWithLoading:Pe,confirmDialog:Be,handleConfirm:Z,errorDialog:Ue,hideErrorDialog:ke,getCompletedTasksCount:nt,getReleasableTasksCount:Ge,showConfirm:Ne}=$n(),me=he(null);(()=>{var _;try{if((_=window.Application)!=null&&_.PluginStorage){const g=window.Application.PluginStorage.getItem("user_info");g?(me.value=JSON.parse(g),console.log("用户信息已加载:",me.value)):console.log("未找到用户信息")}}catch(g){console.error("解析用户信息时出错:",g)}})(),Yt(me,_=>{_&&_.orgs&&_.orgs[0]&&console.log(`用户企业ID: ${_.orgs[0].orgId}, 校对功能${_.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Ze=ut(()=>!me.value||me.value.isAdmin||me.value.isOwner?S:me.value.subject?S.filter(_=>_.value===me.value.subject):S),Te=()=>{me.value&&!me.value.isAdmin&&!me.value.isOwner&&me.value.subject&&(T.value=me.value.subject)};ut(()=>["physics","chemistry","biology","math"].includes(T.value)),Yt(I,_=>{_&&(console.log("TaskPane组件收到应用配置更新:",_),console.log("当前版本类型:",_.EDITION),console.log("当前年级选项:",m.value))},{deep:!0,immediate:!0});const at=()=>{try{const _=G();$.value=_.isValid,O.value=_.message,U.value=!_.isValid,_.isValid||console.warn("文档格式检查失败:",_.message)}catch(_){console.error("执行文档格式检查时出错:",_),$.value=!1,O.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",U.value=!0}},Ve=ut(()=>{let _={};const g=b;if(ne.value==="")_={...g};else for(const B in g)if(Object.prototype.hasOwnProperty.call(g,B)){const ee=g[B];ee.status===ne.value&&(_[B]=ee)}return se.value&&V.value?_[V.value]?{[V.value]:_[V.value]}:{}:_}),Le=ut(()=>{const _=Ve.value;return Object.entries(_).map(([B,ee])=>({tid:B,...ee})).sort((B,ee)=>{const fe=B.startTime||0;return(ee.startTime||0)-fe}).reduce((B,ee)=>{const{tid:fe,...X}=ee;return B[fe]=X,B},{})}),Fe=ut(()=>{const _=Le.value,g=Object.entries(_).map(([X,be])=>({tid:X,...be})),B=g.filter(X=>X.status===3),ee=g.filter(X=>X.status!==3),fe=[];if(ee.forEach(X=>{fe.push({type:"task",...X})}),B.length>=2){const X="released-group-all",be=oe.value.has(X);fe.push({type:"group",groupId:X,isCollapsed:be,tasks:B,count:B.length})}else B.forEach(X=>{fe.push({type:"task",...X})});return fe}),rt=_=>{oe.value.has(_)?oe.value.delete(_):oe.value.add(_)},Je=(_="wps-analysis")=>{if(!T.value)o.value="请选择学科",t.value=!0;else if(!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim())o.value="未选中内容",t.value=!0;else{_==="wps-analysis"?C.value=!0:_==="wps-enhance_analysis"&&(D.value=!0);const g=()=>{_==="wps-analysis"?C.value=!1:_==="wps-enhance_analysis"&&(D.value=!1)};We(_,g).catch(B=>{console.log(B),B.message.includes("重叠")?(o.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",B),o.value=B.message,t.value=!0),g()})}},ot=()=>{if(!T.value)o.value="请选择学科",t.value=!0;else if(!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim())o.value="未选中内容",t.value=!0;else{w.value=!0;const _=()=>{w.value=!1};De(_).catch(g=>{console.log(g),g.message.includes("重叠")?(o.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",g),o.value=g.message,t.value=!0),_()})}},Qe=(_,g)=>{V.value=_,$e(_)},it=_=>{b[_]&&(b[_].status=3),V.value===_&&(V.value=null),Pe(_,!0)},ft=async()=>{const _=Object.entries(b).filter(([g,B])=>B.status===2||B.status===4);if(_.length===0){o.value="没有可释放的任务",t.value=!0;return}try{if(await Ne(`确定要释放所有 ${_.length} 个可释放的任务吗？
此操作不可撤销。`)){let B=0;_.forEach(([ee,fe])=>{b[ee]&&(b[ee].status=3,V.value===ee&&(V.value=null),Pe(ee,!0),B++)}),o.value=`已成功释放 ${B} 个任务`,t.value=!0}}catch(g){console.error("释放任务时出错:",g),o.value="释放任务时出现错误",t.value=!0}},lt=()=>{v.value=!v.value},Rt=_=>_?_.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",vt=_=>{const g=Ct(_);return g?Rt(g):"无题目内容"},Ct=_=>{if(!_.selectedText)return"";const g=_.selectedText.split("\r").filter(ee=>ee.trim());if(g.length===1){const ee=_.selectedText.split(`
`).filter(fe=>fe.trim());ee.length>1&&g.splice(0,1,...ee)}const B=g.map((ee,fe)=>{const X=ee.trim();return X.length>200,X});return B.length===1?g[0].trim():B.join(`
`)},$t=(_,g)=>{if(!te.value)return;const B=_.target,ee=Ct(g).toString();if(!ee||ee.trim()===""){console.log("题目内容为空，不显示tooltip");return}const fe=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${ee.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,X=_.clientX,be=_.clientY;if(p.subjects.has(B)){const i=p.subjects.get(B);i.setContent(fe),i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:be,bottom:be,left:X,right:X})}),i.show();return}const n=Tt(B,{content:fe,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:be,bottom:be,left:X,right:X}),onHidden:()=>{n.setProps({getReferenceClientRect:null})}});p.subjects.set(B,n),n.show()},mt=_=>{const g=_.currentTarget,B=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,ee=_.clientX,fe=_.clientY;if(p.enhance.has(g)){const be=p.enhance.get(g);be.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:ee,right:ee})}),be.show();return}const X=Tt(g,{content:B,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});p.enhance.set(g,X),X.show()},y=()=>{p.subjects.forEach(_=>{_.destroy()}),p.subjects.clear(),p.enhance.forEach(_=>{_.destroy()}),p.enhance.clear(),p.softBreak.forEach(_=>{_.destroy()}),p.softBreak.clear(),document.removeEventListener("click",L),document.removeEventListener("mousemove",ie)},L=_=>{const g=document.querySelector(".tippy-box");g&&!g.contains(_.target)&&(p.subjects.forEach(B=>B.hide()),p.enhance.forEach(B=>B.hide()),p.softBreak.forEach(B=>B.hide()))};let z=0;const ie=_=>{const g=Date.now();if(g-z<100)return;z=g;const B=document.querySelector(".tippy-box");if(!B)return;const ee=B.getBoundingClientRect();!(_.clientX>=ee.left-20&&_.clientX<=ee.right+20&&_.clientY>=ee.top-20&&_.clientY<=ee.bottom+20)&&!B.matches(":hover")&&(p.subjects.forEach(X=>X.hide()),p.enhance.forEach(X=>X.hide()),p.softBreak.forEach(X=>X.hide()))},le=()=>{document.addEventListener("click",L),document.addEventListener("mousemove",ie)};Ws(()=>{window.addEventListener("resize",K),le(),Te(),setTimeout(()=>{at()},500);const _=document.createElement("style");_.id="tippy-custom-styles",_.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(_)}),pn(()=>{window.removeEventListener("resize",K),y();const _=document.getElementById("tippy-custom-styles");_&&_.remove()}),Ie();const xe=_=>_.selectedText?_.selectedText.includes("\v")||_.selectedText.includes("\v"):!1,Se=_=>{const g=_.currentTarget,B=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,ee=_.clientX,fe=_.clientY;if(p.softBreak.has(g)){const be=p.softBreak.get(g);be.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:ee,right:ee})}),be.show();return}const X=Tt(g,{content:B,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});p.softBreak.set(g,X),X.show()};return ut(()=>Fe.value.some(_=>_.type==="group")),ut(()=>oe.value.size>0),(_,g)=>{var B,ee,fe,X,be;return j(),W("div",Vr,[de(Ae)?(j(),W("div",Hr,g[31]||(g[31]=[d("div",{class:"loading-spinner"},null,-1),d("div",{class:"loading-text"},"处理中...",-1)]))):J("",!0),U.value?(j(),W("div",zr,[d("div",qr,[g[32]||(g[32]=d("div",{class:"format-error-icon"},"⚠️",-1)),g[33]||(g[33]=d("div",{class:"format-error-title"},"文档格式不支持",-1)),d("div",Jr,ae(O.value),1),d("div",Yr,[d("button",{class:"retry-check-btn",onClick:g[0]||(g[0]=n=>at())},"重新检查")])])])):J("",!0),d("div",Kr,[d("div",Xr,ae(de(ce)||"未选择文档"),1),d("button",{class:"settings-btn",onClick:g[1]||(g[1]=n=>a.value=!0)},g[34]||(g[34]=[d("i",{class:"icon-settings"},null,-1)]))]),d("div",Gr,[d("div",Zr,[d("div",Qr,[g[35]||(g[35]=d("label",{for:"stage-select"},"年级:",-1)),Ye(d("select",{id:"stage-select","onUpdate:modelValue":g[2]||(g[2]=n=>bs(k)?k.value=n:null),class:"select-input",disabled:U.value},[(j(!0),W(wt,null,_t(de(m),n=>(j(),W("option",{key:n.value,value:n.value},ae(n.label),9,to))),128))],8,eo),[[ts,de(k)]])]),d("div",so,[g[36]||(g[36]=d("label",{for:"subject-select"},"学科:",-1)),Ye(d("select",{id:"subject-select","onUpdate:modelValue":g[3]||(g[3]=n=>bs(T)?T.value=n:null),class:"select-input",disabled:U.value},[(j(!0),W(wt,null,_t(Ze.value,n=>(j(),W("option",{key:n.value,value:n.value},ae(n.label),9,ao))),128))],8,no),[[ts,de(T)]]),me.value&&!me.value.isAdmin&&!me.value.isOwner&&me.value.subject?(j(),W("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((B=Ze.value.find(n=>n.value===me.value.subject))==null?void 0:B.label)||me.value.subject} 学科`}," 🔒 ",8,ro)):J("",!0)])]),J("",!0),d("div",oo,[d("button",{class:"action-btn primary",onClick:g[4]||(g[4]=n=>Je("wps-analysis")),disabled:C.value||U.value},[d("div",lo,[C.value?(j(),W("span",co)):J("",!0),g[37]||(g[37]=d("span",{class:"btn-text"},"解析",-1))])],8,io),J("",!0),((fe=(ee=me.value)==null?void 0:ee.orgs[0])==null?void 0:fe.orgId)===2?(j(),W("button",{key:1,class:"action-btn secondary",onClick:g[6]||(g[6]=n=>ot()),disabled:w.value||U.value},[d("div",po,[w.value?(j(),W("span",fo)):J("",!0),g[39]||(g[39]=d("span",{class:"btn-text"},"校对",-1))])],8,uo)):J("",!0)])]),d("div",ho,[s.value?(j(),W("div",{key:0,class:"modal-overlay",onClick:g[9]||(g[9]=n=>s.value=!1)},[d("div",{class:"modal-content",onClick:g[8]||(g[8]=ht(()=>{},["stop"]))},[d("div",go,[g[40]||(g[40]=d("div",{class:"modal-title"},"选中内容",-1)),d("button",{class:"modal-close",onClick:g[7]||(g[7]=n=>s.value=!1)},"×")]),d("div",vo,[d("pre",mo,ae(de(pe)||"未选中内容"),1)])])])):J("",!0),t.value?(j(),W("div",{key:1,class:"modal-overlay",onClick:g[13]||(g[13]=n=>t.value=!1)},[d("div",{class:"modal-content alert-modal",onClick:g[12]||(g[12]=ht(()=>{},["stop"]))},[d("div",bo,[g[41]||(g[41]=d("div",{class:"modal-title"},"提示",-1)),d("button",{class:"modal-close",onClick:g[10]||(g[10]=n=>t.value=!1)},"×")]),d("div",wo,[d("div",yo,ae(o.value),1),d("div",xo,[d("button",{class:"action-btn primary",onClick:g[11]||(g[11]=n=>t.value=!1)},"确定")])])])])):J("",!0),de(Be).show?(j(),W("div",To,[d("div",{class:"modal-content confirm-modal",onClick:g[17]||(g[17]=ht(()=>{},["stop"]))},[d("div",ko,[g[42]||(g[42]=d("div",{class:"modal-title"},"确认",-1)),d("button",{class:"modal-close",onClick:g[14]||(g[14]=n=>de(Z)(!1))},"×")]),d("div",Co,[d("div",$o,ae(de(Be).message),1),d("div",So,[d("button",{class:"action-btn secondary",onClick:g[15]||(g[15]=n=>de(Z)(!1))},"取消"),d("button",{class:"action-btn primary",onClick:g[16]||(g[16]=n=>de(Z)(!0))},"确定")])])])])):J("",!0),de(Ue).show?(j(),W("div",{key:3,class:"modal-overlay",onClick:g[21]||(g[21]=n=>de(ke)())},[d("div",{class:"modal-content alert-modal",onClick:g[20]||(g[20]=ht(()=>{},["stop"]))},[d("div",Eo,[d("div",_o,ae(de(Ue).title),1),d("button",{class:"modal-close",onClick:g[18]||(g[18]=n=>de(ke)())},"×")]),d("div",Ao,[d("div",Mo,ae(de(Ue).message),1),d("div",Do,[d("button",{class:"action-btn primary",onClick:g[19]||(g[19]=n=>de(ke)())},"确定")])])])])):J("",!0),d("div",Po,[d("div",Oo,[g[43]||(g[43]=d("div",{class:"queue-title"},"任务队列",-1)),d("div",Ro,[Ye(d("select",{id:"status-filter-select","onUpdate:modelValue":g[22]||(g[22]=n=>ne.value=n),class:"status-filter-select-input"},[(j(!0),W(wt,null,_t(P.value,n=>(j(),W("option",{key:n.value,value:n.value},ae(n.label),9,Io))),128))],512),[[ts,ne.value]])]),d("div",Uo,[d("button",{class:"release-all-btn",onClick:ft,disabled:de(Ge)()===0,title:de(Ge)()===0?"无可释放任务":`释放所有${de(Ge)()}个可释放任务`}," 一键释放 ",8,Lo)]),d("div",Fo,ae(Object.keys(Ve.value).length)+"个任务",1)]),Object.keys(Ve.value).length>0?(j(),W("div",jo,[d("table",{class:_e(["queue-table",{"narrow-view":R.value,"ultra-narrow-view":N.value}])},[d("thead",null,[d("tr",null,[d("th",Wo,[d("div",Bo,[g[45]||(g[45]=d("span",null,"任务ID",-1)),d("span",{class:"help-icon",onMouseenter:g[23]||(g[23]=n=>mt(n))},g[44]||(g[44]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("circle",{cx:"12",cy:"12",r:"10"}),d("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),d("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),R.value?J("",!0):(j(),W("th",No,[d("div",Vo,[g[46]||(g[46]=d("span",null,"题目内容",-1)),d("label",Ho,[Ye(d("input",{type:"checkbox","onUpdate:modelValue":g[24]||(g[24]=n=>te.value=n)},null,512),[[fn,te.value]]),d("span",{class:"slider round",title:te.value?"关闭题目预览":"开启题目预览"},null,8,zo)])])])),N.value?J("",!0):(j(),W("th",qo,"状态")),g[47]||(g[47]=d("th",{class:"col-actions"},"操作",-1))])]),d("tbody",null,[(j(!0),W(wt,null,_t(Fe.value,n=>(j(),W(wt,{key:n.type==="group"?n.groupId:n.tid},[n.type==="group"?(j(),W("tr",{key:0,class:"group-row",onClick:i=>rt(n.groupId)},[d("td",Yo,[d("div",Ko,[d("span",Xo,ae(n.isCollapsed?"▶":"▼"),1),d("span",Go,"已释放任务组 ("+ae(n.count)+"个)",1)])]),R.value?J("",!0):(j(),W("td",Zo,[d("div",Qo,ae(n.isCollapsed?"点击展开查看详情":"点击折叠隐藏详情"),1)])),N.value?J("",!0):(j(),W("td",ei,g[48]||(g[48]=[d("div",{class:"status-cell"},[d("span",{class:"task-tag status-released"},"已释放")],-1)]))),d("td",ti,[d("div",si,[d("span",ni,ae(n.isCollapsed?"展开":"折叠"),1)])])],8,Jo)):J("",!0),n.type==="group"&&!n.isCollapsed?(j(!0),W(wt,{key:1},_t(n.tasks,(i,l)=>(j(),W("tr",{key:i.tid,class:_e(["task-row group-task-row",[de(ge)(i.status),{"selected-task-row":i.tid===V.value}]]),onClick:c=>Qe(i.tid)},[d("td",ri,[d("div",{class:_e(["id-cell",{"id-with-status":N.value}])},[d("div",oi,[g[50]||(g[50]=d("span",{class:"group-indent"},"└─",-1)),d("span",ii,ae(i.tid.substring(0,8)),1),i.wordType==="wps-analysis"?(j(),W("span",li," 解 ")):J("",!0),i.wordType==="wps-enhance_analysis"||i.isEnhanced?(j(),W("span",ci," 解 ")):J("",!0),i.wordType==="wps-check"||i.isCheckTask?(j(),W("span",ui," 校 ")):J("",!0),xe(i)?(j(),W("span",{key:3,class:"soft-break-warning-icon",onMouseenter:g[25]||(g[25]=c=>Se(c))},g[49]||(g[49]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("title",null,"提示"),d("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),d("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),d("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):J("",!0)]),N.value?(j(),W("div",di,[d("span",{class:_e(["task-tag compact",de(ge)(i.status)])},ae(de(ye)(i.status)),3)])):J("",!0)],2)]),R.value?J("",!0):(j(),W("td",pi,[d("div",{class:"subject-cell",onMouseenter:c=>$t(c,i)},ae(vt(i)),41,fi)])),N.value?J("",!0):(j(),W("td",hi,[d("div",gi,[d("span",{class:_e(["task-tag",de(ge)(i.status)])},ae(de(ye)(i.status)),3)])])),g[51]||(g[51]=d("td",{class:"col-actions"},[d("div",{class:"task-actions"},[d("span",{class:"no-action-icon",title:"无可用操作"},[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[d("circle",{cx:"12",cy:"12",r:"10"}),d("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),d("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})])])])],-1))],10,ai))),128)):J("",!0),n.type==="task"?(j(),W("tr",{key:2,class:_e(["task-row",[de(ge)(n.status),{"selected-task-row":n.tid===V.value}]]),onClick:i=>Qe(n.tid)},[d("td",mi,[d("div",{class:_e(["id-cell",{"id-with-status":N.value}])},[d("div",bi,[d("span",wi,ae(n.tid.substring(0,8)),1),!n.isEnhanced&&!n.isCheckTask?(j(),W("span",yi," 解 ")):J("",!0),n.isEnhanced?(j(),W("span",xi," 解 ")):J("",!0),n.isCheckTask?(j(),W("span",Ti," 校 ")):J("",!0),xe(n)?(j(),W("span",{key:3,class:"soft-break-warning-icon",onMouseenter:g[26]||(g[26]=i=>Se(i))},g[52]||(g[52]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[d("title",null,"提示"),d("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),d("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),d("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):J("",!0)]),N.value?(j(),W("div",ki,[d("span",{class:_e(["task-tag compact",de(ge)(n.status)])},ae(de(ye)(n.status)),3)])):J("",!0)],2)]),R.value?J("",!0):(j(),W("td",Ci,[d("div",{class:"subject-cell",onMouseenter:i=>$t(i,n)},ae(vt(n)),41,$i)])),N.value?J("",!0):(j(),W("td",Si,[d("div",Ei,[d("span",{class:_e(["task-tag",de(ge)(n.status)])},ae(de(ye)(n.status)),3)])])),d("td",_i,[d("div",Ai,[n.status===1?(j(),W("button",{key:0,onClick:ht(i=>de(Me)(n.tid),["stop"]),class:"terminate-btn"}," 终止 ",8,Mi)):J("",!0),n.status===2||n.status===4?(j(),W("button",{key:1,onClick:ht(i=>it(n.tid),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Di)):J("",!0),n.status!==1&&n.status!==2&&n.status!==4?(j(),W("span",Pi,g[53]||(g[53]=[d("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[d("circle",{cx:"12",cy:"12",r:"10"}),d("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),d("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):J("",!0)])])],10,vi)):J("",!0)],64))),128))])],2)])):(j(),W("div",Oi,g[54]||(g[54]=[d("div",{class:"empty-text"},"暂无任务",-1)])))]),((be=(X=me.value)==null?void 0:X.orgs[0])==null?void 0:be.orgId)===2?(j(),W("div",Ri,[d("div",{class:"log-header",onClick:lt},[g[55]||(g[55]=d("div",{class:"log-title"},"执行日志",-1)),d("div",Ii,[d("button",{class:"clear-btn",onClick:g[27]||(g[27]=ht(n=>de(A)(),["stop"]))},"清空日志"),d("span",Ui,ae(v.value?"▼":"▶"),1)])]),v.value?(j(),W("div",{key:0,class:"log-content",innerHTML:de(h)},null,8,Li)):J("",!0)])):J("",!0)]),a.value?(j(),W("div",{key:2,class:"modal-overlay",onClick:g[30]||(g[30]=n=>a.value=!1)},[d("div",{class:"modal-content",onClick:g[29]||(g[29]=ht(()=>{},["stop"]))},[hn(fa,{onClose:g[28]||(g[28]=n=>a.value=!1)})])])):J("",!0)])}}},Wi=Bs(Fi,[["__scopeId","data-v-bb036c81"]]);export{Wi as default};
