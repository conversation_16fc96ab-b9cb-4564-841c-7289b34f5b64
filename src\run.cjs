// run_debug_commonjs.js

// Node.js 内建模块
const fs = require('node:fs'); // 使用 'node:' 前缀，更明确
const path = require('node:path'); // 使用 'node:' 前缀，更明确

// 第三方模块
let Oss;
try {
    Oss = require('ali-oss');
} catch (e) {
    // 尝试记录早期模块加载错误
    const earlyErrorLogDir = 'c:\\Temp';
    if (!fs.existsSync(earlyErrorLogDir)) {
        try {
            fs.mkdirSync(earlyErrorLogDir, { recursive: true });
        } catch (mkdirErr) {
            // 如果 c:\Temp 创建失败，则尝试在 CWD 记录
            console.error(`致命错误：无法创建目录 ${earlyErrorLogDir} 用于早期错误日志。错误：${mkdirErr.message}`);
        }
    }
    const earlyErrorLogPath = path.join(earlyErrorLogDir, 'app_module_load_error.log');
    const errorTimestamp = new Date().toISOString();
    const errorMessage = `${errorTimestamp} - 致命错误：无法加载 'ali-oss'。\n错误：${e.message}\n堆栈：${e.stack}\n当前工作目录：${process.cwd()}\n这通常意味着 'ali-oss' 未安装或 Node 的解析机制未找到。\n请确保在项目目录中运行 'npm install ali-oss'。\n---------------------------------------------------\n`;
    try {
        fs.appendFileSync(earlyErrorLogPath, errorMessage);
        console.error(`致命错误：无法加载 'ali-oss'。请查看 ${earlyErrorLogPath}`);
    } catch (logErr) {
        console.error("致命错误：无法加载 'ali-oss' 且无法写入早期错误日志:", logErr);
        console.error("原始 'ali-oss' 加载错误:", e);
    }
    process.exit(1); // 无法加载核心依赖，退出
}


// --- 全局错误捕获和日志记录 ---
const LOG_DIR = 'c:\\Temp';
const LOG_FILE_NAME = 'app_debug_commonjs.log';
let logFilePath = path.join(LOG_DIR, LOG_FILE_NAME);
let emergencyLogFilePath = path.join(process.cwd(), `app_emergency_${LOG_FILE_NAME}`); // 紧急日志仍在CWD

// 确保日志目录存在
try {
    if (!fs.existsSync(LOG_DIR)) {
        fs.mkdirSync(LOG_DIR, { recursive: true });
        console.log(`日志目录已创建：${LOG_DIR}`);
    }
} catch (e) {
    console.warn(`警告：无法创建日志目录 ${LOG_DIR}。错误：${e.message}。尝试使用当前工作目录作为紧急日志。`);
    logFilePath = emergencyLogFilePath; // 如果目标目录创建失败，主日志也用紧急日志路径
}


// 初始日志，美化
const writeInitialLog = () => {
    const startTime = new Date();
    const formattedStartTime = `${startTime.getFullYear()}-${(startTime.getMonth() + 1).toString().padStart(2, '0')}-${startTime.getDate().toString().padStart(2, '0')} ${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}:${startTime.getSeconds().toString().padStart(2, '0')}`;
    
    const initialLogMessages = [
        `\n`,,
        `═════════════════════════════════════════════════════════════════════════════════════════════════════════`,
        `                                应用程序已启动                                                            `,
        `═════════════════════════════════════════════════════════════════════════════════════════════════════════`,
        ` 启动时间           : ${formattedStartTime}`,
        ` 日志文件路径       : ${logFilePath}`,
        ` 平台               : ${process.platform}`,
        `════════════════════════════════════════════════════════════════════════════════════════════════════════\n`
    ];

    try {
        initialLogMessages.forEach(msg => fs.appendFileSync(logFilePath, msg + '\n'));
        console.log(initialLogMessages.join('\n').trim());
    } catch (e) {
        console.error("致命错误：无法将初始日志条目写入指定的日志路径:", logFilePath, e);
        // 尝试写入紧急日志
        try {
            const emergencyHeader = `紧急日志：无法写入 ${logFilePath}。使用当前工作目录下的紧急日志。\n原始错误：${e.stack}\n${new Date().toISOString()} - 应用程序已启动（紧急日志）\n`;
            fs.appendFileSync(emergencyLogFilePath, emergencyHeader + initialLogMessages.join('\n'));
            console.error("使用当前工作目录中的紧急日志文件:", emergencyLogFilePath);
            logFilePath = emergencyLogFilePath; // 后续日志也写入紧急日志文件
        } catch (emergencyError) {
            console.error("致命错误：同样无法写入当前工作目录中的紧急日志。仅记录到控制台。", emergencyError);
        }
    }
};

writeInitialLog();


function logToFile(level, message, data = '') {
  const now = new Date();
  const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}.${now.getMilliseconds().toString().padStart(3, '0')}`;
  
  const levelStr = `[${level.toUpperCase()}]`.padEnd(7); // [INFO] , [ERROR] 等
  let logMessage = `${timestamp} ${levelStr} | ${message}\n`;

  if (data) {
    let dataString;
    if (data instanceof Error) {
      dataString = `    错误详情：${data.message}\n    堆栈跟踪：\n${data.stack ? data.stack.split('\n').map(line => `      ${line.trim()}`).join('\n') : '      不适用'}\n`;
    } else if (typeof data === 'object') {
      try {
        dataString = `    数据：${JSON.stringify(data, null, 2).split('\n').map(line => `      ${line}`).join('\n')}\n`;
      } catch (jsonError) {
        dataString = `    数据：（无法序列化对象 - ${jsonError.message}）\n      ${String(data)}\n`;
      }
    } else {
      dataString = `    详情：${String(data)}\n`;
    }
    logMessage += dataString;
  }
  logMessage += `----------------------------------------------------------------------------------------------------------\n`; // 分隔线

  try {
    fs.appendFileSync(logFilePath, logMessage);
    // 控制台输出保持简洁，但可以加上颜色（如果环境支持ANSI颜色）
    const consoleColor = {
        ERROR: '\x1b[31m', // Red
        WARN: '\x1b[33m',  // Yellow
        INFO: '\x1b[32m',  // Green
        DEBUG: '\x1b[34m', // Blue
        RESET: '\x1b[0m'   // Reset color
    };
    const color = consoleColor[level.toUpperCase()] || consoleColor.RESET;
    const resetColor = consoleColor.RESET;

    if (level.toLowerCase() === 'error' || level.toLowerCase() === 'warn') {
        console.error(`${color}${timestamp} ${levelStr}| ${message}${resetColor}`, data || '');
    } else {
        console.log(`${color}${timestamp} ${levelStr}| ${message}${resetColor}`, data || '');
    }
  } catch (logErr) {
    // 如果日志写入失败，尝试在控制台打印完整信息
    console.error(`!!!!!!!!!! 致命错误：无法写入日志文件 (${logFilePath}) !!!!!!!!!!`, logErr);
    console.error('原始日志尝试内容：\n', logMessage);
  }
}

process.on('uncaughtException', (err, origin) => {
  logToFile('error', `未捕获的异常！来源：${origin}`, err);
  setTimeout(() => process.exit(1), 1000).unref(); // 给日志写入留一点时间
});

process.on('unhandledRejection', (reason, promise) => {
  logToFile('error', '未处理的Promise拒绝！', reason);
  logToFile('debug', '被拒绝的Promise:', promise);
  setTimeout(() => process.exit(1), 1000).unref();
});

logToFile('info', '全局错误处理程序已挂载。应用程序继续执行。');

// --- 原始代码逻辑 (已转换为 CommonJS 兼容) ---

// OSS配置信息和客户端
const ossInfo = {
    region: 'oss-cn-shanghai',
    accessKeyId: 'G2kh0AfonFa8hNBe', // 示例密钥，请替换
    accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T', // 示例密钥，请替换
    bucket: 'sigma-temp',
};

// 全局OSS客户端实例
let ossClient;
try {
    ossClient = new Oss({
        region: ossInfo.region,
        accessKeyId: ossInfo.accessKeyId,
        accessKeySecret: ossInfo.accessKeySecret,
        bucket: ossInfo.bucket,
        timeout: '120s' // 增加超时时间
    });
    logToFile('info', `OSS客户端初始化成功，bucket: ${ossInfo.bucket}`);
} catch (initError) {
    logToFile('error', '初始化OSS客户端失败，请检查OSS配置和网络', initError);
    process.exit(1); // OSS客户端初始化失败，程序无法继续运行
}

const sleep = (t = 1000) => {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve();
        }, t);
    });
};

const uploadFile = async (fileAbsolutePath, filenameToUpload) => {
    logToFile('info', `尝试上传: "${fileAbsolutePath}" 作为 "temp_docx/${filenameToUpload}"`);
    
    try {
        if (!fs.existsSync(fileAbsolutePath)) {
            logToFile('error', `找不到要上传的文件: ${fileAbsolutePath}`);
            return false;
        }
        const fileBuffer = fs.readFileSync(fileAbsolutePath);
        logToFile('info', `文件读取完成(大小: ${fileBuffer.length} bytes)。开始上传到 "temp_docx/${filenameToUpload}"`);
        
        const result = await ossClient.put(`temp_docx/${filenameToUpload}`, fileBuffer);
        if (result && result.res && result.res.status >= 200 && result.res.status < 300) {
            logToFile('info', `上传成功: "${filenameToUpload}". 状态: ${result.res.status} ${result.res.statusMessage}`);
            return true;
        } else {
            const status = result && result.res ? `${result.res.status} ${result.res.statusMessage}` : '未知状态';
            logToFile('error', `上传失败: "${filenameToUpload}". OSS响应: ${status}`, result);
            return false;
        }
    } catch (e) {
        console.log(e);
        logToFile('error', `上传过程中出现异常: "${filenameToUpload}"`, e);
        return false;
    }
};

const run = async () => {
    logToFile('info', '开始主循环。');
    const dirToWatch = 'c:\\Temp'; // 监控的目录
    logToFile('info', `监控目标目录: "${dirToWatch}"`);
    
    const uploadedFilesMap = {}; // 内存中记录已上传文件

    // eslint-disable-next-line no-constant-condition
    while (true) {
        try {
            if (!fs.existsSync(dirToWatch)) {
                logToFile('warn', `监控目录"${dirToWatch}"不存在。尝试创建。`);
                try {
                    fs.mkdirSync(dirToWatch, { recursive: true });
                    logToFile('info', `监控目录"${dirToWatch}"创建成功。`);
                } catch (mkdirError) {
                    logToFile('error', `创建监控目录"${dirToWatch}"失败。休眠后重试。`, mkdirError);
                    await sleep(15000); // 创建目录失败，等待更长时间
                    continue; // 跳过本次循环的剩余部分
                }
            }

            let filesInDir;
            try {
                filesInDir = fs.readdirSync(dirToWatch);
            } catch (readdirError) {
                logToFile('error', `读取目录"${dirToWatch}"失败。权限问题？休眠后重试。`, readdirError);
                await sleep(15000);
                continue;
            }
            
            const docxFileRegex = /^[a-zA-Z0-9]{8}\.docx$/; // 正则表达式匹配8位字母数字+.docx后缀
            const docxFiles = filesInDir.filter(f => docxFileRegex.test(f));
            if (docxFiles.length > 0) {
                // logToFile('debug', `发现${docxFiles.length}个匹配模式"${docxFileRegex}"的文件: ${docxFiles.join(', ')}`);
            }

            const newFilesToUpload = docxFiles.filter(f => !uploadedFilesMap[f]);
            
            if (!newFilesToUpload.length) {
                logToFile('info', '没有符合条件的新文件需要上传。');
            } else {
                logToFile('info', `发现${newFilesToUpload.length}个新文件需要上传: ${newFilesToUpload.join(', ')}`);
                
                for (const file of newFilesToUpload) {
                    const filePath = path.join(dirToWatch, file); // 使用 path.join 构造安全路径
                    logToFile('info', `正在处理文件准备上传: ${file}`);
                    const success = await uploadFile(filePath, file);
                    if (success) {
                        // 删除成功上传的文件
                        try {
                            fs.unlinkSync(filePath);
                            logToFile('info', `成功删除已上传的文件: "${file}"`);
                        } catch (deleteError) {
                            logToFile('warn', `无法删除已上传的文件: "${file}"`, deleteError);
                        }
                        uploadedFilesMap[file] = true; // 标记为已成功处理
                        logToFile('info', `成功上传并标记: "${file}"`);
                    } else {
                        logToFile('warn', `上传"${file}"失败。将在未来循环中重试。`);
                        // 可选：如果失败次数过多，可以将其加入一个临时忽略列表
                        // uploadedFilesMap[file] = { status: 'failed', attempts: (uploadedFilesMap[file]?.attempts || 0) + 1 };
                    }
                }
                logToFile('info', `本次已完成处理${newFilesToUpload.length}个新文件。`);
            }
        } catch (loopError) {
            logToFile('error', '主循环中的严重错误！', loopError);
            // 发生此类错误时，可能需要更长的等待时间或特定恢复逻辑
            await sleep(3000); // 为防止快速连续失败，这里增加等待时间
        }
        await sleep(3000); // 每次扫描循环结束后等待10秒
    }
};

// 启动主程序
logToFile('info', '尝试启动主程序。');
run().catch(err => {
    // 这个 catch 理论上不应被触发，因为 run() 是无限循环且内部有 try/catch
    // 并且 unhandledRejection 应该已捕获 Promise 链中的未处理拒绝
    logToFile('error', '致命错误：主主程序Promise在其循环外部意外被拒绝！', err);
    setTimeout(() => process.exit(1), 1000).unref();
});

