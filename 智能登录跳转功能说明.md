# 智能登录跳转功能说明

## 问题描述
之前登录成功后会直接跳转到 `/taskpane` 路由，没有考虑用户当前打开的窗格类型，导致用户体验不佳。

## 解决方案
实现了智能跳转功能，根据用户当前打开的窗格类型来决定跳转到解题窗口还是切割窗口。

## 实现细节

### 1. 新增工具函数 (`src/components/js/util.js`)

#### `detectCurrentPaneType()`
- **功能**: 检测当前用户打开的窗格类型
- **返回值**: 
  - `'taskpane'` - 解题窗格
  - `'image-split'` - 切割窗格  
  - `null` - 未检测到或检测失败

#### `redirectToCurrentPane(defaultRoute)`
- **功能**: 根据当前窗格类型进行智能跳转
- **参数**: `defaultRoute` - 默认路由（默认为 `/taskpane`）
- **逻辑**: 
  - 检测当前窗格类型
  - 根据类型跳转到对应路由
  - 如果检测失败，使用默认路由

### 2. 修改的文件

#### `src/components/Login.vue`
- 导入 `Util` 工具类
- 将硬编码的 `window.location.hash = '#/taskpane'` 替换为 `Util.redirectToCurrentPane('/taskpane')`

#### `src/components/js/auth.js`
- 导入 `Util` 工具类
- 在 `setupAuthEvents` 函数中使用智能跳转

#### `src/App.vue`
- 导入 `Util` 工具类
- 在重连和初始化登录检查中使用智能跳转

## 检测逻辑

系统通过以下方式检测当前窗格类型：

1. **获取当前文档ID**: `window.Application.ActiveDocument.DocID`
2. **检查解题窗格**: 
   - 查找存储键: `taskpane_id_${docId}`
   - 检查窗格是否可见: `taskpane.Visible`
3. **检查切割窗格**:
   - 查找存储键: `imagesplit_taskpane_id_${docId}`
   - 检查窗格是否可见: `imageSplitPane.Visible`

## 使用场景

### 场景1: 用户在解题窗格中登录
- 检测到解题窗格可见
- 登录成功后跳转到 `/taskpane`

### 场景2: 用户在切割窗格中登录  
- 检测到切割窗格可见
- 登录成功后跳转到 `/image-split`

### 场景3: 无法检测到窗格类型
- 使用默认路由 `/taskpane`
- 确保用户始终能正常使用

## 优势

1. **智能化**: 根据用户当前使用场景自动跳转
2. **用户体验**: 避免用户登录后需要手动切换窗格
3. **向后兼容**: 检测失败时使用默认行为，不影响现有功能
4. **可扩展**: 未来可以轻松添加更多窗格类型的支持

## 测试建议

1. 在解题窗格中测试登录跳转
2. 在切割窗格中测试登录跳转
3. 在没有打开任何窗格时测试登录跳转
4. 测试重连后的自动跳转功能
