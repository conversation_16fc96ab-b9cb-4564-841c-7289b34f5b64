import{U as Ks,r as se,h as ct,v as Oe,i as Y,j as Ct,k as Es,m as qt,_ as $s,n as Xs,o as j,c as W,a as v,p as Js,t as te,f as Z,q as ke,w as _e,s as _t,e as Wt,F as xt,u as Tt,x as ut,y as Gs,z as re,A as Bt,B as rs,C as st,D as Zs,E as Qs}from"./index-BJHpI8Oo.js";function en(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=Ks.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const tn={onbuttonclick:en};var sn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function nn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function an(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var i=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,i.get?i:{enumerable:!0,get:function(){return e[a]}})}),t}var As={exports:{}};const rn={},on=Object.freeze(Object.defineProperty({__proto__:null,default:rn},Symbol.toStringTag,{value:"Module"})),os=an(on);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",i=a?window:{};i.JS_SHA1_NO_WINDOW&&(a=!1);var g=!a&&typeof self=="object",x=!i.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;x?i=sn:g&&(i=self);var E=!i.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,w=!i.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",T="0123456789abcdef".split(""),$=[-**********,8388608,32768,128],P=[24,16,8,0],I=["hex","array","digest","arrayBuffer"],D=[],V=Array.isArray;(i.JS_SHA1_NO_NODE_JS||!V)&&(V=function(p){return Object.prototype.toString.call(p)==="[object Array]"});var U=ArrayBuffer.isView;w&&(i.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!U)&&(U=function(p){return typeof p=="object"&&p.buffer&&p.buffer.constructor===ArrayBuffer});var N=function(p){var y=typeof p;if(y==="string")return[p,!0];if(y!=="object"||p===null)throw new Error(s);if(w&&p.constructor===ArrayBuffer)return[new Uint8Array(p),!1];if(!V(p)&&!U(p))throw new Error(s);return[p,!1]},K=function(p){return function(y){return new M(!0).update(y)[p]()}},L=function(){var p=K("hex");x&&(p=B(p)),p.create=function(){return new M},p.update=function(C){return p.create().update(C)};for(var y=0;y<I.length;++y){var k=I[y];p[k]=K(k)}return p},B=function(p){var y=os,k=os.Buffer,C;k.from&&!i.JS_SHA1_NO_BUFFER_FROM?C=k.from:C=function(A){return new k(A)};var m=function(A){if(typeof A=="string")return y.createHash("sha1").update(A,"utf8").digest("hex");if(A==null)throw new Error(s);return A.constructor===ArrayBuffer&&(A=new Uint8Array(A)),V(A)||U(A)||A.constructor===k?y.createHash("sha1").update(C(A)).digest("hex"):p(A)};return m},f=function(p){return function(y,k){return new J(y,!0).update(k)[p]()}},X=function(){var p=f("hex");p.create=function(C){return new J(C)},p.update=function(C,m){return p.create(C).update(m)};for(var y=0;y<I.length;++y){var k=I[y];p[k]=f(k)}return p};function M(p){p?(D[0]=D[16]=D[1]=D[2]=D[3]=D[4]=D[5]=D[6]=D[7]=D[8]=D[9]=D[10]=D[11]=D[12]=D[13]=D[14]=D[15]=0,this.blocks=D):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}M.prototype.update=function(p){if(this.finalized)throw new Error(t);var y=N(p);p=y[0];for(var k=y[1],C,m=0,A,_=p.length||0,S=this.blocks;m<_;){if(this.hashed&&(this.hashed=!1,S[0]=this.block,this.block=S[16]=S[1]=S[2]=S[3]=S[4]=S[5]=S[6]=S[7]=S[8]=S[9]=S[10]=S[11]=S[12]=S[13]=S[14]=S[15]=0),k)for(A=this.start;m<_&&A<64;++m)C=p.charCodeAt(m),C<128?S[A>>>2]|=C<<P[A++&3]:C<2048?(S[A>>>2]|=(192|C>>>6)<<P[A++&3],S[A>>>2]|=(128|C&63)<<P[A++&3]):C<55296||C>=57344?(S[A>>>2]|=(224|C>>>12)<<P[A++&3],S[A>>>2]|=(128|C>>>6&63)<<P[A++&3],S[A>>>2]|=(128|C&63)<<P[A++&3]):(C=65536+((C&1023)<<10|p.charCodeAt(++m)&1023),S[A>>>2]|=(240|C>>>18)<<P[A++&3],S[A>>>2]|=(128|C>>>12&63)<<P[A++&3],S[A>>>2]|=(128|C>>>6&63)<<P[A++&3],S[A>>>2]|=(128|C&63)<<P[A++&3]);else for(A=this.start;m<_&&A<64;++m)S[A>>>2]|=p[m]<<P[A++&3];this.lastByteIndex=A,this.bytes+=A-this.start,A>=64?(this.block=S[16],this.start=A-64,this.hash(),this.hashed=!0):this.start=A}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},M.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var p=this.blocks,y=this.lastByteIndex;p[16]=this.block,p[y>>>2]|=$[y&3],this.block=p[16],y>=56&&(this.hashed||this.hash(),p[0]=this.block,p[16]=p[1]=p[2]=p[3]=p[4]=p[5]=p[6]=p[7]=p[8]=p[9]=p[10]=p[11]=p[12]=p[13]=p[14]=p[15]=0),p[14]=this.hBytes<<3|this.bytes>>>29,p[15]=this.bytes<<3,this.hash()}},M.prototype.hash=function(){var p=this.h0,y=this.h1,k=this.h2,C=this.h3,m=this.h4,A,_,S,F=this.blocks;for(_=16;_<80;++_)S=F[_-3]^F[_-8]^F[_-14]^F[_-16],F[_]=S<<1|S>>>31;for(_=0;_<20;_+=5)A=y&k|~y&C,S=p<<5|p>>>27,m=S+A+m+1518500249+F[_]<<0,y=y<<30|y>>>2,A=p&y|~p&k,S=m<<5|m>>>27,C=S+A+C+1518500249+F[_+1]<<0,p=p<<30|p>>>2,A=m&p|~m&y,S=C<<5|C>>>27,k=S+A+k+1518500249+F[_+2]<<0,m=m<<30|m>>>2,A=C&m|~C&p,S=k<<5|k>>>27,y=S+A+y+1518500249+F[_+3]<<0,C=C<<30|C>>>2,A=k&C|~k&m,S=y<<5|y>>>27,p=S+A+p+1518500249+F[_+4]<<0,k=k<<30|k>>>2;for(;_<40;_+=5)A=y^k^C,S=p<<5|p>>>27,m=S+A+m+1859775393+F[_]<<0,y=y<<30|y>>>2,A=p^y^k,S=m<<5|m>>>27,C=S+A+C+1859775393+F[_+1]<<0,p=p<<30|p>>>2,A=m^p^y,S=C<<5|C>>>27,k=S+A+k+1859775393+F[_+2]<<0,m=m<<30|m>>>2,A=C^m^p,S=k<<5|k>>>27,y=S+A+y+1859775393+F[_+3]<<0,C=C<<30|C>>>2,A=k^C^m,S=y<<5|y>>>27,p=S+A+p+1859775393+F[_+4]<<0,k=k<<30|k>>>2;for(;_<60;_+=5)A=y&k|y&C|k&C,S=p<<5|p>>>27,m=S+A+m-1894007588+F[_]<<0,y=y<<30|y>>>2,A=p&y|p&k|y&k,S=m<<5|m>>>27,C=S+A+C-1894007588+F[_+1]<<0,p=p<<30|p>>>2,A=m&p|m&y|p&y,S=C<<5|C>>>27,k=S+A+k-1894007588+F[_+2]<<0,m=m<<30|m>>>2,A=C&m|C&p|m&p,S=k<<5|k>>>27,y=S+A+y-1894007588+F[_+3]<<0,C=C<<30|C>>>2,A=k&C|k&m|C&m,S=y<<5|y>>>27,p=S+A+p-1894007588+F[_+4]<<0,k=k<<30|k>>>2;for(;_<80;_+=5)A=y^k^C,S=p<<5|p>>>27,m=S+A+m-899497514+F[_]<<0,y=y<<30|y>>>2,A=p^y^k,S=m<<5|m>>>27,C=S+A+C-899497514+F[_+1]<<0,p=p<<30|p>>>2,A=m^p^y,S=C<<5|C>>>27,k=S+A+k-899497514+F[_+2]<<0,m=m<<30|m>>>2,A=C^m^p,S=k<<5|k>>>27,y=S+A+y-899497514+F[_+3]<<0,C=C<<30|C>>>2,A=k^C^m,S=y<<5|y>>>27,p=S+A+p-899497514+F[_+4]<<0,k=k<<30|k>>>2;this.h0=this.h0+p<<0,this.h1=this.h1+y<<0,this.h2=this.h2+k<<0,this.h3=this.h3+C<<0,this.h4=this.h4+m<<0},M.prototype.hex=function(){this.finalize();var p=this.h0,y=this.h1,k=this.h2,C=this.h3,m=this.h4;return T[p>>>28&15]+T[p>>>24&15]+T[p>>>20&15]+T[p>>>16&15]+T[p>>>12&15]+T[p>>>8&15]+T[p>>>4&15]+T[p&15]+T[y>>>28&15]+T[y>>>24&15]+T[y>>>20&15]+T[y>>>16&15]+T[y>>>12&15]+T[y>>>8&15]+T[y>>>4&15]+T[y&15]+T[k>>>28&15]+T[k>>>24&15]+T[k>>>20&15]+T[k>>>16&15]+T[k>>>12&15]+T[k>>>8&15]+T[k>>>4&15]+T[k&15]+T[C>>>28&15]+T[C>>>24&15]+T[C>>>20&15]+T[C>>>16&15]+T[C>>>12&15]+T[C>>>8&15]+T[C>>>4&15]+T[C&15]+T[m>>>28&15]+T[m>>>24&15]+T[m>>>20&15]+T[m>>>16&15]+T[m>>>12&15]+T[m>>>8&15]+T[m>>>4&15]+T[m&15]},M.prototype.toString=M.prototype.hex,M.prototype.digest=function(){this.finalize();var p=this.h0,y=this.h1,k=this.h2,C=this.h3,m=this.h4;return[p>>>24&255,p>>>16&255,p>>>8&255,p&255,y>>>24&255,y>>>16&255,y>>>8&255,y&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,C>>>24&255,C>>>16&255,C>>>8&255,C&255,m>>>24&255,m>>>16&255,m>>>8&255,m&255]},M.prototype.array=M.prototype.digest,M.prototype.arrayBuffer=function(){this.finalize();var p=new ArrayBuffer(20),y=new DataView(p);return y.setUint32(0,this.h0),y.setUint32(4,this.h1),y.setUint32(8,this.h2),y.setUint32(12,this.h3),y.setUint32(16,this.h4),p};function J(p,y){var k,C=N(p);if(p=C[0],C[1]){var m=[],A=p.length,_=0,S;for(k=0;k<A;++k)S=p.charCodeAt(k),S<128?m[_++]=S:S<2048?(m[_++]=192|S>>>6,m[_++]=128|S&63):S<55296||S>=57344?(m[_++]=224|S>>>12,m[_++]=128|S>>>6&63,m[_++]=128|S&63):(S=65536+((S&1023)<<10|p.charCodeAt(++k)&1023),m[_++]=240|S>>>18,m[_++]=128|S>>>12&63,m[_++]=128|S>>>6&63,m[_++]=128|S&63);p=m}p.length>64&&(p=new M(!0).update(p).array());var F=[],ae=[];for(k=0;k<64;++k){var oe=p[k]||0;F[k]=92^oe,ae[k]=54^oe}M.call(this,y),this.update(ae),this.oKeyPad=F,this.inner=!0,this.sharedMemory=y}J.prototype=new M,J.prototype.finalize=function(){if(M.prototype.finalize.call(this),this.inner){this.inner=!1;var p=this.array();M.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(p),M.prototype.finalize.call(this)}};var G=L();G.sha1=G,G.sha1.hmac=X(),E?e.exports=G:i.sha1=G})()})(As);var ln=As.exports;const cn=nn(ln);function is(){return"http://worksheet.hexinedu.com"}function dt(){return"http://127.0.0.1:3000"}function un(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const pt=async(e,s,t,a={},i=8e3)=>{try{return await Promise.race([e(),new Promise((g,x)=>setTimeout(()=>x(new Error("WebSocket请求超时，切换到HTTP")),i))])}catch{try{let x;return s==="get"?x=await Ct.get(t,{params:a}):s==="post"?x=await Ct.post(t,a):s==="delete"&&(x=await Ct.delete(t)),x.data}catch(x){throw new Error(`请求失败: ${x.message||"未知错误"}`)}}};function dn(e,s,t,a){const i=[e,s,t,a].join(":");return cn(i)}function pn(){const e=se(""),s=se(""),t=se(""),a=ct({}),i=se(""),g=se("");let x="",E=null;const w=se("c:\\Temp"),T=ct({appKey:"",appSecret:""}),$=ct({show:!1,message:"",resolveCallback:null,rejectCallback:null}),P=se(""),I=se("junior"),D=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],V=()=>Oe.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],U=ct(V()),N=async()=>{try{const n=await Y.getWatcherStatus();n.data&&n.data.watchDir&&(w.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${w.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},K=async()=>{var n,r,o;try{if(!T.appKey||!T.appSecret)throw new Error("未初始化app信息");const h=60,u=Date.now();let d;try{const ee=window.Application.PluginStorage.getItem("token_info");ee&&(d=JSON.parse(ee))}catch(ee){d=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${ee.message}</span><br/>`}if(d&&d.access_token&&d.expired_time>u+h*1e3)return d.access_token;const b=T.appKey,O="1234567",R=Math.floor(Date.now()/1e3),q=dn(b,O,T.appSecret,R),z=await Ct.get(is()+"/api/open/account/v1/auth/token",{params:{app_key:b,app_nonstr:O,app_timestamp:R,app_signature:q}});if((r=(n=z.data)==null?void 0:n.data)!=null&&r.access_token){const ee=z.data.data.access_token;let ne;if(z.data.data.expired_time){const he=parseInt(z.data.data.expired_time);ne=u+he*1e3,t.value+=`<span class="log-item info">token已更新，有效期${he}秒</span><br/>`}else ne=u+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const le={access_token:ee,expired_time:ne};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(le))}catch(he){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${he.message}</span><br/>`}return ee}else throw new Error(((o=z.data)==null?void 0:o.message)||"获取access_token失败")}catch(h){throw t.value+=`<span class="log-item error">获取access_token失败: ${h.message}</span><br/>`,h}},L=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},B=()=>{const n=window.Application,r=n.Documents.Count;for(let o=1;o<=r;o++){const h=n.Documents.Item(o);if(h.DocID===i.value)return h}return null},f=()=>{try{const n=B();if(!n)return{isValid:!1,message:"未找到当前文档"};let r="";try{r=n.Name||""}catch{try{r=y("getDocName")||""}catch{r=""}}if(r){const o=r.toLowerCase();return o.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:o.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},X=n=>n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",M=n=>n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"进行中",J=n=>{const r=Date.now()-n,o=Math.floor(r/1e3);return o<60?`${o}秒`:o<3600?`${Math.floor(o/60)}分${o%60}秒`:`${Math.floor(o/3600)}时${Math.floor(o%3600/60)}分`},G=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const o=B();if(o&&o.ContentControls)for(let h=1;h<=o.ContentControls.Count;h++)try{const u=o.ContentControls.Item(h);if(u&&u.Title&&(u.Title===`任务_${n}`||u.Title===`任务增强_${n}`)){const d=u.Title===`任务增强_${n}`||a[n].isEnhanced;u.Title=d?`已停止增强_${n}`:`已停止_${n}`,t.value+=`<span class="log-item info">已将${d?"增强":"普通"}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(o){t.value+=`<span class="log-item warning">更新控件标题失败: ${o.message}</span><br/>`}let r=null;if(H[n]&&H[n].urlId){r=H[n].urlId;try{try{const o=await pt(async()=>await Y.getUrlMonitorStatus(),"get",`${dt()}/api/url/status`),u=(Array.isArray(o)?o:o.data?Array.isArray(o.data)?o.data:[]:[]).find(d=>d.urlId===r);u&&u.downloadedPath&&(a[n].resultFile=u.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${u.downloadedPath}</span><br/>`)}catch(o){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${o.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ie(r),delete H[n]}catch(o){t.value+=`<span class="log-item warning">停止URL监控出错: ${o.message}，将重试</span><br/>`,delete H[n],setTimeout(async()=>{try{r&&await pt(async()=>await Y.stopUrlMonitoring(r),"delete",`${dt()}/api/url/monitor/${r}`)}catch(h){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${h.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(r){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${r.message}</span><br/>`,H[n]&&delete H[n]}},p=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const r=B();if(r&&r.ContentControls)for(let h=1;h<=r.ContentControls.Count;h++)try{const u=r.ContentControls.Item(h);if(u&&u.Title&&(u.Title===`任务_${n}`||u.Title===`任务增强_${n}`)){const d=u.Title===`任务增强_${n}`||a[n].isEnhanced;u.Title=d?`已停止增强_${n}`:`已停止_${n}`,u.LockContents=!1,t.value+=`<span class="log-item info">已将${d?"增强":"普通"}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let o=null;if(H[n]&&H[n].urlId){o=H[n].urlId;try{const h=await Y.getUrlMonitorStatus(),d=(Array.isArray(h)?h:h.data?Array.isArray(h.data)?h.data:[]:[]).find(b=>b.urlId===o);d&&d.downloadedPath&&(a[n].resultFile=d.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${d.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ie(o),delete H[n]}catch(h){t.value+=`<span class="log-item warning">停止URL监控出错: ${h.message}，将重试</span><br/>`,delete H[n]}}},y=n=>tn.onbuttonclick(n),k=()=>{try{e.value=y("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},C=()=>{B().ActiveWindow.Selection.Copy()},m=n=>{const r=window.Application.Documents.Add();r.Content.Paste(),r.SaveAs2(`${w.value}\\${n}`,12,"","",!1),r.Close(),B().ActiveWindow.Activate()},A=n=>{const r=window.Application.Documents.Add("",!1,0,!1);r.Content.Paste(),r.SaveAs2(`${w.value}\\${n}`,12,"","",!1),r.Close()},_=n=>{try{const o=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,h=window.Application.Documents.Add("",!1,0,!1);h.Content.Paste();const u=`${o}\\${n}`;h.SaveAs2(u,12,"","",!1),h.Close(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${u}.docx</span><br/>`}catch(r){throw t.value+=`<span class="log-item error">方式三保存失败: ${r.message}</span><br/>`,r}},S=n=>{const r=window.Application.Documents.Add();r.Content.Paste(),r.SaveAs2(`${w.value}\\${n}`,12,"","",!1),B().ActiveWindow.Activate()},F=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let r="method2";try{const o=await Y.getSaveMethod();if(o.success&&o.saveMethod){r=o.saveMethod;const h=r==="method1"?"方式一":r==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${h}</span><br/>`}}catch(o){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${o.message}</span><br/>`}r==="method1"?(m(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):r==="method2"?(A(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):r==="method3"?(_(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):r==="method4"&&(S(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${w.value}\\${n}.docx</span><br/>`),Y.associateFileWithClient(`${n}.docx`).then(o=>{o.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${o.message||"未知错误"}</span><br/>`}).catch(o=>{t.value+=`<span class="log-item warning">关联文件时出错: ${o.message}</span><br/>`})}catch(r){t.value+=`<span class="log-item error">保存文件失败: ${r.message}</span><br/>`}},ae=se(null),oe=ct([]),ce=new Set,Ae=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),ve=async n=>{try{const r=n.slice(x.length);if(r.trim()){const o=Y.getClientId();if(!o){console.warn("无法获取客户端ID，跳过日志同步");return}const h=Ae(r);if(!h.trim()){x=n;return}await Y.sendRequest("logger","syncLog",{content:h,timestamp:new Date().toISOString(),clientId:o}),x=n}}catch(r){console.error("同步日志到服务端失败:",r)}},we=()=>{Y.connect().then(()=>{N()}).catch(r=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${r.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const r=[];for(const o in a)if(a.hasOwnProperty(o)){const h=a[o];h.status===1&&!h.terminated&&(r.includes(o)||r.push(o))}if(r.length>0){let o=!1;try{const h=B();if(h){const d=`taskpane_id_${h.DocID}`,b=window.Application.PluginStorage.getItem(d);if(b){const O=window.Application.GetTaskPane(b);O&&(o=O.Visible)}}}catch(h){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${h.message}</span><br/>`,o=!0}setTimeout(()=>{if(o){const h=`检测到 ${r.length} 个未完成的任务，是否继续？`;it(h).then(u=>{u?(t.value+=`<span class="log-item info">用户选择继续 ${r.length} 个进行中的任务 (by taskId)...</span><br/>`,Y.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:r}).then(d=>{d&&d.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${d.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(d==null?void 0:d.message)||"未知错误"}</span><br/>`}).catch(d=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${d.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',r.forEach(d=>{G(d)}),t.value+=`<span class="log-item success">${r.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(u=>{t.value+=`<span class="log-item error">弹窗错误: ${u.message}，默认停止任务（保留控件）</span><br/>`,r.forEach(d=>{G(d)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Y.setConnectionSuccessHandler(n),Y.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),Y.addEventListener("connection",r=>{r.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${r.reason||"未知"}, 代码: ${r.code||"N/A"}，将自动重连</span><br/>`)}),Y.addEventListener("watcher",r=>{var o,h;if(oe.push(r),r.type,r.eventType==="uploadSuccess"){const u=(o=r.data)==null?void 0:o.file,d=u==null?void 0:u.replace(/\.docx$/,""),b=`${r.eventType}_${u}_${Date.now()}`;if(ce.has(b)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${u}</span><br/>`;return}if(ce.add(b),ce.size>100){const R=ce.values();ce.delete(R.next().value)}const O=d&&((h=a[d])==null?void 0:h.wordType);Ce(r,O)}else r.eventType&&Ce(r,null)}),Y.addEventListener("urlMonitor",r=>{oe.push(r),r.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${r.eventType||r.action}</span><br/>`),r.eventType&&Ce(r,null)}),Y.addEventListener("health",r=>{}),Y.addEventListener("error",r=>{const o=r.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${o}</span><br/>`,console.error("WebSocket错误:",r)})},H=ct({}),ue=async(n,r,o=!1,h=5e3,u={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const d=await Y.startUrlMonitoring(n,h,{downloadOnSuccess:u.downloadOnSuccess!==void 0?u.downloadOnSuccess:!0,appKey:u.appKey,filename:u.filename,taskId:r});t.value+=`<span class="log-item info">URL监控请求数据: ${JSON.stringify(d)}</span><br/>`;const b=d.success||(d==null?void 0:d.success),O=d.urlId||(d==null?void 0:d.urlId);if(b&&O){H[r]={urlId:O,url:n,isResultUrl:o,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${O}</span><br/>`;try{await Y.startUrlChecking(O)}catch{}return O}else throw new Error("服务器返回失败")}catch(d){return t.value+=`<span class="log-item error">启动URL监控失败: ${d.message}</span><br/>`,null}},ie=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(H).forEach(o=>{H[o].urlId===n&&delete H[o]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const r=await pt(async()=>await Y.stopUrlMonitoring(n),"delete",`${dt()}/api/url/monitor/${n}`);return r&&(r.success||r!=null&&r.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(r){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${r.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await pt(async()=>await Y.stopUrlMonitoring(n),"delete",`${dt()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},Ie=async()=>{try{const n=await pt(async()=>await Y.getUrlMonitorStatus(),"get",`${dt()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Q=async n=>{try{return await Y.forceUrlCheck(n)}catch{return!1}},Ce=async(n,r)=>{var o;if(n.eventType==="uploadSuccess"){const h=n.data.file,u=h.replace(/\.docx$/,"");if(a[u]){if(a[u].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${u.substring(0,8)} 的上传通知</span><br/>`;return}if(a[u].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${u.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${h} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${r||a[u].wordType||"wps-analysis"}</span><br/>`,a[u].status=1,a[u].uploadSuccess=!0,await ye(u,r||a[u].wordType||"wps-analysis")}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:h,url:u,taskId:d,downloadedPath:b}=n.data,O=Object.keys(H).filter(R=>H[R].urlId===h);O.length>0&&O.forEach(R=>{b&&a[R]&&(a[R].resultFile=b,a[R].resultDownloaded=!0),delete H[R]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:h,url:u,filePath:d,taskId:b}=n.data;if(!Object.values(H).some(R=>R.urlId===h)&&b&&((o=a[b])!=null&&o.terminated))return;if(b&&a[b]&&a[b].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${h} 的文件下载通知</span><br/>`,h)try{await Y.stopUrlMonitoring(h),H[b]&&delete H[b]}catch{}return}if(b&&a[b]){t.value+=`<span class="log-item success">收到结果文件通知: ${d}</span><br/>`,a[b].resultFile=d,a[b].resultDownloaded=!0;const R=J(a[b].startTime);t.value+=`<span class="log-item success">任务${b.substring(0,8)}完成，总耗时${R}</span><br/>`,await Le(b),H[b]&&H[b].urlId&&(ie(H[b].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete H[b])}else if(h){t.value+=`<span class="log-item info">URL文件已下载: ${d}</span><br/>`;const R=Object.keys(H).filter(q=>{var z;return H[q].urlId===h&&!((z=a[q])!=null&&z.terminated)});R.length>0&&R.forEach(q=>{if(a[q]&&(t.value+=`<span class="log-item info">关联到任务: ${q.substring(0,8)}</span><br/>`,a[q].resultFile=d,a[q].resultDownloaded=!0,a[q].status===1)){a[q].status=2;const z=J(a[q].startTime);t.value+=`<span class="log-item success">任务${q.substring(0,8)}完成，总耗时${z}</span><br/>`}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:h,url:u,error:d,taskId:b}=n.data;if(b&&a[b]&&a[b].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${b.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${d}</span><br/>`,b&&a[b]&&(a[b].status=-1,a[b].errorMessage=`下载失败: ${d}`,de(b),H[b]&&delete H[b]),h)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${h}</span><br/>`,await pt(async()=>await Y.stopUrlMonitoring(h),"delete",`${dt()}/api/url/monitor/${h}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},ye=async(n,r="wps-analysis")=>{try{if(!T.appKey)throw new Error("未初始化appKey信息");const o=await K(),h=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,u=await Ct.post(is()+"/api/open/ticket/v1/ai_comment/create",{access_token:o,data:{app_key:T.appKey,subject:P.value,stage:I.value,file_name:`${n}`,word_url:h,word_type:r,is_ai_auto:!0,is_ai_edit:!0,create_user_id:T.userId,create_username:T.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),d=u.data.data.ticket_id;if(!d)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",de(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${d}，开始监控结果文件</span><br/>`;const b=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${d}.wps.docx`,O={downloadOnSuccess:!0,filename:`${d}.wps.docx`,appKey:T.appKey,ticketId:d,taskId:n},R=await ue(b,n,!0,3e3,O);return a[n]&&(a[n].ticketId=d,a[n].resultUrl=b,a[n].urlMonitorId=R,a[n].status=1),u.data}catch(o){return t.value+=`<span class="log-item error">API调用失败: ${o.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${o.message}`,de(n)),!1}},Ue=async(n,r="wps-analysis")=>new Promise((o,h)=>{try{t.value+=`<span class="log-item info">监控目录: ${w.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${r}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=r,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const u=1e3,d=30;let b=0;const O=setInterval(()=>{if(b++,a[n]&&a[n].uploadSuccess){clearInterval(O),o(!0);return}b>=d&&(clearInterval(O),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',h(new Error("上传超时，未收到完成通知")))},u)}catch(u){t.value+=`<span class="log-item error">任务处理异常: ${u.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${u.message}`),de(n),h(u)}}),ze=async()=>{var o;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,r=n.ActiveDocument;if(i.value=r.DocID,g.value=n.ActiveWindow.Index,r!=null&&r.ContentControls)for(let h=1;h<=r.ContentControls.Count;h++){const u=r.ContentControls.Item(h);if(u&&u.Title){let d=null,b=1,O=!1;if(u.Title.startsWith("任务增强_")?(d=u.Title.substring(5),b=1,O=!0):u.Title.startsWith("任务_")?(d=u.Title.substring(3),b=1):u.Title.startsWith("已完成增强_")?(d=u.Title.substring(6),b=2,O=!0):u.Title.startsWith("已完成_")?(d=u.Title.substring(4),b=2):u.Title.startsWith("异常增强_")?(d=u.Title.substring(5),b=-1,O=!0):u.Title.startsWith("异常_")?(d=u.Title.substring(3),b=-1):u.Title.startsWith("已停止增强_")?(d=u.Title.substring(6),b=4,O=!0):u.Title.startsWith("已停止_")&&(d=u.Title.substring(4),b=4),d&&!a[d]){let R="";try{R=((o=u.Range)==null?void 0:o.Text)||""}catch{}let q=Date.now()-24*60*60*1e3;try{if(d.length===24){const ne=d.substring(0,8),le=parseInt(ne,16);!isNaN(le)&&le>0&&le<2147483647&&(q=le*1e3)}else{const ne=Date.now()-864e5;b===2?q=ne-60*60*1e3:b===-1?q=ne-30*60*1e3:b===4?q=ne-45*60*1e3:q=ne}}catch{}a[d]={status:b,startTime:q,contentControlId:u.ID,isEnhanced:O,selectedText:R};const z=b===1?"进行中":b===2?"已完成":b===-1?"异常":b===4?"已停止":"未知",ee=O?"增强":"普通";t.value+=`<span class="log-item info">发现已有${ee}任务: ${d.substring(0,8)}, 状态: ${z}</span><br/>`}}}},de=(n,r=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}r&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const o=B();if(!o){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let h=!1;const u=a[n].isEnhanced;if(o.ContentControls&&o.ContentControls.Count>0)for(let d=o.ContentControls.Count;d>=1;d--)try{const b=o.ContentControls.Item(d);b&&b.Title&&b.Title.includes(n)&&(b.LockContents=!1,b.Delete(!1),h=!0,console.log(b.Title),t.value+=`<span class="log-item success">已解锁并删除${u?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(b){t.value+=`<span class="log-item warning">访问第${d}个控件时出错: ${b.message}</span><br/>`;continue}h?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${u?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(o){t.value+=`<span class="log-item error">删除内容控件失败: ${o.message}</span><br/>`}},Re=n=>{var r;try{const o=window.wps,h=B();if(!h||!h.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const u=(r=a[n])==null?void 0:r.isEnhanced;let d=null;for(let O=1;O<=h.ContentControls.Count;O++)try{const R=h.ContentControls.Item(O);if(R&&R.Title&&R.Title.includes(n)){d=R;break}}catch{continue}if(!d){const O=a[n];O&&(O.status===2||O.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}d.Range.Select();const b=o.Windows.Item(g.value);b&&b.Selection&&b.Selection.Range&&b.ScrollIntoView(b.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${u?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(o){t.value+=`<span class="log-item error">跳转到任务控件失败: ${o.message}</span><br/>`}},Le=async n=>{var b,O,R;const r=B(),o=a[n];if(!o){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(o.terminated||o.status!==1)return;if(o.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const h=B();let u=null;if(h&&h.ContentControls)for(let q=1;q<=h.ContentControls.Count;q++){const z=h.ContentControls.Item(q);if(z&&z.Title&&z.Title.includes(n)){u=z;break}}if(!u){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,o.errorMessage&&o.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${o.errorMessage}</span><br/>`;return}return}if(o.errorMessage&&o.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${o.errorMessage}</span><br/>`,de(n);return}if(!o.resultFile)return;a[n].documentInserted=!0;const d=u.Range;u.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${o.resultFile}...</span><br/>`;const q=r.Range(d.End,d.End);q.InsertParagraphAfter(),d.Text.includes("\v")&&q.InsertAfter("\v");let z=q.End;const ee=(b=d.Tables)==null?void 0:b.Count;if(ee){const fe=d.Tables.Item(ee);((O=fe==null?void 0:fe.Range)==null?void 0:O.End)>z&&(fe.Range.InsertParagraphAfter(),z=(R=fe==null?void 0:fe.Range)==null?void 0:R.End)}r.Range(z,z).InsertFile(o.resultFile);for(let fe=1;fe<=h.ContentControls.Count;fe++){const pe=h.ContentControls.Item(fe);if(pe&&pe.Title&&(pe.Title===`任务_${n}`||pe.Title===`任务增强_${n}`)){u=pe;break}}const le=r.Range(u.Range.End-1,u.Range.End);le.Text.includes("\r")&&le.Delete(),a[n].status=2,H[n]&&H[n].urlId&&Y.sendRequest("urlMonitor","updateTaskStatus",{urlId:H[n].urlId,status:"completed",taskId:n}).then(fe=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(fe=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${fe.message}</span><br/>`});const wt=J(o.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${wt}</span><br/>`;const lt=u.Title===`任务增强_${n}`||o.isEnhanced;u&&(u.Title=lt?`已完成增强_${n}`:`已完成_${n}`,t.value+=`<span class="log-item info">已将${lt?"增强":"普通"}任务${n.substring(0,8)}控件标记为已完成</span><br/>`)}catch(q){a[n].documentInserted=!1,a[n].status=-1;const z=u.Title===`任务增强_${n}`||o.isEnhanced;u&&(u.Title=z?`异常增强_${n}`:`异常_${n}`,t.value+=`<span class="log-item info">已将${z?"增强":"普通"}任务${n.substring(0,8)}控件标记为异常</span><br/>`),t.value+=`<span class="log-item error">插入文档失败: ${q.message}</span><br/>`}},Fe=async()=>{const n=(h=1e3)=>new Promise(u=>{setTimeout(()=>u(),h)}),r=new Map,o=Object.keys(a).filter(h=>a[h].status===1&&!a[h].terminated);for(o.length?(o.forEach(h=>{r.has(h)||r.set(h,Date.now())}),await Promise.all(o.map(h=>Le(h)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const h=Object.keys(a).filter(u=>a[u].status===1&&!a[u].terminated);h.forEach(u=>{r.has(u)||r.set(u,Date.now());const d=r.get(u);(Date.now()-d)/1e3/60>=5e4&&a[u]&&!a[u].terminated&&(a[u].terminated=!0,a[u].status=-1,t.value+=`<span class="log-item warning">任务 ${u} 执行超过5分钟，已自动终止</span><br/>`,r.delete(u))}),h.length&&await Promise.all(h.map(u=>Le(u)))}},qe=async(n="wps-analysis")=>{const r=Be();if(r){const{primary:h,all:u}=r,{taskId:d,control:b,task:O,isEnhanced:R,overlapType:q}=h,z=u.filter(ee=>ee.task&&ee.task.status===1);if(z.length>0){const ee=z.map(ne=>ne.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${ee})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${u.length}个已有任务重叠，重叠类型: ${q}</span><br/>`,et();try{for(const ee of u){const{taskId:ne,control:le,task:he,isEnhanced:wt}=ee;t.value+=`<span class="log-item info">删除重叠的${wt?"增强":"普通"}任务 ${ne.substring(0,8)}，状态为 ${M((he==null?void 0:he.status)||0)}</span><br/>`,he&&(he.status=3,he.terminated=!0,he.errorMessage="用户重新创建任务时删除");try{le.LockContents=!1,le.Delete(!1),a[ne]&&(a[ne].placeholderRemoved=!0)}catch(lt){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${lt.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${u.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(ee){return Ze(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ee.message}</span><br/>`,Promise.reject(ee)}Ze()}const o=un().replace(/-/g,"").substring(0,8);return new Promise(async(h,u)=>{try{const d=window.wps,b=B(),O=b.ActiveWindow.Selection,R=O.Range,q=O.Text||"";if(s.value=q,C(),R){const z=R.Paragraphs,ee=z.Item(1),ne=z.Item(z.Count),le=ee.Range.Start,he=ne.Range.End,wt=R.Start,lt=R.End,fe=b.Range(Math.min(le,wt),Math.max(he,lt));try{let pe=b.ContentControls.Add(d.Enum.wdContentControlRichText,fe);if(!pe){if(console.log("创建内容控件失败"),pe=b.ContentControls.Add(d.Enum.wdContentControlRichText),!pe){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',u(new Error("创建内容控件失败"));return}R.Cut(),pe.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const jt=n==="wps-enhance_analysis";pe.Title=jt?`任务增强_${o}`:`任务_${o}`,pe.LockContents=!0,a[o]||(a[o]={}),a[o].contentControlId=pe.ID,a[o].isEnhanced=jt,t.value+=`<span class="log-item info">已创建${jt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const Ys=pe.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${Ys.length}</span><br/>`}catch(pe){t.value+=`<span class="log-item error">创建内容控件失败: ${pe.message}</span><br/>`,u(pe);return}}await F(o),a[o]={status:1,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:q},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${o}，类型: ${n}</span><br/>`;try{await Ue(o,n)?h():(a[o]&&a[o].status===1&&(a[o].status=-1,a[o].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${o.substring(0,8)}失败</span><br/>`,tt(o)),u(new Error("API调用失败或超时")))}catch(z){a[o]&&(a[o].status=-1,a[o].errorMessage=`执行错误: ${z.message}`,t.value+=`<span class="log-item error">任务${o.substring(0,8)}执行出错: ${z.message}</span><br/>`,tt(o)),u(z)}}catch(d){u(d)}})},Ke=async()=>{},Xe=()=>je()>0?"status-error":ge()>0?"status-running":xe()>0?"status-completed":"",ge=()=>Object.values(a).filter(n=>n.status===1).length,xe=()=>Object.values(a).filter(n=>n.status===2).length,je=()=>Object.values(a).filter(n=>n.status===-1).length,Me=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,r=B();if(!r){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let o=0;if(Object.keys(a).forEach(h=>{try{a[h].status===1&&(a[h].status=3,a[h].terminated=!0,a[h].errorMessage="强制清除"),de(h),o++}catch(u){t.value+=`<span class="log-item warning">清除任务${h.substring(0,8)}失败: ${u.message}</span><br/>`}}),r.ContentControls&&r.ContentControls.Count>0)for(let h=r.ContentControls.Count;h>=1;h--)try{const u=r.ContentControls.Item(h);if(u&&u.Title&&(u.Title.startsWith("任务_")||u.Title.startsWith("任务增强_")||u.Title.startsWith("已完成_")||u.Title.startsWith("已完成增强_")||u.Title.startsWith("异常_")||u.Title.startsWith("异常增强_")||u.Title.startsWith("已停止_")||u.Title.startsWith("已停止增强_")))try{u.LockContents=!1,u.Delete(!1);let d;u.Title.startsWith("任务增强_")?d=u.Title.substring(5):u.Title.startsWith("任务_")?d=u.Title.substring(3):u.Title.startsWith("已完成增强_")?d=u.Title.substring(6):u.Title.startsWith("已完成_")?d=u.Title.substring(4):u.Title.startsWith("异常增强_")?d=u.Title.substring(5):u.Title.startsWith("异常_")?d=u.Title.substring(3):u.Title.startsWith("已停止增强_")?d=u.Title.substring(6):u.Title.startsWith("已停止_")&&(d=u.Title.substring(4)),a[d]?(a[d].placeholderRemoved=!0,a[d].status=3):a[d]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},o++,t.value+=`<span class="log-item success">已删除任务${d.substring(0,8)}的内容控件</span><br/>`}catch(d){t.value+=`<span class="log-item error">删除控件失败: ${d.message}</span><br/>`}}catch(u){t.value+=`<span class="log-item warning">访问控件时出错: ${u.message}</span><br/>`}o>0?t.value+=`<span class="log-item success">已清除${o}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},De=async()=>{try{const n=Object.values(H).map(r=>r.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const r of n)await ie(r)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Je=()=>{Es(async()=>{try{const r=window.Application.PluginStorage.getItem("user_info");if(!r)throw new Error("未找到用户信息");const o=JSON.parse(r);if(!o.orgs||!o.orgs[0])throw new Error("未找到组织信息");T.appKey=o.appKey,T.userName=o.nickname,T.userId=o.userId,T.appSecret=o.appSecret}catch(r){t.value+=`<span class="log-item error">初始化appKey失败: ${r.message}</span><br/>`}await N(),qt([P,I],async()=>{await c()},{immediate:!1}),await l();const n=Oe.onVersionChange(()=>{const r=V();U.splice(0,U.length,...r),Oe.isSeniorEdition()&&I.value==="junior"?I.value="senior":!Oe.isSeniorEdition()&&I.value==="senior"&&(I.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Oe.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',k(),await ze(),we(),Ge(),Fe(),window.addEventListener("beforeunload",De),()=>{E&&clearTimeout(E),n&&n()}}),qt(t,n=>{E&&clearTimeout(E),E=setTimeout(()=>{ve(n)},10)},{immediate:!1})},Ge=()=>{Y.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==P.value&&(P.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${P.value}</span><br/>`),n.data.stage!==I.value&&(I.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${I.value}</span><br/>`))})},We=se(!1),Be=()=>{try{const n=B(),r=n.ActiveWindow.Selection;if(!r||!n||!n.ContentControls)return null;const o=r.Range,h=[];for(let u=1;u<=n.ContentControls.Count;u++)try{const d=n.ContentControls.Item(u);if(!d)continue;const b=(d.Title||"").trim(),O=d.Range;if(o.Start<O.End&&o.End>O.Start){let R=null,q=!1,z=!1;if(!b)z=!0,R=`empty_${d.ID||Date.now()}`;else if(b.startsWith("任务_")||b.startsWith("任务增强_")||b.startsWith("已完成_")||b.startsWith("已完成增强_")||b.startsWith("异常_")||b.startsWith("异常增强_")||b.startsWith("已停止_")||b.startsWith("已停止增强_"))b.startsWith("任务增强_")?(R=b.substring(5),q=!0):b.startsWith("任务_")?R=b.substring(3):b.startsWith("已完成增强_")?(R=b.substring(6),q=!0):b.startsWith("已完成_")?R=b.substring(4):b.startsWith("异常增强_")?(R=b.substring(5),q=!0):b.startsWith("异常_")?R=b.substring(3):b.startsWith("已停止增强_")?(R=b.substring(6),q=!0):b.startsWith("已停止_")&&(R=b.substring(4));else continue;if(R){let ee;o.Start>=O.Start&&o.End<=O.End?ee="completely_within":o.Start<=O.Start&&o.End>=O.End?ee="completely_contains":ee="partial_overlap",h.push({taskId:R,control:d,task:z?null:a[R]||null,isEnhanced:q,isEmptyTitle:z,overlapType:ee,controlRange:{start:O.Start,end:O.End},selectionRange:{start:o.Start,end:o.End}})}}}catch{continue}return h.length===0?null:{primary:h[0],all:h}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},et=()=>{We.value=!0},Ze=()=>{We.value=!1},tt=async(n,r=!1)=>{et();try{await de(n,r)}finally{Ze()}},it=n=>new Promise((r,o)=>{$.show=!0,$.message=n,$.resolveCallback=r,$.rejectCallback=o}),bt=n=>{$.show=!1,n&&$.resolveCallback?$.resolveCallback(!0):$.resolveCallback&&$.resolveCallback(!1),$.resolveCallback=null,$.rejectCallback=null},l=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await Y.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(P.value=n.data.subject),n.data.stage&&(I.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${P.value})和年级(${I.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},c=async()=>{try{if(P.value||I.value){t.value+=`<span class="log-item info">正在保存学科(${P.value})和年级(${I.value})设置到服务器...</span><br/>`;const n=await Y.setSubjectAndStage(P.value,I.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}};return{docName:e,selected:s,logger:t,map:a,watchedDir:w,subject:P,stage:I,subjectOptions:D,stageOptions:U,fetchWatchedDir:N,clearLog:L,getCurrentDocument:B,checkDocumentFormat:f,getTaskStatusClass:X,getTaskStatusText:M,getElapsedTime:J,terminateTask:p,stopTaskWithoutRemovingControl:G,run1:qe,run2:Ke,getHeaderStatusClass:Xe,getRunningTasksCount:ge,getCompletedTasksCount:xe,getErrorTasksCount:je,setupLifecycle:Je,navigateToTaskControl:Re,forceCleanAllTasks:Me,ws:ae,wsMessages:oe,initWebSocket:we,handleWatcherEvent:Ce,urlMonitorTasks:H,monitorUrlForTask:ue,stopUrlMonitoring:ie,getUrlMonitorStatus:Ie,forceUrlCheck:Q,cleanupUrlMonitoringTasks:De,tryRemoveTaskPlaceholder:de,isLoading:We,isSelectionInTaskControl:Be,tryRemoveTaskPlaceholderWithLoading:tt,showConfirm:it,handleConfirm:bt,confirmDialog:$,loadSubjectAndStage:l,saveSubjectAndStage:c}}const fn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Oe.getVersionConfig(),selectedEdition:Oe.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),i=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${i}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Y.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Y[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Y.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Y.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Y.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Y.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Y.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Y.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Y.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Oe.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await Xs()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Y.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Y.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Y.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Y.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Y.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Y.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Y.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Oe.onVersionChange(e=>{this.versionConfig=Oe.getVersionConfig(),this.selectedEdition=Oe.getEdition()}),await Y.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},hn={class:"file-watcher"},vn={class:"settings-modal"},gn={class:"modal-content"},mn={class:"modal-header"},bn={class:"version-tag"},wn={key:0,class:"user-info"},yn={class:"header-actions"},xn={class:"modal-body"},Tn={class:"status-section"},Cn={class:"status-item"},kn={key:0,class:"status-item"},Sn={class:"directory-section"},En={class:"directory-form"},$n={class:"radio-group"},An={class:"radio-item"},Mn={class:"radio-item"},Dn={class:"radio-item"},_n={key:0,class:"radio-item"},On={key:1,class:"directory-section"},Pn={class:"directory-form"},In={class:"form-group"},Un=["placeholder"],Rn=["disabled"],Ln={key:2,class:"directory-section"},Fn={class:"directory-form"},jn={class:"form-group"},Wn=["placeholder"],Bn=["disabled"],Vn={key:3,class:"directory-section"},Nn={class:"directory-form"},Hn={class:"form-group"},zn=["placeholder"],qn=["disabled"],Yn={key:4,class:"events-section"},Kn={class:"events-list"},Xn={class:"event-time"},Jn={class:"event-message"},Gn={key:1,class:"modal-footer"};function Zn(e,s,t,a,i,g){var x,E,w,T,$,P,I,D,V,U,N,K,L,B,f,X,M,J,G,p,y,k,C;return j(),W("div",hn,[v("div",vn,[v("div",gn,[v("div",mn,[v("h3",null,[Js(te(i.versionConfig.shortName)+"设置 ",1),v("span",bn,te(i.versionConfig.appVersion),1),g.userInfo?(j(),W("span",wn,"欢迎您，"+te(g.userInfo.nickname)+" "+te((E=(x=g.userInfo.orgs)==null?void 0:x[0])==null?void 0:E.orgId),1)):Z("",!0)]),v("div",yn,[v("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>g.handleLogout&&g.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[v("span",{class:"icon-logout"},null,-1)])),v("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),v("div",xn,[v("div",Tn,[v("div",Cn,[s[22]||(s[22]=v("span",{class:"label"},"状态：",-1)),v("span",{class:ke(["status-badge",i.status.status])},te(i.status.status==="running"?"运行中":"已停止"),3)]),(($=(T=(w=g.userInfo)==null?void 0:w.orgs)==null?void 0:T[0])==null?void 0:$.orgId)===2?(j(),W("div",kn,[s[23]||(s[23]=v("span",{class:"label"},"本次上传：",-1)),v("span",null,te(i.status.processedFiles||0)+" 个文件",1)])):Z("",!0),v("div",Sn,[s[28]||(s[28]=v("h4",null,"保存方式设置",-1)),v("div",En,[v("div",$n,[v("label",An,[_e(v("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>i.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[_t,i.currentSaveMethod]]),s[24]||(s[24]=v("span",{class:"radio-label"},"方式一",-1))]),v("label",Mn,[_e(v("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>i.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[_t,i.currentSaveMethod]]),s[25]||(s[25]=v("span",{class:"radio-label"},"方式二 (默认)",-1))]),v("label",Dn,[_e(v("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>i.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[_t,i.currentSaveMethod]]),s[26]||(s[26]=v("span",{class:"radio-label"},"方式三",-1))]),((D=(I=(P=g.userInfo)==null?void 0:P.orgs)==null?void 0:I[0])==null?void 0:D.orgId)===2?(j(),W("label",_n,[_e(v("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>i.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[_t,i.currentSaveMethod]]),s[27]||(s[27]=v("span",{class:"radio-label"},"方式四",-1))])):Z("",!0)]),i.saveMethodUpdateMessage?(j(),W("div",{key:0,class:ke(["update-message",i.saveMethodUpdateSuccess?"success":"error"])},te(i.saveMethodUpdateMessage),3)):Z("",!0)])])]),Z("",!0),((N=(U=(V=g.userInfo)==null?void 0:V.orgs)==null?void 0:U[0])==null?void 0:N.orgId)===2?(j(),W("div",On,[s[32]||(s[32]=v("h4",null,"上传目录设置",-1)),v("div",Pn,[v("div",In,[s[31]||(s[31]=v("span",{class:"label"},"路径：",-1)),_e(v("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>i.newWatchDir=m),placeholder:i.status.watchDir||"C:\\Temp"},null,8,Un),[[Wt,i.newWatchDir]]),v("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>g.updateWatchDir&&g.updateWatchDir(...m)),disabled:i.isUpdating||!i.newWatchDir},te(i.isUpdating?"更新中...":"更新目录"),9,Rn)]),i.updateMessage?(j(),W("div",{key:0,class:ke(["update-message",i.updateSuccess?"success":"error"])},te(i.updateMessage),3)):Z("",!0)])])):Z("",!0),((B=(L=(K=g.userInfo)==null?void 0:K.orgs)==null?void 0:L[0])==null?void 0:B.orgId)===2?(j(),W("div",Ln,[s[34]||(s[34]=v("h4",null,"下载目录设置",-1)),v("div",Fn,[v("div",jn,[s[33]||(s[33]=v("span",{class:"label"},"路径：",-1)),_e(v("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>i.newDownloadPath=m),placeholder:i.downloadPath||"C:\\Temp\\Downloads"},null,8,Wn),[[Wt,i.newDownloadPath]]),v("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>g.updateDownloadPath&&g.updateDownloadPath(...m)),disabled:i.isUpdatingDownloadPath||!i.newDownloadPath},te(i.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Bn)]),i.downloadPathUpdateMessage?(j(),W("div",{key:0,class:ke(["update-message",i.downloadPathUpdateSuccess?"success":"error"])},te(i.downloadPathUpdateMessage),3)):Z("",!0)])])):Z("",!0),((M=(X=(f=g.userInfo)==null?void 0:f.orgs)==null?void 0:X[0])==null?void 0:M.orgId)===2?(j(),W("div",Vn,[s[36]||(s[36]=v("h4",null,"配置目录设置",-1)),v("div",Nn,[v("div",Hn,[s[35]||(s[35]=v("span",{class:"label"},"路径：",-1)),_e(v("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>i.newAddonConfigPath=m),placeholder:i.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,zn),[[Wt,i.newAddonConfigPath]]),v("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...m)),disabled:i.isUpdatingAddonConfigPath||!i.newAddonConfigPath},te(i.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,qn)]),i.addonConfigPathUpdateMessage?(j(),W("div",{key:0,class:ke(["update-message",i.addonConfigPathUpdateSuccess?"success":"error"])},te(i.addonConfigPathUpdateMessage),3)):Z("",!0)])])):Z("",!0),((p=(G=(J=g.userInfo)==null?void 0:J.orgs)==null?void 0:G[0])==null?void 0:p.orgId)===2?(j(),W("div",Yn,[s[37]||(s[37]=v("h4",null,"最近事件",-1)),v("div",Kn,[(j(!0),W(xt,null,Tt(i.recentEvents,(m,A)=>(j(),W("div",{key:A,class:"event-item"},[v("span",Xn,te(g.formatTime(m.timestamp)),1),v("span",{class:ke(["event-type",m.eventType])},te(g.getEventTypeText(m.eventType)),3),v("span",Jn,te(g.getEventMessage(m)),1)]))),128))])])):Z("",!0)]),Z("",!0),((C=(k=(y=g.userInfo)==null?void 0:y.orgs)==null?void 0:k[0])==null?void 0:C.orgId)===2?(j(),W("div",Gn,[v("button",{class:ke(["control-btn",i.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>g.controlService&&g.controlService(...m))},te(i.status.status==="running"?"停止服务":"启动服务"),3)])):Z("",!0)])])])}const Qn=$s(fn,[["render",Zn],["__scopeId","data-v-41184c23"]]);var me="top",Ee="bottom",$e="right",be="left",Gt="auto",Mt=[me,Ee,$e,be],ht="start",$t="end",ea="clippingParents",Ms="viewport",yt="popper",ta="reference",ls=Mt.reduce(function(e,s){return e.concat([s+"-"+ht,s+"-"+$t])},[]),Ds=[].concat(Mt,[Gt]).reduce(function(e,s){return e.concat([s,s+"-"+ht,s+"-"+$t])},[]),sa="beforeRead",na="read",aa="afterRead",ra="beforeMain",oa="main",ia="afterMain",la="beforeWrite",ca="write",ua="afterWrite",da=[sa,na,aa,ra,oa,ia,la,ca,ua];function He(e){return e?(e.nodeName||"").toLowerCase():null}function Te(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function ot(e){var s=Te(e).Element;return e instanceof s||e instanceof Element}function Se(e){var s=Te(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function Zt(e){if(typeof ShadowRoot>"u")return!1;var s=Te(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function pa(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},i=s.attributes[t]||{},g=s.elements[t];!Se(g)||!He(g)||(Object.assign(g.style,a),Object.keys(i).forEach(function(x){var E=i[x];E===!1?g.removeAttribute(x):g.setAttribute(x,E===!0?"":E)}))})}function fa(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var i=s.elements[a],g=s.attributes[a]||{},x=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),E=x.reduce(function(w,T){return w[T]="",w},{});!Se(i)||!He(i)||(Object.assign(i.style,E),Object.keys(g).forEach(function(w){i.removeAttribute(w)}))})}}const _s={name:"applyStyles",enabled:!0,phase:"write",fn:pa,effect:fa,requires:["computeStyles"]};function Ne(e){return e.split("-")[0]}var at=Math.max,Ut=Math.min,vt=Math.round;function Yt(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Os(){return!/^((?!chrome|android).)*safari/i.test(Yt())}function gt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),i=1,g=1;s&&Se(e)&&(i=e.offsetWidth>0&&vt(a.width)/e.offsetWidth||1,g=e.offsetHeight>0&&vt(a.height)/e.offsetHeight||1);var x=ot(e)?Te(e):window,E=x.visualViewport,w=!Os()&&t,T=(a.left+(w&&E?E.offsetLeft:0))/i,$=(a.top+(w&&E?E.offsetTop:0))/g,P=a.width/i,I=a.height/g;return{width:P,height:I,top:$,right:T+P,bottom:$+I,left:T,x:T,y:$}}function Qt(e){var s=gt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function Ps(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&Zt(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function Ye(e){return Te(e).getComputedStyle(e)}function ha(e){return["table","td","th"].indexOf(He(e))>=0}function Qe(e){return((ot(e)?e.ownerDocument:e.document)||window.document).documentElement}function Lt(e){return He(e)==="html"?e:e.assignedSlot||e.parentNode||(Zt(e)?e.host:null)||Qe(e)}function cs(e){return!Se(e)||Ye(e).position==="fixed"?null:e.offsetParent}function va(e){var s=/firefox/i.test(Yt()),t=/Trident/i.test(Yt());if(t&&Se(e)){var a=Ye(e);if(a.position==="fixed")return null}var i=Lt(e);for(Zt(i)&&(i=i.host);Se(i)&&["html","body"].indexOf(He(i))<0;){var g=Ye(i);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return i;i=i.parentNode}return null}function Dt(e){for(var s=Te(e),t=cs(e);t&&ha(t)&&Ye(t).position==="static";)t=cs(t);return t&&(He(t)==="html"||He(t)==="body"&&Ye(t).position==="static")?s:t||va(e)||s}function es(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function kt(e,s,t){return at(e,Ut(s,t))}function ga(e,s,t){var a=kt(e,s,t);return a>t?t:a}function Is(){return{top:0,right:0,bottom:0,left:0}}function Us(e){return Object.assign({},Is(),e)}function Rs(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var ma=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Us(typeof s!="number"?s:Rs(s,Mt))};function ba(e){var s,t=e.state,a=e.name,i=e.options,g=t.elements.arrow,x=t.modifiersData.popperOffsets,E=Ne(t.placement),w=es(E),T=[be,$e].indexOf(E)>=0,$=T?"height":"width";if(!(!g||!x)){var P=ma(i.padding,t),I=Qt(g),D=w==="y"?me:be,V=w==="y"?Ee:$e,U=t.rects.reference[$]+t.rects.reference[w]-x[w]-t.rects.popper[$],N=x[w]-t.rects.reference[w],K=Dt(g),L=K?w==="y"?K.clientHeight||0:K.clientWidth||0:0,B=U/2-N/2,f=P[D],X=L-I[$]-P[V],M=L/2-I[$]/2+B,J=kt(f,M,X),G=w;t.modifiersData[a]=(s={},s[G]=J,s.centerOffset=J-M,s)}}function wa(e){var s=e.state,t=e.options,a=t.element,i=a===void 0?"[data-popper-arrow]":a;i!=null&&(typeof i=="string"&&(i=s.elements.popper.querySelector(i),!i)||Ps(s.elements.popper,i)&&(s.elements.arrow=i))}const ya={name:"arrow",enabled:!0,phase:"main",fn:ba,effect:wa,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function mt(e){return e.split("-")[1]}var xa={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ta(e,s){var t=e.x,a=e.y,i=s.devicePixelRatio||1;return{x:vt(t*i)/i||0,y:vt(a*i)/i||0}}function us(e){var s,t=e.popper,a=e.popperRect,i=e.placement,g=e.variation,x=e.offsets,E=e.position,w=e.gpuAcceleration,T=e.adaptive,$=e.roundOffsets,P=e.isFixed,I=x.x,D=I===void 0?0:I,V=x.y,U=V===void 0?0:V,N=typeof $=="function"?$({x:D,y:U}):{x:D,y:U};D=N.x,U=N.y;var K=x.hasOwnProperty("x"),L=x.hasOwnProperty("y"),B=be,f=me,X=window;if(T){var M=Dt(t),J="clientHeight",G="clientWidth";if(M===Te(t)&&(M=Qe(t),Ye(M).position!=="static"&&E==="absolute"&&(J="scrollHeight",G="scrollWidth")),M=M,i===me||(i===be||i===$e)&&g===$t){f=Ee;var p=P&&M===X&&X.visualViewport?X.visualViewport.height:M[J];U-=p-a.height,U*=w?1:-1}if(i===be||(i===me||i===Ee)&&g===$t){B=$e;var y=P&&M===X&&X.visualViewport?X.visualViewport.width:M[G];D-=y-a.width,D*=w?1:-1}}var k=Object.assign({position:E},T&&xa),C=$===!0?Ta({x:D,y:U},Te(t)):{x:D,y:U};if(D=C.x,U=C.y,w){var m;return Object.assign({},k,(m={},m[f]=L?"0":"",m[B]=K?"0":"",m.transform=(X.devicePixelRatio||1)<=1?"translate("+D+"px, "+U+"px)":"translate3d("+D+"px, "+U+"px, 0)",m))}return Object.assign({},k,(s={},s[f]=L?U+"px":"",s[B]=K?D+"px":"",s.transform="",s))}function Ca(e){var s=e.state,t=e.options,a=t.gpuAcceleration,i=a===void 0?!0:a,g=t.adaptive,x=g===void 0?!0:g,E=t.roundOffsets,w=E===void 0?!0:E,T={placement:Ne(s.placement),variation:mt(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:i,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,us(Object.assign({},T,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:x,roundOffsets:w})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,us(Object.assign({},T,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:w})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const ka={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ca,data:{}};var Ot={passive:!0};function Sa(e){var s=e.state,t=e.instance,a=e.options,i=a.scroll,g=i===void 0?!0:i,x=a.resize,E=x===void 0?!0:x,w=Te(s.elements.popper),T=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&T.forEach(function($){$.addEventListener("scroll",t.update,Ot)}),E&&w.addEventListener("resize",t.update,Ot),function(){g&&T.forEach(function($){$.removeEventListener("scroll",t.update,Ot)}),E&&w.removeEventListener("resize",t.update,Ot)}}const Ea={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Sa,data:{}};var $a={left:"right",right:"left",bottom:"top",top:"bottom"};function It(e){return e.replace(/left|right|bottom|top/g,function(s){return $a[s]})}var Aa={start:"end",end:"start"};function ds(e){return e.replace(/start|end/g,function(s){return Aa[s]})}function ts(e){var s=Te(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function ss(e){return gt(Qe(e)).left+ts(e).scrollLeft}function Ma(e,s){var t=Te(e),a=Qe(e),i=t.visualViewport,g=a.clientWidth,x=a.clientHeight,E=0,w=0;if(i){g=i.width,x=i.height;var T=Os();(T||!T&&s==="fixed")&&(E=i.offsetLeft,w=i.offsetTop)}return{width:g,height:x,x:E+ss(e),y:w}}function Da(e){var s,t=Qe(e),a=ts(e),i=(s=e.ownerDocument)==null?void 0:s.body,g=at(t.scrollWidth,t.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),x=at(t.scrollHeight,t.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),E=-a.scrollLeft+ss(e),w=-a.scrollTop;return Ye(i||t).direction==="rtl"&&(E+=at(t.clientWidth,i?i.clientWidth:0)-g),{width:g,height:x,x:E,y:w}}function ns(e){var s=Ye(e),t=s.overflow,a=s.overflowX,i=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+i+a)}function Ls(e){return["html","body","#document"].indexOf(He(e))>=0?e.ownerDocument.body:Se(e)&&ns(e)?e:Ls(Lt(e))}function St(e,s){var t;s===void 0&&(s=[]);var a=Ls(e),i=a===((t=e.ownerDocument)==null?void 0:t.body),g=Te(a),x=i?[g].concat(g.visualViewport||[],ns(a)?a:[]):a,E=s.concat(x);return i?E:E.concat(St(Lt(x)))}function Kt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function _a(e,s){var t=gt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function ps(e,s,t){return s===Ms?Kt(Ma(e,t)):ot(s)?_a(s,t):Kt(Da(Qe(e)))}function Oa(e){var s=St(Lt(e)),t=["absolute","fixed"].indexOf(Ye(e).position)>=0,a=t&&Se(e)?Dt(e):e;return ot(a)?s.filter(function(i){return ot(i)&&Ps(i,a)&&He(i)!=="body"}):[]}function Pa(e,s,t,a){var i=s==="clippingParents"?Oa(e):[].concat(s),g=[].concat(i,[t]),x=g[0],E=g.reduce(function(w,T){var $=ps(e,T,a);return w.top=at($.top,w.top),w.right=Ut($.right,w.right),w.bottom=Ut($.bottom,w.bottom),w.left=at($.left,w.left),w},ps(e,x,a));return E.width=E.right-E.left,E.height=E.bottom-E.top,E.x=E.left,E.y=E.top,E}function Fs(e){var s=e.reference,t=e.element,a=e.placement,i=a?Ne(a):null,g=a?mt(a):null,x=s.x+s.width/2-t.width/2,E=s.y+s.height/2-t.height/2,w;switch(i){case me:w={x,y:s.y-t.height};break;case Ee:w={x,y:s.y+s.height};break;case $e:w={x:s.x+s.width,y:E};break;case be:w={x:s.x-t.width,y:E};break;default:w={x:s.x,y:s.y}}var T=i?es(i):null;if(T!=null){var $=T==="y"?"height":"width";switch(g){case ht:w[T]=w[T]-(s[$]/2-t[$]/2);break;case $t:w[T]=w[T]+(s[$]/2-t[$]/2);break}}return w}function At(e,s){s===void 0&&(s={});var t=s,a=t.placement,i=a===void 0?e.placement:a,g=t.strategy,x=g===void 0?e.strategy:g,E=t.boundary,w=E===void 0?ea:E,T=t.rootBoundary,$=T===void 0?Ms:T,P=t.elementContext,I=P===void 0?yt:P,D=t.altBoundary,V=D===void 0?!1:D,U=t.padding,N=U===void 0?0:U,K=Us(typeof N!="number"?N:Rs(N,Mt)),L=I===yt?ta:yt,B=e.rects.popper,f=e.elements[V?L:I],X=Pa(ot(f)?f:f.contextElement||Qe(e.elements.popper),w,$,x),M=gt(e.elements.reference),J=Fs({reference:M,element:B,strategy:"absolute",placement:i}),G=Kt(Object.assign({},B,J)),p=I===yt?G:M,y={top:X.top-p.top+K.top,bottom:p.bottom-X.bottom+K.bottom,left:X.left-p.left+K.left,right:p.right-X.right+K.right},k=e.modifiersData.offset;if(I===yt&&k){var C=k[i];Object.keys(y).forEach(function(m){var A=[$e,Ee].indexOf(m)>=0?1:-1,_=[me,Ee].indexOf(m)>=0?"y":"x";y[m]+=C[_]*A})}return y}function Ia(e,s){s===void 0&&(s={});var t=s,a=t.placement,i=t.boundary,g=t.rootBoundary,x=t.padding,E=t.flipVariations,w=t.allowedAutoPlacements,T=w===void 0?Ds:w,$=mt(a),P=$?E?ls:ls.filter(function(V){return mt(V)===$}):Mt,I=P.filter(function(V){return T.indexOf(V)>=0});I.length===0&&(I=P);var D=I.reduce(function(V,U){return V[U]=At(e,{placement:U,boundary:i,rootBoundary:g,padding:x})[Ne(U)],V},{});return Object.keys(D).sort(function(V,U){return D[V]-D[U]})}function Ua(e){if(Ne(e)===Gt)return[];var s=It(e);return[ds(e),s,ds(s)]}function Ra(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var i=t.mainAxis,g=i===void 0?!0:i,x=t.altAxis,E=x===void 0?!0:x,w=t.fallbackPlacements,T=t.padding,$=t.boundary,P=t.rootBoundary,I=t.altBoundary,D=t.flipVariations,V=D===void 0?!0:D,U=t.allowedAutoPlacements,N=s.options.placement,K=Ne(N),L=K===N,B=w||(L||!V?[It(N)]:Ua(N)),f=[N].concat(B).reduce(function(H,ue){return H.concat(Ne(ue)===Gt?Ia(s,{placement:ue,boundary:$,rootBoundary:P,padding:T,flipVariations:V,allowedAutoPlacements:U}):ue)},[]),X=s.rects.reference,M=s.rects.popper,J=new Map,G=!0,p=f[0],y=0;y<f.length;y++){var k=f[y],C=Ne(k),m=mt(k)===ht,A=[me,Ee].indexOf(C)>=0,_=A?"width":"height",S=At(s,{placement:k,boundary:$,rootBoundary:P,altBoundary:I,padding:T}),F=A?m?$e:be:m?Ee:me;X[_]>M[_]&&(F=It(F));var ae=It(F),oe=[];if(g&&oe.push(S[C]<=0),E&&oe.push(S[F]<=0,S[ae]<=0),oe.every(function(H){return H})){p=k,G=!1;break}J.set(k,oe)}if(G)for(var ce=V?3:1,Ae=function(ue){var ie=f.find(function(Ie){var Q=J.get(Ie);if(Q)return Q.slice(0,ue).every(function(Ce){return Ce})});if(ie)return p=ie,"break"},ve=ce;ve>0;ve--){var we=Ae(ve);if(we==="break")break}s.placement!==p&&(s.modifiersData[a]._skip=!0,s.placement=p,s.reset=!0)}}const La={name:"flip",enabled:!0,phase:"main",fn:Ra,requiresIfExists:["offset"],data:{_skip:!1}};function fs(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function hs(e){return[me,$e,Ee,be].some(function(s){return e[s]>=0})}function Fa(e){var s=e.state,t=e.name,a=s.rects.reference,i=s.rects.popper,g=s.modifiersData.preventOverflow,x=At(s,{elementContext:"reference"}),E=At(s,{altBoundary:!0}),w=fs(x,a),T=fs(E,i,g),$=hs(w),P=hs(T);s.modifiersData[t]={referenceClippingOffsets:w,popperEscapeOffsets:T,isReferenceHidden:$,hasPopperEscaped:P},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":$,"data-popper-escaped":P})}const ja={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Fa};function Wa(e,s,t){var a=Ne(e),i=[be,me].indexOf(a)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,x=g[0],E=g[1];return x=x||0,E=(E||0)*i,[be,$e].indexOf(a)>=0?{x:E,y:x}:{x,y:E}}function Ba(e){var s=e.state,t=e.options,a=e.name,i=t.offset,g=i===void 0?[0,0]:i,x=Ds.reduce(function($,P){return $[P]=Wa(P,s.rects,g),$},{}),E=x[s.placement],w=E.x,T=E.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=w,s.modifiersData.popperOffsets.y+=T),s.modifiersData[a]=x}const Va={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Ba};function Na(e){var s=e.state,t=e.name;s.modifiersData[t]=Fs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const Ha={name:"popperOffsets",enabled:!0,phase:"read",fn:Na,data:{}};function za(e){return e==="x"?"y":"x"}function qa(e){var s=e.state,t=e.options,a=e.name,i=t.mainAxis,g=i===void 0?!0:i,x=t.altAxis,E=x===void 0?!1:x,w=t.boundary,T=t.rootBoundary,$=t.altBoundary,P=t.padding,I=t.tether,D=I===void 0?!0:I,V=t.tetherOffset,U=V===void 0?0:V,N=At(s,{boundary:w,rootBoundary:T,padding:P,altBoundary:$}),K=Ne(s.placement),L=mt(s.placement),B=!L,f=es(K),X=za(f),M=s.modifiersData.popperOffsets,J=s.rects.reference,G=s.rects.popper,p=typeof U=="function"?U(Object.assign({},s.rects,{placement:s.placement})):U,y=typeof p=="number"?{mainAxis:p,altAxis:p}:Object.assign({mainAxis:0,altAxis:0},p),k=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,C={x:0,y:0};if(M){if(g){var m,A=f==="y"?me:be,_=f==="y"?Ee:$e,S=f==="y"?"height":"width",F=M[f],ae=F+N[A],oe=F-N[_],ce=D?-G[S]/2:0,Ae=L===ht?J[S]:G[S],ve=L===ht?-G[S]:-J[S],we=s.elements.arrow,H=D&&we?Qt(we):{width:0,height:0},ue=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Is(),ie=ue[A],Ie=ue[_],Q=kt(0,J[S],H[S]),Ce=B?J[S]/2-ce-Q-ie-y.mainAxis:Ae-Q-ie-y.mainAxis,ye=B?-J[S]/2+ce+Q+Ie+y.mainAxis:ve+Q+Ie+y.mainAxis,Ue=s.elements.arrow&&Dt(s.elements.arrow),ze=Ue?f==="y"?Ue.clientTop||0:Ue.clientLeft||0:0,de=(m=k==null?void 0:k[f])!=null?m:0,Re=F+Ce-de-ze,Le=F+ye-de,Fe=kt(D?Ut(ae,Re):ae,F,D?at(oe,Le):oe);M[f]=Fe,C[f]=Fe-F}if(E){var qe,Ke=f==="x"?me:be,Xe=f==="x"?Ee:$e,ge=M[X],xe=X==="y"?"height":"width",je=ge+N[Ke],Me=ge-N[Xe],De=[me,be].indexOf(K)!==-1,Je=(qe=k==null?void 0:k[X])!=null?qe:0,Ge=De?je:ge-J[xe]-G[xe]-Je+y.altAxis,We=De?ge+J[xe]+G[xe]-Je-y.altAxis:Me,Be=D&&De?ga(Ge,ge,We):kt(D?Ge:je,ge,D?We:Me);M[X]=Be,C[X]=Be-ge}s.modifiersData[a]=C}}const Ya={name:"preventOverflow",enabled:!0,phase:"main",fn:qa,requiresIfExists:["offset"]};function Ka(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Xa(e){return e===Te(e)||!Se(e)?ts(e):Ka(e)}function Ja(e){var s=e.getBoundingClientRect(),t=vt(s.width)/e.offsetWidth||1,a=vt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function Ga(e,s,t){t===void 0&&(t=!1);var a=Se(s),i=Se(s)&&Ja(s),g=Qe(s),x=gt(e,i,t),E={scrollLeft:0,scrollTop:0},w={x:0,y:0};return(a||!a&&!t)&&((He(s)!=="body"||ns(g))&&(E=Xa(s)),Se(s)?(w=gt(s,!0),w.x+=s.clientLeft,w.y+=s.clientTop):g&&(w.x=ss(g))),{x:x.left+E.scrollLeft-w.x,y:x.top+E.scrollTop-w.y,width:x.width,height:x.height}}function Za(e){var s=new Map,t=new Set,a=[];e.forEach(function(g){s.set(g.name,g)});function i(g){t.add(g.name);var x=[].concat(g.requires||[],g.requiresIfExists||[]);x.forEach(function(E){if(!t.has(E)){var w=s.get(E);w&&i(w)}}),a.push(g)}return e.forEach(function(g){t.has(g.name)||i(g)}),a}function Qa(e){var s=Za(e);return da.reduce(function(t,a){return t.concat(s.filter(function(i){return i.phase===a}))},[])}function er(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function tr(e){var s=e.reduce(function(t,a){var i=t[a.name];return t[a.name]=i?Object.assign({},i,a,{options:Object.assign({},i.options,a.options),data:Object.assign({},i.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var vs={placement:"bottom",modifiers:[],strategy:"absolute"};function gs(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function sr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,i=s.defaultOptions,g=i===void 0?vs:i;return function(E,w,T){T===void 0&&(T=g);var $={placement:"bottom",orderedModifiers:[],options:Object.assign({},vs,g),modifiersData:{},elements:{reference:E,popper:w},attributes:{},styles:{}},P=[],I=!1,D={state:$,setOptions:function(K){var L=typeof K=="function"?K($.options):K;U(),$.options=Object.assign({},g,$.options,L),$.scrollParents={reference:ot(E)?St(E):E.contextElement?St(E.contextElement):[],popper:St(w)};var B=Qa(tr([].concat(a,$.options.modifiers)));return $.orderedModifiers=B.filter(function(f){return f.enabled}),V(),D.update()},forceUpdate:function(){if(!I){var K=$.elements,L=K.reference,B=K.popper;if(gs(L,B)){$.rects={reference:Ga(L,Dt(B),$.options.strategy==="fixed"),popper:Qt(B)},$.reset=!1,$.placement=$.options.placement,$.orderedModifiers.forEach(function(y){return $.modifiersData[y.name]=Object.assign({},y.data)});for(var f=0;f<$.orderedModifiers.length;f++){if($.reset===!0){$.reset=!1,f=-1;continue}var X=$.orderedModifiers[f],M=X.fn,J=X.options,G=J===void 0?{}:J,p=X.name;typeof M=="function"&&($=M({state:$,options:G,name:p,instance:D})||$)}}}},update:er(function(){return new Promise(function(N){D.forceUpdate(),N($)})}),destroy:function(){U(),I=!0}};if(!gs(E,w))return D;D.setOptions(T).then(function(N){!I&&T.onFirstUpdate&&T.onFirstUpdate(N)});function V(){$.orderedModifiers.forEach(function(N){var K=N.name,L=N.options,B=L===void 0?{}:L,f=N.effect;if(typeof f=="function"){var X=f({state:$,name:K,instance:D,options:B}),M=function(){};P.push(X||M)}})}function U(){P.forEach(function(N){return N()}),P=[]}return D}}var nr=[Ea,Ha,ka,_s,Va,La,Ya,ya,ja],ar=sr({defaultModifiers:nr}),rr="tippy-box",js="tippy-content",or="tippy-backdrop",Ws="tippy-arrow",Bs="tippy-svg-arrow",nt={passive:!0,capture:!0},Vs=function(){return document.body};function Vt(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function as(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function Ns(e,s){return typeof e=="function"?e.apply(void 0,s):e}function ms(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function ir(e){return e.split(/\s+/).filter(Boolean)}function ft(e){return[].concat(e)}function bs(e,s){e.indexOf(s)===-1&&e.push(s)}function lr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function cr(e){return e.split("-")[0]}function Rt(e){return[].slice.call(e)}function ws(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Et(){return document.createElement("div")}function Ft(e){return["Element","Fragment"].some(function(s){return as(e,s)})}function ur(e){return as(e,"NodeList")}function dr(e){return as(e,"MouseEvent")}function pr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function fr(e){return Ft(e)?[e]:ur(e)?Rt(e):Array.isArray(e)?e:Rt(document.querySelectorAll(e))}function Nt(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function ys(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function hr(e){var s,t=ft(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function vr(e,s){var t=s.clientX,a=s.clientY;return e.every(function(i){var g=i.popperRect,x=i.popperState,E=i.props,w=E.interactiveBorder,T=cr(x.placement),$=x.modifiersData.offset;if(!$)return!0;var P=T==="bottom"?$.top.y:0,I=T==="top"?$.bottom.y:0,D=T==="right"?$.left.x:0,V=T==="left"?$.right.x:0,U=g.top-a+P>w,N=a-g.bottom-I>w,K=g.left-t+D>w,L=t-g.right-V>w;return U||N||K||L})}function Ht(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(i){e[a](i,t)})}function xs(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var Ve={isTouch:!1},Ts=0;function gr(){Ve.isTouch||(Ve.isTouch=!0,window.performance&&document.addEventListener("mousemove",Hs))}function Hs(){var e=performance.now();e-Ts<20&&(Ve.isTouch=!1,document.removeEventListener("mousemove",Hs)),Ts=e}function mr(){var e=document.activeElement;if(pr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function br(){document.addEventListener("touchstart",gr,nt),window.addEventListener("blur",mr)}var wr=typeof window<"u"&&typeof document<"u",yr=wr?!!window.msCrypto:!1,xr={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Tr={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Pe=Object.assign({appendTo:Vs,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},xr,Tr),Cr=Object.keys(Pe),kr=function(s){var t=Object.keys(s);t.forEach(function(a){Pe[a]=s[a]})};function zs(e){var s=e.plugins||[],t=s.reduce(function(a,i){var g=i.name,x=i.defaultValue;if(g){var E;a[g]=e[g]!==void 0?e[g]:(E=Pe[g])!=null?E:x}return a},{});return Object.assign({},e,t)}function Sr(e,s){var t=s?Object.keys(zs(Object.assign({},Pe,{plugins:s}))):Cr,a=t.reduce(function(i,g){var x=(e.getAttribute("data-tippy-"+g)||"").trim();if(!x)return i;if(g==="content")i[g]=x;else try{i[g]=JSON.parse(x)}catch{i[g]=x}return i},{});return a}function Cs(e,s){var t=Object.assign({},s,{content:Ns(s.content,[e])},s.ignoreAttributes?{}:Sr(e,s.plugins));return t.aria=Object.assign({},Pe.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Er=function(){return"innerHTML"};function Xt(e,s){e[Er()]=s}function ks(e){var s=Et();return e===!0?s.className=Ws:(s.className=Bs,Ft(e)?s.appendChild(e):Xt(s,e)),s}function Ss(e,s){Ft(s.content)?(Xt(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?Xt(e,s.content):e.textContent=s.content)}function Jt(e){var s=e.firstElementChild,t=Rt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(js)}),arrow:t.find(function(a){return a.classList.contains(Ws)||a.classList.contains(Bs)}),backdrop:t.find(function(a){return a.classList.contains(or)})}}function qs(e){var s=Et(),t=Et();t.className=rr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=Et();a.className=js,a.setAttribute("data-state","hidden"),Ss(a,e.props),s.appendChild(t),t.appendChild(a),i(e.props,e.props);function i(g,x){var E=Jt(s),w=E.box,T=E.content,$=E.arrow;x.theme?w.setAttribute("data-theme",x.theme):w.removeAttribute("data-theme"),typeof x.animation=="string"?w.setAttribute("data-animation",x.animation):w.removeAttribute("data-animation"),x.inertia?w.setAttribute("data-inertia",""):w.removeAttribute("data-inertia"),w.style.maxWidth=typeof x.maxWidth=="number"?x.maxWidth+"px":x.maxWidth,x.role?w.setAttribute("role",x.role):w.removeAttribute("role"),(g.content!==x.content||g.allowHTML!==x.allowHTML)&&Ss(T,e.props),x.arrow?$?g.arrow!==x.arrow&&(w.removeChild($),w.appendChild(ks(x.arrow))):w.appendChild(ks(x.arrow)):$&&w.removeChild($)}return{popper:s,onUpdate:i}}qs.$$tippy=!0;var $r=1,Pt=[],zt=[];function Ar(e,s){var t=Cs(e,Object.assign({},Pe,zs(ws(s)))),a,i,g,x=!1,E=!1,w=!1,T=!1,$,P,I,D=[],V=ms(Re,t.interactiveDebounce),U,N=$r++,K=null,L=lr(t.plugins),B={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},f={id:N,reference:e,popper:Et(),popperInstance:K,props:t,state:B,plugins:L,clearDelayTimeouts:Ge,setProps:We,setContent:Be,show:et,hide:Ze,hideWithInteractivity:tt,enable:De,disable:Je,unmount:it,destroy:bt};if(!t.render)return f;var X=t.render(f),M=X.popper,J=X.onUpdate;M.setAttribute("data-tippy-root",""),M.id="tippy-"+f.id,f.popper=M,e._tippy=f,M._tippy=f;var G=L.map(function(l){return l.fn(f)}),p=e.hasAttribute("aria-expanded");return Ue(),ce(),F(),ae("onCreate",[f]),t.showOnCreate&&je(),M.addEventListener("mouseenter",function(){f.props.interactive&&f.state.isVisible&&f.clearDelayTimeouts()}),M.addEventListener("mouseleave",function(){f.props.interactive&&f.props.trigger.indexOf("mouseenter")>=0&&A().addEventListener("mousemove",V)}),f;function y(){var l=f.props.touch;return Array.isArray(l)?l:[l,0]}function k(){return y()[0]==="hold"}function C(){var l;return!!((l=f.props.render)!=null&&l.$$tippy)}function m(){return U||e}function A(){var l=m().parentNode;return l?hr(l):document}function _(){return Jt(M)}function S(l){return f.state.isMounted&&!f.state.isVisible||Ve.isTouch||$&&$.type==="focus"?0:Vt(f.props.delay,l?0:1,Pe.delay)}function F(l){l===void 0&&(l=!1),M.style.pointerEvents=f.props.interactive&&!l?"":"none",M.style.zIndex=""+f.props.zIndex}function ae(l,c,n){if(n===void 0&&(n=!0),G.forEach(function(o){o[l]&&o[l].apply(o,c)}),n){var r;(r=f.props)[l].apply(r,c)}}function oe(){var l=f.props.aria;if(l.content){var c="aria-"+l.content,n=M.id,r=ft(f.props.triggerTarget||e);r.forEach(function(o){var h=o.getAttribute(c);if(f.state.isVisible)o.setAttribute(c,h?h+" "+n:n);else{var u=h&&h.replace(n,"").trim();u?o.setAttribute(c,u):o.removeAttribute(c)}})}}function ce(){if(!(p||!f.props.aria.expanded)){var l=ft(f.props.triggerTarget||e);l.forEach(function(c){f.props.interactive?c.setAttribute("aria-expanded",f.state.isVisible&&c===m()?"true":"false"):c.removeAttribute("aria-expanded")})}}function Ae(){A().removeEventListener("mousemove",V),Pt=Pt.filter(function(l){return l!==V})}function ve(l){if(!(Ve.isTouch&&(w||l.type==="mousedown"))){var c=l.composedPath&&l.composedPath()[0]||l.target;if(!(f.props.interactive&&xs(M,c))){if(ft(f.props.triggerTarget||e).some(function(n){return xs(n,c)})){if(Ve.isTouch||f.state.isVisible&&f.props.trigger.indexOf("click")>=0)return}else ae("onClickOutside",[f,l]);f.props.hideOnClick===!0&&(f.clearDelayTimeouts(),f.hide(),E=!0,setTimeout(function(){E=!1}),f.state.isMounted||ie())}}}function we(){w=!0}function H(){w=!1}function ue(){var l=A();l.addEventListener("mousedown",ve,!0),l.addEventListener("touchend",ve,nt),l.addEventListener("touchstart",H,nt),l.addEventListener("touchmove",we,nt)}function ie(){var l=A();l.removeEventListener("mousedown",ve,!0),l.removeEventListener("touchend",ve,nt),l.removeEventListener("touchstart",H,nt),l.removeEventListener("touchmove",we,nt)}function Ie(l,c){Ce(l,function(){!f.state.isVisible&&M.parentNode&&M.parentNode.contains(M)&&c()})}function Q(l,c){Ce(l,c)}function Ce(l,c){var n=_().box;function r(o){o.target===n&&(Ht(n,"remove",r),c())}if(l===0)return c();Ht(n,"remove",P),Ht(n,"add",r),P=r}function ye(l,c,n){n===void 0&&(n=!1);var r=ft(f.props.triggerTarget||e);r.forEach(function(o){o.addEventListener(l,c,n),D.push({node:o,eventType:l,handler:c,options:n})})}function Ue(){k()&&(ye("touchstart",de,{passive:!0}),ye("touchend",Le,{passive:!0})),ir(f.props.trigger).forEach(function(l){if(l!=="manual")switch(ye(l,de),l){case"mouseenter":ye("mouseleave",Le);break;case"focus":ye(yr?"focusout":"blur",Fe);break;case"focusin":ye("focusout",Fe);break}})}function ze(){D.forEach(function(l){var c=l.node,n=l.eventType,r=l.handler,o=l.options;c.removeEventListener(n,r,o)}),D=[]}function de(l){var c,n=!1;if(!(!f.state.isEnabled||qe(l)||E)){var r=((c=$)==null?void 0:c.type)==="focus";$=l,U=l.currentTarget,ce(),!f.state.isVisible&&dr(l)&&Pt.forEach(function(o){return o(l)}),l.type==="click"&&(f.props.trigger.indexOf("mouseenter")<0||x)&&f.props.hideOnClick!==!1&&f.state.isVisible?n=!0:je(l),l.type==="click"&&(x=!n),n&&!r&&Me(l)}}function Re(l){var c=l.target,n=m().contains(c)||M.contains(c);if(!(l.type==="mousemove"&&n)){var r=xe().concat(M).map(function(o){var h,u=o._tippy,d=(h=u.popperInstance)==null?void 0:h.state;return d?{popperRect:o.getBoundingClientRect(),popperState:d,props:t}:null}).filter(Boolean);vr(r,l)&&(Ae(),Me(l))}}function Le(l){var c=qe(l)||f.props.trigger.indexOf("click")>=0&&x;if(!c){if(f.props.interactive){f.hideWithInteractivity(l);return}Me(l)}}function Fe(l){f.props.trigger.indexOf("focusin")<0&&l.target!==m()||f.props.interactive&&l.relatedTarget&&M.contains(l.relatedTarget)||Me(l)}function qe(l){return Ve.isTouch?k()!==l.type.indexOf("touch")>=0:!1}function Ke(){Xe();var l=f.props,c=l.popperOptions,n=l.placement,r=l.offset,o=l.getReferenceClientRect,h=l.moveTransition,u=C()?Jt(M).arrow:null,d=o?{getBoundingClientRect:o,contextElement:o.contextElement||m()}:e,b={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(q){var z=q.state;if(C()){var ee=_(),ne=ee.box;["placement","reference-hidden","escaped"].forEach(function(le){le==="placement"?ne.setAttribute("data-placement",z.placement):z.attributes.popper["data-popper-"+le]?ne.setAttribute("data-"+le,""):ne.removeAttribute("data-"+le)}),z.attributes.popper={}}}},O=[{name:"offset",options:{offset:r}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!h}},b];C()&&u&&O.push({name:"arrow",options:{element:u,padding:3}}),O.push.apply(O,(c==null?void 0:c.modifiers)||[]),f.popperInstance=ar(d,M,Object.assign({},c,{placement:n,onFirstUpdate:I,modifiers:O}))}function Xe(){f.popperInstance&&(f.popperInstance.destroy(),f.popperInstance=null)}function ge(){var l=f.props.appendTo,c,n=m();f.props.interactive&&l===Vs||l==="parent"?c=n.parentNode:c=Ns(l,[n]),c.contains(M)||c.appendChild(M),f.state.isMounted=!0,Ke()}function xe(){return Rt(M.querySelectorAll("[data-tippy-root]"))}function je(l){f.clearDelayTimeouts(),l&&ae("onTrigger",[f,l]),ue();var c=S(!0),n=y(),r=n[0],o=n[1];Ve.isTouch&&r==="hold"&&o&&(c=o),c?a=setTimeout(function(){f.show()},c):f.show()}function Me(l){if(f.clearDelayTimeouts(),ae("onUntrigger",[f,l]),!f.state.isVisible){ie();return}if(!(f.props.trigger.indexOf("mouseenter")>=0&&f.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(l.type)>=0&&x)){var c=S(!1);c?i=setTimeout(function(){f.state.isVisible&&f.hide()},c):g=requestAnimationFrame(function(){f.hide()})}}function De(){f.state.isEnabled=!0}function Je(){f.hide(),f.state.isEnabled=!1}function Ge(){clearTimeout(a),clearTimeout(i),cancelAnimationFrame(g)}function We(l){if(!f.state.isDestroyed){ae("onBeforeUpdate",[f,l]),ze();var c=f.props,n=Cs(e,Object.assign({},c,ws(l),{ignoreAttributes:!0}));f.props=n,Ue(),c.interactiveDebounce!==n.interactiveDebounce&&(Ae(),V=ms(Re,n.interactiveDebounce)),c.triggerTarget&&!n.triggerTarget?ft(c.triggerTarget).forEach(function(r){r.removeAttribute("aria-expanded")}):n.triggerTarget&&e.removeAttribute("aria-expanded"),ce(),F(),J&&J(c,n),f.popperInstance&&(Ke(),xe().forEach(function(r){requestAnimationFrame(r._tippy.popperInstance.forceUpdate)})),ae("onAfterUpdate",[f,l])}}function Be(l){f.setProps({content:l})}function et(){var l=f.state.isVisible,c=f.state.isDestroyed,n=!f.state.isEnabled,r=Ve.isTouch&&!f.props.touch,o=Vt(f.props.duration,0,Pe.duration);if(!(l||c||n||r)&&!m().hasAttribute("disabled")&&(ae("onShow",[f],!1),f.props.onShow(f)!==!1)){if(f.state.isVisible=!0,C()&&(M.style.visibility="visible"),F(),ue(),f.state.isMounted||(M.style.transition="none"),C()){var h=_(),u=h.box,d=h.content;Nt([u,d],0)}I=function(){var O;if(!(!f.state.isVisible||T)){if(T=!0,M.offsetHeight,M.style.transition=f.props.moveTransition,C()&&f.props.animation){var R=_(),q=R.box,z=R.content;Nt([q,z],o),ys([q,z],"visible")}oe(),ce(),bs(zt,f),(O=f.popperInstance)==null||O.forceUpdate(),ae("onMount",[f]),f.props.animation&&C()&&Q(o,function(){f.state.isShown=!0,ae("onShown",[f])})}},ge()}}function Ze(){var l=!f.state.isVisible,c=f.state.isDestroyed,n=!f.state.isEnabled,r=Vt(f.props.duration,1,Pe.duration);if(!(l||c||n)&&(ae("onHide",[f],!1),f.props.onHide(f)!==!1)){if(f.state.isVisible=!1,f.state.isShown=!1,T=!1,x=!1,C()&&(M.style.visibility="hidden"),Ae(),ie(),F(!0),C()){var o=_(),h=o.box,u=o.content;f.props.animation&&(Nt([h,u],r),ys([h,u],"hidden"))}oe(),ce(),f.props.animation?C()&&Ie(r,f.unmount):f.unmount()}}function tt(l){A().addEventListener("mousemove",V),bs(Pt,V),V(l)}function it(){f.state.isVisible&&f.hide(),f.state.isMounted&&(Xe(),xe().forEach(function(l){l._tippy.unmount()}),M.parentNode&&M.parentNode.removeChild(M),zt=zt.filter(function(l){return l!==f}),f.state.isMounted=!1,ae("onHidden",[f]))}function bt(){f.state.isDestroyed||(f.clearDelayTimeouts(),f.unmount(),ze(),delete e._tippy,f.state.isDestroyed=!0,ae("onDestroy",[f]))}}function rt(e,s){s===void 0&&(s={});var t=Pe.plugins.concat(s.plugins||[]);br();var a=Object.assign({},s,{plugins:t}),i=fr(e),g=i.reduce(function(x,E){var w=E&&Ar(E,a);return w&&x.push(w),x},[]);return Ft(e)?g[0]:g}rt.defaultProps=Pe;rt.setDefaultProps=kr;rt.currentInput=Ve;Object.assign({},_s,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});rt.setDefaultProps({render:qs});const Mr={class:"task-pane"},Dr={key:0,class:"loading-overlay"},_r={key:1,class:"format-error-overlay"},Or={class:"format-error-content"},Pr={class:"format-error-message"},Ir={class:"format-error-actions"},Ur={class:"doc-header"},Rr={class:"doc-title"},Lr={class:"action-area"},Fr={class:"select-container"},jr={class:"select-group"},Wr=["disabled"],Br=["value"],Vr={class:"select-group"},Nr=["disabled"],Hr=["value"],zr=["title"],qr={key:0,class:"science-warning"},Yr={class:"action-buttons"},Kr=["disabled"],Xr={class:"btn-content"},Jr={key:0,class:"button-loader"},Gr=["disabled"],Zr={class:"btn-content"},Qr={key:0,class:"button-loader"},eo=["disabled"],to={class:"content-area"},so={class:"modal-header"},no={class:"modal-body"},ao={class:"selection-content"},ro={class:"modal-header"},oo={class:"modal-body"},io={class:"alert-message"},lo={class:"alert-actions"},co={key:2,class:"modal-overlay"},uo={class:"modal-header"},po={class:"modal-body"},fo={class:"confirm-message"},ho={class:"confirm-actions"},vo={class:"task-queue"},go={class:"queue-header"},mo={class:"queue-status-filter"},bo=["value"],wo={class:"queue-actions"},yo=["disabled","title"],xo={class:"task-count"},To={key:0,class:"queue-table-container"},Co={class:"col-id"},ko={class:"id-header"},So={key:0,class:"col-subject"},Eo={class:"subject-header"},$o={class:"switch"},Ao=["title"],Mo={key:1,class:"col-status"},Do=["onClick"],_o={class:"col-id"},Oo={class:"id-content"},Po={class:"task-id"},Io={key:0,class:"enhance-svg-icon"},Uo={key:0,class:"status-in-id"},Ro={key:0,class:"col-subject"},Lo=["onMouseenter"],Fo={key:1,class:"col-status"},jo={class:"status-cell"},Wo={class:"col-actions"},Bo={class:"task-actions"},Vo=["onClick"],No=["onClick"],Ho={key:2,class:"no-action-icon",title:"无可用操作"},zo={key:1,class:"empty-queue"},qo={key:3,class:"log-container"},Yo={class:"log-actions"},Ko={class:"toggle-icon"},Xo=["innerHTML"],Jo={__name:"TaskPane",setup(e){const s=se(!1),t=se(!1),a=se(!1),i=se(""),g=se(!1),x=se(!1),E=se(!1),w=se(!0),T=se(""),$=se(!1),P=se(window.innerWidth),I=ut(()=>P.value<750),D=ut(()=>P.value<380),V=()=>{P.value=window.innerWidth},U=se(null),N=se(!1),K=se(""),L={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},B=se(!1),f=se([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"}]),{docName:X,selected:M,logger:J,map:G,subject:p,stage:y,subjectOptions:k,stageOptions:C,appConfig:m,clearLog:A,checkDocumentFormat:_,getTaskStatusClass:S,getTaskStatusText:F,terminateTask:ae,run1:oe,setupLifecycle:ce,navigateToTaskControl:Ae,isLoading:ve,tryRemoveTaskPlaceholderWithLoading:we,confirmDialog:H,handleConfirm:ue,getCompletedTasksCount:ie,showConfirm:Ie}=pn(),Q=se(null);(()=>{var l;try{if((l=window.Application)!=null&&l.PluginStorage){const c=window.Application.PluginStorage.getItem("user_info");c?(Q.value=JSON.parse(c),console.log("用户信息已加载:",Q.value)):console.log("未找到用户信息")}}catch(c){console.error("解析用户信息时出错:",c)}})();const ye=ut(()=>!Q.value||Q.value.isAdmin||Q.value.isOwner?k:Q.value.subject?k.filter(l=>l.value===Q.value.subject):k),Ue=()=>{Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject&&(p.value=Q.value.subject)},ze=ut(()=>["physics","chemistry","biology","math"].includes(p.value));qt(m,l=>{l&&(console.log("TaskPane组件收到应用配置更新:",l),console.log("当前版本类型:",l.EDITION),console.log("当前年级选项:",C.value))},{deep:!0,immediate:!0});const de=()=>{try{const l=_();w.value=l.isValid,T.value=l.message,$.value=!l.isValid,l.isValid||console.warn("文档格式检查失败:",l.message)}catch(l){console.error("执行文档格式检查时出错:",l),w.value=!1,T.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",$.value=!0}},Re=ut(()=>{let l={};const c=G;if(K.value==="")l={...c};else for(const n in c)if(Object.prototype.hasOwnProperty.call(c,n)){const r=c[n];r.status===K.value&&(l[n]=r)}return N.value&&U.value?l[U.value]?{[U.value]:l[U.value]}:{}:l}),Le=ut(()=>{const l=Re.value;return Object.entries(l).map(([n,r])=>({tid:n,...r})).sort((n,r)=>{const o=n.startTime||0;return(r.startTime||0)-o}).reduce((n,r)=>{const{tid:o,...h}=r;return n[o]=h,n},{})}),Fe=(l="wps-analysis")=>{p.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(i.value="未选中内容",t.value=!0):(l==="wps-analysis"?x.value=!0:l==="wps-enhance_analysis"&&(E.value=!0),oe(l).catch(c=>{console.log(c),c.message.includes("重叠")?(i.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",c),i.value=c.message,t.value=!0)}).finally(()=>{l==="wps-analysis"?x.value=!1:l==="wps-enhance_analysis"&&(E.value=!1)})):(i.value="请选择学科",t.value=!0)},qe=()=>{i.value="功能开发中……敬请期待",t.value=!0},Ke=(l,c)=>{U.value=l,Ae(l)},Xe=l=>{G[l]&&(G[l].status=3),U.value===l&&(U.value=null),we(l,!0)},ge=async()=>{const l=Object.entries(G).filter(([c,n])=>n.status===2);if(l.length===0){i.value="没有已完成的任务可释放",t.value=!0;return}try{if(await Ie(`确定要释放所有 ${l.length} 个已完成的任务吗？
此操作不可撤销。`)){let n=0;l.forEach(([r,o])=>{G[r]&&(G[r].status=3,U.value===r&&(U.value=null),we(r,!0),n++)}),i.value=`已成功释放 ${n} 个任务`,t.value=!0}}catch(c){console.error("释放任务时出错:",c),i.value="释放任务时出现错误",t.value=!0}},xe=()=>{g.value=!g.value},je=l=>l?l.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Me=l=>{const c=De(l);return c?je(c):"无题目内容"},De=l=>{if(!l.selectedText)return"";const c=l.selectedText.split("\r").filter(r=>r.trim());if(c.length===1){const r=l.selectedText.split(`
`).filter(o=>o.trim());r.length>1&&c.splice(0,1,...r)}const n=c.map((r,o)=>{const h=r.trim();return h.length>200,h});return n.length===1?c[0].trim():n.join(`
`)},Je=(l,c)=>{if(!B.value)return;const n=l.target,r=De(c).toString();if(!r||r.trim()===""){console.log("题目内容为空，不显示tooltip");return}const o=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${r.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,h=l.clientX,u=l.clientY;if(L.subjects.has(n)){const b=L.subjects.get(n);b.setContent(o),b.setProps({getReferenceClientRect:()=>({width:0,height:0,top:u,bottom:u,left:h,right:h})}),b.show();return}const d=rt(n,{content:o,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:u,bottom:u,left:h,right:h}),onHidden:()=>{d.setProps({getReferenceClientRect:null})}});L.subjects.set(n,d),d.show()},Ge=l=>{const c=l.currentTarget,n=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,r=l.clientX,o=l.clientY;if(L.enhance.has(c)){const u=L.enhance.get(c);u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:o,bottom:o,left:r,right:r})}),u.show();return}const h=rt(c,{content:n,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});L.enhance.set(c,h),h.show()},We=()=>{L.subjects.forEach(l=>{l.destroy()}),L.subjects.clear(),L.enhance.forEach(l=>{l.destroy()}),L.enhance.clear(),L.softBreak.forEach(l=>{l.destroy()}),L.softBreak.clear(),document.removeEventListener("click",Be),document.removeEventListener("mousemove",Ze)},Be=l=>{const c=document.querySelector(".tippy-box");c&&!c.contains(l.target)&&(L.subjects.forEach(n=>n.hide()),L.enhance.forEach(n=>n.hide()),L.softBreak.forEach(n=>n.hide()))};let et=0;const Ze=l=>{const c=Date.now();if(c-et<100)return;et=c;const n=document.querySelector(".tippy-box");if(!n)return;const r=n.getBoundingClientRect();!(l.clientX>=r.left-20&&l.clientX<=r.right+20&&l.clientY>=r.top-20&&l.clientY<=r.bottom+20)&&!n.matches(":hover")&&(L.subjects.forEach(h=>h.hide()),L.enhance.forEach(h=>h.hide()),L.softBreak.forEach(h=>h.hide()))},tt=()=>{document.addEventListener("click",Be),document.addEventListener("mousemove",Ze)};Es(()=>{window.addEventListener("resize",V),tt(),Ue(),setTimeout(()=>{de()},500);const l=document.createElement("style");l.id="tippy-custom-styles",l.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(l)}),Gs(()=>{window.removeEventListener("resize",V),We();const l=document.getElementById("tippy-custom-styles");l&&l.remove()}),ce();const it=l=>l.selectedText?l.selectedText.includes("\v")||l.selectedText.includes("\v"):!1,bt=l=>{const c=l.currentTarget,n=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,r=l.clientX,o=l.clientY;if(L.softBreak.has(c)){const u=L.softBreak.get(c);u.setProps({getReferenceClientRect:()=>({width:0,height:0,top:o,bottom:o,left:r,right:r})}),u.show();return}const h=rt(c,{content:n,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});L.softBreak.set(c,h),h.show()};return(l,c)=>{var n,r,o,h,u;return j(),W("div",Mr,[re(ve)?(j(),W("div",Dr,c[26]||(c[26]=[v("div",{class:"loading-spinner"},null,-1),v("div",{class:"loading-text"},"处理中...",-1)]))):Z("",!0),$.value?(j(),W("div",_r,[v("div",Or,[c[27]||(c[27]=v("div",{class:"format-error-icon"},"⚠️",-1)),c[28]||(c[28]=v("div",{class:"format-error-title"},"文档格式不支持",-1)),v("div",Pr,te(T.value),1),v("div",Ir,[v("button",{class:"retry-check-btn",onClick:c[0]||(c[0]=d=>de())},"重新检查")])])])):Z("",!0),v("div",Ur,[v("div",Rr,te(re(X)||"未选择文档"),1),v("button",{class:"settings-btn",onClick:c[1]||(c[1]=d=>a.value=!0)},c[29]||(c[29]=[v("i",{class:"icon-settings"},null,-1)]))]),v("div",Lr,[v("div",Fr,[v("div",jr,[c[30]||(c[30]=v("label",{for:"stage-select"},"年级:",-1)),_e(v("select",{id:"stage-select","onUpdate:modelValue":c[2]||(c[2]=d=>rs(y)?y.value=d:null),class:"select-input",disabled:$.value},[(j(!0),W(xt,null,Tt(re(C),d=>(j(),W("option",{key:d.value,value:d.value},te(d.label),9,Br))),128))],8,Wr),[[Bt,re(y)]])]),v("div",Vr,[c[31]||(c[31]=v("label",{for:"subject-select"},"学科:",-1)),_e(v("select",{id:"subject-select","onUpdate:modelValue":c[3]||(c[3]=d=>rs(p)?p.value=d:null),class:"select-input",disabled:$.value},[(j(!0),W(xt,null,Tt(ye.value,d=>(j(),W("option",{key:d.value,value:d.value},te(d.label),9,Hr))),128))],8,Nr),[[Bt,re(p)]]),Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject?(j(),W("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((n=ye.value.find(d=>d.value===Q.value.subject))==null?void 0:n.label)||Q.value.subject} 学科`}," 🔒 ",8,zr)):Z("",!0)])]),ze.value?(j(),W("div",qr," 理科可使用增强模式以获取更精准的解析 ")):Z("",!0),v("div",Yr,[v("button",{class:"action-btn primary",onClick:c[4]||(c[4]=d=>Fe("wps-analysis")),disabled:x.value||$.value},[v("div",Xr,[x.value?(j(),W("span",Jr)):Z("",!0),c[32]||(c[32]=v("span",{class:"btn-text"},"解析",-1))])],8,Kr),ze.value?(j(),W("button",{key:0,class:"action-btn enhance",onClick:c[5]||(c[5]=d=>Fe("wps-enhance_analysis")),disabled:E.value||$.value},[v("div",Zr,[E.value?(j(),W("span",Qr)):Z("",!0),c[33]||(c[33]=v("span",{class:"btn-text"},"增强解析",-1))])],8,Gr)):Z("",!0),((o=(r=Q.value)==null?void 0:r.orgs[0])==null?void 0:o.orgId)===2?(j(),W("button",{key:1,class:"action-btn secondary",onClick:c[6]||(c[6]=d=>qe()),disabled:$.value},c[34]||(c[34]=[v("div",{class:"btn-content"},[v("span",{class:"btn-text"},"校对")],-1)]),8,eo)):Z("",!0)])]),v("div",to,[s.value?(j(),W("div",{key:0,class:"modal-overlay",onClick:c[9]||(c[9]=d=>s.value=!1)},[v("div",{class:"modal-content",onClick:c[8]||(c[8]=st(()=>{},["stop"]))},[v("div",so,[c[35]||(c[35]=v("div",{class:"modal-title"},"选中内容",-1)),v("button",{class:"modal-close",onClick:c[7]||(c[7]=d=>s.value=!1)},"×")]),v("div",no,[v("pre",ao,te(re(M)||"未选中内容"),1)])])])):Z("",!0),t.value?(j(),W("div",{key:1,class:"modal-overlay",onClick:c[13]||(c[13]=d=>t.value=!1)},[v("div",{class:"modal-content alert-modal",onClick:c[12]||(c[12]=st(()=>{},["stop"]))},[v("div",ro,[c[36]||(c[36]=v("div",{class:"modal-title"},"提示",-1)),v("button",{class:"modal-close",onClick:c[10]||(c[10]=d=>t.value=!1)},"×")]),v("div",oo,[v("div",io,te(i.value),1),v("div",lo,[v("button",{class:"action-btn primary",onClick:c[11]||(c[11]=d=>t.value=!1)},"确定")])])])])):Z("",!0),re(H).show?(j(),W("div",co,[v("div",{class:"modal-content confirm-modal",onClick:c[17]||(c[17]=st(()=>{},["stop"]))},[v("div",uo,[c[37]||(c[37]=v("div",{class:"modal-title"},"确认",-1)),v("button",{class:"modal-close",onClick:c[14]||(c[14]=d=>re(ue)(!1))},"×")]),v("div",po,[v("div",fo,te(re(H).message),1),v("div",ho,[v("button",{class:"action-btn secondary",onClick:c[15]||(c[15]=d=>re(ue)(!1))},"取消"),v("button",{class:"action-btn primary",onClick:c[16]||(c[16]=d=>re(ue)(!0))},"确定")])])])])):Z("",!0),v("div",vo,[v("div",go,[c[38]||(c[38]=v("div",{class:"queue-title"},"任务队列",-1)),v("div",mo,[_e(v("select",{id:"status-filter-select","onUpdate:modelValue":c[18]||(c[18]=d=>K.value=d),class:"status-filter-select-input"},[(j(!0),W(xt,null,Tt(f.value,d=>(j(),W("option",{key:d.value,value:d.value},te(d.label),9,bo))),128))],512),[[Bt,K.value]])]),v("div",wo,[v("button",{class:"release-all-btn",onClick:ge,disabled:re(ie)()===0,title:re(ie)()===0?"无已完成任务可释放":`释放所有${re(ie)()}个已完成任务`}," 一键释放 ",8,yo)]),v("div",xo,te(Object.keys(Re.value).length)+"个任务",1)]),Object.keys(Re.value).length>0?(j(),W("div",To,[v("table",{class:ke(["queue-table",{"narrow-view":I.value,"ultra-narrow-view":D.value}])},[v("thead",null,[v("tr",null,[v("th",Co,[v("div",ko,[c[40]||(c[40]=v("span",null,"任务ID",-1)),v("span",{class:"help-icon",onMouseenter:c[19]||(c[19]=d=>Ge(d))},c[39]||(c[39]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[v("circle",{cx:"12",cy:"12",r:"10"}),v("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),v("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),I.value?Z("",!0):(j(),W("th",So,[v("div",Eo,[c[41]||(c[41]=v("span",null,"题目内容",-1)),v("label",$o,[_e(v("input",{type:"checkbox","onUpdate:modelValue":c[20]||(c[20]=d=>B.value=d)},null,512),[[Zs,B.value]]),v("span",{class:"slider round",title:B.value?"关闭题目预览":"开启题目预览"},null,8,Ao)])])])),D.value?Z("",!0):(j(),W("th",Mo,"状态")),c[42]||(c[42]=v("th",{class:"col-actions"},"操作",-1))])]),v("tbody",null,[(j(!0),W(xt,null,Tt(Le.value,(d,b)=>(j(),W("tr",{key:b,class:ke(["task-row",[re(S)(d.status),{"selected-task-row":b===U.value}]]),onClick:O=>Ke(b)},[v("td",_o,[v("div",{class:ke(["id-cell",{"id-with-status":D.value}])},[v("div",Oo,[v("span",Po,te(b.substring(0,8)),1),d.wordType==="wps-enhance_analysis"||d.isEnhanced?(j(),W("span",Io,c[43]||(c[43]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[v("title",null,"增强模式"),v("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):Z("",!0),it(d)?(j(),W("span",{key:1,class:"soft-break-warning-icon",onMouseenter:c[21]||(c[21]=O=>bt(O))},c[44]||(c[44]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[v("title",null,"提示"),v("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),v("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),v("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):Z("",!0)]),D.value?(j(),W("div",Uo,[v("span",{class:ke(["task-tag compact",re(S)(d.status)])},te(re(F)(d.status)),3)])):Z("",!0)],2)]),I.value?Z("",!0):(j(),W("td",Ro,[v("div",{class:"subject-cell",onMouseenter:O=>Je(O,d)},te(Me(d)),41,Lo)])),D.value?Z("",!0):(j(),W("td",Fo,[v("div",jo,[v("span",{class:ke(["task-tag",re(S)(d.status)])},te(re(F)(d.status)),3)])])),v("td",Wo,[v("div",Bo,[d.status===1?(j(),W("button",{key:0,onClick:st(O=>re(ae)(b),["stop"]),class:"terminate-btn"}," 终止 ",8,Vo)):Z("",!0),d.status===2?(j(),W("button",{key:1,onClick:st(O=>Xe(b),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,No)):Z("",!0),d.status!==1&&d.status!==2?(j(),W("span",Ho,c[45]||(c[45]=[v("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[v("circle",{cx:"12",cy:"12",r:"10"}),v("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),v("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):Z("",!0)])])],10,Do))),128))])],2)])):(j(),W("div",zo,c[46]||(c[46]=[v("div",{class:"empty-text"},"暂无任务",-1)])))]),((u=(h=Q.value)==null?void 0:h.orgs[0])==null?void 0:u.orgId)===2?(j(),W("div",qo,[v("div",{class:"log-header",onClick:xe},[c[47]||(c[47]=v("div",{class:"log-title"},"执行日志",-1)),v("div",Yo,[v("button",{class:"clear-btn",onClick:c[22]||(c[22]=st(d=>re(A)(),["stop"]))},"清空日志"),v("span",Ko,te(g.value?"▼":"▶"),1)])]),g.value?(j(),W("div",{key:0,class:"log-content",innerHTML:re(J)},null,8,Xo)):Z("",!0)])):Z("",!0)]),a.value?(j(),W("div",{key:2,class:"modal-overlay",onClick:c[25]||(c[25]=d=>a.value=!1)},[v("div",{class:"modal-content",onClick:c[24]||(c[24]=st(()=>{},["stop"]))},[Qs(Qn,{onClose:c[23]||(c[23]=d=>a.value=!1)})])])):Z("",!0)])}}},Zo=$s(Jo,[["__scopeId","data-v-e69ce6c3"]]);export{Zo as default};
