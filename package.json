{"name": "hexin-wps-addon", "addonType": "wps", "version": "1.1.23", "private": true, "type": "module", "scripts": {"dev": "vite --port 3889", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "wps-build": "wpsjs build && node scripts/post-build.js", "debug": "wpsjs debug", "postinstall": "patch-package"}, "dependencies": {"@popperjs/core": "^2.11.8", "ali-oss": "^6.22.0", "axios": "^1.7.2", "core-js": "^3.37.1", "js-sha1": "^0.7.0", "tippy.js": "^6.3.7", "vue": "^3.4.29", "vue-router": "^4.3.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "chalk": "^5.4.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "patch-package": "^8.0.0", "prettier": "^3.2.5", "vite": "^5.3.1", "wps-jsapi-declare": "latest", "wpsjs": "^2.2.1"}}