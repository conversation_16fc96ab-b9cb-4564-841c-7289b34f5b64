import { ref } from 'vue'

export function useNotification() {
  const showNotification = ref(false)
  const notificationMessage = ref('')
  const notificationType = ref('info')
  const notificationTimer = ref(null)
  
  /**
   * 显示通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 (success, error, info, warning)
   * @param {number} duration - 显示时长（毫秒）
   */
  const showNotify = (message, type = 'info', duration = 2000) => {
    // 清除之前的定时器
    if (notificationTimer.value) {
      clearTimeout(notificationTimer.value)
    }
    
    // 设置通知内容和类型
    notificationMessage.value = message
    notificationType.value = type
    showNotification.value = true
    
    // 设置自动关闭
    notificationTimer.value = setTimeout(() => {
      showNotification.value = false
    }, duration)
  }
  
  /**
   * 清理定时器
   */
  const cleanupNotification = () => {
    if (notificationTimer.value) {
      clearTimeout(notificationTimer.value)
    }
  }

  return {
    showNotification,
    notificationMessage,
    notificationType,
    showNotify,
    cleanupNotification
  }
} 