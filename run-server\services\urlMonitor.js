const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { defaultLogger } = require('../utils/logger');
const wsServer = require('./wsServer');
const configStore = require('./configStore');

class UrlMonitor {
    constructor() {
        // 明确初始化Map对象
        try {
            this.monitoredUrls = new Map(); // Map of URL -> {status, lastChecked, interval, urlId}
            defaultLogger.info('URL监控器初始化，Map已创建');
        } catch (error) {
            defaultLogger.error(`创建Map对象失败: ${error.message}`);
            // 回退到使用对象
            this.monitoredUrls = {};
            this._isUsingObject = true;
            defaultLogger.info('回退到使用普通对象存储URL数据');
        }
        
        this.nextUrlId = 1;
        
        // 添加下载路径设置，默认使用临时目录
        this.downloadPath = configStore.getUrlDownloadPath() || 'c:\\Temp\\Downloads';
        
        // 确保下载目录存在
        this.ensureDownloadDirectory();
        
        // 添加客户端与URL监控的关联映射
        this.clientUrlMap = new Map(); // 存储客户端ID与URL监控ID的关联关系
    }
    
    // 辅助方法，确保即使回退到对象也能正常工作
    _getUrlData(urlId) {
        if (this._isUsingObject) {
            return this.monitoredUrls[urlId];
        } else {
            return this.monitoredUrls.get(urlId);
        }
    }
    
    // 辅助方法，设置URL数据，兼容对象和Map
    _setUrlData(urlId, data) {
        if (this._isUsingObject) {
            this.monitoredUrls[urlId] = data;
        } else {
            this.monitoredUrls.set(urlId, data);
        }
    }
    
    // 辅助方法，删除URL数据，兼容对象和Map
    _deleteUrlData(urlId) {
        if (this._isUsingObject) {
            delete this.monitoredUrls[urlId];
        } else {
            this.monitoredUrls.delete(urlId);
        }
    }

    /**
     * 确保下载目录存在
     */
    ensureDownloadDirectory() {
        try {
            if (!fs.existsSync(this.downloadPath)) {
                fs.mkdirSync(this.downloadPath, { recursive: true });
                defaultLogger.info(`创建下载目录: ${this.downloadPath}`);
            }
        } catch (error) {
            defaultLogger.error(`创建下载目录失败: ${error.message}`);
        }
    }

    /**
     * 设置下载路径
     * @param {string} dirPath - 新的下载目录路径
     * @param {string} clientId - 客户端ID
     * @returns {boolean} 是否成功设置
     */
    setDownloadPath(dirPath, clientId) {
        if (!dirPath) {
            defaultLogger.error('无效的下载目录路径');
            return false;
        }
        
        try {
            // 保存设置
            configStore.setUrlDownloadPath(dirPath);
            this.downloadPath = dirPath;
            
            // 确保目录存在
            this.ensureDownloadDirectory();
            
            defaultLogger.info(`URL下载路径已设置为: ${dirPath}`);
            
            // 仅通知请求更改的客户端
            if (clientId) {
                this._sendEventToClient('urlDownloadPathChanged', {
                    path: dirPath
                }, clientId);
            }
            
            return true;
        } catch (error) {
            defaultLogger.error(`设置下载路径失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 获取当前下载路径
     * @returns {string} 当前下载路径
     */
    getDownloadPath() {
        return this.downloadPath;
    }

    /**
     * 将客户端ID与URL监控关联起来
     * @param {string} clientId - 客户端ID
     * @param {string} urlId - URL监控ID
     * @private
     */
    _associateClientWithUrl(clientId, urlId) {
        if (!clientId || !urlId) return;
        
        try {
            if (!this.clientUrlMap.has(clientId)) {
                this.clientUrlMap.set(clientId, new Set());
            }
            
            const urlSet = this.clientUrlMap.get(clientId);
            urlSet.add(urlId);
            
            defaultLogger.info(`关联客户端 ${clientId} 与 URL监控 ${urlId}`);
        } catch (error) {
            defaultLogger.error(`关联客户端与URL监控失败: ${error.message}`);
        }
    }
    
    /**
     * 解除客户端ID与URL监控的关联
     * @param {string} urlId - URL监控ID
     * @private
     */
    _dissociateUrlFromClients(urlId) {
        if (!urlId) return;
        
        try {
            // 查找所有关联了该URL的客户端，并移除关联
            for (const [clientId, urlSet] of this.clientUrlMap.entries()) {
                if (urlSet.has(urlId)) {
                    urlSet.delete(urlId);
                    defaultLogger.info(`解除客户端 ${clientId} 与 URL监控 ${urlId} 的关联`);
                }
                
                // 如果客户端没有关联任何URL，清除该客户端记录
                if (urlSet.size === 0) {
                    this.clientUrlMap.delete(clientId);
                }
            }
        } catch (error) {
            defaultLogger.error(`解除URL监控关联失败: ${error.message}`);
        }
    }
    
    /**
     * 获取与URL监控关联的客户端
     * @param {string} urlId - URL监控ID
     * @returns {Array<string>} 关联的客户端ID数组
     * @private
     */
    _getClientsForUrl(urlId) {
        if (!urlId) return [];
        
        try {
            const clients = [];
            
            for (const [clientId, urlSet] of this.clientUrlMap.entries()) {
                if (urlSet.has(urlId)) {
                    clients.push(clientId);
                }
            }
            
            return clients;
        } catch (error) {
            defaultLogger.error(`获取URL关联客户端失败: ${error.message}`);
            return [];
        }
    }
    
    /**
     * 向特定客户端发送事件
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     * @param {string} clientId - 客户端ID
     * @private
     */
    _sendEventToClient(eventType, data, clientId) {
        if (!clientId) return;
        
        try {
            wsServer.sendWatcherEventToClient(eventType, data, clientId);
        } catch (error) {
            defaultLogger.error(`向客户端 ${clientId} 发送事件失败: ${error.message}`);
        }
    }
    
    /**
     * 向与URL监控关联的所有客户端发送事件
     * @param {string} eventType - 事件类型
     * @param {Object} data - 事件数据
     * @param {string} urlId - URL监控ID
     * @private
     */
    _sendEventToAssociatedClients(eventType, data, urlId) {
        if (!urlId) return;
        
        try {
            const clients = this._getClientsForUrl(urlId);
            
            if (clients.length === 0) {
                defaultLogger.warn(`没有客户端与URL监控 ${urlId} 相关联，无法发送事件`);
                return;
            }
            
            clients.forEach(clientId => {
                this._sendEventToClient(eventType, data, clientId);
            });
            
            defaultLogger.info(`已向 ${clients.length} 个关联客户端发送 ${eventType} 事件`);
        } catch (error) {
            defaultLogger.error(`向关联客户端发送事件失败: ${error.message}`);
        }
    }

    /**
     * Start monitoring a URL
     * @param {string} url - URL to monitor
     * @param {number} interval - Check interval in milliseconds (default: 5000ms)
     * @param {Object} options - Additional options
     * @param {boolean} options.downloadOnSuccess - Whether to download the file when accessible
     * @param {string} options.filename - Custom filename for downloaded file
     * @param {string} options.taskId - Associated task ID
     * @param {string} options.clientId - ID of the client initiating the monitoring
     * @returns {string} urlId - ID for the monitored URL
     */
    startMonitoring(url, interval = 1000, options = {}) {
        if (!url) {
            defaultLogger.error('Invalid URL provided for monitoring');
            return null;
        }

        // Generate a unique ID for this URL monitoring task
        const urlId = `url_${this.nextUrlId++}`;
        
        defaultLogger.info(`Starting URL monitoring for: ${url} with ID: ${urlId}, options: ${JSON.stringify(options)}`);
        
        // Create the URL data object first to ensure all properties are set
        const urlData = {
            url,
            status: 'pending',
            lastChecked: null,
            interval,
            timerId: null,
            downloadOnSuccess: options.downloadOnSuccess !== undefined ? options.downloadOnSuccess : false,
            filename: options.filename || this._generateFilenameFromUrl(url),
            taskId: options.taskId || null,
            downloadedPath: null,
            checkStarted: false, // Flag to track if checking has started
            timeoutId: null, // Added for timeout handling
            clientId: options.clientId || null // 存储关联的客户端ID
        };
        
        // Use our helper method to set the URL data
        this._setUrlData(urlId, urlData);
        
        // 如果提供了客户端ID，建立关联
        if (options.clientId) {
            this._associateClientWithUrl(options.clientId, urlId);
        }
        
        // Verify the data was set correctly
        const setData = this._getUrlData(urlId);
        if (!setData) {
            defaultLogger.error(`Failed to set URL data for ${urlId}!`);
            
            // Try again with direct assignment (as a last resort)
            if (this._isUsingObject) {
                this.monitoredUrls[urlId] = urlData;
            } else {
                try {
                    // Try recreating the map with the new entry
                    const tempMap = new Map();
                    this.monitoredUrls.forEach((value, key) => {
                        tempMap.set(key, value);
                    });
                    tempMap.set(urlId, urlData);
                    this.monitoredUrls = tempMap;
                } catch (mapError) {
                    defaultLogger.error(`Map recreation failed: ${mapError.message}`);
                    // Last resort - switch to object mode
                    this._isUsingObject = true;
                    const tempObject = {};
                    this.monitoredUrls.forEach((value, key) => {
                        tempObject[key] = value;
                    });
                    tempObject[urlId] = urlData;
                    this.monitoredUrls = tempObject;
                    defaultLogger.info('Switched to object storage due to Map issues');
                }
            }
        } else {
            defaultLogger.info(`Successfully set URL data for ${urlId}`);
        }

        // Only set up the timer, but don't start checking until explicitly requested
        const timerId = setInterval(async () => {
            // Only check if checking has been started
            const currentData = this._getUrlData(urlId);
            if (currentData && currentData.checkStarted) {
                await this._checkUrl(urlId);
            }
        }, interval);

        // Store the timer ID using an explicit method to update the object
        const currentData = this.monitoredUrls.get(urlId);
        if (currentData) {
            currentData.timerId = timerId;
            this.monitoredUrls.set(urlId, currentData);
        } else {
            defaultLogger.error(`Can't update timer ID because URL data for ${urlId} is missing`);
            // If the data is still missing, try to set everything again
            urlData.timerId = timerId;
            this.monitoredUrls.set(urlId, urlData);
        }
        
        // Return the urlId without starting checks
        return urlId;
    }
    
    /**
     * 从URL生成文件名
     * @param {string} url - URL
     * @returns {string} 生成的文件名
     * @private
     */
    _generateFilenameFromUrl(url) {
        try {
            // 解析URL获取路径部分
            const urlObj = new URL(url);
            let pathname = urlObj.pathname;
            
            // 获取路径的最后一部分作为文件名
            let filename = pathname.split('/').pop();
            
            // 如果文件名为空，使用时间戳
            if (!filename) {
                filename = `file_${Date.now()}`;
            }
            
            // 确保文件名没有特殊字符
            filename = filename.replace(/[^a-zA-Z0-9\-_.]/g, '_');
            
            return filename;
        } catch (error) {
            // 如果URL解析失败，使用时间戳作为文件名
            return `file_${Date.now()}`;
        }
    }

    /**
     * 下载URL对应的文件
     * @param {string} urlId - URL监控ID
     * @returns {Promise<string|null>} 下载的文件路径，失败返回null
     * @private
     */
    async _downloadFile(urlId) {
        const urlData = this.monitoredUrls.get(urlId);
        if (!urlData) return null;
        
        const url = urlData.url;
        const filename = urlData.filename;
        
        try {
            defaultLogger.info(`开始下载文件: ${url}`);
            
            // 确保下载目录存在
            this.ensureDownloadDirectory();
            
            // 构建完整的文件路径
            const filePath = path.join(this.downloadPath, filename);
            
            // 创建写入流
            const writer = fs.createWriteStream(filePath);
            
            // 下载文件
            const response = await axios({
                method: 'get',
                url: url,
                responseType: 'stream',
                timeout: 60000 // 60秒超时
            });
            
            // 管道响应流到文件
            response.data.pipe(writer);
            
            // 返回一个Promise，当文件写入完成或出错时解析
            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    defaultLogger.info(`文件下载成功: ${filePath}`);
                    
                    // 更新URL数据以包含下载的文件路径 - 使用完整的更新方式
                    try {
                        // 重新获取可能已更新的URL数据
                        const currentData = this.monitoredUrls.get(urlId);
                        if (currentData) {
                            // 创建包含所有当前数据的新对象，并更新文件路径
                            const updatedData = {
                                ...currentData,
                                downloadedPath: filePath
                            };
                            
                            // 重新设置到Map中
                            this.monitoredUrls.set(urlId, updatedData);
                            defaultLogger.info(`成功更新URL ${urlId} 的下载路径: ${filePath}`);
                        } else {
                            defaultLogger.error(`无法更新文件路径，URL数据 ${urlId} 不存在`);
                            // 尝试使用原始数据重新设置
                            urlData.downloadedPath = filePath;
                            this.monitoredUrls.set(urlId, urlData);
                        }
                    } catch (updateError) {
                        defaultLogger.error(`更新下载路径时出错: ${updateError.message}`);
                        // 尝试简单直接更新
                        urlData.downloadedPath = filePath;
                        this.monitoredUrls.set(urlId, urlData);
                    }
                    
                    // 发送下载成功通知到关联的客户端
                    const eventData = {
                        urlId,
                        url,
                        filePath,
                        taskId: urlData.taskId
                    };
                    
                    this._sendEventToAssociatedClients('urlFileDownloaded', eventData, urlId);
                    
                    resolve(filePath);
                });
                
                writer.on('error', error => {
                    defaultLogger.error(`文件下载失败: ${error.message}`);
                    
                    // 发送下载失败通知到关联的客户端
                    const eventData = {
                        urlId,
                        url,
                        error: error.message,
                        taskId: urlData.taskId
                    };
                    
                    this._sendEventToAssociatedClients('urlFileDownloadError', eventData, urlId);
                    
                    reject(error);
                });
            });
        } catch (error) {
            defaultLogger.error(`下载文件失败: ${error.message}`);
            
            // 发送下载失败通知到关联的客户端
            const eventData = {
                urlId,
                url,
                error: error.message,
                taskId: urlData.taskId
            };
            
            this._sendEventToAssociatedClients('urlFileDownloadError', eventData, urlId);
            
            return null;
        }
    }

    /**
     * Stop monitoring a specific URL
     * @param {string} urlId - ID of the URL to stop monitoring
     * @param {string} clientId - ID of client requesting to stop (optional)
     */
    stopMonitoring(urlId, clientId) {
        // Use our helper method to get the URL data
        const urlData = this._getUrlData(urlId);
        if (!urlData) {
            defaultLogger.warn(`No URL monitoring found with ID: ${urlId}`);
            return false;
        }
        
        // 验证客户端权限（如果提供了clientId）
        if (clientId && urlData.clientId && urlData.clientId !== clientId) {
            defaultLogger.warn(`Client ${clientId} attempted to stop URL monitoring ${urlId} owned by ${urlData.clientId}`);
            // 可以选择拒绝请求，也可以允许任何客户端停止监控
            // 这里选择允许，但记录警告日志
        }

        // Clear the interval timer
        if (urlData.timerId) {
            try {
                clearInterval(urlData.timerId);
            } catch (timerError) {
                defaultLogger.error(`Error clearing timer: ${timerError.message}`);
            }
        }
        
        // Clear the timeout if it exists
        if (urlData.timeoutId) {
            try {
                clearTimeout(urlData.timeoutId);
                defaultLogger.info(`Cleared timeout for URL monitoring: ${urlId}`);
            } catch (timeoutError) {
                defaultLogger.error(`Error clearing timeout: ${timeoutError.message}`);
            }
        }

        // Get the downloaded file path before removing
        const downloadedPath = urlData.downloadedPath;
        const url = urlData.url;
        const taskId = urlData.taskId;
        
        // 删除下载的文件（如果存在）
        if (downloadedPath && fs.existsSync(downloadedPath)) {
            try {
                fs.unlinkSync(downloadedPath);
                defaultLogger.info(`Deleted downloaded file: ${downloadedPath}`);
            } catch (deleteError) {
                defaultLogger.error(`Error deleting downloaded file: ${deleteError.message}`);
            }
        }
        
        // 在发送停止事件前，发送给关联的客户端
        const eventData = {
            urlId,
            url,
            taskId,
            downloadedPath
        };
        
        this._sendEventToAssociatedClients('urlMonitorStopped', eventData, urlId);
        
        // 解除URL与所有客户端的关联
        this._dissociateUrlFromClients(urlId);
        
        // Remove the URL data using our helper method
        this._deleteUrlData(urlId);
        
        defaultLogger.info(`Stopped monitoring URL: ${url} with ID: ${urlId}`);
        
        return true;
    }

    /**
     * Get the status of all monitored URLs
     * @param {string} clientId - Optional client ID to filter URLs associated with this client
     * @returns {Array} Array of URL monitoring status objects
     */
    getStatus(clientId) {
        const statusList = [];
        
        // 如果指定了客户端ID，只返回与该客户端关联的URL监控
        const urlIdsToInclude = clientId ? 
            Array.from(this.clientUrlMap.get(clientId) || []) : 
            null;
        
        // Handle both Map and Object storage
        if (this._isUsingObject) {
            // If using object storage
            Object.keys(this.monitoredUrls).forEach(urlId => {
                // 如果指定了客户端筛选，检查URL是否与该客户端关联
                if (urlIdsToInclude && !urlIdsToInclude.includes(urlId)) {
                    return;
                }
                
                const data = this.monitoredUrls[urlId];
                if (data) {
                    statusList.push({
                        urlId,
                        url: data.url,
                        status: data.status,
                        lastChecked: data.lastChecked,
                        interval: data.interval,
                        downloadOnSuccess: data.downloadOnSuccess,
                        filename: data.filename,
                        taskId: data.taskId,
                        downloadedPath: data.downloadedPath
                    });
                }
            });
        } else {
            // If using Map storage
            try {
                this.monitoredUrls.forEach((data, urlId) => {
                    // 如果指定了客户端筛选，检查URL是否与该客户端关联
                    if (urlIdsToInclude && !urlIdsToInclude.includes(urlId)) {
                        return;
                    }
                    
                    statusList.push({
                        urlId,
                        url: data.url,
                        status: data.status,
                        lastChecked: data.lastChecked,
                        interval: data.interval,
                        downloadOnSuccess: data.downloadOnSuccess,
                        filename: data.filename,
                        taskId: data.taskId,
                        downloadedPath: data.downloadedPath
                    });
                });
            } catch (error) {
                defaultLogger.error(`Error getting status from Map: ${error.message}`);
                
                // If there was an error with the Map, try converting it to array
                try {
                    Array.from(this.monitoredUrls.entries()).forEach(([urlId, data]) => {
                        // 如果指定了客户端筛选，检查URL是否与该客户端关联
                        if (urlIdsToInclude && !urlIdsToInclude.includes(urlId)) {
                            return;
                        }
                        
                        if (data) {
                            statusList.push({
                                urlId,
                                url: data.url,
                                status: data.status,
                                lastChecked: data.lastChecked, 
                                interval: data.interval,
                                downloadOnSuccess: data.downloadOnSuccess,
                                filename: data.filename,
                                taskId: data.taskId,
                                downloadedPath: data.downloadedPath
                            });
                        }
                    });
                } catch (arrayError) {
                    defaultLogger.error(`Error converting Map to array: ${arrayError.message}`);
                }
            }
        }
        
        return statusList;
    }

    /**
     * Internal method to check a URL's accessibility
     * @param {string} urlId - ID of the URL to check
     * @returns {Promise<void>} A promise that resolves when the check is complete
     * @private
     */
    async _checkUrl(urlId) {
        // Add defensive check and logging for urlId
        if (!urlId) {
            defaultLogger.error('_checkUrl called with null or undefined urlId');
            return;
        }
        
        defaultLogger.info(`Checking URL with ID: ${urlId}`);
        
        // Use our helper method to get the URL data
        const urlData = this._getUrlData(urlId);
        if (!urlData) {
            defaultLogger.error(`No URL data found for ID: ${urlId}`);
            // List all available keys for debugging
            if (this._isUsingObject) {
                defaultLogger.info(`Available URL IDs: ${JSON.stringify(Object.keys(this.monitoredUrls))}`);
            } else {
                try {
                    const keys = Array.from(this.monitoredUrls.keys());
                } catch (error) {
                    defaultLogger.error(`Unable to get keys from Map: ${error.message}`);
                }
            }
            return;
        }

        const url = urlData.url;
        if (!url) {
            defaultLogger.error(`URL is missing for ID: ${urlId}`);
            return;
        }
        
        let previousStatus = urlData.status || 'pending';
        let newStatus = 'pending';
        let statusCode = null;
        let responseTime = null;
        
        // defaultLogger.info(`Starting check for URL: ${url} with ID: ${urlId}`);
        
        const startTime = Date.now();
        
        try {
            // Use axios to make a HEAD request to check if the URL is accessible
            const response = await axios({
                method: 'head',
                url: url,
                timeout: 10000, // 10-second timeout
                validateStatus: () => true // Don't throw on any status code
            });
            
            responseTime = Date.now() - startTime;
            statusCode = response.status;
            
            // Consider 2xx and 3xx status codes as accessible
            if (statusCode >= 200 && statusCode < 400) {
                newStatus = 'accessible';
                console.log('urlData.timeoutId',url, response.status, urlData.timeoutId);
                // If the URL becomes accessible, clear the timeout
                if (urlData.timeoutId && previousStatus !== 'accessible') {
                    clearTimeout(urlData.timeoutId);
                    urlData.timeoutId = null;
                    defaultLogger.info(`URL ${url} became accessible, cleared timeout`);
                }
            } else {
                newStatus = 'inaccessible';
            }
            
            // 如果状态变为可访问且需要下载
            if (newStatus === 'accessible' && 
               (previousStatus !== 'accessible' || !urlData.downloadedPath) && 
                urlData.downloadOnSuccess) {
                // 下载文件 - 异步处理，但不等待结果
                // 由于_checkUrl是在定时器中调用的，我们不应该在这里等待下载完成
                this._downloadFile(urlId)
                    .then(filePath => {
                        if (filePath) {
                            defaultLogger.info(`文件已下载并保存到: ${filePath}`);
                        }
                    })
                    .catch(downloadError => {
                        defaultLogger.error(`下载文件失败: ${downloadError.message}`);
                    });
            }
        } catch (error) {
            responseTime = Date.now() - startTime;
            newStatus = 'error';
            
            defaultLogger.error(`Error checking URL ${url}: ${error.message}`);
        }
        
        // Create updated data object
        const updatedData = {
            ...urlData,
            status: newStatus,
            lastChecked: new Date().toISOString(),
            responseTime: responseTime,
            statusCode: statusCode
        };
        
        // Update using our helper method
        try {
            this._setUrlData(urlId, updatedData);
            // defaultLogger.info(`Updated status for URL ${url} to ${newStatus}`);
        } catch (error) {
            defaultLogger.error(`Failed to update URL data: ${error.message}`);
            
            // Direct fallback attempt
            try {
                if (this._isUsingObject) {
                    this.monitoredUrls[urlId] = updatedData;
                } else {
                    this.monitoredUrls.set(urlId, updatedData);
                }
            } catch (fallbackError) {
                defaultLogger.error(`Fallback update also failed: ${fallbackError.message}`);
            }
        }
        
        // Verify the data was updated
        const verifyData = this._getUrlData(urlId);
        if (!verifyData) {
            defaultLogger.error(`Status update verification failed - no data found for ${urlId}`);
        } else if (verifyData.status !== newStatus) {
            defaultLogger.error(`Status update verification failed - expected ${newStatus} but found ${verifyData.status}`);
        }
        
        // 向关联的客户端发送状态更新
        const eventData = {
            urlId,
            url,
            status: newStatus,
            previousStatus,
            statusChanged: newStatus !== previousStatus,
            lastChecked: updatedData.lastChecked,
            responseTime,
            statusCode,
            taskId: updatedData.taskId,
            downloadedPath: updatedData.downloadedPath
        };
        
        this._sendEventToAssociatedClients('urlMonitorUpdate', eventData, urlId);
    }

    /**
     * Force an immediate check of a URL
     * @param {string} urlId - ID of the URL to check
     * @param {string} clientId - ID of client requesting the check (optional)
     * @returns {Promise<boolean>} A promise that resolves to true if check started, false otherwise
     */
    async forceCheck(urlId, clientId) {
        // 使用辅助方法检查URL是否存在
        const urlData = this._getUrlData(urlId);
        if (!urlData) {
            defaultLogger.warn(`No URL monitoring found with ID: ${urlId}`);
            return false;
        }
        
        // 验证客户端权限（如果提供了clientId）
        if (clientId && urlData.clientId && urlData.clientId !== clientId) {
            defaultLogger.warn(`Client ${clientId} attempted to force check URL ${urlId} owned by ${urlData.clientId}`);
            // 同样，这里选择允许任何客户端强制检查
        }
        
        try {
            // 正确处理Promise
            await this._checkUrl(urlId);
            return true;
        } catch (error) {
            defaultLogger.error(`Force check failed: ${error.message}`);
            return false;
        }
    }
    
    /**
     * 强制下载URL对应的文件
     * @param {string} urlId - URL监控ID
     * @param {string} clientId - 请求下载的客户端ID (optional)
     * @returns {Promise<boolean>} 是否成功触发下载
     */
    async forceDownload(urlId, clientId) {
        if (!this.monitoredUrls.has(urlId)) {
            defaultLogger.warn(`No URL monitoring found with ID: ${urlId}`);
            return false;
        }
        
        // 验证客户端权限（如果提供了clientId）
        const urlData = this._getUrlData(urlId);
        if (clientId && urlData.clientId && urlData.clientId !== clientId) {
            defaultLogger.warn(`Client ${clientId} attempted to force download from URL ${urlId} owned by ${urlData.clientId}`);
            // 允许任何客户端强制下载
        }
        
        try {
            const filePath = await this._downloadFile(urlId);
            return !!filePath;
        } catch (error) {
            defaultLogger.error(`强制下载失败: ${error.message}`);
            return false;
        }
    }

    /**
     * Start checking a URL that has been set up for monitoring
     * @param {string} urlId - ID of the URL to start checking
     * @param {string} clientId - ID of client requesting to start checking (optional)
     * @returns {boolean} Whether the operation was successful
     */
    startChecking(urlId, clientId) {
        const urlData = this._getUrlData(urlId);
        if (!urlData) {
            defaultLogger.error(`No URL data found for ID: ${urlId}`);
            return false;
        }
        
        // 验证客户端权限（如果提供了clientId）
        if (clientId && urlData.clientId && urlData.clientId !== clientId) {
            defaultLogger.warn(`Client ${clientId} attempted to start checking URL ${urlId} owned by ${urlData.clientId}`);
            // 允许任何客户端启动检查
        }
        
        defaultLogger.info(`Starting URL checks for ID: ${urlId}`);
        
        // Update the checkStarted flag
        urlData.checkStarted = true;
        
        // Set a 5-minute timeout to stop checking if no success
        urlData.timeoutId = setTimeout(() => {
            // Check if URL is still being monitored and hasn't succeeded yet
            const currentData = this._getUrlData(urlId);
            if (currentData && currentData.checkStarted && currentData.status !== 'accessible') {
                defaultLogger.warn(`URL check timeout reached for ${urlId} after 5 minutes`);
                
                // Update status to error with timeout message
                currentData.status = 'error';
                currentData.timeoutReached = true;
                currentData.errorMessage = 'Check timeout reached (5 minutes)';
                this._setUrlData(urlId, currentData);
                
                // 向关联的客户端发送超时事件
                const eventData = {
                    urlId,
                    url: currentData.url,
                    taskId: currentData.taskId,
                    message: 'Check timeout reached (5 minutes)'
                };

                
                this._sendEventToAssociatedClients('urlMonitorTimeout', eventData, urlId);
            }
        }, 300000); // 5 minutes = 300,000 ms
        
        this._setUrlData(urlId, urlData);
        
        // Perform the first check immediately
        this._checkUrl(urlId).catch(error => {
            defaultLogger.error(`Error in first URL check: ${error.message}`);
        });
        
        return true;
    }

    /**
     * 获取当前监控的URL数量
     * @returns {number} 当前监控的URL数量
     */
    getMonitoringCount() {
        try {
            if (this._isUsingObject) {
                return Object.keys(this.monitoredUrls).length;
            } else {
                return this.monitoredUrls.size;
            }
        } catch (error) {
            defaultLogger.error(`获取监控URL数量时出错: ${error.message}`);
            return 0;
        }
    }
    
    /**
     * 当客户端断开连接时清理相关资源
     * @param {string} clientId - 断开连接的客户端ID
     */
    cleanupDisconnectedClient(clientId) {
        if (!clientId) return;
        
        try {
            // 获取与该客户端关联的所有URL监控
            const urlSet = this.clientUrlMap.get(clientId);
            if (!urlSet || urlSet.size === 0) {
                // 没有关联的URL监控，直接移除客户端记录
                this.clientUrlMap.delete(clientId);
                return;
            }
            
            // 复制一份URL列表，因为在循环中会修改urlSet
            const urlIds = Array.from(urlSet);
            
            // 清理每个URL监控
            urlIds.forEach(urlId => {
                const urlData = this._getUrlData(urlId);
                if (!urlData) return;
                
                // 如果这个URL监控只与当前客户端关联，可以考虑停止监控
                const clients = this._getClientsForUrl(urlId);
                if (clients.length <= 1) {
                    // 可选：停止监控
                    // this.stopMonitoring(urlId);
                    
                    // 或者保持监控，但记录日志
                    defaultLogger.info(`URL监控 ${urlId} 的唯一关联客户端 ${clientId} 已断开连接，保持监控`);
                } else {
                    // 只解除与当前客户端的关联
                    urlSet.delete(urlId);
                    defaultLogger.info(`已解除客户端 ${clientId} 与 URL监控 ${urlId} 的关联，还有 ${clients.length - 1} 个关联客户端`);
                }
            });
            
            // 移除客户端记录
            this.clientUrlMap.delete(clientId);
            defaultLogger.info(`已清理客户端 ${clientId} 的所有关联`);
        } catch (error) {
            defaultLogger.error(`清理断开连接的客户端失败: ${error.message}`);
        }
    }
}

// Export a singleton instance
module.exports = new UrlMonitor(); 
