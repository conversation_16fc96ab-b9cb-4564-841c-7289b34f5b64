import{U as qs,r as ne,h as mt,i as W,j as xt,k as ks,m as ss,_ as Ss,v as it,n as Ys,o as N,c as V,a as p,p as Ks,t as ee,f as G,q as Ee,w as Le,e as Lt,F as wt,s as yt,u as Ze,x as Ft,y as lt,z as Xs,A as ae,B as jt,C as ns,D as Js,E as Gs}from"./index-DJieWGGJ.js";function Zs(e,n){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=qs.WPS_Enum),e){case"dockLeft":{let s=window.Application.PluginStorage.getItem("taskpane_id");if(s){let a=window.Application.GetTaskPane(s);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let s=window.Application.PluginStorage.getItem("taskpane_id");if(s){let a=window.Application.GetTaskPane(s);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let s=window.Application.PluginStorage.getItem("taskpane_id");if(s){let a=window.Application.GetTaskPane(s);a.Visible=!1}break}case"addString":{let s=window.Application.ActiveDocument;if(s){s.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let s=window.Application.ActiveDocument;return s?s.Name:"当前没有打开任何文档"}}}const Qs={onbuttonclick:Zs};var en=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function tn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function sn(e){if(e.__esModule)return e;var n=e.default;if(typeof n=="function"){var s=function a(){return this instanceof a?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};s.prototype=n.prototype}else s={};return Object.defineProperty(s,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var l=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(s,a,l.get?l:{enumerable:!0,get:function(){return e[a]}})}),s}var Es={exports:{}};const nn={},an=Object.freeze(Object.defineProperty({__proto__:null,default:nn},Symbol.toStringTag,{value:"Module"})),as=sn(an);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var n="input is invalid type",s="finalize already called",a=typeof window=="object",l=a?window:{};l.JS_SHA1_NO_WINDOW&&(a=!1);var v=!a&&typeof self=="object",m=!l.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;m?l=en:v&&(l=self);var T=!l.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,g=!l.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",b="0123456789abcdef".split(""),S=[-**********,8388608,32768,128],I=[24,16,8,0],$=["hex","array","digest","arrayBuffer"],D=[],H=Array.isArray;(l.JS_SHA1_NO_NODE_JS||!H)&&(H=function(u){return Object.prototype.toString.call(u)==="[object Array]"});var R=ArrayBuffer.isView;g&&(l.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!R)&&(R=function(u){return typeof u=="object"&&u.buffer&&u.buffer.constructor===ArrayBuffer});var z=function(u){var w=typeof u;if(w==="string")return[u,!0];if(w!=="object"||u===null)throw new Error(n);if(g&&u.constructor===ArrayBuffer)return[new Uint8Array(u),!1];if(!H(u)&&!R(u))throw new Error(n);return[u,!1]},q=function(u){return function(w){return new M(!0).update(w)[u]()}},P=function(){var u=q("hex");m&&(u=Z(u)),u.create=function(){return new M},u.update=function(y){return u.create().update(y)};for(var w=0;w<$.length;++w){var x=$[w];u[x]=q(x)}return u},Z=function(u){var w=as,x=as.Buffer,y;x.from&&!l.JS_SHA1_NO_BUFFER_FROM?y=x.from:y=function(E){return new x(E)};var k=function(E){if(typeof E=="string")return w.createHash("sha1").update(E,"utf8").digest("hex");if(E==null)throw new Error(n);return E.constructor===ArrayBuffer&&(E=new Uint8Array(E)),H(E)||R(E)||E.constructor===x?w.createHash("sha1").update(y(E)).digest("hex"):u(E)};return k},f=function(u){return function(w,x){return new K(w,!0).update(x)[u]()}},Y=function(){var u=f("hex");u.create=function(y){return new K(y)},u.update=function(y,k){return u.create(y).update(k)};for(var w=0;w<$.length;++w){var x=$[w];u[x]=f(x)}return u};function M(u){u?(D[0]=D[16]=D[1]=D[2]=D[3]=D[4]=D[5]=D[6]=D[7]=D[8]=D[9]=D[10]=D[11]=D[12]=D[13]=D[14]=D[15]=0,this.blocks=D):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}M.prototype.update=function(u){if(this.finalized)throw new Error(s);var w=z(u);u=w[0];for(var x=w[1],y,k=0,E,_=u.length||0,C=this.blocks;k<_;){if(this.hashed&&(this.hashed=!1,C[0]=this.block,this.block=C[16]=C[1]=C[2]=C[3]=C[4]=C[5]=C[6]=C[7]=C[8]=C[9]=C[10]=C[11]=C[12]=C[13]=C[14]=C[15]=0),x)for(E=this.start;k<_&&E<64;++k)y=u.charCodeAt(k),y<128?C[E>>>2]|=y<<I[E++&3]:y<2048?(C[E>>>2]|=(192|y>>>6)<<I[E++&3],C[E>>>2]|=(128|y&63)<<I[E++&3]):y<55296||y>=57344?(C[E>>>2]|=(224|y>>>12)<<I[E++&3],C[E>>>2]|=(128|y>>>6&63)<<I[E++&3],C[E>>>2]|=(128|y&63)<<I[E++&3]):(y=65536+((y&1023)<<10|u.charCodeAt(++k)&1023),C[E>>>2]|=(240|y>>>18)<<I[E++&3],C[E>>>2]|=(128|y>>>12&63)<<I[E++&3],C[E>>>2]|=(128|y>>>6&63)<<I[E++&3],C[E>>>2]|=(128|y&63)<<I[E++&3]);else for(E=this.start;k<_&&E<64;++k)C[E>>>2]|=u[k]<<I[E++&3];this.lastByteIndex=E,this.bytes+=E-this.start,E>=64?(this.block=C[16],this.start=E-64,this.hash(),this.hashed=!0):this.start=E}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},M.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var u=this.blocks,w=this.lastByteIndex;u[16]=this.block,u[w>>>2]|=S[w&3],this.block=u[16],w>=56&&(this.hashed||this.hash(),u[0]=this.block,u[16]=u[1]=u[2]=u[3]=u[4]=u[5]=u[6]=u[7]=u[8]=u[9]=u[10]=u[11]=u[12]=u[13]=u[14]=u[15]=0),u[14]=this.hBytes<<3|this.bytes>>>29,u[15]=this.bytes<<3,this.hash()}},M.prototype.hash=function(){var u=this.h0,w=this.h1,x=this.h2,y=this.h3,k=this.h4,E,_,C,L=this.blocks;for(_=16;_<80;++_)C=L[_-3]^L[_-8]^L[_-14]^L[_-16],L[_]=C<<1|C>>>31;for(_=0;_<20;_+=5)E=w&x|~w&y,C=u<<5|u>>>27,k=C+E+k+1518500249+L[_]<<0,w=w<<30|w>>>2,E=u&w|~u&x,C=k<<5|k>>>27,y=C+E+y+1518500249+L[_+1]<<0,u=u<<30|u>>>2,E=k&u|~k&w,C=y<<5|y>>>27,x=C+E+x+1518500249+L[_+2]<<0,k=k<<30|k>>>2,E=y&k|~y&u,C=x<<5|x>>>27,w=C+E+w+1518500249+L[_+3]<<0,y=y<<30|y>>>2,E=x&y|~x&k,C=w<<5|w>>>27,u=C+E+u+1518500249+L[_+4]<<0,x=x<<30|x>>>2;for(;_<40;_+=5)E=w^x^y,C=u<<5|u>>>27,k=C+E+k+1859775393+L[_]<<0,w=w<<30|w>>>2,E=u^w^x,C=k<<5|k>>>27,y=C+E+y+1859775393+L[_+1]<<0,u=u<<30|u>>>2,E=k^u^w,C=y<<5|y>>>27,x=C+E+x+1859775393+L[_+2]<<0,k=k<<30|k>>>2,E=y^k^u,C=x<<5|x>>>27,w=C+E+w+1859775393+L[_+3]<<0,y=y<<30|y>>>2,E=x^y^k,C=w<<5|w>>>27,u=C+E+u+1859775393+L[_+4]<<0,x=x<<30|x>>>2;for(;_<60;_+=5)E=w&x|w&y|x&y,C=u<<5|u>>>27,k=C+E+k-1894007588+L[_]<<0,w=w<<30|w>>>2,E=u&w|u&x|w&x,C=k<<5|k>>>27,y=C+E+y-1894007588+L[_+1]<<0,u=u<<30|u>>>2,E=k&u|k&w|u&w,C=y<<5|y>>>27,x=C+E+x-1894007588+L[_+2]<<0,k=k<<30|k>>>2,E=y&k|y&u|k&u,C=x<<5|x>>>27,w=C+E+w-1894007588+L[_+3]<<0,y=y<<30|y>>>2,E=x&y|x&k|y&k,C=w<<5|w>>>27,u=C+E+u-1894007588+L[_+4]<<0,x=x<<30|x>>>2;for(;_<80;_+=5)E=w^x^y,C=u<<5|u>>>27,k=C+E+k-899497514+L[_]<<0,w=w<<30|w>>>2,E=u^w^x,C=k<<5|k>>>27,y=C+E+y-899497514+L[_+1]<<0,u=u<<30|u>>>2,E=k^u^w,C=y<<5|y>>>27,x=C+E+x-899497514+L[_+2]<<0,k=k<<30|k>>>2,E=y^k^u,C=x<<5|x>>>27,w=C+E+w-899497514+L[_+3]<<0,y=y<<30|y>>>2,E=x^y^k,C=w<<5|w>>>27,u=C+E+u-899497514+L[_+4]<<0,x=x<<30|x>>>2;this.h0=this.h0+u<<0,this.h1=this.h1+w<<0,this.h2=this.h2+x<<0,this.h3=this.h3+y<<0,this.h4=this.h4+k<<0},M.prototype.hex=function(){this.finalize();var u=this.h0,w=this.h1,x=this.h2,y=this.h3,k=this.h4;return b[u>>>28&15]+b[u>>>24&15]+b[u>>>20&15]+b[u>>>16&15]+b[u>>>12&15]+b[u>>>8&15]+b[u>>>4&15]+b[u&15]+b[w>>>28&15]+b[w>>>24&15]+b[w>>>20&15]+b[w>>>16&15]+b[w>>>12&15]+b[w>>>8&15]+b[w>>>4&15]+b[w&15]+b[x>>>28&15]+b[x>>>24&15]+b[x>>>20&15]+b[x>>>16&15]+b[x>>>12&15]+b[x>>>8&15]+b[x>>>4&15]+b[x&15]+b[y>>>28&15]+b[y>>>24&15]+b[y>>>20&15]+b[y>>>16&15]+b[y>>>12&15]+b[y>>>8&15]+b[y>>>4&15]+b[y&15]+b[k>>>28&15]+b[k>>>24&15]+b[k>>>20&15]+b[k>>>16&15]+b[k>>>12&15]+b[k>>>8&15]+b[k>>>4&15]+b[k&15]},M.prototype.toString=M.prototype.hex,M.prototype.digest=function(){this.finalize();var u=this.h0,w=this.h1,x=this.h2,y=this.h3,k=this.h4;return[u>>>24&255,u>>>16&255,u>>>8&255,u&255,w>>>24&255,w>>>16&255,w>>>8&255,w&255,x>>>24&255,x>>>16&255,x>>>8&255,x&255,y>>>24&255,y>>>16&255,y>>>8&255,y&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255]},M.prototype.array=M.prototype.digest,M.prototype.arrayBuffer=function(){this.finalize();var u=new ArrayBuffer(20),w=new DataView(u);return w.setUint32(0,this.h0),w.setUint32(4,this.h1),w.setUint32(8,this.h2),w.setUint32(12,this.h3),w.setUint32(16,this.h4),u};function K(u,w){var x,y=z(u);if(u=y[0],y[1]){var k=[],E=u.length,_=0,C;for(x=0;x<E;++x)C=u.charCodeAt(x),C<128?k[_++]=C:C<2048?(k[_++]=192|C>>>6,k[_++]=128|C&63):C<55296||C>=57344?(k[_++]=224|C>>>12,k[_++]=128|C>>>6&63,k[_++]=128|C&63):(C=65536+((C&1023)<<10|u.charCodeAt(++x)&1023),k[_++]=240|C>>>18,k[_++]=128|C>>>12&63,k[_++]=128|C>>>6&63,k[_++]=128|C&63);u=k}u.length>64&&(u=new M(!0).update(u).array());var L=[],te=[];for(x=0;x<64;++x){var le=u[x]||0;L[x]=92^le,te[x]=54^le}M.call(this,w),this.update(te),this.oKeyPad=L,this.inner=!0,this.sharedMemory=w}K.prototype=new M,K.prototype.finalize=function(){if(M.prototype.finalize.call(this),this.inner){this.inner=!1;var u=this.array();M.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(u),M.prototype.finalize.call(this)}};var X=P();X.sha1=X,X.sha1.hmac=Y(),T?e.exports=X:l.sha1=X})()})(Es);var on=Es.exports;const rn=tn(on);function os(){return"http://worksheet.hexinedu.com"}function ct(){return"http://127.0.0.1:3000"}function ln(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){const s=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(n=="x"?s:s&3|8).toString(16)})}const ut=async(e,n,s,a={},l=8e3)=>{try{return await Promise.race([e(),new Promise((v,m)=>setTimeout(()=>m(new Error("WebSocket请求超时，切换到HTTP")),l))])}catch{try{let m;return n==="get"?m=await xt.get(s,{params:a}):n==="post"?m=await xt.post(s,a):n==="delete"&&(m=await xt.delete(s)),m.data}catch(m){throw new Error(`请求失败: ${m.message||"未知错误"}`)}}};function cn(e,n,s,a){const l=[e,n,s,a].join(":");return rn(l)}function un(){const e=ne(""),n=ne(""),s=ne(""),a=mt({}),l=ne(""),v=ne("");let m="",T=null;const g=ne("c:\\Temp"),b=mt({appKey:"",appSecret:""}),S=mt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),I=ne(""),$=ne("junior"),D=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],H=[{value:"junior",label:"初中"}],R=async()=>{try{const t=await W.getWatcherStatus();t.data&&t.data.watchDir&&(g.value=t.data.watchDir,s.value+=`<span class="log-item info">已获取监控目录: ${g.value}</span><br/>`)}catch(t){s.value+=`<span class="log-item error">获取监控目录失败: ${t.message}</span><br/>`,console.error("获取监控目录失败:",t)}},z=async()=>{var t,o,r;try{if(!b.appKey||!b.appSecret)throw new Error("未初始化app信息");const d=60,i=Date.now();let h;try{const J=window.Application.PluginStorage.getItem("token_info");J&&(h=JSON.parse(J))}catch(J){h=null,s.value+=`<span class="log-item warning">解析缓存token失败: ${J.message}</span><br/>`}if(h&&h.access_token&&h.expired_time>i+d*1e3)return h.access_token;const c=b.appKey,O="1234567",U=Math.floor(Date.now()/1e3),j=cn(c,O,b.appSecret,U),B=await xt.get(os()+"/api/open/account/v1/auth/token",{params:{app_key:c,app_nonstr:O,app_timestamp:U,app_signature:j}});if((o=(t=B.data)==null?void 0:t.data)!=null&&o.access_token){const J=B.data.data.access_token;let se;if(B.data.data.expired_time){const re=parseInt(B.data.data.expired_time);se=i+re*1e3,s.value+=`<span class="log-item info">token已更新，有效期${re}秒</span><br/>`}else se=i+3600*1e3,s.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const de={access_token:J,expired_time:se};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(de))}catch(re){s.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${re.message}</span><br/>`}return J}else throw new Error(((r=B.data)==null?void 0:r.message)||"获取access_token失败")}catch(d){throw s.value+=`<span class="log-item error">获取access_token失败: ${d.message}</span><br/>`,d}},q=()=>{s.value='<span class="log-item info">日志已清空</span><br/>'},P=()=>{const t=window.Application,o=t.Documents.Count;for(let r=1;r<=o;r++){const d=t.Documents.Item(r);if(d.DocID===l.value)return d}return null},Z=()=>{try{const t=P();if(!t)return{isValid:!1,message:"未找到当前文档"};let o="";try{o=t.Name||""}catch{try{o=u("getDocName")||""}catch{o=""}}if(o){const r=o.toLowerCase();return r.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:r.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(t){return console.error("检查文档格式时出错:",t),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},f=t=>t===1?"status-running":t===2?"status-completed":t===-1?"status-error":t===3?"status-released":t===4?"status-stopped":"",Y=t=>t===1?"进行中":t===2?"已完成":t===-1?"异常":t===3?"已释放":t===4?"已停止":"进行中",M=t=>{const o=Date.now()-t,r=Math.floor(o/1e3);return r<60?`${r}秒`:r<3600?`${Math.floor(r/60)}分${r%60}秒`:`${Math.floor(r/3600)}时${Math.floor(r%3600/60)}分`},K=async t=>{try{if(!a[t]){s.value+=`<span class="log-item error">找不到任务${t.substring(0,8)}的数据</span><br/>`;return}a[t].status=4,a[t].terminated=!0,a[t].errorMessage="用户选择不继续";try{const r=P();if(r&&r.ContentControls)for(let d=1;d<=r.ContentControls.Count;d++)try{const i=r.ContentControls.Item(d);if(i&&i.Title&&(i.Title===`任务_${t}`||i.Title===`任务增强_${t}`)){const h=i.Title===`任务增强_${t}`||a[t].isEnhanced;i.Title=h?`已停止增强_${t}`:`已停止_${t}`,s.value+=`<span class="log-item info">已将${h?"增强":"普通"}任务${t.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(r){s.value+=`<span class="log-item warning">更新控件标题失败: ${r.message}</span><br/>`}let o=null;if(F[t]&&F[t].urlId){o=F[t].urlId;try{try{const r=await ut(async()=>await W.getUrlMonitorStatus(),"get",`${ct()}/api/url/status`),i=(Array.isArray(r)?r:r.data?Array.isArray(r.data)?r.data:[]:[]).find(h=>h.urlId===o);i&&i.downloadedPath&&(a[t].resultFile=i.downloadedPath,a[t].resultDownloaded=!0,s.value+=`<span class="log-item success">发现任务${t.substring(0,8)}已下载文件: ${i.downloadedPath}</span><br/>`)}catch(r){s.value+=`<span class="log-item warning">检查URL下载状态出错: ${r.message}</span><br/>`}s.value+=`<span class="log-item info">停止任务${t.substring(0,8)}的URL监控</span><br/>`,await oe(o),delete F[t]}catch(r){s.value+=`<span class="log-item warning">停止URL监控出错: ${r.message}，将重试</span><br/>`,delete F[t],setTimeout(async()=>{try{o&&await ut(async()=>await W.stopUrlMonitoring(o),"delete",`${ct()}/api/url/monitor/${o}`)}catch(d){s.value+=`<span class="log-item warning">重试停止URL监控失败: ${d.message}</span><br/>`}},1e3)}}s.value+=`<span class="log-item success">任务${t.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(o){s.value+=`<span class="log-item error">停止任务${t.substring(0,8)}出错: ${o.message}</span><br/>`,F[t]&&delete F[t]}},X=async t=>{if(!a[t]){s.value+=`<span class="log-item error">找不到任务${t.substring(0,8)}的数据</span><br/>`;return}a[t].status=4,a[t].terminated=!0,a[t].errorMessage="用户手动终止";const o=P();if(o&&o.ContentControls)for(let d=1;d<=o.ContentControls.Count;d++)try{const i=o.ContentControls.Item(d);if(i&&i.Title&&(i.Title===`任务_${t}`||i.Title===`任务增强_${t}`)){const h=i.Title===`任务增强_${t}`||a[t].isEnhanced;i.Title=h?`已停止增强_${t}`:`已停止_${t}`,i.LockContents=!1,s.value+=`<span class="log-item info">已将${h?"增强":"普通"}任务${t.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let r=null;if(F[t]&&F[t].urlId){r=F[t].urlId;try{const d=await W.getUrlMonitorStatus(),h=(Array.isArray(d)?d:d.data?Array.isArray(d.data)?d.data:[]:[]).find(c=>c.urlId===r);h&&h.downloadedPath&&(a[t].resultFile=h.downloadedPath,a[t].resultDownloaded=!0,s.value+=`<span class="log-item success">发现任务${t.substring(0,8)}已下载文件: ${h.downloadedPath}</span><br/>`),s.value+=`<span class="log-item info">停止任务${t.substring(0,8)}的URL监控</span><br/>`,await oe(r),delete F[t]}catch(d){s.value+=`<span class="log-item warning">停止URL监控出错: ${d.message}，将重试</span><br/>`,delete F[t]}}},u=t=>Qs.onbuttonclick(t),w=()=>{try{e.value=u("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},x=()=>{P().ActiveWindow.Selection.Copy()},y=t=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${g.value}\\${t}`,12,"","",!1),o.Close(),P().ActiveWindow.Activate()},k=t=>{const o=window.Application.Documents.Add("",!1,0,!1);o.Content.Paste(),o.SaveAs2(`${g.value}\\${t}`,12,"","",!1),o.Close()},E=t=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${g.value}\\${t}`,12,"","",!1),P().ActiveWindow.Activate()},_=async t=>{try{s.value+=`<span class="log-item info">开始生成文档: ${t}</span><br/>`;let o="method2";try{const r=await W.getSaveMethod();if(r.success&&r.saveMethod){o=r.saveMethod;const d=o==="method1"?"方式一":o==="method2"?"方式二":"方式三";s.value+=`<span class="log-item info">使用保存方式: ${d}</span><br/>`}}catch(r){s.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${r.message}</span><br/>`}o==="method1"?(y(t),s.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${g.value}\\${t}.docx</span><br/>`):o==="method2"?(k(t),s.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${g.value}\\${t}.docx</span><br/>`):o==="method3"&&(E(t),s.value+=`<span class="log-item success">文件已通过方式三保存到监控目录: ${g.value}\\${t}.docx</span><br/>`),W.associateFileWithClient(`${t}.docx`).then(r=>{r.success?s.value+=`<span class="log-item info">文件 ${t}.docx 已关联到当前客户端</span><br/>`:s.value+=`<span class="log-item warning">关联文件失败: ${r.message||"未知错误"}</span><br/>`}).catch(r=>{s.value+=`<span class="log-item warning">关联文件时出错: ${r.message}</span><br/>`})}catch(o){s.value+=`<span class="log-item error">保存文件失败: ${o.message}</span><br/>`}},C=ne(null),L=mt([]),te=new Set,le=t=>!t||typeof t!="string"?"":t.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),fe=async t=>{try{const o=t.slice(m.length);if(o.trim()){const r=W.getClientId();if(!r){console.warn("无法获取客户端ID，跳过日志同步");return}const d=le(o);if(!d.trim()){m=t;return}await W.sendRequest("logger","syncLog",{content:d,timestamp:new Date().toISOString(),clientId:r}),m=t}}catch(o){console.error("同步日志到服务端失败:",o)}},xe=()=>{W.connect().then(()=>{R()}).catch(o=>{s.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${o.message}</span><br/>`});const t=()=>{s.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const o=[];for(const r in a)if(a.hasOwnProperty(r)){const d=a[r];d.status===1&&!d.terminated&&(o.includes(r)||o.push(r))}if(o.length>0){let r=!1;try{const d=P();if(d){const h=`taskpane_id_${d.DocID}`,c=window.Application.PluginStorage.getItem(h);if(c){const O=window.Application.GetTaskPane(c);O&&(r=O.Visible)}}}catch(d){s.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${d.message}</span><br/>`,r=!0}setTimeout(()=>{if(r){const d=`检测到 ${o.length} 个未完成的任务，是否继续？`;at(d).then(i=>{i?(s.value+=`<span class="log-item info">用户选择继续 ${o.length} 个进行中的任务 (by taskId)...</span><br/>`,W.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:o}).then(h=>{h&&h.success?s.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${h.message||""}</span><br/>`:s.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(h==null?void 0:h.message)||"未知错误"}</span><br/>`}).catch(h=>{s.value+=`<span class="log-item error">请求恢复任务出错: ${h.message}</span><br/>`})):(s.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',o.forEach(h=>{K(h)}),s.value+=`<span class="log-item success">${o.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(i=>{s.value+=`<span class="log-item error">弹窗错误: ${i.message}，默认停止任务（保留控件）</span><br/>`,o.forEach(h=>{K(h)})})}else s.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};W.setConnectionSuccessHandler(t),W.isConnected&&(s.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',t()),W.addEventListener("connection",o=>{o.status==="disconnected"&&(s.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${o.reason||"未知"}, 代码: ${o.code||"N/A"}，将自动重连</span><br/>`)}),W.addEventListener("watcher",o=>{var r,d;if(L.push(o),o.type,o.eventType==="uploadSuccess"){const i=(r=o.data)==null?void 0:r.file,h=i==null?void 0:i.replace(/\.docx$/,""),c=`${o.eventType}_${i}_${Date.now()}`;if(te.has(c)){s.value+=`<span class="log-item warning">忽略重复的上传事件: ${i}</span><br/>`;return}if(te.add(c),te.size>100){const U=te.values();te.delete(U.next().value)}const O=h&&((d=a[h])==null?void 0:d.wordType);Q(o,O)}else o.eventType&&Q(o,null)}),W.addEventListener("urlMonitor",o=>{L.push(o),o.type==="urlMonitor"&&(s.value+=`<span class="log-item info">收到URL监控事件: ${o.eventType||o.action}</span><br/>`),o.eventType&&Q(o,null)}),W.addEventListener("health",o=>{}),W.addEventListener("error",o=>{const r=o.error||"未知错误";s.value+=`<span class="log-item error">WebSocket错误: ${r}</span><br/>`,console.error("WebSocket错误:",o)})},F=mt({}),me=async(t,o,r=!1,d=5e3,i={})=>{try{s.value+=`<span class="log-item info">开始监控URL: ${t}</span><br/>`;const h=await W.startUrlMonitoring(t,d,{downloadOnSuccess:i.downloadOnSuccess!==void 0?i.downloadOnSuccess:!0,appKey:i.appKey,filename:i.filename,taskId:o});s.value+=`<span class="log-item info">URL监控请求数据: ${JSON.stringify(h)}</span><br/>`;const c=h.success||(h==null?void 0:h.success),O=h.urlId||(h==null?void 0:h.urlId);if(c&&O){F[o]={urlId:O,url:t,isResultUrl:r,startTime:Date.now()},s.value+=`<span class="log-item success">URL监控已启动，ID: ${O}</span><br/>`;try{await W.startUrlChecking(O)}catch{}return O}else throw new Error("服务器返回失败")}catch(h){return s.value+=`<span class="log-item error">启动URL监控失败: ${h.message}</span><br/>`,null}},oe=async t=>{if(!t)return s.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(F).forEach(r=>{F[r].urlId===t&&delete F[r]}),s.value+=`<span class="log-item info">正在停止URL监控: ${t}</span><br/>`;const o=await ut(async()=>await W.stopUrlMonitoring(t),"delete",`${ct()}/api/url/monitor/${t}`);return o&&(o.success||o!=null&&o.success)?(s.value+=`<span class="log-item success">已停止URL监控: ${t}</span><br/>`,!0):(s.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(o){s.value+=`<span class="log-item warning">停止URL监控API调用失败: ${o.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await ut(async()=>await W.stopUrlMonitoring(t),"delete",`${ct()}/api/url/monitor/${t}`)}catch{}},1e3)}catch{}return!0}},ce=async()=>{try{const t=await ut(async()=>await W.getUrlMonitorStatus(),"get",`${ct()}/api/url/status`);return t.data||t}catch(t){return s.value+=`<span class="log-item error">获取URL监控状态失败: ${t.message}</span><br/>`,[]}},be=async t=>{try{return await W.forceUrlCheck(t)}catch{return!1}},Q=async(t,o)=>{var r;if(t.eventType==="uploadSuccess"){const d=t.data.file,i=d.replace(/\.docx$/,"");if(a[i]){if(a[i].terminated){s.value+=`<span class="log-item info">忽略已终止任务 ${i.substring(0,8)} 的上传通知</span><br/>`;return}if(a[i].uploadSuccess){s.value+=`<span class="log-item warning">任务 ${i.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}s.value+=`<span class="log-item success">收到文件 ${d} 上传成功通知</span><br/>`,s.value+=`<span class="log-item info">使用 wordType: ${o||a[i].wordType||"wps-analysis"}</span><br/>`,a[i].status=1,a[i].uploadSuccess=!0,await Ce(i,o||a[i].wordType||"wps-analysis")}}else if(t.eventType!=="urlMonitorUpdate")if(t.eventType==="urlMonitorStopped"){const{urlId:d,url:i,taskId:h,downloadedPath:c}=t.data,O=Object.keys(F).filter(U=>F[U].urlId===d);O.length>0&&O.forEach(U=>{c&&a[U]&&(a[U].resultFile=c,a[U].resultDownloaded=!0),delete F[U]})}else if(t.eventType==="urlFileDownloaded"){const{urlId:d,url:i,filePath:h,taskId:c}=t.data;if(!Object.values(F).some(U=>U.urlId===d)&&c&&((r=a[c])!=null&&r.terminated))return;if(c&&a[c]&&a[c].terminated){if(s.value+=`<span class="log-item info">忽略已终止任务 ${d} 的文件下载通知</span><br/>`,d)try{await W.stopUrlMonitoring(d),F[c]&&delete F[c]}catch{}return}if(c&&a[c]){s.value+=`<span class="log-item success">收到结果文件通知: ${h}</span><br/>`,a[c].resultFile=h,a[c].resultDownloaded=!0;const U=M(a[c].startTime);s.value+=`<span class="log-item success">任务${c.substring(0,8)}完成，总耗时${U}</span><br/>`,await we(c),F[c]&&F[c].urlId&&(oe(F[c].urlId),s.value+='<span class="log-item info">已停止URL监控</span><br/>',delete F[c])}else if(d){s.value+=`<span class="log-item info">URL文件已下载: ${h}</span><br/>`;const U=Object.keys(F).filter(j=>{var B;return F[j].urlId===d&&!((B=a[j])!=null&&B.terminated)});U.length>0&&U.forEach(j=>{if(a[j]&&(s.value+=`<span class="log-item info">关联到任务: ${j.substring(0,8)}</span><br/>`,a[j].resultFile=h,a[j].resultDownloaded=!0,a[j].status===1)){a[j].status=2;const B=M(a[j].startTime);s.value+=`<span class="log-item success">任务${j.substring(0,8)}完成，总耗时${B}</span><br/>`}})}}else if(t.eventType==="urlFileDownloadError"){const{urlId:d,url:i,error:h,taskId:c}=t.data;if(c&&a[c]&&a[c].terminated){s.value+=`<span class="log-item info">忽略已终止任务 ${c.substring(0,8)} 的下载失败通知</span><br/>`;return}if(s.value+=`<span class="log-item error">下载URL文件失败: ${h}</span><br/>`,c&&a[c]&&(a[c].status=-1,a[c].errorMessage=`下载失败: ${h}`,ie(c),F[c]&&delete F[c]),d)try{s.value+=`<span class="log-item info">尝试停止URL监控: ${d}</span><br/>`,await ut(async()=>await W.stopUrlMonitoring(d),"delete",`${ct()}/api/url/monitor/${d}`)}catch{}}else t.eventType==="resumeUrlMonitors"&&console.log(t.data)},Ce=async(t,o="wps-analysis")=>{try{if(!b.appKey)throw new Error("未初始化appKey信息");const r=await z(),d=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${t}.docx`,i=await xt.post(os()+"/api/open/ticket/v1/ai_comment/create",{access_token:r,data:{app_key:b.appKey,subject:I.value,stage:$.value,file_name:`${t}`,word_url:d,word_type:o,is_ai_auto:!0,is_ai_edit:!0,create_user_id:b.userId,create_username:b.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),h=i.data.data.ticket_id;if(!h)return a[t]&&(a[t].status=-1,a[t].errorMessage="无法获取ticket_id",ie(t)),!1;s.value+=`<span class="log-item info">获取到ticket_id: ${h}，开始监控结果文件</span><br/>`;const c=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${h}.wps.docx`,O={downloadOnSuccess:!0,filename:`${h}.wps.docx`,appKey:b.appKey,ticketId:h,taskId:t},U=await me(c,t,!0,3e3,O);return a[t]&&(a[t].ticketId=h,a[t].resultUrl=c,a[t].urlMonitorId=U,a[t].status=1),i.data}catch(r){return s.value+=`<span class="log-item error">API调用失败: ${r.message}</span><br/>`,a[t]&&(a[t].status=-1,a[t].errorMessage=`API调用失败: ${r.message}`,ie(t)),!1}},De=async(t,o="wps-analysis")=>new Promise((r,d)=>{try{s.value+=`<span class="log-item info">监控目录: ${g.value}</span><br/>`,s.value+=`<span class="log-item info">使用文档类型: ${o}</span><br/>`,a[t]&&(a[t].status=0,a[t].wordType=o,a[t].uploadSuccess=!1),s.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const i=1e3,h=30;let c=0;const O=setInterval(()=>{if(c++,a[t]&&a[t].uploadSuccess){clearInterval(O),r(!0);return}c>=h&&(clearInterval(O),s.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',d(new Error("上传超时，未收到完成通知")))},i)}catch(i){s.value+=`<span class="log-item error">任务处理异常: ${i.message}</span><br/>`,a[t]&&(a[t].status=-1,a[t].errorMessage=`任务处理异常: ${i.message}`),ie(t),d(i)}}),Te=async()=>{var r;s.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const t=window.wps,o=t.ActiveDocument;if(l.value=o.DocID,v.value=t.ActiveWindow.Index,o!=null&&o.ContentControls)for(let d=1;d<=o.ContentControls.Count;d++){const i=o.ContentControls.Item(d);if(i&&i.Title){let h=null,c=1,O=!1;if(i.Title.startsWith("任务增强_")?(h=i.Title.substring(5),c=1,O=!0):i.Title.startsWith("任务_")?(h=i.Title.substring(3),c=1):i.Title.startsWith("已完成增强_")?(h=i.Title.substring(6),c=2,O=!0):i.Title.startsWith("已完成_")?(h=i.Title.substring(4),c=2):i.Title.startsWith("异常增强_")?(h=i.Title.substring(5),c=-1,O=!0):i.Title.startsWith("异常_")?(h=i.Title.substring(3),c=-1):i.Title.startsWith("已停止增强_")?(h=i.Title.substring(6),c=4,O=!0):i.Title.startsWith("已停止_")&&(h=i.Title.substring(4),c=4),h&&!a[h]){let U="";try{U=((r=i.Range)==null?void 0:r.Text)||""}catch{}let j=Date.now()-24*60*60*1e3;try{if(h.length===24){const se=h.substring(0,8),de=parseInt(se,16);!isNaN(de)&&de>0&&de<2147483647&&(j=de*1e3)}else{const se=Date.now()-864e5;c===2?j=se-60*60*1e3:c===-1?j=se-30*60*1e3:c===4?j=se-45*60*1e3:j=se}}catch{}a[h]={status:c,startTime:j,contentControlId:i.ID,isEnhanced:O,selectedText:U};const B=c===1?"进行中":c===2?"已完成":c===-1?"异常":c===4?"已停止":"未知",J=O?"增强":"普通";s.value+=`<span class="log-item info">发现已有${J}任务: ${h.substring(0,8)}, 状态: ${B}</span><br/>`}}}},ie=(t,o=!1)=>{try{if(!a[t]){s.value+=`<span class="log-item warning">找不到任务${t.substring(0,8)}的记录，无法清除控件</span><br/>`;return}o&&a[t].status===1&&(a[t].status=3,a[t].errorMessage="用户主动释放");const r=P();if(!r){s.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let d=!1;const i=a[t].isEnhanced;if(r.ContentControls&&r.ContentControls.Count>0)for(let h=r.ContentControls.Count;h>=1;h--)try{const c=r.ContentControls.Item(h);c&&c.Title&&c.Title.includes(t)&&(c.LockContents=!1,c.Delete(!1),d=!0,console.log(c.Title),s.value+=`<span class="log-item success">已解锁并删除${i?"增强":"普通"}任务${t.substring(0,8)}的内容控件</span><br/>`)}catch(c){s.value+=`<span class="log-item warning">访问第${h}个控件时出错: ${c.message}</span><br/>`;continue}d?a[t].placeholderRemoved=!0:s.value+=`<span class="log-item warning">未能找到或删除${i?"增强":"普通"}任务${t.substring(0,8)}的内容控件</span><br/>`}catch(r){s.value+=`<span class="log-item error">删除内容控件失败: ${r.message}</span><br/>`}},Be=t=>{var o;try{const r=window.wps,d=P();if(!d||!d.ContentControls){s.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const i=(o=a[t])==null?void 0:o.isEnhanced;let h=null;for(let O=1;O<=d.ContentControls.Count;O++)try{const U=d.ContentControls.Item(O);if(U&&U.Title&&U.Title.includes(t)){h=U;break}}catch{continue}if(!h){const O=a[t];O&&(O.status===2||O.status===-1)?s.value+=`<span class="log-item info">任务ID: ${t.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:s.value+=`<span class="log-item error">找不到任务ID: ${t.substring(0,8)} 对应的内容控件</span><br/>`;return}h.Range.Select();const c=r.Windows.Item(v.value);c&&c.Selection&&c.Selection.Range&&c.ScrollIntoView(c.Selection.Range,!0),s.value+=`<span class="log-item success">已跳转到${i?"增强":"普通"}任务ID: ${t.substring(0,8)} 位置</span><br/>`}catch(r){s.value+=`<span class="log-item error">跳转到任务控件失败: ${r.message}</span><br/>`}},we=async t=>{var c,O,U;const o=P(),r=a[t];if(!r){s.value+=`<span class="log-item error">找不到ID为${t.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(r.terminated||r.status!==1)return;if(r.documentInserted){s.value+=`<span class="log-item info">任务${t.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const d=P();let i=null;if(d&&d.ContentControls)for(let j=1;j<=d.ContentControls.Count;j++){const B=d.ContentControls.Item(j);if(B&&B.Title&&B.Title.includes(t)){i=B;break}}if(!i){if(s.value+=`<span class="log-item error">找不到任务${t.substring(0,8)}的内容控件</span><br/>`,r.errorMessage&&r.status===1){a[t].status=-1,s.value+=`<span class="log-item error">任务${t.substring(0,8)}失败: ${r.errorMessage}</span><br/>`;return}return}if(r.errorMessage&&r.status===1){a[t].status=-1,s.value+=`<span class="log-item error">任务${t.substring(0,8)}失败: ${r.errorMessage}</span><br/>`,ie(t);return}if(!r.resultFile)return;a[t].documentInserted=!0;const h=i.Range;i.LockContents=!1;try{s.value+=`<span class="log-item info">正在自动插入结果文件${r.resultFile}...</span><br/>`;const j=o.Range(h.End,h.End);j.InsertParagraphAfter(),h.Text.includes("\v")&&j.InsertAfter("\v");let B=j.End;const J=(c=h.Tables)==null?void 0:c.Count;if(J){const pe=h.Tables.Item(J);((O=pe==null?void 0:pe.Range)==null?void 0:O.End)>B&&(pe.Range.InsertParagraphAfter(),B=(U=pe==null?void 0:pe.Range)==null?void 0:U.End)}o.Range(B,B).InsertFile(r.resultFile);for(let pe=1;pe<=d.ContentControls.Count;pe++){const ue=d.ContentControls.Item(pe);if(ue&&ue.Title&&(ue.Title===`任务_${t}`||ue.Title===`任务增强_${t}`)){i=ue;break}}const de=o.Range(i.Range.End-1,i.Range.End);de.Text.includes("\r")&&de.Delete(),a[t].status=2,F[t]&&F[t].urlId&&W.sendRequest("urlMonitor","updateTaskStatus",{urlId:F[t].urlId,status:"completed",taskId:t}).then(pe=>{s.value+=`<span class="log-item info">已通知服务端更新任务${t.substring(0,8)}状态为完成</span><br/>`}).catch(pe=>{s.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${pe.message}</span><br/>`});const Re=M(r.startTime);s.value+=`<span class="log-item success">任务${t.substring(0,8)}处理完成，总耗时${Re}</span><br/>`;const rt=i.Title===`任务增强_${t}`||r.isEnhanced;i&&(i.Title=rt?`已完成增强_${t}`:`已完成_${t}`,s.value+=`<span class="log-item info">已将${rt?"增强":"普通"}任务${t.substring(0,8)}控件标记为已完成</span><br/>`)}catch(j){a[t].documentInserted=!1,a[t].status=-1;const B=i.Title===`任务增强_${t}`||r.isEnhanced;i&&(i.Title=B?`异常增强_${t}`:`异常_${t}`,s.value+=`<span class="log-item info">已将${B?"增强":"普通"}任务${t.substring(0,8)}控件标记为异常</span><br/>`),s.value+=`<span class="log-item error">插入文档失败: ${j.message}</span><br/>`}},Je=async()=>{const t=(d=1e3)=>new Promise(i=>{setTimeout(()=>i(),d)}),o=new Map,r=Object.keys(a).filter(d=>a[d].status===1&&!a[d].terminated);for(r.length?(r.forEach(d=>{o.has(d)||o.set(d,Date.now())}),await Promise.all(r.map(d=>we(d)))):s.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await t(3e3);const d=Object.keys(a).filter(i=>a[i].status===1&&!a[i].terminated);d.forEach(i=>{o.has(i)||o.set(i,Date.now());const h=o.get(i);(Date.now()-h)/1e3/60>=5e4&&a[i]&&!a[i].terminated&&(a[i].terminated=!0,a[i].status=-1,s.value+=`<span class="log-item warning">任务 ${i} 执行超过5分钟，已自动终止</span><br/>`,o.delete(i))}),d.length&&await Promise.all(d.map(i=>we(i)))}},Ne=async(t="wps-analysis")=>{const o=Ke();if(o){const{primary:d,all:i}=o,{taskId:h,control:c,task:O,isEnhanced:U,overlapType:j}=d,B=i.filter(J=>J.task&&J.task.status===1);if(B.length>0){const J=B.map(se=>se.taskId.substring(0,8)).join(", ");return s.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${J})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}s.value+=`<span class="log-item info">发现选区与${i.length}个已有任务重叠，重叠类型: ${j}</span><br/>`,Ue();try{for(const J of i){const{taskId:se,control:de,task:re,isEnhanced:Re}=J;s.value+=`<span class="log-item info">删除重叠的${Re?"增强":"普通"}任务 ${se.substring(0,8)}，状态为 ${Y((re==null?void 0:re.status)||0)}</span><br/>`,re&&(re.status=3,re.terminated=!0,re.errorMessage="用户重新创建任务时删除");try{de.LockContents=!1,de.Delete(!1),a[se]&&(a[se].placeholderRemoved=!0)}catch(rt){s.value+=`<span class="log-item error">删除重叠任务控件失败: ${rt.message}</span><br/>`}}s.value+=`<span class="log-item success">已删除${i.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(J){return _e(),s.value+=`<span class="log-item error">删除重叠任务控件失败: ${J.message}</span><br/>`,Promise.reject(J)}_e()}const r=ln().replace(/-/g,"").substring(0,8);return new Promise(async(d,i)=>{try{const h=window.wps,c=P(),O=c.ActiveWindow.Selection,U=O.Range,j=O.Text||"";if(n.value=j,x(),U){const B=U.Paragraphs,J=B.Item(1),se=B.Item(B.Count),de=J.Range.Start,re=se.Range.End,Re=U.Start,rt=U.End,pe=c.Range(Math.min(de,Re),Math.max(re,rt));try{let ue=c.ContentControls.Add(h.Enum.wdContentControlRichText,pe);if(!ue){if(console.log("创建内容控件失败"),ue=c.ContentControls.Add(h.Enum.wdContentControlRichText),!ue){s.value+='<span class="log-item error">创建内容控件失败</span><br/>',i(new Error("创建内容控件失败"));return}U.Cut(),ue.Range.Paste()}s.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const Rt=t==="wps-enhance_analysis";ue.Title=Rt?`任务增强_${r}`:`任务_${r}`,ue.LockContents=!0,a[r]||(a[r]={}),a[r].contentControlId=ue.ID,a[r].isEnhanced=Rt,s.value+=`<span class="log-item info">已创建${Rt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const zs=ue.Range.Text;s.value+=`<span class="log-item success">控件内容验证通过，长度: ${zs.length}</span><br/>`}catch(ue){s.value+=`<span class="log-item error">创建内容控件失败: ${ue.message}</span><br/>`,i(ue);return}}await _(r),a[r]={status:1,startTime:Date.now(),wordType:t,isEnhanced:t==="wps-enhance_analysis",selectedText:j},s.value+=`<span class="log-item success">创建${t==="wps-enhanced"?"增强":"普通"}任务: ${r}，类型: ${t}</span><br/>`;try{await De(r,t)?d():(a[r]&&a[r].status===1&&(a[r].status=-1,a[r].errorMessage="API调用失败或超时",s.value+=`<span class="log-item error">任务${r.substring(0,8)}失败</span><br/>`,Ge(r)),i(new Error("API调用失败或超时")))}catch(B){a[r]&&(a[r].status=-1,a[r].errorMessage=`执行错误: ${B.message}`,s.value+=`<span class="log-item error">任务${r.substring(0,8)}执行出错: ${B.message}</span><br/>`,Ge(r)),i(B)}}catch(h){i(h)}})},Ve=async()=>{},He=()=>he()>0?"status-error":ze()>0?"status-running":qe()>0?"status-completed":"",ze=()=>Object.values(a).filter(t=>t.status===1).length,qe=()=>Object.values(a).filter(t=>t.status===2).length,he=()=>Object.values(a).filter(t=>t.status===-1).length,ke=()=>{try{s.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const t=window.wps,o=P();if(!o){s.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let r=0;if(Object.keys(a).forEach(d=>{try{a[d].status===1&&(a[d].status=3,a[d].terminated=!0,a[d].errorMessage="强制清除"),ie(d),r++}catch(i){s.value+=`<span class="log-item warning">清除任务${d.substring(0,8)}失败: ${i.message}</span><br/>`}}),o.ContentControls&&o.ContentControls.Count>0)for(let d=o.ContentControls.Count;d>=1;d--)try{const i=o.ContentControls.Item(d);if(i&&i.Title&&(i.Title.startsWith("任务_")||i.Title.startsWith("任务增强_")||i.Title.startsWith("已完成_")||i.Title.startsWith("已完成增强_")||i.Title.startsWith("异常_")||i.Title.startsWith("异常增强_")||i.Title.startsWith("已停止_")||i.Title.startsWith("已停止增强_")))try{i.LockContents=!1,i.Delete(!1);let h;i.Title.startsWith("任务增强_")?h=i.Title.substring(5):i.Title.startsWith("任务_")?h=i.Title.substring(3):i.Title.startsWith("已完成增强_")?h=i.Title.substring(6):i.Title.startsWith("已完成_")?h=i.Title.substring(4):i.Title.startsWith("异常增强_")?h=i.Title.substring(5):i.Title.startsWith("异常_")?h=i.Title.substring(3):i.Title.startsWith("已停止增强_")?h=i.Title.substring(6):i.Title.startsWith("已停止_")&&(h=i.Title.substring(4)),a[h]?(a[h].placeholderRemoved=!0,a[h].status=3):a[h]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},r++,s.value+=`<span class="log-item success">已删除任务${h.substring(0,8)}的内容控件</span><br/>`}catch(h){s.value+=`<span class="log-item error">删除控件失败: ${h.message}</span><br/>`}}catch(i){s.value+=`<span class="log-item warning">访问控件时出错: ${i.message}</span><br/>`}r>0?s.value+=`<span class="log-item success">已清除${r}个任务控件</span><br/>`:s.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(t){s.value+=`<span class="log-item error">强制清除任务控件时出错: ${t.message}</span><br/>`}},Pe=async()=>{try{const t=Object.values(F).map(o=>o.urlId);if(t.length>0){s.value+=`<span class="log-item info">清理${t.length}个URL监控任务...</span><br/>`;for(const o of t)await oe(o)}}catch(t){s.value+=`<span class="log-item error">清理URL监控任务失败: ${t.message}</span><br/>`}},Se=()=>{ks(async()=>{try{const t=window.Application.PluginStorage.getItem("user_info");if(!t)throw new Error("未找到用户信息");const o=JSON.parse(t);if(!o.orgs||!o.orgs[0])throw new Error("未找到组织信息");b.appKey=o.appKey,b.userName=o.nickname,b.userId=o.userId,b.appSecret=o.appSecret}catch(t){s.value+=`<span class="log-item error">初始化appKey失败: ${t.message}</span><br/>`}return await R(),ss([I,$],async()=>{await A()},{immediate:!1}),await ot(),s.value='<span class="log-item">已加载任务窗格...</span><br/>',w(),await Te(),xe(),Ye(),Je(),window.addEventListener("beforeunload",Pe),()=>{T&&clearTimeout(T)}}),ss(s,t=>{T&&clearTimeout(T),T=setTimeout(()=>{fe(t)},10)},{immediate:!1})},Ye=()=>{W.addEventListener("config",t=>{t.eventType==="subjectAndStageChanged"&&t.data&&(t.data.subject!==I.value&&(I.value=t.data.subject,s.value+=`<span class="log-item info">学科设置已从服务器同步: ${I.value}</span><br/>`),t.data.stage!==$.value&&($.value=t.data.stage,s.value+=`<span class="log-item info">年级设置已从服务器同步: ${$.value}</span><br/>`))})},Ie=ne(!1),Ke=()=>{try{const t=P(),o=t.ActiveWindow.Selection;if(!o||!t||!t.ContentControls)return null;const r=o.Range,d=[];for(let i=1;i<=t.ContentControls.Count;i++)try{const h=t.ContentControls.Item(i);if(!h)continue;const c=(h.Title||"").trim(),O=h.Range;if(r.Start<O.End&&r.End>O.Start){let U=null,j=!1,B=!1;if(!c)B=!0,U=`empty_${h.ID||Date.now()}`;else if(c.startsWith("任务_")||c.startsWith("任务增强_")||c.startsWith("已完成_")||c.startsWith("已完成增强_")||c.startsWith("异常_")||c.startsWith("异常增强_")||c.startsWith("已停止_")||c.startsWith("已停止增强_"))c.startsWith("任务增强_")?(U=c.substring(5),j=!0):c.startsWith("任务_")?U=c.substring(3):c.startsWith("已完成增强_")?(U=c.substring(6),j=!0):c.startsWith("已完成_")?U=c.substring(4):c.startsWith("异常增强_")?(U=c.substring(5),j=!0):c.startsWith("异常_")?U=c.substring(3):c.startsWith("已停止增强_")?(U=c.substring(6),j=!0):c.startsWith("已停止_")&&(U=c.substring(4));else continue;if(U){let J;r.Start>=O.Start&&r.End<=O.End?J="completely_within":r.Start<=O.Start&&r.End>=O.End?J="completely_contains":J="partial_overlap",d.push({taskId:U,control:h,task:B?null:a[U]||null,isEnhanced:j,isEmptyTitle:B,overlapType:J,controlRange:{start:O.Start,end:O.End},selectionRange:{start:r.Start,end:r.End}})}}}catch{continue}return d.length===0?null:{primary:d[0],all:d}}catch(t){return s.value+=`<span class="log-item error">检查选区位置出错: ${t.message}</span><br/>`,null}},Ue=()=>{Ie.value=!0},_e=()=>{Ie.value=!1},Ge=async(t,o=!1)=>{Ue();try{await ie(t,o)}finally{_e()}},at=t=>new Promise((o,r)=>{S.show=!0,S.message=t,S.resolveCallback=o,S.rejectCallback=r}),gt=t=>{S.show=!1,t&&S.resolveCallback?S.resolveCallback(!0):S.resolveCallback&&S.resolveCallback(!1),S.resolveCallback=null,S.rejectCallback=null},ot=async()=>{try{s.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const t=await W.getSubjectAndStage();t.success&&t.data?(t.data.subject&&(I.value=t.data.subject),t.data.stage&&($.value=t.data.stage),s.value+=`<span class="log-item success">已从服务器加载学科(${I.value})和年级(${$.value})设置</span><br/>`):s.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(t){console.error("从服务器加载学科和年级设置失败:",t),s.value+=`<span class="log-item error">从服务器加载设置失败: ${t.message}</span><br/>`}},A=async()=>{try{if(I.value||$.value){s.value+=`<span class="log-item info">正在保存学科(${I.value})和年级(${$.value})设置到服务器...</span><br/>`;const t=await W.setSubjectAndStage(I.value,$.value);t&&t.success?s.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':s.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(t==null?void 0:t.message)||"未知错误"}</span><br/>`}}catch(t){console.error("保存学科和年级到服务器失败:",t),s.value+=`<span class="log-item error">保存设置到服务器失败: ${t.message}</span><br/>`}};return{docName:e,selected:n,logger:s,map:a,watchedDir:g,subject:I,stage:$,subjectOptions:D,stageOptions:H,fetchWatchedDir:R,clearLog:q,getCurrentDocument:P,checkDocumentFormat:Z,getTaskStatusClass:f,getTaskStatusText:Y,getElapsedTime:M,terminateTask:X,stopTaskWithoutRemovingControl:K,run1:Ne,run2:Ve,getHeaderStatusClass:He,getRunningTasksCount:ze,getCompletedTasksCount:qe,getErrorTasksCount:he,setupLifecycle:Se,navigateToTaskControl:Be,forceCleanAllTasks:ke,ws:C,wsMessages:L,initWebSocket:xe,handleWatcherEvent:Q,urlMonitorTasks:F,monitorUrlForTask:me,stopUrlMonitoring:oe,getUrlMonitorStatus:ce,forceUrlCheck:be,cleanupUrlMonitoringTasks:Pe,tryRemoveTaskPlaceholder:ie,isLoading:Ie,isSelectionInTaskControl:Ke,tryRemoveTaskPlaceholderWithLoading:Ge,showConfirm:at,handleConfirm:gt,confirmDialog:S,loadSubjectAndStage:ot,saveSubjectAndStage:A}}const dn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:it.getVersionConfig(),selectedEdition:it.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,userNameClickCount:0,userNameClickTimer:null,showSaveMethodDialog:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),s=new Date-e,a=Math.floor(s/(1e3*60*60)),l=Math.floor(s%(1e3*60*60)/(1e3*60)),v=Math.floor(s%(1e3*60)/1e3);return`${a}小时 ${l}分 ${v}秒`},userInfo(){var n,s;const e=(s=(n=window.Application)==null?void 0:n.PluginStorage)==null?void 0:s.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await W.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await W[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await W.stopWatcher(),await new Promise(n=>setTimeout(n,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await W.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await W.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await W.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await W.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await W.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await W.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await it.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await Ys()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const n=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${n}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const s=Object.keys(e.data)[0];return s?`${s}: ${e.data[s]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await W.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,n=await W.sendRequest("config","setShowTooltip",{showTooltip:e});n.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${n.message||"未知错误"}`,console.error("Tooltip设置更新失败:",n.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},handleUserNameClick(){this.userNameClickTimer&&clearTimeout(this.userNameClickTimer),this.userNameClickCount++,this.userNameClickCount>=3?(this.showSaveMethodDialog=!0,this.userNameClickCount=0):this.userNameClickTimer=setTimeout(()=>{this.userNameClickCount=0},2e3)},async fetchSaveMethod(){try{const e=await W.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await W.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":"方式三"}`)},setupEventListeners(){this.removeWatcherListener=W.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=W.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=W.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=it.onVersionChange(e=>{this.versionConfig=it.getVersionConfig(),this.selectedEdition=it.getEdition()}),await W.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},pn={class:"file-watcher"},fn={class:"settings-modal"},hn={class:"modal-content"},vn={class:"modal-header"},gn={class:"version-tag"},mn={class:"header-actions"},bn={key:0,class:"modal-body"},wn={class:"status-section"},yn={class:"status-item"},xn={class:"status-item"},Cn={class:"directory-section"},Tn={class:"directory-form"},kn={class:"form-group"},Sn=["placeholder"],En=["disabled"],$n={class:"directory-section"},An={class:"directory-form"},Mn={class:"form-group"},Dn=["placeholder"],_n=["disabled"],On={class:"directory-section"},Pn={class:"directory-form"},In={class:"form-group"},Un=["placeholder"],Rn=["disabled"],Ln={class:"events-section"},Fn={class:"events-list"},jn={class:"event-time"},Wn={class:"event-message"},Bn={class:"modal-header"},Nn={class:"modal-body"},Vn={class:"save-method-section"},Hn={class:"radio-group"},zn={class:"radio-item"},qn={class:"radio-item"},Yn={class:"radio-item"},Kn={class:"modal-footer"},Xn={class:"modal-footer"},Jn=["disabled"];function Gn(e,n,s,a,l,v){var m,T,g,b,S,I;return N(),V("div",pn,[p("div",fn,[p("div",hn,[p("div",vn,[p("h3",null,[Ks(ee(l.versionConfig.shortName)+"设置 ",1),p("span",gn,ee(l.versionConfig.appVersion),1),v.userInfo?(N(),V("span",{key:0,class:"user-info",onClick:n[0]||(n[0]=(...$)=>v.handleUserNameClick&&v.handleUserNameClick(...$))},"欢迎您，"+ee(v.userInfo.nickname),1)):G("",!0)]),p("div",mn,[p("button",{class:"logout-btn",onClick:n[1]||(n[1]=(...$)=>v.handleLogout&&v.handleLogout(...$)),title:"退出登录"},n[24]||(n[24]=[p("span",{class:"icon-logout"},null,-1)])),p("button",{class:"close-btn",onClick:n[2]||(n[2]=()=>e.$emit("close"))},"×")])]),((g=(T=(m=v.userInfo)==null?void 0:m.orgs)==null?void 0:T[0])==null?void 0:g.orgId)===2?(N(),V("div",bn,[p("div",wn,[p("div",yn,[n[25]||(n[25]=p("span",{class:"label"},"状态：",-1)),p("span",{class:Ee(["status-badge",l.status.status])},ee(l.status.status==="running"?"运行中":"已停止"),3)]),p("div",xn,[n[26]||(n[26]=p("span",{class:"label"},"本次上传：",-1)),p("span",null,ee(l.status.processedFiles||0)+" 个文件",1)])]),G("",!0),p("div",Cn,[n[30]||(n[30]=p("h4",null,"上传目录设置",-1)),p("div",Tn,[p("div",kn,[n[29]||(n[29]=p("span",{class:"label"},"路径：",-1)),Le(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":n[5]||(n[5]=$=>l.newWatchDir=$),placeholder:l.status.watchDir||"C:\\Temp"},null,8,Sn),[[Lt,l.newWatchDir]]),p("button",{class:"action-btn",onClick:n[6]||(n[6]=(...$)=>v.updateWatchDir&&v.updateWatchDir(...$)),disabled:l.isUpdating||!l.newWatchDir},ee(l.isUpdating?"更新中...":"更新目录"),9,En)]),l.updateMessage?(N(),V("div",{key:0,class:Ee(["update-message",l.updateSuccess?"success":"error"])},ee(l.updateMessage),3)):G("",!0)])]),p("div",$n,[n[32]||(n[32]=p("h4",null,"下载目录设置",-1)),p("div",An,[p("div",Mn,[n[31]||(n[31]=p("span",{class:"label"},"路径：",-1)),Le(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":n[7]||(n[7]=$=>l.newDownloadPath=$),placeholder:l.downloadPath||"C:\\Temp\\Downloads"},null,8,Dn),[[Lt,l.newDownloadPath]]),p("button",{class:"action-btn",onClick:n[8]||(n[8]=(...$)=>v.updateDownloadPath&&v.updateDownloadPath(...$)),disabled:l.isUpdatingDownloadPath||!l.newDownloadPath},ee(l.isUpdatingDownloadPath?"更新中...":"更新路径"),9,_n)]),l.downloadPathUpdateMessage?(N(),V("div",{key:0,class:Ee(["update-message",l.downloadPathUpdateSuccess?"success":"error"])},ee(l.downloadPathUpdateMessage),3)):G("",!0)])]),p("div",On,[n[34]||(n[34]=p("h4",null,"配置目录设置",-1)),p("div",Pn,[p("div",In,[n[33]||(n[33]=p("span",{class:"label"},"路径：",-1)),Le(p("input",{type:"text",class:"directory-input","onUpdate:modelValue":n[9]||(n[9]=$=>l.newAddonConfigPath=$),placeholder:l.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,Un),[[Lt,l.newAddonConfigPath]]),p("button",{class:"action-btn",onClick:n[10]||(n[10]=(...$)=>v.updateAddonConfigPath&&v.updateAddonConfigPath(...$)),disabled:l.isUpdatingAddonConfigPath||!l.newAddonConfigPath},ee(l.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,Rn)]),l.addonConfigPathUpdateMessage?(N(),V("div",{key:0,class:Ee(["update-message",l.addonConfigPathUpdateSuccess?"success":"error"])},ee(l.addonConfigPathUpdateMessage),3)):G("",!0)])]),p("div",Ln,[n[35]||(n[35]=p("h4",null,"最近事件",-1)),p("div",Fn,[(N(!0),V(wt,null,yt(l.recentEvents,($,D)=>(N(),V("div",{key:D,class:"event-item"},[p("span",jn,ee(v.formatTime($.timestamp)),1),p("span",{class:Ee(["event-type",$.eventType])},ee(v.getEventTypeText($.eventType)),3),p("span",Wn,ee(v.getEventMessage($)),1)]))),128))])])])):G("",!0),G("",!0),l.showSaveMethodDialog?(N(),V("div",{key:2,class:"modal-overlay",onClick:n[22]||(n[22]=$=>l.showSaveMethodDialog=!1)},[p("div",{class:"modal-content save-method-modal",onClick:n[21]||(n[21]=Ze(()=>{},["stop"]))},[p("div",Bn,[n[40]||(n[40]=p("h4",null,"Debug - 保存方式设置",-1)),p("button",{class:"close-btn",onClick:n[13]||(n[13]=$=>l.showSaveMethodDialog=!1)},"×")]),p("div",Nn,[p("div",Vn,[n[44]||(n[44]=p("h5",null,"选择保存方式：",-1)),p("div",Hn,[p("label",zn,[Le(p("input",{type:"radio","onUpdate:modelValue":n[14]||(n[14]=$=>l.currentSaveMethod=$),value:"method1",onChange:n[15]||(n[15]=(...$)=>v.updateSaveMethod&&v.updateSaveMethod(...$))},null,544),[[Ft,l.currentSaveMethod]]),n[41]||(n[41]=p("span",{class:"radio-label"},"方式一",-1))]),p("label",qn,[Le(p("input",{type:"radio","onUpdate:modelValue":n[16]||(n[16]=$=>l.currentSaveMethod=$),value:"method2",onChange:n[17]||(n[17]=(...$)=>v.updateSaveMethod&&v.updateSaveMethod(...$))},null,544),[[Ft,l.currentSaveMethod]]),n[42]||(n[42]=p("span",{class:"radio-label"},"方式二 (默认)",-1))]),p("label",Yn,[Le(p("input",{type:"radio","onUpdate:modelValue":n[18]||(n[18]=$=>l.currentSaveMethod=$),value:"method3",onChange:n[19]||(n[19]=(...$)=>v.updateSaveMethod&&v.updateSaveMethod(...$))},null,544),[[Ft,l.currentSaveMethod]]),n[43]||(n[43]=p("span",{class:"radio-label"},"方式三",-1))])]),l.saveMethodUpdateMessage?(N(),V("div",{key:0,class:Ee(["update-message",l.saveMethodUpdateSuccess?"success":"error"])},ee(l.saveMethodUpdateMessage),3)):G("",!0)])]),p("div",Kn,[p("button",{class:"action-btn",onClick:n[20]||(n[20]=$=>l.showSaveMethodDialog=!1)},"关闭")])])])):G("",!0),p("div",Xn,[p("button",{class:Ee(["control-btn",l.status.status==="running"?"stop":"start"]),onClick:n[23]||(n[23]=(...$)=>v.controlService&&v.controlService(...$)),disabled:!((I=(S=(b=v.userInfo)==null?void 0:b.orgs)==null?void 0:S[0])!=null&&I.orgId)===2},ee(l.status.status==="running"?"停止服务":"启动服务"),11,Jn)])])])])}const Zn=Ss(dn,[["render",Gn],["__scopeId","data-v-135fe45d"]]);var ve="top",Ae="bottom",Me="right",ge="left",Kt="auto",$t=[ve,Ae,Me,ge],pt="start",St="end",Qn="clippingParents",$s="viewport",bt="popper",ea="reference",rs=$t.reduce(function(e,n){return e.concat([n+"-"+pt,n+"-"+St])},[]),As=[].concat($t,[Kt]).reduce(function(e,n){return e.concat([n,n+"-"+pt,n+"-"+St])},[]),ta="beforeRead",sa="read",na="afterRead",aa="beforeMain",oa="main",ra="afterMain",ia="beforeWrite",la="write",ca="afterWrite",ua=[ta,sa,na,aa,oa,ra,ia,la,ca];function We(e){return e?(e.nodeName||"").toLowerCase():null}function ye(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var n=e.ownerDocument;return n&&n.defaultView||window}return e}function nt(e){var n=ye(e).Element;return e instanceof n||e instanceof Element}function $e(e){var n=ye(e).HTMLElement;return e instanceof n||e instanceof HTMLElement}function Xt(e){if(typeof ShadowRoot>"u")return!1;var n=ye(e).ShadowRoot;return e instanceof n||e instanceof ShadowRoot}function da(e){var n=e.state;Object.keys(n.elements).forEach(function(s){var a=n.styles[s]||{},l=n.attributes[s]||{},v=n.elements[s];!$e(v)||!We(v)||(Object.assign(v.style,a),Object.keys(l).forEach(function(m){var T=l[m];T===!1?v.removeAttribute(m):v.setAttribute(m,T===!0?"":T)}))})}function pa(e){var n=e.state,s={popper:{position:n.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(n.elements.popper.style,s.popper),n.styles=s,n.elements.arrow&&Object.assign(n.elements.arrow.style,s.arrow),function(){Object.keys(n.elements).forEach(function(a){var l=n.elements[a],v=n.attributes[a]||{},m=Object.keys(n.styles.hasOwnProperty(a)?n.styles[a]:s[a]),T=m.reduce(function(g,b){return g[b]="",g},{});!$e(l)||!We(l)||(Object.assign(l.style,T),Object.keys(v).forEach(function(g){l.removeAttribute(g)}))})}}const Ms={name:"applyStyles",enabled:!0,phase:"write",fn:da,effect:pa,requires:["computeStyles"]};function je(e){return e.split("-")[0]}var tt=Math.max,Ot=Math.min,ft=Math.round;function Ht(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(n){return n.brand+"/"+n.version}).join(" "):navigator.userAgent}function Ds(){return!/^((?!chrome|android).)*safari/i.test(Ht())}function ht(e,n,s){n===void 0&&(n=!1),s===void 0&&(s=!1);var a=e.getBoundingClientRect(),l=1,v=1;n&&$e(e)&&(l=e.offsetWidth>0&&ft(a.width)/e.offsetWidth||1,v=e.offsetHeight>0&&ft(a.height)/e.offsetHeight||1);var m=nt(e)?ye(e):window,T=m.visualViewport,g=!Ds()&&s,b=(a.left+(g&&T?T.offsetLeft:0))/l,S=(a.top+(g&&T?T.offsetTop:0))/v,I=a.width/l,$=a.height/v;return{width:I,height:$,top:S,right:b+I,bottom:S+$,left:b,x:b,y:S}}function Jt(e){var n=ht(e),s=e.offsetWidth,a=e.offsetHeight;return Math.abs(n.width-s)<=1&&(s=n.width),Math.abs(n.height-a)<=1&&(a=n.height),{x:e.offsetLeft,y:e.offsetTop,width:s,height:a}}function _s(e,n){var s=n.getRootNode&&n.getRootNode();if(e.contains(n))return!0;if(s&&Xt(s)){var a=n;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function Xe(e){return ye(e).getComputedStyle(e)}function fa(e){return["table","td","th"].indexOf(We(e))>=0}function Qe(e){return((nt(e)?e.ownerDocument:e.document)||window.document).documentElement}function It(e){return We(e)==="html"?e:e.assignedSlot||e.parentNode||(Xt(e)?e.host:null)||Qe(e)}function is(e){return!$e(e)||Xe(e).position==="fixed"?null:e.offsetParent}function ha(e){var n=/firefox/i.test(Ht()),s=/Trident/i.test(Ht());if(s&&$e(e)){var a=Xe(e);if(a.position==="fixed")return null}var l=It(e);for(Xt(l)&&(l=l.host);$e(l)&&["html","body"].indexOf(We(l))<0;){var v=Xe(l);if(v.transform!=="none"||v.perspective!=="none"||v.contain==="paint"||["transform","perspective"].indexOf(v.willChange)!==-1||n&&v.willChange==="filter"||n&&v.filter&&v.filter!=="none")return l;l=l.parentNode}return null}function At(e){for(var n=ye(e),s=is(e);s&&fa(s)&&Xe(s).position==="static";)s=is(s);return s&&(We(s)==="html"||We(s)==="body"&&Xe(s).position==="static")?n:s||ha(e)||n}function Gt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ct(e,n,s){return tt(e,Ot(n,s))}function va(e,n,s){var a=Ct(e,n,s);return a>s?s:a}function Os(){return{top:0,right:0,bottom:0,left:0}}function Ps(e){return Object.assign({},Os(),e)}function Is(e,n){return n.reduce(function(s,a){return s[a]=e,s},{})}var ga=function(n,s){return n=typeof n=="function"?n(Object.assign({},s.rects,{placement:s.placement})):n,Ps(typeof n!="number"?n:Is(n,$t))};function ma(e){var n,s=e.state,a=e.name,l=e.options,v=s.elements.arrow,m=s.modifiersData.popperOffsets,T=je(s.placement),g=Gt(T),b=[ge,Me].indexOf(T)>=0,S=b?"height":"width";if(!(!v||!m)){var I=ga(l.padding,s),$=Jt(v),D=g==="y"?ve:ge,H=g==="y"?Ae:Me,R=s.rects.reference[S]+s.rects.reference[g]-m[g]-s.rects.popper[S],z=m[g]-s.rects.reference[g],q=At(v),P=q?g==="y"?q.clientHeight||0:q.clientWidth||0:0,Z=R/2-z/2,f=I[D],Y=P-$[S]-I[H],M=P/2-$[S]/2+Z,K=Ct(f,M,Y),X=g;s.modifiersData[a]=(n={},n[X]=K,n.centerOffset=K-M,n)}}function ba(e){var n=e.state,s=e.options,a=s.element,l=a===void 0?"[data-popper-arrow]":a;l!=null&&(typeof l=="string"&&(l=n.elements.popper.querySelector(l),!l)||_s(n.elements.popper,l)&&(n.elements.arrow=l))}const wa={name:"arrow",enabled:!0,phase:"main",fn:ma,effect:ba,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function vt(e){return e.split("-")[1]}var ya={top:"auto",right:"auto",bottom:"auto",left:"auto"};function xa(e,n){var s=e.x,a=e.y,l=n.devicePixelRatio||1;return{x:ft(s*l)/l||0,y:ft(a*l)/l||0}}function ls(e){var n,s=e.popper,a=e.popperRect,l=e.placement,v=e.variation,m=e.offsets,T=e.position,g=e.gpuAcceleration,b=e.adaptive,S=e.roundOffsets,I=e.isFixed,$=m.x,D=$===void 0?0:$,H=m.y,R=H===void 0?0:H,z=typeof S=="function"?S({x:D,y:R}):{x:D,y:R};D=z.x,R=z.y;var q=m.hasOwnProperty("x"),P=m.hasOwnProperty("y"),Z=ge,f=ve,Y=window;if(b){var M=At(s),K="clientHeight",X="clientWidth";if(M===ye(s)&&(M=Qe(s),Xe(M).position!=="static"&&T==="absolute"&&(K="scrollHeight",X="scrollWidth")),M=M,l===ve||(l===ge||l===Me)&&v===St){f=Ae;var u=I&&M===Y&&Y.visualViewport?Y.visualViewport.height:M[K];R-=u-a.height,R*=g?1:-1}if(l===ge||(l===ve||l===Ae)&&v===St){Z=Me;var w=I&&M===Y&&Y.visualViewport?Y.visualViewport.width:M[X];D-=w-a.width,D*=g?1:-1}}var x=Object.assign({position:T},b&&ya),y=S===!0?xa({x:D,y:R},ye(s)):{x:D,y:R};if(D=y.x,R=y.y,g){var k;return Object.assign({},x,(k={},k[f]=P?"0":"",k[Z]=q?"0":"",k.transform=(Y.devicePixelRatio||1)<=1?"translate("+D+"px, "+R+"px)":"translate3d("+D+"px, "+R+"px, 0)",k))}return Object.assign({},x,(n={},n[f]=P?R+"px":"",n[Z]=q?D+"px":"",n.transform="",n))}function Ca(e){var n=e.state,s=e.options,a=s.gpuAcceleration,l=a===void 0?!0:a,v=s.adaptive,m=v===void 0?!0:v,T=s.roundOffsets,g=T===void 0?!0:T,b={placement:je(n.placement),variation:vt(n.placement),popper:n.elements.popper,popperRect:n.rects.popper,gpuAcceleration:l,isFixed:n.options.strategy==="fixed"};n.modifiersData.popperOffsets!=null&&(n.styles.popper=Object.assign({},n.styles.popper,ls(Object.assign({},b,{offsets:n.modifiersData.popperOffsets,position:n.options.strategy,adaptive:m,roundOffsets:g})))),n.modifiersData.arrow!=null&&(n.styles.arrow=Object.assign({},n.styles.arrow,ls(Object.assign({},b,{offsets:n.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:g})))),n.attributes.popper=Object.assign({},n.attributes.popper,{"data-popper-placement":n.placement})}const Ta={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ca,data:{}};var Mt={passive:!0};function ka(e){var n=e.state,s=e.instance,a=e.options,l=a.scroll,v=l===void 0?!0:l,m=a.resize,T=m===void 0?!0:m,g=ye(n.elements.popper),b=[].concat(n.scrollParents.reference,n.scrollParents.popper);return v&&b.forEach(function(S){S.addEventListener("scroll",s.update,Mt)}),T&&g.addEventListener("resize",s.update,Mt),function(){v&&b.forEach(function(S){S.removeEventListener("scroll",s.update,Mt)}),T&&g.removeEventListener("resize",s.update,Mt)}}const Sa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ka,data:{}};var Ea={left:"right",right:"left",bottom:"top",top:"bottom"};function _t(e){return e.replace(/left|right|bottom|top/g,function(n){return Ea[n]})}var $a={start:"end",end:"start"};function cs(e){return e.replace(/start|end/g,function(n){return $a[n]})}function Zt(e){var n=ye(e),s=n.pageXOffset,a=n.pageYOffset;return{scrollLeft:s,scrollTop:a}}function Qt(e){return ht(Qe(e)).left+Zt(e).scrollLeft}function Aa(e,n){var s=ye(e),a=Qe(e),l=s.visualViewport,v=a.clientWidth,m=a.clientHeight,T=0,g=0;if(l){v=l.width,m=l.height;var b=Ds();(b||!b&&n==="fixed")&&(T=l.offsetLeft,g=l.offsetTop)}return{width:v,height:m,x:T+Qt(e),y:g}}function Ma(e){var n,s=Qe(e),a=Zt(e),l=(n=e.ownerDocument)==null?void 0:n.body,v=tt(s.scrollWidth,s.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),m=tt(s.scrollHeight,s.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),T=-a.scrollLeft+Qt(e),g=-a.scrollTop;return Xe(l||s).direction==="rtl"&&(T+=tt(s.clientWidth,l?l.clientWidth:0)-v),{width:v,height:m,x:T,y:g}}function es(e){var n=Xe(e),s=n.overflow,a=n.overflowX,l=n.overflowY;return/auto|scroll|overlay|hidden/.test(s+l+a)}function Us(e){return["html","body","#document"].indexOf(We(e))>=0?e.ownerDocument.body:$e(e)&&es(e)?e:Us(It(e))}function Tt(e,n){var s;n===void 0&&(n=[]);var a=Us(e),l=a===((s=e.ownerDocument)==null?void 0:s.body),v=ye(a),m=l?[v].concat(v.visualViewport||[],es(a)?a:[]):a,T=n.concat(m);return l?T:T.concat(Tt(It(m)))}function zt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Da(e,n){var s=ht(e,!1,n==="fixed");return s.top=s.top+e.clientTop,s.left=s.left+e.clientLeft,s.bottom=s.top+e.clientHeight,s.right=s.left+e.clientWidth,s.width=e.clientWidth,s.height=e.clientHeight,s.x=s.left,s.y=s.top,s}function us(e,n,s){return n===$s?zt(Aa(e,s)):nt(n)?Da(n,s):zt(Ma(Qe(e)))}function _a(e){var n=Tt(It(e)),s=["absolute","fixed"].indexOf(Xe(e).position)>=0,a=s&&$e(e)?At(e):e;return nt(a)?n.filter(function(l){return nt(l)&&_s(l,a)&&We(l)!=="body"}):[]}function Oa(e,n,s,a){var l=n==="clippingParents"?_a(e):[].concat(n),v=[].concat(l,[s]),m=v[0],T=v.reduce(function(g,b){var S=us(e,b,a);return g.top=tt(S.top,g.top),g.right=Ot(S.right,g.right),g.bottom=Ot(S.bottom,g.bottom),g.left=tt(S.left,g.left),g},us(e,m,a));return T.width=T.right-T.left,T.height=T.bottom-T.top,T.x=T.left,T.y=T.top,T}function Rs(e){var n=e.reference,s=e.element,a=e.placement,l=a?je(a):null,v=a?vt(a):null,m=n.x+n.width/2-s.width/2,T=n.y+n.height/2-s.height/2,g;switch(l){case ve:g={x:m,y:n.y-s.height};break;case Ae:g={x:m,y:n.y+n.height};break;case Me:g={x:n.x+n.width,y:T};break;case ge:g={x:n.x-s.width,y:T};break;default:g={x:n.x,y:n.y}}var b=l?Gt(l):null;if(b!=null){var S=b==="y"?"height":"width";switch(v){case pt:g[b]=g[b]-(n[S]/2-s[S]/2);break;case St:g[b]=g[b]+(n[S]/2-s[S]/2);break}}return g}function Et(e,n){n===void 0&&(n={});var s=n,a=s.placement,l=a===void 0?e.placement:a,v=s.strategy,m=v===void 0?e.strategy:v,T=s.boundary,g=T===void 0?Qn:T,b=s.rootBoundary,S=b===void 0?$s:b,I=s.elementContext,$=I===void 0?bt:I,D=s.altBoundary,H=D===void 0?!1:D,R=s.padding,z=R===void 0?0:R,q=Ps(typeof z!="number"?z:Is(z,$t)),P=$===bt?ea:bt,Z=e.rects.popper,f=e.elements[H?P:$],Y=Oa(nt(f)?f:f.contextElement||Qe(e.elements.popper),g,S,m),M=ht(e.elements.reference),K=Rs({reference:M,element:Z,strategy:"absolute",placement:l}),X=zt(Object.assign({},Z,K)),u=$===bt?X:M,w={top:Y.top-u.top+q.top,bottom:u.bottom-Y.bottom+q.bottom,left:Y.left-u.left+q.left,right:u.right-Y.right+q.right},x=e.modifiersData.offset;if($===bt&&x){var y=x[l];Object.keys(w).forEach(function(k){var E=[Me,Ae].indexOf(k)>=0?1:-1,_=[ve,Ae].indexOf(k)>=0?"y":"x";w[k]+=y[_]*E})}return w}function Pa(e,n){n===void 0&&(n={});var s=n,a=s.placement,l=s.boundary,v=s.rootBoundary,m=s.padding,T=s.flipVariations,g=s.allowedAutoPlacements,b=g===void 0?As:g,S=vt(a),I=S?T?rs:rs.filter(function(H){return vt(H)===S}):$t,$=I.filter(function(H){return b.indexOf(H)>=0});$.length===0&&($=I);var D=$.reduce(function(H,R){return H[R]=Et(e,{placement:R,boundary:l,rootBoundary:v,padding:m})[je(R)],H},{});return Object.keys(D).sort(function(H,R){return D[H]-D[R]})}function Ia(e){if(je(e)===Kt)return[];var n=_t(e);return[cs(e),n,cs(n)]}function Ua(e){var n=e.state,s=e.options,a=e.name;if(!n.modifiersData[a]._skip){for(var l=s.mainAxis,v=l===void 0?!0:l,m=s.altAxis,T=m===void 0?!0:m,g=s.fallbackPlacements,b=s.padding,S=s.boundary,I=s.rootBoundary,$=s.altBoundary,D=s.flipVariations,H=D===void 0?!0:D,R=s.allowedAutoPlacements,z=n.options.placement,q=je(z),P=q===z,Z=g||(P||!H?[_t(z)]:Ia(z)),f=[z].concat(Z).reduce(function(oe,ce){return oe.concat(je(ce)===Kt?Pa(n,{placement:ce,boundary:S,rootBoundary:I,padding:b,flipVariations:H,allowedAutoPlacements:R}):ce)},[]),Y=n.rects.reference,M=n.rects.popper,K=new Map,X=!0,u=f[0],w=0;w<f.length;w++){var x=f[w],y=je(x),k=vt(x)===pt,E=[ve,Ae].indexOf(y)>=0,_=E?"width":"height",C=Et(n,{placement:x,boundary:S,rootBoundary:I,altBoundary:$,padding:b}),L=E?k?Me:ge:k?Ae:ve;Y[_]>M[_]&&(L=_t(L));var te=_t(L),le=[];if(v&&le.push(C[y]<=0),T&&le.push(C[L]<=0,C[te]<=0),le.every(function(oe){return oe})){u=x,X=!1;break}K.set(x,le)}if(X)for(var fe=H?3:1,xe=function(ce){var be=f.find(function(Q){var Ce=K.get(Q);if(Ce)return Ce.slice(0,ce).every(function(De){return De})});if(be)return u=be,"break"},F=fe;F>0;F--){var me=xe(F);if(me==="break")break}n.placement!==u&&(n.modifiersData[a]._skip=!0,n.placement=u,n.reset=!0)}}const Ra={name:"flip",enabled:!0,phase:"main",fn:Ua,requiresIfExists:["offset"],data:{_skip:!1}};function ds(e,n,s){return s===void 0&&(s={x:0,y:0}),{top:e.top-n.height-s.y,right:e.right-n.width+s.x,bottom:e.bottom-n.height+s.y,left:e.left-n.width-s.x}}function ps(e){return[ve,Me,Ae,ge].some(function(n){return e[n]>=0})}function La(e){var n=e.state,s=e.name,a=n.rects.reference,l=n.rects.popper,v=n.modifiersData.preventOverflow,m=Et(n,{elementContext:"reference"}),T=Et(n,{altBoundary:!0}),g=ds(m,a),b=ds(T,l,v),S=ps(g),I=ps(b);n.modifiersData[s]={referenceClippingOffsets:g,popperEscapeOffsets:b,isReferenceHidden:S,hasPopperEscaped:I},n.attributes.popper=Object.assign({},n.attributes.popper,{"data-popper-reference-hidden":S,"data-popper-escaped":I})}const Fa={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:La};function ja(e,n,s){var a=je(e),l=[ge,ve].indexOf(a)>=0?-1:1,v=typeof s=="function"?s(Object.assign({},n,{placement:e})):s,m=v[0],T=v[1];return m=m||0,T=(T||0)*l,[ge,Me].indexOf(a)>=0?{x:T,y:m}:{x:m,y:T}}function Wa(e){var n=e.state,s=e.options,a=e.name,l=s.offset,v=l===void 0?[0,0]:l,m=As.reduce(function(S,I){return S[I]=ja(I,n.rects,v),S},{}),T=m[n.placement],g=T.x,b=T.y;n.modifiersData.popperOffsets!=null&&(n.modifiersData.popperOffsets.x+=g,n.modifiersData.popperOffsets.y+=b),n.modifiersData[a]=m}const Ba={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Wa};function Na(e){var n=e.state,s=e.name;n.modifiersData[s]=Rs({reference:n.rects.reference,element:n.rects.popper,strategy:"absolute",placement:n.placement})}const Va={name:"popperOffsets",enabled:!0,phase:"read",fn:Na,data:{}};function Ha(e){return e==="x"?"y":"x"}function za(e){var n=e.state,s=e.options,a=e.name,l=s.mainAxis,v=l===void 0?!0:l,m=s.altAxis,T=m===void 0?!1:m,g=s.boundary,b=s.rootBoundary,S=s.altBoundary,I=s.padding,$=s.tether,D=$===void 0?!0:$,H=s.tetherOffset,R=H===void 0?0:H,z=Et(n,{boundary:g,rootBoundary:b,padding:I,altBoundary:S}),q=je(n.placement),P=vt(n.placement),Z=!P,f=Gt(q),Y=Ha(f),M=n.modifiersData.popperOffsets,K=n.rects.reference,X=n.rects.popper,u=typeof R=="function"?R(Object.assign({},n.rects,{placement:n.placement})):R,w=typeof u=="number"?{mainAxis:u,altAxis:u}:Object.assign({mainAxis:0,altAxis:0},u),x=n.modifiersData.offset?n.modifiersData.offset[n.placement]:null,y={x:0,y:0};if(M){if(v){var k,E=f==="y"?ve:ge,_=f==="y"?Ae:Me,C=f==="y"?"height":"width",L=M[f],te=L+z[E],le=L-z[_],fe=D?-X[C]/2:0,xe=P===pt?K[C]:X[C],F=P===pt?-X[C]:-K[C],me=n.elements.arrow,oe=D&&me?Jt(me):{width:0,height:0},ce=n.modifiersData["arrow#persistent"]?n.modifiersData["arrow#persistent"].padding:Os(),be=ce[E],Q=ce[_],Ce=Ct(0,K[C],oe[C]),De=Z?K[C]/2-fe-Ce-be-w.mainAxis:xe-Ce-be-w.mainAxis,Te=Z?-K[C]/2+fe+Ce+Q+w.mainAxis:F+Ce+Q+w.mainAxis,ie=n.elements.arrow&&At(n.elements.arrow),Be=ie?f==="y"?ie.clientTop||0:ie.clientLeft||0:0,we=(k=x==null?void 0:x[f])!=null?k:0,Je=L+De-we-Be,Ne=L+Te-we,Ve=Ct(D?Ot(te,Je):te,L,D?tt(le,Ne):le);M[f]=Ve,y[f]=Ve-L}if(T){var He,ze=f==="x"?ve:ge,qe=f==="x"?Ae:Me,he=M[Y],ke=Y==="y"?"height":"width",Pe=he+z[ze],Se=he-z[qe],Ye=[ve,ge].indexOf(q)!==-1,Ie=(He=x==null?void 0:x[Y])!=null?He:0,Ke=Ye?Pe:he-K[ke]-X[ke]-Ie+w.altAxis,Ue=Ye?he+K[ke]+X[ke]-Ie-w.altAxis:Se,_e=D&&Ye?va(Ke,he,Ue):Ct(D?Ke:Pe,he,D?Ue:Se);M[Y]=_e,y[Y]=_e-he}n.modifiersData[a]=y}}const qa={name:"preventOverflow",enabled:!0,phase:"main",fn:za,requiresIfExists:["offset"]};function Ya(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Ka(e){return e===ye(e)||!$e(e)?Zt(e):Ya(e)}function Xa(e){var n=e.getBoundingClientRect(),s=ft(n.width)/e.offsetWidth||1,a=ft(n.height)/e.offsetHeight||1;return s!==1||a!==1}function Ja(e,n,s){s===void 0&&(s=!1);var a=$e(n),l=$e(n)&&Xa(n),v=Qe(n),m=ht(e,l,s),T={scrollLeft:0,scrollTop:0},g={x:0,y:0};return(a||!a&&!s)&&((We(n)!=="body"||es(v))&&(T=Ka(n)),$e(n)?(g=ht(n,!0),g.x+=n.clientLeft,g.y+=n.clientTop):v&&(g.x=Qt(v))),{x:m.left+T.scrollLeft-g.x,y:m.top+T.scrollTop-g.y,width:m.width,height:m.height}}function Ga(e){var n=new Map,s=new Set,a=[];e.forEach(function(v){n.set(v.name,v)});function l(v){s.add(v.name);var m=[].concat(v.requires||[],v.requiresIfExists||[]);m.forEach(function(T){if(!s.has(T)){var g=n.get(T);g&&l(g)}}),a.push(v)}return e.forEach(function(v){s.has(v.name)||l(v)}),a}function Za(e){var n=Ga(e);return ua.reduce(function(s,a){return s.concat(n.filter(function(l){return l.phase===a}))},[])}function Qa(e){var n;return function(){return n||(n=new Promise(function(s){Promise.resolve().then(function(){n=void 0,s(e())})})),n}}function eo(e){var n=e.reduce(function(s,a){var l=s[a.name];return s[a.name]=l?Object.assign({},l,a,{options:Object.assign({},l.options,a.options),data:Object.assign({},l.data,a.data)}):a,s},{});return Object.keys(n).map(function(s){return n[s]})}var fs={placement:"bottom",modifiers:[],strategy:"absolute"};function hs(){for(var e=arguments.length,n=new Array(e),s=0;s<e;s++)n[s]=arguments[s];return!n.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function to(e){e===void 0&&(e={});var n=e,s=n.defaultModifiers,a=s===void 0?[]:s,l=n.defaultOptions,v=l===void 0?fs:l;return function(T,g,b){b===void 0&&(b=v);var S={placement:"bottom",orderedModifiers:[],options:Object.assign({},fs,v),modifiersData:{},elements:{reference:T,popper:g},attributes:{},styles:{}},I=[],$=!1,D={state:S,setOptions:function(q){var P=typeof q=="function"?q(S.options):q;R(),S.options=Object.assign({},v,S.options,P),S.scrollParents={reference:nt(T)?Tt(T):T.contextElement?Tt(T.contextElement):[],popper:Tt(g)};var Z=Za(eo([].concat(a,S.options.modifiers)));return S.orderedModifiers=Z.filter(function(f){return f.enabled}),H(),D.update()},forceUpdate:function(){if(!$){var q=S.elements,P=q.reference,Z=q.popper;if(hs(P,Z)){S.rects={reference:Ja(P,At(Z),S.options.strategy==="fixed"),popper:Jt(Z)},S.reset=!1,S.placement=S.options.placement,S.orderedModifiers.forEach(function(w){return S.modifiersData[w.name]=Object.assign({},w.data)});for(var f=0;f<S.orderedModifiers.length;f++){if(S.reset===!0){S.reset=!1,f=-1;continue}var Y=S.orderedModifiers[f],M=Y.fn,K=Y.options,X=K===void 0?{}:K,u=Y.name;typeof M=="function"&&(S=M({state:S,options:X,name:u,instance:D})||S)}}}},update:Qa(function(){return new Promise(function(z){D.forceUpdate(),z(S)})}),destroy:function(){R(),$=!0}};if(!hs(T,g))return D;D.setOptions(b).then(function(z){!$&&b.onFirstUpdate&&b.onFirstUpdate(z)});function H(){S.orderedModifiers.forEach(function(z){var q=z.name,P=z.options,Z=P===void 0?{}:P,f=z.effect;if(typeof f=="function"){var Y=f({state:S,name:q,instance:D,options:Z}),M=function(){};I.push(Y||M)}})}function R(){I.forEach(function(z){return z()}),I=[]}return D}}var so=[Sa,Va,Ta,Ms,Ba,Ra,qa,wa,Fa],no=to({defaultModifiers:so}),ao="tippy-box",Ls="tippy-content",oo="tippy-backdrop",Fs="tippy-arrow",js="tippy-svg-arrow",et={passive:!0,capture:!0},Ws=function(){return document.body};function Wt(e,n,s){if(Array.isArray(e)){var a=e[n];return a??(Array.isArray(s)?s[n]:s)}return e}function ts(e,n){var s={}.toString.call(e);return s.indexOf("[object")===0&&s.indexOf(n+"]")>-1}function Bs(e,n){return typeof e=="function"?e.apply(void 0,n):e}function vs(e,n){if(n===0)return e;var s;return function(a){clearTimeout(s),s=setTimeout(function(){e(a)},n)}}function ro(e){return e.split(/\s+/).filter(Boolean)}function dt(e){return[].concat(e)}function gs(e,n){e.indexOf(n)===-1&&e.push(n)}function io(e){return e.filter(function(n,s){return e.indexOf(n)===s})}function lo(e){return e.split("-")[0]}function Pt(e){return[].slice.call(e)}function ms(e){return Object.keys(e).reduce(function(n,s){return e[s]!==void 0&&(n[s]=e[s]),n},{})}function kt(){return document.createElement("div")}function Ut(e){return["Element","Fragment"].some(function(n){return ts(e,n)})}function co(e){return ts(e,"NodeList")}function uo(e){return ts(e,"MouseEvent")}function po(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function fo(e){return Ut(e)?[e]:co(e)?Pt(e):Array.isArray(e)?e:Pt(document.querySelectorAll(e))}function Bt(e,n){e.forEach(function(s){s&&(s.style.transitionDuration=n+"ms")})}function bs(e,n){e.forEach(function(s){s&&s.setAttribute("data-state",n)})}function ho(e){var n,s=dt(e),a=s[0];return a!=null&&(n=a.ownerDocument)!=null&&n.body?a.ownerDocument:document}function vo(e,n){var s=n.clientX,a=n.clientY;return e.every(function(l){var v=l.popperRect,m=l.popperState,T=l.props,g=T.interactiveBorder,b=lo(m.placement),S=m.modifiersData.offset;if(!S)return!0;var I=b==="bottom"?S.top.y:0,$=b==="top"?S.bottom.y:0,D=b==="right"?S.left.x:0,H=b==="left"?S.right.x:0,R=v.top-a+I>g,z=a-v.bottom-$>g,q=v.left-s+D>g,P=s-v.right-H>g;return R||z||q||P})}function Nt(e,n,s){var a=n+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(l){e[a](l,s)})}function ws(e,n){for(var s=n;s;){var a;if(e.contains(s))return!0;s=s.getRootNode==null||(a=s.getRootNode())==null?void 0:a.host}return!1}var Fe={isTouch:!1},ys=0;function go(){Fe.isTouch||(Fe.isTouch=!0,window.performance&&document.addEventListener("mousemove",Ns))}function Ns(){var e=performance.now();e-ys<20&&(Fe.isTouch=!1,document.removeEventListener("mousemove",Ns)),ys=e}function mo(){var e=document.activeElement;if(po(e)){var n=e._tippy;e.blur&&!n.state.isVisible&&e.blur()}}function bo(){document.addEventListener("touchstart",go,et),window.addEventListener("blur",mo)}var wo=typeof window<"u"&&typeof document<"u",yo=wo?!!window.msCrypto:!1,xo={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Co={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Oe=Object.assign({appendTo:Ws,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},xo,Co),To=Object.keys(Oe),ko=function(n){var s=Object.keys(n);s.forEach(function(a){Oe[a]=n[a]})};function Vs(e){var n=e.plugins||[],s=n.reduce(function(a,l){var v=l.name,m=l.defaultValue;if(v){var T;a[v]=e[v]!==void 0?e[v]:(T=Oe[v])!=null?T:m}return a},{});return Object.assign({},e,s)}function So(e,n){var s=n?Object.keys(Vs(Object.assign({},Oe,{plugins:n}))):To,a=s.reduce(function(l,v){var m=(e.getAttribute("data-tippy-"+v)||"").trim();if(!m)return l;if(v==="content")l[v]=m;else try{l[v]=JSON.parse(m)}catch{l[v]=m}return l},{});return a}function xs(e,n){var s=Object.assign({},n,{content:Bs(n.content,[e])},n.ignoreAttributes?{}:So(e,n.plugins));return s.aria=Object.assign({},Oe.aria,s.aria),s.aria={expanded:s.aria.expanded==="auto"?n.interactive:s.aria.expanded,content:s.aria.content==="auto"?n.interactive?null:"describedby":s.aria.content},s}var Eo=function(){return"innerHTML"};function qt(e,n){e[Eo()]=n}function Cs(e){var n=kt();return e===!0?n.className=Fs:(n.className=js,Ut(e)?n.appendChild(e):qt(n,e)),n}function Ts(e,n){Ut(n.content)?(qt(e,""),e.appendChild(n.content)):typeof n.content!="function"&&(n.allowHTML?qt(e,n.content):e.textContent=n.content)}function Yt(e){var n=e.firstElementChild,s=Pt(n.children);return{box:n,content:s.find(function(a){return a.classList.contains(Ls)}),arrow:s.find(function(a){return a.classList.contains(Fs)||a.classList.contains(js)}),backdrop:s.find(function(a){return a.classList.contains(oo)})}}function Hs(e){var n=kt(),s=kt();s.className=ao,s.setAttribute("data-state","hidden"),s.setAttribute("tabindex","-1");var a=kt();a.className=Ls,a.setAttribute("data-state","hidden"),Ts(a,e.props),n.appendChild(s),s.appendChild(a),l(e.props,e.props);function l(v,m){var T=Yt(n),g=T.box,b=T.content,S=T.arrow;m.theme?g.setAttribute("data-theme",m.theme):g.removeAttribute("data-theme"),typeof m.animation=="string"?g.setAttribute("data-animation",m.animation):g.removeAttribute("data-animation"),m.inertia?g.setAttribute("data-inertia",""):g.removeAttribute("data-inertia"),g.style.maxWidth=typeof m.maxWidth=="number"?m.maxWidth+"px":m.maxWidth,m.role?g.setAttribute("role",m.role):g.removeAttribute("role"),(v.content!==m.content||v.allowHTML!==m.allowHTML)&&Ts(b,e.props),m.arrow?S?v.arrow!==m.arrow&&(g.removeChild(S),g.appendChild(Cs(m.arrow))):g.appendChild(Cs(m.arrow)):S&&g.removeChild(S)}return{popper:n,onUpdate:l}}Hs.$$tippy=!0;var $o=1,Dt=[],Vt=[];function Ao(e,n){var s=xs(e,Object.assign({},Oe,Vs(ms(n)))),a,l,v,m=!1,T=!1,g=!1,b=!1,S,I,$,D=[],H=vs(Je,s.interactiveDebounce),R,z=$o++,q=null,P=io(s.plugins),Z={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},f={id:z,reference:e,popper:kt(),popperInstance:q,props:s,state:Z,plugins:P,clearDelayTimeouts:Ke,setProps:Ue,setContent:_e,show:Ge,hide:at,hideWithInteractivity:gt,enable:Ye,disable:Ie,unmount:ot,destroy:A};if(!s.render)return f;var Y=s.render(f),M=Y.popper,K=Y.onUpdate;M.setAttribute("data-tippy-root",""),M.id="tippy-"+f.id,f.popper=M,e._tippy=f,M._tippy=f;var X=P.map(function(t){return t.fn(f)}),u=e.hasAttribute("aria-expanded");return ie(),fe(),L(),te("onCreate",[f]),s.showOnCreate&&Pe(),M.addEventListener("mouseenter",function(){f.props.interactive&&f.state.isVisible&&f.clearDelayTimeouts()}),M.addEventListener("mouseleave",function(){f.props.interactive&&f.props.trigger.indexOf("mouseenter")>=0&&E().addEventListener("mousemove",H)}),f;function w(){var t=f.props.touch;return Array.isArray(t)?t:[t,0]}function x(){return w()[0]==="hold"}function y(){var t;return!!((t=f.props.render)!=null&&t.$$tippy)}function k(){return R||e}function E(){var t=k().parentNode;return t?ho(t):document}function _(){return Yt(M)}function C(t){return f.state.isMounted&&!f.state.isVisible||Fe.isTouch||S&&S.type==="focus"?0:Wt(f.props.delay,t?0:1,Oe.delay)}function L(t){t===void 0&&(t=!1),M.style.pointerEvents=f.props.interactive&&!t?"":"none",M.style.zIndex=""+f.props.zIndex}function te(t,o,r){if(r===void 0&&(r=!0),X.forEach(function(i){i[t]&&i[t].apply(i,o)}),r){var d;(d=f.props)[t].apply(d,o)}}function le(){var t=f.props.aria;if(t.content){var o="aria-"+t.content,r=M.id,d=dt(f.props.triggerTarget||e);d.forEach(function(i){var h=i.getAttribute(o);if(f.state.isVisible)i.setAttribute(o,h?h+" "+r:r);else{var c=h&&h.replace(r,"").trim();c?i.setAttribute(o,c):i.removeAttribute(o)}})}}function fe(){if(!(u||!f.props.aria.expanded)){var t=dt(f.props.triggerTarget||e);t.forEach(function(o){f.props.interactive?o.setAttribute("aria-expanded",f.state.isVisible&&o===k()?"true":"false"):o.removeAttribute("aria-expanded")})}}function xe(){E().removeEventListener("mousemove",H),Dt=Dt.filter(function(t){return t!==H})}function F(t){if(!(Fe.isTouch&&(g||t.type==="mousedown"))){var o=t.composedPath&&t.composedPath()[0]||t.target;if(!(f.props.interactive&&ws(M,o))){if(dt(f.props.triggerTarget||e).some(function(r){return ws(r,o)})){if(Fe.isTouch||f.state.isVisible&&f.props.trigger.indexOf("click")>=0)return}else te("onClickOutside",[f,t]);f.props.hideOnClick===!0&&(f.clearDelayTimeouts(),f.hide(),T=!0,setTimeout(function(){T=!1}),f.state.isMounted||be())}}}function me(){g=!0}function oe(){g=!1}function ce(){var t=E();t.addEventListener("mousedown",F,!0),t.addEventListener("touchend",F,et),t.addEventListener("touchstart",oe,et),t.addEventListener("touchmove",me,et)}function be(){var t=E();t.removeEventListener("mousedown",F,!0),t.removeEventListener("touchend",F,et),t.removeEventListener("touchstart",oe,et),t.removeEventListener("touchmove",me,et)}function Q(t,o){De(t,function(){!f.state.isVisible&&M.parentNode&&M.parentNode.contains(M)&&o()})}function Ce(t,o){De(t,o)}function De(t,o){var r=_().box;function d(i){i.target===r&&(Nt(r,"remove",d),o())}if(t===0)return o();Nt(r,"remove",I),Nt(r,"add",d),I=d}function Te(t,o,r){r===void 0&&(r=!1);var d=dt(f.props.triggerTarget||e);d.forEach(function(i){i.addEventListener(t,o,r),D.push({node:i,eventType:t,handler:o,options:r})})}function ie(){x()&&(Te("touchstart",we,{passive:!0}),Te("touchend",Ne,{passive:!0})),ro(f.props.trigger).forEach(function(t){if(t!=="manual")switch(Te(t,we),t){case"mouseenter":Te("mouseleave",Ne);break;case"focus":Te(yo?"focusout":"blur",Ve);break;case"focusin":Te("focusout",Ve);break}})}function Be(){D.forEach(function(t){var o=t.node,r=t.eventType,d=t.handler,i=t.options;o.removeEventListener(r,d,i)}),D=[]}function we(t){var o,r=!1;if(!(!f.state.isEnabled||He(t)||T)){var d=((o=S)==null?void 0:o.type)==="focus";S=t,R=t.currentTarget,fe(),!f.state.isVisible&&uo(t)&&Dt.forEach(function(i){return i(t)}),t.type==="click"&&(f.props.trigger.indexOf("mouseenter")<0||m)&&f.props.hideOnClick!==!1&&f.state.isVisible?r=!0:Pe(t),t.type==="click"&&(m=!r),r&&!d&&Se(t)}}function Je(t){var o=t.target,r=k().contains(o)||M.contains(o);if(!(t.type==="mousemove"&&r)){var d=ke().concat(M).map(function(i){var h,c=i._tippy,O=(h=c.popperInstance)==null?void 0:h.state;return O?{popperRect:i.getBoundingClientRect(),popperState:O,props:s}:null}).filter(Boolean);vo(d,t)&&(xe(),Se(t))}}function Ne(t){var o=He(t)||f.props.trigger.indexOf("click")>=0&&m;if(!o){if(f.props.interactive){f.hideWithInteractivity(t);return}Se(t)}}function Ve(t){f.props.trigger.indexOf("focusin")<0&&t.target!==k()||f.props.interactive&&t.relatedTarget&&M.contains(t.relatedTarget)||Se(t)}function He(t){return Fe.isTouch?x()!==t.type.indexOf("touch")>=0:!1}function ze(){qe();var t=f.props,o=t.popperOptions,r=t.placement,d=t.offset,i=t.getReferenceClientRect,h=t.moveTransition,c=y()?Yt(M).arrow:null,O=i?{getBoundingClientRect:i,contextElement:i.contextElement||k()}:e,U={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(J){var se=J.state;if(y()){var de=_(),re=de.box;["placement","reference-hidden","escaped"].forEach(function(Re){Re==="placement"?re.setAttribute("data-placement",se.placement):se.attributes.popper["data-popper-"+Re]?re.setAttribute("data-"+Re,""):re.removeAttribute("data-"+Re)}),se.attributes.popper={}}}},j=[{name:"offset",options:{offset:d}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!h}},U];y()&&c&&j.push({name:"arrow",options:{element:c,padding:3}}),j.push.apply(j,(o==null?void 0:o.modifiers)||[]),f.popperInstance=no(O,M,Object.assign({},o,{placement:r,onFirstUpdate:$,modifiers:j}))}function qe(){f.popperInstance&&(f.popperInstance.destroy(),f.popperInstance=null)}function he(){var t=f.props.appendTo,o,r=k();f.props.interactive&&t===Ws||t==="parent"?o=r.parentNode:o=Bs(t,[r]),o.contains(M)||o.appendChild(M),f.state.isMounted=!0,ze()}function ke(){return Pt(M.querySelectorAll("[data-tippy-root]"))}function Pe(t){f.clearDelayTimeouts(),t&&te("onTrigger",[f,t]),ce();var o=C(!0),r=w(),d=r[0],i=r[1];Fe.isTouch&&d==="hold"&&i&&(o=i),o?a=setTimeout(function(){f.show()},o):f.show()}function Se(t){if(f.clearDelayTimeouts(),te("onUntrigger",[f,t]),!f.state.isVisible){be();return}if(!(f.props.trigger.indexOf("mouseenter")>=0&&f.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&m)){var o=C(!1);o?l=setTimeout(function(){f.state.isVisible&&f.hide()},o):v=requestAnimationFrame(function(){f.hide()})}}function Ye(){f.state.isEnabled=!0}function Ie(){f.hide(),f.state.isEnabled=!1}function Ke(){clearTimeout(a),clearTimeout(l),cancelAnimationFrame(v)}function Ue(t){if(!f.state.isDestroyed){te("onBeforeUpdate",[f,t]),Be();var o=f.props,r=xs(e,Object.assign({},o,ms(t),{ignoreAttributes:!0}));f.props=r,ie(),o.interactiveDebounce!==r.interactiveDebounce&&(xe(),H=vs(Je,r.interactiveDebounce)),o.triggerTarget&&!r.triggerTarget?dt(o.triggerTarget).forEach(function(d){d.removeAttribute("aria-expanded")}):r.triggerTarget&&e.removeAttribute("aria-expanded"),fe(),L(),K&&K(o,r),f.popperInstance&&(ze(),ke().forEach(function(d){requestAnimationFrame(d._tippy.popperInstance.forceUpdate)})),te("onAfterUpdate",[f,t])}}function _e(t){f.setProps({content:t})}function Ge(){var t=f.state.isVisible,o=f.state.isDestroyed,r=!f.state.isEnabled,d=Fe.isTouch&&!f.props.touch,i=Wt(f.props.duration,0,Oe.duration);if(!(t||o||r||d)&&!k().hasAttribute("disabled")&&(te("onShow",[f],!1),f.props.onShow(f)!==!1)){if(f.state.isVisible=!0,y()&&(M.style.visibility="visible"),L(),ce(),f.state.isMounted||(M.style.transition="none"),y()){var h=_(),c=h.box,O=h.content;Bt([c,O],0)}$=function(){var j;if(!(!f.state.isVisible||b)){if(b=!0,M.offsetHeight,M.style.transition=f.props.moveTransition,y()&&f.props.animation){var B=_(),J=B.box,se=B.content;Bt([J,se],i),bs([J,se],"visible")}le(),fe(),gs(Vt,f),(j=f.popperInstance)==null||j.forceUpdate(),te("onMount",[f]),f.props.animation&&y()&&Ce(i,function(){f.state.isShown=!0,te("onShown",[f])})}},he()}}function at(){var t=!f.state.isVisible,o=f.state.isDestroyed,r=!f.state.isEnabled,d=Wt(f.props.duration,1,Oe.duration);if(!(t||o||r)&&(te("onHide",[f],!1),f.props.onHide(f)!==!1)){if(f.state.isVisible=!1,f.state.isShown=!1,b=!1,m=!1,y()&&(M.style.visibility="hidden"),xe(),be(),L(!0),y()){var i=_(),h=i.box,c=i.content;f.props.animation&&(Bt([h,c],d),bs([h,c],"hidden"))}le(),fe(),f.props.animation?y()&&Q(d,f.unmount):f.unmount()}}function gt(t){E().addEventListener("mousemove",H),gs(Dt,H),H(t)}function ot(){f.state.isVisible&&f.hide(),f.state.isMounted&&(qe(),ke().forEach(function(t){t._tippy.unmount()}),M.parentNode&&M.parentNode.removeChild(M),Vt=Vt.filter(function(t){return t!==f}),f.state.isMounted=!1,te("onHidden",[f]))}function A(){f.state.isDestroyed||(f.clearDelayTimeouts(),f.unmount(),Be(),delete e._tippy,f.state.isDestroyed=!0,te("onDestroy",[f]))}}function st(e,n){n===void 0&&(n={});var s=Oe.plugins.concat(n.plugins||[]);bo();var a=Object.assign({},n,{plugins:s}),l=fo(e),v=l.reduce(function(m,T){var g=T&&Ao(T,a);return g&&m.push(g),m},[]);return Ut(e)?v[0]:v}st.defaultProps=Oe;st.setDefaultProps=ko;st.currentInput=Fe;Object.assign({},Ms,{effect:function(n){var s=n.state,a={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(s.elements.popper.style,a.popper),s.styles=a,s.elements.arrow&&Object.assign(s.elements.arrow.style,a.arrow)}});st.setDefaultProps({render:Hs});const Mo={class:"task-pane"},Do={key:0,class:"loading-overlay"},_o={key:1,class:"format-error-overlay"},Oo={class:"format-error-content"},Po={class:"format-error-message"},Io={class:"format-error-actions"},Uo={class:"doc-header"},Ro={class:"doc-title"},Lo={class:"action-area"},Fo={class:"select-container"},jo={class:"select-group"},Wo=["disabled"],Bo=["value"],No={class:"select-group"},Vo=["disabled"],Ho=["value"],zo=["title"],qo={key:0,class:"science-warning"},Yo={class:"action-buttons"},Ko=["disabled"],Xo={class:"btn-content"},Jo={key:0,class:"button-loader"},Go=["disabled"],Zo={class:"btn-content"},Qo={key:0,class:"button-loader"},er=["disabled"],tr={class:"content-area"},sr={class:"modal-header"},nr={class:"modal-body"},ar={class:"selection-content"},or={class:"modal-header"},rr={class:"modal-body"},ir={class:"alert-message"},lr={class:"alert-actions"},cr={key:2,class:"modal-overlay"},ur={class:"modal-header"},dr={class:"modal-body"},pr={class:"confirm-message"},fr={class:"confirm-actions"},hr={class:"task-queue"},vr={class:"queue-header"},gr={class:"queue-status-filter"},mr=["value"],br={class:"queue-actions"},wr=["disabled","title"],yr={class:"task-count"},xr={key:0,class:"queue-table-container"},Cr={class:"col-id"},Tr={class:"id-header"},kr={key:0,class:"col-subject"},Sr={class:"subject-header"},Er={class:"switch"},$r=["title"],Ar={key:1,class:"col-status"},Mr=["onClick"],Dr={class:"col-id"},_r={class:"id-content"},Or={class:"task-id"},Pr={key:0,class:"enhance-svg-icon"},Ir={key:0,class:"status-in-id"},Ur={key:0,class:"col-subject"},Rr=["onMouseenter"],Lr={key:1,class:"col-status"},Fr={class:"status-cell"},jr={class:"col-actions"},Wr={class:"task-actions"},Br=["onClick"],Nr=["onClick"],Vr={key:2,class:"no-action-icon",title:"无可用操作"},Hr={key:1,class:"empty-queue"},zr={key:3,class:"log-container"},qr={class:"log-actions"},Yr={class:"toggle-icon"},Kr=["innerHTML"],Xr={__name:"TaskPane",setup(e){const n=ne(!1),s=ne(!1),a=ne(!1),l=ne(""),v=ne(!1),m=ne(!1),T=ne(!1),g=ne(!0),b=ne(""),S=ne(!1),I=ne(window.innerWidth),$=lt(()=>I.value<750),D=lt(()=>I.value<380),H=()=>{I.value=window.innerWidth},R=ne(null),z=ne(!1),q=ne(""),P={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},Z=ne(!1),f=ne([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"}]),{docName:Y,selected:M,logger:K,map:X,subject:u,stage:w,subjectOptions:x,stageOptions:y,clearLog:k,checkDocumentFormat:E,getTaskStatusClass:_,getTaskStatusText:C,terminateTask:L,run1:te,setupLifecycle:le,navigateToTaskControl:fe,isLoading:xe,tryRemoveTaskPlaceholderWithLoading:F,confirmDialog:me,handleConfirm:oe,getCompletedTasksCount:ce,showConfirm:be}=un(),Q=ne(null);(()=>{var A;try{if((A=window.Application)!=null&&A.PluginStorage){const t=window.Application.PluginStorage.getItem("user_info");t?(Q.value=JSON.parse(t),console.log("用户信息已加载:",Q.value)):console.log("未找到用户信息")}}catch(t){console.error("解析用户信息时出错:",t)}})();const De=lt(()=>!Q.value||Q.value.isAdmin||Q.value.isOwner?x:Q.value.subject?x.filter(A=>A.value===Q.value.subject):x),Te=()=>{Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject&&(u.value=Q.value.subject)},ie=lt(()=>["physics","chemistry","biology","math"].includes(u.value)),Be=()=>{try{const A=E();g.value=A.isValid,b.value=A.message,S.value=!A.isValid,A.isValid||console.warn("文档格式检查失败:",A.message)}catch(A){console.error("执行文档格式检查时出错:",A),g.value=!1,b.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",S.value=!0}},we=lt(()=>{let A={};const t=X;if(q.value==="")A={...t};else for(const o in t)if(Object.prototype.hasOwnProperty.call(t,o)){const r=t[o];r.status===q.value&&(A[o]=r)}return z.value&&R.value?A[R.value]?{[R.value]:A[R.value]}:{}:A}),Je=lt(()=>{const A=we.value;return Object.entries(A).map(([o,r])=>({tid:o,...r})).sort((o,r)=>{const d=o.startTime||0;return(r.startTime||0)-d}).reduce((o,r)=>{const{tid:d,...i}=r;return o[d]=i,o},{})}),Ne=(A="wps-analysis")=>{u.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(l.value="未选中内容",s.value=!0):(A==="wps-analysis"?m.value=!0:A==="wps-enhance_analysis"&&(T.value=!0),te(A).catch(t=>{console.log(t),t.message.includes("重叠")?(l.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,s.value=!0):(console.error("操作失败:",t),l.value=t.message,s.value=!0)}).finally(()=>{A==="wps-analysis"?m.value=!1:A==="wps-enhance_analysis"&&(T.value=!1)})):(l.value="请选择学科",s.value=!0)},Ve=()=>{l.value="功能开发中……敬请期待",s.value=!0},He=(A,t)=>{R.value=A,fe(A)},ze=A=>{X[A]&&(X[A].status=3),R.value===A&&(R.value=null),F(A,!0)},qe=async()=>{const A=Object.entries(X).filter(([t,o])=>o.status===2);if(A.length===0){l.value="没有已完成的任务可释放",s.value=!0;return}try{if(await be(`确定要释放所有 ${A.length} 个已完成的任务吗？
此操作不可撤销。`)){let o=0;A.forEach(([r,d])=>{X[r]&&(X[r].status=3,R.value===r&&(R.value=null),F(r,!0),o++)}),l.value=`已成功释放 ${o} 个任务`,s.value=!0}}catch(t){console.error("释放任务时出错:",t),l.value="释放任务时出现错误",s.value=!0}},he=()=>{v.value=!v.value},ke=A=>A?A.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Pe=A=>{const t=Se(A);return t?ke(t):"无题目内容"},Se=A=>{if(!A.selectedText)return"";const t=A.selectedText.split("\r").filter(r=>r.trim());if(t.length===1){const r=A.selectedText.split(`
`).filter(d=>d.trim());r.length>1&&t.splice(0,1,...r)}const o=t.map((r,d)=>{const i=r.trim();return i.length>200,i});return o.length===1?t[0].trim():o.join(`
`)},Ye=(A,t)=>{if(!Z.value)return;const o=A.target,r=Se(t).toString();if(!r||r.trim()===""){console.log("题目内容为空，不显示tooltip");return}const d=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${r.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,i=A.clientX,h=A.clientY;if(P.subjects.has(o)){const O=P.subjects.get(o);O.setContent(d),O.setProps({getReferenceClientRect:()=>({width:0,height:0,top:h,bottom:h,left:i,right:i})}),O.show();return}const c=st(o,{content:d,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:h,bottom:h,left:i,right:i}),onHidden:()=>{c.setProps({getReferenceClientRect:null})}});P.subjects.set(o,c),c.show()},Ie=A=>{const t=A.currentTarget,o=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,r=A.clientX,d=A.clientY;if(P.enhance.has(t)){const h=P.enhance.get(t);h.setProps({getReferenceClientRect:()=>({width:0,height:0,top:d,bottom:d,left:r,right:r})}),h.show();return}const i=st(t,{content:o,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});P.enhance.set(t,i),i.show()},Ke=()=>{P.subjects.forEach(A=>{A.destroy()}),P.subjects.clear(),P.enhance.forEach(A=>{A.destroy()}),P.enhance.clear(),P.softBreak.forEach(A=>{A.destroy()}),P.softBreak.clear(),document.removeEventListener("click",Ue),document.removeEventListener("mousemove",Ge)},Ue=A=>{const t=document.querySelector(".tippy-box");t&&!t.contains(A.target)&&(P.subjects.forEach(o=>o.hide()),P.enhance.forEach(o=>o.hide()),P.softBreak.forEach(o=>o.hide()))};let _e=0;const Ge=A=>{const t=Date.now();if(t-_e<100)return;_e=t;const o=document.querySelector(".tippy-box");if(!o)return;const r=o.getBoundingClientRect();!(A.clientX>=r.left-20&&A.clientX<=r.right+20&&A.clientY>=r.top-20&&A.clientY<=r.bottom+20)&&!o.matches(":hover")&&(P.subjects.forEach(i=>i.hide()),P.enhance.forEach(i=>i.hide()),P.softBreak.forEach(i=>i.hide()))},at=()=>{document.addEventListener("click",Ue),document.addEventListener("mousemove",Ge)};ks(()=>{window.addEventListener("resize",H),at(),Te(),setTimeout(()=>{Be()},500);const A=document.createElement("style");A.id="tippy-custom-styles",A.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(A)}),Xs(()=>{window.removeEventListener("resize",H),Ke();const A=document.getElementById("tippy-custom-styles");A&&A.remove()}),le();const gt=A=>A.selectedText?A.selectedText.includes("\v")||A.selectedText.includes("\v"):!1,ot=A=>{const t=A.currentTarget,o=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,r=A.clientX,d=A.clientY;if(P.softBreak.has(t)){const h=P.softBreak.get(t);h.setProps({getReferenceClientRect:()=>({width:0,height:0,top:d,bottom:d,left:r,right:r})}),h.show();return}const i=st(t,{content:o,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});P.softBreak.set(t,i),i.show()};return(A,t)=>{var o,r,d,i,h;return N(),V("div",Mo,[ae(xe)?(N(),V("div",Do,t[26]||(t[26]=[p("div",{class:"loading-spinner"},null,-1),p("div",{class:"loading-text"},"处理中...",-1)]))):G("",!0),S.value?(N(),V("div",_o,[p("div",Oo,[t[27]||(t[27]=p("div",{class:"format-error-icon"},"⚠️",-1)),t[28]||(t[28]=p("div",{class:"format-error-title"},"文档格式不支持",-1)),p("div",Po,ee(b.value),1),p("div",Io,[p("button",{class:"retry-check-btn",onClick:t[0]||(t[0]=c=>Be())},"重新检查")])])])):G("",!0),p("div",Uo,[p("div",Ro,ee(ae(Y)||"未选择文档"),1),p("button",{class:"settings-btn",onClick:t[1]||(t[1]=c=>a.value=!0)},t[29]||(t[29]=[p("i",{class:"icon-settings"},null,-1)]))]),p("div",Lo,[p("div",Fo,[p("div",jo,[t[30]||(t[30]=p("label",{for:"stage-select"},"年级:",-1)),Le(p("select",{id:"stage-select","onUpdate:modelValue":t[2]||(t[2]=c=>ns(w)?w.value=c:null),class:"select-input",disabled:S.value},[(N(!0),V(wt,null,yt(ae(y),c=>(N(),V("option",{key:c.value,value:c.value},ee(c.label),9,Bo))),128))],8,Wo),[[jt,ae(w)]])]),p("div",No,[t[31]||(t[31]=p("label",{for:"subject-select"},"学科:",-1)),Le(p("select",{id:"subject-select","onUpdate:modelValue":t[3]||(t[3]=c=>ns(u)?u.value=c:null),class:"select-input",disabled:S.value},[(N(!0),V(wt,null,yt(De.value,c=>(N(),V("option",{key:c.value,value:c.value},ee(c.label),9,Ho))),128))],8,Vo),[[jt,ae(u)]]),Q.value&&!Q.value.isAdmin&&!Q.value.isOwner&&Q.value.subject?(N(),V("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((o=De.value.find(c=>c.value===Q.value.subject))==null?void 0:o.label)||Q.value.subject} 学科`}," 🔒 ",8,zo)):G("",!0)])]),ie.value?(N(),V("div",qo," 理科可使用增强模式以获取更精准的解析 ")):G("",!0),p("div",Yo,[p("button",{class:"action-btn primary",onClick:t[4]||(t[4]=c=>Ne("wps-analysis")),disabled:m.value||S.value},[p("div",Xo,[m.value?(N(),V("span",Jo)):G("",!0),t[32]||(t[32]=p("span",{class:"btn-text"},"解析",-1))])],8,Ko),ie.value?(N(),V("button",{key:0,class:"action-btn enhance",onClick:t[5]||(t[5]=c=>Ne("wps-enhance_analysis")),disabled:T.value||S.value},[p("div",Zo,[T.value?(N(),V("span",Qo)):G("",!0),t[33]||(t[33]=p("span",{class:"btn-text"},"增强解析",-1))])],8,Go)):G("",!0),((d=(r=Q.value)==null?void 0:r.orgs[0])==null?void 0:d.orgId)===2?(N(),V("button",{key:1,class:"action-btn secondary",onClick:t[6]||(t[6]=c=>Ve()),disabled:S.value},t[34]||(t[34]=[p("div",{class:"btn-content"},[p("span",{class:"btn-text"},"校对")],-1)]),8,er)):G("",!0)])]),p("div",tr,[n.value?(N(),V("div",{key:0,class:"modal-overlay",onClick:t[9]||(t[9]=c=>n.value=!1)},[p("div",{class:"modal-content",onClick:t[8]||(t[8]=Ze(()=>{},["stop"]))},[p("div",sr,[t[35]||(t[35]=p("div",{class:"modal-title"},"选中内容",-1)),p("button",{class:"modal-close",onClick:t[7]||(t[7]=c=>n.value=!1)},"×")]),p("div",nr,[p("pre",ar,ee(ae(M)||"未选中内容"),1)])])])):G("",!0),s.value?(N(),V("div",{key:1,class:"modal-overlay",onClick:t[13]||(t[13]=c=>s.value=!1)},[p("div",{class:"modal-content alert-modal",onClick:t[12]||(t[12]=Ze(()=>{},["stop"]))},[p("div",or,[t[36]||(t[36]=p("div",{class:"modal-title"},"提示",-1)),p("button",{class:"modal-close",onClick:t[10]||(t[10]=c=>s.value=!1)},"×")]),p("div",rr,[p("div",ir,ee(l.value),1),p("div",lr,[p("button",{class:"action-btn primary",onClick:t[11]||(t[11]=c=>s.value=!1)},"确定")])])])])):G("",!0),ae(me).show?(N(),V("div",cr,[p("div",{class:"modal-content confirm-modal",onClick:t[17]||(t[17]=Ze(()=>{},["stop"]))},[p("div",ur,[t[37]||(t[37]=p("div",{class:"modal-title"},"确认",-1)),p("button",{class:"modal-close",onClick:t[14]||(t[14]=c=>ae(oe)(!1))},"×")]),p("div",dr,[p("div",pr,ee(ae(me).message),1),p("div",fr,[p("button",{class:"action-btn secondary",onClick:t[15]||(t[15]=c=>ae(oe)(!1))},"取消"),p("button",{class:"action-btn primary",onClick:t[16]||(t[16]=c=>ae(oe)(!0))},"确定")])])])])):G("",!0),p("div",hr,[p("div",vr,[t[38]||(t[38]=p("div",{class:"queue-title"},"任务队列",-1)),p("div",gr,[Le(p("select",{id:"status-filter-select","onUpdate:modelValue":t[18]||(t[18]=c=>q.value=c),class:"status-filter-select-input"},[(N(!0),V(wt,null,yt(f.value,c=>(N(),V("option",{key:c.value,value:c.value},ee(c.label),9,mr))),128))],512),[[jt,q.value]])]),p("div",br,[p("button",{class:"release-all-btn",onClick:qe,disabled:ae(ce)()===0,title:ae(ce)()===0?"无已完成任务可释放":`释放所有${ae(ce)()}个已完成任务`}," 一键释放 ",8,wr)]),p("div",yr,ee(Object.keys(we.value).length)+"个任务",1)]),Object.keys(we.value).length>0?(N(),V("div",xr,[p("table",{class:Ee(["queue-table",{"narrow-view":$.value,"ultra-narrow-view":D.value}])},[p("thead",null,[p("tr",null,[p("th",Cr,[p("div",Tr,[t[40]||(t[40]=p("span",null,"任务ID",-1)),p("span",{class:"help-icon",onMouseenter:t[19]||(t[19]=c=>Ie(c))},t[39]||(t[39]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("circle",{cx:"12",cy:"12",r:"10"}),p("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),p("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),$.value?G("",!0):(N(),V("th",kr,[p("div",Sr,[t[41]||(t[41]=p("span",null,"题目内容",-1)),p("label",Er,[Le(p("input",{type:"checkbox","onUpdate:modelValue":t[20]||(t[20]=c=>Z.value=c)},null,512),[[Js,Z.value]]),p("span",{class:"slider round",title:Z.value?"关闭题目预览":"开启题目预览"},null,8,$r)])])])),D.value?G("",!0):(N(),V("th",Ar,"状态")),t[42]||(t[42]=p("th",{class:"col-actions"},"操作",-1))])]),p("tbody",null,[(N(!0),V(wt,null,yt(Je.value,(c,O)=>(N(),V("tr",{key:O,class:Ee(["task-row",[ae(_)(c.status),{"selected-task-row":O===R.value}]]),onClick:U=>He(O)},[p("td",Dr,[p("div",{class:Ee(["id-cell",{"id-with-status":D.value}])},[p("div",_r,[p("span",Or,ee(O.substring(0,8)),1),c.wordType==="wps-enhance_analysis"||c.isEnhanced?(N(),V("span",Pr,t[43]||(t[43]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("title",null,"增强模式"),p("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):G("",!0),gt(c)?(N(),V("span",{key:1,class:"soft-break-warning-icon",onMouseenter:t[21]||(t[21]=U=>ot(U))},t[44]||(t[44]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[p("title",null,"提示"),p("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),p("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),p("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):G("",!0)]),D.value?(N(),V("div",Ir,[p("span",{class:Ee(["task-tag compact",ae(_)(c.status)])},ee(ae(C)(c.status)),3)])):G("",!0)],2)]),$.value?G("",!0):(N(),V("td",Ur,[p("div",{class:"subject-cell",onMouseenter:U=>Ye(U,c)},ee(Pe(c)),41,Rr)])),D.value?G("",!0):(N(),V("td",Lr,[p("div",Fr,[p("span",{class:Ee(["task-tag",ae(_)(c.status)])},ee(ae(C)(c.status)),3)])])),p("td",jr,[p("div",Wr,[c.status===1?(N(),V("button",{key:0,onClick:Ze(U=>ae(L)(O),["stop"]),class:"terminate-btn"}," 终止 ",8,Br)):G("",!0),c.status===2?(N(),V("button",{key:1,onClick:Ze(U=>ze(O),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Nr)):G("",!0),c.status!==1&&c.status!==2?(N(),V("span",Vr,t[45]||(t[45]=[p("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[p("circle",{cx:"12",cy:"12",r:"10"}),p("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),p("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):G("",!0)])])],10,Mr))),128))])],2)])):(N(),V("div",Hr,t[46]||(t[46]=[p("div",{class:"empty-text"},"暂无任务",-1)])))]),((h=(i=Q.value)==null?void 0:i.orgs[0])==null?void 0:h.orgId)===2?(N(),V("div",zr,[p("div",{class:"log-header",onClick:he},[t[47]||(t[47]=p("div",{class:"log-title"},"执行日志",-1)),p("div",qr,[p("button",{class:"clear-btn",onClick:t[22]||(t[22]=Ze(c=>ae(k)(),["stop"]))},"清空日志"),p("span",Yr,ee(v.value?"▼":"▶"),1)])]),v.value?(N(),V("div",{key:0,class:"log-content",innerHTML:ae(K)},null,8,Kr)):G("",!0)])):G("",!0)]),a.value?(N(),V("div",{key:2,class:"modal-overlay",onClick:t[25]||(t[25]=c=>a.value=!1)},[p("div",{class:"modal-content",onClick:t[24]||(t[24]=Ze(()=>{},["stop"]))},[Gs(Zn,{onClose:t[23]||(t[23]=c=>a.value=!1)})])])):G("",!0)])}}},Gr=Ss(Xr,[["__scopeId","data-v-ee3a083c"]]);export{Gr as default};
