import{U as cn,r as he,h as mt,v as Ke,i as ee,j as Ut,k as Ws,m as Yt,_ as Bs,n as un,o as L,c as F,a as c,p as dn,t as ae,f as J,q as _e,w as Ye,s as Ht,e as es,F as bt,u as Et,x as ut,y as pn,z as pe,A as ts,B as bs,C as gt,D as fn,E as hn}from"./index-BS8UQ-9X.js";function gn(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=cn.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const vn={onbuttonclick:gn};var mn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function bn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function wn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var l=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,l.get?l:{enumerable:!0,get:function(){return e[a]}})}),t}var Ns={exports:{}};const yn={},xn=Object.freeze(Object.defineProperty({__proto__:null,default:yn},Symbol.toStringTag,{value:"Module"})),ws=wn(xn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",l=a?window:{};l.JS_SHA1_NO_WINDOW&&(a=!1);var v=!a&&typeof self=="object",C=!l.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;C?l=mn:v&&(l=self);var A=!l.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,w=!l.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",$="0123456789abcdef".split(""),O=[-**********,8388608,32768,128],U=[24,16,8,0],H=["hex","array","digest","arrayBuffer"],I=[],B=Array.isArray;(l.JS_SHA1_NO_NODE_JS||!B)&&(B=function(g){return Object.prototype.toString.call(g)==="[object Array]"});var K=ArrayBuffer.isView;w&&(l.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!K)&&(K=function(g){return typeof g=="object"&&g.buffer&&g.buffer.constructor===ArrayBuffer});var V=function(g){var b=typeof g;if(b==="string")return[g,!0];if(b!=="object"||g===null)throw new Error(s);if(w&&g.constructor===ArrayBuffer)return[new Uint8Array(g),!1];if(!B(g)&&!K(g))throw new Error(s);return[g,!1]},se=function(g){return function(b){return new P(!0).update(b)[g]()}},ne=function(){var g=se("hex");C&&(g=ie(g)),g.create=function(){return new P},g.update=function(T){return g.create().update(T)};for(var b=0;b<H.length;++b){var k=H[b];g[k]=se(k)}return g},ie=function(g){var b=ws,k=ws.Buffer,T;k.from&&!l.JS_SHA1_NO_BUFFER_FROM?T=k.from:T=function(m){return new k(m)};var S=function(m){if(typeof m=="string")return b.createHash("sha1").update(m,"utf8").digest("hex");if(m==null)throw new Error(s);return m.constructor===ArrayBuffer&&(m=new Uint8Array(m)),B(m)||K(m)||m.constructor===k?b.createHash("sha1").update(T(m)).digest("hex"):g(m)};return S},d=function(g){return function(b,k){return new ce(b,!0).update(k)[g]()}},te=function(){var g=d("hex");g.create=function(T){return new ce(T)},g.update=function(T,S){return g.create(T).update(S)};for(var b=0;b<H.length;++b){var k=H[b];g[k]=d(k)}return g};function P(g){g?(I[0]=I[16]=I[1]=I[2]=I[3]=I[4]=I[5]=I[6]=I[7]=I[8]=I[9]=I[10]=I[11]=I[12]=I[13]=I[14]=I[15]=0,this.blocks=I):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}P.prototype.update=function(g){if(this.finalized)throw new Error(t);var b=V(g);g=b[0];for(var k=b[1],T,S=0,m,R=g.length||0,E=this.blocks;S<R;){if(this.hashed&&(this.hashed=!1,E[0]=this.block,this.block=E[16]=E[1]=E[2]=E[3]=E[4]=E[5]=E[6]=E[7]=E[8]=E[9]=E[10]=E[11]=E[12]=E[13]=E[14]=E[15]=0),k)for(m=this.start;S<R&&m<64;++S)T=g.charCodeAt(S),T<128?E[m>>>2]|=T<<U[m++&3]:T<2048?(E[m>>>2]|=(192|T>>>6)<<U[m++&3],E[m>>>2]|=(128|T&63)<<U[m++&3]):T<55296||T>=57344?(E[m>>>2]|=(224|T>>>12)<<U[m++&3],E[m>>>2]|=(128|T>>>6&63)<<U[m++&3],E[m>>>2]|=(128|T&63)<<U[m++&3]):(T=65536+((T&1023)<<10|g.charCodeAt(++S)&1023),E[m>>>2]|=(240|T>>>18)<<U[m++&3],E[m>>>2]|=(128|T>>>12&63)<<U[m++&3],E[m>>>2]|=(128|T>>>6&63)<<U[m++&3],E[m>>>2]|=(128|T&63)<<U[m++&3]);else for(m=this.start;S<R&&m<64;++S)E[m>>>2]|=g[S]<<U[m++&3];this.lastByteIndex=m,this.bytes+=m-this.start,m>=64?(this.block=E[16],this.start=m-64,this.hash(),this.hashed=!0):this.start=m}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},P.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var g=this.blocks,b=this.lastByteIndex;g[16]=this.block,g[b>>>2]|=O[b&3],this.block=g[16],b>=56&&(this.hashed||this.hash(),g[0]=this.block,g[16]=g[1]=g[2]=g[3]=g[4]=g[5]=g[6]=g[7]=g[8]=g[9]=g[10]=g[11]=g[12]=g[13]=g[14]=g[15]=0),g[14]=this.hBytes<<3|this.bytes>>>29,g[15]=this.bytes<<3,this.hash()}},P.prototype.hash=function(){var g=this.h0,b=this.h1,k=this.h2,T=this.h3,S=this.h4,m,R,E,G=this.blocks;for(R=16;R<80;++R)E=G[R-3]^G[R-8]^G[R-14]^G[R-16],G[R]=E<<1|E>>>31;for(R=0;R<20;R+=5)m=b&k|~b&T,E=g<<5|g>>>27,S=E+m+S+1518500249+G[R]<<0,b=b<<30|b>>>2,m=g&b|~g&k,E=S<<5|S>>>27,T=E+m+T+1518500249+G[R+1]<<0,g=g<<30|g>>>2,m=S&g|~S&b,E=T<<5|T>>>27,k=E+m+k+1518500249+G[R+2]<<0,S=S<<30|S>>>2,m=T&S|~T&g,E=k<<5|k>>>27,b=E+m+b+1518500249+G[R+3]<<0,T=T<<30|T>>>2,m=k&T|~k&S,E=b<<5|b>>>27,g=E+m+g+1518500249+G[R+4]<<0,k=k<<30|k>>>2;for(;R<40;R+=5)m=b^k^T,E=g<<5|g>>>27,S=E+m+S+1859775393+G[R]<<0,b=b<<30|b>>>2,m=g^b^k,E=S<<5|S>>>27,T=E+m+T+1859775393+G[R+1]<<0,g=g<<30|g>>>2,m=S^g^b,E=T<<5|T>>>27,k=E+m+k+1859775393+G[R+2]<<0,S=S<<30|S>>>2,m=T^S^g,E=k<<5|k>>>27,b=E+m+b+1859775393+G[R+3]<<0,T=T<<30|T>>>2,m=k^T^S,E=b<<5|b>>>27,g=E+m+g+1859775393+G[R+4]<<0,k=k<<30|k>>>2;for(;R<60;R+=5)m=b&k|b&T|k&T,E=g<<5|g>>>27,S=E+m+S-1894007588+G[R]<<0,b=b<<30|b>>>2,m=g&b|g&k|b&k,E=S<<5|S>>>27,T=E+m+T-1894007588+G[R+1]<<0,g=g<<30|g>>>2,m=S&g|S&b|g&b,E=T<<5|T>>>27,k=E+m+k-1894007588+G[R+2]<<0,S=S<<30|S>>>2,m=T&S|T&g|S&g,E=k<<5|k>>>27,b=E+m+b-1894007588+G[R+3]<<0,T=T<<30|T>>>2,m=k&T|k&S|T&S,E=b<<5|b>>>27,g=E+m+g-1894007588+G[R+4]<<0,k=k<<30|k>>>2;for(;R<80;R+=5)m=b^k^T,E=g<<5|g>>>27,S=E+m+S-899497514+G[R]<<0,b=b<<30|b>>>2,m=g^b^k,E=S<<5|S>>>27,T=E+m+T-899497514+G[R+1]<<0,g=g<<30|g>>>2,m=S^g^b,E=T<<5|T>>>27,k=E+m+k-899497514+G[R+2]<<0,S=S<<30|S>>>2,m=T^S^g,E=k<<5|k>>>27,b=E+m+b-899497514+G[R+3]<<0,T=T<<30|T>>>2,m=k^T^S,E=b<<5|b>>>27,g=E+m+g-899497514+G[R+4]<<0,k=k<<30|k>>>2;this.h0=this.h0+g<<0,this.h1=this.h1+b<<0,this.h2=this.h2+k<<0,this.h3=this.h3+T<<0,this.h4=this.h4+S<<0},P.prototype.hex=function(){this.finalize();var g=this.h0,b=this.h1,k=this.h2,T=this.h3,S=this.h4;return $[g>>>28&15]+$[g>>>24&15]+$[g>>>20&15]+$[g>>>16&15]+$[g>>>12&15]+$[g>>>8&15]+$[g>>>4&15]+$[g&15]+$[b>>>28&15]+$[b>>>24&15]+$[b>>>20&15]+$[b>>>16&15]+$[b>>>12&15]+$[b>>>8&15]+$[b>>>4&15]+$[b&15]+$[k>>>28&15]+$[k>>>24&15]+$[k>>>20&15]+$[k>>>16&15]+$[k>>>12&15]+$[k>>>8&15]+$[k>>>4&15]+$[k&15]+$[T>>>28&15]+$[T>>>24&15]+$[T>>>20&15]+$[T>>>16&15]+$[T>>>12&15]+$[T>>>8&15]+$[T>>>4&15]+$[T&15]+$[S>>>28&15]+$[S>>>24&15]+$[S>>>20&15]+$[S>>>16&15]+$[S>>>12&15]+$[S>>>8&15]+$[S>>>4&15]+$[S&15]},P.prototype.toString=P.prototype.hex,P.prototype.digest=function(){this.finalize();var g=this.h0,b=this.h1,k=this.h2,T=this.h3,S=this.h4;return[g>>>24&255,g>>>16&255,g>>>8&255,g&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,T>>>24&255,T>>>16&255,T>>>8&255,T&255,S>>>24&255,S>>>16&255,S>>>8&255,S&255]},P.prototype.array=P.prototype.digest,P.prototype.arrayBuffer=function(){this.finalize();var g=new ArrayBuffer(20),b=new DataView(g);return b.setUint32(0,this.h0),b.setUint32(4,this.h1),b.setUint32(8,this.h2),b.setUint32(12,this.h3),b.setUint32(16,this.h4),g};function ce(g,b){var k,T=V(g);if(g=T[0],T[1]){var S=[],m=g.length,R=0,E;for(k=0;k<m;++k)E=g.charCodeAt(k),E<128?S[R++]=E:E<2048?(S[R++]=192|E>>>6,S[R++]=128|E&63):E<55296||E>=57344?(S[R++]=224|E>>>12,S[R++]=128|E>>>6&63,S[R++]=128|E&63):(E=65536+((E&1023)<<10|g.charCodeAt(++k)&1023),S[R++]=240|E>>>18,S[R++]=128|E>>>12&63,S[R++]=128|E>>>6&63,S[R++]=128|E&63);g=S}g.length>64&&(g=new P(!0).update(g).array());var G=[],ge=[];for(k=0;k<64;++k){var we=g[k]||0;G[k]=92^we,ge[k]=54^we}P.call(this,b),this.update(ge),this.oKeyPad=G,this.inner=!0,this.sharedMemory=b}ce.prototype=new P,ce.prototype.finalize=function(){if(P.prototype.finalize.call(this),this.inner){this.inner=!1;var g=this.array();P.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(g),P.prototype.finalize.call(this)}};var fe=ne();fe.sha1=fe,fe.sha1.hmac=te(),A?e.exports=fe:l.sha1=fe})()})(Ns);var kn=Ns.exports;const Tn=bn(kn);function ys(){return"http://worksheet.hexinedu.com"}function St(){return"http://127.0.0.1:3000"}function xs(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const _t=async(e,s,t,a={},l=8e3)=>{try{return await Promise.race([e(),new Promise((v,C)=>setTimeout(()=>C(new Error("WebSocket请求超时，切换到HTTP")),l))])}catch{try{let C;return s==="get"?C=await Ut.get(t,{params:a}):s==="post"?C=await Ut.post(t,a):s==="delete"&&(C=await Ut.delete(t)),C.data}catch(C){throw new Error(`请求失败: ${C.message||"未知错误"}`)}}};function Cn(e,s,t,a){const l=[e,s,t,a].join(":");return Tn(l)}function $n(){const e=he(""),s=he(""),t=he(""),a=mt({}),l=he(""),v=he("");let C="",A=null;const w=he("c:\\Temp"),$=mt({appKey:"",appSecret:""}),O=mt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),U=mt({show:!1,title:"",message:"",type:"error"}),H=he(""),I=he("junior"),B=he(null),K=he(!1),V=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],se=()=>Ke.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],ne=mt(se()),ie=async()=>{try{const n=await ee.getWatcherStatus();n.data&&n.data.watchDir&&(w.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${w.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},d=async()=>{var n,r,i;try{if(!$.appKey||!$.appSecret)throw new Error("未初始化app信息");const p=60,o=Date.now();let h;try{const q=window.Application.PluginStorage.getItem("token_info");q&&(h=JSON.parse(q))}catch(q){h=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${q.message}</span><br/>`}if(h&&h.access_token&&h.expired_time>o+p*1e3)return h.access_token;const u=$.appKey,x="1234567",_=Math.floor(Date.now()/1e3),D=Cn(u,x,$.appSecret,_),W=await Ut.get(ys()+"/api/open/account/v1/auth/token",{params:{app_key:u,app_nonstr:x,app_timestamp:_,app_signature:D}});if((r=(n=W.data)==null?void 0:n.data)!=null&&r.access_token){const q=W.data.data.access_token;let Y;if(W.data.data.expired_time){const de=parseInt(W.data.data.expired_time);Y=o+de*1e3,t.value+=`<span class="log-item info">token已更新，有效期${de}秒</span><br/>`}else Y=o+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const oe={access_token:q,expired_time:Y};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(oe))}catch(de){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${de.message}</span><br/>`}return q}else throw new Error(((i=W.data)==null?void 0:i.message)||"获取access_token失败")}catch(p){throw t.value+=`<span class="log-item error">获取access_token失败: ${p.message}</span><br/>`,p}},te=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},P=()=>{const n=window.Application,r=n.Documents.Count;for(let i=1;i<=r;i++){const p=n.Documents.Item(i);if(p.DocID===l.value)return p}return null},ce=()=>{try{const n=P();if(!n)return{isValid:!1,message:"未找到当前文档"};let r="";try{r=n.Name||""}catch{try{r=m("getDocName")||""}catch{r=""}}if(r){const i=r.toLowerCase();return i.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:i.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},fe=async(n,r="",i=0,p=null)=>await X("添加批注",async()=>{const o=window.Application,h=P();let u;if(p)u=p;else{const x=h.ActiveWindow.Selection;if(!x||x.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;u=x.Range}if(r){const x=u.Text,_=u.Find;_.ClearFormatting(),_.Text=r,_.Forward=!0,_.Wrap=0;let D=0,W=[];const q=u.Start,Y=u.End;for(;_.Execute()&&(_.Found&&_.Parent.Start>=q&&_.Parent.End<=Y);){const oe=_.Parent.Start,de=_.Parent.End;if(W.some(be=>oe===be.start&&de===be.end)){const be=Math.min(de,Y);if(be>=Y)break;_.Parent.SetRange(be,Y)}else{if(W.push({start:oe,end:de}),i===-1||D===i){const ve=h.Comments.Add(_.Parent,n);try{ve&&ve.Range&&ve.Range.ParagraphFormat&&(ve.Range.ParagraphFormat.Reset(),ve.Range.ParagraphFormat.LineSpacingRule=3,ve.Range.ParagraphFormat.LineSpacing=10)}catch(Te){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${Te.message}</span><br/>`}if(i!==-1&&D===i)return!0}D++;const be=Math.min(de,Y);if(console.log("nextStart",be),be>=Y)break;const ct=h.Range(be,Y);_.Parent.SetRange(be,Y)}}return i!==-1&&D<=i?!1:i===-1&&D>0?!0:!(i===-1&&D===0)}else{const x=h.Comments.Add(u,n);try{x&&x.Range&&x.Range.ParagraphFormat&&(x.Range.ParagraphFormat.Reset(),x.Range.ParagraphFormat.LineSpacingRule=3,x.Range.ParagraphFormat.LineSpacing=10)}catch(_){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${_.message}</span><br/>`}return t.value+=`<span class="log-item success">已为${p?"指定范围":"选中内容"}添加批注: "${n}"</span><br/>`,!0}}),g=n=>n===0?"status-preparing":n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",b=n=>n===0?"准备中":n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"准备中",k=n=>{const r=Date.now()-n,i=Math.floor(r/1e3);return i<60?`${i}秒`:i<3600?`${Math.floor(i/60)}分${i%60}秒`:`${Math.floor(i/3600)}时${Math.floor(i%3600/60)}分`},T=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const i=P();if(i&&i.ContentControls)for(let p=1;p<=i.ContentControls.Count;p++)try{const o=i.ContentControls.Item(p);if(o&&o.Title&&(o.Title===`任务_${n}`||o.Title===`任务增强_${n}`||o.Title===`校对_${n}`)){const h=o.Title===`任务增强_${n}`||a[n].isEnhanced,u=o.Title===`校对_${n}`||a[n].isCheckTask;u?o.Title=`已停止校对_${n}`:h?o.Title=`已停止增强_${n}`:o.Title=`已停止_${n}`;const x=u?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${x}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(i){t.value+=`<span class="log-item warning">更新控件标题失败: ${i.message}</span><br/>`}let r=null;if(Z[n]&&Z[n].urlId){r=Z[n].urlId;try{try{const i=await _t(async()=>await ee.getUrlMonitorStatus(),"get",`${St()}/api/url/status`),o=(Array.isArray(i)?i:i.data?Array.isArray(i.data)?i.data:[]:[]).find(h=>h.urlId===r);o&&o.downloadedPath&&(a[n].resultFile=o.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${o.downloadedPath}</span><br/>`)}catch(i){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${i.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ke(r),delete Z[n]}catch(i){t.value+=`<span class="log-item warning">停止URL监控出错: ${i.message}，将重试</span><br/>`,delete Z[n],setTimeout(async()=>{try{r&&await _t(async()=>await ee.stopUrlMonitoring(r),"delete",`${St()}/api/url/monitor/${r}`)}catch(p){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${p.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(r){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${r.message}</span><br/>`,Z[n]&&delete Z[n]}},S=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const r=P();if(r&&r.ContentControls)for(let p=1;p<=r.ContentControls.Count;p++)try{const o=r.ContentControls.Item(p);if(o&&o.Title&&(o.Title===`任务_${n}`||o.Title===`任务增强_${n}`||o.Title===`校对_${n}`)){const h=o.Title===`任务增强_${n}`||a[n].isEnhanced,u=o.Title===`校对_${n}`||a[n].isCheckTask;u?o.Title=`已停止校对_${n}`:h?o.Title=`已停止增强_${n}`:o.Title=`已停止_${n}`,o.LockContents=!1;const x=u?"校对":h?"增强":"普通";t.value+=`<span class="log-item info">已将${x}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let i=null;if(Z[n]&&Z[n].urlId){i=Z[n].urlId;try{const p=await ee.getUrlMonitorStatus(),h=(Array.isArray(p)?p:p.data?Array.isArray(p.data)?p.data:[]:[]).find(u=>u.urlId===i);h&&h.downloadedPath&&(a[n].resultFile=h.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${h.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ke(i),delete Z[n]}catch(p){t.value+=`<span class="log-item warning">停止URL监控出错: ${p.message}，将重试</span><br/>`,delete Z[n]}}},m=n=>vn.onbuttonclick(n),R=()=>{try{e.value=m("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},E=()=>{P().ActiveWindow.Selection.Copy()},G=n=>{const r=window.Application.Documents.Add();r.Content.Paste(),r.SaveAs2(`${w.value}\\${n}`,12,"","",!1),r.Close(),P().ActiveWindow.Activate()},ge=n=>{const r=window.Application.Documents.Add("",!1,0,!1);r.Content.Paste(),r.SaveAs2(`${w.value}\\${n}`,12,"","",!1),r.Close(),P().ActiveWindow.Activate()},we=n=>{try{const i=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,p=window.Application.Documents.Add("",!1,0,!1);p.Content.Paste();const o=`${i}\\${n}`;p.SaveAs2(o,12,"","",!1),p.Close(),P().ActiveWindow.Activate(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${o}.docx</span><br/>`}catch(r){throw t.value+=`<span class="log-item error">方式三保存失败: ${r.message}</span><br/>`,r}},Me=n=>{const r=window.Application.Documents.Add();r.Content.Paste(),r.SaveAs2(`${w.value}\\${n}`,12,"","",!1),P().ActiveWindow.Activate()},Fe=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let r="method2";try{const i=await ee.getSaveMethod();if(i.success&&i.saveMethod){r=i.saveMethod;const p=r==="method1"?"方式一":r==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${p}</span><br/>`}}catch(i){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${i.message}</span><br/>`}r==="method1"?(G(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):r==="method2"?(ge(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):r==="method3"?(we(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):r==="method4"&&(Me(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${w.value}\\${n}.docx</span><br/>`),ee.associateFileWithClient(`${n}.docx`).then(i=>{i.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${i.message||"未知错误"}</span><br/>`}).catch(i=>{t.value+=`<span class="log-item warning">关联文件时出错: ${i.message}</span><br/>`})}catch(r){t.value+=`<span class="log-item error">保存文件失败: ${r.message}</span><br/>`}},De=he(null),Re=mt([]),Ce=new Set,Ee=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),Pe=async n=>{try{const r=n.slice(C.length);if(r.trim()){const i=ee.getClientId();if(!i){console.warn("无法获取客户端ID，跳过日志同步");return}const p=Ee(r);if(!p.trim()){C=n;return}await ee.sendRequest("logger","syncLog",{content:p,timestamp:new Date().toISOString(),clientId:i}),C=n}}catch(r){console.error("同步日志到服务端失败:",r)}},je=()=>{ee.connect().then(()=>{ie()}).catch(r=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${r.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const r=[];for(const i in a)if(a.hasOwnProperty(i)){const p=a[i];(p.status===0||p.status===1)&&!p.terminated&&(r.includes(i)||r.push(i))}if(r.length>0){let i=!1;try{const p=P();if(p){const h=`taskpane_id_${p.DocID}`,u=window.Application.PluginStorage.getItem(h);if(u){const x=window.Application.GetTaskPane(u);x&&(i=x.Visible)}}}catch(p){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${p.message}</span><br/>`,i=!0}setTimeout(()=>{if(i){const p=`检测到 ${r.length} 个未完成的任务，是否继续？`;re(p).then(o=>{o?(t.value+=`<span class="log-item info">用户选择继续 ${r.length} 个进行中的任务 (by taskId)...</span><br/>`,ee.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:r}).then(h=>{h&&h.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${h.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(h==null?void 0:h.message)||"未知错误"}</span><br/>`}).catch(h=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${h.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',r.forEach(h=>{T(h)}),t.value+=`<span class="log-item success">${r.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(o=>{t.value+=`<span class="log-item error">弹窗错误: ${o.message}，默认停止任务（保留控件）</span><br/>`,r.forEach(h=>{T(h)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};ee.setConnectionSuccessHandler(n),ee.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),ee.addEventListener("connection",r=>{r.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${r.reason||"未知"}, 代码: ${r.code||"N/A"}，将自动重连</span><br/>`)}),ee.addEventListener("watcher",r=>{var i,p;if(Re.push(r),r.type,r.eventType==="uploadSuccess"){const o=(i=r.data)==null?void 0:i.file,h=o==null?void 0:o.replace(/\.docx$/,""),u=`${r.eventType}_${o}_${Date.now()}`;if(Ce.has(u)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${o}</span><br/>`;return}if(Ce.add(u),Ce.size>100){const _=Ce.values();Ce.delete(_.next().value)}const x=h&&((p=a[h])==null?void 0:p.wordType);We(r,x)}else r.eventType&&We(r,null)}),ee.addEventListener("urlMonitor",r=>{Re.push(r),r.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${r.eventType||r.action}</span><br/>`),r.eventType&&We(r,null)}),ee.addEventListener("health",r=>{}),ee.addEventListener("error",r=>{const i=r.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${i}</span><br/>`,console.error("WebSocket错误:",r)})},Z=mt({}),Ue=async(n,r,i=!1,p=5e3,o={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const h=await ee.startUrlMonitoring(n,p,{downloadOnSuccess:o.downloadOnSuccess!==void 0?o.downloadOnSuccess:!0,appKey:o.appKey,filename:o.filename,taskId:r}),u=h.success||(h==null?void 0:h.success),x=h.urlId||(h==null?void 0:h.urlId);if(u&&x){Z[r]={urlId:x,url:n,isResultUrl:i,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${x}</span><br/>`;try{await ee.startUrlChecking(x)}catch{}return x}else throw new Error("服务器返回失败")}catch(h){return t.value+=`<span class="log-item error">启动URL监控失败: ${h.message}</span><br/>`,null}},ke=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(Z).forEach(i=>{Z[i].urlId===n&&delete Z[i]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const r=await _t(async()=>await ee.stopUrlMonitoring(n),"delete",`${St()}/api/url/monitor/${n}`);return r&&(r.success||r!=null&&r.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(r){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${r.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await _t(async()=>await ee.stopUrlMonitoring(n),"delete",`${St()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},rt=async()=>{try{const n=await _t(async()=>await ee.getUrlMonitorStatus(),"get",`${St()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Ge=async n=>{try{return await ee.forceUrlCheck(n)}catch{return!1}},We=async(n,r)=>{var i;if(n.eventType==="uploadSuccess"){const p=n.data.file,o=p.replace(/\.docx$/,"");if(a[o]){if(a[o].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${o.substring(0,8)} 的上传通知</span><br/>`;return}if(a[o].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${o.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${p} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${r||a[o].wordType||"wps-analysis"}</span><br/>`,a[o].status=1,a[o].uploadSuccess=!0,await me(o,r||a[o].wordType||"wps-analysis")}}else if(n.eventType==="encryptedFileError"){const p=n.data.file,o=p.replace(/\.docx$/,"");if(a[o]){t.value+=`<span class="log-item error">文件加密错误: ${p}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[o].status=-1,a[o].errorMessage=n.data.message||"文件已加密，无法处理";try{const h=P();if(h&&h.ContentControls)for(let u=1;u<=h.ContentControls.Count;u++)try{const x=h.ContentControls.Item(u);if(x&&x.Title&&(x.Title===`任务_${o}`||x.Title===`任务增强_${o}`||x.Title===`校对_${o}`)){const _=x.Title===`任务增强_${o}`||a[o].isEnhanced,D=x.Title===`校对_${o}`||a[o].isCheckTask;D?x.Title=`异常校对_${o}`:_?x.Title=`异常增强_${o}`:x.Title=`异常_${o}`;const W=D?"校对":_?"增强":"普通";t.value+=`<span class="log-item info">已将${W}任务${o.substring(0,8)}控件标记为异常（文件加密）</span><br/>`;break}}catch{continue}}catch(h){t.value+=`<span class="log-item warning">更新控件标题失败: ${h.message}</span><br/>`}ye(o)}}else if(n.eventType==="uploadError"){const p=n.data.file,o=p.replace(/\.docx$/,"");if(a[o]){t.value+=`<span class="log-item error">文件上传错误: ${p}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[o].status=-1,a[o].errorMessage=n.data.message||"文件上传失败";try{const h=P();if(h&&h.ContentControls)for(let u=1;u<=h.ContentControls.Count;u++)try{const x=h.ContentControls.Item(u);if(x&&x.Title&&(x.Title===`任务_${o}`||x.Title===`任务增强_${o}`||x.Title===`校对_${o}`)){const _=x.Title===`任务增强_${o}`||a[o].isEnhanced,D=x.Title===`校对_${o}`||a[o].isCheckTask;D?x.Title=`异常校对_${o}`:_?x.Title=`异常增强_${o}`:x.Title=`异常_${o}`;const W=D?"校对":_?"增强":"普通";t.value+=`<span class="log-item info">已将${W}任务${o.substring(0,8)}控件标记为异常（文件上传失败）</span><br/>`;break}}catch{continue}}catch(h){t.value+=`<span class="log-item warning">更新控件标题失败: ${h.message}</span><br/>`}ye(o)}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:p,url:o,taskId:h,downloadedPath:u}=n.data,x=Object.keys(Z).filter(_=>Z[_].urlId===p);x.length>0&&x.forEach(_=>{u&&a[_]&&(a[_].resultFile=u,a[_].resultDownloaded=!0),delete Z[_]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:p,url:o,filePath:h,taskId:u}=n.data;if(!Object.values(Z).some(_=>_.urlId===p)&&u&&((i=a[u])!=null&&i.terminated))return;if(u&&a[u]&&a[u].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${p} 的文件下载通知</span><br/>`,p)try{await ee.stopUrlMonitoring(p),Z[u]&&delete Z[u]}catch{}return}if(u&&a[u]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${h}</span><br/>`,a[u].isCheckTask&&h.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${h}</span><br/>`;try{const D=window.Application.FileSystem.ReadFile(h),W=JSON.parse(D);if(await ue(W,u)){a[u].status=2,t.value+=`<span class="log-item success">校对任务${u.substring(0,8)}已完成批注处理</span><br/>`;const Y=k(a[u].startTime);t.value+=`<span class="log-item success">校对任务${u.substring(0,8)}完成，总耗时${Y}</span><br/>`}}catch(_){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${_.message}</span><br/>`,_.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${h}</span><br/>`),a[u].status=-1,a[u].errorMessage=`JSON处理失败: ${_.message}`,t.value+=`<span class="log-item error">校对任务${u.substring(0,8)}处理失败</span><br/>`}}else{a[u].resultFile=h,a[u].resultDownloaded=!0;const _=k(a[u].startTime);t.value+=`<span class="log-item success">任务${u.substring(0,8)}完成，总耗时${_}</span><br/>`,await ze(u)}Z[u]&&Z[u].urlId&&(ke(Z[u].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete Z[u])}else if(p){t.value+=`<span class="log-item info">URL文件已下载: ${h}</span><br/>`;const _=Object.keys(Z).filter(D=>{var W;return Z[D].urlId===p&&!((W=a[D])!=null&&W.terminated)});_.length>0&&_.forEach(async D=>{if(a[D]){if(t.value+=`<span class="log-item info">关联到任务: ${D.substring(0,8)}</span><br/>`,a[D].resultFile=h,a[D].resultDownloaded=!0,a[D].isCheckTask&&h.endsWith(".wps.json"))try{const q=window.Application.FileSystem.ReadFile(h),Y=JSON.parse(q);if(await ue(Y,D)&&a[D].status===1){a[D].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const de=k(a[D].startTime);t.value+=`<span class="log-item success">校对任务${D.substring(0,8)}完成，总耗时${de}</span><br/>`}}catch(W){t.value+=`<span class="log-item error">处理校对JSON失败: ${W.message}</span><br/>`,a[D].status===1&&(a[D].status=-1,a[D].errorMessage=`JSON处理失败: ${W.message}`,t.value+=`<span class="log-item error">校对任务${D.substring(0,8)}处理失败</span><br/>`)}else if(a[D].status===1){a[D].status=2;const W=k(a[D].startTime);t.value+=`<span class="log-item success">任务${D.substring(0,8)}完成，总耗时${W}</span><br/>`}}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:p,url:o,error:h,taskId:u}=n.data;if(u&&a[u]&&a[u].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${u.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${h}</span><br/>`,u&&a[u]){a[u].status=-1,a[u].errorMessage=`下载失败: ${h}`;try{const x=P();if(x&&x.ContentControls)for(let _=1;_<=x.ContentControls.Count;_++)try{const D=x.ContentControls.Item(_);if(D&&D.Title&&(D.Title===`任务_${u}`||D.Title===`任务增强_${u}`||D.Title===`校对_${u}`)){const W=D.Title===`任务增强_${u}`||a[u].isEnhanced,q=D.Title===`校对_${u}`||a[u].isCheckTask;q?D.Title=`异常校对_${u}`:W?D.Title=`异常增强_${u}`:D.Title=`异常_${u}`;const Y=q?"校对":W?"增强":"普通";t.value+=`<span class="log-item info">已将${Y}任务${u.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(x){t.value+=`<span class="log-item warning">更新控件标题失败: ${x.message}</span><br/>`}ye(u),Z[u]&&delete Z[u]}if(p)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${p}</span><br/>`,await _t(async()=>await ee.stopUrlMonitoring(p),"delete",`${St()}/api/url/monitor/${p}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},me=async(n,r="wps-analysis")=>{try{if(!$.appKey)throw new Error("未初始化appKey信息");const i=await d(),p=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,o=await Ut.post(ys()+"/api/open/ticket/v1/ai_comment/create",{access_token:i,data:{app_key:$.appKey,subject:H.value,stage:I.value,file_name:`${n}`,word_url:p,word_type:r,is_ai_auto:!0,is_ai_edit:!0,create_user_id:$.userId,create_username:$.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),h=o.data.data.ticket_id;if(!h)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",ye(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${h}，开始监控结果文件</span><br/>`;let u,x;r==="wps-check"?(u=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${$.appKey}/ai/${h}.wps.json`,x=`${h}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${x}</span><br/>`):(u=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${h}.wps.docx`,x=`${h}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${x}</span><br/>`);const _={downloadOnSuccess:!0,filename:x,appKey:$.appKey,ticketId:h,taskId:n},D=await Ue(u,n,!0,3e3,_);return a[n]&&(a[n].ticketId=h,a[n].resultUrl=u,a[n].urlMonitorId=D,a[n].status=1),o.data}catch(i){return t.value+=`<span class="log-item error">API调用失败: ${i.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${i.message}`,ye(n)),!1}},pt=async(n,r="wps-analysis")=>new Promise((i,p)=>{try{t.value+=`<span class="log-item info">监控目录: ${w.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${r}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=r,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const o=1e3,h=10;let u=0;const x=setInterval(()=>{if(u++,a[n]&&a[n].uploadSuccess){clearInterval(x),i(!0);return}if(a[n]&&a[n].status===-1&&(clearInterval(x),t.value+=`<span class="log-item error">任务${n.substring(0,8)}已异常，停止等待上传完成</span><br/>`,p(new Error(a[n].errorMessage||"任务已异常"))),u>=h){clearInterval(x),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',ye(n);return}},o)}catch(o){t.value+=`<span class="log-item error">任务处理异常: ${o.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${o.message}`),ye(n),p(o)}}),Ze=async()=>{var i;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,r=n.ActiveDocument;if(l.value=r.DocID,v.value=n.ActiveWindow.Index,r!=null&&r.ContentControls)for(let p=1;p<=r.ContentControls.Count;p++){const o=r.ContentControls.Item(p);if(o&&o.Title){let h=null,u=1,x=!1,_=!1;if(o.Title.startsWith("任务增强_")?(h=o.Title.substring(5),u=1,x=!0):o.Title.startsWith("任务_")?(h=o.Title.substring(3),u=1):o.Title.startsWith("校对_")?(h=o.Title.substring(3),u=1,_=!0):o.Title.startsWith("已完成增强_")?(h=o.Title.substring(6),u=2,x=!0):o.Title.startsWith("已完成校对_")?(h=o.Title.substring(6),u=2,_=!0):o.Title.startsWith("已完成_")?(h=o.Title.substring(4),u=2):o.Title.startsWith("异常增强_")?(h=o.Title.substring(5),u=-1,x=!0):o.Title.startsWith("异常校对_")?(h=o.Title.substring(5),u=-1,_=!0):o.Title.startsWith("异常_")?(h=o.Title.substring(3),u=-1):o.Title.startsWith("已停止增强_")?(h=o.Title.substring(6),u=4,x=!0):o.Title.startsWith("已停止校对_")?(h=o.Title.substring(6),u=4,_=!0):o.Title.startsWith("已停止_")&&(h=o.Title.substring(4),u=4),h&&!a[h]){let D="";try{D=((i=o.Range)==null?void 0:i.Text)||""}catch{}let W=Date.now()-24*60*60*1e3;try{if(h.length===24){const oe=h.substring(0,8),de=parseInt(oe,16);!isNaN(de)&&de>0&&de<2147483647&&(W=de*1e3)}else{const oe=Date.now()-864e5;u===2?W=oe-60*60*1e3:u===-1?W=oe-30*60*1e3:u===4?W=oe-45*60*1e3:W=oe}}catch{}a[h]={status:u,startTime:W,contentControlId:o.ID,isEnhanced:x,isCheckTask:_,selectedText:D};const q=u===1?"进行中":u===2?"已完成":u===-1?"异常":u===4?"已停止":"未知",Y=_?"校对":x?"增强":"普通";t.value+=`<span class="log-item info">发现已有${Y}任务: ${h.substring(0,8)}, 状态: ${q}</span><br/>`}}}},ye=async(n,r=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}r&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const i=P();if(!i){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let p=!1;const o=a[n].isEnhanced;await X("删除内容控件",async()=>{if(i.ContentControls&&i.ContentControls.Count>0)for(let h=i.ContentControls.Count;h>=1;h--)try{const u=i.ContentControls.Item(h);u&&u.Title&&u.Title.includes(n)&&(u.LockContents=!1,u.Delete(!1),p=!0,console.log(u.Title),t.value+=`<span class="log-item success">已解锁并删除${o?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(u){t.value+=`<span class="log-item warning">访问第${h}个控件时出错: ${u.message}</span><br/>`;continue}}),p?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${o?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(i){t.value+=`<span class="log-item error">删除内容控件失败: ${i.message}</span><br/>`}},ot=n=>{var r;try{const i=window.wps,p=P();if(!p||!p.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const o=(r=a[n])==null?void 0:r.isEnhanced;let h=null;for(let x=1;x<=p.ContentControls.Count;x++)try{const _=p.ContentControls.Item(x);if(_&&_.Title&&_.Title.includes(n)){h=_;break}}catch{continue}if(!h){const x=a[n];x&&(x.status===2||x.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}h.Range.Select();const u=i.Windows.Item(v.value);u&&u.Selection&&u.Selection.Range&&u.ScrollIntoView(u.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${o?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(i){t.value+=`<span class="log-item error">跳转到任务控件失败: ${i.message}</span><br/>`}},ze=async n=>{const r=P(),i=a[n];if(!i){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(i.terminated||i.status!==1)return;if(i.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const p=P();let o=null;if(p&&p.ContentControls)for(let u=1;u<=p.ContentControls.Count;u++){const x=p.ContentControls.Item(u);if(x&&x.Title&&x.Title.includes(n)){o=x;break}}if(!o){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,i.errorMessage&&i.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${i.errorMessage}</span><br/>`;return}return}if(i.errorMessage&&i.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${i.errorMessage}</span><br/>`,ye(n);return}if(!i.resultFile)return;a[n].documentInserted=!0;const h=o.Range;o.LockContents=!1;try{await X("插入结果文件",async()=>{var Se,be,ct;t.value+=`<span class="log-item info">正在自动插入结果文件${i.resultFile}...</span><br/>`;const D=r.Range(h.End,h.End);D.InsertParagraphAfter(),h.Text.includes("\v")&&D.InsertAfter("\v");let W=D.End;const q=(Se=h.Tables)==null?void 0:Se.Count;if(q){const ve=h.Tables.Item(q);((be=ve==null?void 0:ve.Range)==null?void 0:be.End)>W&&(ve.Range.InsertParagraphAfter(),W=(ct=ve==null?void 0:ve.Range)==null?void 0:ct.End)}r.Range(W,W).InsertFile(i.resultFile);for(let ve=1;ve<=p.ContentControls.Count;ve++){const Te=p.ContentControls.Item(ve);if(Te&&Te.Title&&(Te.Title===`任务_${n}`||Te.Title===`任务增强_${n}`)){o=Te;break}}const oe=r.Range(o.Range.End-1,o.Range.End);oe.Text.includes("\r")&&oe.Delete()}),a[n].status=2,Z[n]&&Z[n].urlId&&ee.sendRequest("urlMonitor","updateTaskStatus",{urlId:Z[n].urlId,status:"completed",taskId:n}).then(D=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(D=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${D.message}</span><br/>`});const u=k(i.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${u}</span><br/>`;const x=o.Title===`任务增强_${n}`||i.isEnhanced,_=o.Title===`校对_${n}`||i.isCheckTask;if(o){_?o.Title=`已完成校对_${n}`:x?o.Title=`已完成增强_${n}`:o.Title=`已完成_${n}`;const D=_?"校对":x?"增强":"普通";t.value+=`<span class="log-item info">已将${D}任务${n.substring(0,8)}控件标记为已完成</span><br/>`}}catch(u){a[n].documentInserted=!1,a[n].status=-1;const x=o.Title===`任务增强_${n}`||i.isEnhanced,_=o.Title===`校对_${n}`||i.isCheckTask;if(o){_?o.Title=`异常校对_${n}`:x?o.Title=`异常增强_${n}`:o.Title=`异常_${n}`;const D=_?"校对":x?"增强":"普通";t.value+=`<span class="log-item info">已将${D}任务${n.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${u.message}</span><br/>`}},Ae=async()=>{const n=(p=1e3)=>new Promise(o=>{setTimeout(()=>o(),p)}),r=new Map,i=Object.keys(a).filter(p=>a[p].status===1&&!a[p].terminated);for(i.length?(i.forEach(p=>{r.has(p)||r.set(p,Date.now())}),await Promise.all(i.map(p=>ze(p)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const p=Object.keys(a).filter(o=>a[o].status===1&&!a[o].terminated);p.forEach(o=>{r.has(o)||r.set(o,Date.now());const h=r.get(o);(Date.now()-h)/1e3/60>=5e4&&a[o]&&!a[o].terminated&&(a[o].terminated=!0,a[o].status=-1,t.value+=`<span class="log-item warning">任务 ${o} 执行超过5分钟，已自动终止</span><br/>`,r.delete(o))}),p.length&&await Promise.all(p.map(o=>ze(o)))}},Be=async(n="wps-analysis",r=null)=>{const i=y();if(i){const{primary:o,all:h}=i,{taskId:u,control:x,task:_,isEnhanced:D,overlapType:W}=o,q=h.filter(Y=>Y.task&&(Y.task.status===0||Y.task.status===1));if(q.length>0){const Y=q.map(oe=>oe.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${Y})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${h.length}个已有任务重叠，重叠类型: ${W}</span><br/>`,j();try{await X("删除重叠控件",async()=>{for(const Y of h){const{taskId:oe,control:de,task:Se,isEnhanced:be}=Y;t.value+=`<span class="log-item info">删除重叠的${be?"增强":"普通"}任务 ${oe.substring(0,8)}，状态为 ${b((Se==null?void 0:Se.status)||0)}</span><br/>`,Se&&(Se.status=3,Se.terminated=!0,Se.errorMessage="用户重新创建任务时删除");try{de.LockContents=!1,de.Delete(!1),a[oe]&&(a[oe].placeholderRemoved=!0)}catch(ct){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${ct.message}</span><br/>`}}}),t.value+=`<span class="log-item success">已删除${h.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(Y){return z(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${Y.message}</span><br/>`,Promise.reject(Y)}z()}const p=xs().replace(/-/g,"").substring(0,8);return new Promise(async(o,h)=>{try{const u=window.wps,x=P(),_=x.ActiveWindow.Selection,D=_.Range,W=_.Text||"";if(s.value=W,E(),D){const q=D.Paragraphs,Y=q.Item(1),oe=q.Item(q.Count),de=Y.Range.Start,Se=oe.Range.End,be=D.Start,ct=D.End,ve=x.Range(Math.min(de,be),Math.max(Se,ct));await X("创建内容控件",async()=>{let Te=x.ContentControls.Add(u.Enum.wdContentControlRichText,ve);if(!Te){if(console.log("创建内容控件失败"),Te=x.ContentControls.Add(u.Enum.wdContentControlRichText),!Te)throw t.value+='<span class="log-item error">创建内容控件失败</span><br/>',new Error("创建内容控件失败");D.Cut(),Te.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const Qt=n==="wps-enhance_analysis";Te.Title=Qt?`任务增强_${p}`:`任务_${p}`,Te.LockContents=!0,a[p]||(a[p]={}),a[p].contentControlId=Te.ID,a[p].isEnhanced=Qt,t.value+=`<span class="log-item info">已创建${Qt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const ln=Te.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${ln.length}</span><br/>`})}a[p]={status:0,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:W},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${p}，类型: ${n}</span><br/>`,await Fe(p),r&&r(),a[p]&&(a[p].status=1);try{await pt(p,n)?o():(a[p]&&a[p].status===1&&(a[p].status=-1,a[p].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${p.substring(0,8)}失败</span><br/>`,le(p)),h(new Error("API调用失败或超时")))}catch(q){a[p]&&(a[p].status=-1,a[p].errorMessage=`执行错误: ${q.message}`,t.value+=`<span class="log-item error">任务${p.substring(0,8)}执行出错: ${q.message}</span><br/>`,le(p)),h(q)}}catch(u){h(u)}})},Qe=async(n=null)=>{const r=y();if(r){const{primary:p,all:o}=r,{taskId:h,control:u,task:x,isEnhanced:_,overlapType:D}=p,W=o.filter(q=>q.task&&(q.task.status===0||q.task.status===1));if(W.length>0){const q=W.map(Y=>Y.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${q})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${o.length}个已有任务重叠，重叠类型: ${D}</span><br/>`,j();try{await X("删除重叠校对控件",async()=>{for(const q of o){const{taskId:Y,control:oe,task:de,isEnhanced:Se}=q;t.value+=`<span class="log-item info">删除重叠的校对任务 ${Y.substring(0,8)}，状态为 ${b((de==null?void 0:de.status)||0)}</span><br/>`,de&&(de.status=3,de.terminated=!0,de.errorMessage="用户重新创建校对任务时删除");try{oe.LockContents=!1,oe.Delete(!1),a[Y]&&(a[Y].placeholderRemoved=!0)}catch(be){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${be.message}</span><br/>`}}}),t.value+=`<span class="log-item success">已删除${o.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(q){return z(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${q.message}</span><br/>`,Promise.reject(q)}z()}const i=xs().replace(/-/g,"").substring(0,8);return new Promise(async(p,o)=>{try{const h=window.wps,u=P(),x=u.ActiveWindow.Selection,_=x.Range,D=x.Text||"";if(s.value=D,E(),_){const W=_.Paragraphs,q=W.Item(1),Y=W.Item(W.Count),oe=q.Range.Start,de=Y.Range.End,Se=_.Start,be=_.End,ct=u.Range(Math.min(oe,Se),Math.max(de,be));await X("创建校对控件",async()=>{let ve=u.ContentControls.Add(h.Enum.wdContentControlRichText,ct);if(!ve){if(console.log("创建校对内容控件失败"),ve=u.ContentControls.Add(h.Enum.wdContentControlRichText),!ve)throw t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',new Error("创建校对内容控件失败");_.Cut(),ve.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',ve.Title=`校对_${i}`,ve.LockContents=!0,a[i]||(a[i]={}),a[i].contentControlId=ve.ID,a[i].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const Te=ve.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${Te.length}</span><br/>`})}a[i]={status:0,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:D},t.value+=`<span class="log-item success">创建校对任务: ${i}，类型: wps-check</span><br/>`,await Fe(i),n&&n(),a[i]&&(a[i].status=1);try{await pt(i,"wps-check")?p():(a[i]&&a[i].status===1&&(a[i].status=-1,a[i].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}失败</span><br/>`,le(i)),o(new Error("校对API调用失败或超时")))}catch(W){a[i]&&(a[i].status=-1,a[i].errorMessage=`校对执行错误: ${W.message}`,t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}执行出错: ${W.message}</span><br/>`,le(i)),o(W)}}catch(h){o(h)}})},qe=async()=>{},et=()=>lt()>0?"status-error":it()>0?"status-running":tt()>0?"status-completed":"",it=()=>Object.values(a).filter(n=>n.status===0||n.status===1).length,tt=()=>Object.values(a).filter(n=>n.status===2).length,ft=()=>Object.values(a).filter(n=>n.status===2||n.status===4).length,lt=()=>Object.values(a).filter(n=>n.status===-1).length,It=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,r=P();if(!r){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let i=0;if(Object.keys(a).forEach(p=>{try{a[p].status===1&&(a[p].status=3,a[p].terminated=!0,a[p].errorMessage="强制清除"),ye(p),i++}catch(o){t.value+=`<span class="log-item warning">清除任务${p.substring(0,8)}失败: ${o.message}</span><br/>`}}),r.ContentControls&&r.ContentControls.Count>0)for(let p=r.ContentControls.Count;p>=1;p--)try{const o=r.ContentControls.Item(p);if(o&&o.Title&&(o.Title.startsWith("任务_")||o.Title.startsWith("任务增强_")||o.Title.startsWith("已完成_")||o.Title.startsWith("已完成增强_")||o.Title.startsWith("异常_")||o.Title.startsWith("异常增强_")||o.Title.startsWith("已停止_")||o.Title.startsWith("已停止增强_")))try{o.LockContents=!1,o.Delete(!1);let h;o.Title.startsWith("任务增强_")?h=o.Title.substring(5):o.Title.startsWith("任务_")?h=o.Title.substring(3):o.Title.startsWith("已完成增强_")?h=o.Title.substring(6):o.Title.startsWith("已完成_")?h=o.Title.substring(4):o.Title.startsWith("异常增强_")?h=o.Title.substring(5):o.Title.startsWith("异常_")?h=o.Title.substring(3):o.Title.startsWith("已停止增强_")?h=o.Title.substring(6):o.Title.startsWith("已停止_")&&(h=o.Title.substring(4)),a[h]?(a[h].placeholderRemoved=!0,a[h].status=3):a[h]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},i++,t.value+=`<span class="log-item success">已删除任务${h.substring(0,8)}的内容控件</span><br/>`}catch(h){t.value+=`<span class="log-item error">删除控件失败: ${h.message}</span><br/>`}}catch(o){t.value+=`<span class="log-item warning">访问控件时出错: ${o.message}</span><br/>`}i>0?t.value+=`<span class="log-item success">已清除${i}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},Tt=async()=>{try{const n=Object.values(Z).map(r=>r.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const r of n)await ke(r)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Ct=()=>{Ws(async()=>{try{const r=window.Application.PluginStorage.getItem("user_info");if(!r)throw new Error("未找到用户信息");const i=JSON.parse(r);if(!i.orgs||!i.orgs[0])throw new Error("未找到组织信息");$.appKey=i.appKey,$.userName=i.nickname,$.userId=i.userId,$.appSecret=i.appSecret}catch(r){t.value+=`<span class="log-item error">初始化appKey失败: ${r.message}</span><br/>`}await ie(),Yt([H,I],async()=>{await f()},{immediate:!1}),await M();const n=Ke.onVersionChange(()=>{const r=se();ne.splice(0,ne.length,...r),Ke.isSeniorEdition()&&I.value==="junior"?I.value="senior":!Ke.isSeniorEdition()&&I.value==="senior"&&(I.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Ke.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',R(),await Ze(),je(),$t(),Ae(),window.addEventListener("beforeunload",Tt),()=>{A&&clearTimeout(A),n&&n()}}),Yt(t,n=>{A&&clearTimeout(A),A=setTimeout(()=>{Pe(n)},10)},{immediate:!1})},$t=()=>{ee.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==H.value&&(H.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${H.value}</span><br/>`),n.data.stage!==I.value&&(I.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${I.value}</span><br/>`))})},ht=he(!1),y=()=>{try{const n=P(),r=n.ActiveWindow.Selection;if(!r||!n||!n.ContentControls)return null;const i=r.Range,p=[];for(let o=1;o<=n.ContentControls.Count;o++)try{const h=n.ContentControls.Item(o);if(!h)continue;const u=(h.Title||"").trim(),x=h.Range;if(i.Start<x.End&&i.End>x.Start){let _=null,D=!1,W=!1;if(!u)W=!0,_=`empty_${h.ID||Date.now()}`;else if(u.startsWith("任务_")||u.startsWith("任务增强_")||u.startsWith("校对_")||u.startsWith("已完成_")||u.startsWith("已完成增强_")||u.startsWith("已完成校对_")||u.startsWith("异常_")||u.startsWith("异常增强_")||u.startsWith("异常校对_")||u.startsWith("已停止_")||u.startsWith("已停止增强_")||u.startsWith("已停止校对_"))u.startsWith("任务增强_")?(_=u.substring(5),D=!0):u.startsWith("任务_")||u.startsWith("校对_")?_=u.substring(3):u.startsWith("已完成增强_")?(_=u.substring(6),D=!0):u.startsWith("已完成校对_")?_=u.substring(6):u.startsWith("已完成_")?_=u.substring(4):u.startsWith("异常增强_")?(_=u.substring(5),D=!0):u.startsWith("异常校对_")?_=u.substring(5):u.startsWith("异常_")?_=u.substring(3):u.startsWith("已停止增强_")?(_=u.substring(6),D=!0):u.startsWith("已停止校对_")?_=u.substring(6):u.startsWith("已停止_")&&(_=u.substring(4));else continue;if(_){let q;i.Start>=x.Start&&i.End<=x.End?q="completely_within":i.Start<=x.Start&&i.End>=x.End?q="completely_contains":q="partial_overlap",p.push({taskId:_,control:h,task:W?null:a[_]||null,isEnhanced:D,isEmptyTitle:W,overlapType:q,controlRange:{start:x.Start,end:x.End},selectionRange:{start:i.Start,end:i.End}})}}}catch{continue}return p.length===0?null:{primary:p[0],all:p}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},j=()=>{ht.value=!0},z=()=>{ht.value=!1},le=async(n,r=!1)=>{j();try{await ye(n,r)}finally{z()}},re=n=>new Promise((r,i)=>{O.show=!0,O.message=n,O.resolveCallback=r,O.rejectCallback=i}),xe=n=>{O.show=!1,n&&O.resolveCallback?O.resolveCallback(!0):O.resolveCallback&&O.resolveCallback(!1),O.resolveCallback=null,O.rejectCallback=null},$e=(n,r,i="error")=>{U.show=!0,U.title=n,U.message=r,U.type=i},Je=()=>{U.show=!1,U.title="",U.message="",U.type="error"},M=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await ee.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(H.value=n.data.subject),n.data.stage&&(I.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${H.value})和年级(${I.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},f=async()=>{try{if(H.value||I.value){t.value+=`<span class="log-item info">正在保存学科(${H.value})和年级(${I.value})设置到服务器...</span><br/>`;const n=await ee.setSubjectAndStage(H.value,I.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}},N=async()=>{try{B.value=2,K.value=B.value===2,K.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${B.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${B.value}）</span><br/>`}catch(n){console.error("检查企业ID失败:",n),t.value+=`<span class="log-item error">检查企业ID失败: ${n.message}</span><br/>`,K.value=!1}},Q=async(n,r)=>await X("为范围添加批注",async()=>{const i=P();if(!i||!n)return!1;const p=i.Comments.Add(n,r);try{p!=null&&p.Range&&(p.Range.ParagraphFormat.Reset(),p.Range.ParagraphFormat.LineSpacingRule=3,p.Range.ParagraphFormat.LineSpacing=10,p.Edit())}catch(o){t.value+=`<span class="log-item warning">设置批注段落格式失败: ${o.message}</span><br/>`}return!0}),ue=async(n,r)=>{try{if(!n||!Array.isArray(n))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const i=P();let p=null,o=null;if(i&&i.ContentControls)for(let _=1;_<=i.ContentControls.Count;_++)try{const D=i.ContentControls.Item(_);if((D==null?void 0:D.Title)===`校对_${r}`){p=D,o=D.Range,t.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!o){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const _=a[r];if(_&&_.selectedText)try{const D=i.Range().Find;if(D.ClearFormatting(),D.Text=_.selectedText.substring(0,Math.min(_.selectedText.length,100)),D.Forward=!0,D.Wrap=1,D.Execute()){const W=D.Parent;if(_.selectedText.length>100){const q=W.Start+_.selectedText.length;o=i.Range(W.Start,Math.min(q,i.Range().End))}else o=W;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(D){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${D.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let h=0,u=0,x=[];for(const _ of n){if(!_.mode1||!Array.isArray(_.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const D of _.mode1){if(!D.error_info||!Array.isArray(D.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const W=D.quest_html||"",q=D.quest_type||"";t.value+=`<span class="log-item info">处理${q}题目，发现${D.error_info.length}个错误信息</span><br/>`;for(const Y of D.error_info)try{let oe="";if(Y.error_info&&(oe+=`【错误类型】${Y.error_info}`),Y.fix_info&&(oe+=`\r【修正建议】${Y.fix_info}`),Y.keywords&&Y.keywords.trim()){let de=p?p.Range:o;p.LockContents=!1,fe(oe,Y.keywords.trim(),0,p.Range)?(h++,t.value+=`<span class="log-item success">已为关键词"${Y.keywords.trim()}"添加批注: ${oe}</span><br/>`):(x.push({comment:oe,keyword:Y.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${Y.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else x.push({comment:oe,keyword:null})}catch(oe){u++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${oe.message}</span><br/>`}}}if(x.length>0){t.value+=`<span class="log-item info">为整个控件范围添加${x.length}个批注</span><br/>`;for(const _ of x)try{let D=_.comment,W=p?p.Range:o;p.LockContents=!1,Q(p.Range,D)?(h++,t.value+=`<span class="log-item success">已为整个范围添加批注${_.keyword?`（关键词：${_.keyword}）`:""}</span><br/>`):u++}catch(D){u++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${D.message}</span><br/>`}}return h>0?(t.value+=`<span class="log-item success">校对任务${r.substring(0,8)}处理完成：成功添加${h}个批注</span><br/>`,u>0&&(t.value+=`<span class="log-item warning">校对任务${r.substring(0,8)}：${u}个批注添加失败</span><br/>`),p.Title=`已完成校对_${r}`,!0):(t.value+=`<span class="log-item error">校对任务${r.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(i){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${i.message}</span><br/>`,!1}},X=async(n,r)=>{const i=window.Application;if(!i)throw new Error("无法获取 Application 对象");const p=i.ScreenUpdating;try{i.ScreenUpdating=!1;const o=P();return o&&o.UndoRecord&&o.UndoRecord.StartCustomRecord(n),await r()}catch(o){throw t.value+=`<span class="log-item error">${n}操作失败: ${o.message}</span><br/>`,o}finally{try{i.ScreenUpdating=p!==!1;const o=P();o&&o.UndoRecord&&o.UndoRecord.EndCustomRecord()}catch(o){t.value+=`<span class="log-item warning">清理VBA操作状态时出错: ${o.message}</span><br/>`}}};return{docName:e,selected:s,logger:t,map:a,watchedDir:w,subject:H,stage:I,subjectOptions:V,stageOptions:ne,fetchWatchedDir:ie,clearLog:te,getCurrentDocument:P,checkDocumentFormat:ce,getTaskStatusClass:g,getTaskStatusText:b,getElapsedTime:k,terminateTask:S,stopTaskWithoutRemovingControl:T,run1:Be,run2:qe,runCheck:Qe,getHeaderStatusClass:et,getRunningTasksCount:it,getCompletedTasksCount:tt,getReleasableTasksCount:ft,getErrorTasksCount:lt,setupLifecycle:Ct,navigateToTaskControl:ot,forceCleanAllTasks:It,ws:De,wsMessages:Re,initWebSocket:je,handleWatcherEvent:We,urlMonitorTasks:Z,monitorUrlForTask:Ue,stopUrlMonitoring:ke,getUrlMonitorStatus:rt,forceUrlCheck:Ge,cleanupUrlMonitoringTasks:Tt,tryRemoveTaskPlaceholder:ye,isLoading:ht,isSelectionInTaskControl:y,tryRemoveTaskPlaceholderWithLoading:le,showConfirm:re,handleConfirm:xe,confirmDialog:O,errorDialog:U,showErrorDialog:$e,hideErrorDialog:Je,loadSubjectAndStage:M,saveSubjectAndStage:f,enterpriseId:B,isCheckingVisible:K,checkEnterpriseAndSetCheckingVisibility:N,processCheckingJson:ue}}const Sn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Ke.getVersionConfig(),selectedEdition:Ke.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),l=Math.floor(t%(1e3*60*60)/(1e3*60)),v=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${l}分 ${v}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await ee.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await ee[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await ee.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await ee.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await ee.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await ee.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await ee.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await ee.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await ee.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Ke.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await un()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await ee.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await ee.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await ee.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await ee.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=ee.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=ee.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=ee.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Ke.onVersionChange(e=>{this.versionConfig=Ke.getVersionConfig(),this.selectedEdition=Ke.getEdition()}),await ee.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},_n={class:"file-watcher"},En={class:"settings-modal"},An={class:"modal-content"},Mn={class:"modal-header"},Dn={class:"version-tag"},Pn={key:0,class:"user-info"},On={key:1,class:"user-info inner-tag"},In={class:"header-actions"},Rn={class:"modal-body"},Un={class:"status-section"},Ln={class:"status-item"},Fn={key:0,class:"status-item"},jn={class:"directory-section"},Wn={class:"directory-form"},Bn={class:"radio-group"},Nn={class:"radio-item"},Vn={class:"radio-item"},Hn={class:"radio-item"},zn={key:0,class:"radio-item"},qn={key:1,class:"directory-section"},Jn={class:"directory-form"},Yn={class:"form-group"},Kn=["placeholder"],Xn=["disabled"],Gn={key:2,class:"directory-section"},Zn={class:"directory-form"},Qn={class:"form-group"},ea=["placeholder"],ta=["disabled"],sa={key:3,class:"directory-section"},na={class:"directory-form"},aa={class:"form-group"},ra=["placeholder"],oa=["disabled"],ia={key:4,class:"events-section"},la={class:"events-list"},ca={class:"event-time"},ua={class:"event-message"},da={key:1,class:"modal-footer"};function pa(e,s,t,a,l,v){var C,A,w,$,O,U,H,I,B,K,V,se,ne,ie,d,te,P,ce,fe,g,b,k,T,S;return L(),F("div",_n,[c("div",En,[c("div",An,[c("div",Mn,[c("h3",null,[dn(ae(l.versionConfig.shortName)+"设置 ",1),c("span",Dn,ae(l.versionConfig.appVersion),1),v.userInfo?(L(),F("span",Pn,"欢迎您，"+ae(v.userInfo.nickname),1)):J("",!0),((w=(A=(C=v.userInfo)==null?void 0:C.orgs)==null?void 0:A[0])==null?void 0:w.orgId)===2?(L(),F("span",On,"内部版本号：1.1.25")):J("",!0)]),c("div",In,[c("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>v.handleLogout&&v.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[c("span",{class:"icon-logout"},null,-1)])),c("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),c("div",Rn,[c("div",Un,[c("div",Ln,[s[22]||(s[22]=c("span",{class:"label"},"状态：",-1)),c("span",{class:_e(["status-badge",l.status.status])},ae(l.status.status==="running"?"运行中":"已停止"),3)]),((U=(O=($=v.userInfo)==null?void 0:$.orgs)==null?void 0:O[0])==null?void 0:U.orgId)===2?(L(),F("div",Fn,[s[23]||(s[23]=c("span",{class:"label"},"本次上传：",-1)),c("span",null,ae(l.status.processedFiles||0)+" 个文件",1)])):J("",!0),c("div",jn,[s[28]||(s[28]=c("h4",null,"保存方式设置",-1)),c("div",Wn,[c("div",Bn,[c("label",Nn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>l.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,l.currentSaveMethod]]),s[24]||(s[24]=c("span",{class:"radio-label"},"方式一",-1))]),c("label",Vn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>l.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,l.currentSaveMethod]]),s[25]||(s[25]=c("span",{class:"radio-label"},"方式二 (默认)",-1))]),c("label",Hn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>l.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,l.currentSaveMethod]]),s[26]||(s[26]=c("span",{class:"radio-label"},"方式三",-1))]),((B=(I=(H=v.userInfo)==null?void 0:H.orgs)==null?void 0:I[0])==null?void 0:B.orgId)===2?(L(),F("label",zn,[Ye(c("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>l.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[Ht,l.currentSaveMethod]]),s[27]||(s[27]=c("span",{class:"radio-label"},"方式四",-1))])):J("",!0)]),l.saveMethodUpdateMessage?(L(),F("div",{key:0,class:_e(["update-message",l.saveMethodUpdateSuccess?"success":"error"])},ae(l.saveMethodUpdateMessage),3)):J("",!0)])])]),J("",!0),((se=(V=(K=v.userInfo)==null?void 0:K.orgs)==null?void 0:V[0])==null?void 0:se.orgId)===2?(L(),F("div",qn,[s[32]||(s[32]=c("h4",null,"上传目录设置",-1)),c("div",Jn,[c("div",Yn,[s[31]||(s[31]=c("span",{class:"label"},"路径：",-1)),Ye(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>l.newWatchDir=m),placeholder:l.status.watchDir||"C:\\Temp"},null,8,Kn),[[es,l.newWatchDir]]),c("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>v.updateWatchDir&&v.updateWatchDir(...m)),disabled:l.isUpdating||!l.newWatchDir},ae(l.isUpdating?"更新中...":"更新目录"),9,Xn)]),l.updateMessage?(L(),F("div",{key:0,class:_e(["update-message",l.updateSuccess?"success":"error"])},ae(l.updateMessage),3)):J("",!0)])])):J("",!0),((d=(ie=(ne=v.userInfo)==null?void 0:ne.orgs)==null?void 0:ie[0])==null?void 0:d.orgId)===2?(L(),F("div",Gn,[s[34]||(s[34]=c("h4",null,"下载目录设置",-1)),c("div",Zn,[c("div",Qn,[s[33]||(s[33]=c("span",{class:"label"},"路径：",-1)),Ye(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>l.newDownloadPath=m),placeholder:l.downloadPath||"C:\\Temp\\Downloads"},null,8,ea),[[es,l.newDownloadPath]]),c("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>v.updateDownloadPath&&v.updateDownloadPath(...m)),disabled:l.isUpdatingDownloadPath||!l.newDownloadPath},ae(l.isUpdatingDownloadPath?"更新中...":"更新路径"),9,ta)]),l.downloadPathUpdateMessage?(L(),F("div",{key:0,class:_e(["update-message",l.downloadPathUpdateSuccess?"success":"error"])},ae(l.downloadPathUpdateMessage),3)):J("",!0)])])):J("",!0),((ce=(P=(te=v.userInfo)==null?void 0:te.orgs)==null?void 0:P[0])==null?void 0:ce.orgId)===2?(L(),F("div",sa,[s[36]||(s[36]=c("h4",null,"配置目录设置",-1)),c("div",na,[c("div",aa,[s[35]||(s[35]=c("span",{class:"label"},"路径：",-1)),Ye(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>l.newAddonConfigPath=m),placeholder:l.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,ra),[[es,l.newAddonConfigPath]]),c("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>v.updateAddonConfigPath&&v.updateAddonConfigPath(...m)),disabled:l.isUpdatingAddonConfigPath||!l.newAddonConfigPath},ae(l.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,oa)]),l.addonConfigPathUpdateMessage?(L(),F("div",{key:0,class:_e(["update-message",l.addonConfigPathUpdateSuccess?"success":"error"])},ae(l.addonConfigPathUpdateMessage),3)):J("",!0)])])):J("",!0),((b=(g=(fe=v.userInfo)==null?void 0:fe.orgs)==null?void 0:g[0])==null?void 0:b.orgId)===2?(L(),F("div",ia,[s[37]||(s[37]=c("h4",null,"最近事件",-1)),c("div",la,[(L(!0),F(bt,null,Et(l.recentEvents,(m,R)=>(L(),F("div",{key:R,class:"event-item"},[c("span",ca,ae(v.formatTime(m.timestamp)),1),c("span",{class:_e(["event-type",m.eventType])},ae(v.getEventTypeText(m.eventType)),3),c("span",ua,ae(v.getEventMessage(m)),1)]))),128))])])):J("",!0)]),J("",!0),((S=(T=(k=v.userInfo)==null?void 0:k.orgs)==null?void 0:T[0])==null?void 0:S.orgId)===2?(L(),F("div",da,[c("button",{class:_e(["control-btn",l.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>v.controlService&&v.controlService(...m))},ae(l.status.status==="running"?"停止服务":"启动服务"),3)])):J("",!0)])])])}const fa=Bs(Sn,[["render",pa],["__scopeId","data-v-81dfa9af"]]);var Oe="top",Ve="bottom",He="right",Ie="left",us="auto",Nt=[Oe,Ve,He,Ie],Mt="start",Wt="end",ha="clippingParents",Vs="viewport",Rt="popper",ga="reference",ks=Nt.reduce(function(e,s){return e.concat([s+"-"+Mt,s+"-"+Wt])},[]),Hs=[].concat(Nt,[us]).reduce(function(e,s){return e.concat([s,s+"-"+Mt,s+"-"+Wt])},[]),va="beforeRead",ma="read",ba="afterRead",wa="beforeMain",ya="main",xa="afterMain",ka="beforeWrite",Ta="write",Ca="afterWrite",$a=[va,ma,ba,wa,ya,xa,ka,Ta,Ca];function at(e){return e?(e.nodeName||"").toLowerCase():null}function Le(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function kt(e){var s=Le(e).Element;return e instanceof s||e instanceof Element}function Ne(e){var s=Le(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function ds(e){if(typeof ShadowRoot>"u")return!1;var s=Le(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function Sa(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},l=s.attributes[t]||{},v=s.elements[t];!Ne(v)||!at(v)||(Object.assign(v.style,a),Object.keys(l).forEach(function(C){var A=l[C];A===!1?v.removeAttribute(C):v.setAttribute(C,A===!0?"":A)}))})}function _a(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var l=s.elements[a],v=s.attributes[a]||{},C=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),A=C.reduce(function(w,$){return w[$]="",w},{});!Ne(l)||!at(l)||(Object.assign(l.style,A),Object.keys(v).forEach(function(w){l.removeAttribute(w)}))})}}const zs={name:"applyStyles",enabled:!0,phase:"write",fn:Sa,effect:_a,requires:["computeStyles"]};function nt(e){return e.split("-")[0]}var yt=Math.max,Kt=Math.min,Dt=Math.round;function os(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function qs(){return!/^((?!chrome|android).)*safari/i.test(os())}function Pt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),l=1,v=1;s&&Ne(e)&&(l=e.offsetWidth>0&&Dt(a.width)/e.offsetWidth||1,v=e.offsetHeight>0&&Dt(a.height)/e.offsetHeight||1);var C=kt(e)?Le(e):window,A=C.visualViewport,w=!qs()&&t,$=(a.left+(w&&A?A.offsetLeft:0))/l,O=(a.top+(w&&A?A.offsetTop:0))/v,U=a.width/l,H=a.height/v;return{width:U,height:H,top:O,right:$+U,bottom:O+H,left:$,x:$,y:O}}function ps(e){var s=Pt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function Js(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&ds(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function dt(e){return Le(e).getComputedStyle(e)}function Ea(e){return["table","td","th"].indexOf(at(e))>=0}function vt(e){return((kt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Gt(e){return at(e)==="html"?e:e.assignedSlot||e.parentNode||(ds(e)?e.host:null)||vt(e)}function Ts(e){return!Ne(e)||dt(e).position==="fixed"?null:e.offsetParent}function Aa(e){var s=/firefox/i.test(os()),t=/Trident/i.test(os());if(t&&Ne(e)){var a=dt(e);if(a.position==="fixed")return null}var l=Gt(e);for(ds(l)&&(l=l.host);Ne(l)&&["html","body"].indexOf(at(l))<0;){var v=dt(l);if(v.transform!=="none"||v.perspective!=="none"||v.contain==="paint"||["transform","perspective"].indexOf(v.willChange)!==-1||s&&v.willChange==="filter"||s&&v.filter&&v.filter!=="none")return l;l=l.parentNode}return null}function Vt(e){for(var s=Le(e),t=Ts(e);t&&Ea(t)&&dt(t).position==="static";)t=Ts(t);return t&&(at(t)==="html"||at(t)==="body"&&dt(t).position==="static")?s:t||Aa(e)||s}function fs(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Lt(e,s,t){return yt(e,Kt(s,t))}function Ma(e,s,t){var a=Lt(e,s,t);return a>t?t:a}function Ys(){return{top:0,right:0,bottom:0,left:0}}function Ks(e){return Object.assign({},Ys(),e)}function Xs(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var Da=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Ks(typeof s!="number"?s:Xs(s,Nt))};function Pa(e){var s,t=e.state,a=e.name,l=e.options,v=t.elements.arrow,C=t.modifiersData.popperOffsets,A=nt(t.placement),w=fs(A),$=[Ie,He].indexOf(A)>=0,O=$?"height":"width";if(!(!v||!C)){var U=Da(l.padding,t),H=ps(v),I=w==="y"?Oe:Ie,B=w==="y"?Ve:He,K=t.rects.reference[O]+t.rects.reference[w]-C[w]-t.rects.popper[O],V=C[w]-t.rects.reference[w],se=Vt(v),ne=se?w==="y"?se.clientHeight||0:se.clientWidth||0:0,ie=K/2-V/2,d=U[I],te=ne-H[O]-U[B],P=ne/2-H[O]/2+ie,ce=Lt(d,P,te),fe=w;t.modifiersData[a]=(s={},s[fe]=ce,s.centerOffset=ce-P,s)}}function Oa(e){var s=e.state,t=e.options,a=t.element,l=a===void 0?"[data-popper-arrow]":a;l!=null&&(typeof l=="string"&&(l=s.elements.popper.querySelector(l),!l)||Js(s.elements.popper,l)&&(s.elements.arrow=l))}const Ia={name:"arrow",enabled:!0,phase:"main",fn:Pa,effect:Oa,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ot(e){return e.split("-")[1]}var Ra={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ua(e,s){var t=e.x,a=e.y,l=s.devicePixelRatio||1;return{x:Dt(t*l)/l||0,y:Dt(a*l)/l||0}}function Cs(e){var s,t=e.popper,a=e.popperRect,l=e.placement,v=e.variation,C=e.offsets,A=e.position,w=e.gpuAcceleration,$=e.adaptive,O=e.roundOffsets,U=e.isFixed,H=C.x,I=H===void 0?0:H,B=C.y,K=B===void 0?0:B,V=typeof O=="function"?O({x:I,y:K}):{x:I,y:K};I=V.x,K=V.y;var se=C.hasOwnProperty("x"),ne=C.hasOwnProperty("y"),ie=Ie,d=Oe,te=window;if($){var P=Vt(t),ce="clientHeight",fe="clientWidth";if(P===Le(t)&&(P=vt(t),dt(P).position!=="static"&&A==="absolute"&&(ce="scrollHeight",fe="scrollWidth")),P=P,l===Oe||(l===Ie||l===He)&&v===Wt){d=Ve;var g=U&&P===te&&te.visualViewport?te.visualViewport.height:P[ce];K-=g-a.height,K*=w?1:-1}if(l===Ie||(l===Oe||l===Ve)&&v===Wt){ie=He;var b=U&&P===te&&te.visualViewport?te.visualViewport.width:P[fe];I-=b-a.width,I*=w?1:-1}}var k=Object.assign({position:A},$&&Ra),T=O===!0?Ua({x:I,y:K},Le(t)):{x:I,y:K};if(I=T.x,K=T.y,w){var S;return Object.assign({},k,(S={},S[d]=ne?"0":"",S[ie]=se?"0":"",S.transform=(te.devicePixelRatio||1)<=1?"translate("+I+"px, "+K+"px)":"translate3d("+I+"px, "+K+"px, 0)",S))}return Object.assign({},k,(s={},s[d]=ne?K+"px":"",s[ie]=se?I+"px":"",s.transform="",s))}function La(e){var s=e.state,t=e.options,a=t.gpuAcceleration,l=a===void 0?!0:a,v=t.adaptive,C=v===void 0?!0:v,A=t.roundOffsets,w=A===void 0?!0:A,$={placement:nt(s.placement),variation:Ot(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:l,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,Cs(Object.assign({},$,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:C,roundOffsets:w})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,Cs(Object.assign({},$,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:w})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Fa={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:La,data:{}};var zt={passive:!0};function ja(e){var s=e.state,t=e.instance,a=e.options,l=a.scroll,v=l===void 0?!0:l,C=a.resize,A=C===void 0?!0:C,w=Le(s.elements.popper),$=[].concat(s.scrollParents.reference,s.scrollParents.popper);return v&&$.forEach(function(O){O.addEventListener("scroll",t.update,zt)}),A&&w.addEventListener("resize",t.update,zt),function(){v&&$.forEach(function(O){O.removeEventListener("scroll",t.update,zt)}),A&&w.removeEventListener("resize",t.update,zt)}}const Wa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ja,data:{}};var Ba={left:"right",right:"left",bottom:"top",top:"bottom"};function Jt(e){return e.replace(/left|right|bottom|top/g,function(s){return Ba[s]})}var Na={start:"end",end:"start"};function $s(e){return e.replace(/start|end/g,function(s){return Na[s]})}function hs(e){var s=Le(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function gs(e){return Pt(vt(e)).left+hs(e).scrollLeft}function Va(e,s){var t=Le(e),a=vt(e),l=t.visualViewport,v=a.clientWidth,C=a.clientHeight,A=0,w=0;if(l){v=l.width,C=l.height;var $=qs();($||!$&&s==="fixed")&&(A=l.offsetLeft,w=l.offsetTop)}return{width:v,height:C,x:A+gs(e),y:w}}function Ha(e){var s,t=vt(e),a=hs(e),l=(s=e.ownerDocument)==null?void 0:s.body,v=yt(t.scrollWidth,t.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),C=yt(t.scrollHeight,t.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),A=-a.scrollLeft+gs(e),w=-a.scrollTop;return dt(l||t).direction==="rtl"&&(A+=yt(t.clientWidth,l?l.clientWidth:0)-v),{width:v,height:C,x:A,y:w}}function vs(e){var s=dt(e),t=s.overflow,a=s.overflowX,l=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+l+a)}function Gs(e){return["html","body","#document"].indexOf(at(e))>=0?e.ownerDocument.body:Ne(e)&&vs(e)?e:Gs(Gt(e))}function Ft(e,s){var t;s===void 0&&(s=[]);var a=Gs(e),l=a===((t=e.ownerDocument)==null?void 0:t.body),v=Le(a),C=l?[v].concat(v.visualViewport||[],vs(a)?a:[]):a,A=s.concat(C);return l?A:A.concat(Ft(Gt(C)))}function is(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function za(e,s){var t=Pt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Ss(e,s,t){return s===Vs?is(Va(e,t)):kt(s)?za(s,t):is(Ha(vt(e)))}function qa(e){var s=Ft(Gt(e)),t=["absolute","fixed"].indexOf(dt(e).position)>=0,a=t&&Ne(e)?Vt(e):e;return kt(a)?s.filter(function(l){return kt(l)&&Js(l,a)&&at(l)!=="body"}):[]}function Ja(e,s,t,a){var l=s==="clippingParents"?qa(e):[].concat(s),v=[].concat(l,[t]),C=v[0],A=v.reduce(function(w,$){var O=Ss(e,$,a);return w.top=yt(O.top,w.top),w.right=Kt(O.right,w.right),w.bottom=Kt(O.bottom,w.bottom),w.left=yt(O.left,w.left),w},Ss(e,C,a));return A.width=A.right-A.left,A.height=A.bottom-A.top,A.x=A.left,A.y=A.top,A}function Zs(e){var s=e.reference,t=e.element,a=e.placement,l=a?nt(a):null,v=a?Ot(a):null,C=s.x+s.width/2-t.width/2,A=s.y+s.height/2-t.height/2,w;switch(l){case Oe:w={x:C,y:s.y-t.height};break;case Ve:w={x:C,y:s.y+s.height};break;case He:w={x:s.x+s.width,y:A};break;case Ie:w={x:s.x-t.width,y:A};break;default:w={x:s.x,y:s.y}}var $=l?fs(l):null;if($!=null){var O=$==="y"?"height":"width";switch(v){case Mt:w[$]=w[$]-(s[O]/2-t[O]/2);break;case Wt:w[$]=w[$]+(s[O]/2-t[O]/2);break}}return w}function Bt(e,s){s===void 0&&(s={});var t=s,a=t.placement,l=a===void 0?e.placement:a,v=t.strategy,C=v===void 0?e.strategy:v,A=t.boundary,w=A===void 0?ha:A,$=t.rootBoundary,O=$===void 0?Vs:$,U=t.elementContext,H=U===void 0?Rt:U,I=t.altBoundary,B=I===void 0?!1:I,K=t.padding,V=K===void 0?0:K,se=Ks(typeof V!="number"?V:Xs(V,Nt)),ne=H===Rt?ga:Rt,ie=e.rects.popper,d=e.elements[B?ne:H],te=Ja(kt(d)?d:d.contextElement||vt(e.elements.popper),w,O,C),P=Pt(e.elements.reference),ce=Zs({reference:P,element:ie,strategy:"absolute",placement:l}),fe=is(Object.assign({},ie,ce)),g=H===Rt?fe:P,b={top:te.top-g.top+se.top,bottom:g.bottom-te.bottom+se.bottom,left:te.left-g.left+se.left,right:g.right-te.right+se.right},k=e.modifiersData.offset;if(H===Rt&&k){var T=k[l];Object.keys(b).forEach(function(S){var m=[He,Ve].indexOf(S)>=0?1:-1,R=[Oe,Ve].indexOf(S)>=0?"y":"x";b[S]+=T[R]*m})}return b}function Ya(e,s){s===void 0&&(s={});var t=s,a=t.placement,l=t.boundary,v=t.rootBoundary,C=t.padding,A=t.flipVariations,w=t.allowedAutoPlacements,$=w===void 0?Hs:w,O=Ot(a),U=O?A?ks:ks.filter(function(B){return Ot(B)===O}):Nt,H=U.filter(function(B){return $.indexOf(B)>=0});H.length===0&&(H=U);var I=H.reduce(function(B,K){return B[K]=Bt(e,{placement:K,boundary:l,rootBoundary:v,padding:C})[nt(K)],B},{});return Object.keys(I).sort(function(B,K){return I[B]-I[K]})}function Ka(e){if(nt(e)===us)return[];var s=Jt(e);return[$s(e),s,$s(s)]}function Xa(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var l=t.mainAxis,v=l===void 0?!0:l,C=t.altAxis,A=C===void 0?!0:C,w=t.fallbackPlacements,$=t.padding,O=t.boundary,U=t.rootBoundary,H=t.altBoundary,I=t.flipVariations,B=I===void 0?!0:I,K=t.allowedAutoPlacements,V=s.options.placement,se=nt(V),ne=se===V,ie=w||(ne||!B?[Jt(V)]:Ka(V)),d=[V].concat(ie).reduce(function(Ce,Ee){return Ce.concat(nt(Ee)===us?Ya(s,{placement:Ee,boundary:O,rootBoundary:U,padding:$,flipVariations:B,allowedAutoPlacements:K}):Ee)},[]),te=s.rects.reference,P=s.rects.popper,ce=new Map,fe=!0,g=d[0],b=0;b<d.length;b++){var k=d[b],T=nt(k),S=Ot(k)===Mt,m=[Oe,Ve].indexOf(T)>=0,R=m?"width":"height",E=Bt(s,{placement:k,boundary:O,rootBoundary:U,altBoundary:H,padding:$}),G=m?S?He:Ie:S?Ve:Oe;te[R]>P[R]&&(G=Jt(G));var ge=Jt(G),we=[];if(v&&we.push(E[T]<=0),A&&we.push(E[G]<=0,E[ge]<=0),we.every(function(Ce){return Ce})){g=k,fe=!1;break}ce.set(k,we)}if(fe)for(var Me=B?3:1,Fe=function(Ee){var Pe=d.find(function(je){var Z=ce.get(je);if(Z)return Z.slice(0,Ee).every(function(Ue){return Ue})});if(Pe)return g=Pe,"break"},De=Me;De>0;De--){var Re=Fe(De);if(Re==="break")break}s.placement!==g&&(s.modifiersData[a]._skip=!0,s.placement=g,s.reset=!0)}}const Ga={name:"flip",enabled:!0,phase:"main",fn:Xa,requiresIfExists:["offset"],data:{_skip:!1}};function _s(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function Es(e){return[Oe,He,Ve,Ie].some(function(s){return e[s]>=0})}function Za(e){var s=e.state,t=e.name,a=s.rects.reference,l=s.rects.popper,v=s.modifiersData.preventOverflow,C=Bt(s,{elementContext:"reference"}),A=Bt(s,{altBoundary:!0}),w=_s(C,a),$=_s(A,l,v),O=Es(w),U=Es($);s.modifiersData[t]={referenceClippingOffsets:w,popperEscapeOffsets:$,isReferenceHidden:O,hasPopperEscaped:U},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":O,"data-popper-escaped":U})}const Qa={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Za};function er(e,s,t){var a=nt(e),l=[Ie,Oe].indexOf(a)>=0?-1:1,v=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,C=v[0],A=v[1];return C=C||0,A=(A||0)*l,[Ie,He].indexOf(a)>=0?{x:A,y:C}:{x:C,y:A}}function tr(e){var s=e.state,t=e.options,a=e.name,l=t.offset,v=l===void 0?[0,0]:l,C=Hs.reduce(function(O,U){return O[U]=er(U,s.rects,v),O},{}),A=C[s.placement],w=A.x,$=A.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=w,s.modifiersData.popperOffsets.y+=$),s.modifiersData[a]=C}const sr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:tr};function nr(e){var s=e.state,t=e.name;s.modifiersData[t]=Zs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const ar={name:"popperOffsets",enabled:!0,phase:"read",fn:nr,data:{}};function rr(e){return e==="x"?"y":"x"}function or(e){var s=e.state,t=e.options,a=e.name,l=t.mainAxis,v=l===void 0?!0:l,C=t.altAxis,A=C===void 0?!1:C,w=t.boundary,$=t.rootBoundary,O=t.altBoundary,U=t.padding,H=t.tether,I=H===void 0?!0:H,B=t.tetherOffset,K=B===void 0?0:B,V=Bt(s,{boundary:w,rootBoundary:$,padding:U,altBoundary:O}),se=nt(s.placement),ne=Ot(s.placement),ie=!ne,d=fs(se),te=rr(d),P=s.modifiersData.popperOffsets,ce=s.rects.reference,fe=s.rects.popper,g=typeof K=="function"?K(Object.assign({},s.rects,{placement:s.placement})):K,b=typeof g=="number"?{mainAxis:g,altAxis:g}:Object.assign({mainAxis:0,altAxis:0},g),k=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,T={x:0,y:0};if(P){if(v){var S,m=d==="y"?Oe:Ie,R=d==="y"?Ve:He,E=d==="y"?"height":"width",G=P[d],ge=G+V[m],we=G-V[R],Me=I?-fe[E]/2:0,Fe=ne===Mt?ce[E]:fe[E],De=ne===Mt?-fe[E]:-ce[E],Re=s.elements.arrow,Ce=I&&Re?ps(Re):{width:0,height:0},Ee=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Ys(),Pe=Ee[m],je=Ee[R],Z=Lt(0,ce[E],Ce[E]),Ue=ie?ce[E]/2-Me-Z-Pe-b.mainAxis:Fe-Z-Pe-b.mainAxis,ke=ie?-ce[E]/2+Me+Z+je+b.mainAxis:De+Z+je+b.mainAxis,rt=s.elements.arrow&&Vt(s.elements.arrow),Ge=rt?d==="y"?rt.clientTop||0:rt.clientLeft||0:0,We=(S=k==null?void 0:k[d])!=null?S:0,me=G+Ue-We-Ge,pt=G+ke-We,Ze=Lt(I?Kt(ge,me):ge,G,I?yt(we,pt):we);P[d]=Ze,T[d]=Ze-G}if(A){var ye,ot=d==="x"?Oe:Ie,ze=d==="x"?Ve:He,Ae=P[te],Be=te==="y"?"height":"width",Qe=Ae+V[ot],qe=Ae-V[ze],et=[Oe,Ie].indexOf(se)!==-1,it=(ye=k==null?void 0:k[te])!=null?ye:0,tt=et?Qe:Ae-ce[Be]-fe[Be]-it+b.altAxis,ft=et?Ae+ce[Be]+fe[Be]-it-b.altAxis:qe,lt=I&&et?Ma(tt,Ae,ft):Lt(I?tt:Qe,Ae,I?ft:qe);P[te]=lt,T[te]=lt-Ae}s.modifiersData[a]=T}}const ir={name:"preventOverflow",enabled:!0,phase:"main",fn:or,requiresIfExists:["offset"]};function lr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function cr(e){return e===Le(e)||!Ne(e)?hs(e):lr(e)}function ur(e){var s=e.getBoundingClientRect(),t=Dt(s.width)/e.offsetWidth||1,a=Dt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function dr(e,s,t){t===void 0&&(t=!1);var a=Ne(s),l=Ne(s)&&ur(s),v=vt(s),C=Pt(e,l,t),A={scrollLeft:0,scrollTop:0},w={x:0,y:0};return(a||!a&&!t)&&((at(s)!=="body"||vs(v))&&(A=cr(s)),Ne(s)?(w=Pt(s,!0),w.x+=s.clientLeft,w.y+=s.clientTop):v&&(w.x=gs(v))),{x:C.left+A.scrollLeft-w.x,y:C.top+A.scrollTop-w.y,width:C.width,height:C.height}}function pr(e){var s=new Map,t=new Set,a=[];e.forEach(function(v){s.set(v.name,v)});function l(v){t.add(v.name);var C=[].concat(v.requires||[],v.requiresIfExists||[]);C.forEach(function(A){if(!t.has(A)){var w=s.get(A);w&&l(w)}}),a.push(v)}return e.forEach(function(v){t.has(v.name)||l(v)}),a}function fr(e){var s=pr(e);return $a.reduce(function(t,a){return t.concat(s.filter(function(l){return l.phase===a}))},[])}function hr(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function gr(e){var s=e.reduce(function(t,a){var l=t[a.name];return t[a.name]=l?Object.assign({},l,a,{options:Object.assign({},l.options,a.options),data:Object.assign({},l.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var As={placement:"bottom",modifiers:[],strategy:"absolute"};function Ms(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function vr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,l=s.defaultOptions,v=l===void 0?As:l;return function(A,w,$){$===void 0&&($=v);var O={placement:"bottom",orderedModifiers:[],options:Object.assign({},As,v),modifiersData:{},elements:{reference:A,popper:w},attributes:{},styles:{}},U=[],H=!1,I={state:O,setOptions:function(se){var ne=typeof se=="function"?se(O.options):se;K(),O.options=Object.assign({},v,O.options,ne),O.scrollParents={reference:kt(A)?Ft(A):A.contextElement?Ft(A.contextElement):[],popper:Ft(w)};var ie=fr(gr([].concat(a,O.options.modifiers)));return O.orderedModifiers=ie.filter(function(d){return d.enabled}),B(),I.update()},forceUpdate:function(){if(!H){var se=O.elements,ne=se.reference,ie=se.popper;if(Ms(ne,ie)){O.rects={reference:dr(ne,Vt(ie),O.options.strategy==="fixed"),popper:ps(ie)},O.reset=!1,O.placement=O.options.placement,O.orderedModifiers.forEach(function(b){return O.modifiersData[b.name]=Object.assign({},b.data)});for(var d=0;d<O.orderedModifiers.length;d++){if(O.reset===!0){O.reset=!1,d=-1;continue}var te=O.orderedModifiers[d],P=te.fn,ce=te.options,fe=ce===void 0?{}:ce,g=te.name;typeof P=="function"&&(O=P({state:O,options:fe,name:g,instance:I})||O)}}}},update:hr(function(){return new Promise(function(V){I.forceUpdate(),V(O)})}),destroy:function(){K(),H=!0}};if(!Ms(A,w))return I;I.setOptions($).then(function(V){!H&&$.onFirstUpdate&&$.onFirstUpdate(V)});function B(){O.orderedModifiers.forEach(function(V){var se=V.name,ne=V.options,ie=ne===void 0?{}:ne,d=V.effect;if(typeof d=="function"){var te=d({state:O,name:se,instance:I,options:ie}),P=function(){};U.push(te||P)}})}function K(){U.forEach(function(V){return V()}),U=[]}return I}}var mr=[Wa,ar,Fa,zs,sr,Ga,ir,Ia,Qa],br=vr({defaultModifiers:mr}),wr="tippy-box",Qs="tippy-content",yr="tippy-backdrop",en="tippy-arrow",tn="tippy-svg-arrow",wt={passive:!0,capture:!0},sn=function(){return document.body};function ss(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function ms(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function nn(e,s){return typeof e=="function"?e.apply(void 0,s):e}function Ds(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function xr(e){return e.split(/\s+/).filter(Boolean)}function At(e){return[].concat(e)}function Ps(e,s){e.indexOf(s)===-1&&e.push(s)}function kr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function Tr(e){return e.split("-")[0]}function Xt(e){return[].slice.call(e)}function Os(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function jt(){return document.createElement("div")}function Zt(e){return["Element","Fragment"].some(function(s){return ms(e,s)})}function Cr(e){return ms(e,"NodeList")}function $r(e){return ms(e,"MouseEvent")}function Sr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function _r(e){return Zt(e)?[e]:Cr(e)?Xt(e):Array.isArray(e)?e:Xt(document.querySelectorAll(e))}function ns(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function Is(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function Er(e){var s,t=At(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function Ar(e,s){var t=s.clientX,a=s.clientY;return e.every(function(l){var v=l.popperRect,C=l.popperState,A=l.props,w=A.interactiveBorder,$=Tr(C.placement),O=C.modifiersData.offset;if(!O)return!0;var U=$==="bottom"?O.top.y:0,H=$==="top"?O.bottom.y:0,I=$==="right"?O.left.x:0,B=$==="left"?O.right.x:0,K=v.top-a+U>w,V=a-v.bottom-H>w,se=v.left-t+I>w,ne=t-v.right-B>w;return K||V||se||ne})}function as(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(l){e[a](l,t)})}function Rs(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var st={isTouch:!1},Us=0;function Mr(){st.isTouch||(st.isTouch=!0,window.performance&&document.addEventListener("mousemove",an))}function an(){var e=performance.now();e-Us<20&&(st.isTouch=!1,document.removeEventListener("mousemove",an)),Us=e}function Dr(){var e=document.activeElement;if(Sr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function Pr(){document.addEventListener("touchstart",Mr,wt),window.addEventListener("blur",Dr)}var Or=typeof window<"u"&&typeof document<"u",Ir=Or?!!window.msCrypto:!1,Rr={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ur={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Xe=Object.assign({appendTo:sn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Rr,Ur),Lr=Object.keys(Xe),Fr=function(s){var t=Object.keys(s);t.forEach(function(a){Xe[a]=s[a]})};function rn(e){var s=e.plugins||[],t=s.reduce(function(a,l){var v=l.name,C=l.defaultValue;if(v){var A;a[v]=e[v]!==void 0?e[v]:(A=Xe[v])!=null?A:C}return a},{});return Object.assign({},e,t)}function jr(e,s){var t=s?Object.keys(rn(Object.assign({},Xe,{plugins:s}))):Lr,a=t.reduce(function(l,v){var C=(e.getAttribute("data-tippy-"+v)||"").trim();if(!C)return l;if(v==="content")l[v]=C;else try{l[v]=JSON.parse(C)}catch{l[v]=C}return l},{});return a}function Ls(e,s){var t=Object.assign({},s,{content:nn(s.content,[e])},s.ignoreAttributes?{}:jr(e,s.plugins));return t.aria=Object.assign({},Xe.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Wr=function(){return"innerHTML"};function ls(e,s){e[Wr()]=s}function Fs(e){var s=jt();return e===!0?s.className=en:(s.className=tn,Zt(e)?s.appendChild(e):ls(s,e)),s}function js(e,s){Zt(s.content)?(ls(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?ls(e,s.content):e.textContent=s.content)}function cs(e){var s=e.firstElementChild,t=Xt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(Qs)}),arrow:t.find(function(a){return a.classList.contains(en)||a.classList.contains(tn)}),backdrop:t.find(function(a){return a.classList.contains(yr)})}}function on(e){var s=jt(),t=jt();t.className=wr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=jt();a.className=Qs,a.setAttribute("data-state","hidden"),js(a,e.props),s.appendChild(t),t.appendChild(a),l(e.props,e.props);function l(v,C){var A=cs(s),w=A.box,$=A.content,O=A.arrow;C.theme?w.setAttribute("data-theme",C.theme):w.removeAttribute("data-theme"),typeof C.animation=="string"?w.setAttribute("data-animation",C.animation):w.removeAttribute("data-animation"),C.inertia?w.setAttribute("data-inertia",""):w.removeAttribute("data-inertia"),w.style.maxWidth=typeof C.maxWidth=="number"?C.maxWidth+"px":C.maxWidth,C.role?w.setAttribute("role",C.role):w.removeAttribute("role"),(v.content!==C.content||v.allowHTML!==C.allowHTML)&&js($,e.props),C.arrow?O?v.arrow!==C.arrow&&(w.removeChild(O),w.appendChild(Fs(C.arrow))):w.appendChild(Fs(C.arrow)):O&&w.removeChild(O)}return{popper:s,onUpdate:l}}on.$$tippy=!0;var Br=1,qt=[],rs=[];function Nr(e,s){var t=Ls(e,Object.assign({},Xe,rn(Os(s)))),a,l,v,C=!1,A=!1,w=!1,$=!1,O,U,H,I=[],B=Ds(me,t.interactiveDebounce),K,V=Br++,se=null,ne=kr(t.plugins),ie={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},d={id:V,reference:e,popper:jt(),popperInstance:se,props:t,state:ie,plugins:ne,clearDelayTimeouts:tt,setProps:ft,setContent:lt,show:It,hide:Tt,hideWithInteractivity:Ct,enable:et,disable:it,unmount:$t,destroy:ht};if(!t.render)return d;var te=t.render(d),P=te.popper,ce=te.onUpdate;P.setAttribute("data-tippy-root",""),P.id="tippy-"+d.id,d.popper=P,e._tippy=d,P._tippy=d;var fe=ne.map(function(y){return y.fn(d)}),g=e.hasAttribute("aria-expanded");return rt(),Me(),G(),ge("onCreate",[d]),t.showOnCreate&&Qe(),P.addEventListener("mouseenter",function(){d.props.interactive&&d.state.isVisible&&d.clearDelayTimeouts()}),P.addEventListener("mouseleave",function(){d.props.interactive&&d.props.trigger.indexOf("mouseenter")>=0&&m().addEventListener("mousemove",B)}),d;function b(){var y=d.props.touch;return Array.isArray(y)?y:[y,0]}function k(){return b()[0]==="hold"}function T(){var y;return!!((y=d.props.render)!=null&&y.$$tippy)}function S(){return K||e}function m(){var y=S().parentNode;return y?Er(y):document}function R(){return cs(P)}function E(y){return d.state.isMounted&&!d.state.isVisible||st.isTouch||O&&O.type==="focus"?0:ss(d.props.delay,y?0:1,Xe.delay)}function G(y){y===void 0&&(y=!1),P.style.pointerEvents=d.props.interactive&&!y?"":"none",P.style.zIndex=""+d.props.zIndex}function ge(y,j,z){if(z===void 0&&(z=!0),fe.forEach(function(re){re[y]&&re[y].apply(re,j)}),z){var le;(le=d.props)[y].apply(le,j)}}function we(){var y=d.props.aria;if(y.content){var j="aria-"+y.content,z=P.id,le=At(d.props.triggerTarget||e);le.forEach(function(re){var xe=re.getAttribute(j);if(d.state.isVisible)re.setAttribute(j,xe?xe+" "+z:z);else{var $e=xe&&xe.replace(z,"").trim();$e?re.setAttribute(j,$e):re.removeAttribute(j)}})}}function Me(){if(!(g||!d.props.aria.expanded)){var y=At(d.props.triggerTarget||e);y.forEach(function(j){d.props.interactive?j.setAttribute("aria-expanded",d.state.isVisible&&j===S()?"true":"false"):j.removeAttribute("aria-expanded")})}}function Fe(){m().removeEventListener("mousemove",B),qt=qt.filter(function(y){return y!==B})}function De(y){if(!(st.isTouch&&(w||y.type==="mousedown"))){var j=y.composedPath&&y.composedPath()[0]||y.target;if(!(d.props.interactive&&Rs(P,j))){if(At(d.props.triggerTarget||e).some(function(z){return Rs(z,j)})){if(st.isTouch||d.state.isVisible&&d.props.trigger.indexOf("click")>=0)return}else ge("onClickOutside",[d,y]);d.props.hideOnClick===!0&&(d.clearDelayTimeouts(),d.hide(),A=!0,setTimeout(function(){A=!1}),d.state.isMounted||Pe())}}}function Re(){w=!0}function Ce(){w=!1}function Ee(){var y=m();y.addEventListener("mousedown",De,!0),y.addEventListener("touchend",De,wt),y.addEventListener("touchstart",Ce,wt),y.addEventListener("touchmove",Re,wt)}function Pe(){var y=m();y.removeEventListener("mousedown",De,!0),y.removeEventListener("touchend",De,wt),y.removeEventListener("touchstart",Ce,wt),y.removeEventListener("touchmove",Re,wt)}function je(y,j){Ue(y,function(){!d.state.isVisible&&P.parentNode&&P.parentNode.contains(P)&&j()})}function Z(y,j){Ue(y,j)}function Ue(y,j){var z=R().box;function le(re){re.target===z&&(as(z,"remove",le),j())}if(y===0)return j();as(z,"remove",U),as(z,"add",le),U=le}function ke(y,j,z){z===void 0&&(z=!1);var le=At(d.props.triggerTarget||e);le.forEach(function(re){re.addEventListener(y,j,z),I.push({node:re,eventType:y,handler:j,options:z})})}function rt(){k()&&(ke("touchstart",We,{passive:!0}),ke("touchend",pt,{passive:!0})),xr(d.props.trigger).forEach(function(y){if(y!=="manual")switch(ke(y,We),y){case"mouseenter":ke("mouseleave",pt);break;case"focus":ke(Ir?"focusout":"blur",Ze);break;case"focusin":ke("focusout",Ze);break}})}function Ge(){I.forEach(function(y){var j=y.node,z=y.eventType,le=y.handler,re=y.options;j.removeEventListener(z,le,re)}),I=[]}function We(y){var j,z=!1;if(!(!d.state.isEnabled||ye(y)||A)){var le=((j=O)==null?void 0:j.type)==="focus";O=y,K=y.currentTarget,Me(),!d.state.isVisible&&$r(y)&&qt.forEach(function(re){return re(y)}),y.type==="click"&&(d.props.trigger.indexOf("mouseenter")<0||C)&&d.props.hideOnClick!==!1&&d.state.isVisible?z=!0:Qe(y),y.type==="click"&&(C=!z),z&&!le&&qe(y)}}function me(y){var j=y.target,z=S().contains(j)||P.contains(j);if(!(y.type==="mousemove"&&z)){var le=Be().concat(P).map(function(re){var xe,$e=re._tippy,Je=(xe=$e.popperInstance)==null?void 0:xe.state;return Je?{popperRect:re.getBoundingClientRect(),popperState:Je,props:t}:null}).filter(Boolean);Ar(le,y)&&(Fe(),qe(y))}}function pt(y){var j=ye(y)||d.props.trigger.indexOf("click")>=0&&C;if(!j){if(d.props.interactive){d.hideWithInteractivity(y);return}qe(y)}}function Ze(y){d.props.trigger.indexOf("focusin")<0&&y.target!==S()||d.props.interactive&&y.relatedTarget&&P.contains(y.relatedTarget)||qe(y)}function ye(y){return st.isTouch?k()!==y.type.indexOf("touch")>=0:!1}function ot(){ze();var y=d.props,j=y.popperOptions,z=y.placement,le=y.offset,re=y.getReferenceClientRect,xe=y.moveTransition,$e=T()?cs(P).arrow:null,Je=re?{getBoundingClientRect:re,contextElement:re.contextElement||S()}:e,M={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(Q){var ue=Q.state;if(T()){var X=R(),n=X.box;["placement","reference-hidden","escaped"].forEach(function(r){r==="placement"?n.setAttribute("data-placement",ue.placement):ue.attributes.popper["data-popper-"+r]?n.setAttribute("data-"+r,""):n.removeAttribute("data-"+r)}),ue.attributes.popper={}}}},f=[{name:"offset",options:{offset:le}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!xe}},M];T()&&$e&&f.push({name:"arrow",options:{element:$e,padding:3}}),f.push.apply(f,(j==null?void 0:j.modifiers)||[]),d.popperInstance=br(Je,P,Object.assign({},j,{placement:z,onFirstUpdate:H,modifiers:f}))}function ze(){d.popperInstance&&(d.popperInstance.destroy(),d.popperInstance=null)}function Ae(){var y=d.props.appendTo,j,z=S();d.props.interactive&&y===sn||y==="parent"?j=z.parentNode:j=nn(y,[z]),j.contains(P)||j.appendChild(P),d.state.isMounted=!0,ot()}function Be(){return Xt(P.querySelectorAll("[data-tippy-root]"))}function Qe(y){d.clearDelayTimeouts(),y&&ge("onTrigger",[d,y]),Ee();var j=E(!0),z=b(),le=z[0],re=z[1];st.isTouch&&le==="hold"&&re&&(j=re),j?a=setTimeout(function(){d.show()},j):d.show()}function qe(y){if(d.clearDelayTimeouts(),ge("onUntrigger",[d,y]),!d.state.isVisible){Pe();return}if(!(d.props.trigger.indexOf("mouseenter")>=0&&d.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(y.type)>=0&&C)){var j=E(!1);j?l=setTimeout(function(){d.state.isVisible&&d.hide()},j):v=requestAnimationFrame(function(){d.hide()})}}function et(){d.state.isEnabled=!0}function it(){d.hide(),d.state.isEnabled=!1}function tt(){clearTimeout(a),clearTimeout(l),cancelAnimationFrame(v)}function ft(y){if(!d.state.isDestroyed){ge("onBeforeUpdate",[d,y]),Ge();var j=d.props,z=Ls(e,Object.assign({},j,Os(y),{ignoreAttributes:!0}));d.props=z,rt(),j.interactiveDebounce!==z.interactiveDebounce&&(Fe(),B=Ds(me,z.interactiveDebounce)),j.triggerTarget&&!z.triggerTarget?At(j.triggerTarget).forEach(function(le){le.removeAttribute("aria-expanded")}):z.triggerTarget&&e.removeAttribute("aria-expanded"),Me(),G(),ce&&ce(j,z),d.popperInstance&&(ot(),Be().forEach(function(le){requestAnimationFrame(le._tippy.popperInstance.forceUpdate)})),ge("onAfterUpdate",[d,y])}}function lt(y){d.setProps({content:y})}function It(){var y=d.state.isVisible,j=d.state.isDestroyed,z=!d.state.isEnabled,le=st.isTouch&&!d.props.touch,re=ss(d.props.duration,0,Xe.duration);if(!(y||j||z||le)&&!S().hasAttribute("disabled")&&(ge("onShow",[d],!1),d.props.onShow(d)!==!1)){if(d.state.isVisible=!0,T()&&(P.style.visibility="visible"),G(),Ee(),d.state.isMounted||(P.style.transition="none"),T()){var xe=R(),$e=xe.box,Je=xe.content;ns([$e,Je],0)}H=function(){var f;if(!(!d.state.isVisible||$)){if($=!0,P.offsetHeight,P.style.transition=d.props.moveTransition,T()&&d.props.animation){var N=R(),Q=N.box,ue=N.content;ns([Q,ue],re),Is([Q,ue],"visible")}we(),Me(),Ps(rs,d),(f=d.popperInstance)==null||f.forceUpdate(),ge("onMount",[d]),d.props.animation&&T()&&Z(re,function(){d.state.isShown=!0,ge("onShown",[d])})}},Ae()}}function Tt(){var y=!d.state.isVisible,j=d.state.isDestroyed,z=!d.state.isEnabled,le=ss(d.props.duration,1,Xe.duration);if(!(y||j||z)&&(ge("onHide",[d],!1),d.props.onHide(d)!==!1)){if(d.state.isVisible=!1,d.state.isShown=!1,$=!1,C=!1,T()&&(P.style.visibility="hidden"),Fe(),Pe(),G(!0),T()){var re=R(),xe=re.box,$e=re.content;d.props.animation&&(ns([xe,$e],le),Is([xe,$e],"hidden"))}we(),Me(),d.props.animation?T()&&je(le,d.unmount):d.unmount()}}function Ct(y){m().addEventListener("mousemove",B),Ps(qt,B),B(y)}function $t(){d.state.isVisible&&d.hide(),d.state.isMounted&&(ze(),Be().forEach(function(y){y._tippy.unmount()}),P.parentNode&&P.parentNode.removeChild(P),rs=rs.filter(function(y){return y!==d}),d.state.isMounted=!1,ge("onHidden",[d]))}function ht(){d.state.isDestroyed||(d.clearDelayTimeouts(),d.unmount(),Ge(),delete e._tippy,d.state.isDestroyed=!0,ge("onDestroy",[d]))}}function xt(e,s){s===void 0&&(s={});var t=Xe.plugins.concat(s.plugins||[]);Pr();var a=Object.assign({},s,{plugins:t}),l=_r(e),v=l.reduce(function(C,A){var w=A&&Nr(A,a);return w&&C.push(w),C},[]);return Zt(e)?v[0]:v}xt.defaultProps=Xe;xt.setDefaultProps=Fr;xt.currentInput=st;Object.assign({},zs,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});xt.setDefaultProps({render:on});const Vr={class:"task-pane"},Hr={key:0,class:"loading-overlay"},zr={key:1,class:"format-error-overlay"},qr={class:"format-error-content"},Jr={class:"format-error-message"},Yr={class:"format-error-actions"},Kr={class:"doc-header"},Xr={class:"doc-title"},Gr={class:"action-area"},Zr={class:"select-container"},Qr={class:"select-group"},eo=["disabled"],to=["value"],so={class:"select-group"},no=["disabled"],ao=["value"],ro=["title"],oo={key:0,class:"science-warning"},io={class:"action-buttons"},lo=["disabled"],co={class:"btn-content"},uo={key:0,class:"button-loader"},po=["disabled"],fo={class:"btn-content"},ho={key:0,class:"button-loader"},go=["disabled"],vo={class:"btn-content"},mo={key:0,class:"button-loader"},bo={class:"content-area"},wo={class:"modal-header"},yo={class:"modal-body"},xo={class:"selection-content"},ko={class:"modal-header"},To={class:"modal-body"},Co={class:"alert-message"},$o={class:"alert-actions"},So={key:2,class:"modal-overlay"},_o={class:"modal-header"},Eo={class:"modal-body"},Ao={class:"confirm-message"},Mo={class:"confirm-actions"},Do={class:"modal-header"},Po={class:"modal-title"},Oo={class:"modal-body"},Io={class:"alert-message"},Ro={class:"alert-actions"},Uo={class:"task-queue"},Lo={class:"queue-header"},Fo={class:"queue-status-filter"},jo=["value"],Wo={class:"queue-actions"},Bo=["disabled","title"],No={class:"task-count"},Vo={key:0,class:"queue-table-container"},Ho={class:"col-id"},zo={class:"id-header"},qo={key:0,class:"col-subject"},Jo={class:"subject-header"},Yo={class:"switch"},Ko=["title"],Xo={key:1,class:"col-status"},Go=["onClick"],Zo={class:"col-id"},Qo={class:"id-cell group-cell"},ei={class:"group-toggle-icon"},ti={class:"group-label"},si={key:0,class:"col-subject"},ni={class:"subject-cell group-subject"},ai={key:1,class:"col-status"},ri={class:"col-actions"},oi={class:"task-actions"},ii={class:"group-action-text"},li=["onClick"],ci={class:"col-id"},ui={class:"id-content"},di={class:"task-id"},pi={key:0,class:"analysis-task-icon",title:"解析任务"},fi={key:1,class:"enhance-task-icon",title:"增强解析任务"},hi={key:2,class:"check-task-icon",title:"校对任务"},gi={key:0,class:"status-in-id"},vi={key:0,class:"col-subject"},mi=["onMouseenter"],bi={key:1,class:"col-status"},wi={class:"status-cell"},yi=["onClick"],xi={class:"col-id"},ki={class:"id-content"},Ti={class:"task-id"},Ci={key:0,class:"analysis-task-icon",title:"解析任务"},$i={key:1,class:"enhance-task-icon",title:"增强解析任务"},Si={key:2,class:"check-task-icon",title:"校对任务"},_i={key:0,class:"status-in-id"},Ei={key:0,class:"col-subject"},Ai=["onMouseenter"],Mi={key:1,class:"col-status"},Di={class:"status-cell"},Pi={class:"col-actions"},Oi={class:"task-actions"},Ii=["onClick"],Ri=["onClick"],Ui={key:2,class:"no-action-icon",title:"无可用操作"},Li={key:1,class:"empty-queue"},Fi={key:4,class:"log-container"},ji={class:"log-actions"},Wi={class:"toggle-icon"},Bi=["innerHTML"],Ni={__name:"TaskPane",setup(e){const s=he(!1),t=he(!1),a=he(!1),l=he(""),v=he(!1),C=he(!1),A=he(!1),w=he(!1),$=he(!0),O=he(""),U=he(!1),H=he(window.innerWidth),I=ut(()=>H.value<750),B=ut(()=>H.value<380),K=()=>{H.value=window.innerWidth},V=he(null),se=he(!1),ne=he(""),ie=he(new Set);he(!1);const d={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},te=he(!1),P=he([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"}]),{docName:ce,selected:fe,logger:g,map:b,subject:k,stage:T,subjectOptions:S,stageOptions:m,appConfig:R,clearLog:E,checkDocumentFormat:G,getTaskStatusClass:ge,getTaskStatusText:we,terminateTask:Me,run1:Fe,runCheck:De,setupLifecycle:Re,navigateToTaskControl:Ce,isLoading:Ee,tryRemoveTaskPlaceholderWithLoading:Pe,confirmDialog:je,handleConfirm:Z,errorDialog:Ue,hideErrorDialog:ke,getCompletedTasksCount:rt,getReleasableTasksCount:Ge,showConfirm:We}=$n(),me=he(null);(()=>{var M;try{if((M=window.Application)!=null&&M.PluginStorage){const f=window.Application.PluginStorage.getItem("user_info");f?(me.value=JSON.parse(f),console.log("用户信息已加载:",me.value)):console.log("未找到用户信息")}}catch(f){console.error("解析用户信息时出错:",f)}})(),Yt(me,M=>{M&&M.orgs&&M.orgs[0]&&console.log(`用户企业ID: ${M.orgs[0].orgId}, 校对功能${M.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Ze=ut(()=>!me.value||me.value.isAdmin||me.value.isOwner?S:me.value.subject?S.filter(M=>M.value===me.value.subject):S),ye=()=>{me.value&&!me.value.isAdmin&&!me.value.isOwner&&me.value.subject&&(k.value=me.value.subject)},ot=ut(()=>["physics","chemistry","biology","math"].includes(k.value));Yt(R,M=>{M&&(console.log("TaskPane组件收到应用配置更新:",M),console.log("当前版本类型:",M.EDITION),console.log("当前年级选项:",m.value))},{deep:!0,immediate:!0});const ze=()=>{try{const M=G();$.value=M.isValid,O.value=M.message,U.value=!M.isValid,M.isValid||console.warn("文档格式检查失败:",M.message)}catch(M){console.error("执行文档格式检查时出错:",M),$.value=!1,O.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",U.value=!0}},Ae=ut(()=>{let M={};const f=b;if(ne.value==="")M={...f};else for(const N in f)if(Object.prototype.hasOwnProperty.call(f,N)){const Q=f[N];Q.status===ne.value&&(M[N]=Q)}return se.value&&V.value?M[V.value]?{[V.value]:M[V.value]}:{}:M}),Be=ut(()=>{const M=Ae.value;return Object.entries(M).map(([N,Q])=>({tid:N,...Q})).sort((N,Q)=>{const ue=N.startTime||0;return(Q.startTime||0)-ue}).reduce((N,Q)=>{const{tid:ue,...X}=Q;return N[ue]=X,N},{})}),Qe=ut(()=>{const M=Be.value,f=Object.entries(M).map(([X,n])=>({tid:X,...n})),N=f.filter(X=>X.status===3),Q=f.filter(X=>X.status!==3),ue=[];if(Q.forEach(X=>{ue.push({type:"task",...X})}),N.length>=2){const X="released-group-all",n=ie.value.has(X);ue.push({type:"group",groupId:X,isCollapsed:n,tasks:N,count:N.length})}else N.forEach(X=>{ue.push({type:"task",...X})});return ue}),qe=M=>{ie.value.has(M)?ie.value.delete(M):ie.value.add(M)},et=(M="wps-analysis")=>{if(!k.value)l.value="请选择学科",t.value=!0;else if(!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim())l.value="未选中内容",t.value=!0;else{M==="wps-analysis"?C.value=!0:M==="wps-enhance_analysis"&&(A.value=!0);const f=()=>{M==="wps-analysis"?C.value=!1:M==="wps-enhance_analysis"&&(A.value=!1)};Fe(M,f).catch(N=>{console.log(N),N.message.includes("重叠")?(l.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",N),l.value=N.message,t.value=!0),f()})}},it=()=>{if(!k.value)l.value="请选择学科",t.value=!0;else if(!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim())l.value="未选中内容",t.value=!0;else{w.value=!0;const M=()=>{w.value=!1};De(M).catch(f=>{console.log(f),f.message.includes("重叠")?(l.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",f),l.value=f.message,t.value=!0),M()})}},tt=(M,f)=>{V.value=M,Ce(M)},ft=M=>{b[M]&&(b[M].status=3),V.value===M&&(V.value=null),Pe(M,!0)},lt=async()=>{const M=Object.entries(b).filter(([f,N])=>N.status===2||N.status===4);if(M.length===0){l.value="没有可释放的任务",t.value=!0;return}try{if(await We(`确定要释放所有 ${M.length} 个可释放的任务吗？
此操作不可撤销。`)){let N=0;M.forEach(([Q,ue])=>{b[Q]&&(b[Q].status=3,V.value===Q&&(V.value=null),Pe(Q,!0),N++)}),l.value=`已成功释放 ${N} 个任务`,t.value=!0}}catch(f){console.error("释放任务时出错:",f),l.value="释放任务时出现错误",t.value=!0}},It=()=>{v.value=!v.value},Tt=M=>M?M.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Ct=M=>{const f=$t(M);return f?Tt(f):"无题目内容"},$t=M=>{if(!M.selectedText)return"";const f=M.selectedText.split("\r").filter(Q=>Q.trim());if(f.length===1){const Q=M.selectedText.split(`
`).filter(ue=>ue.trim());Q.length>1&&f.splice(0,1,...Q)}const N=f.map((Q,ue)=>{const X=Q.trim();return X.length>200,X});return N.length===1?f[0].trim():N.join(`
`)},ht=(M,f)=>{if(!te.value)return;const N=M.target,Q=$t(f).toString();if(!Q||Q.trim()===""){console.log("题目内容为空，不显示tooltip");return}const ue=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${Q.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,X=M.clientX,n=M.clientY;if(d.subjects.has(N)){const i=d.subjects.get(N);i.setContent(ue),i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:n,bottom:n,left:X,right:X})}),i.show();return}const r=xt(N,{content:ue,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:n,bottom:n,left:X,right:X}),onHidden:()=>{r.setProps({getReferenceClientRect:null})}});d.subjects.set(N,r),r.show()},y=M=>{const f=M.currentTarget,N=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,Q=M.clientX,ue=M.clientY;if(d.enhance.has(f)){const n=d.enhance.get(f);n.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ue,bottom:ue,left:Q,right:Q})}),n.show();return}const X=xt(f,{content:N,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});d.enhance.set(f,X),X.show()},j=()=>{d.subjects.forEach(M=>{M.destroy()}),d.subjects.clear(),d.enhance.forEach(M=>{M.destroy()}),d.enhance.clear(),d.softBreak.forEach(M=>{M.destroy()}),d.softBreak.clear(),document.removeEventListener("click",z),document.removeEventListener("mousemove",re)},z=M=>{const f=document.querySelector(".tippy-box");f&&!f.contains(M.target)&&(d.subjects.forEach(N=>N.hide()),d.enhance.forEach(N=>N.hide()),d.softBreak.forEach(N=>N.hide()))};let le=0;const re=M=>{const f=Date.now();if(f-le<100)return;le=f;const N=document.querySelector(".tippy-box");if(!N)return;const Q=N.getBoundingClientRect();!(M.clientX>=Q.left-20&&M.clientX<=Q.right+20&&M.clientY>=Q.top-20&&M.clientY<=Q.bottom+20)&&!N.matches(":hover")&&(d.subjects.forEach(X=>X.hide()),d.enhance.forEach(X=>X.hide()),d.softBreak.forEach(X=>X.hide()))},xe=()=>{document.addEventListener("click",z),document.addEventListener("mousemove",re)};Ws(()=>{window.addEventListener("resize",K),xe(),ye(),setTimeout(()=>{ze()},500);const M=document.createElement("style");M.id="tippy-custom-styles",M.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(M)}),pn(()=>{window.removeEventListener("resize",K),j();const M=document.getElementById("tippy-custom-styles");M&&M.remove()}),Re();const $e=M=>M.selectedText?M.selectedText.includes("\v")||M.selectedText.includes("\v"):!1,Je=M=>{const f=M.currentTarget,N=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,Q=M.clientX,ue=M.clientY;if(d.softBreak.has(f)){const n=d.softBreak.get(f);n.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ue,bottom:ue,left:Q,right:Q})}),n.show();return}const X=xt(f,{content:N,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});d.softBreak.set(f,X),X.show()};return ut(()=>Qe.value.some(M=>M.type==="group")),ut(()=>ie.value.size>0),(M,f)=>{var N,Q,ue,X,n;return L(),F("div",Vr,[pe(Ee)?(L(),F("div",Hr,f[31]||(f[31]=[c("div",{class:"loading-spinner"},null,-1),c("div",{class:"loading-text"},"处理中...",-1)]))):J("",!0),U.value?(L(),F("div",zr,[c("div",qr,[f[32]||(f[32]=c("div",{class:"format-error-icon"},"⚠️",-1)),f[33]||(f[33]=c("div",{class:"format-error-title"},"文档格式不支持",-1)),c("div",Jr,ae(O.value),1),c("div",Yr,[c("button",{class:"retry-check-btn",onClick:f[0]||(f[0]=r=>ze())},"重新检查")])])])):J("",!0),c("div",Kr,[c("div",Xr,ae(pe(ce)||"未选择文档"),1),c("button",{class:"settings-btn",onClick:f[1]||(f[1]=r=>a.value=!0)},f[34]||(f[34]=[c("i",{class:"icon-settings"},null,-1)]))]),c("div",Gr,[c("div",Zr,[c("div",Qr,[f[35]||(f[35]=c("label",{for:"stage-select"},"年级:",-1)),Ye(c("select",{id:"stage-select","onUpdate:modelValue":f[2]||(f[2]=r=>bs(T)?T.value=r:null),class:"select-input",disabled:U.value},[(L(!0),F(bt,null,Et(pe(m),r=>(L(),F("option",{key:r.value,value:r.value},ae(r.label),9,to))),128))],8,eo),[[ts,pe(T)]])]),c("div",so,[f[36]||(f[36]=c("label",{for:"subject-select"},"学科:",-1)),Ye(c("select",{id:"subject-select","onUpdate:modelValue":f[3]||(f[3]=r=>bs(k)?k.value=r:null),class:"select-input",disabled:U.value},[(L(!0),F(bt,null,Et(Ze.value,r=>(L(),F("option",{key:r.value,value:r.value},ae(r.label),9,ao))),128))],8,no),[[ts,pe(k)]]),me.value&&!me.value.isAdmin&&!me.value.isOwner&&me.value.subject?(L(),F("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((N=Ze.value.find(r=>r.value===me.value.subject))==null?void 0:N.label)||me.value.subject} 学科`}," 🔒 ",8,ro)):J("",!0)])]),ot.value?(L(),F("div",oo," 理科可使用增强模式以获取更精准的解析 ")):J("",!0),c("div",io,[c("button",{class:"action-btn primary",onClick:f[4]||(f[4]=r=>et("wps-analysis")),disabled:C.value||U.value},[c("div",co,[C.value?(L(),F("span",uo)):J("",!0),f[37]||(f[37]=c("span",{class:"btn-text"},"解析",-1))])],8,lo),ot.value?(L(),F("button",{key:0,class:"action-btn enhance",onClick:f[5]||(f[5]=r=>et("wps-enhance_analysis")),disabled:A.value||U.value},[c("div",fo,[A.value?(L(),F("span",ho)):J("",!0),f[38]||(f[38]=c("span",{class:"btn-text"},"增强解析",-1))])],8,po)):J("",!0),((ue=(Q=me.value)==null?void 0:Q.orgs[0])==null?void 0:ue.orgId)===2?(L(),F("button",{key:1,class:"action-btn secondary",onClick:f[6]||(f[6]=r=>it()),disabled:w.value||U.value},[c("div",vo,[w.value?(L(),F("span",mo)):J("",!0),f[39]||(f[39]=c("span",{class:"btn-text"},"校对",-1))])],8,go)):J("",!0)])]),c("div",bo,[s.value?(L(),F("div",{key:0,class:"modal-overlay",onClick:f[9]||(f[9]=r=>s.value=!1)},[c("div",{class:"modal-content",onClick:f[8]||(f[8]=gt(()=>{},["stop"]))},[c("div",wo,[f[40]||(f[40]=c("div",{class:"modal-title"},"选中内容",-1)),c("button",{class:"modal-close",onClick:f[7]||(f[7]=r=>s.value=!1)},"×")]),c("div",yo,[c("pre",xo,ae(pe(fe)||"未选中内容"),1)])])])):J("",!0),t.value?(L(),F("div",{key:1,class:"modal-overlay",onClick:f[13]||(f[13]=r=>t.value=!1)},[c("div",{class:"modal-content alert-modal",onClick:f[12]||(f[12]=gt(()=>{},["stop"]))},[c("div",ko,[f[41]||(f[41]=c("div",{class:"modal-title"},"提示",-1)),c("button",{class:"modal-close",onClick:f[10]||(f[10]=r=>t.value=!1)},"×")]),c("div",To,[c("div",Co,ae(l.value),1),c("div",$o,[c("button",{class:"action-btn primary",onClick:f[11]||(f[11]=r=>t.value=!1)},"确定")])])])])):J("",!0),pe(je).show?(L(),F("div",So,[c("div",{class:"modal-content confirm-modal",onClick:f[17]||(f[17]=gt(()=>{},["stop"]))},[c("div",_o,[f[42]||(f[42]=c("div",{class:"modal-title"},"确认",-1)),c("button",{class:"modal-close",onClick:f[14]||(f[14]=r=>pe(Z)(!1))},"×")]),c("div",Eo,[c("div",Ao,ae(pe(je).message),1),c("div",Mo,[c("button",{class:"action-btn secondary",onClick:f[15]||(f[15]=r=>pe(Z)(!1))},"取消"),c("button",{class:"action-btn primary",onClick:f[16]||(f[16]=r=>pe(Z)(!0))},"确定")])])])])):J("",!0),pe(Ue).show?(L(),F("div",{key:3,class:"modal-overlay",onClick:f[21]||(f[21]=r=>pe(ke)())},[c("div",{class:"modal-content alert-modal",onClick:f[20]||(f[20]=gt(()=>{},["stop"]))},[c("div",Do,[c("div",Po,ae(pe(Ue).title),1),c("button",{class:"modal-close",onClick:f[18]||(f[18]=r=>pe(ke)())},"×")]),c("div",Oo,[c("div",Io,ae(pe(Ue).message),1),c("div",Ro,[c("button",{class:"action-btn primary",onClick:f[19]||(f[19]=r=>pe(ke)())},"确定")])])])])):J("",!0),c("div",Uo,[c("div",Lo,[f[43]||(f[43]=c("div",{class:"queue-title"},"任务队列",-1)),c("div",Fo,[Ye(c("select",{id:"status-filter-select","onUpdate:modelValue":f[22]||(f[22]=r=>ne.value=r),class:"status-filter-select-input"},[(L(!0),F(bt,null,Et(P.value,r=>(L(),F("option",{key:r.value,value:r.value},ae(r.label),9,jo))),128))],512),[[ts,ne.value]])]),c("div",Wo,[c("button",{class:"release-all-btn",onClick:lt,disabled:pe(Ge)()===0,title:pe(Ge)()===0?"无可释放任务":`释放所有${pe(Ge)()}个可释放任务`}," 一键释放 ",8,Bo)]),c("div",No,ae(Object.keys(Ae.value).length)+"个任务",1)]),Object.keys(Ae.value).length>0?(L(),F("div",Vo,[c("table",{class:_e(["queue-table",{"narrow-view":I.value,"ultra-narrow-view":B.value}])},[c("thead",null,[c("tr",null,[c("th",Ho,[c("div",zo,[f[45]||(f[45]=c("span",null,"任务ID",-1)),c("span",{class:"help-icon",onMouseenter:f[23]||(f[23]=r=>y(r))},f[44]||(f[44]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),I.value?J("",!0):(L(),F("th",qo,[c("div",Jo,[f[46]||(f[46]=c("span",null,"题目内容",-1)),c("label",Yo,[Ye(c("input",{type:"checkbox","onUpdate:modelValue":f[24]||(f[24]=r=>te.value=r)},null,512),[[fn,te.value]]),c("span",{class:"slider round",title:te.value?"关闭题目预览":"开启题目预览"},null,8,Ko)])])])),B.value?J("",!0):(L(),F("th",Xo,"状态")),f[47]||(f[47]=c("th",{class:"col-actions"},"操作",-1))])]),c("tbody",null,[(L(!0),F(bt,null,Et(Qe.value,r=>(L(),F(bt,{key:r.type==="group"?r.groupId:r.tid},[r.type==="group"?(L(),F("tr",{key:0,class:"group-row",onClick:i=>qe(r.groupId)},[c("td",Zo,[c("div",Qo,[c("span",ei,ae(r.isCollapsed?"▶":"▼"),1),c("span",ti,"已释放任务组 ("+ae(r.count)+"个)",1)])]),I.value?J("",!0):(L(),F("td",si,[c("div",ni,ae(r.isCollapsed?"点击展开查看详情":"点击折叠隐藏详情"),1)])),B.value?J("",!0):(L(),F("td",ai,f[48]||(f[48]=[c("div",{class:"status-cell"},[c("span",{class:"task-tag status-released"},"已释放")],-1)]))),c("td",ri,[c("div",oi,[c("span",ii,ae(r.isCollapsed?"展开":"折叠"),1)])])],8,Go)):J("",!0),r.type==="group"&&!r.isCollapsed?(L(!0),F(bt,{key:1},Et(r.tasks,(i,p)=>(L(),F("tr",{key:i.tid,class:_e(["task-row group-task-row",[pe(ge)(i.status),{"selected-task-row":i.tid===V.value}]]),onClick:o=>tt(i.tid)},[c("td",ci,[c("div",{class:_e(["id-cell",{"id-with-status":B.value}])},[c("div",ui,[f[50]||(f[50]=c("span",{class:"group-indent"},"└─",-1)),c("span",di,ae(i.tid.substring(0,8)),1),i.wordType==="wps-analysis"?(L(),F("span",pi," 解 ")):J("",!0),i.wordType==="wps-enhance_analysis"||i.isEnhanced?(L(),F("span",fi," 解 ")):J("",!0),i.wordType==="wps-check"||i.isCheckTask?(L(),F("span",hi," 校 ")):J("",!0),$e(i)?(L(),F("span",{key:3,class:"soft-break-warning-icon",onMouseenter:f[25]||(f[25]=o=>Je(o))},f[49]||(f[49]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("title",null,"提示"),c("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),c("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):J("",!0)]),B.value?(L(),F("div",gi,[c("span",{class:_e(["task-tag compact",pe(ge)(i.status)])},ae(pe(we)(i.status)),3)])):J("",!0)],2)]),I.value?J("",!0):(L(),F("td",vi,[c("div",{class:"subject-cell",onMouseenter:o=>ht(o,i)},ae(Ct(i)),41,mi)])),B.value?J("",!0):(L(),F("td",bi,[c("div",wi,[c("span",{class:_e(["task-tag",pe(ge)(i.status)])},ae(pe(we)(i.status)),3)])])),f[51]||(f[51]=c("td",{class:"col-actions"},[c("div",{class:"task-actions"},[c("span",{class:"no-action-icon",title:"无可用操作"},[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),c("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})])])])],-1))],10,li))),128)):J("",!0),r.type==="task"?(L(),F("tr",{key:2,class:_e(["task-row",[pe(ge)(r.status),{"selected-task-row":r.tid===V.value}]]),onClick:i=>tt(r.tid)},[c("td",xi,[c("div",{class:_e(["id-cell",{"id-with-status":B.value}])},[c("div",ki,[c("span",Ti,ae(r.tid.substring(0,8)),1),!r.isEnhanced&&!r.isCheckTask?(L(),F("span",Ci," 解 ")):J("",!0),r.isEnhanced?(L(),F("span",$i," 解 ")):J("",!0),r.isCheckTask?(L(),F("span",Si," 校 ")):J("",!0),$e(r)?(L(),F("span",{key:3,class:"soft-break-warning-icon",onMouseenter:f[26]||(f[26]=i=>Je(i))},f[52]||(f[52]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("title",null,"提示"),c("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),c("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):J("",!0)]),B.value?(L(),F("div",_i,[c("span",{class:_e(["task-tag compact",pe(ge)(r.status)])},ae(pe(we)(r.status)),3)])):J("",!0)],2)]),I.value?J("",!0):(L(),F("td",Ei,[c("div",{class:"subject-cell",onMouseenter:i=>ht(i,r)},ae(Ct(r)),41,Ai)])),B.value?J("",!0):(L(),F("td",Mi,[c("div",Di,[c("span",{class:_e(["task-tag",pe(ge)(r.status)])},ae(pe(we)(r.status)),3)])])),c("td",Pi,[c("div",Oi,[r.status===1?(L(),F("button",{key:0,onClick:gt(i=>pe(Me)(r.tid),["stop"]),class:"terminate-btn"}," 终止 ",8,Ii)):J("",!0),r.status===2||r.status===4?(L(),F("button",{key:1,onClick:gt(i=>ft(r.tid),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Ri)):J("",!0),r.status!==1&&r.status!==2&&r.status!==4?(L(),F("span",Ui,f[53]||(f[53]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),c("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):J("",!0)])])],10,yi)):J("",!0)],64))),128))])],2)])):(L(),F("div",Li,f[54]||(f[54]=[c("div",{class:"empty-text"},"暂无任务",-1)])))]),((n=(X=me.value)==null?void 0:X.orgs[0])==null?void 0:n.orgId)===2?(L(),F("div",Fi,[c("div",{class:"log-header",onClick:It},[f[55]||(f[55]=c("div",{class:"log-title"},"执行日志",-1)),c("div",ji,[c("button",{class:"clear-btn",onClick:f[27]||(f[27]=gt(r=>pe(E)(),["stop"]))},"清空日志"),c("span",Wi,ae(v.value?"▼":"▶"),1)])]),v.value?(L(),F("div",{key:0,class:"log-content",innerHTML:pe(g)},null,8,Bi)):J("",!0)])):J("",!0)]),a.value?(L(),F("div",{key:2,class:"modal-overlay",onClick:f[30]||(f[30]=r=>a.value=!1)},[c("div",{class:"modal-content",onClick:f[29]||(f[29]=gt(()=>{},["stop"]))},[hn(fa,{onClose:f[28]||(f[28]=r=>a.value=!1)})])])):J("",!0)])}}},Hi=Bs(Ni,[["__scopeId","data-v-e04ccd22"]]);export{Hi as default};
