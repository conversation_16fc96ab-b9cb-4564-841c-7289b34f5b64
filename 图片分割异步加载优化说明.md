# 图片分割异步加载优化说明

## 问题描述
之前的图片分割逻辑存在问题：
1. 在 `saveSelectedImageToWatchDir` 中同步等待OSS上传结果
2. 没有正确的loading状态显示
3. 用户体验不佳，无法看到上传进度

## 解决方案
重构图片加载逻辑，采用异步事件驱动的方式：
1. 保存图片后立即通知服务端，不等待上传结果
2. 先显示loading状态的演示图片
3. 通过WebSocket事件监听上传结果
4. 上传成功后自动替换为真实图片

## 实现细节

### 1. 修改 `saveSelectedImageToWatchDir` 函数

#### 原有问题
```javascript
// 错误的同步等待方式
const uploadResult = await wsClient.sendMessage('imageSplit', 'uploadFile', {
  fileName: fileName
})
```

#### 新的异步方式
```javascript
// 只通知服务端关联文件，不等待上传结果
await wsClient.sendMessage('imageSplit', 'associateFile', {
  fileName: fileName
})

// 返回基本信息，等待WebSocket事件通知
return {
  path: fullPath,
  fileName: fileName,
  width: shape.Width || 400,
  height: shape.Height || 300,
  needsDemo: true // 初始状态，等待服务端上传成功通知
}
```

### 2. 新增状态管理

#### 响应式状态
```javascript
const isLoadingImage = ref(false) // 图片加载状态
const currentImageInfo = ref(null) // 当前图片信息
```

#### 状态流转
```
保存图片 → 显示loading → 等待上传 → 加载真实图片 → 完成
```

### 3. 修改 `openSplitModal` 函数

#### 新的加载流程
```javascript
// 1. 保存图片信息
currentImageInfo.value = imageInfo
showSplitModal.value = true

// 2. 显示loading状态的演示图片
isLoadingImage.value = true
await loadDemoImageToCanvas(imageInfo.width, imageInfo.height)

// 3. 等待WebSocket事件通知
// 实际的图片加载将在WebSocket事件处理器中完成
```

### 4. WebSocket事件处理优化

#### 上传成功事件
```javascript
wsClient.on('imageSplitUploadSuccess', async (data) => {
  // 检查是否是当前等待的原图文件
  if (isLoadingImage.value && currentImageInfo.value && 
      data.file === currentImageInfo.value.fileName) {
    
    // 加载真实OSS图片
    await loadImageFromOSS(data.ossUrl, currentImageInfo.value.width, currentImageInfo.value.height)
    isLoadingImage.value = false
    successMessage.value = '图片加载成功！'
  }
})
```

#### 上传失败事件
```javascript
wsClient.on('imageSplitUploadError', (data) => {
  // 检查是否是当前等待的原图文件
  if (isLoadingImage.value && currentImageInfo.value && 
      data.file === currentImageInfo.value.fileName) {
    
    // 保持演示图片，停止loading状态
    isLoadingImage.value = false
    errorMessage.value = '图片上传失败，使用演示图片进行分割'
  }
})
```

### 5. UI Loading状态

#### 模板结构
```vue
<div class="image-editor">
  <canvas ref="imageCanvas" class="image-canvas"></canvas>
  
  <!-- Loading 覆盖层 -->
  <div v-if="isLoadingImage" class="loading-overlay">
    <div class="loading-spinner"></div>
    <div class="loading-text">正在加载真实图片...</div>
  </div>
</div>
```

#### CSS样式
```css
.loading-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px; height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
```

## 工作流程

### 正常流程
```
1. 用户选择图片
2. 保存到监控目录
3. 通知服务端关联文件
4. 显示分割弹窗 + loading状态
5. 显示演示图片 + 转圈loading
6. 服务端自动上传到OSS
7. 收到上传成功WebSocket事件
8. 加载真实OSS图片到Canvas
9. 隐藏loading，显示成功消息
```

### 异常流程
```
1-5. 同正常流程
6. 服务端上传失败
7. 收到上传失败WebSocket事件
8. 隐藏loading，保持演示图片
9. 显示错误消息，用户可继续使用演示图片分割
```

## 技术优势

### 1. 用户体验优化
- **即时反馈**: 立即显示分割界面，不需要等待上传
- **进度可视**: 清晰的loading状态和进度提示
- **容错处理**: 上传失败时仍可使用演示图片

### 2. 性能优化
- **非阻塞**: 不阻塞UI线程等待网络请求
- **异步处理**: 利用WebSocket事件驱动架构
- **资源节约**: 避免重复的同步请求

### 3. 架构改进
- **事件驱动**: 基于WebSocket事件的松耦合架构
- **状态管理**: 清晰的状态流转和管理
- **错误处理**: 完善的异常情况处理

## 兼容性保证

### 向后兼容
- 保留所有现有的分割功能
- 演示图片作为可靠的备用方案
- 现有的WebSocket事件处理逻辑

### 容错机制
- WebSocket连接失败时使用演示图片
- OSS服务不可用时使用演示图片
- 网络异常时提供清晰的错误提示

## 测试建议

### 正常场景
1. 选择图片 → 验证loading状态显示
2. 等待上传 → 验证真实图片加载
3. 进行分割 → 验证分割功能正常

### 异常场景
1. 网络中断 → 验证演示图片备用方案
2. OSS服务异常 → 验证错误处理
3. WebSocket断开 → 验证降级处理

### 性能测试
1. 大图片上传 → 验证loading状态持续时间
2. 并发操作 → 验证状态管理正确性
3. 内存使用 → 验证无内存泄漏
