/* 通知样式 */
.notification {
  position: fixed;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 9998;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
  min-width: 200px;
}

.notification.success {
  background-color: #52c41a;
}

.notification.error {
  background-color: #f5222d;
}

.notification.info {
  background-color: #1890ff;
}

.notification.warning {
  background-color: #faad14;
}

/* 通知动画 */
.notification-fade-enter-active,
.notification-fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.notification-fade-enter-from,
.notification-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -20px);
} 