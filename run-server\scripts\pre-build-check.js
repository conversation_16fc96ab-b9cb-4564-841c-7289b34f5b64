const fs = require('fs');
const path = require('path');
const { execSync, spawnSync } = require('child_process');

/**
 * 构建前检查版本号是否更新
 * 可以集成到npm构建脚本中
 */

// 获取项目根目录
const rootDir = path.resolve(__dirname, '..');
const packageJsonPath = path.join(rootDir, 'package.json');

// 检查package.json是否存在
if (!fs.existsSync(packageJsonPath)) {
  console.error('错误: package.json文件不存在!');
  process.exit(1);
}

// 读取当前package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
const currentVersion = packageJson.version;

console.log(`当前版本号: ${currentVersion}`);

try {
  // 检查git仓库中最近提交的package.json版本
  const lastCommitVersion = execSync('git show HEAD:package.json', { cwd: rootDir })
    .toString()
    .match(/"version":\s*"([^"]+)"/)[1];

  console.log(`Git中的版本号: ${lastCommitVersion}`);

  // 比较版本号
  if (currentVersion === lastCommitVersion) {
    console.log('警告: 版本号未更新!');
    
    // 询问用户是否要运行版本检查工具
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('版本号未更新，是否更新版本号? (y/n): ', (answer) => {
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        rl.close();
        
        // 运行版本检查工具
        console.log('正在运行版本检查工具...');
        const result = spawnSync('node', ['scripts/version-check.js'], {
          cwd: rootDir,
          stdio: 'inherit'
        });
        
        if (result.status !== 0) {
          console.error('版本检查工具运行失败');
          process.exit(1);
        }
      } else {
        console.log('继续使用当前版本号构建。');
        rl.close();
      }
    });
  } else {
    console.log('版本号已更新，继续构建...');
  }
} catch (error) {
  console.error('获取Git历史版本号时出错:', error.message);
  process.exit(1);
} 