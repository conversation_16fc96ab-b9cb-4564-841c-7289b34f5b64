<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>万唯AI编辑WPS插件服务</title>
  <link rel="stylesheet" href="style.css">
</head>

<body>
<div class="title-bar">
  <div class="title">万唯AI编辑WPS插件服务 <span id="version-number" style="font-size: 12px; opacity: 0.8;"></span></div>
  <div class="controls">
    <label for="debug-switch"
           style="cursor: pointer; color: white; font-size: 12px; display: flex; align-items: center; -webkit-app-region: no-drag;">
    </label>
    <button id="check-update-button" title="检查更新" style="-webkit-app-region: no-drag;">↻</button>
    <button id="minimize-button" title="最小化">-</button>
    <button id="close-button" class="close-button" title="关闭">×</button>
  </div>
</div>

<div class="content-area">
  <div class="panel-container">
    <div id="loading-container" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在检查登录状态...</div>
    </div>

    <div id="permission-check-container" class="permission-check-container" style="display: none; flex-direction: column; align-items: center; justify-content: center; height: 100%; gap: 15px;">
      <h3 style="margin-bottom: 10px; text-align: center;">权限检查</h3>
      <div class="permission-progress-info" style="width: 100%; display: flex; justify-content: space-between; font-size: 12px; margin-bottom: 5px;">
        <span id="current-permission-name">准备检查...</span>
        <span id="permission-progress-percent">0%</span>
      </div>
      <div class="permission-progress-bar" style="width: 100%; height: 8px; background-color: #f0f0f0; border-radius: 4px; overflow: hidden;">
        <div id="permission-progress-fill" style="height: 100%; width: 0%; background-color: #229a52; transition: width 0.3s ease;"></div>
      </div>
      <div class="permission-check-status" style="margin-top: 10px; text-align: center; font-size: 14px; color: #666;">
        <span id="permission-status-text">正在初始化权限检查...</span>
      </div>
    </div>

    <div id="network-folder-container" class="network-folder-container" style="display: none; flex-direction: column; align-items: center; justify-content: center; height: 100%; gap: 15px; padding: 5px;">
      <div class="network-folder-header" style="text-align: center; margin-bottom: 15px;">
        <h3 style="color: #e67e22; margin-bottom: 10px;">⚠️ 检测到加密软件</h3>
        <p style="font-size: 14px; color: #666; line-height: 1.4;">
          请选择一个网络设备，程序将自动创建所需的文件夹。
        </p>
      </div>

      <div class="current-path-info" style="width: 100%; background-color: #f8f9fa; padding: 10px; border-radius: 6px; margin-bottom: 15px;">
        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">当前数据目录：</div>
        <div id="current-watch-dir" style="font-size: 13px; color: #333; word-break: break-all;"></div>
      </div>

      <div class="network-folder-buttons" style="width: 100%; display: flex; flex-direction: column; gap: 10px;">
        <button id="select-network-folder-btn" type="button" style="background-color: #e67e22;">选择网络设备</button>
        <button id="confirm-network-folder-btn" type="button" style="background-color: #28a745; display: none;">确认使用此文件夹</button>
      </div>

      <div id="network-folder-message" class="network-folder-message" style="display: none; margin-top: 10px; padding: 8px; border-radius: 4px; font-size: 13px; text-align: center;"></div>

      <!-- <div class="network-folder-help" style="margin-top: 15px; font-size: 12px; color: #999; text-align: center; line-height: 1.3;">
        <div>网络设备示例：</div>
        <div>\\\\192.168.1.12 或 Z:\\ （映射的网络驱动器）</div>
        <div style="margin-top: 5px; font-size: 11px;">程序将自动创建：[设备]\ww-wps-addon\Temp</div>
      </div> -->
    </div>

    <div id="login-form" class="login-form">
      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" placeholder="请输入用户名" required>
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" placeholder="请输入密码" required>
      </div>
      <div class="button-container">
        <button id="login-button" type="button">登录</button>
      </div>
      <div id="error-message" class="error-message"></div>
    </div>

    <div id="status-panel" class="status-panel">
      <div id="user-greeting" style="text-align: center; margin-bottom: 20px; font-size: 16px;"></div>

      <div class="wps-addon-panel">
        <div class="wps-addon-info">
          <div class="wps-addon-name">插件: 万唯AI编辑WPS插件</div>
          <div id="addon-status" class="wps-addon-status status-not-installed">未安装</div>
        </div>
        <div class="wps-addon-buttons">
          <button id="addon-install-button">安装插件</button>
          <button id="open-wps-button">打开WPS</button>
        </div>
      </div>

      <button id="disable-all-addons-button" type="button" class="button-danger">禁用所有插件</button>

      <button id="logout-button" type="button" class="secondary">退出登录</button>
    </div>
  </div>
</div>

<script src="wps-utils.js"></script>
<script src="script.js"></script>
</body>

</html>
