<?xml version="1.0" encoding="UTF-8"?>
<customUI xmlns="http://schemas.microsoft.com/office/2006/01/customui" onLoad="ribbon.OnAddinLoad">
    <ribbon startFromScratch="false">
        <tabs>
            <tab id="wpsAddinTab" label="万唯AI编辑" onActivate="ribbon.OnTabActivate"  onAction="ribbon.OnAction" visible="true">
				<group id="btnDemoGroup" label="group1" visible="true">
                    <button id="btnShowTaskPane" visible="true" label="打开解题窗口" onAction="ribbon.OnAction" enabled="true"  getImage="ribbon.GetImage" getVisible="ribbon.OnGetVisible" size="large"/>
                    <button id="btnImageSplit" visible="true" label="图片分割" onAction="ribbon.OnAction" enabled="true"  getImage="ribbon.GetImage" getVisible="ribbon.OnGetVisible" size="large"/>
                </group>
            </tab>
        </tabs>
    </ribbon>
</customUI>
