const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

/**
 * 检查package.json版本号是否在git中发生变化
 * 如果没有变化，提示用户更新版本号
 */

// 创建readline接口用于用户交互
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 获取项目根目录
const rootDir = path.resolve(__dirname, '..');
const packageJsonPath = path.join(rootDir, 'package.json');

// 检查package.json是否存在
if (!fs.existsSync(packageJsonPath)) {
  console.error('错误: package.json文件不存在!');
  process.exit(1);
}

// 读取当前package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
const currentVersion = packageJson.version;

console.log(`当前版本号: ${currentVersion}`);
console.log(`当前目录: ${rootDir}`);
try {
  // 检查git仓库中最近提交的package.json版本
  const lastCommitVersion = execSync('git show HEAD:run-server/package.json', { cwd: rootDir })
    .toString()
    .match(/"version":\s*"([^"]+)"/)[1];

  console.log(`Git中的版本号: ${lastCommitVersion}`);

  // 比较版本号
  if (currentVersion === lastCommitVersion) {
    console.log('警告: 版本号未更新!');
    promptForVersionUpdate(packageJson);
  } else {
    console.log('版本号已更新，无需操作。');
    rl.close();
  }
} catch (error) {
  console.error('获取Git历史版本号时出错:', error.message);
  promptForVersionUpdate(packageJson);
}

function promptForVersionUpdate(packageJson) {
  const currentVersion = packageJson.version;
  const versionParts = currentVersion.split('.');
  const major = parseInt(versionParts[0], 10);
  const minor = parseInt(versionParts[1], 10);
  const patch = parseInt(versionParts[2], 10);

  const suggestedPatchVersion = `${major}.${minor}.${patch + 1}`;
  const suggestedMinorVersion = `${major}.${minor + 1}.0`;
  const suggestedMajorVersion = `${major + 1}.0.0`;

  console.log('请选择更新版本号的方式:');
  console.log(`1. 补丁版本 (${suggestedPatchVersion})`);
  console.log(`2. 次要版本 (${suggestedMinorVersion})`);
  console.log(`3. 主要版本 (${suggestedMajorVersion})`);
  console.log('4. 自定义版本');
  console.log('5. 不更新');

  rl.question('请输入选项 (1-5): ', (answer) => {
    let newVersion = currentVersion;

    switch (answer.trim()) {
      case '1':
        newVersion = suggestedPatchVersion;
        break;
      case '2':
        newVersion = suggestedMinorVersion;
        break;
      case '3':
        newVersion = suggestedMajorVersion;
        break;
      case '4':
        return rl.question('请输入新版本号 (例如 1.2.3): ', (customVersion) => {
          if (/^\d+\.\d+\.\d+$/.test(customVersion)) {
            updateVersion(customVersion, packageJson);
          } else {
            console.error('错误: 版本号格式无效，请使用 x.y.z 格式');
            rl.close();
          }
        });
      case '5':
        console.log('未更新版本号。');
        rl.close();
        return;
      default:
        console.log('无效选项，使用默认补丁版本更新。');
        newVersion = suggestedPatchVersion;
    }

    updateVersion(newVersion, packageJson);
  });
}

function updateVersion(newVersion, packageJson) {
  packageJson.version = newVersion;
  
  // 写入新版本号到package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, "\t"));
  
  console.log(`版本号已更新为: ${newVersion}`);
  rl.close();
}

// 当readline接口关闭时结束进程
rl.on('close', () => {
  process.exit(0);
}); 