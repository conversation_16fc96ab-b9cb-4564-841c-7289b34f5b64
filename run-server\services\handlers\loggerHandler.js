const loggerService = require('../loggerService');
const { defaultLogger } = require('../../utils/logger');

class LoggerHandler {
    constructor(wsServer) {
        this.wsServer = wsServer;
    }

    /**
     * 处理日志消息
     * @param {WebSocket} client WebSocket客户端连接
     * @param {string} action 执行的动作
     * @param {Object} data 消息数据
     */
    handleMessage(client, action, data) {
        try {
            switch (action) {
                case 'syncLog':
                    this.handleSyncLog(client, data);
                    break;
                case 'getStats':
                    this.handleGetStats(client, data);
                    break;
                case 'readLog':
                    this.handleReadLog(client, data);
                    break;
                default:
                    this.wsServer.sendResponse(client, 'logger', action, {
                        error: `未知的日志操作: ${action}`
                    }, false, data.messageId);
            }
        } catch (error) {
            defaultLogger.error(`处理日志消息失败 [${action}]:`, error);
            this.wsServer.sendResponse(client, 'logger', action, {
                error: `处理失败: ${error.message}`
            }, false, data.messageId);
        }
    }

    /**
     * 处理同步日志请求
     * @param {WebSocket} client WebSocket客户端连接
     * @param {Object} data 消息数据
     */
    handleSyncLog(client, data) {
        const { content, timestamp, clientId, messageId } = data;

        if (!clientId || !content || !timestamp) {
            return this.wsServer.sendResponse(client, 'logger', 'syncLog', {
                error: '缺少必要参数: clientId, content, timestamp'
            }, false, messageId);
        }

        // 调用日志服务同步日志
        const result = loggerService.syncLog(clientId, content, timestamp);

        // 发送响应
        this.wsServer.sendResponse(client, 'logger', 'syncLog', result, result.success, messageId);

        // 可选：如果需要，可以广播日志事件给其他监听者
        // this.wsServer.broadcast({
        //     type: 'logger',
        //     eventType: 'logSynced',
        //     data: {
        //         clientId: clientId.substring(0, 8),
        //         timestamp,
        //         success: result.success
        //     }
        // });
    }

    /**
     * 处理获取日志统计请求
     * @param {WebSocket} client WebSocket客户端连接
     * @param {Object} data 消息数据
     */
    handleGetStats(client, data) {
        const { messageId } = data;

        try {
            const stats = loggerService.getStats();
            this.wsServer.sendResponse(client, 'logger', 'getStats', { stats }, true, messageId);
        } catch (error) {
            defaultLogger.error('获取日志统计失败:', error);
            this.wsServer.sendResponse(client, 'logger', 'getStats', {
                error: `获取统计失败: ${error.message}`
            }, false, messageId);
        }
    }

    /**
     * 处理读取日志请求
     * @param {WebSocket} client WebSocket客户端连接
     * @param {Object} data 消息数据
     */
    handleReadLog(client, data) {
        const { clientId, lines = 100, messageId } = data;

        if (!clientId) {
            return this.wsServer.sendResponse(client, 'logger', 'readLog', {
                error: '缺少必要参数: clientId'
            }, false, messageId);
        }

        try {
            const result = loggerService.readClientLog(clientId, lines);
            this.wsServer.sendResponse(client, 'logger', 'readLog', result, result.success, messageId);
        } catch (error) {
            defaultLogger.error(`读取客户端日志失败 [${clientId}]:`, error);
            this.wsServer.sendResponse(client, 'logger', 'readLog', {
                error: `读取失败: ${error.message}`
            }, false, messageId);
        }
    }
}

module.exports = LoggerHandler; 