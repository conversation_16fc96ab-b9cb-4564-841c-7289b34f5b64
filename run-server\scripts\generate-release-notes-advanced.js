const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 高级更新日志生成脚本
 * 支持从多种来源生成更新日志
 */

class ReleaseNotesGenerator {
    constructor(version) {
        this.version = version;
        this.projectRoot = path.resolve(__dirname, '..');
    }

    // 从Git提交历史生成更新日志
    generateFromGitHistory(fromTag = null) {
        try {
            // 获取最新的tag作为起始点
            if (!fromTag) {
                try {
                    fromTag = execSync('git describe --tags --abbrev=0 HEAD~1', { 
                        encoding: 'utf8', 
                        cwd: this.projectRoot 
                    }).trim();
                } catch (error) {
                    // 如果没有tag，从第一个commit开始
                    fromTag = 'HEAD~20'; // 最近20个提交
                }
            }

            // 获取提交历史
            const command = `git log ${fromTag}..HEAD --pretty=format:"%s|%b|%an|%ad" --date=short --no-merges`;
            const gitLog = execSync(command, { 
                encoding: 'utf8', 
                cwd: this.projectRoot 
            });

            return this.parseGitCommits(gitLog);
        } catch (error) {
            console.warn('无法从Git历史生成更新日志:', error.message);
            return null;
        }
    }

    // 解析Git提交记录
    parseGitCommits(gitLog) {
        if (!gitLog.trim()) {
            return null;
        }

        const commits = gitLog.split('\n').filter(line => line.trim());
        const categories = {
            features: [],
            fixes: [],
            improvements: [],
            breaking: [],
            others: []
        };

        commits.forEach(commit => {
            const [subject, body, author, date] = commit.split('|');
            const lowerSubject = subject.toLowerCase();

            // 根据commit信息分类
            if (lowerSubject.includes('feat') || lowerSubject.includes('新增') || lowerSubject.includes('添加')) {
                categories.features.push(this.formatCommitMessage(subject));
            } else if (lowerSubject.includes('fix') || lowerSubject.includes('修复') || lowerSubject.includes('解决')) {
                categories.fixes.push(this.formatCommitMessage(subject));
            } else if (lowerSubject.includes('improve') || lowerSubject.includes('优化') || lowerSubject.includes('改进')) {
                categories.improvements.push(this.formatCommitMessage(subject));
            } else if (lowerSubject.includes('breaking') || lowerSubject.includes('重大变更')) {
                categories.breaking.push(this.formatCommitMessage(subject));
            } else {
                categories.others.push(this.formatCommitMessage(subject));
            }
        });

        return this.formatReleaseNotes(categories);
    }

    // 格式化commit信息
    formatCommitMessage(message) {
        // 移除常见的前缀
        return message
            .replace(/^(feat|fix|improve|refactor|style|docs|test|chore)[\(\):]*\s*/i, '')
            .replace(/^(新增|修复|优化|改进|重构|样式|文档|测试|构建)[\(\):]*\s*/, '')
            .trim();
    }

    // 从CHANGELOG.md文件获取更新日志
    generateFromChangelog() {
        const changelogPath = path.join(this.projectRoot, 'CHANGELOG.md');
        
        if (!fs.existsSync(changelogPath)) {
            return null;
        }

        try {
            const content = fs.readFileSync(changelogPath, 'utf8');
            
            // 查找当前版本的更新日志
            const versionRegex = new RegExp(`## \\[?v?${this.version}\\]?.*?\\n([\\s\\S]*?)(?=\\n## |$)`, 'i');
            const match = content.match(versionRegex);
            
            if (match && match[1]) {
                return match[1].trim();
            }
            
            return null;
        } catch (error) {
            console.warn('无法从CHANGELOG.md读取更新日志:', error.message);
            return null;
        }
    }

    // 从release-notes目录获取预定义的更新日志
    generateFromFile() {
        const releaseNotesPath = path.join(this.projectRoot, 'release-notes', `${this.version}.md`);
        
        if (fs.existsSync(releaseNotesPath)) {
            try {
                return fs.readFileSync(releaseNotesPath, 'utf8');
            } catch (error) {
                console.warn('无法读取预定义更新日志文件:', error.message);
            }
        }
        
        return null;
    }

    // 格式化最终的更新日志
    formatReleaseNotes(categories) {
        let releaseNotes = `## v${this.version} 更新内容\n\n`;

        if (categories.breaking.length > 0) {
            releaseNotes += '**重大变更：**\n';
            categories.breaking.forEach(item => {
                releaseNotes += `- ${item}\n`;
            });
            releaseNotes += '\n';
        }

        if (categories.features.length > 0) {
            releaseNotes += '**新功能：**\n';
            categories.features.forEach(item => {
                releaseNotes += `- ${item}\n`;
            });
            releaseNotes += '\n';
        }

        if (categories.fixes.length > 0) {
            releaseNotes += '**修复：**\n';
            categories.fixes.forEach(item => {
                releaseNotes += `- ${item}\n`;
            });
            releaseNotes += '\n';
        }

        if (categories.improvements.length > 0) {
            releaseNotes += '**改进：**\n';
            categories.improvements.forEach(item => {
                releaseNotes += `- ${item}\n`;
            });
            releaseNotes += '\n';
        }

        if (categories.others.length > 0) {
            releaseNotes += '**其他更新：**\n';
            categories.others.forEach(item => {
                releaseNotes += `- ${item}\n`;
            });
            releaseNotes += '\n';
        }

        // 如果没有任何内容，提供默认信息
        if (categories.features.length === 0 && categories.fixes.length === 0 && 
            categories.improvements.length === 0 && categories.others.length === 0) {
            releaseNotes += '本次更新包含性能优化和问题修复。\n\n';
        }

        releaseNotes += '如有任何问题或建议，请联系技术支持团队。';

        return releaseNotes;
    }

    // 生成默认更新日志
    generateDefaultReleaseNotes() {
        return `## v${this.version} 更新内容

**新功能：**
- 优化了用户界面体验
- 增强了系统稳定性
- 改进了文件处理效率

**修复：**
- 修复了已知的问题和漏洞
- 优化了内存使用
- 改善了错误处理机制

**改进：**
- 提升了整体性能
- 更新了用户帮助文档
- 增强了兼容性支持

如有任何问题或建议，请联系技术支持团队。`;
    }

    // 主生成方法 - 按优先级尝试不同来源
    generate() {
        console.log(`正在为版本 ${this.version} 生成更新日志...`);

        // 1. 首先尝试从预定义文件读取
        let releaseNotes = this.generateFromFile();
        if (releaseNotes) {
            console.log('✓ 使用预定义的更新日志文件');
            return releaseNotes;
        }

        // 2. 尝试从CHANGELOG.md读取
        releaseNotes = this.generateFromChangelog();
        if (releaseNotes) {
            console.log('✓ 从CHANGELOG.md生成更新日志');
            return releaseNotes;
        }

        // 3. 尝试从Git历史生成
        releaseNotes = this.generateFromGitHistory();
        if (releaseNotes) {
            console.log('✓ 从Git提交历史生成更新日志');
            return releaseNotes;
        }

        // 4. 使用默认模板
        console.log('⚠️  使用默认更新日志模板');
        return this.generateDefaultReleaseNotes();
    }
}

// 更新latest.yml文件
function updateLatestYml(distPath, version, releaseNotes) {
    const latestYmlPath = path.join(distPath, 'latest.yml');
    
    if (!fs.existsSync(latestYmlPath)) {
        console.log(`警告: ${latestYmlPath} 文件不存在`);
        return false;
    }

    try {
        let content = fs.readFileSync(latestYmlPath, 'utf8');
        
        // 格式化releaseNotes用于YAML
        const formattedReleaseNotes = releaseNotes
            .split('\n')
            .map(line => `  ${line}`)
            .join('\n');
        
        // 如果已存在 releaseNotes，则替换；否则添加
        if (content.includes('releaseNotes:')) {
            content = content.replace(
                /releaseNotes:[\s\S]*?(?=\n[a-zA-Z]|\n$|$)/,
                `releaseNotes: |\n${formattedReleaseNotes}`
            );
        } else {
            content = content.trim() + `\nreleaseNotes: |\n${formattedReleaseNotes}\n`;
        }

        fs.writeFileSync(latestYmlPath, content);
        console.log(`✅ 已更新更新日志到 ${latestYmlPath}`);
        return true;
        
    } catch (error) {
        console.error(`❌ 更新 ${latestYmlPath} 时发生错误:`, error.message);
        return false;
    }
}

// 主函数
function main() {
    const version = process.argv[2] || require('../package.json').version;
    const generator = new ReleaseNotesGenerator(version);
    
    // 生成更新日志
    const releaseNotes = generator.generate();
    
    // 更新各个版本的latest.yml
    const editions = [
        { name: '万唯版本', distPath: path.join(__dirname, '..', 'dist') },
        { name: '合心版本', distPath: path.join(__dirname, '..', 'dist-hexin') },
        { name: '万唯高中版本', distPath: path.join(__dirname, '..', 'dist-wwsenior') }
    ];

    console.log(`\n📝 开始为版本 ${version} 更新更新日志...`);
    
    let updateCount = 0;
    editions.forEach(({ name, distPath }) => {
        if (fs.existsSync(distPath)) {
            console.log(`\n处理 ${name}...`);
            if (updateLatestYml(distPath, version, releaseNotes)) {
                updateCount++;
            }
        } else {
            console.log(`⚠️  跳过 ${name}，目录不存在: ${distPath}`);
        }
    });

    console.log(`\n✨ 更新完成！成功更新了 ${updateCount} 个版本的更新日志。`);
    
    // 显示生成的更新日志内容
    console.log('\n📋 生成的更新日志内容:');
    console.log('='.repeat(50));
    console.log(releaseNotes);
    console.log('='.repeat(50));
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    ReleaseNotesGenerator,
    updateLatestYml
}; 