<template>
  <div v-if="visible" 
       class="log-viewer" 
       :style="{ top: position.y + 'px', left: position.x + 'px' }"
       ref="logPanel">
    <div class="log-header" @mousedown="startDrag">
      <h3>开发者日志</h3>
      <div class="log-controls">
        <button @click="clearLogs" class="control-btn">清空</button>
        <button @click="copyLogs" class="control-btn">复制</button>
        <button @click="testConnection" class="control-btn">测试连接</button>
        <button @click="closeViewer" class="close-btn">×</button>
      </div>
    </div>
    <div class="log-body">
      <div v-if="logs.length === 0" class="no-logs">
        暂无日志信息
      </div>
      <div v-else class="log-list">
        <div v-for="(log, index) in logs" :key="index" :class="['log-entry', log.type]">
          <div class="log-time">{{ formatTime(log.timestamp) }}</div>
          <div class="log-content">
            <div class="log-message">{{ log.message }}</div>
            <div v-if="log.data" class="log-data">{{ log.data }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue';
import logger from './js/logger';

export default {
  name: 'LogViewer',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const logs = ref([]);
    const position = ref({ x: 20, y: 20 });
    const isDragging = ref(false);
    const dragOffset = ref({ x: 0, y: 0 });
    const logPanel = ref(null);

    // Update logs when they change
    const updateLogs = (newLogs) => {
      logs.value = newLogs;
    };

    onMounted(() => {
      // Get initial logs
      logs.value = logger.getLogs();
      
      // Add listener for log updates
      logger.addListener(updateLogs);
      
      // Add document event listeners for dragging
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    });

    onUnmounted(() => {
      // Remove listener when component is destroyed
      logger.removeListener(updateLogs);
      
      // Remove document event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    });

    // Start dragging the log window
    const startDrag = (event) => {
      if (logPanel.value) {
        isDragging.value = true;
        const rect = logPanel.value.getBoundingClientRect();
        dragOffset.value = {
          x: event.clientX - rect.left,
          y: event.clientY - rect.top
        };
      }
    };

    // Handle mouse movement for dragging
    const handleMouseMove = (event) => {
      if (!isDragging.value) return;
      
      position.value = {
        x: event.clientX - dragOffset.value.x,
        y: event.clientY - dragOffset.value.y
      };
    };

    // Stop dragging
    const handleMouseUp = () => {
      isDragging.value = false;
    };

    // Clear all logs
    const clearLogs = () => {
      logger.clear();
    };

    // Copy logs to clipboard
    const copyLogs = () => {
      const logText = logs.value.map(log => 
        `[${log.timestamp}][${log.type}] ${log.message}${log.data ? ' - Data: ' + log.data : ''}`
      ).join('\n');
      
      navigator.clipboard.writeText(logText)
        .then(() => {
          logger.success('日志已复制到剪贴板');
        })
        .catch(err => {
          logger.error('复制失败', err);
        });
    };

    // Close the log viewer
    const closeViewer = () => {
      emit('close');
    };

    // Test connection to localhost:3000
    const testConnection = async () => {
      try {
        const startTime = Date.now();
        const response = await fetch('http://localhost:3000');
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        if (response.ok) {
          logger.success(`连接成功 (${duration}ms)`);
        } else {
          logger.error(`连接失败: HTTP ${response.status}`);
        }
      } catch (err) {
        logger.error('连接失败', err.message);
      }
    };

    // Format ISO timestamp to readable time
    const formatTime = (isoString) => {
      const date = new Date(isoString);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}.${date.getMilliseconds().toString().padStart(3, '0')}`;
    };

    return {
      logs,
      position,
      logPanel,
      startDrag,
      clearLogs,
      copyLogs,
      closeViewer,
      testConnection,
      formatTime
    };
  }
};
</script>

<style scoped>
.log-viewer {
  position: fixed;
  width: 600px;
  max-height: 400px;
  background-color: #1e1e1e;
  color: #f1f1f1;
  border-radius: 5px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  z-index: 99999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  resize: both;
}

.log-header {
  background-color: #333;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  user-select: none;
}

.log-header h3 {
  margin: 0;
  font-size: 14px;
}

.log-controls {
  display: flex;
  gap: 5px;
}

.control-btn {
  background-color: #555;
  border: none;
  color: white;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 3px;
  cursor: pointer;
}

.control-btn:hover {
  background-color: #666;
}

.close-btn {
  background: none;
  border: none;
  color: #f1f1f1;
  font-size: 18px;
  cursor: pointer;
  padding: 0 5px;
}

.close-btn:hover {
  color: #ff6b6b;
}

.log-body {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.no-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #888;
  font-style: italic;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.log-entry {
  padding: 5px 8px;
  border-radius: 3px;
  font-size: 13px;
  font-family: 'Consolas', monospace;
}

.log-entry.info {
  background-color: #2d2d2d;
  border-left: 3px solid #2196F3;
}

.log-entry.warning {
  background-color: #2d2d2d;
  border-left: 3px solid #FFC107;
}

.log-entry.error {
  background-color: #2d2d2d;
  border-left: 3px solid #F44336;
}

.log-entry.success {
  background-color: #2d2d2d;
  border-left: 3px solid #4CAF50;
}

.log-time {
  font-size: 11px;
  color: #888;
  margin-bottom: 3px;
}

.log-content {
  display: flex;
  flex-direction: column;
}

.log-message {
  word-break: break-word;
}

.log-data {
  margin-top: 3px;
  padding-top: 3px;
  border-top: 1px dashed #555;
  color: #888;
  font-size: 11px;
  max-height: 100px;
  overflow-y: auto;
  word-break: break-word;
}
</style> 