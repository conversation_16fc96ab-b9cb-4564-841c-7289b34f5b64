import{U as sn,r as pe,h as bt,v as Ve,i as Z,j as Dt,k as Is,m as Vt,_ as Rs,n as nn,o as Y,c as K,a as h,p as an,t as de,f as se,q as Re,w as Ne,s as jt,e as Yt,F as At,u as Mt,x as wt,y as rn,z as he,A as Kt,B as ds,C as dt,D as on,E as ln}from"./index-WlJwBPYs.js";function cn(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=sn.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const un={onbuttonclick:cn};var pn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function dn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function fn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var r=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,r.get?r:{enumerable:!0,get:function(){return e[a]}})}),t}var Us={exports:{}};const hn={},vn=Object.freeze(Object.defineProperty({__proto__:null,default:hn},Symbol.toStringTag,{value:"Module"})),fs=fn(vn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",r=a?window:{};r.JS_SHA1_NO_WINDOW&&(a=!1);var g=!a&&typeof self=="object",T=!r.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;T?r=pn:g&&(r=self);var E=!r.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,y=!r.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",C="0123456789abcdef".split(""),M=[-**********,8388608,32768,128],F=[24,16,8,0],B=["hex","array","digest","arrayBuffer"],R=[],z=Array.isArray;(r.JS_SHA1_NO_NODE_JS||!z)&&(z=function(u){return Object.prototype.toString.call(u)==="[object Array]"});var X=ArrayBuffer.isView;y&&(r.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!X)&&(X=function(u){return typeof u=="object"&&u.buffer&&u.buffer.constructor===ArrayBuffer});var H=function(u){var w=typeof u;if(w==="string")return[u,!0];if(w!=="object"||u===null)throw new Error(s);if(y&&u.constructor===ArrayBuffer)return[new Uint8Array(u),!1];if(!z(u)&&!X(u))throw new Error(s);return[u,!1]},ee=function(u){return function(w){return new O(!0).update(w)[u]()}},te=function(){var u=ee("hex");T&&(u=q(u)),u.create=function(){return new O},u.update=function(k){return u.create().update(k)};for(var w=0;w<B.length;++w){var m=B[w];u[m]=ee(m)}return u},q=function(u){var w=fs,m=fs.Buffer,k;m.from&&!r.JS_SHA1_NO_BUFFER_FROM?k=m.from:k=function(A){return new m(A)};var S=function(A){if(typeof A=="string")return w.createHash("sha1").update(A,"utf8").digest("hex");if(A==null)throw new Error(s);return A.constructor===ArrayBuffer&&(A=new Uint8Array(A)),z(A)||X(A)||A.constructor===m?w.createHash("sha1").update(k(A)).digest("hex"):u(A)};return S},d=function(u){return function(w,m){return new ne(w,!0).update(m)[u]()}},N=function(){var u=d("hex");u.create=function(k){return new ne(k)},u.update=function(k,S){return u.create(k).update(S)};for(var w=0;w<B.length;++w){var m=B[w];u[m]=d(m)}return u};function O(u){u?(R[0]=R[16]=R[1]=R[2]=R[3]=R[4]=R[5]=R[6]=R[7]=R[8]=R[9]=R[10]=R[11]=R[12]=R[13]=R[14]=R[15]=0,this.blocks=R):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}O.prototype.update=function(u){if(this.finalized)throw new Error(t);var w=H(u);u=w[0];for(var m=w[1],k,S=0,A,L=u.length||0,_=this.blocks;S<L;){if(this.hashed&&(this.hashed=!1,_[0]=this.block,this.block=_[16]=_[1]=_[2]=_[3]=_[4]=_[5]=_[6]=_[7]=_[8]=_[9]=_[10]=_[11]=_[12]=_[13]=_[14]=_[15]=0),m)for(A=this.start;S<L&&A<64;++S)k=u.charCodeAt(S),k<128?_[A>>>2]|=k<<F[A++&3]:k<2048?(_[A>>>2]|=(192|k>>>6)<<F[A++&3],_[A>>>2]|=(128|k&63)<<F[A++&3]):k<55296||k>=57344?(_[A>>>2]|=(224|k>>>12)<<F[A++&3],_[A>>>2]|=(128|k>>>6&63)<<F[A++&3],_[A>>>2]|=(128|k&63)<<F[A++&3]):(k=65536+((k&1023)<<10|u.charCodeAt(++S)&1023),_[A>>>2]|=(240|k>>>18)<<F[A++&3],_[A>>>2]|=(128|k>>>12&63)<<F[A++&3],_[A>>>2]|=(128|k>>>6&63)<<F[A++&3],_[A>>>2]|=(128|k&63)<<F[A++&3]);else for(A=this.start;S<L&&A<64;++S)_[A>>>2]|=u[S]<<F[A++&3];this.lastByteIndex=A,this.bytes+=A-this.start,A>=64?(this.block=_[16],this.start=A-64,this.hash(),this.hashed=!0):this.start=A}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},O.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var u=this.blocks,w=this.lastByteIndex;u[16]=this.block,u[w>>>2]|=M[w&3],this.block=u[16],w>=56&&(this.hashed||this.hash(),u[0]=this.block,u[16]=u[1]=u[2]=u[3]=u[4]=u[5]=u[6]=u[7]=u[8]=u[9]=u[10]=u[11]=u[12]=u[13]=u[14]=u[15]=0),u[14]=this.hBytes<<3|this.bytes>>>29,u[15]=this.bytes<<3,this.hash()}},O.prototype.hash=function(){var u=this.h0,w=this.h1,m=this.h2,k=this.h3,S=this.h4,A,L,_,J=this.blocks;for(L=16;L<80;++L)_=J[L-3]^J[L-8]^J[L-14]^J[L-16],J[L]=_<<1|_>>>31;for(L=0;L<20;L+=5)A=w&m|~w&k,_=u<<5|u>>>27,S=_+A+S+1518500249+J[L]<<0,w=w<<30|w>>>2,A=u&w|~u&m,_=S<<5|S>>>27,k=_+A+k+1518500249+J[L+1]<<0,u=u<<30|u>>>2,A=S&u|~S&w,_=k<<5|k>>>27,m=_+A+m+1518500249+J[L+2]<<0,S=S<<30|S>>>2,A=k&S|~k&u,_=m<<5|m>>>27,w=_+A+w+1518500249+J[L+3]<<0,k=k<<30|k>>>2,A=m&k|~m&S,_=w<<5|w>>>27,u=_+A+u+1518500249+J[L+4]<<0,m=m<<30|m>>>2;for(;L<40;L+=5)A=w^m^k,_=u<<5|u>>>27,S=_+A+S+1859775393+J[L]<<0,w=w<<30|w>>>2,A=u^w^m,_=S<<5|S>>>27,k=_+A+k+1859775393+J[L+1]<<0,u=u<<30|u>>>2,A=S^u^w,_=k<<5|k>>>27,m=_+A+m+1859775393+J[L+2]<<0,S=S<<30|S>>>2,A=k^S^u,_=m<<5|m>>>27,w=_+A+w+1859775393+J[L+3]<<0,k=k<<30|k>>>2,A=m^k^S,_=w<<5|w>>>27,u=_+A+u+1859775393+J[L+4]<<0,m=m<<30|m>>>2;for(;L<60;L+=5)A=w&m|w&k|m&k,_=u<<5|u>>>27,S=_+A+S-1894007588+J[L]<<0,w=w<<30|w>>>2,A=u&w|u&m|w&m,_=S<<5|S>>>27,k=_+A+k-1894007588+J[L+1]<<0,u=u<<30|u>>>2,A=S&u|S&w|u&w,_=k<<5|k>>>27,m=_+A+m-1894007588+J[L+2]<<0,S=S<<30|S>>>2,A=k&S|k&u|S&u,_=m<<5|m>>>27,w=_+A+w-1894007588+J[L+3]<<0,k=k<<30|k>>>2,A=m&k|m&S|k&S,_=w<<5|w>>>27,u=_+A+u-1894007588+J[L+4]<<0,m=m<<30|m>>>2;for(;L<80;L+=5)A=w^m^k,_=u<<5|u>>>27,S=_+A+S-899497514+J[L]<<0,w=w<<30|w>>>2,A=u^w^m,_=S<<5|S>>>27,k=_+A+k-899497514+J[L+1]<<0,u=u<<30|u>>>2,A=S^u^w,_=k<<5|k>>>27,m=_+A+m-899497514+J[L+2]<<0,S=S<<30|S>>>2,A=k^S^u,_=m<<5|m>>>27,w=_+A+w-899497514+J[L+3]<<0,k=k<<30|k>>>2,A=m^k^S,_=w<<5|w>>>27,u=_+A+u-899497514+J[L+4]<<0,m=m<<30|m>>>2;this.h0=this.h0+u<<0,this.h1=this.h1+w<<0,this.h2=this.h2+m<<0,this.h3=this.h3+k<<0,this.h4=this.h4+S<<0},O.prototype.hex=function(){this.finalize();var u=this.h0,w=this.h1,m=this.h2,k=this.h3,S=this.h4;return C[u>>>28&15]+C[u>>>24&15]+C[u>>>20&15]+C[u>>>16&15]+C[u>>>12&15]+C[u>>>8&15]+C[u>>>4&15]+C[u&15]+C[w>>>28&15]+C[w>>>24&15]+C[w>>>20&15]+C[w>>>16&15]+C[w>>>12&15]+C[w>>>8&15]+C[w>>>4&15]+C[w&15]+C[m>>>28&15]+C[m>>>24&15]+C[m>>>20&15]+C[m>>>16&15]+C[m>>>12&15]+C[m>>>8&15]+C[m>>>4&15]+C[m&15]+C[k>>>28&15]+C[k>>>24&15]+C[k>>>20&15]+C[k>>>16&15]+C[k>>>12&15]+C[k>>>8&15]+C[k>>>4&15]+C[k&15]+C[S>>>28&15]+C[S>>>24&15]+C[S>>>20&15]+C[S>>>16&15]+C[S>>>12&15]+C[S>>>8&15]+C[S>>>4&15]+C[S&15]},O.prototype.toString=O.prototype.hex,O.prototype.digest=function(){this.finalize();var u=this.h0,w=this.h1,m=this.h2,k=this.h3,S=this.h4;return[u>>>24&255,u>>>16&255,u>>>8&255,u&255,w>>>24&255,w>>>16&255,w>>>8&255,w&255,m>>>24&255,m>>>16&255,m>>>8&255,m&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,S>>>24&255,S>>>16&255,S>>>8&255,S&255]},O.prototype.array=O.prototype.digest,O.prototype.arrayBuffer=function(){this.finalize();var u=new ArrayBuffer(20),w=new DataView(u);return w.setUint32(0,this.h0),w.setUint32(4,this.h1),w.setUint32(8,this.h2),w.setUint32(12,this.h3),w.setUint32(16,this.h4),u};function ne(u,w){var m,k=H(u);if(u=k[0],k[1]){var S=[],A=u.length,L=0,_;for(m=0;m<A;++m)_=u.charCodeAt(m),_<128?S[L++]=_:_<2048?(S[L++]=192|_>>>6,S[L++]=128|_&63):_<55296||_>=57344?(S[L++]=224|_>>>12,S[L++]=128|_>>>6&63,S[L++]=128|_&63):(_=65536+((_&1023)<<10|u.charCodeAt(++m)&1023),S[L++]=240|_>>>18,S[L++]=128|_>>>12&63,S[L++]=128|_>>>6&63,S[L++]=128|_&63);u=S}u.length>64&&(u=new O(!0).update(u).array());var J=[],fe=[];for(m=0;m<64;++m){var me=u[m]||0;J[m]=92^me,fe[m]=54^me}O.call(this,w),this.update(fe),this.oKeyPad=J,this.inner=!0,this.sharedMemory=w}ne.prototype=new O,ne.prototype.finalize=function(){if(O.prototype.finalize.call(this),this.inner){this.inner=!1;var u=this.array();O.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(u),O.prototype.finalize.call(this)}};var ie=te();ie.sha1=ie,ie.sha1.hmac=N(),E?e.exports=ie:r.sha1=ie})()})(Us);var gn=Us.exports;const mn=dn(gn);function hs(){return"http://worksheet.hexinedu.com"}function yt(){return"http://127.0.0.1:3000"}function vs(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const xt=async(e,s,t,a={},r=8e3)=>{try{return await Promise.race([e(),new Promise((g,T)=>setTimeout(()=>T(new Error("WebSocket请求超时，切换到HTTP")),r))])}catch{try{let T;return s==="get"?T=await Dt.get(t,{params:a}):s==="post"?T=await Dt.post(t,a):s==="delete"&&(T=await Dt.delete(t)),T.data}catch(T){throw new Error(`请求失败: ${T.message||"未知错误"}`)}}};function bn(e,s,t,a){const r=[e,s,t,a].join(":");return mn(r)}function wn(){const e=pe(""),s=pe(""),t=pe(""),a=bt({}),r=pe(""),g=pe("");let T="",E=null;const y=pe("c:\\Temp"),C=bt({appKey:"",appSecret:""}),M=bt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),F=pe(""),B=pe("junior"),R=pe(null),z=pe(!1),X=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],H=()=>Ve.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],ee=bt(H()),te=async()=>{try{const n=await Z.getWatcherStatus();n.data&&n.data.watchDir&&(y.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${y.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},q=async()=>{var n,o,i;try{if(!C.appKey||!C.appSecret)throw new Error("未初始化app信息");const f=60,l=Date.now();let v;try{const W=window.Application.PluginStorage.getItem("token_info");W&&(v=JSON.parse(W))}catch(W){v=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${W.message}</span><br/>`}if(v&&v.access_token&&v.expired_time>l+f*1e3)return v.access_token;const c=C.appKey,D="1234567",$=Math.floor(Date.now()/1e3),P=bn(c,D,C.appSecret,$),U=await Dt.get(hs()+"/api/open/account/v1/auth/token",{params:{app_key:c,app_nonstr:D,app_timestamp:$,app_signature:P}});if((o=(n=U.data)==null?void 0:n.data)!=null&&o.access_token){const W=U.data.data.access_token;let Q;if(U.data.data.expired_time){const ce=parseInt(U.data.data.expired_time);Q=l+ce*1e3,t.value+=`<span class="log-item info">token已更新，有效期${ce}秒</span><br/>`}else Q=l+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const oe={access_token:W,expired_time:Q};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(oe))}catch(ce){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${ce.message}</span><br/>`}return W}else throw new Error(((i=U.data)==null?void 0:i.message)||"获取access_token失败")}catch(f){throw t.value+=`<span class="log-item error">获取access_token失败: ${f.message}</span><br/>`,f}},d=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},N=()=>{const n=window.Application,o=n.Documents.Count;for(let i=1;i<=o;i++){const f=n.Documents.Item(i);if(f.DocID===r.value)return f}return null},O=()=>{try{const n=N();if(!n)return{isValid:!1,message:"未找到当前文档"};let o="";try{o=n.Name||""}catch{try{o=S("getDocName")||""}catch{o=""}}if(o){const i=o.toLowerCase();return i.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:i.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},ne=(n,o="",i=0,f=null)=>{try{const l=window.Application,v=N();let c;if(f)c=f;else{const D=v.ActiveWindow.Selection;if(!D||D.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;c=D.Range}if(o){const D=c.Text,$=c.Find;$.ClearFormatting(),$.Text=o,$.Forward=!0,$.Wrap=1;let P=0,U=[];for(;$.Execute()&&($.Found&&c.Start<=$.Parent.Start&&$.Parent.End<=c.End);){const W=$.Parent.Start,Q=$.Parent.End;if(U.some(ce=>W===ce.start&&Q===ce.end))$.Parent.Start=Q;else{if(U.push({start:W,end:Q}),i===-1||P===i){const ce=v.Comments.Add($.Parent,n);if(i!==-1&&P===i)return t.value+=`<span class="log-item success">已为第${i+1}个"${o}"添加批注: "${n}"</span><br/>`,!0}P++,$.Parent.Start=Q}}return i!==-1&&P<=i?(t.value+=`<span class="log-item error">在${f?"指定范围":"选中内容"}中未找到第${i+1}个"${o}"</span><br/>`,!1):i===-1&&P>0?(t.value+=`<span class="log-item success">已为${P}处"${o}"添加批注: "${n}"</span><br/>`,!0):i===-1&&P===0?(t.value+=`<span class="log-item error">在${f?"指定范围":"选中内容"}中未找到关键字"${o}"</span><br/>`,!1):!0}else{const D=v.Comments.Add(c,n);return t.value+=`<span class="log-item success">已为${f?"指定范围":"选中内容"}添加批注: "${n}"</span><br/>`,!0}}catch(l){return t.value+=`<span class="log-item error">添加批注失败: ${l.message}</span><br/>`,!1}},ie=n=>n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",u=n=>n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"进行中",w=n=>{const o=Date.now()-n,i=Math.floor(o/1e3);return i<60?`${i}秒`:i<3600?`${Math.floor(i/60)}分${i%60}秒`:`${Math.floor(i/3600)}时${Math.floor(i%3600/60)}分`},m=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const i=N();if(i&&i.ContentControls)for(let f=1;f<=i.ContentControls.Count;f++)try{const l=i.ContentControls.Item(f);if(l&&l.Title&&(l.Title===`任务_${n}`||l.Title===`任务增强_${n}`||l.Title===`校对_${n}`)){const v=l.Title===`任务增强_${n}`||a[n].isEnhanced,c=l.Title===`校对_${n}`||a[n].isCheckTask;c?l.Title=`已停止校对_${n}`:v?l.Title=`已停止增强_${n}`:l.Title=`已停止_${n}`;const D=c?"校对":v?"增强":"普通";t.value+=`<span class="log-item info">已将${D}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(i){t.value+=`<span class="log-item warning">更新控件标题失败: ${i.message}</span><br/>`}let o=null;if(G[n]&&G[n].urlId){o=G[n].urlId;try{try{const i=await xt(async()=>await Z.getUrlMonitorStatus(),"get",`${yt()}/api/url/status`),l=(Array.isArray(i)?i:i.data?Array.isArray(i.data)?i.data:[]:[]).find(v=>v.urlId===o);l&&l.downloadedPath&&(a[n].resultFile=l.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${l.downloadedPath}</span><br/>`)}catch(i){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${i.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await Se(o),delete G[n]}catch(i){t.value+=`<span class="log-item warning">停止URL监控出错: ${i.message}，将重试</span><br/>`,delete G[n],setTimeout(async()=>{try{o&&await xt(async()=>await Z.stopUrlMonitoring(o),"delete",`${yt()}/api/url/monitor/${o}`)}catch(f){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${f.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(o){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${o.message}</span><br/>`,G[n]&&delete G[n]}},k=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const o=N();if(o&&o.ContentControls)for(let f=1;f<=o.ContentControls.Count;f++)try{const l=o.ContentControls.Item(f);if(l&&l.Title&&(l.Title===`任务_${n}`||l.Title===`任务增强_${n}`||l.Title===`校对_${n}`)){const v=l.Title===`任务增强_${n}`||a[n].isEnhanced,c=l.Title===`校对_${n}`||a[n].isCheckTask;c?l.Title=`已停止校对_${n}`:v?l.Title=`已停止增强_${n}`:l.Title=`已停止_${n}`,l.LockContents=!1;const D=c?"校对":v?"增强":"普通";t.value+=`<span class="log-item info">已将${D}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let i=null;if(G[n]&&G[n].urlId){i=G[n].urlId;try{const f=await Z.getUrlMonitorStatus(),v=(Array.isArray(f)?f:f.data?Array.isArray(f.data)?f.data:[]:[]).find(c=>c.urlId===i);v&&v.downloadedPath&&(a[n].resultFile=v.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${v.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await Se(i),delete G[n]}catch(f){t.value+=`<span class="log-item warning">停止URL监控出错: ${f.message}，将重试</span><br/>`,delete G[n]}}},S=n=>un.onbuttonclick(n),A=()=>{try{e.value=S("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},L=()=>{N().ActiveWindow.Selection.Copy()},_=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${y.value}\\${n}`,12,"","",!1),o.Close(),N().ActiveWindow.Activate()},J=n=>{const o=window.Application.Documents.Add("",!1,0,!1);o.Content.Paste(),o.SaveAs2(`${y.value}\\${n}`,12,"","",!1),o.Close()},fe=n=>{try{const i=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,f=window.Application.Documents.Add("",!1,0,!1);f.Content.Paste();const l=`${i}\\${n}`;f.SaveAs2(l,12,"","",!1),f.Close(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${l}.docx</span><br/>`}catch(o){throw t.value+=`<span class="log-item error">方式三保存失败: ${o.message}</span><br/>`,o}},me=n=>{const o=window.Application.Documents.Add();o.Content.Paste(),o.SaveAs2(`${y.value}\\${n}`,12,"","",!1),N().ActiveWindow.Activate()},Ce=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let o="method2";try{const i=await Z.getSaveMethod();if(i.success&&i.saveMethod){o=i.saveMethod;const f=o==="method1"?"方式一":o==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${f}</span><br/>`}}catch(i){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${i.message}</span><br/>`}o==="method1"?(_(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${y.value}\\${n}.docx</span><br/>`):o==="method2"?(J(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${y.value}\\${n}.docx</span><br/>`):o==="method3"?(fe(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):o==="method4"&&(me(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${y.value}\\${n}.docx</span><br/>`),Z.associateFileWithClient(`${n}.docx`).then(i=>{i.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${i.message||"未知错误"}</span><br/>`}).catch(i=>{t.value+=`<span class="log-item warning">关联文件时出错: ${i.message}</span><br/>`})}catch(o){t.value+=`<span class="log-item error">保存文件失败: ${o.message}</span><br/>`}},je=pe(null),be=bt([]),ke=new Set,Ae=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),we=async n=>{try{const o=n.slice(T.length);if(o.trim()){const i=Z.getClientId();if(!i){console.warn("无法获取客户端ID，跳过日志同步");return}const f=Ae(o);if(!f.trim()){T=n;return}await Z.sendRequest("logger","syncLog",{content:f,timestamp:new Date().toISOString(),clientId:i}),T=n}}catch(o){console.error("同步日志到服务端失败:",o)}},$e=()=>{Z.connect().then(()=>{te()}).catch(o=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${o.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const o=[];for(const i in a)if(a.hasOwnProperty(i)){const f=a[i];f.status===1&&!f.terminated&&(o.includes(i)||o.push(i))}if(o.length>0){let i=!1;try{const f=N();if(f){const v=`taskpane_id_${f.DocID}`,c=window.Application.PluginStorage.getItem(v);if(c){const D=window.Application.GetTaskPane(c);D&&(i=D.Visible)}}}catch(f){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${f.message}</span><br/>`,i=!0}setTimeout(()=>{if(i){const f=`检测到 ${o.length} 个未完成的任务，是否继续？`;b(f).then(l=>{l?(t.value+=`<span class="log-item info">用户选择继续 ${o.length} 个进行中的任务 (by taskId)...</span><br/>`,Z.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:o}).then(v=>{v&&v.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${v.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(v==null?void 0:v.message)||"未知错误"}</span><br/>`}).catch(v=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${v.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',o.forEach(v=>{m(v)}),t.value+=`<span class="log-item success">${o.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(l=>{t.value+=`<span class="log-item error">弹窗错误: ${l.message}，默认停止任务（保留控件）</span><br/>`,o.forEach(v=>{m(v)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Z.setConnectionSuccessHandler(n),Z.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),Z.addEventListener("connection",o=>{o.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${o.reason||"未知"}, 代码: ${o.code||"N/A"}，将自动重连</span><br/>`)}),Z.addEventListener("watcher",o=>{var i,f;if(be.push(o),o.type,o.eventType==="uploadSuccess"){const l=(i=o.data)==null?void 0:i.file,v=l==null?void 0:l.replace(/\.docx$/,""),c=`${o.eventType}_${l}_${Date.now()}`;if(ke.has(c)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${l}</span><br/>`;return}if(ke.add(c),ke.size>100){const $=ke.values();ke.delete($.next().value)}const D=v&&((f=a[v])==null?void 0:f.wordType);Pe(o,D)}else o.eventType&&Pe(o,null)}),Z.addEventListener("urlMonitor",o=>{be.push(o),o.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${o.eventType||o.action}</span><br/>`),o.eventType&&Pe(o,null)}),Z.addEventListener("health",o=>{}),Z.addEventListener("error",o=>{const i=o.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${i}</span><br/>`,console.error("WebSocket错误:",o)})},G=bt({}),ye=async(n,o,i=!1,f=5e3,l={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const v=await Z.startUrlMonitoring(n,f,{downloadOnSuccess:l.downloadOnSuccess!==void 0?l.downloadOnSuccess:!0,appKey:l.appKey,filename:l.filename,taskId:o}),c=v.success||(v==null?void 0:v.success),D=v.urlId||(v==null?void 0:v.urlId);if(c&&D){G[o]={urlId:D,url:n,isResultUrl:i,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${D}</span><br/>`;try{await Z.startUrlChecking(D)}catch{}return D}else throw new Error("服务器返回失败")}catch(v){return t.value+=`<span class="log-item error">启动URL监控失败: ${v.message}</span><br/>`,null}},Se=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(G).forEach(i=>{G[i].urlId===n&&delete G[i]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const o=await xt(async()=>await Z.stopUrlMonitoring(n),"delete",`${yt()}/api/url/monitor/${n}`);return o&&(o.success||o!=null&&o.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(o){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${o.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await xt(async()=>await Z.stopUrlMonitoring(n),"delete",`${yt()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},le=async()=>{try{const n=await xt(async()=>await Z.getUrlMonitorStatus(),"get",`${yt()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Ge=async n=>{try{return await Z.forceUrlCheck(n)}catch{return!1}},Pe=async(n,o)=>{var i;if(n.eventType==="uploadSuccess"){const f=n.data.file,l=f.replace(/\.docx$/,"");if(a[l]){if(a[l].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${l.substring(0,8)} 的上传通知</span><br/>`;return}if(a[l].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${l.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${f} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${o||a[l].wordType||"wps-analysis"}</span><br/>`,a[l].status=1,a[l].uploadSuccess=!0,await Ze(l,o||a[l].wordType||"wps-analysis")}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:f,url:l,taskId:v,downloadedPath:c}=n.data,D=Object.keys(G).filter($=>G[$].urlId===f);D.length>0&&D.forEach($=>{c&&a[$]&&(a[$].resultFile=c,a[$].resultDownloaded=!0),delete G[$]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:f,url:l,filePath:v,taskId:c}=n.data;if(!Object.values(G).some($=>$.urlId===f)&&c&&((i=a[c])!=null&&i.terminated))return;if(c&&a[c]&&a[c].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${f} 的文件下载通知</span><br/>`,f)try{await Z.stopUrlMonitoring(f),G[c]&&delete G[c]}catch{}return}if(c&&a[c]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${v}</span><br/>`,a[c].isCheckTask&&v.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${v}</span><br/>`;try{const P=window.Application.FileSystem.ReadFile(v),U=JSON.parse(P);if(await ve(U,c)){a[c].status=2,t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}已完成批注处理</span><br/>`;const Q=w(a[c].startTime);t.value+=`<span class="log-item success">校对任务${c.substring(0,8)}完成，总耗时${Q}</span><br/>`}}catch($){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${$.message}</span><br/>`,$.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${v}</span><br/>`),a[c].status=-1,a[c].errorMessage=`JSON处理失败: ${$.message}`,t.value+=`<span class="log-item error">校对任务${c.substring(0,8)}处理失败</span><br/>`}}else{a[c].resultFile=v,a[c].resultDownloaded=!0;const $=w(a[c].startTime);t.value+=`<span class="log-item success">任务${c.substring(0,8)}完成，总耗时${$}</span><br/>`,await We(c)}G[c]&&G[c].urlId&&(Se(G[c].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete G[c])}else if(f){t.value+=`<span class="log-item info">URL文件已下载: ${v}</span><br/>`;const $=Object.keys(G).filter(P=>{var U;return G[P].urlId===f&&!((U=a[P])!=null&&U.terminated)});$.length>0&&$.forEach(async P=>{if(a[P]){if(t.value+=`<span class="log-item info">关联到任务: ${P.substring(0,8)}</span><br/>`,a[P].resultFile=v,a[P].resultDownloaded=!0,a[P].isCheckTask&&v.endsWith(".wps.json"))try{const W=window.Application.FileSystem.ReadFile(v),Q=JSON.parse(W);if(await ve(Q,P)&&a[P].status===1){a[P].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const ce=w(a[P].startTime);t.value+=`<span class="log-item success">校对任务${P.substring(0,8)}完成，总耗时${ce}</span><br/>`}}catch(U){t.value+=`<span class="log-item error">处理校对JSON失败: ${U.message}</span><br/>`,a[P].status===1&&(a[P].status=-1,a[P].errorMessage=`JSON处理失败: ${U.message}`,t.value+=`<span class="log-item error">校对任务${P.substring(0,8)}处理失败</span><br/>`)}else if(a[P].status===1){a[P].status=2;const U=w(a[P].startTime);t.value+=`<span class="log-item success">任务${P.substring(0,8)}完成，总耗时${U}</span><br/>`}}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:f,url:l,error:v,taskId:c}=n.data;if(c&&a[c]&&a[c].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${c.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${v}</span><br/>`,c&&a[c]){a[c].status=-1,a[c].errorMessage=`下载失败: ${v}`;try{const D=N();if(D&&D.ContentControls)for(let $=1;$<=D.ContentControls.Count;$++)try{const P=D.ContentControls.Item($);if(P&&P.Title&&(P.Title===`任务_${c}`||P.Title===`任务增强_${c}`||P.Title===`校对_${c}`)){const U=P.Title===`任务增强_${c}`||a[c].isEnhanced,W=P.Title===`校对_${c}`||a[c].isCheckTask;W?P.Title=`异常校对_${c}`:U?P.Title=`异常增强_${c}`:P.Title=`异常_${c}`;const Q=W?"校对":U?"增强":"普通";t.value+=`<span class="log-item info">已将${Q}任务${c.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(D){t.value+=`<span class="log-item warning">更新控件标题失败: ${D.message}</span><br/>`}ge(c),G[c]&&delete G[c]}if(f)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${f}</span><br/>`,await xt(async()=>await Z.stopUrlMonitoring(f),"delete",`${yt()}/api/url/monitor/${f}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},Ze=async(n,o="wps-analysis")=>{try{if(!C.appKey)throw new Error("未初始化appKey信息");const i=await q(),f=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,l=await Dt.post(hs()+"/api/open/ticket/v1/ai_comment/create",{access_token:i,data:{app_key:C.appKey,subject:F.value,stage:B.value,file_name:`${n}`,word_url:f,word_type:o,is_ai_auto:!0,is_ai_edit:!0,create_user_id:C.userId,create_username:C.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),v=l.data.data.ticket_id;if(!v)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",ge(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${v}，开始监控结果文件</span><br/>`;let c,D;o==="wps-check"?(c=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${C.appKey}/ai/${v}.wps.json`,D=`${v}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${D}</span><br/>`):(c=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${v}.wps.docx`,D=`${v}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${D}</span><br/>`);const $={downloadOnSuccess:!0,filename:D,appKey:C.appKey,ticketId:v,taskId:n},P=await ye(c,n,!0,3e3,$);return a[n]&&(a[n].ticketId=v,a[n].resultUrl=c,a[n].urlMonitorId=P,a[n].status=1),l.data}catch(i){return t.value+=`<span class="log-item error">API调用失败: ${i.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${i.message}`,ge(n)),!1}},ze=async(n,o="wps-analysis")=>new Promise((i,f)=>{try{t.value+=`<span class="log-item info">监控目录: ${y.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${o}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=o,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const l=1e3,v=30;let c=0;const D=setInterval(()=>{if(c++,a[n]&&a[n].uploadSuccess){clearInterval(D),i(!0);return}c>=v&&(clearInterval(D),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',f(new Error("上传超时，未收到完成通知")))},l)}catch(l){t.value+=`<span class="log-item error">任务处理异常: ${l.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${l.message}`),ge(n),f(l)}}),Qe=async()=>{var i;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,o=n.ActiveDocument;if(r.value=o.DocID,g.value=n.ActiveWindow.Index,o!=null&&o.ContentControls)for(let f=1;f<=o.ContentControls.Count;f++){const l=o.ContentControls.Item(f);if(l&&l.Title){let v=null,c=1,D=!1,$=!1;if(l.Title.startsWith("任务增强_")?(v=l.Title.substring(5),c=1,D=!0):l.Title.startsWith("任务_")?(v=l.Title.substring(3),c=1):l.Title.startsWith("校对_")?(v=l.Title.substring(3),c=1,$=!0):l.Title.startsWith("已完成增强_")?(v=l.Title.substring(6),c=2,D=!0):l.Title.startsWith("已完成校对_")?(v=l.Title.substring(6),c=2,$=!0):l.Title.startsWith("已完成_")?(v=l.Title.substring(4),c=2):l.Title.startsWith("异常增强_")?(v=l.Title.substring(5),c=-1,D=!0):l.Title.startsWith("异常校对_")?(v=l.Title.substring(5),c=-1,$=!0):l.Title.startsWith("异常_")?(v=l.Title.substring(3),c=-1):l.Title.startsWith("已停止增强_")?(v=l.Title.substring(6),c=4,D=!0):l.Title.startsWith("已停止校对_")?(v=l.Title.substring(6),c=4,$=!0):l.Title.startsWith("已停止_")&&(v=l.Title.substring(4),c=4),v&&!a[v]){let P="";try{P=((i=l.Range)==null?void 0:i.Text)||""}catch{}let U=Date.now()-24*60*60*1e3;try{if(v.length===24){const oe=v.substring(0,8),ce=parseInt(oe,16);!isNaN(ce)&&ce>0&&ce<2147483647&&(U=ce*1e3)}else{const oe=Date.now()-864e5;c===2?U=oe-60*60*1e3:c===-1?U=oe-30*60*1e3:c===4?U=oe-45*60*1e3:U=oe}}catch{}a[v]={status:c,startTime:U,contentControlId:l.ID,isEnhanced:D,isCheckTask:$,selectedText:P};const W=c===1?"进行中":c===2?"已完成":c===-1?"异常":c===4?"已停止":"未知",Q=$?"校对":D?"增强":"普通";t.value+=`<span class="log-item info">发现已有${Q}任务: ${v.substring(0,8)}, 状态: ${W}</span><br/>`}}}},ge=(n,o=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}o&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const i=N();if(!i){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let f=!1;const l=a[n].isEnhanced;if(i.ContentControls&&i.ContentControls.Count>0)for(let v=i.ContentControls.Count;v>=1;v--)try{const c=i.ContentControls.Item(v);c&&c.Title&&c.Title.includes(n)&&(c.LockContents=!1,c.Delete(!1),f=!0,console.log(c.Title),t.value+=`<span class="log-item success">已解锁并删除${l?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(c){t.value+=`<span class="log-item warning">访问第${v}个控件时出错: ${c.message}</span><br/>`;continue}f?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${l?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(i){t.value+=`<span class="log-item error">删除内容控件失败: ${i.message}</span><br/>`}},et=n=>{var o;try{const i=window.wps,f=N();if(!f||!f.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const l=(o=a[n])==null?void 0:o.isEnhanced;let v=null;for(let D=1;D<=f.ContentControls.Count;D++)try{const $=f.ContentControls.Item(D);if($&&$.Title&&$.Title.includes(n)){v=$;break}}catch{continue}if(!v){const D=a[n];D&&(D.status===2||D.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}v.Range.Select();const c=i.Windows.Item(g.value);c&&c.Selection&&c.Selection.Range&&c.ScrollIntoView(c.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${l?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(i){t.value+=`<span class="log-item error">跳转到任务控件失败: ${i.message}</span><br/>`}},We=async n=>{var c,D,$;const o=N(),i=a[n];if(!i){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(i.terminated||i.status!==1)return;if(i.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const f=N();let l=null;if(f&&f.ContentControls)for(let P=1;P<=f.ContentControls.Count;P++){const U=f.ContentControls.Item(P);if(U&&U.Title&&U.Title.includes(n)){l=U;break}}if(!l){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,i.errorMessage&&i.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${i.errorMessage}</span><br/>`;return}return}if(i.errorMessage&&i.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${i.errorMessage}</span><br/>`,ge(n);return}if(!i.resultFile)return;a[n].documentInserted=!0;const v=l.Range;l.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${i.resultFile}...</span><br/>`;const P=o.Range(v.End,v.End);P.InsertParagraphAfter(),v.Text.includes("\v")&&P.InsertAfter("\v");let U=P.End;const W=(c=v.Tables)==null?void 0:c.Count;if(W){const ue=v.Tables.Item(W);((D=ue==null?void 0:ue.Range)==null?void 0:D.End)>U&&(ue.Range.InsertParagraphAfter(),U=($=ue==null?void 0:ue.Range)==null?void 0:$.End)}o.Range(U,U).InsertFile(i.resultFile);for(let ue=1;ue<=f.ContentControls.Count;ue++){const at=f.ContentControls.Item(ue);if(at&&at.Title&&(at.Title===`任务_${n}`||at.Title===`任务增强_${n}`)){l=at;break}}const oe=o.Range(l.Range.End-1,l.Range.End);oe.Text.includes("\r")&&oe.Delete(),a[n].status=2,G[n]&&G[n].urlId&&Z.sendRequest("urlMonitor","updateTaskStatus",{urlId:G[n].urlId,status:"completed",taskId:n}).then(ue=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(ue=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${ue.message}</span><br/>`});const Ie=w(i.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${Ie}</span><br/>`;const xe=l.Title===`任务增强_${n}`||i.isEnhanced,Te=l.Title===`校对_${n}`||i.isCheckTask;if(l){Te?l.Title=`已完成校对_${n}`:xe?l.Title=`已完成增强_${n}`:l.Title=`已完成_${n}`;const ue=Te?"校对":xe?"增强":"普通";t.value+=`<span class="log-item info">已将${ue}任务${n.substring(0,8)}控件标记为已完成</span><br/>`}}catch(P){a[n].documentInserted=!1,a[n].status=-1;const U=l.Title===`任务增强_${n}`||i.isEnhanced,W=l.Title===`校对_${n}`||i.isCheckTask;if(l){W?l.Title=`异常校对_${n}`:U?l.Title=`异常增强_${n}`:l.Title=`异常_${n}`;const Q=W?"校对":U?"增强":"普通";t.value+=`<span class="log-item info">已将${Q}任务${n.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${P.message}</span><br/>`}},ot=async()=>{const n=(f=1e3)=>new Promise(l=>{setTimeout(()=>l(),f)}),o=new Map,i=Object.keys(a).filter(f=>a[f].status===1&&!a[f].terminated);for(i.length?(i.forEach(f=>{o.has(f)||o.set(f,Date.now())}),await Promise.all(i.map(f=>We(f)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const f=Object.keys(a).filter(l=>a[l].status===1&&!a[l].terminated);f.forEach(l=>{o.has(l)||o.set(l,Date.now());const v=o.get(l);(Date.now()-v)/1e3/60>=5e4&&a[l]&&!a[l].terminated&&(a[l].terminated=!0,a[l].status=-1,t.value+=`<span class="log-item warning">任务 ${l} 执行超过5分钟，已自动终止</span><br/>`,o.delete(l))}),f.length&&await Promise.all(f.map(l=>We(l)))}},Me=async(n="wps-analysis")=>{const o=ct();if(o){const{primary:f,all:l}=o,{taskId:v,control:c,task:D,isEnhanced:$,overlapType:P}=f,U=l.filter(W=>W.task&&W.task.status===1);if(U.length>0){const W=U.map(Q=>Q.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${W})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${l.length}个已有任务重叠，重叠类型: ${P}</span><br/>`,pt();try{for(const W of l){const{taskId:Q,control:oe,task:ce,isEnhanced:Ie}=W;t.value+=`<span class="log-item info">删除重叠的${Ie?"增强":"普通"}任务 ${Q.substring(0,8)}，状态为 ${u((ce==null?void 0:ce.status)||0)}</span><br/>`,ce&&(ce.status=3,ce.terminated=!0,ce.errorMessage="用户重新创建任务时删除");try{oe.LockContents=!1,oe.Delete(!1),a[Q]&&(a[Q].placeholderRemoved=!0)}catch(xe){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${xe.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${l.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(W){return x(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${W.message}</span><br/>`,Promise.reject(W)}x()}const i=vs().replace(/-/g,"").substring(0,8);return new Promise(async(f,l)=>{try{const v=window.wps,c=N(),D=c.ActiveWindow.Selection,$=D.Range,P=D.Text||"";if(s.value=P,L(),$){const U=$.Paragraphs,W=U.Item(1),Q=U.Item(U.Count),oe=W.Range.Start,ce=Q.Range.End,Ie=$.Start,xe=$.End,Te=c.Range(Math.min(oe,Ie),Math.max(ce,xe));try{let ue=c.ContentControls.Add(v.Enum.wdContentControlRichText,Te);if(!ue){if(console.log("创建内容控件失败"),ue=c.ContentControls.Add(v.Enum.wdContentControlRichText),!ue){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',l(new Error("创建内容控件失败"));return}$.Cut(),ue.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const at=n==="wps-enhance_analysis";ue.Title=at?`任务增强_${i}`:`任务_${i}`,ue.LockContents=!0,a[i]||(a[i]={}),a[i].contentControlId=ue.ID,a[i].isEnhanced=at,t.value+=`<span class="log-item info">已创建${at?"增强":"普通"}内容控件并锁定选区</span><br/>`;const tn=ue.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${tn.length}</span><br/>`}catch(ue){t.value+=`<span class="log-item error">创建内容控件失败: ${ue.message}</span><br/>`,l(ue);return}}await Ce(i),a[i]={status:1,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:P},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${i}，类型: ${n}</span><br/>`;try{await ze(i,n)?f():(a[i]&&a[i].status===1&&(a[i].status=-1,a[i].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${i.substring(0,8)}失败</span><br/>`,j(i)),l(new Error("API调用失败或超时")))}catch(U){a[i]&&(a[i].status=-1,a[i].errorMessage=`执行错误: ${U.message}`,t.value+=`<span class="log-item error">任务${i.substring(0,8)}执行出错: ${U.message}</span><br/>`,j(i)),l(U)}}catch(v){l(v)}})},Oe=async()=>{const n=ct();if(n){const{primary:i,all:f}=n,{taskId:l,control:v,task:c,isEnhanced:D,overlapType:$}=i,P=f.filter(U=>U.task&&U.task.status===1);if(P.length>0){const U=P.map(W=>W.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${U})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${f.length}个已有任务重叠，重叠类型: ${$}</span><br/>`,pt();try{for(const U of f){const{taskId:W,control:Q,task:oe,isEnhanced:ce}=U;t.value+=`<span class="log-item info">删除重叠的校对任务 ${W.substring(0,8)}，状态为 ${u((oe==null?void 0:oe.status)||0)}</span><br/>`,oe&&(oe.status=3,oe.terminated=!0,oe.errorMessage="用户重新创建校对任务时删除");try{Q.LockContents=!1,Q.Delete(!1),a[W]&&(a[W].placeholderRemoved=!0)}catch(Ie){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${Ie.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${f.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(U){return x(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${U.message}</span><br/>`,Promise.reject(U)}x()}const o=vs().replace(/-/g,"").substring(0,8);return console.log("uuid",o),new Promise(async(i,f)=>{try{const l=window.wps,v=N(),c=v.ActiveWindow.Selection,D=c.Range,$=c.Text||"";if(s.value=$,L(),D){const P=D.Paragraphs,U=P.Item(1),W=P.Item(P.Count),Q=U.Range.Start,oe=W.Range.End,ce=D.Start,Ie=D.End,xe=v.Range(Math.min(Q,ce),Math.max(oe,Ie));try{let Te=v.ContentControls.Add(l.Enum.wdContentControlRichText,xe);if(!Te){if(console.log("创建校对内容控件失败"),Te=v.ContentControls.Add(l.Enum.wdContentControlRichText),!Te){t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',f(new Error("创建校对内容控件失败"));return}D.Cut(),Te.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',Te.Title=`校对_${o}`,Te.LockContents=!0,a[o]||(a[o]={}),a[o].contentControlId=Te.ID,a[o].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const ue=Te.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${ue.length}</span><br/>`}catch(Te){t.value+=`<span class="log-item error">创建校对内容控件失败: ${Te.message}</span><br/>`,f(Te);return}}await Ce(o),a[o]={status:1,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:$},t.value+=`<span class="log-item success">创建校对任务: ${o}，类型: wps-check</span><br/>`,console.log("uuid",o);try{await ze(o,"wps-check")?i():(a[o]&&a[o].status===1&&(a[o].status=-1,a[o].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}失败</span><br/>`,j(o)),f(new Error("校对API调用失败或超时")))}catch(P){a[o]&&(a[o].status=-1,a[o].errorMessage=`校对执行错误: ${P.message}`,t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}执行出错: ${P.message}</span><br/>`,j(o)),f(P)}}catch(l){f(l)}})},tt=async()=>{},Be=()=>Je()>0?"status-error":qe()>0?"status-running":st()>0?"status-completed":"",qe=()=>Object.values(a).filter(n=>n.status===1).length,st=()=>Object.values(a).filter(n=>n.status===2).length,Je=()=>Object.values(a).filter(n=>n.status===-1).length,it=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,o=N();if(!o){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let i=0;if(Object.keys(a).forEach(f=>{try{a[f].status===1&&(a[f].status=3,a[f].terminated=!0,a[f].errorMessage="强制清除"),ge(f),i++}catch(l){t.value+=`<span class="log-item warning">清除任务${f.substring(0,8)}失败: ${l.message}</span><br/>`}}),o.ContentControls&&o.ContentControls.Count>0)for(let f=o.ContentControls.Count;f>=1;f--)try{const l=o.ContentControls.Item(f);if(l&&l.Title&&(l.Title.startsWith("任务_")||l.Title.startsWith("任务增强_")||l.Title.startsWith("已完成_")||l.Title.startsWith("已完成增强_")||l.Title.startsWith("异常_")||l.Title.startsWith("异常增强_")||l.Title.startsWith("已停止_")||l.Title.startsWith("已停止增强_")))try{l.LockContents=!1,l.Delete(!1);let v;l.Title.startsWith("任务增强_")?v=l.Title.substring(5):l.Title.startsWith("任务_")?v=l.Title.substring(3):l.Title.startsWith("已完成增强_")?v=l.Title.substring(6):l.Title.startsWith("已完成_")?v=l.Title.substring(4):l.Title.startsWith("异常增强_")?v=l.Title.substring(5):l.Title.startsWith("异常_")?v=l.Title.substring(3):l.Title.startsWith("已停止增强_")?v=l.Title.substring(6):l.Title.startsWith("已停止_")&&(v=l.Title.substring(4)),a[v]?(a[v].placeholderRemoved=!0,a[v].status=3):a[v]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},i++,t.value+=`<span class="log-item success">已删除任务${v.substring(0,8)}的内容控件</span><br/>`}catch(v){t.value+=`<span class="log-item error">删除控件失败: ${v.message}</span><br/>`}}catch(l){t.value+=`<span class="log-item warning">访问控件时出错: ${l.message}</span><br/>`}i>0?t.value+=`<span class="log-item success">已清除${i}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},nt=async()=>{try{const n=Object.values(G).map(o=>o.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const o of n)await Se(o)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Et=()=>{Is(async()=>{try{const o=window.Application.PluginStorage.getItem("user_info");if(!o)throw new Error("未找到用户信息");const i=JSON.parse(o);if(!i.orgs||!i.orgs[0])throw new Error("未找到组织信息");C.appKey=i.appKey,C.userName=i.nickname,C.userId=i.userId,C.appSecret=i.appSecret}catch(o){t.value+=`<span class="log-item error">初始化appKey失败: ${o.message}</span><br/>`}await te(),Vt([F,B],async()=>{await V()},{immediate:!1}),await I();const n=Ve.onVersionChange(()=>{const o=H();ee.splice(0,ee.length,...o),Ve.isSeniorEdition()&&B.value==="junior"?B.value="senior":!Ve.isSeniorEdition()&&B.value==="senior"&&(B.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Ve.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',A(),await Qe(),$e(),mt(),ot(),window.addEventListener("beforeunload",nt),()=>{E&&clearTimeout(E),n&&n()}}),Vt(t,n=>{E&&clearTimeout(E),E=setTimeout(()=>{we(n)},10)},{immediate:!1})},mt=()=>{Z.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==F.value&&(F.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${F.value}</span><br/>`),n.data.stage!==B.value&&(B.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${B.value}</span><br/>`))})},lt=pe(!1),ct=()=>{try{const n=N(),o=n.ActiveWindow.Selection;if(!o||!n||!n.ContentControls)return null;const i=o.Range,f=[];for(let l=1;l<=n.ContentControls.Count;l++)try{const v=n.ContentControls.Item(l);if(!v)continue;const c=(v.Title||"").trim(),D=v.Range;if(i.Start<D.End&&i.End>D.Start){let $=null,P=!1,U=!1;if(!c)U=!0,$=`empty_${v.ID||Date.now()}`;else if(c.startsWith("任务_")||c.startsWith("任务增强_")||c.startsWith("校对_")||c.startsWith("已完成_")||c.startsWith("已完成增强_")||c.startsWith("已完成校对_")||c.startsWith("异常_")||c.startsWith("异常增强_")||c.startsWith("异常校对_")||c.startsWith("已停止_")||c.startsWith("已停止增强_")||c.startsWith("已停止校对_"))c.startsWith("任务增强_")?($=c.substring(5),P=!0):c.startsWith("任务_")||c.startsWith("校对_")?$=c.substring(3):c.startsWith("已完成增强_")?($=c.substring(6),P=!0):c.startsWith("已完成校对_")?$=c.substring(6):c.startsWith("已完成_")?$=c.substring(4):c.startsWith("异常增强_")?($=c.substring(5),P=!0):c.startsWith("异常校对_")?$=c.substring(5):c.startsWith("异常_")?$=c.substring(3):c.startsWith("已停止增强_")?($=c.substring(6),P=!0):c.startsWith("已停止校对_")?$=c.substring(6):c.startsWith("已停止_")&&($=c.substring(4));else continue;if($){let W;i.Start>=D.Start&&i.End<=D.End?W="completely_within":i.Start<=D.Start&&i.End>=D.End?W="completely_contains":W="partial_overlap",f.push({taskId:$,control:v,task:U?null:a[$]||null,isEnhanced:P,isEmptyTitle:U,overlapType:W,controlRange:{start:D.Start,end:D.End},selectionRange:{start:i.Start,end:i.End}})}}}catch{continue}return f.length===0?null:{primary:f[0],all:f}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},pt=()=>{lt.value=!0},x=()=>{lt.value=!1},j=async(n,o=!1)=>{pt();try{await ge(n,o)}finally{x()}},b=n=>new Promise((o,i)=>{M.show=!0,M.message=n,M.resolveCallback=o,M.rejectCallback=i}),p=n=>{M.show=!1,n&&M.resolveCallback?M.resolveCallback(!0):M.resolveCallback&&M.resolveCallback(!1),M.resolveCallback=null,M.rejectCallback=null},I=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await Z.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(F.value=n.data.subject),n.data.stage&&(B.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${F.value})和年级(${B.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},V=async()=>{try{if(F.value||B.value){t.value+=`<span class="log-item info">正在保存学科(${F.value})和年级(${B.value})设置到服务器...</span><br/>`;const n=await Z.setSubjectAndStage(F.value,B.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}},ae=async()=>{try{R.value=2,z.value=R.value===2,z.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${R.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${R.value}）</span><br/>`}catch(n){console.error("检查企业ID失败:",n),t.value+=`<span class="log-item error">检查企业ID失败: ${n.message}</span><br/>`,z.value=!1}},re=(n,o)=>{try{const i=N();if(!i||!n)return!1;const f=i.Comments.Add(n,o);return!0}catch(i){return t.value+=`<span class="log-item error">为范围添加批注失败: ${i.message}</span><br/>`,!1}},ve=async(n,o)=>{try{if(!n||!Array.isArray(n))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const i=N();let f=null;if(i&&i.ContentControls)for(let D=1;D<=i.ContentControls.Count;D++)try{const $=i.ContentControls.Item(D);if($&&$.Title&&$.Title===`校对_${o}`){f=i.Range($.Range.Start,$.Range.End),t.value+='<span class="log-item info">已保存校对控件范围，准备删除控件</span><br/>',$.LockContents=!1,$.Delete(!1),t.value+='<span class="log-item success">已删除校对控件，保留内容用于添加批注</span><br/>';break}}catch{continue}if(!f){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const D=a[o];if(D&&D.selectedText)try{const $=i.Range().Find;if($.ClearFormatting(),$.Text=D.selectedText.substring(0,Math.min(D.selectedText.length,100)),$.Forward=!0,$.Wrap=1,$.Execute()){const P=$.Parent;if(D.selectedText.length>100){const U=P.Start+D.selectedText.length;f=i.Range(P.Start,Math.min(U,i.Range().End))}else f=P;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch($){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${$.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let l=0,v=0,c=[];for(const D of n){if(!D.mode1||!Array.isArray(D.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const $ of D.mode1){if(!$.error_info||!Array.isArray($.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const P=$.quest_html||"",U=$.quest_type||"";t.value+=`<span class="log-item info">处理${U}题目，发现${$.error_info.length}个错误信息</span><br/>`;for(const W of $.error_info)try{let Q="";W.error_info&&(Q+=`【错误类型】${W.error_info}`),W.fix_info&&(Q+=`\r
【修正建议】${W.fix_info}`),W.keywords&&W.keywords.trim()?ne(Q,W.keywords.trim(),-1,f)?(l++,t.value+=`<span class="log-item success">已为关键词"${W.keywords.trim()}"添加批注</span><br/>`):(c.push({comment:Q,keyword:W.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${W.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`):c.push({comment:Q,keyword:null})}catch(Q){v++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${Q.message}</span><br/>`}}}if(c.length>0&&f){t.value+=`<span class="log-item info">为整个控件范围添加${c.length}个批注</span><br/>`;for(const D of c)try{let $=D.comment;re(f,$)?(l++,t.value+=`<span class="log-item success">已为整个范围添加批注${D.keyword?`（关键词：${D.keyword}）`:""}</span><br/>`):v++}catch($){v++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${$.message}</span><br/>`}}if(f&&l>0)try{t.value+='<span class="log-item info">重新为校对完成的内容创建控件</span><br/>';const D=window.wps;console.log(f);const $=f.Paragraphs,P=$.Item(1),U=$.Item($.Count);console.log(P),console.log(U);const W=P.Range.Start,Q=U.Range.End,oe=f.Start,ce=f.End,Ie=i.Range(Math.min(W,oe),Math.max(Q,ce));let xe=i.ContentControls.Add(D.Enum.wdContentControlRichText,Ie);xe||(console.log("创建校对完成控件失败，尝试备用方案"),xe=i.ContentControls.Add(D.Enum.wdContentControlRichText),xe?(Ie.Cut(),xe.Range.Paste(),t.value+='<span class="log-item info">使用备用方案创建校对控件并转移内容</span><br/>'):t.value+='<span class="log-item error">重新创建校对控件失败</span><br/>'),xe?(xe.Title=`已完成校对_${o}`,xe.LockContents=!1,t.value+=`<span class="log-item success">已重新创建校对控件，标题：已完成校对_${o.substring(0,8)}</span><br/>`):t.value+='<span class="log-item warning">重新创建校对控件失败</span><br/>'}catch(D){t.value+=`<span class="log-item error">重新创建控件失败: ${D.message}</span><br/>`}return l>0?(t.value+=`<span class="log-item success">校对任务${o.substring(0,8)}处理完成：成功添加${l}个批注</span><br/>`,v>0&&(t.value+=`<span class="log-item warning">校对任务${o.substring(0,8)}：${v}个批注添加失败</span><br/>`),!0):(t.value+=`<span class="log-item error">校对任务${o.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(i){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${i.message}</span><br/>`,!1}};return{docName:e,selected:s,logger:t,map:a,watchedDir:y,subject:F,stage:B,subjectOptions:X,stageOptions:ee,fetchWatchedDir:te,clearLog:d,getCurrentDocument:N,checkDocumentFormat:O,getTaskStatusClass:ie,getTaskStatusText:u,getElapsedTime:w,terminateTask:k,stopTaskWithoutRemovingControl:m,run1:Me,run2:tt,runCheck:Oe,getHeaderStatusClass:Be,getRunningTasksCount:qe,getCompletedTasksCount:st,getErrorTasksCount:Je,setupLifecycle:Et,navigateToTaskControl:et,forceCleanAllTasks:it,ws:je,wsMessages:be,initWebSocket:$e,handleWatcherEvent:Pe,urlMonitorTasks:G,monitorUrlForTask:ye,stopUrlMonitoring:Se,getUrlMonitorStatus:le,forceUrlCheck:Ge,cleanupUrlMonitoringTasks:nt,tryRemoveTaskPlaceholder:ge,isLoading:lt,isSelectionInTaskControl:ct,tryRemoveTaskPlaceholderWithLoading:j,showConfirm:b,handleConfirm:p,confirmDialog:M,loadSubjectAndStage:I,saveSubjectAndStage:V,enterpriseId:R,isCheckingVisible:z,checkEnterpriseAndSetCheckingVisibility:ae,processCheckingJson:ve}}const yn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Ve.getVersionConfig(),selectedEdition:Ve.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),r=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${r}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Z.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Z[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Z.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Z.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Z.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Z.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Z.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Z.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Z.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Ve.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await nn()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Z.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Z.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Z.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Z.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Z.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Z.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Z.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Ve.onVersionChange(e=>{this.versionConfig=Ve.getVersionConfig(),this.selectedEdition=Ve.getEdition()}),await Z.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},xn={class:"file-watcher"},Tn={class:"settings-modal"},Cn={class:"modal-content"},kn={class:"modal-header"},$n={class:"version-tag"},Sn={key:0,class:"user-info"},En={class:"header-actions"},_n={class:"modal-body"},An={class:"status-section"},Mn={class:"status-item"},Dn={key:0,class:"status-item"},Pn={class:"directory-section"},On={class:"directory-form"},In={class:"radio-group"},Rn={class:"radio-item"},Un={class:"radio-item"},Ln={class:"radio-item"},Fn={key:0,class:"radio-item"},jn={key:1,class:"directory-section"},Wn={class:"directory-form"},Bn={class:"form-group"},Nn=["placeholder"],Vn=["disabled"],Hn={key:2,class:"directory-section"},zn={class:"directory-form"},qn={class:"form-group"},Jn=["placeholder"],Yn=["disabled"],Kn={key:3,class:"directory-section"},Xn={class:"directory-form"},Gn={class:"form-group"},Zn=["placeholder"],Qn=["disabled"],ea={key:4,class:"events-section"},ta={class:"events-list"},sa={class:"event-time"},na={class:"event-message"},aa={key:1,class:"modal-footer"};function ra(e,s,t,a,r,g){var T,E,y,C,M,F,B,R,z,X,H,ee,te,q,d,N,O,ne,ie,u,w;return Y(),K("div",xn,[h("div",Tn,[h("div",Cn,[h("div",kn,[h("h3",null,[an(de(r.versionConfig.shortName)+"设置 ",1),h("span",$n,de(r.versionConfig.appVersion),1),g.userInfo?(Y(),K("span",Sn,"欢迎您，"+de(g.userInfo.nickname),1)):se("",!0)]),h("div",En,[h("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>g.handleLogout&&g.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[h("span",{class:"icon-logout"},null,-1)])),h("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),h("div",_n,[h("div",An,[h("div",Mn,[s[22]||(s[22]=h("span",{class:"label"},"状态：",-1)),h("span",{class:Re(["status-badge",r.status.status])},de(r.status.status==="running"?"运行中":"已停止"),3)]),((y=(E=(T=g.userInfo)==null?void 0:T.orgs)==null?void 0:E[0])==null?void 0:y.orgId)===2?(Y(),K("div",Dn,[s[23]||(s[23]=h("span",{class:"label"},"本次上传：",-1)),h("span",null,de(r.status.processedFiles||0)+" 个文件",1)])):se("",!0),h("div",Pn,[s[28]||(s[28]=h("h4",null,"保存方式设置",-1)),h("div",On,[h("div",In,[h("label",Rn,[Ne(h("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>r.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[jt,r.currentSaveMethod]]),s[24]||(s[24]=h("span",{class:"radio-label"},"方式一",-1))]),h("label",Un,[Ne(h("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>r.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[jt,r.currentSaveMethod]]),s[25]||(s[25]=h("span",{class:"radio-label"},"方式二 (默认)",-1))]),h("label",Ln,[Ne(h("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>r.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[jt,r.currentSaveMethod]]),s[26]||(s[26]=h("span",{class:"radio-label"},"方式三",-1))]),((F=(M=(C=g.userInfo)==null?void 0:C.orgs)==null?void 0:M[0])==null?void 0:F.orgId)===2?(Y(),K("label",Fn,[Ne(h("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>r.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[jt,r.currentSaveMethod]]),s[27]||(s[27]=h("span",{class:"radio-label"},"方式四",-1))])):se("",!0)]),r.saveMethodUpdateMessage?(Y(),K("div",{key:0,class:Re(["update-message",r.saveMethodUpdateSuccess?"success":"error"])},de(r.saveMethodUpdateMessage),3)):se("",!0)])])]),se("",!0),((z=(R=(B=g.userInfo)==null?void 0:B.orgs)==null?void 0:R[0])==null?void 0:z.orgId)===2?(Y(),K("div",jn,[s[32]||(s[32]=h("h4",null,"上传目录设置",-1)),h("div",Wn,[h("div",Bn,[s[31]||(s[31]=h("span",{class:"label"},"路径：",-1)),Ne(h("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>r.newWatchDir=m),placeholder:r.status.watchDir||"C:\\Temp"},null,8,Nn),[[Yt,r.newWatchDir]]),h("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>g.updateWatchDir&&g.updateWatchDir(...m)),disabled:r.isUpdating||!r.newWatchDir},de(r.isUpdating?"更新中...":"更新目录"),9,Vn)]),r.updateMessage?(Y(),K("div",{key:0,class:Re(["update-message",r.updateSuccess?"success":"error"])},de(r.updateMessage),3)):se("",!0)])])):se("",!0),((ee=(H=(X=g.userInfo)==null?void 0:X.orgs)==null?void 0:H[0])==null?void 0:ee.orgId)===2?(Y(),K("div",Hn,[s[34]||(s[34]=h("h4",null,"下载目录设置",-1)),h("div",zn,[h("div",qn,[s[33]||(s[33]=h("span",{class:"label"},"路径：",-1)),Ne(h("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>r.newDownloadPath=m),placeholder:r.downloadPath||"C:\\Temp\\Downloads"},null,8,Jn),[[Yt,r.newDownloadPath]]),h("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>g.updateDownloadPath&&g.updateDownloadPath(...m)),disabled:r.isUpdatingDownloadPath||!r.newDownloadPath},de(r.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Yn)]),r.downloadPathUpdateMessage?(Y(),K("div",{key:0,class:Re(["update-message",r.downloadPathUpdateSuccess?"success":"error"])},de(r.downloadPathUpdateMessage),3)):se("",!0)])])):se("",!0),((d=(q=(te=g.userInfo)==null?void 0:te.orgs)==null?void 0:q[0])==null?void 0:d.orgId)===2?(Y(),K("div",Kn,[s[36]||(s[36]=h("h4",null,"配置目录设置",-1)),h("div",Xn,[h("div",Gn,[s[35]||(s[35]=h("span",{class:"label"},"路径：",-1)),Ne(h("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>r.newAddonConfigPath=m),placeholder:r.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,Zn),[[Yt,r.newAddonConfigPath]]),h("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...m)),disabled:r.isUpdatingAddonConfigPath||!r.newAddonConfigPath},de(r.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,Qn)]),r.addonConfigPathUpdateMessage?(Y(),K("div",{key:0,class:Re(["update-message",r.addonConfigPathUpdateSuccess?"success":"error"])},de(r.addonConfigPathUpdateMessage),3)):se("",!0)])])):se("",!0),((ne=(O=(N=g.userInfo)==null?void 0:N.orgs)==null?void 0:O[0])==null?void 0:ne.orgId)===2?(Y(),K("div",ea,[s[37]||(s[37]=h("h4",null,"最近事件",-1)),h("div",ta,[(Y(!0),K(At,null,Mt(r.recentEvents,(m,k)=>(Y(),K("div",{key:k,class:"event-item"},[h("span",sa,de(g.formatTime(m.timestamp)),1),h("span",{class:Re(["event-type",m.eventType])},de(g.getEventTypeText(m.eventType)),3),h("span",na,de(g.getEventMessage(m)),1)]))),128))])])):se("",!0)]),se("",!0),((w=(u=(ie=g.userInfo)==null?void 0:ie.orgs)==null?void 0:u[0])==null?void 0:w.orgId)===2?(Y(),K("div",aa,[h("button",{class:Re(["control-btn",r.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>g.controlService&&g.controlService(...m))},de(r.status.status==="running"?"停止服务":"启动服务"),3)])):se("",!0)])])])}const oa=Rs(yn,[["render",ra],["__scopeId","data-v-48242008"]]);var Ee="top",Le="bottom",Fe="right",_e="left",as="auto",Lt=[Ee,Le,Fe,_e],Ct="start",Rt="end",ia="clippingParents",Ls="viewport",_t="popper",la="reference",gs=Lt.reduce(function(e,s){return e.concat([s+"-"+Ct,s+"-"+Rt])},[]),Fs=[].concat(Lt,[as]).reduce(function(e,s){return e.concat([s,s+"-"+Ct,s+"-"+Rt])},[]),ca="beforeRead",ua="read",pa="afterRead",da="beforeMain",fa="main",ha="afterMain",va="beforeWrite",ga="write",ma="afterWrite",ba=[ca,ua,pa,da,fa,ha,va,ga,ma];function Xe(e){return e?(e.nodeName||"").toLowerCase():null}function De(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function gt(e){var s=De(e).Element;return e instanceof s||e instanceof Element}function Ue(e){var s=De(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function rs(e){if(typeof ShadowRoot>"u")return!1;var s=De(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function wa(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},r=s.attributes[t]||{},g=s.elements[t];!Ue(g)||!Xe(g)||(Object.assign(g.style,a),Object.keys(r).forEach(function(T){var E=r[T];E===!1?g.removeAttribute(T):g.setAttribute(T,E===!0?"":E)}))})}function ya(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var r=s.elements[a],g=s.attributes[a]||{},T=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),E=T.reduce(function(y,C){return y[C]="",y},{});!Ue(r)||!Xe(r)||(Object.assign(r.style,E),Object.keys(g).forEach(function(y){r.removeAttribute(y)}))})}}const js={name:"applyStyles",enabled:!0,phase:"write",fn:wa,effect:ya,requires:["computeStyles"]};function Ke(e){return e.split("-")[0]}var ht=Math.max,Ht=Math.min,kt=Math.round;function es(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Ws(){return!/^((?!chrome|android).)*safari/i.test(es())}function $t(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),r=1,g=1;s&&Ue(e)&&(r=e.offsetWidth>0&&kt(a.width)/e.offsetWidth||1,g=e.offsetHeight>0&&kt(a.height)/e.offsetHeight||1);var T=gt(e)?De(e):window,E=T.visualViewport,y=!Ws()&&t,C=(a.left+(y&&E?E.offsetLeft:0))/r,M=(a.top+(y&&E?E.offsetTop:0))/g,F=a.width/r,B=a.height/g;return{width:F,height:B,top:M,right:C+F,bottom:M+B,left:C,x:C,y:M}}function os(e){var s=$t(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function Bs(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&rs(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function rt(e){return De(e).getComputedStyle(e)}function xa(e){return["table","td","th"].indexOf(Xe(e))>=0}function ut(e){return((gt(e)?e.ownerDocument:e.document)||window.document).documentElement}function qt(e){return Xe(e)==="html"?e:e.assignedSlot||e.parentNode||(rs(e)?e.host:null)||ut(e)}function ms(e){return!Ue(e)||rt(e).position==="fixed"?null:e.offsetParent}function Ta(e){var s=/firefox/i.test(es()),t=/Trident/i.test(es());if(t&&Ue(e)){var a=rt(e);if(a.position==="fixed")return null}var r=qt(e);for(rs(r)&&(r=r.host);Ue(r)&&["html","body"].indexOf(Xe(r))<0;){var g=rt(r);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return r;r=r.parentNode}return null}function Ft(e){for(var s=De(e),t=ms(e);t&&xa(t)&&rt(t).position==="static";)t=ms(t);return t&&(Xe(t)==="html"||Xe(t)==="body"&&rt(t).position==="static")?s:t||Ta(e)||s}function is(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Pt(e,s,t){return ht(e,Ht(s,t))}function Ca(e,s,t){var a=Pt(e,s,t);return a>t?t:a}function Ns(){return{top:0,right:0,bottom:0,left:0}}function Vs(e){return Object.assign({},Ns(),e)}function Hs(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var ka=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Vs(typeof s!="number"?s:Hs(s,Lt))};function $a(e){var s,t=e.state,a=e.name,r=e.options,g=t.elements.arrow,T=t.modifiersData.popperOffsets,E=Ke(t.placement),y=is(E),C=[_e,Fe].indexOf(E)>=0,M=C?"height":"width";if(!(!g||!T)){var F=ka(r.padding,t),B=os(g),R=y==="y"?Ee:_e,z=y==="y"?Le:Fe,X=t.rects.reference[M]+t.rects.reference[y]-T[y]-t.rects.popper[M],H=T[y]-t.rects.reference[y],ee=Ft(g),te=ee?y==="y"?ee.clientHeight||0:ee.clientWidth||0:0,q=X/2-H/2,d=F[R],N=te-B[M]-F[z],O=te/2-B[M]/2+q,ne=Pt(d,O,N),ie=y;t.modifiersData[a]=(s={},s[ie]=ne,s.centerOffset=ne-O,s)}}function Sa(e){var s=e.state,t=e.options,a=t.element,r=a===void 0?"[data-popper-arrow]":a;r!=null&&(typeof r=="string"&&(r=s.elements.popper.querySelector(r),!r)||Bs(s.elements.popper,r)&&(s.elements.arrow=r))}const Ea={name:"arrow",enabled:!0,phase:"main",fn:$a,effect:Sa,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function St(e){return e.split("-")[1]}var _a={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Aa(e,s){var t=e.x,a=e.y,r=s.devicePixelRatio||1;return{x:kt(t*r)/r||0,y:kt(a*r)/r||0}}function bs(e){var s,t=e.popper,a=e.popperRect,r=e.placement,g=e.variation,T=e.offsets,E=e.position,y=e.gpuAcceleration,C=e.adaptive,M=e.roundOffsets,F=e.isFixed,B=T.x,R=B===void 0?0:B,z=T.y,X=z===void 0?0:z,H=typeof M=="function"?M({x:R,y:X}):{x:R,y:X};R=H.x,X=H.y;var ee=T.hasOwnProperty("x"),te=T.hasOwnProperty("y"),q=_e,d=Ee,N=window;if(C){var O=Ft(t),ne="clientHeight",ie="clientWidth";if(O===De(t)&&(O=ut(t),rt(O).position!=="static"&&E==="absolute"&&(ne="scrollHeight",ie="scrollWidth")),O=O,r===Ee||(r===_e||r===Fe)&&g===Rt){d=Le;var u=F&&O===N&&N.visualViewport?N.visualViewport.height:O[ne];X-=u-a.height,X*=y?1:-1}if(r===_e||(r===Ee||r===Le)&&g===Rt){q=Fe;var w=F&&O===N&&N.visualViewport?N.visualViewport.width:O[ie];R-=w-a.width,R*=y?1:-1}}var m=Object.assign({position:E},C&&_a),k=M===!0?Aa({x:R,y:X},De(t)):{x:R,y:X};if(R=k.x,X=k.y,y){var S;return Object.assign({},m,(S={},S[d]=te?"0":"",S[q]=ee?"0":"",S.transform=(N.devicePixelRatio||1)<=1?"translate("+R+"px, "+X+"px)":"translate3d("+R+"px, "+X+"px, 0)",S))}return Object.assign({},m,(s={},s[d]=te?X+"px":"",s[q]=ee?R+"px":"",s.transform="",s))}function Ma(e){var s=e.state,t=e.options,a=t.gpuAcceleration,r=a===void 0?!0:a,g=t.adaptive,T=g===void 0?!0:g,E=t.roundOffsets,y=E===void 0?!0:E,C={placement:Ke(s.placement),variation:St(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:r,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,bs(Object.assign({},C,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:T,roundOffsets:y})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,bs(Object.assign({},C,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:y})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Da={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ma,data:{}};var Wt={passive:!0};function Pa(e){var s=e.state,t=e.instance,a=e.options,r=a.scroll,g=r===void 0?!0:r,T=a.resize,E=T===void 0?!0:T,y=De(s.elements.popper),C=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&C.forEach(function(M){M.addEventListener("scroll",t.update,Wt)}),E&&y.addEventListener("resize",t.update,Wt),function(){g&&C.forEach(function(M){M.removeEventListener("scroll",t.update,Wt)}),E&&y.removeEventListener("resize",t.update,Wt)}}const Oa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Pa,data:{}};var Ia={left:"right",right:"left",bottom:"top",top:"bottom"};function Nt(e){return e.replace(/left|right|bottom|top/g,function(s){return Ia[s]})}var Ra={start:"end",end:"start"};function ws(e){return e.replace(/start|end/g,function(s){return Ra[s]})}function ls(e){var s=De(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function cs(e){return $t(ut(e)).left+ls(e).scrollLeft}function Ua(e,s){var t=De(e),a=ut(e),r=t.visualViewport,g=a.clientWidth,T=a.clientHeight,E=0,y=0;if(r){g=r.width,T=r.height;var C=Ws();(C||!C&&s==="fixed")&&(E=r.offsetLeft,y=r.offsetTop)}return{width:g,height:T,x:E+cs(e),y}}function La(e){var s,t=ut(e),a=ls(e),r=(s=e.ownerDocument)==null?void 0:s.body,g=ht(t.scrollWidth,t.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),T=ht(t.scrollHeight,t.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),E=-a.scrollLeft+cs(e),y=-a.scrollTop;return rt(r||t).direction==="rtl"&&(E+=ht(t.clientWidth,r?r.clientWidth:0)-g),{width:g,height:T,x:E,y}}function us(e){var s=rt(e),t=s.overflow,a=s.overflowX,r=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+r+a)}function zs(e){return["html","body","#document"].indexOf(Xe(e))>=0?e.ownerDocument.body:Ue(e)&&us(e)?e:zs(qt(e))}function Ot(e,s){var t;s===void 0&&(s=[]);var a=zs(e),r=a===((t=e.ownerDocument)==null?void 0:t.body),g=De(a),T=r?[g].concat(g.visualViewport||[],us(a)?a:[]):a,E=s.concat(T);return r?E:E.concat(Ot(qt(T)))}function ts(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Fa(e,s){var t=$t(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function ys(e,s,t){return s===Ls?ts(Ua(e,t)):gt(s)?Fa(s,t):ts(La(ut(e)))}function ja(e){var s=Ot(qt(e)),t=["absolute","fixed"].indexOf(rt(e).position)>=0,a=t&&Ue(e)?Ft(e):e;return gt(a)?s.filter(function(r){return gt(r)&&Bs(r,a)&&Xe(r)!=="body"}):[]}function Wa(e,s,t,a){var r=s==="clippingParents"?ja(e):[].concat(s),g=[].concat(r,[t]),T=g[0],E=g.reduce(function(y,C){var M=ys(e,C,a);return y.top=ht(M.top,y.top),y.right=Ht(M.right,y.right),y.bottom=Ht(M.bottom,y.bottom),y.left=ht(M.left,y.left),y},ys(e,T,a));return E.width=E.right-E.left,E.height=E.bottom-E.top,E.x=E.left,E.y=E.top,E}function qs(e){var s=e.reference,t=e.element,a=e.placement,r=a?Ke(a):null,g=a?St(a):null,T=s.x+s.width/2-t.width/2,E=s.y+s.height/2-t.height/2,y;switch(r){case Ee:y={x:T,y:s.y-t.height};break;case Le:y={x:T,y:s.y+s.height};break;case Fe:y={x:s.x+s.width,y:E};break;case _e:y={x:s.x-t.width,y:E};break;default:y={x:s.x,y:s.y}}var C=r?is(r):null;if(C!=null){var M=C==="y"?"height":"width";switch(g){case Ct:y[C]=y[C]-(s[M]/2-t[M]/2);break;case Rt:y[C]=y[C]+(s[M]/2-t[M]/2);break}}return y}function Ut(e,s){s===void 0&&(s={});var t=s,a=t.placement,r=a===void 0?e.placement:a,g=t.strategy,T=g===void 0?e.strategy:g,E=t.boundary,y=E===void 0?ia:E,C=t.rootBoundary,M=C===void 0?Ls:C,F=t.elementContext,B=F===void 0?_t:F,R=t.altBoundary,z=R===void 0?!1:R,X=t.padding,H=X===void 0?0:X,ee=Vs(typeof H!="number"?H:Hs(H,Lt)),te=B===_t?la:_t,q=e.rects.popper,d=e.elements[z?te:B],N=Wa(gt(d)?d:d.contextElement||ut(e.elements.popper),y,M,T),O=$t(e.elements.reference),ne=qs({reference:O,element:q,strategy:"absolute",placement:r}),ie=ts(Object.assign({},q,ne)),u=B===_t?ie:O,w={top:N.top-u.top+ee.top,bottom:u.bottom-N.bottom+ee.bottom,left:N.left-u.left+ee.left,right:u.right-N.right+ee.right},m=e.modifiersData.offset;if(B===_t&&m){var k=m[r];Object.keys(w).forEach(function(S){var A=[Fe,Le].indexOf(S)>=0?1:-1,L=[Ee,Le].indexOf(S)>=0?"y":"x";w[S]+=k[L]*A})}return w}function Ba(e,s){s===void 0&&(s={});var t=s,a=t.placement,r=t.boundary,g=t.rootBoundary,T=t.padding,E=t.flipVariations,y=t.allowedAutoPlacements,C=y===void 0?Fs:y,M=St(a),F=M?E?gs:gs.filter(function(z){return St(z)===M}):Lt,B=F.filter(function(z){return C.indexOf(z)>=0});B.length===0&&(B=F);var R=B.reduce(function(z,X){return z[X]=Ut(e,{placement:X,boundary:r,rootBoundary:g,padding:T})[Ke(X)],z},{});return Object.keys(R).sort(function(z,X){return R[z]-R[X]})}function Na(e){if(Ke(e)===as)return[];var s=Nt(e);return[ws(e),s,ws(s)]}function Va(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var r=t.mainAxis,g=r===void 0?!0:r,T=t.altAxis,E=T===void 0?!0:T,y=t.fallbackPlacements,C=t.padding,M=t.boundary,F=t.rootBoundary,B=t.altBoundary,R=t.flipVariations,z=R===void 0?!0:R,X=t.allowedAutoPlacements,H=s.options.placement,ee=Ke(H),te=ee===H,q=y||(te||!z?[Nt(H)]:Na(H)),d=[H].concat(q).reduce(function(Ae,we){return Ae.concat(Ke(we)===as?Ba(s,{placement:we,boundary:M,rootBoundary:F,padding:C,flipVariations:z,allowedAutoPlacements:X}):we)},[]),N=s.rects.reference,O=s.rects.popper,ne=new Map,ie=!0,u=d[0],w=0;w<d.length;w++){var m=d[w],k=Ke(m),S=St(m)===Ct,A=[Ee,Le].indexOf(k)>=0,L=A?"width":"height",_=Ut(s,{placement:m,boundary:M,rootBoundary:F,altBoundary:B,padding:C}),J=A?S?Fe:_e:S?Le:Ee;N[L]>O[L]&&(J=Nt(J));var fe=Nt(J),me=[];if(g&&me.push(_[k]<=0),E&&me.push(_[J]<=0,_[fe]<=0),me.every(function(Ae){return Ae})){u=m,ie=!1;break}ne.set(m,me)}if(ie)for(var Ce=z?3:1,je=function(we){var $e=d.find(function(G){var ye=ne.get(G);if(ye)return ye.slice(0,we).every(function(Se){return Se})});if($e)return u=$e,"break"},be=Ce;be>0;be--){var ke=je(be);if(ke==="break")break}s.placement!==u&&(s.modifiersData[a]._skip=!0,s.placement=u,s.reset=!0)}}const Ha={name:"flip",enabled:!0,phase:"main",fn:Va,requiresIfExists:["offset"],data:{_skip:!1}};function xs(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function Ts(e){return[Ee,Fe,Le,_e].some(function(s){return e[s]>=0})}function za(e){var s=e.state,t=e.name,a=s.rects.reference,r=s.rects.popper,g=s.modifiersData.preventOverflow,T=Ut(s,{elementContext:"reference"}),E=Ut(s,{altBoundary:!0}),y=xs(T,a),C=xs(E,r,g),M=Ts(y),F=Ts(C);s.modifiersData[t]={referenceClippingOffsets:y,popperEscapeOffsets:C,isReferenceHidden:M,hasPopperEscaped:F},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":M,"data-popper-escaped":F})}const qa={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:za};function Ja(e,s,t){var a=Ke(e),r=[_e,Ee].indexOf(a)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,T=g[0],E=g[1];return T=T||0,E=(E||0)*r,[_e,Fe].indexOf(a)>=0?{x:E,y:T}:{x:T,y:E}}function Ya(e){var s=e.state,t=e.options,a=e.name,r=t.offset,g=r===void 0?[0,0]:r,T=Fs.reduce(function(M,F){return M[F]=Ja(F,s.rects,g),M},{}),E=T[s.placement],y=E.x,C=E.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=y,s.modifiersData.popperOffsets.y+=C),s.modifiersData[a]=T}const Ka={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Ya};function Xa(e){var s=e.state,t=e.name;s.modifiersData[t]=qs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const Ga={name:"popperOffsets",enabled:!0,phase:"read",fn:Xa,data:{}};function Za(e){return e==="x"?"y":"x"}function Qa(e){var s=e.state,t=e.options,a=e.name,r=t.mainAxis,g=r===void 0?!0:r,T=t.altAxis,E=T===void 0?!1:T,y=t.boundary,C=t.rootBoundary,M=t.altBoundary,F=t.padding,B=t.tether,R=B===void 0?!0:B,z=t.tetherOffset,X=z===void 0?0:z,H=Ut(s,{boundary:y,rootBoundary:C,padding:F,altBoundary:M}),ee=Ke(s.placement),te=St(s.placement),q=!te,d=is(ee),N=Za(d),O=s.modifiersData.popperOffsets,ne=s.rects.reference,ie=s.rects.popper,u=typeof X=="function"?X(Object.assign({},s.rects,{placement:s.placement})):X,w=typeof u=="number"?{mainAxis:u,altAxis:u}:Object.assign({mainAxis:0,altAxis:0},u),m=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,k={x:0,y:0};if(O){if(g){var S,A=d==="y"?Ee:_e,L=d==="y"?Le:Fe,_=d==="y"?"height":"width",J=O[d],fe=J+H[A],me=J-H[L],Ce=R?-ie[_]/2:0,je=te===Ct?ne[_]:ie[_],be=te===Ct?-ie[_]:-ne[_],ke=s.elements.arrow,Ae=R&&ke?os(ke):{width:0,height:0},we=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:Ns(),$e=we[A],G=we[L],ye=Pt(0,ne[_],Ae[_]),Se=q?ne[_]/2-Ce-ye-$e-w.mainAxis:je-ye-$e-w.mainAxis,le=q?-ne[_]/2+Ce+ye+G+w.mainAxis:be+ye+G+w.mainAxis,Ge=s.elements.arrow&&Ft(s.elements.arrow),Pe=Ge?d==="y"?Ge.clientTop||0:Ge.clientLeft||0:0,Ze=(S=m==null?void 0:m[d])!=null?S:0,ze=J+Se-Ze-Pe,Qe=J+le-Ze,ge=Pt(R?Ht(fe,ze):fe,J,R?ht(me,Qe):me);O[d]=ge,k[d]=ge-J}if(E){var et,We=d==="x"?Ee:_e,ot=d==="x"?Le:Fe,Me=O[N],Oe=N==="y"?"height":"width",tt=Me+H[We],Be=Me-H[ot],qe=[Ee,_e].indexOf(ee)!==-1,st=(et=m==null?void 0:m[N])!=null?et:0,Je=qe?tt:Me-ne[Oe]-ie[Oe]-st+w.altAxis,it=qe?Me+ne[Oe]+ie[Oe]-st-w.altAxis:Be,nt=R&&qe?Ca(Je,Me,it):Pt(R?Je:tt,Me,R?it:Be);O[N]=nt,k[N]=nt-Me}s.modifiersData[a]=k}}const er={name:"preventOverflow",enabled:!0,phase:"main",fn:Qa,requiresIfExists:["offset"]};function tr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function sr(e){return e===De(e)||!Ue(e)?ls(e):tr(e)}function nr(e){var s=e.getBoundingClientRect(),t=kt(s.width)/e.offsetWidth||1,a=kt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function ar(e,s,t){t===void 0&&(t=!1);var a=Ue(s),r=Ue(s)&&nr(s),g=ut(s),T=$t(e,r,t),E={scrollLeft:0,scrollTop:0},y={x:0,y:0};return(a||!a&&!t)&&((Xe(s)!=="body"||us(g))&&(E=sr(s)),Ue(s)?(y=$t(s,!0),y.x+=s.clientLeft,y.y+=s.clientTop):g&&(y.x=cs(g))),{x:T.left+E.scrollLeft-y.x,y:T.top+E.scrollTop-y.y,width:T.width,height:T.height}}function rr(e){var s=new Map,t=new Set,a=[];e.forEach(function(g){s.set(g.name,g)});function r(g){t.add(g.name);var T=[].concat(g.requires||[],g.requiresIfExists||[]);T.forEach(function(E){if(!t.has(E)){var y=s.get(E);y&&r(y)}}),a.push(g)}return e.forEach(function(g){t.has(g.name)||r(g)}),a}function or(e){var s=rr(e);return ba.reduce(function(t,a){return t.concat(s.filter(function(r){return r.phase===a}))},[])}function ir(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function lr(e){var s=e.reduce(function(t,a){var r=t[a.name];return t[a.name]=r?Object.assign({},r,a,{options:Object.assign({},r.options,a.options),data:Object.assign({},r.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var Cs={placement:"bottom",modifiers:[],strategy:"absolute"};function ks(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function cr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,r=s.defaultOptions,g=r===void 0?Cs:r;return function(E,y,C){C===void 0&&(C=g);var M={placement:"bottom",orderedModifiers:[],options:Object.assign({},Cs,g),modifiersData:{},elements:{reference:E,popper:y},attributes:{},styles:{}},F=[],B=!1,R={state:M,setOptions:function(ee){var te=typeof ee=="function"?ee(M.options):ee;X(),M.options=Object.assign({},g,M.options,te),M.scrollParents={reference:gt(E)?Ot(E):E.contextElement?Ot(E.contextElement):[],popper:Ot(y)};var q=or(lr([].concat(a,M.options.modifiers)));return M.orderedModifiers=q.filter(function(d){return d.enabled}),z(),R.update()},forceUpdate:function(){if(!B){var ee=M.elements,te=ee.reference,q=ee.popper;if(ks(te,q)){M.rects={reference:ar(te,Ft(q),M.options.strategy==="fixed"),popper:os(q)},M.reset=!1,M.placement=M.options.placement,M.orderedModifiers.forEach(function(w){return M.modifiersData[w.name]=Object.assign({},w.data)});for(var d=0;d<M.orderedModifiers.length;d++){if(M.reset===!0){M.reset=!1,d=-1;continue}var N=M.orderedModifiers[d],O=N.fn,ne=N.options,ie=ne===void 0?{}:ne,u=N.name;typeof O=="function"&&(M=O({state:M,options:ie,name:u,instance:R})||M)}}}},update:ir(function(){return new Promise(function(H){R.forceUpdate(),H(M)})}),destroy:function(){X(),B=!0}};if(!ks(E,y))return R;R.setOptions(C).then(function(H){!B&&C.onFirstUpdate&&C.onFirstUpdate(H)});function z(){M.orderedModifiers.forEach(function(H){var ee=H.name,te=H.options,q=te===void 0?{}:te,d=H.effect;if(typeof d=="function"){var N=d({state:M,name:ee,instance:R,options:q}),O=function(){};F.push(N||O)}})}function X(){F.forEach(function(H){return H()}),F=[]}return R}}var ur=[Oa,Ga,Da,js,Ka,Ha,er,Ea,qa],pr=cr({defaultModifiers:ur}),dr="tippy-box",Js="tippy-content",fr="tippy-backdrop",Ys="tippy-arrow",Ks="tippy-svg-arrow",ft={passive:!0,capture:!0},Xs=function(){return document.body};function Xt(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function ps(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function Gs(e,s){return typeof e=="function"?e.apply(void 0,s):e}function $s(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function hr(e){return e.split(/\s+/).filter(Boolean)}function Tt(e){return[].concat(e)}function Ss(e,s){e.indexOf(s)===-1&&e.push(s)}function vr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function gr(e){return e.split("-")[0]}function zt(e){return[].slice.call(e)}function Es(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function It(){return document.createElement("div")}function Jt(e){return["Element","Fragment"].some(function(s){return ps(e,s)})}function mr(e){return ps(e,"NodeList")}function br(e){return ps(e,"MouseEvent")}function wr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function yr(e){return Jt(e)?[e]:mr(e)?zt(e):Array.isArray(e)?e:zt(document.querySelectorAll(e))}function Gt(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function _s(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function xr(e){var s,t=Tt(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function Tr(e,s){var t=s.clientX,a=s.clientY;return e.every(function(r){var g=r.popperRect,T=r.popperState,E=r.props,y=E.interactiveBorder,C=gr(T.placement),M=T.modifiersData.offset;if(!M)return!0;var F=C==="bottom"?M.top.y:0,B=C==="top"?M.bottom.y:0,R=C==="right"?M.left.x:0,z=C==="left"?M.right.x:0,X=g.top-a+F>y,H=a-g.bottom-B>y,ee=g.left-t+R>y,te=t-g.right-z>y;return X||H||ee||te})}function Zt(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(r){e[a](r,t)})}function As(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var Ye={isTouch:!1},Ms=0;function Cr(){Ye.isTouch||(Ye.isTouch=!0,window.performance&&document.addEventListener("mousemove",Zs))}function Zs(){var e=performance.now();e-Ms<20&&(Ye.isTouch=!1,document.removeEventListener("mousemove",Zs)),Ms=e}function kr(){var e=document.activeElement;if(wr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function $r(){document.addEventListener("touchstart",Cr,ft),window.addEventListener("blur",kr)}var Sr=typeof window<"u"&&typeof document<"u",Er=Sr?!!window.msCrypto:!1,_r={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ar={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},He=Object.assign({appendTo:Xs,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},_r,Ar),Mr=Object.keys(He),Dr=function(s){var t=Object.keys(s);t.forEach(function(a){He[a]=s[a]})};function Qs(e){var s=e.plugins||[],t=s.reduce(function(a,r){var g=r.name,T=r.defaultValue;if(g){var E;a[g]=e[g]!==void 0?e[g]:(E=He[g])!=null?E:T}return a},{});return Object.assign({},e,t)}function Pr(e,s){var t=s?Object.keys(Qs(Object.assign({},He,{plugins:s}))):Mr,a=t.reduce(function(r,g){var T=(e.getAttribute("data-tippy-"+g)||"").trim();if(!T)return r;if(g==="content")r[g]=T;else try{r[g]=JSON.parse(T)}catch{r[g]=T}return r},{});return a}function Ds(e,s){var t=Object.assign({},s,{content:Gs(s.content,[e])},s.ignoreAttributes?{}:Pr(e,s.plugins));return t.aria=Object.assign({},He.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Or=function(){return"innerHTML"};function ss(e,s){e[Or()]=s}function Ps(e){var s=It();return e===!0?s.className=Ys:(s.className=Ks,Jt(e)?s.appendChild(e):ss(s,e)),s}function Os(e,s){Jt(s.content)?(ss(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?ss(e,s.content):e.textContent=s.content)}function ns(e){var s=e.firstElementChild,t=zt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(Js)}),arrow:t.find(function(a){return a.classList.contains(Ys)||a.classList.contains(Ks)}),backdrop:t.find(function(a){return a.classList.contains(fr)})}}function en(e){var s=It(),t=It();t.className=dr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=It();a.className=Js,a.setAttribute("data-state","hidden"),Os(a,e.props),s.appendChild(t),t.appendChild(a),r(e.props,e.props);function r(g,T){var E=ns(s),y=E.box,C=E.content,M=E.arrow;T.theme?y.setAttribute("data-theme",T.theme):y.removeAttribute("data-theme"),typeof T.animation=="string"?y.setAttribute("data-animation",T.animation):y.removeAttribute("data-animation"),T.inertia?y.setAttribute("data-inertia",""):y.removeAttribute("data-inertia"),y.style.maxWidth=typeof T.maxWidth=="number"?T.maxWidth+"px":T.maxWidth,T.role?y.setAttribute("role",T.role):y.removeAttribute("role"),(g.content!==T.content||g.allowHTML!==T.allowHTML)&&Os(C,e.props),T.arrow?M?g.arrow!==T.arrow&&(y.removeChild(M),y.appendChild(Ps(T.arrow))):y.appendChild(Ps(T.arrow)):M&&y.removeChild(M)}return{popper:s,onUpdate:r}}en.$$tippy=!0;var Ir=1,Bt=[],Qt=[];function Rr(e,s){var t=Ds(e,Object.assign({},He,Qs(Es(s)))),a,r,g,T=!1,E=!1,y=!1,C=!1,M,F,B,R=[],z=$s(ze,t.interactiveDebounce),X,H=Ir++,ee=null,te=vr(t.plugins),q={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},d={id:H,reference:e,popper:It(),popperInstance:ee,props:t,state:q,plugins:te,clearDelayTimeouts:Je,setProps:it,setContent:nt,show:Et,hide:mt,hideWithInteractivity:lt,enable:qe,disable:st,unmount:ct,destroy:pt};if(!t.render)return d;var N=t.render(d),O=N.popper,ne=N.onUpdate;O.setAttribute("data-tippy-root",""),O.id="tippy-"+d.id,d.popper=O,e._tippy=d,O._tippy=d;var ie=te.map(function(x){return x.fn(d)}),u=e.hasAttribute("aria-expanded");return Ge(),Ce(),J(),fe("onCreate",[d]),t.showOnCreate&&tt(),O.addEventListener("mouseenter",function(){d.props.interactive&&d.state.isVisible&&d.clearDelayTimeouts()}),O.addEventListener("mouseleave",function(){d.props.interactive&&d.props.trigger.indexOf("mouseenter")>=0&&A().addEventListener("mousemove",z)}),d;function w(){var x=d.props.touch;return Array.isArray(x)?x:[x,0]}function m(){return w()[0]==="hold"}function k(){var x;return!!((x=d.props.render)!=null&&x.$$tippy)}function S(){return X||e}function A(){var x=S().parentNode;return x?xr(x):document}function L(){return ns(O)}function _(x){return d.state.isMounted&&!d.state.isVisible||Ye.isTouch||M&&M.type==="focus"?0:Xt(d.props.delay,x?0:1,He.delay)}function J(x){x===void 0&&(x=!1),O.style.pointerEvents=d.props.interactive&&!x?"":"none",O.style.zIndex=""+d.props.zIndex}function fe(x,j,b){if(b===void 0&&(b=!0),ie.forEach(function(I){I[x]&&I[x].apply(I,j)}),b){var p;(p=d.props)[x].apply(p,j)}}function me(){var x=d.props.aria;if(x.content){var j="aria-"+x.content,b=O.id,p=Tt(d.props.triggerTarget||e);p.forEach(function(I){var V=I.getAttribute(j);if(d.state.isVisible)I.setAttribute(j,V?V+" "+b:b);else{var ae=V&&V.replace(b,"").trim();ae?I.setAttribute(j,ae):I.removeAttribute(j)}})}}function Ce(){if(!(u||!d.props.aria.expanded)){var x=Tt(d.props.triggerTarget||e);x.forEach(function(j){d.props.interactive?j.setAttribute("aria-expanded",d.state.isVisible&&j===S()?"true":"false"):j.removeAttribute("aria-expanded")})}}function je(){A().removeEventListener("mousemove",z),Bt=Bt.filter(function(x){return x!==z})}function be(x){if(!(Ye.isTouch&&(y||x.type==="mousedown"))){var j=x.composedPath&&x.composedPath()[0]||x.target;if(!(d.props.interactive&&As(O,j))){if(Tt(d.props.triggerTarget||e).some(function(b){return As(b,j)})){if(Ye.isTouch||d.state.isVisible&&d.props.trigger.indexOf("click")>=0)return}else fe("onClickOutside",[d,x]);d.props.hideOnClick===!0&&(d.clearDelayTimeouts(),d.hide(),E=!0,setTimeout(function(){E=!1}),d.state.isMounted||$e())}}}function ke(){y=!0}function Ae(){y=!1}function we(){var x=A();x.addEventListener("mousedown",be,!0),x.addEventListener("touchend",be,ft),x.addEventListener("touchstart",Ae,ft),x.addEventListener("touchmove",ke,ft)}function $e(){var x=A();x.removeEventListener("mousedown",be,!0),x.removeEventListener("touchend",be,ft),x.removeEventListener("touchstart",Ae,ft),x.removeEventListener("touchmove",ke,ft)}function G(x,j){Se(x,function(){!d.state.isVisible&&O.parentNode&&O.parentNode.contains(O)&&j()})}function ye(x,j){Se(x,j)}function Se(x,j){var b=L().box;function p(I){I.target===b&&(Zt(b,"remove",p),j())}if(x===0)return j();Zt(b,"remove",F),Zt(b,"add",p),F=p}function le(x,j,b){b===void 0&&(b=!1);var p=Tt(d.props.triggerTarget||e);p.forEach(function(I){I.addEventListener(x,j,b),R.push({node:I,eventType:x,handler:j,options:b})})}function Ge(){m()&&(le("touchstart",Ze,{passive:!0}),le("touchend",Qe,{passive:!0})),hr(d.props.trigger).forEach(function(x){if(x!=="manual")switch(le(x,Ze),x){case"mouseenter":le("mouseleave",Qe);break;case"focus":le(Er?"focusout":"blur",ge);break;case"focusin":le("focusout",ge);break}})}function Pe(){R.forEach(function(x){var j=x.node,b=x.eventType,p=x.handler,I=x.options;j.removeEventListener(b,p,I)}),R=[]}function Ze(x){var j,b=!1;if(!(!d.state.isEnabled||et(x)||E)){var p=((j=M)==null?void 0:j.type)==="focus";M=x,X=x.currentTarget,Ce(),!d.state.isVisible&&br(x)&&Bt.forEach(function(I){return I(x)}),x.type==="click"&&(d.props.trigger.indexOf("mouseenter")<0||T)&&d.props.hideOnClick!==!1&&d.state.isVisible?b=!0:tt(x),x.type==="click"&&(T=!b),b&&!p&&Be(x)}}function ze(x){var j=x.target,b=S().contains(j)||O.contains(j);if(!(x.type==="mousemove"&&b)){var p=Oe().concat(O).map(function(I){var V,ae=I._tippy,re=(V=ae.popperInstance)==null?void 0:V.state;return re?{popperRect:I.getBoundingClientRect(),popperState:re,props:t}:null}).filter(Boolean);Tr(p,x)&&(je(),Be(x))}}function Qe(x){var j=et(x)||d.props.trigger.indexOf("click")>=0&&T;if(!j){if(d.props.interactive){d.hideWithInteractivity(x);return}Be(x)}}function ge(x){d.props.trigger.indexOf("focusin")<0&&x.target!==S()||d.props.interactive&&x.relatedTarget&&O.contains(x.relatedTarget)||Be(x)}function et(x){return Ye.isTouch?m()!==x.type.indexOf("touch")>=0:!1}function We(){ot();var x=d.props,j=x.popperOptions,b=x.placement,p=x.offset,I=x.getReferenceClientRect,V=x.moveTransition,ae=k()?ns(O).arrow:null,re=I?{getBoundingClientRect:I,contextElement:I.contextElement||S()}:e,ve={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(i){var f=i.state;if(k()){var l=L(),v=l.box;["placement","reference-hidden","escaped"].forEach(function(c){c==="placement"?v.setAttribute("data-placement",f.placement):f.attributes.popper["data-popper-"+c]?v.setAttribute("data-"+c,""):v.removeAttribute("data-"+c)}),f.attributes.popper={}}}},n=[{name:"offset",options:{offset:p}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!V}},ve];k()&&ae&&n.push({name:"arrow",options:{element:ae,padding:3}}),n.push.apply(n,(j==null?void 0:j.modifiers)||[]),d.popperInstance=pr(re,O,Object.assign({},j,{placement:b,onFirstUpdate:B,modifiers:n}))}function ot(){d.popperInstance&&(d.popperInstance.destroy(),d.popperInstance=null)}function Me(){var x=d.props.appendTo,j,b=S();d.props.interactive&&x===Xs||x==="parent"?j=b.parentNode:j=Gs(x,[b]),j.contains(O)||j.appendChild(O),d.state.isMounted=!0,We()}function Oe(){return zt(O.querySelectorAll("[data-tippy-root]"))}function tt(x){d.clearDelayTimeouts(),x&&fe("onTrigger",[d,x]),we();var j=_(!0),b=w(),p=b[0],I=b[1];Ye.isTouch&&p==="hold"&&I&&(j=I),j?a=setTimeout(function(){d.show()},j):d.show()}function Be(x){if(d.clearDelayTimeouts(),fe("onUntrigger",[d,x]),!d.state.isVisible){$e();return}if(!(d.props.trigger.indexOf("mouseenter")>=0&&d.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(x.type)>=0&&T)){var j=_(!1);j?r=setTimeout(function(){d.state.isVisible&&d.hide()},j):g=requestAnimationFrame(function(){d.hide()})}}function qe(){d.state.isEnabled=!0}function st(){d.hide(),d.state.isEnabled=!1}function Je(){clearTimeout(a),clearTimeout(r),cancelAnimationFrame(g)}function it(x){if(!d.state.isDestroyed){fe("onBeforeUpdate",[d,x]),Pe();var j=d.props,b=Ds(e,Object.assign({},j,Es(x),{ignoreAttributes:!0}));d.props=b,Ge(),j.interactiveDebounce!==b.interactiveDebounce&&(je(),z=$s(ze,b.interactiveDebounce)),j.triggerTarget&&!b.triggerTarget?Tt(j.triggerTarget).forEach(function(p){p.removeAttribute("aria-expanded")}):b.triggerTarget&&e.removeAttribute("aria-expanded"),Ce(),J(),ne&&ne(j,b),d.popperInstance&&(We(),Oe().forEach(function(p){requestAnimationFrame(p._tippy.popperInstance.forceUpdate)})),fe("onAfterUpdate",[d,x])}}function nt(x){d.setProps({content:x})}function Et(){var x=d.state.isVisible,j=d.state.isDestroyed,b=!d.state.isEnabled,p=Ye.isTouch&&!d.props.touch,I=Xt(d.props.duration,0,He.duration);if(!(x||j||b||p)&&!S().hasAttribute("disabled")&&(fe("onShow",[d],!1),d.props.onShow(d)!==!1)){if(d.state.isVisible=!0,k()&&(O.style.visibility="visible"),J(),we(),d.state.isMounted||(O.style.transition="none"),k()){var V=L(),ae=V.box,re=V.content;Gt([ae,re],0)}B=function(){var n;if(!(!d.state.isVisible||C)){if(C=!0,O.offsetHeight,O.style.transition=d.props.moveTransition,k()&&d.props.animation){var o=L(),i=o.box,f=o.content;Gt([i,f],I),_s([i,f],"visible")}me(),Ce(),Ss(Qt,d),(n=d.popperInstance)==null||n.forceUpdate(),fe("onMount",[d]),d.props.animation&&k()&&ye(I,function(){d.state.isShown=!0,fe("onShown",[d])})}},Me()}}function mt(){var x=!d.state.isVisible,j=d.state.isDestroyed,b=!d.state.isEnabled,p=Xt(d.props.duration,1,He.duration);if(!(x||j||b)&&(fe("onHide",[d],!1),d.props.onHide(d)!==!1)){if(d.state.isVisible=!1,d.state.isShown=!1,C=!1,T=!1,k()&&(O.style.visibility="hidden"),je(),$e(),J(!0),k()){var I=L(),V=I.box,ae=I.content;d.props.animation&&(Gt([V,ae],p),_s([V,ae],"hidden"))}me(),Ce(),d.props.animation?k()&&G(p,d.unmount):d.unmount()}}function lt(x){A().addEventListener("mousemove",z),Ss(Bt,z),z(x)}function ct(){d.state.isVisible&&d.hide(),d.state.isMounted&&(ot(),Oe().forEach(function(x){x._tippy.unmount()}),O.parentNode&&O.parentNode.removeChild(O),Qt=Qt.filter(function(x){return x!==d}),d.state.isMounted=!1,fe("onHidden",[d]))}function pt(){d.state.isDestroyed||(d.clearDelayTimeouts(),d.unmount(),Pe(),delete e._tippy,d.state.isDestroyed=!0,fe("onDestroy",[d]))}}function vt(e,s){s===void 0&&(s={});var t=He.plugins.concat(s.plugins||[]);$r();var a=Object.assign({},s,{plugins:t}),r=yr(e),g=r.reduce(function(T,E){var y=E&&Rr(E,a);return y&&T.push(y),T},[]);return Jt(e)?g[0]:g}vt.defaultProps=He;vt.setDefaultProps=Dr;vt.currentInput=Ye;Object.assign({},js,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});vt.setDefaultProps({render:en});const Ur={class:"task-pane"},Lr={key:0,class:"loading-overlay"},Fr={key:1,class:"format-error-overlay"},jr={class:"format-error-content"},Wr={class:"format-error-message"},Br={class:"format-error-actions"},Nr={class:"doc-header"},Vr={class:"doc-title"},Hr={class:"action-area"},zr={class:"select-container"},qr={class:"select-group"},Jr=["disabled"],Yr=["value"],Kr={class:"select-group"},Xr=["disabled"],Gr=["value"],Zr=["title"],Qr={key:0,class:"science-warning"},eo={class:"action-buttons"},to=["disabled"],so={class:"btn-content"},no={key:0,class:"button-loader"},ao=["disabled"],ro={class:"btn-content"},oo={key:0,class:"button-loader"},io=["disabled"],lo={class:"btn-content"},co={key:0,class:"button-loader"},uo={class:"content-area"},po={class:"modal-header"},fo={class:"modal-body"},ho={class:"selection-content"},vo={class:"modal-header"},go={class:"modal-body"},mo={class:"alert-message"},bo={class:"alert-actions"},wo={key:2,class:"modal-overlay"},yo={class:"modal-header"},xo={class:"modal-body"},To={class:"confirm-message"},Co={class:"confirm-actions"},ko={class:"task-queue"},$o={class:"queue-header"},So={class:"queue-status-filter"},Eo=["value"],_o={class:"queue-actions"},Ao=["disabled","title"],Mo={class:"task-count"},Do={key:0,class:"queue-table-container"},Po={class:"col-id"},Oo={class:"id-header"},Io={key:0,class:"col-subject"},Ro={class:"subject-header"},Uo={class:"switch"},Lo=["title"],Fo={key:1,class:"col-status"},jo=["onClick"],Wo={class:"col-id"},Bo={class:"id-content"},No={class:"task-id"},Vo={key:0,class:"enhance-svg-icon"},Ho={key:0,class:"status-in-id"},zo={key:0,class:"col-subject"},qo=["onMouseenter"],Jo={key:1,class:"col-status"},Yo={class:"status-cell"},Ko={class:"col-actions"},Xo={class:"task-actions"},Go=["onClick"],Zo=["onClick"],Qo={key:2,class:"no-action-icon",title:"无可用操作"},ei={key:1,class:"empty-queue"},ti={key:3,class:"log-container"},si={class:"log-actions"},ni={class:"toggle-icon"},ai=["innerHTML"],ri={__name:"TaskPane",setup(e){const s=pe(!1),t=pe(!1),a=pe(!1),r=pe(""),g=pe(!1),T=pe(!1),E=pe(!1),y=pe(!1),C=pe(!0),M=pe(""),F=pe(!1),B=pe(window.innerWidth),R=wt(()=>B.value<750),z=wt(()=>B.value<380),X=()=>{B.value=window.innerWidth},H=pe(null),ee=pe(!1),te=pe(""),q={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},d=pe(!1),N=pe([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"}]),{docName:O,selected:ne,logger:ie,map:u,subject:w,stage:m,subjectOptions:k,stageOptions:S,appConfig:A,clearLog:L,checkDocumentFormat:_,getTaskStatusClass:J,getTaskStatusText:fe,terminateTask:me,run1:Ce,runCheck:je,setupLifecycle:be,navigateToTaskControl:ke,isLoading:Ae,tryRemoveTaskPlaceholderWithLoading:we,confirmDialog:$e,handleConfirm:G,getCompletedTasksCount:ye,showConfirm:Se}=wn(),le=pe(null);(()=>{var b;try{if((b=window.Application)!=null&&b.PluginStorage){const p=window.Application.PluginStorage.getItem("user_info");p?(le.value=JSON.parse(p),console.log("用户信息已加载:",le.value)):console.log("未找到用户信息")}}catch(p){console.error("解析用户信息时出错:",p)}})(),Vt(le,b=>{b&&b.orgs&&b.orgs[0]&&console.log(`用户企业ID: ${b.orgs[0].orgId}, 校对功能${b.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Pe=wt(()=>!le.value||le.value.isAdmin||le.value.isOwner?k:le.value.subject?k.filter(b=>b.value===le.value.subject):k),Ze=()=>{le.value&&!le.value.isAdmin&&!le.value.isOwner&&le.value.subject&&(w.value=le.value.subject)},ze=wt(()=>["physics","chemistry","biology","math"].includes(w.value));Vt(A,b=>{b&&(console.log("TaskPane组件收到应用配置更新:",b),console.log("当前版本类型:",b.EDITION),console.log("当前年级选项:",S.value))},{deep:!0,immediate:!0});const Qe=()=>{try{const b=_();C.value=b.isValid,M.value=b.message,F.value=!b.isValid,b.isValid||console.warn("文档格式检查失败:",b.message)}catch(b){console.error("执行文档格式检查时出错:",b),C.value=!1,M.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",F.value=!0}},ge=wt(()=>{let b={};const p=u;if(te.value==="")b={...p};else for(const I in p)if(Object.prototype.hasOwnProperty.call(p,I)){const V=p[I];V.status===te.value&&(b[I]=V)}return ee.value&&H.value?b[H.value]?{[H.value]:b[H.value]}:{}:b}),et=wt(()=>{const b=ge.value;return Object.entries(b).map(([I,V])=>({tid:I,...V})).sort((I,V)=>{const ae=I.startTime||0;return(V.startTime||0)-ae}).reduce((I,V)=>{const{tid:ae,...re}=V;return I[ae]=re,I},{})}),We=(b="wps-analysis")=>{w.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(r.value="未选中内容",t.value=!0):(b==="wps-analysis"?T.value=!0:b==="wps-enhance_analysis"&&(E.value=!0),Ce(b).catch(p=>{console.log(p),p.message.includes("重叠")?(r.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",p),r.value=p.message,t.value=!0)}).finally(()=>{b==="wps-analysis"?T.value=!1:b==="wps-enhance_analysis"&&(E.value=!1)})):(r.value="请选择学科",t.value=!0)},ot=()=>{w.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(r.value="未选中内容",t.value=!0):(y.value=!0,je().catch(b=>{console.log(b),b.message.includes("重叠")?(r.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",b),r.value=b.message,t.value=!0)}).finally(()=>{y.value=!1})):(r.value="请选择学科",t.value=!0)},Me=(b,p)=>{H.value=b,ke(b)},Oe=b=>{u[b]&&(u[b].status=3),H.value===b&&(H.value=null),we(b,!0)},tt=async()=>{const b=Object.entries(u).filter(([p,I])=>I.status===2);if(b.length===0){r.value="没有已完成的任务可释放",t.value=!0;return}try{if(await Se(`确定要释放所有 ${b.length} 个已完成的任务吗？
此操作不可撤销。`)){let I=0;b.forEach(([V,ae])=>{u[V]&&(u[V].status=3,H.value===V&&(H.value=null),we(V,!0),I++)}),r.value=`已成功释放 ${I} 个任务`,t.value=!0}}catch(p){console.error("释放任务时出错:",p),r.value="释放任务时出现错误",t.value=!0}},Be=()=>{g.value=!g.value},qe=b=>b?b.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",st=b=>{const p=Je(b);return p?qe(p):"无题目内容"},Je=b=>{if(!b.selectedText)return"";const p=b.selectedText.split("\r").filter(V=>V.trim());if(p.length===1){const V=b.selectedText.split(`
`).filter(ae=>ae.trim());V.length>1&&p.splice(0,1,...V)}const I=p.map((V,ae)=>{const re=V.trim();return re.length>200,re});return I.length===1?p[0].trim():I.join(`
`)},it=(b,p)=>{if(!d.value)return;const I=b.target,V=Je(p).toString();if(!V||V.trim()===""){console.log("题目内容为空，不显示tooltip");return}const ae=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${V.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,re=b.clientX,ve=b.clientY;if(q.subjects.has(I)){const o=q.subjects.get(I);o.setContent(ae),o.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ve,bottom:ve,left:re,right:re})}),o.show();return}const n=vt(I,{content:ae,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:ve,bottom:ve,left:re,right:re}),onHidden:()=>{n.setProps({getReferenceClientRect:null})}});q.subjects.set(I,n),n.show()},nt=b=>{const p=b.currentTarget,I=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,V=b.clientX,ae=b.clientY;if(q.enhance.has(p)){const ve=q.enhance.get(p);ve.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ae,bottom:ae,left:V,right:V})}),ve.show();return}const re=vt(p,{content:I,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});q.enhance.set(p,re),re.show()},Et=()=>{q.subjects.forEach(b=>{b.destroy()}),q.subjects.clear(),q.enhance.forEach(b=>{b.destroy()}),q.enhance.clear(),q.softBreak.forEach(b=>{b.destroy()}),q.softBreak.clear(),document.removeEventListener("click",mt),document.removeEventListener("mousemove",ct)},mt=b=>{const p=document.querySelector(".tippy-box");p&&!p.contains(b.target)&&(q.subjects.forEach(I=>I.hide()),q.enhance.forEach(I=>I.hide()),q.softBreak.forEach(I=>I.hide()))};let lt=0;const ct=b=>{const p=Date.now();if(p-lt<100)return;lt=p;const I=document.querySelector(".tippy-box");if(!I)return;const V=I.getBoundingClientRect();!(b.clientX>=V.left-20&&b.clientX<=V.right+20&&b.clientY>=V.top-20&&b.clientY<=V.bottom+20)&&!I.matches(":hover")&&(q.subjects.forEach(re=>re.hide()),q.enhance.forEach(re=>re.hide()),q.softBreak.forEach(re=>re.hide()))},pt=()=>{document.addEventListener("click",mt),document.addEventListener("mousemove",ct)};Is(()=>{window.addEventListener("resize",X),pt(),Ze(),setTimeout(()=>{Qe()},500);const b=document.createElement("style");b.id="tippy-custom-styles",b.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(b)}),rn(()=>{window.removeEventListener("resize",X),Et();const b=document.getElementById("tippy-custom-styles");b&&b.remove()}),be();const x=b=>b.selectedText?b.selectedText.includes("\v")||b.selectedText.includes("\v"):!1,j=b=>{const p=b.currentTarget,I=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,V=b.clientX,ae=b.clientY;if(q.softBreak.has(p)){const ve=q.softBreak.get(p);ve.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ae,bottom:ae,left:V,right:V})}),ve.show();return}const re=vt(p,{content:I,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});q.softBreak.set(p,re),re.show()};return(b,p)=>{var I,V,ae,re,ve;return Y(),K("div",Ur,[he(Ae)?(Y(),K("div",Lr,p[26]||(p[26]=[h("div",{class:"loading-spinner"},null,-1),h("div",{class:"loading-text"},"处理中...",-1)]))):se("",!0),F.value?(Y(),K("div",Fr,[h("div",jr,[p[27]||(p[27]=h("div",{class:"format-error-icon"},"⚠️",-1)),p[28]||(p[28]=h("div",{class:"format-error-title"},"文档格式不支持",-1)),h("div",Wr,de(M.value),1),h("div",Br,[h("button",{class:"retry-check-btn",onClick:p[0]||(p[0]=n=>Qe())},"重新检查")])])])):se("",!0),h("div",Nr,[h("div",Vr,de(he(O)||"未选择文档"),1),h("button",{class:"settings-btn",onClick:p[1]||(p[1]=n=>a.value=!0)},p[29]||(p[29]=[h("i",{class:"icon-settings"},null,-1)]))]),h("div",Hr,[h("div",zr,[h("div",qr,[p[30]||(p[30]=h("label",{for:"stage-select"},"年级:",-1)),Ne(h("select",{id:"stage-select","onUpdate:modelValue":p[2]||(p[2]=n=>ds(m)?m.value=n:null),class:"select-input",disabled:F.value},[(Y(!0),K(At,null,Mt(he(S),n=>(Y(),K("option",{key:n.value,value:n.value},de(n.label),9,Yr))),128))],8,Jr),[[Kt,he(m)]])]),h("div",Kr,[p[31]||(p[31]=h("label",{for:"subject-select"},"学科:",-1)),Ne(h("select",{id:"subject-select","onUpdate:modelValue":p[3]||(p[3]=n=>ds(w)?w.value=n:null),class:"select-input",disabled:F.value},[(Y(!0),K(At,null,Mt(Pe.value,n=>(Y(),K("option",{key:n.value,value:n.value},de(n.label),9,Gr))),128))],8,Xr),[[Kt,he(w)]]),le.value&&!le.value.isAdmin&&!le.value.isOwner&&le.value.subject?(Y(),K("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((I=Pe.value.find(n=>n.value===le.value.subject))==null?void 0:I.label)||le.value.subject} 学科`}," 🔒 ",8,Zr)):se("",!0)])]),ze.value?(Y(),K("div",Qr," 理科可使用增强模式以获取更精准的解析 ")):se("",!0),h("div",eo,[h("button",{class:"action-btn primary",onClick:p[4]||(p[4]=n=>We("wps-analysis")),disabled:T.value||F.value},[h("div",so,[T.value?(Y(),K("span",no)):se("",!0),p[32]||(p[32]=h("span",{class:"btn-text"},"解析",-1))])],8,to),ze.value?(Y(),K("button",{key:0,class:"action-btn enhance",onClick:p[5]||(p[5]=n=>We("wps-enhance_analysis")),disabled:E.value||F.value},[h("div",ro,[E.value?(Y(),K("span",oo)):se("",!0),p[33]||(p[33]=h("span",{class:"btn-text"},"增强解析",-1))])],8,ao)):se("",!0),((ae=(V=le.value)==null?void 0:V.orgs[0])==null?void 0:ae.orgId)===2?(Y(),K("button",{key:1,class:"action-btn secondary",onClick:p[6]||(p[6]=n=>ot()),disabled:y.value||F.value},[h("div",lo,[y.value?(Y(),K("span",co)):se("",!0),p[34]||(p[34]=h("span",{class:"btn-text"},"校对",-1))])],8,io)):se("",!0)])]),h("div",uo,[s.value?(Y(),K("div",{key:0,class:"modal-overlay",onClick:p[9]||(p[9]=n=>s.value=!1)},[h("div",{class:"modal-content",onClick:p[8]||(p[8]=dt(()=>{},["stop"]))},[h("div",po,[p[35]||(p[35]=h("div",{class:"modal-title"},"选中内容",-1)),h("button",{class:"modal-close",onClick:p[7]||(p[7]=n=>s.value=!1)},"×")]),h("div",fo,[h("pre",ho,de(he(ne)||"未选中内容"),1)])])])):se("",!0),t.value?(Y(),K("div",{key:1,class:"modal-overlay",onClick:p[13]||(p[13]=n=>t.value=!1)},[h("div",{class:"modal-content alert-modal",onClick:p[12]||(p[12]=dt(()=>{},["stop"]))},[h("div",vo,[p[36]||(p[36]=h("div",{class:"modal-title"},"提示",-1)),h("button",{class:"modal-close",onClick:p[10]||(p[10]=n=>t.value=!1)},"×")]),h("div",go,[h("div",mo,de(r.value),1),h("div",bo,[h("button",{class:"action-btn primary",onClick:p[11]||(p[11]=n=>t.value=!1)},"确定")])])])])):se("",!0),he($e).show?(Y(),K("div",wo,[h("div",{class:"modal-content confirm-modal",onClick:p[17]||(p[17]=dt(()=>{},["stop"]))},[h("div",yo,[p[37]||(p[37]=h("div",{class:"modal-title"},"确认",-1)),h("button",{class:"modal-close",onClick:p[14]||(p[14]=n=>he(G)(!1))},"×")]),h("div",xo,[h("div",To,de(he($e).message),1),h("div",Co,[h("button",{class:"action-btn secondary",onClick:p[15]||(p[15]=n=>he(G)(!1))},"取消"),h("button",{class:"action-btn primary",onClick:p[16]||(p[16]=n=>he(G)(!0))},"确定")])])])])):se("",!0),h("div",ko,[h("div",$o,[p[38]||(p[38]=h("div",{class:"queue-title"},"任务队列",-1)),h("div",So,[Ne(h("select",{id:"status-filter-select","onUpdate:modelValue":p[18]||(p[18]=n=>te.value=n),class:"status-filter-select-input"},[(Y(!0),K(At,null,Mt(N.value,n=>(Y(),K("option",{key:n.value,value:n.value},de(n.label),9,Eo))),128))],512),[[Kt,te.value]])]),h("div",_o,[h("button",{class:"release-all-btn",onClick:tt,disabled:he(ye)()===0,title:he(ye)()===0?"无已完成任务可释放":`释放所有${he(ye)()}个已完成任务`}," 一键释放 ",8,Ao)]),h("div",Mo,de(Object.keys(ge.value).length)+"个任务",1)]),Object.keys(ge.value).length>0?(Y(),K("div",Do,[h("table",{class:Re(["queue-table",{"narrow-view":R.value,"ultra-narrow-view":z.value}])},[h("thead",null,[h("tr",null,[h("th",Po,[h("div",Oo,[p[40]||(p[40]=h("span",null,"任务ID",-1)),h("span",{class:"help-icon",onMouseenter:p[19]||(p[19]=n=>nt(n))},p[39]||(p[39]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[h("circle",{cx:"12",cy:"12",r:"10"}),h("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),h("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),R.value?se("",!0):(Y(),K("th",Io,[h("div",Ro,[p[41]||(p[41]=h("span",null,"题目内容",-1)),h("label",Uo,[Ne(h("input",{type:"checkbox","onUpdate:modelValue":p[20]||(p[20]=n=>d.value=n)},null,512),[[on,d.value]]),h("span",{class:"slider round",title:d.value?"关闭题目预览":"开启题目预览"},null,8,Lo)])])])),z.value?se("",!0):(Y(),K("th",Fo,"状态")),p[42]||(p[42]=h("th",{class:"col-actions"},"操作",-1))])]),h("tbody",null,[(Y(!0),K(At,null,Mt(et.value,(n,o)=>(Y(),K("tr",{key:o,class:Re(["task-row",[he(J)(n.status),{"selected-task-row":o===H.value}]]),onClick:i=>Me(o)},[h("td",Wo,[h("div",{class:Re(["id-cell",{"id-with-status":z.value}])},[h("div",Bo,[h("span",No,de(o.substring(0,8)),1),n.wordType==="wps-enhance_analysis"||n.isEnhanced?(Y(),K("span",Vo,p[43]||(p[43]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9c27b0","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[h("title",null,"增强模式"),h("path",{d:"M13 2L3 14h9l-1 8 10-12h-9l1-8z"})],-1)]))):se("",!0),x(n)?(Y(),K("span",{key:1,class:"soft-break-warning-icon",onMouseenter:p[21]||(p[21]=i=>j(i))},p[44]||(p[44]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[h("title",null,"提示"),h("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),h("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),h("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):se("",!0)]),z.value?(Y(),K("div",Ho,[h("span",{class:Re(["task-tag compact",he(J)(n.status)])},de(he(fe)(n.status)),3)])):se("",!0)],2)]),R.value?se("",!0):(Y(),K("td",zo,[h("div",{class:"subject-cell",onMouseenter:i=>it(i,n)},de(st(n)),41,qo)])),z.value?se("",!0):(Y(),K("td",Jo,[h("div",Yo,[h("span",{class:Re(["task-tag",he(J)(n.status)])},de(he(fe)(n.status)),3)])])),h("td",Ko,[h("div",Xo,[n.status===1?(Y(),K("button",{key:0,onClick:dt(i=>he(me)(o),["stop"]),class:"terminate-btn"}," 终止 ",8,Go)):se("",!0),n.status===2?(Y(),K("button",{key:1,onClick:dt(i=>Oe(o),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Zo)):se("",!0),n.status!==1&&n.status!==2?(Y(),K("span",Qo,p[45]||(p[45]=[h("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[h("circle",{cx:"12",cy:"12",r:"10"}),h("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),h("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):se("",!0)])])],10,jo))),128))])],2)])):(Y(),K("div",ei,p[46]||(p[46]=[h("div",{class:"empty-text"},"暂无任务",-1)])))]),((ve=(re=le.value)==null?void 0:re.orgs[0])==null?void 0:ve.orgId)===2?(Y(),K("div",ti,[h("div",{class:"log-header",onClick:Be},[p[47]||(p[47]=h("div",{class:"log-title"},"执行日志",-1)),h("div",si,[h("button",{class:"clear-btn",onClick:p[22]||(p[22]=dt(n=>he(L)(),["stop"]))},"清空日志"),h("span",ni,de(g.value?"▼":"▶"),1)])]),g.value?(Y(),K("div",{key:0,class:"log-content",innerHTML:he(ie)},null,8,ai)):se("",!0)])):se("",!0)]),a.value?(Y(),K("div",{key:2,class:"modal-overlay",onClick:p[25]||(p[25]=n=>a.value=!1)},[h("div",{class:"modal-content",onClick:p[24]||(p[24]=dt(()=>{},["stop"]))},[ln(oa,{onClose:p[23]||(p[23]=n=>a.value=!1)})])])):se("",!0)])}}},ii=Rs(ri,[["__scopeId","data-v-e7c6c2a7"]]);export{ii as default};
