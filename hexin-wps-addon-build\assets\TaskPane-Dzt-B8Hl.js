import{U as on,r as pe,h as gt,v as Ye,i as Q,j as Rt,k as Fs,m as Jt,_ as js,n as ln,o as F,c as j,a as c,p as cn,t as ae,f as z,q as $e,w as Je,s as Vt,e as Zt,F as mt,u as _t,x as ct,y as un,z as ue,A as Qt,B as gs,C as ht,D as dn,E as pn}from"./index-ByeLOF6d.js";function fn(e,s){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=on.WPS_Enum),e){case"dockLeft":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let t=window.Application.PluginStorage.getItem("taskpane_id");if(t){let a=window.Application.GetTaskPane(t);a.Visible=!1}break}case"addString":{let t=window.Application.ActiveDocument;if(t){t.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let t=window.Application.ActiveDocument;return t?t.Name:"当前没有打开任何文档"}}}const hn={onbuttonclick:fn};var vn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function gn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function mn(e){if(e.__esModule)return e;var s=e.default;if(typeof s=="function"){var t=function a(){return this instanceof a?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};t.prototype=s.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(e).forEach(function(a){var l=Object.getOwnPropertyDescriptor(e,a);Object.defineProperty(t,a,l.get?l:{enumerable:!0,get:function(){return e[a]}})}),t}var Ws={exports:{}};const bn={},wn=Object.freeze(Object.defineProperty({__proto__:null,default:bn},Symbol.toStringTag,{value:"Module"})),ms=mn(wn);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(e){(function(){var s="input is invalid type",t="finalize already called",a=typeof window=="object",l=a?window:{};l.JS_SHA1_NO_WINDOW&&(a=!1);var g=!a&&typeof self=="object",T=!l.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;T?l=vn:g&&(l=self);var E=!l.JS_SHA1_NO_COMMON_JS&&!0&&e.exports,w=!l.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",C="0123456789abcdef".split(""),P=[-**********,8388608,32768,128],L=[24,16,8,0],V=["hex","array","digest","arrayBuffer"],I=[],B=Array.isArray;(l.JS_SHA1_NO_NODE_JS||!B)&&(B=function(h){return Object.prototype.toString.call(h)==="[object Array]"});var Y=ArrayBuffer.isView;w&&(l.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!Y)&&(Y=function(h){return typeof h=="object"&&h.buffer&&h.buffer.constructor===ArrayBuffer});var N=function(h){var b=typeof h;if(b==="string")return[h,!0];if(b!=="object"||h===null)throw new Error(s);if(w&&h.constructor===ArrayBuffer)return[new Uint8Array(h),!1];if(!B(h)&&!Y(h))throw new Error(s);return[h,!1]},se=function(h){return function(b){return new D(!0).update(b)[h]()}},ne=function(){var h=se("hex");T&&(h=oe(h)),h.create=function(){return new D},h.update=function(k){return h.create().update(k)};for(var b=0;b<V.length;++b){var x=V[b];h[x]=se(x)}return h},oe=function(h){var b=ms,x=ms.Buffer,k;x.from&&!l.JS_SHA1_NO_BUFFER_FROM?k=x.from:k=function(m){return new x(m)};var $=function(m){if(typeof m=="string")return b.createHash("sha1").update(m,"utf8").digest("hex");if(m==null)throw new Error(s);return m.constructor===ArrayBuffer&&(m=new Uint8Array(m)),B(m)||Y(m)||m.constructor===x?b.createHash("sha1").update(k(m)).digest("hex"):h(m)};return $},u=function(h){return function(b,x){return new le(b,!0).update(x)[h]()}},ee=function(){var h=u("hex");h.create=function(k){return new le(k)},h.update=function(k,$){return h.create(k).update($)};for(var b=0;b<V.length;++b){var x=V[b];h[x]=u(x)}return h};function D(h){h?(I[0]=I[16]=I[1]=I[2]=I[3]=I[4]=I[5]=I[6]=I[7]=I[8]=I[9]=I[10]=I[11]=I[12]=I[13]=I[14]=I[15]=0,this.blocks=I):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}D.prototype.update=function(h){if(this.finalized)throw new Error(t);var b=N(h);h=b[0];for(var x=b[1],k,$=0,m,U=h.length||0,_=this.blocks;$<U;){if(this.hashed&&(this.hashed=!1,_[0]=this.block,this.block=_[16]=_[1]=_[2]=_[3]=_[4]=_[5]=_[6]=_[7]=_[8]=_[9]=_[10]=_[11]=_[12]=_[13]=_[14]=_[15]=0),x)for(m=this.start;$<U&&m<64;++$)k=h.charCodeAt($),k<128?_[m>>>2]|=k<<L[m++&3]:k<2048?(_[m>>>2]|=(192|k>>>6)<<L[m++&3],_[m>>>2]|=(128|k&63)<<L[m++&3]):k<55296||k>=57344?(_[m>>>2]|=(224|k>>>12)<<L[m++&3],_[m>>>2]|=(128|k>>>6&63)<<L[m++&3],_[m>>>2]|=(128|k&63)<<L[m++&3]):(k=65536+((k&1023)<<10|h.charCodeAt(++$)&1023),_[m>>>2]|=(240|k>>>18)<<L[m++&3],_[m>>>2]|=(128|k>>>12&63)<<L[m++&3],_[m>>>2]|=(128|k>>>6&63)<<L[m++&3],_[m>>>2]|=(128|k&63)<<L[m++&3]);else for(m=this.start;$<U&&m<64;++$)_[m>>>2]|=h[$]<<L[m++&3];this.lastByteIndex=m,this.bytes+=m-this.start,m>=64?(this.block=_[16],this.start=m-64,this.hash(),this.hashed=!0):this.start=m}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},D.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var h=this.blocks,b=this.lastByteIndex;h[16]=this.block,h[b>>>2]|=P[b&3],this.block=h[16],b>=56&&(this.hashed||this.hash(),h[0]=this.block,h[16]=h[1]=h[2]=h[3]=h[4]=h[5]=h[6]=h[7]=h[8]=h[9]=h[10]=h[11]=h[12]=h[13]=h[14]=h[15]=0),h[14]=this.hBytes<<3|this.bytes>>>29,h[15]=this.bytes<<3,this.hash()}},D.prototype.hash=function(){var h=this.h0,b=this.h1,x=this.h2,k=this.h3,$=this.h4,m,U,_,K=this.blocks;for(U=16;U<80;++U)_=K[U-3]^K[U-8]^K[U-14]^K[U-16],K[U]=_<<1|_>>>31;for(U=0;U<20;U+=5)m=b&x|~b&k,_=h<<5|h>>>27,$=_+m+$+1518500249+K[U]<<0,b=b<<30|b>>>2,m=h&b|~h&x,_=$<<5|$>>>27,k=_+m+k+1518500249+K[U+1]<<0,h=h<<30|h>>>2,m=$&h|~$&b,_=k<<5|k>>>27,x=_+m+x+1518500249+K[U+2]<<0,$=$<<30|$>>>2,m=k&$|~k&h,_=x<<5|x>>>27,b=_+m+b+1518500249+K[U+3]<<0,k=k<<30|k>>>2,m=x&k|~x&$,_=b<<5|b>>>27,h=_+m+h+1518500249+K[U+4]<<0,x=x<<30|x>>>2;for(;U<40;U+=5)m=b^x^k,_=h<<5|h>>>27,$=_+m+$+1859775393+K[U]<<0,b=b<<30|b>>>2,m=h^b^x,_=$<<5|$>>>27,k=_+m+k+1859775393+K[U+1]<<0,h=h<<30|h>>>2,m=$^h^b,_=k<<5|k>>>27,x=_+m+x+1859775393+K[U+2]<<0,$=$<<30|$>>>2,m=k^$^h,_=x<<5|x>>>27,b=_+m+b+1859775393+K[U+3]<<0,k=k<<30|k>>>2,m=x^k^$,_=b<<5|b>>>27,h=_+m+h+1859775393+K[U+4]<<0,x=x<<30|x>>>2;for(;U<60;U+=5)m=b&x|b&k|x&k,_=h<<5|h>>>27,$=_+m+$-1894007588+K[U]<<0,b=b<<30|b>>>2,m=h&b|h&x|b&x,_=$<<5|$>>>27,k=_+m+k-1894007588+K[U+1]<<0,h=h<<30|h>>>2,m=$&h|$&b|h&b,_=k<<5|k>>>27,x=_+m+x-1894007588+K[U+2]<<0,$=$<<30|$>>>2,m=k&$|k&h|$&h,_=x<<5|x>>>27,b=_+m+b-1894007588+K[U+3]<<0,k=k<<30|k>>>2,m=x&k|x&$|k&$,_=b<<5|b>>>27,h=_+m+h-1894007588+K[U+4]<<0,x=x<<30|x>>>2;for(;U<80;U+=5)m=b^x^k,_=h<<5|h>>>27,$=_+m+$-899497514+K[U]<<0,b=b<<30|b>>>2,m=h^b^x,_=$<<5|$>>>27,k=_+m+k-899497514+K[U+1]<<0,h=h<<30|h>>>2,m=$^h^b,_=k<<5|k>>>27,x=_+m+x-899497514+K[U+2]<<0,$=$<<30|$>>>2,m=k^$^h,_=x<<5|x>>>27,b=_+m+b-899497514+K[U+3]<<0,k=k<<30|k>>>2,m=x^k^$,_=b<<5|b>>>27,h=_+m+h-899497514+K[U+4]<<0,x=x<<30|x>>>2;this.h0=this.h0+h<<0,this.h1=this.h1+b<<0,this.h2=this.h2+x<<0,this.h3=this.h3+k<<0,this.h4=this.h4+$<<0},D.prototype.hex=function(){this.finalize();var h=this.h0,b=this.h1,x=this.h2,k=this.h3,$=this.h4;return C[h>>>28&15]+C[h>>>24&15]+C[h>>>20&15]+C[h>>>16&15]+C[h>>>12&15]+C[h>>>8&15]+C[h>>>4&15]+C[h&15]+C[b>>>28&15]+C[b>>>24&15]+C[b>>>20&15]+C[b>>>16&15]+C[b>>>12&15]+C[b>>>8&15]+C[b>>>4&15]+C[b&15]+C[x>>>28&15]+C[x>>>24&15]+C[x>>>20&15]+C[x>>>16&15]+C[x>>>12&15]+C[x>>>8&15]+C[x>>>4&15]+C[x&15]+C[k>>>28&15]+C[k>>>24&15]+C[k>>>20&15]+C[k>>>16&15]+C[k>>>12&15]+C[k>>>8&15]+C[k>>>4&15]+C[k&15]+C[$>>>28&15]+C[$>>>24&15]+C[$>>>20&15]+C[$>>>16&15]+C[$>>>12&15]+C[$>>>8&15]+C[$>>>4&15]+C[$&15]},D.prototype.toString=D.prototype.hex,D.prototype.digest=function(){this.finalize();var h=this.h0,b=this.h1,x=this.h2,k=this.h3,$=this.h4;return[h>>>24&255,h>>>16&255,h>>>8&255,h&255,b>>>24&255,b>>>16&255,b>>>8&255,b&255,x>>>24&255,x>>>16&255,x>>>8&255,x&255,k>>>24&255,k>>>16&255,k>>>8&255,k&255,$>>>24&255,$>>>16&255,$>>>8&255,$&255]},D.prototype.array=D.prototype.digest,D.prototype.arrayBuffer=function(){this.finalize();var h=new ArrayBuffer(20),b=new DataView(h);return b.setUint32(0,this.h0),b.setUint32(4,this.h1),b.setUint32(8,this.h2),b.setUint32(12,this.h3),b.setUint32(16,this.h4),h};function le(h,b){var x,k=N(h);if(h=k[0],k[1]){var $=[],m=h.length,U=0,_;for(x=0;x<m;++x)_=h.charCodeAt(x),_<128?$[U++]=_:_<2048?($[U++]=192|_>>>6,$[U++]=128|_&63):_<55296||_>=57344?($[U++]=224|_>>>12,$[U++]=128|_>>>6&63,$[U++]=128|_&63):(_=65536+((_&1023)<<10|h.charCodeAt(++x)&1023),$[U++]=240|_>>>18,$[U++]=128|_>>>12&63,$[U++]=128|_>>>6&63,$[U++]=128|_&63);h=$}h.length>64&&(h=new D(!0).update(h).array());var K=[],ve=[];for(x=0;x<64;++x){var me=h[x]||0;K[x]=92^me,ve[x]=54^me}D.call(this,b),this.update(ve),this.oKeyPad=K,this.inner=!0,this.sharedMemory=b}le.prototype=new D,le.prototype.finalize=function(){if(D.prototype.finalize.call(this),this.inner){this.inner=!1;var h=this.array();D.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(h),D.prototype.finalize.call(this)}};var de=ne();de.sha1=de,de.sha1.hmac=ee(),E?e.exports=de:l.sha1=de})()})(Ws);var yn=Ws.exports;const xn=gn(yn);function bs(){return"http://worksheet.hexinedu.com"}function $t(){return"http://127.0.0.1:3000"}function ws(){let e=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(e+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(s){const t=(e+Math.random()*16)%16|0;return e=Math.floor(e/16),(s=="x"?t:t&3|8).toString(16)})}const St=async(e,s,t,a={},l=8e3)=>{try{return await Promise.race([e(),new Promise((g,T)=>setTimeout(()=>T(new Error("WebSocket请求超时，切换到HTTP")),l))])}catch{try{let T;return s==="get"?T=await Rt.get(t,{params:a}):s==="post"?T=await Rt.post(t,a):s==="delete"&&(T=await Rt.delete(t)),T.data}catch(T){throw new Error(`请求失败: ${T.message||"未知错误"}`)}}};function kn(e,s,t,a){const l=[e,s,t,a].join(":");return xn(l)}function Tn(){const e=pe(""),s=pe(""),t=pe(""),a=gt({}),l=pe(""),g=pe("");let T="",E=null;const w=pe("c:\\Temp"),C=gt({appKey:"",appSecret:""}),P=gt({show:!1,message:"",resolveCallback:null,rejectCallback:null}),L=gt({show:!1,title:"",message:"",type:"error"}),V=pe(""),I=pe("junior"),B=pe(null),Y=pe(!1),N=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],se=()=>Ye.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],ne=gt(se()),oe=async()=>{try{const n=await Q.getWatcherStatus();n.data&&n.data.watchDir&&(w.value=n.data.watchDir,t.value+=`<span class="log-item info">已获取监控目录: ${w.value}</span><br/>`)}catch(n){t.value+=`<span class="log-item error">获取监控目录失败: ${n.message}</span><br/>`,console.error("获取监控目录失败:",n)}},u=async()=>{var n,i,r;try{if(!C.appKey||!C.appSecret)throw new Error("未初始化app信息");const d=60,o=Date.now();let v;try{const q=window.Application.PluginStorage.getItem("token_info");q&&(v=JSON.parse(q))}catch(q){v=null,t.value+=`<span class="log-item warning">解析缓存token失败: ${q.message}</span><br/>`}if(v&&v.access_token&&v.expired_time>o+d*1e3)return v.access_token;const p=C.appKey,O="1234567",M=Math.floor(Date.now()/1e3),A=kn(p,O,C.appSecret,M),R=await Rt.get(bs()+"/api/open/account/v1/auth/token",{params:{app_key:p,app_nonstr:O,app_timestamp:M,app_signature:A}});if((i=(n=R.data)==null?void 0:n.data)!=null&&i.access_token){const q=R.data.data.access_token;let Z;if(R.data.data.expired_time){const fe=parseInt(R.data.data.expired_time);Z=o+fe*1e3,t.value+=`<span class="log-item info">token已更新，有效期${fe}秒</span><br/>`}else Z=o+3600*1e3,t.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const te={access_token:q,expired_time:Z};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(te))}catch(fe){t.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${fe.message}</span><br/>`}return q}else throw new Error(((r=R.data)==null?void 0:r.message)||"获取access_token失败")}catch(d){throw t.value+=`<span class="log-item error">获取access_token失败: ${d.message}</span><br/>`,d}},ee=()=>{t.value='<span class="log-item info">日志已清空</span><br/>'},D=()=>{const n=window.Application,i=n.Documents.Count;for(let r=1;r<=i;r++){const d=n.Documents.Item(r);if(d.DocID===l.value)return d}return null},le=()=>{try{const n=D();if(!n)return{isValid:!1,message:"未找到当前文档"};let i="";try{i=n.Name||""}catch{try{i=m("getDocName")||""}catch{i=""}}if(i){const r=i.toLowerCase();return r.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:r.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(n){return console.error("检查文档格式时出错:",n),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},de=(n,i="",r=0,d=null)=>{try{const o=window.Application,v=D();let p;if(d)p=d;else{const O=v.ActiveWindow.Selection;if(!O||O.Text==="")return t.value+='<span class="log-item error">请先选择文本</span><br/>',!1;p=O.Range}if(i){const O=p.Text,M=p.Find;M.ClearFormatting(),M.Text=i,M.Forward=!0,M.Wrap=0;let A=0,R=[];const q=p.Start,Z=p.End;for(;M.Execute()&&(M.Found&&M.Parent.Start>=q&&M.Parent.End<=Z);){const te=M.Parent.Start,fe=M.Parent.End;if(R.some(we=>te===we.start&&fe===we.end)){const we=Math.min(fe,Z);if(we>=Z)break;M.Parent.SetRange(we,Z)}else{if(R.push({start:te,end:fe}),r===-1||A===r){const he=v.Comments.Add(M.Parent,n);if(r!==-1&&A===r)return!0}A++;const we=Math.min(fe,Z);if(console.log("nextStart",we),we>=Z)break;const Ce=v.Range(we,Z);M.Parent.SetRange(we,Z)}}return r!==-1&&A<=r?!1:r===-1&&A>0?!0:!(r===-1&&A===0)}else{const O=v.Comments.Add(p,n);return t.value+=`<span class="log-item success">已为${d?"指定范围":"选中内容"}添加批注: "${n}"</span><br/>`,!0}}catch(o){return t.value+=`<span class="log-item error">添加批注失败: ${o.message}</span><br/>`,!1}},h=n=>n===0?"status-preparing":n===1?"status-running":n===2?"status-completed":n===-1?"status-error":n===3?"status-released":n===4?"status-stopped":"",b=n=>n===0?"准备中":n===1?"进行中":n===2?"已完成":n===-1?"异常":n===3?"已释放":n===4?"已停止":"准备中",x=n=>{const i=Date.now()-n,r=Math.floor(i/1e3);return r<60?`${r}秒`:r<3600?`${Math.floor(r/60)}分${r%60}秒`:`${Math.floor(r/3600)}时${Math.floor(r%3600/60)}分`},k=async n=>{try{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户选择不继续";try{const r=D();if(r&&r.ContentControls)for(let d=1;d<=r.ContentControls.Count;d++)try{const o=r.ContentControls.Item(d);if(o&&o.Title&&(o.Title===`任务_${n}`||o.Title===`任务增强_${n}`||o.Title===`校对_${n}`)){const v=o.Title===`任务增强_${n}`||a[n].isEnhanced,p=o.Title===`校对_${n}`||a[n].isCheckTask;p?o.Title=`已停止校对_${n}`:v?o.Title=`已停止增强_${n}`:o.Title=`已停止_${n}`;const O=p?"校对":v?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${n.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(r){t.value+=`<span class="log-item warning">更新控件标题失败: ${r.message}</span><br/>`}let i=null;if(X[n]&&X[n].urlId){i=X[n].urlId;try{try{const r=await St(async()=>await Q.getUrlMonitorStatus(),"get",`${$t()}/api/url/status`),o=(Array.isArray(r)?r:r.data?Array.isArray(r.data)?r.data:[]:[]).find(v=>v.urlId===i);o&&o.downloadedPath&&(a[n].resultFile=o.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${o.downloadedPath}</span><br/>`)}catch(r){t.value+=`<span class="log-item warning">检查URL下载状态出错: ${r.message}</span><br/>`}t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ye(i),delete X[n]}catch(r){t.value+=`<span class="log-item warning">停止URL监控出错: ${r.message}，将重试</span><br/>`,delete X[n],setTimeout(async()=>{try{i&&await St(async()=>await Q.stopUrlMonitoring(i),"delete",`${$t()}/api/url/monitor/${i}`)}catch(d){t.value+=`<span class="log-item warning">重试停止URL监控失败: ${d.message}</span><br/>`}},1e3)}}t.value+=`<span class="log-item success">任务${n.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(i){t.value+=`<span class="log-item error">停止任务${n.substring(0,8)}出错: ${i.message}</span><br/>`,X[n]&&delete X[n]}},$=async n=>{if(!a[n]){t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的数据</span><br/>`;return}a[n].status=4,a[n].terminated=!0,a[n].errorMessage="用户手动终止";const i=D();if(i&&i.ContentControls)for(let d=1;d<=i.ContentControls.Count;d++)try{const o=i.ContentControls.Item(d);if(o&&o.Title&&(o.Title===`任务_${n}`||o.Title===`任务增强_${n}`||o.Title===`校对_${n}`)){const v=o.Title===`任务增强_${n}`||a[n].isEnhanced,p=o.Title===`校对_${n}`||a[n].isCheckTask;p?o.Title=`已停止校对_${n}`:v?o.Title=`已停止增强_${n}`:o.Title=`已停止_${n}`,o.LockContents=!1;const O=p?"校对":v?"增强":"普通";t.value+=`<span class="log-item info">已将${O}任务${n.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let r=null;if(X[n]&&X[n].urlId){r=X[n].urlId;try{const d=await Q.getUrlMonitorStatus(),v=(Array.isArray(d)?d:d.data?Array.isArray(d.data)?d.data:[]:[]).find(p=>p.urlId===r);v&&v.downloadedPath&&(a[n].resultFile=v.downloadedPath,a[n].resultDownloaded=!0,t.value+=`<span class="log-item success">发现任务${n.substring(0,8)}已下载文件: ${v.downloadedPath}</span><br/>`),t.value+=`<span class="log-item info">停止任务${n.substring(0,8)}的URL监控</span><br/>`,await ye(r),delete X[n]}catch(d){t.value+=`<span class="log-item warning">停止URL监控出错: ${d.message}，将重试</span><br/>`,delete X[n]}}},m=n=>hn.onbuttonclick(n),U=()=>{try{e.value=m("getDocName")||"未命名文档"}catch{e.value="未命名文档"}},_=()=>{D().ActiveWindow.Selection.Copy()},K=n=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),i.Close(),D().ActiveWindow.Activate()},ve=n=>{const i=window.Application.Documents.Add("",!1,0,!1);i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),i.Close(),D().ActiveWindow.Activate()},me=n=>{try{const r=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,d=window.Application.Documents.Add("",!1,0,!1);d.Content.Paste();const o=`${r}\\${n}`;d.SaveAs2(o,12,"","",!1),d.Close(),D().ActiveWindow.Activate(),t.value+=`<span class="log-item success">文件已保存到中转目录: ${o}.docx</span><br/>`}catch(i){throw t.value+=`<span class="log-item error">方式三保存失败: ${i.message}</span><br/>`,i}},Ee=n=>{const i=window.Application.Documents.Add();i.Content.Paste(),i.SaveAs2(`${w.value}\\${n}`,12,"","",!1),D().ActiveWindow.Activate()},Ue=async n=>{try{t.value+=`<span class="log-item info">开始生成文档: ${n}</span><br/>`;let i="method2";try{const r=await Q.getSaveMethod();if(r.success&&r.saveMethod){i=r.saveMethod;const d=i==="method1"?"方式一":i==="method2"?"方式二":"方式三";t.value+=`<span class="log-item info">使用保存方式: ${d}</span><br/>`}}catch(r){t.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式二: ${r.message}</span><br/>`}i==="method1"?(K(n),t.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):i==="method2"?(ve(n),t.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${w.value}\\${n}.docx</span><br/>`):i==="method3"?(me(n),t.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):i==="method4"&&(Ee(n),t.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${w.value}\\${n}.docx</span><br/>`),Q.associateFileWithClient(`${n}.docx`).then(r=>{r.success?t.value+=`<span class="log-item info">文件 ${n}.docx 已关联到当前客户端</span><br/>`:t.value+=`<span class="log-item warning">关联文件失败: ${r.message||"未知错误"}</span><br/>`}).catch(r=>{t.value+=`<span class="log-item warning">关联文件时出错: ${r.message}</span><br/>`})}catch(i){t.value+=`<span class="log-item error">保存文件失败: ${i.message}</span><br/>`}},Ae=pe(null),Oe=gt([]),ke=new Set,Se=n=>!n||typeof n!="string"?"":n.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),Me=async n=>{try{const i=n.slice(T.length);if(i.trim()){const r=Q.getClientId();if(!r){console.warn("无法获取客户端ID，跳过日志同步");return}const d=Se(i);if(!d.trim()){T=n;return}await Q.sendRequest("logger","syncLog",{content:d,timestamp:new Date().toISOString(),clientId:r}),T=n}}catch(i){console.error("同步日志到服务端失败:",i)}},Le=()=>{Q.connect().then(()=>{oe()}).catch(i=>{t.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${i.message}</span><br/>`});const n=()=>{t.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const i=[];for(const r in a)if(a.hasOwnProperty(r)){const d=a[r];(d.status===0||d.status===1)&&!d.terminated&&(i.includes(r)||i.push(r))}if(i.length>0){let r=!1;try{const d=D();if(d){const v=`taskpane_id_${d.DocID}`,p=window.Application.PluginStorage.getItem(v);if(p){const O=window.Application.GetTaskPane(p);O&&(r=O.Visible)}}}catch(d){t.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${d.message}</span><br/>`,r=!0}setTimeout(()=>{if(r){const d=`检测到 ${i.length} 个未完成的任务，是否继续？`;re(d).then(o=>{o?(t.value+=`<span class="log-item info">用户选择继续 ${i.length} 个进行中的任务 (by taskId)...</span><br/>`,Q.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:i}).then(v=>{v&&v.success?t.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${v.message||""}</span><br/>`:t.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(v==null?void 0:v.message)||"未知错误"}</span><br/>`}).catch(v=>{t.value+=`<span class="log-item error">请求恢复任务出错: ${v.message}</span><br/>`})):(t.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',i.forEach(v=>{k(v)}),t.value+=`<span class="log-item success">${i.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(o=>{t.value+=`<span class="log-item error">弹窗错误: ${o.message}，默认停止任务（保留控件）</span><br/>`,i.forEach(v=>{k(v)})})}else t.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};Q.setConnectionSuccessHandler(n),Q.isConnected&&(t.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',n()),Q.addEventListener("connection",i=>{i.status==="disconnected"&&(t.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${i.reason||"未知"}, 代码: ${i.code||"N/A"}，将自动重连</span><br/>`)}),Q.addEventListener("watcher",i=>{var r,d;if(Oe.push(i),i.type,i.eventType==="uploadSuccess"){const o=(r=i.data)==null?void 0:r.file,v=o==null?void 0:o.replace(/\.docx$/,""),p=`${i.eventType}_${o}_${Date.now()}`;if(ke.has(p)){t.value+=`<span class="log-item warning">忽略重复的上传事件: ${o}</span><br/>`;return}if(ke.add(p),ke.size>100){const M=ke.values();ke.delete(M.next().value)}const O=v&&((d=a[v])==null?void 0:d.wordType);Fe(i,O)}else i.eventType&&Fe(i,null)}),Q.addEventListener("urlMonitor",i=>{Oe.push(i),i.type==="urlMonitor"&&(t.value+=`<span class="log-item info">收到URL监控事件: ${i.eventType||i.action}</span><br/>`),i.eventType&&Fe(i,null)}),Q.addEventListener("health",i=>{}),Q.addEventListener("error",i=>{const r=i.error||"未知错误";t.value+=`<span class="log-item error">WebSocket错误: ${r}</span><br/>`,console.error("WebSocket错误:",i)})},X=gt({}),Ie=async(n,i,r=!1,d=5e3,o={})=>{try{t.value+=`<span class="log-item info">开始监控URL: ${n}</span><br/>`;const v=await Q.startUrlMonitoring(n,d,{downloadOnSuccess:o.downloadOnSuccess!==void 0?o.downloadOnSuccess:!0,appKey:o.appKey,filename:o.filename,taskId:i}),p=v.success||(v==null?void 0:v.success),O=v.urlId||(v==null?void 0:v.urlId);if(p&&O){X[i]={urlId:O,url:n,isResultUrl:r,startTime:Date.now()},t.value+=`<span class="log-item success">URL监控已启动，ID: ${O}</span><br/>`;try{await Q.startUrlChecking(O)}catch{}return O}else throw new Error("服务器返回失败")}catch(v){return t.value+=`<span class="log-item error">启动URL监控失败: ${v.message}</span><br/>`,null}},ye=async n=>{if(!n)return t.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(X).forEach(r=>{X[r].urlId===n&&delete X[r]}),t.value+=`<span class="log-item info">正在停止URL监控: ${n}</span><br/>`;const i=await St(async()=>await Q.stopUrlMonitoring(n),"delete",`${$t()}/api/url/monitor/${n}`);return i&&(i.success||i!=null&&i.success)?(t.value+=`<span class="log-item success">已停止URL监控: ${n}</span><br/>`,!0):(t.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(i){t.value+=`<span class="log-item warning">停止URL监控API调用失败: ${i.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await St(async()=>await Q.stopUrlMonitoring(n),"delete",`${$t()}/api/url/monitor/${n}`)}catch{}},1e3)}catch{}return!0}},at=async()=>{try{const n=await St(async()=>await Q.getUrlMonitorStatus(),"get",`${$t()}/api/url/status`);return n.data||n}catch(n){return t.value+=`<span class="log-item error">获取URL监控状态失败: ${n.message}</span><br/>`,[]}},Xe=async n=>{try{return await Q.forceUrlCheck(n)}catch{return!1}},Fe=async(n,i)=>{var r;if(n.eventType==="uploadSuccess"){const d=n.data.file,o=d.replace(/\.docx$/,"");if(a[o]){if(a[o].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${o.substring(0,8)} 的上传通知</span><br/>`;return}if(a[o].uploadSuccess){t.value+=`<span class="log-item warning">任务 ${o.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}t.value+=`<span class="log-item success">收到文件 ${d} 上传成功通知</span><br/>`,t.value+=`<span class="log-item info">使用 wordType: ${i||a[o].wordType||"wps-analysis"}</span><br/>`,a[o].status=1,a[o].uploadSuccess=!0,await ge(o,i||a[o].wordType||"wps-analysis")}}else if(n.eventType==="encryptedFileError"){const d=n.data.file,o=d.replace(/\.docx$/,"");if(a[o]){t.value+=`<span class="log-item error">文件加密错误: ${d}</span><br/>`,t.value+=`<span class="log-item error">${n.data.message}</span><br/>`,a[o].status=-1,a[o].errorMessage=n.data.message||"文件已加密，无法处理";try{const v=D();if(v&&v.ContentControls)for(let p=1;p<=v.ContentControls.Count;p++)try{const O=v.ContentControls.Item(p);if(O&&O.Title&&(O.Title===`任务_${o}`||O.Title===`任务增强_${o}`||O.Title===`校对_${o}`)){const M=O.Title===`任务增强_${o}`||a[o].isEnhanced,A=O.Title===`校对_${o}`||a[o].isCheckTask;A?O.Title=`异常校对_${o}`:M?O.Title=`异常增强_${o}`:O.Title=`异常_${o}`;const R=A?"校对":M?"增强":"普通";t.value+=`<span class="log-item info">已将${R}任务${o.substring(0,8)}控件标记为异常（文件加密）</span><br/>`;break}}catch{continue}}catch(v){t.value+=`<span class="log-item warning">更新控件标题失败: ${v.message}</span><br/>`}xe(o)}}else if(n.eventType!=="urlMonitorUpdate")if(n.eventType==="urlMonitorStopped"){const{urlId:d,url:o,taskId:v,downloadedPath:p}=n.data,O=Object.keys(X).filter(M=>X[M].urlId===d);O.length>0&&O.forEach(M=>{p&&a[M]&&(a[M].resultFile=p,a[M].resultDownloaded=!0),delete X[M]})}else if(n.eventType==="urlFileDownloaded"){const{urlId:d,url:o,filePath:v,taskId:p}=n.data;if(!Object.values(X).some(M=>M.urlId===d)&&p&&((r=a[p])!=null&&r.terminated))return;if(p&&a[p]&&a[p].terminated){if(t.value+=`<span class="log-item info">忽略已终止任务 ${d} 的文件下载通知</span><br/>`,d)try{await Q.stopUrlMonitoring(d),X[p]&&delete X[p]}catch{}return}if(p&&a[p]){if(t.value+=`<span class="log-item success">收到结果文件通知: ${v}</span><br/>`,a[p].isCheckTask&&v.endsWith(".wps.json")){t.value+=`<span class="log-item info">处理校对任务JSON文件: ${v}</span><br/>`;try{const A=window.Application.FileSystem.ReadFile(v),R=JSON.parse(A);if(await ce(R,p)){a[p].status=2,t.value+=`<span class="log-item success">校对任务${p.substring(0,8)}已完成批注处理</span><br/>`;const Z=x(a[p].startTime);t.value+=`<span class="log-item success">校对任务${p.substring(0,8)}完成，总耗时${Z}</span><br/>`}}catch(M){t.value+=`<span class="log-item error">处理校对JSON文件失败: ${M.message}</span><br/>`,M.message.includes("Unsupported protocol")&&(t.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${v}</span><br/>`),a[p].status=-1,a[p].errorMessage=`JSON处理失败: ${M.message}`,t.value+=`<span class="log-item error">校对任务${p.substring(0,8)}处理失败</span><br/>`}}else{a[p].resultFile=v,a[p].resultDownloaded=!0;const M=x(a[p].startTime);t.value+=`<span class="log-item success">任务${p.substring(0,8)}完成，总耗时${M}</span><br/>`,await Ve(p)}X[p]&&X[p].urlId&&(ye(X[p].urlId),t.value+='<span class="log-item info">已停止URL监控</span><br/>',delete X[p])}else if(d){t.value+=`<span class="log-item info">URL文件已下载: ${v}</span><br/>`;const M=Object.keys(X).filter(A=>{var R;return X[A].urlId===d&&!((R=a[A])!=null&&R.terminated)});M.length>0&&M.forEach(async A=>{if(a[A]){if(t.value+=`<span class="log-item info">关联到任务: ${A.substring(0,8)}</span><br/>`,a[A].resultFile=v,a[A].resultDownloaded=!0,a[A].isCheckTask&&v.endsWith(".wps.json"))try{const q=window.Application.FileSystem.ReadFile(v),Z=JSON.parse(q);if(await ce(Z,A)&&a[A].status===1){a[A].status=2,t.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const fe=x(a[A].startTime);t.value+=`<span class="log-item success">校对任务${A.substring(0,8)}完成，总耗时${fe}</span><br/>`}}catch(R){t.value+=`<span class="log-item error">处理校对JSON失败: ${R.message}</span><br/>`,a[A].status===1&&(a[A].status=-1,a[A].errorMessage=`JSON处理失败: ${R.message}`,t.value+=`<span class="log-item error">校对任务${A.substring(0,8)}处理失败</span><br/>`)}else if(a[A].status===1){a[A].status=2;const R=x(a[A].startTime);t.value+=`<span class="log-item success">任务${A.substring(0,8)}完成，总耗时${R}</span><br/>`}}})}}else if(n.eventType==="urlFileDownloadError"){const{urlId:d,url:o,error:v,taskId:p}=n.data;if(p&&a[p]&&a[p].terminated){t.value+=`<span class="log-item info">忽略已终止任务 ${p.substring(0,8)} 的下载失败通知</span><br/>`;return}if(t.value+=`<span class="log-item error">下载URL文件失败: ${v}</span><br/>`,p&&a[p]){a[p].status=-1,a[p].errorMessage=`下载失败: ${v}`;try{const O=D();if(O&&O.ContentControls)for(let M=1;M<=O.ContentControls.Count;M++)try{const A=O.ContentControls.Item(M);if(A&&A.Title&&(A.Title===`任务_${p}`||A.Title===`任务增强_${p}`||A.Title===`校对_${p}`)){const R=A.Title===`任务增强_${p}`||a[p].isEnhanced,q=A.Title===`校对_${p}`||a[p].isCheckTask;q?A.Title=`异常校对_${p}`:R?A.Title=`异常增强_${p}`:A.Title=`异常_${p}`;const Z=q?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${Z}任务${p.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(O){t.value+=`<span class="log-item warning">更新控件标题失败: ${O.message}</span><br/>`}xe(p),X[p]&&delete X[p]}if(d)try{t.value+=`<span class="log-item info">尝试停止URL监控: ${d}</span><br/>`,await St(async()=>await Q.stopUrlMonitoring(d),"delete",`${$t()}/api/url/monitor/${d}`)}catch{}}else n.eventType==="resumeUrlMonitors"&&console.log(n.data)},ge=async(n,i="wps-analysis")=>{try{if(!C.appKey)throw new Error("未初始化appKey信息");const r=await u(),d=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${n}.docx`,o=await Rt.post(bs()+"/api/open/ticket/v1/ai_comment/create",{access_token:r,data:{app_key:C.appKey,subject:V.value,stage:I.value,file_name:`${n}`,word_url:d,word_type:i,is_ai_auto:!0,is_ai_edit:!0,create_user_id:C.userId,create_username:C.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),v=o.data.data.ticket_id;if(!v)return a[n]&&(a[n].status=-1,a[n].errorMessage="无法获取ticket_id",xe(n)),!1;t.value+=`<span class="log-item info">获取到ticket_id: ${v}，开始监控结果文件</span><br/>`;let p,O;i==="wps-check"?(p=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${C.appKey}/ai/${v}.wps.json`,O=`${v}.wps.json`,t.value+=`<span class="log-item info">校对任务，监控JSON文件: ${O}</span><br/>`):(p=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${v}.wps.docx`,O=`${v}.wps.docx`,t.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${O}</span><br/>`);const M={downloadOnSuccess:!0,filename:O,appKey:C.appKey,ticketId:v,taskId:n},A=await Ie(p,n,!0,3e3,M);return a[n]&&(a[n].ticketId=v,a[n].resultUrl=p,a[n].urlMonitorId=A,a[n].status=1),o.data}catch(r){return t.value+=`<span class="log-item error">API调用失败: ${r.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`API调用失败: ${r.message}`,xe(n)),!1}},dt=async(n,i="wps-analysis")=>new Promise((r,d)=>{try{t.value+=`<span class="log-item info">监控目录: ${w.value}</span><br/>`,t.value+=`<span class="log-item info">使用文档类型: ${i}</span><br/>`,a[n]&&(a[n].status=0,a[n].wordType=i,a[n].uploadSuccess=!1),t.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const o=1e3,v=10;let p=0;const O=setInterval(()=>{if(p++,a[n]&&a[n].uploadSuccess){clearInterval(O),r(!0);return}if(a[n]&&a[n].status===-1&&(clearInterval(O),t.value+=`<span class="log-item error">任务${n.substring(0,8)}已异常，停止等待上传完成</span><br/>`,d(new Error(a[n].errorMessage||"任务已异常"))),p>=v){clearInterval(O),t.value+='<span class="log-item warning">等待上传完成超时，请检查文件状态</span><br/>',xe(n);return}},o)}catch(o){t.value+=`<span class="log-item error">任务处理异常: ${o.message}</span><br/>`,a[n]&&(a[n].status=-1,a[n].errorMessage=`任务处理异常: ${o.message}`),xe(n),d(o)}}),Ge=async()=>{var r;t.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const n=window.wps,i=n.ActiveDocument;if(l.value=i.DocID,g.value=n.ActiveWindow.Index,i!=null&&i.ContentControls)for(let d=1;d<=i.ContentControls.Count;d++){const o=i.ContentControls.Item(d);if(o&&o.Title){let v=null,p=1,O=!1,M=!1;if(o.Title.startsWith("任务增强_")?(v=o.Title.substring(5),p=1,O=!0):o.Title.startsWith("任务_")?(v=o.Title.substring(3),p=1):o.Title.startsWith("校对_")?(v=o.Title.substring(3),p=1,M=!0):o.Title.startsWith("已完成增强_")?(v=o.Title.substring(6),p=2,O=!0):o.Title.startsWith("已完成校对_")?(v=o.Title.substring(6),p=2,M=!0):o.Title.startsWith("已完成_")?(v=o.Title.substring(4),p=2):o.Title.startsWith("异常增强_")?(v=o.Title.substring(5),p=-1,O=!0):o.Title.startsWith("异常校对_")?(v=o.Title.substring(5),p=-1,M=!0):o.Title.startsWith("异常_")?(v=o.Title.substring(3),p=-1):o.Title.startsWith("已停止增强_")?(v=o.Title.substring(6),p=4,O=!0):o.Title.startsWith("已停止校对_")?(v=o.Title.substring(6),p=4,M=!0):o.Title.startsWith("已停止_")&&(v=o.Title.substring(4),p=4),v&&!a[v]){let A="";try{A=((r=o.Range)==null?void 0:r.Text)||""}catch{}let R=Date.now()-24*60*60*1e3;try{if(v.length===24){const te=v.substring(0,8),fe=parseInt(te,16);!isNaN(fe)&&fe>0&&fe<2147483647&&(R=fe*1e3)}else{const te=Date.now()-864e5;p===2?R=te-60*60*1e3:p===-1?R=te-30*60*1e3:p===4?R=te-45*60*1e3:R=te}}catch{}a[v]={status:p,startTime:R,contentControlId:o.ID,isEnhanced:O,isCheckTask:M,selectedText:A};const q=p===1?"进行中":p===2?"已完成":p===-1?"异常":p===4?"已停止":"未知",Z=M?"校对":O?"增强":"普通";t.value+=`<span class="log-item info">发现已有${Z}任务: ${v.substring(0,8)}, 状态: ${q}</span><br/>`}}}},xe=(n,i=!1)=>{try{if(!a[n]){t.value+=`<span class="log-item warning">找不到任务${n.substring(0,8)}的记录，无法清除控件</span><br/>`;return}i&&a[n].status===1&&(a[n].status=3,a[n].errorMessage="用户主动释放");const r=D();if(!r){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let d=!1;const o=a[n].isEnhanced;if(r.ContentControls&&r.ContentControls.Count>0)for(let v=r.ContentControls.Count;v>=1;v--)try{const p=r.ContentControls.Item(v);p&&p.Title&&p.Title.includes(n)&&(p.LockContents=!1,p.Delete(!1),d=!0,console.log(p.Title),t.value+=`<span class="log-item success">已解锁并删除${o?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`)}catch(p){t.value+=`<span class="log-item warning">访问第${v}个控件时出错: ${p.message}</span><br/>`;continue}d?a[n].placeholderRemoved=!0:t.value+=`<span class="log-item warning">未能找到或删除${o?"增强":"普通"}任务${n.substring(0,8)}的内容控件</span><br/>`}catch(r){t.value+=`<span class="log-item error">删除内容控件失败: ${r.message}</span><br/>`}},rt=n=>{var i;try{const r=window.wps,d=D();if(!d||!d.ContentControls){t.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const o=(i=a[n])==null?void 0:i.isEnhanced;let v=null;for(let O=1;O<=d.ContentControls.Count;O++)try{const M=d.ContentControls.Item(O);if(M&&M.Title&&M.Title.includes(n)){v=M;break}}catch{continue}if(!v){const O=a[n];O&&(O.status===2||O.status===-1)?t.value+=`<span class="log-item info">任务ID: ${n.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:t.value+=`<span class="log-item error">找不到任务ID: ${n.substring(0,8)} 对应的内容控件</span><br/>`;return}v.Range.Select();const p=r.Windows.Item(g.value);p&&p.Selection&&p.Selection.Range&&p.ScrollIntoView(p.Selection.Range,!0),t.value+=`<span class="log-item success">已跳转到${o?"增强":"普通"}任务ID: ${n.substring(0,8)} 位置</span><br/>`}catch(r){t.value+=`<span class="log-item error">跳转到任务控件失败: ${r.message}</span><br/>`}},Ve=async n=>{var p,O,M;const i=D(),r=a[n];if(!r){t.value+=`<span class="log-item error">找不到ID为${n.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(r.terminated||r.status!==1)return;if(r.documentInserted){t.value+=`<span class="log-item info">任务${n.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const d=D();let o=null;if(d&&d.ContentControls)for(let A=1;A<=d.ContentControls.Count;A++){const R=d.ContentControls.Item(A);if(R&&R.Title&&R.Title.includes(n)){o=R;break}}if(!o){if(t.value+=`<span class="log-item error">找不到任务${n.substring(0,8)}的内容控件</span><br/>`,r.errorMessage&&r.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${r.errorMessage}</span><br/>`;return}return}if(r.errorMessage&&r.status===1){a[n].status=-1,t.value+=`<span class="log-item error">任务${n.substring(0,8)}失败: ${r.errorMessage}</span><br/>`,xe(n);return}if(!r.resultFile)return;a[n].documentInserted=!0;const v=o.Range;o.LockContents=!1;try{t.value+=`<span class="log-item info">正在自动插入结果文件${r.resultFile}...</span><br/>`;const A=i.Range(v.End,v.End);A.InsertParagraphAfter(),v.Text.includes("\v")&&A.InsertAfter("\v");let R=A.End;const q=(p=v.Tables)==null?void 0:p.Count;if(q){const he=v.Tables.Item(q);((O=he==null?void 0:he.Range)==null?void 0:O.End)>R&&(he.Range.InsertParagraphAfter(),R=(M=he==null?void 0:he.Range)==null?void 0:M.End)}i.Range(R,R).InsertFile(r.resultFile);for(let he=1;he<=d.ContentControls.Count;he++){const lt=d.ContentControls.Item(he);if(lt&&lt.Title&&(lt.Title===`任务_${n}`||lt.Title===`任务增强_${n}`)){o=lt;break}}const te=i.Range(o.Range.End-1,o.Range.End);te.Text.includes("\r")&&te.Delete(),a[n].status=2,X[n]&&X[n].urlId&&Q.sendRequest("urlMonitor","updateTaskStatus",{urlId:X[n].urlId,status:"completed",taskId:n}).then(he=>{t.value+=`<span class="log-item info">已通知服务端更新任务${n.substring(0,8)}状态为完成</span><br/>`}).catch(he=>{t.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${he.message}</span><br/>`});const qe=x(r.startTime);t.value+=`<span class="log-item success">任务${n.substring(0,8)}处理完成，总耗时${qe}</span><br/>`;const we=o.Title===`任务增强_${n}`||r.isEnhanced,Ce=o.Title===`校对_${n}`||r.isCheckTask;if(o){Ce?o.Title=`已完成校对_${n}`:we?o.Title=`已完成增强_${n}`:o.Title=`已完成_${n}`;const he=Ce?"校对":we?"增强":"普通";t.value+=`<span class="log-item info">已将${he}任务${n.substring(0,8)}控件标记为已完成</span><br/>`}}catch(A){a[n].documentInserted=!1,a[n].status=-1;const R=o.Title===`任务增强_${n}`||r.isEnhanced,q=o.Title===`校对_${n}`||r.isCheckTask;if(o){q?o.Title=`异常校对_${n}`:R?o.Title=`异常增强_${n}`:o.Title=`异常_${n}`;const Z=q?"校对":R?"增强":"普通";t.value+=`<span class="log-item info">已将${Z}任务${n.substring(0,8)}控件标记为异常</span><br/>`}t.value+=`<span class="log-item error">插入文档失败: ${A.message}</span><br/>`}},_e=async()=>{const n=(d=1e3)=>new Promise(o=>{setTimeout(()=>o(),d)}),i=new Map,r=Object.keys(a).filter(d=>a[d].status===1&&!a[d].terminated);for(r.length?(r.forEach(d=>{i.has(d)||i.set(d,Date.now())}),await Promise.all(r.map(d=>Ve(d)))):t.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';;){await n(3e3);const d=Object.keys(a).filter(o=>a[o].status===1&&!a[o].terminated);d.forEach(o=>{i.has(o)||i.set(o,Date.now());const v=i.get(o);(Date.now()-v)/1e3/60>=5e4&&a[o]&&!a[o].terminated&&(a[o].terminated=!0,a[o].status=-1,t.value+=`<span class="log-item warning">任务 ${o} 执行超过5分钟，已自动终止</span><br/>`,i.delete(o))}),d.length&&await Promise.all(d.map(o=>Ve(o)))}},je=async(n="wps-analysis")=>{const i=y();if(i){const{primary:d,all:o}=i,{taskId:v,control:p,task:O,isEnhanced:M,overlapType:A}=d,R=o.filter(q=>q.task&&(q.task.status===0||q.task.status===1));if(R.length>0){const q=R.map(Z=>Z.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${q})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}t.value+=`<span class="log-item info">发现选区与${o.length}个已有任务重叠，重叠类型: ${A}</span><br/>`,W();try{for(const q of o){const{taskId:Z,control:te,task:fe,isEnhanced:qe}=q;t.value+=`<span class="log-item info">删除重叠的${qe?"增强":"普通"}任务 ${Z.substring(0,8)}，状态为 ${b((fe==null?void 0:fe.status)||0)}</span><br/>`,fe&&(fe.status=3,fe.terminated=!0,fe.errorMessage="用户重新创建任务时删除");try{te.LockContents=!1,te.Delete(!1),a[Z]&&(a[Z].placeholderRemoved=!0)}catch(we){t.value+=`<span class="log-item error">删除重叠任务控件失败: ${we.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${o.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(q){return H(),t.value+=`<span class="log-item error">删除重叠任务控件失败: ${q.message}</span><br/>`,Promise.reject(q)}H()}const r=ws().replace(/-/g,"").substring(0,8);return new Promise(async(d,o)=>{try{const v=window.wps,p=D(),O=p.ActiveWindow.Selection,M=O.Range,A=O.Text||"";if(s.value=A,_(),M){const R=M.Paragraphs,q=R.Item(1),Z=R.Item(R.Count),te=q.Range.Start,fe=Z.Range.End,qe=M.Start,we=M.End,Ce=p.Range(Math.min(te,qe),Math.max(fe,we));try{let he=p.ContentControls.Add(v.Enum.wdContentControlRichText,Ce);if(!he){if(console.log("创建内容控件失败"),he=p.ContentControls.Add(v.Enum.wdContentControlRichText),!he){t.value+='<span class="log-item error">创建内容控件失败</span><br/>',o(new Error("创建内容控件失败"));return}M.Cut(),he.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const lt=n==="wps-enhance_analysis";he.Title=lt?`任务增强_${r}`:`任务_${r}`,he.LockContents=!0,a[r]||(a[r]={}),a[r].contentControlId=he.ID,a[r].isEnhanced=lt,t.value+=`<span class="log-item info">已创建${lt?"增强":"普通"}内容控件并锁定选区</span><br/>`;const rn=he.Range.Text;t.value+=`<span class="log-item success">控件内容验证通过，长度: ${rn.length}</span><br/>`}catch(he){t.value+=`<span class="log-item error">创建内容控件失败: ${he.message}</span><br/>`,o(he);return}}a[r]={status:0,startTime:Date.now(),wordType:n,isEnhanced:n==="wps-enhance_analysis",selectedText:A},t.value+=`<span class="log-item success">创建${n==="wps-enhanced"?"增强":"普通"}任务: ${r}，类型: ${n}</span><br/>`,await Ue(r),a[r]&&(a[r].status=1);try{await dt(r,n)?d():(a[r]&&a[r].status===1&&(a[r].status=-1,a[r].errorMessage="API调用失败或超时",t.value+=`<span class="log-item error">任务${r.substring(0,8)}失败</span><br/>`,ie(r)),o(new Error("API调用失败或超时")))}catch(R){a[r]&&(a[r].status=-1,a[r].errorMessage=`执行错误: ${R.message}`,t.value+=`<span class="log-item error">任务${r.substring(0,8)}执行出错: ${R.message}</span><br/>`,ie(r)),o(R)}}catch(v){o(v)}})},Ze=async()=>{const n=y();if(n){const{primary:r,all:d}=n,{taskId:o,control:v,task:p,isEnhanced:O,overlapType:M}=r,A=d.filter(R=>R.task&&(R.task.status===0||R.task.status===1));if(A.length>0){const R=A.map(q=>q.taskId.substring(0,8)).join(", ");return t.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${R})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}t.value+=`<span class="log-item info">发现选区与${d.length}个已有任务重叠，重叠类型: ${M}</span><br/>`,W();try{for(const R of d){const{taskId:q,control:Z,task:te,isEnhanced:fe}=R;t.value+=`<span class="log-item info">删除重叠的校对任务 ${q.substring(0,8)}，状态为 ${b((te==null?void 0:te.status)||0)}</span><br/>`,te&&(te.status=3,te.terminated=!0,te.errorMessage="用户重新创建校对任务时删除");try{Z.LockContents=!1,Z.Delete(!1),a[q]&&(a[q].placeholderRemoved=!0)}catch(qe){t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${qe.message}</span><br/>`}}t.value+=`<span class="log-item success">已删除${d.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(R){return H(),t.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${R.message}</span><br/>`,Promise.reject(R)}H()}const i=ws().replace(/-/g,"").substring(0,8);return new Promise(async(r,d)=>{try{const o=window.wps,v=D(),p=v.ActiveWindow.Selection,O=p.Range,M=p.Text||"";if(s.value=M,_(),O){const A=O.Paragraphs,R=A.Item(1),q=A.Item(A.Count),Z=R.Range.Start,te=q.Range.End,fe=O.Start,qe=O.End,we=v.Range(Math.min(Z,fe),Math.max(te,qe));try{let Ce=v.ContentControls.Add(o.Enum.wdContentControlRichText,we);if(!Ce){if(console.log("创建校对内容控件失败"),Ce=v.ContentControls.Add(o.Enum.wdContentControlRichText),!Ce){t.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',d(new Error("创建校对内容控件失败"));return}O.Cut(),Ce.Range.Paste()}t.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',Ce.Title=`校对_${i}`,Ce.LockContents=!0,a[i]||(a[i]={}),a[i].contentControlId=Ce.ID,a[i].isCheckTask=!0,t.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const he=Ce.Range.Text;t.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${he.length}</span><br/>`}catch(Ce){t.value+=`<span class="log-item error">创建校对内容控件失败: ${Ce.message}</span><br/>`,d(Ce);return}}a[i]={status:0,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:M},t.value+=`<span class="log-item success">创建校对任务: ${i}，类型: wps-check</span><br/>`,await Ue(i),a[i]&&(a[i].status=1);try{await dt(i,"wps-check")?r():(a[i]&&a[i].status===1&&(a[i].status=-1,a[i].errorMessage="校对API调用失败或超时",t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}失败</span><br/>`,ie(i)),d(new Error("校对API调用失败或超时")))}catch(A){a[i]&&(a[i].status=-1,a[i].errorMessage=`校对执行错误: ${A.message}`,t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}执行出错: ${A.message}</span><br/>`,ie(i)),d(A)}}catch(o){d(o)}})},He=async()=>{},Qe=()=>it()>0?"status-error":ot()>0?"status-running":et()>0?"status-completed":"",ot=()=>Object.values(a).filter(n=>n.status===0||n.status===1).length,et=()=>Object.values(a).filter(n=>n.status===2).length,pt=()=>Object.values(a).filter(n=>n.status===2||n.status===4).length,it=()=>Object.values(a).filter(n=>n.status===-1).length,Ot=()=>{try{t.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const n=window.wps,i=D();if(!i){t.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let r=0;if(Object.keys(a).forEach(d=>{try{a[d].status===1&&(a[d].status=3,a[d].terminated=!0,a[d].errorMessage="强制清除"),xe(d),r++}catch(o){t.value+=`<span class="log-item warning">清除任务${d.substring(0,8)}失败: ${o.message}</span><br/>`}}),i.ContentControls&&i.ContentControls.Count>0)for(let d=i.ContentControls.Count;d>=1;d--)try{const o=i.ContentControls.Item(d);if(o&&o.Title&&(o.Title.startsWith("任务_")||o.Title.startsWith("任务增强_")||o.Title.startsWith("已完成_")||o.Title.startsWith("已完成增强_")||o.Title.startsWith("异常_")||o.Title.startsWith("异常增强_")||o.Title.startsWith("已停止_")||o.Title.startsWith("已停止增强_")))try{o.LockContents=!1,o.Delete(!1);let v;o.Title.startsWith("任务增强_")?v=o.Title.substring(5):o.Title.startsWith("任务_")?v=o.Title.substring(3):o.Title.startsWith("已完成增强_")?v=o.Title.substring(6):o.Title.startsWith("已完成_")?v=o.Title.substring(4):o.Title.startsWith("异常增强_")?v=o.Title.substring(5):o.Title.startsWith("异常_")?v=o.Title.substring(3):o.Title.startsWith("已停止增强_")?v=o.Title.substring(6):o.Title.startsWith("已停止_")&&(v=o.Title.substring(4)),a[v]?(a[v].placeholderRemoved=!0,a[v].status=3):a[v]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},r++,t.value+=`<span class="log-item success">已删除任务${v.substring(0,8)}的内容控件</span><br/>`}catch(v){t.value+=`<span class="log-item error">删除控件失败: ${v.message}</span><br/>`}}catch(o){t.value+=`<span class="log-item warning">访问控件时出错: ${o.message}</span><br/>`}r>0?t.value+=`<span class="log-item success">已清除${r}个任务控件</span><br/>`:t.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(n){t.value+=`<span class="log-item error">强制清除任务控件时出错: ${n.message}</span><br/>`}},kt=async()=>{try{const n=Object.values(X).map(i=>i.urlId);if(n.length>0){t.value+=`<span class="log-item info">清理${n.length}个URL监控任务...</span><br/>`;for(const i of n)await ye(i)}}catch(n){t.value+=`<span class="log-item error">清理URL监控任务失败: ${n.message}</span><br/>`}},Tt=()=>{Fs(async()=>{try{const i=window.Application.PluginStorage.getItem("user_info");if(!i)throw new Error("未找到用户信息");const r=JSON.parse(i);if(!r.orgs||!r.orgs[0])throw new Error("未找到组织信息");C.appKey=r.appKey,C.userName=r.nickname,C.userId=r.userId,C.appSecret=r.appSecret}catch(i){t.value+=`<span class="log-item error">初始化appKey失败: ${i.message}</span><br/>`}await oe(),Jt([V,I],async()=>{await f()},{immediate:!1}),await S();const n=Ye.onVersionChange(()=>{const i=se();ne.splice(0,ne.length,...i),Ye.isSeniorEdition()&&I.value==="junior"?I.value="senior":!Ye.isSeniorEdition()&&I.value==="senior"&&(I.value="junior"),t.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${Ye.isSeniorEdition()?"高中":"初中"}</span><br/>`});return t.value='<span class="log-item">已加载任务窗格...</span><br/>',U(),await Ge(),Le(),Ct(),_e(),window.addEventListener("beforeunload",kt),()=>{E&&clearTimeout(E),n&&n()}}),Jt(t,n=>{E&&clearTimeout(E),E=setTimeout(()=>{Me(n)},10)},{immediate:!1})},Ct=()=>{Q.addEventListener("config",n=>{n.eventType==="subjectAndStageChanged"&&n.data&&(n.data.subject!==V.value&&(V.value=n.data.subject,t.value+=`<span class="log-item info">学科设置已从服务器同步: ${V.value}</span><br/>`),n.data.stage!==I.value&&(I.value=n.data.stage,t.value+=`<span class="log-item info">年级设置已从服务器同步: ${I.value}</span><br/>`))})},ft=pe(!1),y=()=>{try{const n=D(),i=n.ActiveWindow.Selection;if(!i||!n||!n.ContentControls)return null;const r=i.Range,d=[];for(let o=1;o<=n.ContentControls.Count;o++)try{const v=n.ContentControls.Item(o);if(!v)continue;const p=(v.Title||"").trim(),O=v.Range;if(r.Start<O.End&&r.End>O.Start){let M=null,A=!1,R=!1;if(!p)R=!0,M=`empty_${v.ID||Date.now()}`;else if(p.startsWith("任务_")||p.startsWith("任务增强_")||p.startsWith("校对_")||p.startsWith("已完成_")||p.startsWith("已完成增强_")||p.startsWith("已完成校对_")||p.startsWith("异常_")||p.startsWith("异常增强_")||p.startsWith("异常校对_")||p.startsWith("已停止_")||p.startsWith("已停止增强_")||p.startsWith("已停止校对_"))p.startsWith("任务增强_")?(M=p.substring(5),A=!0):p.startsWith("任务_")||p.startsWith("校对_")?M=p.substring(3):p.startsWith("已完成增强_")?(M=p.substring(6),A=!0):p.startsWith("已完成校对_")?M=p.substring(6):p.startsWith("已完成_")?M=p.substring(4):p.startsWith("异常增强_")?(M=p.substring(5),A=!0):p.startsWith("异常校对_")?M=p.substring(5):p.startsWith("异常_")?M=p.substring(3):p.startsWith("已停止增强_")?(M=p.substring(6),A=!0):p.startsWith("已停止校对_")?M=p.substring(6):p.startsWith("已停止_")&&(M=p.substring(4));else continue;if(M){let q;r.Start>=O.Start&&r.End<=O.End?q="completely_within":r.Start<=O.Start&&r.End>=O.End?q="completely_contains":q="partial_overlap",d.push({taskId:M,control:v,task:R?null:a[M]||null,isEnhanced:A,isEmptyTitle:R,overlapType:q,controlRange:{start:O.Start,end:O.End},selectionRange:{start:r.Start,end:r.End}})}}}catch{continue}return d.length===0?null:{primary:d[0],all:d}}catch(n){return t.value+=`<span class="log-item error">检查选区位置出错: ${n.message}</span><br/>`,null}},W=()=>{ft.value=!0},H=()=>{ft.value=!1},ie=async(n,i=!1)=>{W();try{await xe(n,i)}finally{H()}},re=n=>new Promise((i,r)=>{P.show=!0,P.message=n,P.resolveCallback=i,P.rejectCallback=r}),be=n=>{P.show=!1,n&&P.resolveCallback?P.resolveCallback(!0):P.resolveCallback&&P.resolveCallback(!1),P.resolveCallback=null,P.rejectCallback=null},Te=(n,i,r="error")=>{L.show=!0,L.title=n,L.message=i,L.type=r},ze=()=>{L.show=!1,L.title="",L.message="",L.type="error"},S=async()=>{try{t.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const n=await Q.getSubjectAndStage();n.success&&n.data?(n.data.subject&&(V.value=n.data.subject),n.data.stage&&(I.value=n.data.stage),t.value+=`<span class="log-item success">已从服务器加载学科(${V.value})和年级(${I.value})设置</span><br/>`):t.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(n){console.error("从服务器加载学科和年级设置失败:",n),t.value+=`<span class="log-item error">从服务器加载设置失败: ${n.message}</span><br/>`}},f=async()=>{try{if(V.value||I.value){t.value+=`<span class="log-item info">正在保存学科(${V.value})和年级(${I.value})设置到服务器...</span><br/>`;const n=await Q.setSubjectAndStage(V.value,I.value);n&&n.success?t.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':t.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(n==null?void 0:n.message)||"未知错误"}</span><br/>`}}catch(n){console.error("保存学科和年级到服务器失败:",n),t.value+=`<span class="log-item error">保存设置到服务器失败: ${n.message}</span><br/>`}},J=async()=>{try{B.value=2,Y.value=B.value===2,Y.value?t.value+=`<span class="log-item info">校对功能已启用（企业ID: ${B.value}）</span><br/>`:t.value+=`<span class="log-item info">校对功能不可用（企业ID: ${B.value}）</span><br/>`}catch(n){console.error("检查企业ID失败:",n),t.value+=`<span class="log-item error">检查企业ID失败: ${n.message}</span><br/>`,Y.value=!1}},G=(n,i)=>{try{const r=D();if(!r||!n)return!1;const d=r.Comments.Add(n,i);return!0}catch(r){return t.value+=`<span class="log-item error">为范围添加批注失败: ${r.message}</span><br/>`,!1}},ce=async(n,i)=>{try{if(!n||!Array.isArray(n))return t.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const r=D();let d=null,o=null;if(r&&r.ContentControls)for(let M=1;M<=r.ContentControls.Count;M++)try{const A=r.ContentControls.Item(M);if((A==null?void 0:A.Title)===`校对_${i}`){d=A,o=A.Range,t.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!o){t.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const M=a[i];if(M&&M.selectedText)try{const A=r.Range().Find;if(A.ClearFormatting(),A.Text=M.selectedText.substring(0,Math.min(M.selectedText.length,100)),A.Forward=!0,A.Wrap=1,A.Execute()){const R=A.Parent;if(M.selectedText.length>100){const q=R.Start+M.selectedText.length;o=r.Range(R.Start,Math.min(q,r.Range().End))}else o=R;t.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return t.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(A){return t.value+=`<span class="log-item error">查找原始控件范围失败: ${A.message}</span><br/>`,!1}else return t.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let v=0,p=0,O=[];for(const M of n){if(!M.mode1||!Array.isArray(M.mode1)){t.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const A of M.mode1){if(!A.error_info||!Array.isArray(A.error_info)){t.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const R=A.quest_html||"",q=A.quest_type||"";t.value+=`<span class="log-item info">处理${q}题目，发现${A.error_info.length}个错误信息</span><br/>`;for(const Z of A.error_info)try{let te="";if(Z.error_info&&(te+=`【错误类型】${Z.error_info}`),Z.fix_info&&(te+=`\r
【修正建议】${Z.fix_info}`),Z.keywords&&Z.keywords.trim()){let fe=d?d.Range:o;d.LockContents=!1,de(te,Z.keywords.trim(),-1,d.Range)?(v++,t.value+=`<span class="log-item success">已为关键词"${Z.keywords.trim()}"添加批注: ${te}</span><br/>`):(O.push({comment:te,keyword:Z.keywords.trim()}),t.value+=`<span class="log-item warning">关键词"${Z.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else O.push({comment:te,keyword:null})}catch(te){p++,t.value+=`<span class="log-item error">处理单个错误信息失败: ${te.message}</span><br/>`}}}if(O.length>0){t.value+=`<span class="log-item info">为整个控件范围添加${O.length}个批注</span><br/>`;for(const M of O)try{let A=M.comment,R=d?d.Range:o;d.LockContents=!1,G(d.Range,A)?(v++,t.value+=`<span class="log-item success">已为整个范围添加批注${M.keyword?`（关键词：${M.keyword}）`:""}</span><br/>`):p++}catch(A){p++,t.value+=`<span class="log-item error">为整个范围添加批注失败: ${A.message}</span><br/>`}}return v>0?(t.value+=`<span class="log-item success">校对任务${i.substring(0,8)}处理完成：成功添加${v}个批注</span><br/>`,p>0&&(t.value+=`<span class="log-item warning">校对任务${i.substring(0,8)}：${p}个批注添加失败</span><br/>`),d.Title=`已完成校对_${i}`,!0):(t.value+=`<span class="log-item error">校对任务${i.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(r){return t.value+=`<span class="log-item error">处理校对JSON数据失败: ${r.message}</span><br/>`,!1}};return{docName:e,selected:s,logger:t,map:a,watchedDir:w,subject:V,stage:I,subjectOptions:N,stageOptions:ne,fetchWatchedDir:oe,clearLog:ee,getCurrentDocument:D,checkDocumentFormat:le,getTaskStatusClass:h,getTaskStatusText:b,getElapsedTime:x,terminateTask:$,stopTaskWithoutRemovingControl:k,run1:je,run2:He,runCheck:Ze,getHeaderStatusClass:Qe,getRunningTasksCount:ot,getCompletedTasksCount:et,getReleasableTasksCount:pt,getErrorTasksCount:it,setupLifecycle:Tt,navigateToTaskControl:rt,forceCleanAllTasks:Ot,ws:Ae,wsMessages:Oe,initWebSocket:Le,handleWatcherEvent:Fe,urlMonitorTasks:X,monitorUrlForTask:Ie,stopUrlMonitoring:ye,getUrlMonitorStatus:at,forceUrlCheck:Xe,cleanupUrlMonitoringTasks:kt,tryRemoveTaskPlaceholder:xe,isLoading:ft,isSelectionInTaskControl:y,tryRemoveTaskPlaceholderWithLoading:ie,showConfirm:re,handleConfirm:be,confirmDialog:P,errorDialog:L,showErrorDialog:Te,hideErrorDialog:ze,loadSubjectAndStage:S,saveSubjectAndStage:f,enterpriseId:B,isCheckingVisible:Y,checkEnterpriseAndSetCheckingVisibility:J,processCheckingJson:ce}}const Cn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:Ye.getVersionConfig(),selectedEdition:Ye.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method2",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const e=new Date(this.status.startTime),t=new Date-e,a=Math.floor(t/(1e3*60*60)),l=Math.floor(t%(1e3*60*60)/(1e3*60)),g=Math.floor(t%(1e3*60)/1e3);return`${a}小时 ${l}分 ${g}秒`},userInfo(){var s,t;const e=(t=(s=window.Application)==null?void 0:s.PluginStorage)==null?void 0:t.getItem("user_info");return e?JSON.parse(e):null}},methods:{async fetchStatus(){try{const e=await Q.getWatcherStatus();e.success&&(this.status=e.data,e.data.addonConfigPath&&(this.addonConfigPath=e.data.addonConfigPath))}catch(e){console.error("获取状态失败:",e)}},async controlService(){try{const e=this.status.status==="running"?"stopWatcher":"startWatcher";await Q[e](),await this.fetchStatus()}catch(e){console.error("控制服务失败:",e)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await Q.stopWatcher(),await new Promise(s=>setTimeout(s,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const e=await Q.setWatchDirectory(this.newWatchDir);e.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await Q.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${e.message||"未知错误"}`)}catch(e){this.updateSuccess=!1,this.updateMessage=`发生错误: ${e.message}`,console.error("更新上传目录失败:",e)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const e=await Q.getDownloadPath();e.success&&e.downloadPath&&(this.downloadPath=e.downloadPath)}catch(e){console.error("获取下载路径失败:",e)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const e=await Q.setDownloadPath(this.newDownloadPath);e.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新下载路径失败:",e)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const e=await Q.getAddonConfigPath();e.success&&e.addonConfigPath&&(this.addonConfigPath=e.addonConfigPath)}catch(e){console.error("获取配置路径失败:",e)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const e=await Q.setAddonConfigPath(this.newAddonConfigPath);e.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${e.message||"未知错误"}`)}catch(e){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${e.message}`,console.error("更新配置路径失败:",e)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(e){e.eventType==="start"?(this.status.status="running",this.status.startTime=e.data.startTime):e.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=e.data.processedFiles):e.eventType==="uploadSuccess"?this.status.processedFiles=e.data.totalProcessed:e.eventType==="urlDownloadPathChanged"?(this.downloadPath=e.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${e.data.path}`):e.eventType==="addonConfigPathChanged"?(this.addonConfigPath=e.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${e.data.path}`):e.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...e,timestamp:new Date().toISOString()}),this.recentEvents.unshift(e),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(e){console.log("URL监控事件:",e)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await Ye.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(e){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${e.message}`,console.error("版本切换失败:",e)}finally{this.isSwitchingEdition=!1}}},formatTime(e){return new Date(e).toLocaleTimeString()},async handleLogout(){try{await ln()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(e){console.error("Logout error:",e),alert("退出登录失败，请稍后重试")}},getEventTypeText(e){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[e]||e},getEventMessage(e){switch(e.eventType){case"start":return`服务已启动，上传目录: ${e.data.watchDir}`;case"stop":return`服务已停止，处理了 ${e.data.processedFiles} 个文件`;case"filesFound":return`发现 ${e.data.count} 个新文件`;case"uploadStart":return`正在上传: ${e.data.file}`;case"uploadSuccess":return`文件 ${e.data.file} 上传成功`;case"uploadError":return`文件 ${e.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${e.data.file}`;case"dirCreated":return`创建目录: ${e.data.dir}`;case"urlMonitorUpdate":const s=e.data.status==="accessible"?"可访问":e.data.status==="inaccessible"?"不可访问":e.data.status==="error"?"检查出错":"未知状态";return`URL "${e.data.url.substring(0,40)}..." ${s}`;case"urlFileDownloaded":return`文件已下载: ${e.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${e.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${e.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${e.data.path}`;case"error":return`${e.data.error||"错误"}: ${e.data.message}`;default:if(e.data){const t=Object.keys(e.data)[0];return t?`${t}: ${e.data[t]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const e=await Q.sendRequest("config","getShowTooltip");e.success&&e.showTooltip!==void 0&&(this.showTooltip=e.showTooltip)}catch(e){console.error("获取Tooltip设置失败:",e)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const e=!!this.showTooltip,s=await Q.sendRequest("config","setShowTooltip",{showTooltip:e});s.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${s.message||"未知错误"}`,console.error("Tooltip设置更新失败:",s.message))}catch(e){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${e.message}`,console.error("更新Tooltip设置失败:",e)}},async fetchSaveMethod(){try{const e=await Q.sendRequest("config","getSaveMethod");e.success&&e.saveMethod&&(this.currentSaveMethod=e.saveMethod)}catch(e){console.error("获取保存方式设置失败:",e)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const e=await Q.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});e.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${e.message||"未知错误"}`,console.error("保存方式设置更新失败:",e.message))}catch(e){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${e.message}`,console.error("更新保存方式设置失败:",e)}},handleConfigEvent(e){console.log("配置事件:",e),e.eventType==="tooltipSettingChanged"&&(this.showTooltip=e.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${e.data.showTooltip?"显示":"隐藏"}`),e.eventType==="saveMethodChanged"&&(this.currentSaveMethod=e.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${e.data.saveMethod==="method1"?"方式一":e.data.saveMethod==="method2"?"方式二":e.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=Q.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=Q.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=Q.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=Ye.onVersionChange(e=>{this.versionConfig=Ye.getVersionConfig(),this.selectedEdition=Ye.getEdition()}),await Q.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},$n={class:"file-watcher"},Sn={class:"settings-modal"},_n={class:"modal-content"},En={class:"modal-header"},An={class:"version-tag"},Mn={key:0,class:"user-info"},Dn={key:1,class:"user-info inner-tag"},Pn={class:"header-actions"},On={class:"modal-body"},In={class:"status-section"},Rn={class:"status-item"},Un={key:0,class:"status-item"},Ln={class:"directory-section"},Fn={class:"directory-form"},jn={class:"radio-group"},Wn={class:"radio-item"},Bn={class:"radio-item"},Nn={class:"radio-item"},Vn={key:0,class:"radio-item"},Hn={key:1,class:"directory-section"},zn={class:"directory-form"},qn={class:"form-group"},Jn=["placeholder"],Yn=["disabled"],Kn={key:2,class:"directory-section"},Xn={class:"directory-form"},Gn={class:"form-group"},Zn=["placeholder"],Qn=["disabled"],ea={key:3,class:"directory-section"},ta={class:"directory-form"},sa={class:"form-group"},na=["placeholder"],aa=["disabled"],ra={key:4,class:"events-section"},oa={class:"events-list"},ia={class:"event-time"},la={class:"event-message"},ca={key:1,class:"modal-footer"};function ua(e,s,t,a,l,g){var T,E,w,C,P,L,V,I,B,Y,N,se,ne,oe,u,ee,D,le,de,h,b,x,k,$;return F(),j("div",$n,[c("div",Sn,[c("div",_n,[c("div",En,[c("h3",null,[cn(ae(l.versionConfig.shortName)+"设置 ",1),c("span",An,ae(l.versionConfig.appVersion),1),g.userInfo?(F(),j("span",Mn,"欢迎您，"+ae(g.userInfo.nickname),1)):z("",!0),((w=(E=(T=g.userInfo)==null?void 0:T.orgs)==null?void 0:E[0])==null?void 0:w.orgId)===2?(F(),j("span",Dn,"内部版本号：1.1.24")):z("",!0)]),c("div",Pn,[c("button",{class:"logout-btn",onClick:s[0]||(s[0]=(...m)=>g.handleLogout&&g.handleLogout(...m)),title:"退出登录"},s[21]||(s[21]=[c("span",{class:"icon-logout"},null,-1)])),c("button",{class:"close-btn",onClick:s[1]||(s[1]=()=>e.$emit("close"))},"×")])]),c("div",On,[c("div",In,[c("div",Rn,[s[22]||(s[22]=c("span",{class:"label"},"状态：",-1)),c("span",{class:$e(["status-badge",l.status.status])},ae(l.status.status==="running"?"运行中":"已停止"),3)]),((L=(P=(C=g.userInfo)==null?void 0:C.orgs)==null?void 0:P[0])==null?void 0:L.orgId)===2?(F(),j("div",Un,[s[23]||(s[23]=c("span",{class:"label"},"本次上传：",-1)),c("span",null,ae(l.status.processedFiles||0)+" 个文件",1)])):z("",!0),c("div",Ln,[s[28]||(s[28]=c("h4",null,"保存方式设置",-1)),c("div",Fn,[c("div",jn,[c("label",Wn,[Je(c("input",{type:"radio","onUpdate:modelValue":s[2]||(s[2]=m=>l.currentSaveMethod=m),value:"method1",onChange:s[3]||(s[3]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[24]||(s[24]=c("span",{class:"radio-label"},"方式一",-1))]),c("label",Bn,[Je(c("input",{type:"radio","onUpdate:modelValue":s[4]||(s[4]=m=>l.currentSaveMethod=m),value:"method2",onChange:s[5]||(s[5]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[25]||(s[25]=c("span",{class:"radio-label"},"方式二 (默认)",-1))]),c("label",Nn,[Je(c("input",{type:"radio","onUpdate:modelValue":s[6]||(s[6]=m=>l.currentSaveMethod=m),value:"method3",onChange:s[7]||(s[7]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[26]||(s[26]=c("span",{class:"radio-label"},"方式三",-1))]),((B=(I=(V=g.userInfo)==null?void 0:V.orgs)==null?void 0:I[0])==null?void 0:B.orgId)===2?(F(),j("label",Vn,[Je(c("input",{type:"radio","onUpdate:modelValue":s[8]||(s[8]=m=>l.currentSaveMethod=m),value:"method4",onChange:s[9]||(s[9]=(...m)=>g.updateSaveMethod&&g.updateSaveMethod(...m))},null,544),[[Vt,l.currentSaveMethod]]),s[27]||(s[27]=c("span",{class:"radio-label"},"方式四",-1))])):z("",!0)]),l.saveMethodUpdateMessage?(F(),j("div",{key:0,class:$e(["update-message",l.saveMethodUpdateSuccess?"success":"error"])},ae(l.saveMethodUpdateMessage),3)):z("",!0)])])]),z("",!0),((se=(N=(Y=g.userInfo)==null?void 0:Y.orgs)==null?void 0:N[0])==null?void 0:se.orgId)===2?(F(),j("div",Hn,[s[32]||(s[32]=c("h4",null,"上传目录设置",-1)),c("div",zn,[c("div",qn,[s[31]||(s[31]=c("span",{class:"label"},"路径：",-1)),Je(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[12]||(s[12]=m=>l.newWatchDir=m),placeholder:l.status.watchDir||"C:\\Temp"},null,8,Jn),[[Zt,l.newWatchDir]]),c("button",{class:"action-btn",onClick:s[13]||(s[13]=(...m)=>g.updateWatchDir&&g.updateWatchDir(...m)),disabled:l.isUpdating||!l.newWatchDir},ae(l.isUpdating?"更新中...":"更新目录"),9,Yn)]),l.updateMessage?(F(),j("div",{key:0,class:$e(["update-message",l.updateSuccess?"success":"error"])},ae(l.updateMessage),3)):z("",!0)])])):z("",!0),((u=(oe=(ne=g.userInfo)==null?void 0:ne.orgs)==null?void 0:oe[0])==null?void 0:u.orgId)===2?(F(),j("div",Kn,[s[34]||(s[34]=c("h4",null,"下载目录设置",-1)),c("div",Xn,[c("div",Gn,[s[33]||(s[33]=c("span",{class:"label"},"路径：",-1)),Je(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[14]||(s[14]=m=>l.newDownloadPath=m),placeholder:l.downloadPath||"C:\\Temp\\Downloads"},null,8,Zn),[[Zt,l.newDownloadPath]]),c("button",{class:"action-btn",onClick:s[15]||(s[15]=(...m)=>g.updateDownloadPath&&g.updateDownloadPath(...m)),disabled:l.isUpdatingDownloadPath||!l.newDownloadPath},ae(l.isUpdatingDownloadPath?"更新中...":"更新路径"),9,Qn)]),l.downloadPathUpdateMessage?(F(),j("div",{key:0,class:$e(["update-message",l.downloadPathUpdateSuccess?"success":"error"])},ae(l.downloadPathUpdateMessage),3)):z("",!0)])])):z("",!0),((le=(D=(ee=g.userInfo)==null?void 0:ee.orgs)==null?void 0:D[0])==null?void 0:le.orgId)===2?(F(),j("div",ea,[s[36]||(s[36]=c("h4",null,"配置目录设置",-1)),c("div",ta,[c("div",sa,[s[35]||(s[35]=c("span",{class:"label"},"路径：",-1)),Je(c("input",{type:"text",class:"directory-input","onUpdate:modelValue":s[16]||(s[16]=m=>l.newAddonConfigPath=m),placeholder:l.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,na),[[Zt,l.newAddonConfigPath]]),c("button",{class:"action-btn",onClick:s[17]||(s[17]=(...m)=>g.updateAddonConfigPath&&g.updateAddonConfigPath(...m)),disabled:l.isUpdatingAddonConfigPath||!l.newAddonConfigPath},ae(l.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,aa)]),l.addonConfigPathUpdateMessage?(F(),j("div",{key:0,class:$e(["update-message",l.addonConfigPathUpdateSuccess?"success":"error"])},ae(l.addonConfigPathUpdateMessage),3)):z("",!0)])])):z("",!0),((b=(h=(de=g.userInfo)==null?void 0:de.orgs)==null?void 0:h[0])==null?void 0:b.orgId)===2?(F(),j("div",ra,[s[37]||(s[37]=c("h4",null,"最近事件",-1)),c("div",oa,[(F(!0),j(mt,null,_t(l.recentEvents,(m,U)=>(F(),j("div",{key:U,class:"event-item"},[c("span",ia,ae(g.formatTime(m.timestamp)),1),c("span",{class:$e(["event-type",m.eventType])},ae(g.getEventTypeText(m.eventType)),3),c("span",la,ae(g.getEventMessage(m)),1)]))),128))])])):z("",!0)]),z("",!0),(($=(k=(x=g.userInfo)==null?void 0:x.orgs)==null?void 0:k[0])==null?void 0:$.orgId)===2?(F(),j("div",ca,[c("button",{class:$e(["control-btn",l.status.status==="running"?"stop":"start"]),onClick:s[20]||(s[20]=(...m)=>g.controlService&&g.controlService(...m))},ae(l.status.status==="running"?"停止服务":"启动服务"),3)])):z("",!0)])])])}const da=js(Cn,[["render",ua],["__scopeId","data-v-3a2ef3d6"]]);var De="top",Be="bottom",Ne="right",Pe="left",ls="auto",Bt=[De,Be,Ne,Pe],At="start",jt="end",pa="clippingParents",Bs="viewport",It="popper",fa="reference",ys=Bt.reduce(function(e,s){return e.concat([s+"-"+At,s+"-"+jt])},[]),Ns=[].concat(Bt,[ls]).reduce(function(e,s){return e.concat([s,s+"-"+At,s+"-"+jt])},[]),ha="beforeRead",va="read",ga="afterRead",ma="beforeMain",ba="main",wa="afterMain",ya="beforeWrite",xa="write",ka="afterWrite",Ta=[ha,va,ga,ma,ba,wa,ya,xa,ka];function nt(e){return e?(e.nodeName||"").toLowerCase():null}function Re(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var s=e.ownerDocument;return s&&s.defaultView||window}return e}function xt(e){var s=Re(e).Element;return e instanceof s||e instanceof Element}function We(e){var s=Re(e).HTMLElement;return e instanceof s||e instanceof HTMLElement}function cs(e){if(typeof ShadowRoot>"u")return!1;var s=Re(e).ShadowRoot;return e instanceof s||e instanceof ShadowRoot}function Ca(e){var s=e.state;Object.keys(s.elements).forEach(function(t){var a=s.styles[t]||{},l=s.attributes[t]||{},g=s.elements[t];!We(g)||!nt(g)||(Object.assign(g.style,a),Object.keys(l).forEach(function(T){var E=l[T];E===!1?g.removeAttribute(T):g.setAttribute(T,E===!0?"":E)}))})}function $a(e){var s=e.state,t={popper:{position:s.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(s.elements.popper.style,t.popper),s.styles=t,s.elements.arrow&&Object.assign(s.elements.arrow.style,t.arrow),function(){Object.keys(s.elements).forEach(function(a){var l=s.elements[a],g=s.attributes[a]||{},T=Object.keys(s.styles.hasOwnProperty(a)?s.styles[a]:t[a]),E=T.reduce(function(w,C){return w[C]="",w},{});!We(l)||!nt(l)||(Object.assign(l.style,E),Object.keys(g).forEach(function(w){l.removeAttribute(w)}))})}}const Vs={name:"applyStyles",enabled:!0,phase:"write",fn:Ca,effect:$a,requires:["computeStyles"]};function st(e){return e.split("-")[0]}var wt=Math.max,Yt=Math.min,Mt=Math.round;function as(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(s){return s.brand+"/"+s.version}).join(" "):navigator.userAgent}function Hs(){return!/^((?!chrome|android).)*safari/i.test(as())}function Dt(e,s,t){s===void 0&&(s=!1),t===void 0&&(t=!1);var a=e.getBoundingClientRect(),l=1,g=1;s&&We(e)&&(l=e.offsetWidth>0&&Mt(a.width)/e.offsetWidth||1,g=e.offsetHeight>0&&Mt(a.height)/e.offsetHeight||1);var T=xt(e)?Re(e):window,E=T.visualViewport,w=!Hs()&&t,C=(a.left+(w&&E?E.offsetLeft:0))/l,P=(a.top+(w&&E?E.offsetTop:0))/g,L=a.width/l,V=a.height/g;return{width:L,height:V,top:P,right:C+L,bottom:P+V,left:C,x:C,y:P}}function us(e){var s=Dt(e),t=e.offsetWidth,a=e.offsetHeight;return Math.abs(s.width-t)<=1&&(t=s.width),Math.abs(s.height-a)<=1&&(a=s.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:a}}function zs(e,s){var t=s.getRootNode&&s.getRootNode();if(e.contains(s))return!0;if(t&&cs(t)){var a=s;do{if(a&&e.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function ut(e){return Re(e).getComputedStyle(e)}function Sa(e){return["table","td","th"].indexOf(nt(e))>=0}function vt(e){return((xt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Xt(e){return nt(e)==="html"?e:e.assignedSlot||e.parentNode||(cs(e)?e.host:null)||vt(e)}function xs(e){return!We(e)||ut(e).position==="fixed"?null:e.offsetParent}function _a(e){var s=/firefox/i.test(as()),t=/Trident/i.test(as());if(t&&We(e)){var a=ut(e);if(a.position==="fixed")return null}var l=Xt(e);for(cs(l)&&(l=l.host);We(l)&&["html","body"].indexOf(nt(l))<0;){var g=ut(l);if(g.transform!=="none"||g.perspective!=="none"||g.contain==="paint"||["transform","perspective"].indexOf(g.willChange)!==-1||s&&g.willChange==="filter"||s&&g.filter&&g.filter!=="none")return l;l=l.parentNode}return null}function Nt(e){for(var s=Re(e),t=xs(e);t&&Sa(t)&&ut(t).position==="static";)t=xs(t);return t&&(nt(t)==="html"||nt(t)==="body"&&ut(t).position==="static")?s:t||_a(e)||s}function ds(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ut(e,s,t){return wt(e,Yt(s,t))}function Ea(e,s,t){var a=Ut(e,s,t);return a>t?t:a}function qs(){return{top:0,right:0,bottom:0,left:0}}function Js(e){return Object.assign({},qs(),e)}function Ys(e,s){return s.reduce(function(t,a){return t[a]=e,t},{})}var Aa=function(s,t){return s=typeof s=="function"?s(Object.assign({},t.rects,{placement:t.placement})):s,Js(typeof s!="number"?s:Ys(s,Bt))};function Ma(e){var s,t=e.state,a=e.name,l=e.options,g=t.elements.arrow,T=t.modifiersData.popperOffsets,E=st(t.placement),w=ds(E),C=[Pe,Ne].indexOf(E)>=0,P=C?"height":"width";if(!(!g||!T)){var L=Aa(l.padding,t),V=us(g),I=w==="y"?De:Pe,B=w==="y"?Be:Ne,Y=t.rects.reference[P]+t.rects.reference[w]-T[w]-t.rects.popper[P],N=T[w]-t.rects.reference[w],se=Nt(g),ne=se?w==="y"?se.clientHeight||0:se.clientWidth||0:0,oe=Y/2-N/2,u=L[I],ee=ne-V[P]-L[B],D=ne/2-V[P]/2+oe,le=Ut(u,D,ee),de=w;t.modifiersData[a]=(s={},s[de]=le,s.centerOffset=le-D,s)}}function Da(e){var s=e.state,t=e.options,a=t.element,l=a===void 0?"[data-popper-arrow]":a;l!=null&&(typeof l=="string"&&(l=s.elements.popper.querySelector(l),!l)||zs(s.elements.popper,l)&&(s.elements.arrow=l))}const Pa={name:"arrow",enabled:!0,phase:"main",fn:Ma,effect:Da,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Pt(e){return e.split("-")[1]}var Oa={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ia(e,s){var t=e.x,a=e.y,l=s.devicePixelRatio||1;return{x:Mt(t*l)/l||0,y:Mt(a*l)/l||0}}function ks(e){var s,t=e.popper,a=e.popperRect,l=e.placement,g=e.variation,T=e.offsets,E=e.position,w=e.gpuAcceleration,C=e.adaptive,P=e.roundOffsets,L=e.isFixed,V=T.x,I=V===void 0?0:V,B=T.y,Y=B===void 0?0:B,N=typeof P=="function"?P({x:I,y:Y}):{x:I,y:Y};I=N.x,Y=N.y;var se=T.hasOwnProperty("x"),ne=T.hasOwnProperty("y"),oe=Pe,u=De,ee=window;if(C){var D=Nt(t),le="clientHeight",de="clientWidth";if(D===Re(t)&&(D=vt(t),ut(D).position!=="static"&&E==="absolute"&&(le="scrollHeight",de="scrollWidth")),D=D,l===De||(l===Pe||l===Ne)&&g===jt){u=Be;var h=L&&D===ee&&ee.visualViewport?ee.visualViewport.height:D[le];Y-=h-a.height,Y*=w?1:-1}if(l===Pe||(l===De||l===Be)&&g===jt){oe=Ne;var b=L&&D===ee&&ee.visualViewport?ee.visualViewport.width:D[de];I-=b-a.width,I*=w?1:-1}}var x=Object.assign({position:E},C&&Oa),k=P===!0?Ia({x:I,y:Y},Re(t)):{x:I,y:Y};if(I=k.x,Y=k.y,w){var $;return Object.assign({},x,($={},$[u]=ne?"0":"",$[oe]=se?"0":"",$.transform=(ee.devicePixelRatio||1)<=1?"translate("+I+"px, "+Y+"px)":"translate3d("+I+"px, "+Y+"px, 0)",$))}return Object.assign({},x,(s={},s[u]=ne?Y+"px":"",s[oe]=se?I+"px":"",s.transform="",s))}function Ra(e){var s=e.state,t=e.options,a=t.gpuAcceleration,l=a===void 0?!0:a,g=t.adaptive,T=g===void 0?!0:g,E=t.roundOffsets,w=E===void 0?!0:E,C={placement:st(s.placement),variation:Pt(s.placement),popper:s.elements.popper,popperRect:s.rects.popper,gpuAcceleration:l,isFixed:s.options.strategy==="fixed"};s.modifiersData.popperOffsets!=null&&(s.styles.popper=Object.assign({},s.styles.popper,ks(Object.assign({},C,{offsets:s.modifiersData.popperOffsets,position:s.options.strategy,adaptive:T,roundOffsets:w})))),s.modifiersData.arrow!=null&&(s.styles.arrow=Object.assign({},s.styles.arrow,ks(Object.assign({},C,{offsets:s.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:w})))),s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-placement":s.placement})}const Ua={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ra,data:{}};var Ht={passive:!0};function La(e){var s=e.state,t=e.instance,a=e.options,l=a.scroll,g=l===void 0?!0:l,T=a.resize,E=T===void 0?!0:T,w=Re(s.elements.popper),C=[].concat(s.scrollParents.reference,s.scrollParents.popper);return g&&C.forEach(function(P){P.addEventListener("scroll",t.update,Ht)}),E&&w.addEventListener("resize",t.update,Ht),function(){g&&C.forEach(function(P){P.removeEventListener("scroll",t.update,Ht)}),E&&w.removeEventListener("resize",t.update,Ht)}}const Fa={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:La,data:{}};var ja={left:"right",right:"left",bottom:"top",top:"bottom"};function qt(e){return e.replace(/left|right|bottom|top/g,function(s){return ja[s]})}var Wa={start:"end",end:"start"};function Ts(e){return e.replace(/start|end/g,function(s){return Wa[s]})}function ps(e){var s=Re(e),t=s.pageXOffset,a=s.pageYOffset;return{scrollLeft:t,scrollTop:a}}function fs(e){return Dt(vt(e)).left+ps(e).scrollLeft}function Ba(e,s){var t=Re(e),a=vt(e),l=t.visualViewport,g=a.clientWidth,T=a.clientHeight,E=0,w=0;if(l){g=l.width,T=l.height;var C=Hs();(C||!C&&s==="fixed")&&(E=l.offsetLeft,w=l.offsetTop)}return{width:g,height:T,x:E+fs(e),y:w}}function Na(e){var s,t=vt(e),a=ps(e),l=(s=e.ownerDocument)==null?void 0:s.body,g=wt(t.scrollWidth,t.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),T=wt(t.scrollHeight,t.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),E=-a.scrollLeft+fs(e),w=-a.scrollTop;return ut(l||t).direction==="rtl"&&(E+=wt(t.clientWidth,l?l.clientWidth:0)-g),{width:g,height:T,x:E,y:w}}function hs(e){var s=ut(e),t=s.overflow,a=s.overflowX,l=s.overflowY;return/auto|scroll|overlay|hidden/.test(t+l+a)}function Ks(e){return["html","body","#document"].indexOf(nt(e))>=0?e.ownerDocument.body:We(e)&&hs(e)?e:Ks(Xt(e))}function Lt(e,s){var t;s===void 0&&(s=[]);var a=Ks(e),l=a===((t=e.ownerDocument)==null?void 0:t.body),g=Re(a),T=l?[g].concat(g.visualViewport||[],hs(a)?a:[]):a,E=s.concat(T);return l?E:E.concat(Lt(Xt(T)))}function rs(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Va(e,s){var t=Dt(e,!1,s==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Cs(e,s,t){return s===Bs?rs(Ba(e,t)):xt(s)?Va(s,t):rs(Na(vt(e)))}function Ha(e){var s=Lt(Xt(e)),t=["absolute","fixed"].indexOf(ut(e).position)>=0,a=t&&We(e)?Nt(e):e;return xt(a)?s.filter(function(l){return xt(l)&&zs(l,a)&&nt(l)!=="body"}):[]}function za(e,s,t,a){var l=s==="clippingParents"?Ha(e):[].concat(s),g=[].concat(l,[t]),T=g[0],E=g.reduce(function(w,C){var P=Cs(e,C,a);return w.top=wt(P.top,w.top),w.right=Yt(P.right,w.right),w.bottom=Yt(P.bottom,w.bottom),w.left=wt(P.left,w.left),w},Cs(e,T,a));return E.width=E.right-E.left,E.height=E.bottom-E.top,E.x=E.left,E.y=E.top,E}function Xs(e){var s=e.reference,t=e.element,a=e.placement,l=a?st(a):null,g=a?Pt(a):null,T=s.x+s.width/2-t.width/2,E=s.y+s.height/2-t.height/2,w;switch(l){case De:w={x:T,y:s.y-t.height};break;case Be:w={x:T,y:s.y+s.height};break;case Ne:w={x:s.x+s.width,y:E};break;case Pe:w={x:s.x-t.width,y:E};break;default:w={x:s.x,y:s.y}}var C=l?ds(l):null;if(C!=null){var P=C==="y"?"height":"width";switch(g){case At:w[C]=w[C]-(s[P]/2-t[P]/2);break;case jt:w[C]=w[C]+(s[P]/2-t[P]/2);break}}return w}function Wt(e,s){s===void 0&&(s={});var t=s,a=t.placement,l=a===void 0?e.placement:a,g=t.strategy,T=g===void 0?e.strategy:g,E=t.boundary,w=E===void 0?pa:E,C=t.rootBoundary,P=C===void 0?Bs:C,L=t.elementContext,V=L===void 0?It:L,I=t.altBoundary,B=I===void 0?!1:I,Y=t.padding,N=Y===void 0?0:Y,se=Js(typeof N!="number"?N:Ys(N,Bt)),ne=V===It?fa:It,oe=e.rects.popper,u=e.elements[B?ne:V],ee=za(xt(u)?u:u.contextElement||vt(e.elements.popper),w,P,T),D=Dt(e.elements.reference),le=Xs({reference:D,element:oe,strategy:"absolute",placement:l}),de=rs(Object.assign({},oe,le)),h=V===It?de:D,b={top:ee.top-h.top+se.top,bottom:h.bottom-ee.bottom+se.bottom,left:ee.left-h.left+se.left,right:h.right-ee.right+se.right},x=e.modifiersData.offset;if(V===It&&x){var k=x[l];Object.keys(b).forEach(function($){var m=[Ne,Be].indexOf($)>=0?1:-1,U=[De,Be].indexOf($)>=0?"y":"x";b[$]+=k[U]*m})}return b}function qa(e,s){s===void 0&&(s={});var t=s,a=t.placement,l=t.boundary,g=t.rootBoundary,T=t.padding,E=t.flipVariations,w=t.allowedAutoPlacements,C=w===void 0?Ns:w,P=Pt(a),L=P?E?ys:ys.filter(function(B){return Pt(B)===P}):Bt,V=L.filter(function(B){return C.indexOf(B)>=0});V.length===0&&(V=L);var I=V.reduce(function(B,Y){return B[Y]=Wt(e,{placement:Y,boundary:l,rootBoundary:g,padding:T})[st(Y)],B},{});return Object.keys(I).sort(function(B,Y){return I[B]-I[Y]})}function Ja(e){if(st(e)===ls)return[];var s=qt(e);return[Ts(e),s,Ts(s)]}function Ya(e){var s=e.state,t=e.options,a=e.name;if(!s.modifiersData[a]._skip){for(var l=t.mainAxis,g=l===void 0?!0:l,T=t.altAxis,E=T===void 0?!0:T,w=t.fallbackPlacements,C=t.padding,P=t.boundary,L=t.rootBoundary,V=t.altBoundary,I=t.flipVariations,B=I===void 0?!0:I,Y=t.allowedAutoPlacements,N=s.options.placement,se=st(N),ne=se===N,oe=w||(ne||!B?[qt(N)]:Ja(N)),u=[N].concat(oe).reduce(function(ke,Se){return ke.concat(st(Se)===ls?qa(s,{placement:Se,boundary:P,rootBoundary:L,padding:C,flipVariations:B,allowedAutoPlacements:Y}):Se)},[]),ee=s.rects.reference,D=s.rects.popper,le=new Map,de=!0,h=u[0],b=0;b<u.length;b++){var x=u[b],k=st(x),$=Pt(x)===At,m=[De,Be].indexOf(k)>=0,U=m?"width":"height",_=Wt(s,{placement:x,boundary:P,rootBoundary:L,altBoundary:V,padding:C}),K=m?$?Ne:Pe:$?Be:De;ee[U]>D[U]&&(K=qt(K));var ve=qt(K),me=[];if(g&&me.push(_[k]<=0),E&&me.push(_[K]<=0,_[ve]<=0),me.every(function(ke){return ke})){h=x,de=!1;break}le.set(x,me)}if(de)for(var Ee=B?3:1,Ue=function(Se){var Me=u.find(function(Le){var X=le.get(Le);if(X)return X.slice(0,Se).every(function(Ie){return Ie})});if(Me)return h=Me,"break"},Ae=Ee;Ae>0;Ae--){var Oe=Ue(Ae);if(Oe==="break")break}s.placement!==h&&(s.modifiersData[a]._skip=!0,s.placement=h,s.reset=!0)}}const Ka={name:"flip",enabled:!0,phase:"main",fn:Ya,requiresIfExists:["offset"],data:{_skip:!1}};function $s(e,s,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-s.height-t.y,right:e.right-s.width+t.x,bottom:e.bottom-s.height+t.y,left:e.left-s.width-t.x}}function Ss(e){return[De,Ne,Be,Pe].some(function(s){return e[s]>=0})}function Xa(e){var s=e.state,t=e.name,a=s.rects.reference,l=s.rects.popper,g=s.modifiersData.preventOverflow,T=Wt(s,{elementContext:"reference"}),E=Wt(s,{altBoundary:!0}),w=$s(T,a),C=$s(E,l,g),P=Ss(w),L=Ss(C);s.modifiersData[t]={referenceClippingOffsets:w,popperEscapeOffsets:C,isReferenceHidden:P,hasPopperEscaped:L},s.attributes.popper=Object.assign({},s.attributes.popper,{"data-popper-reference-hidden":P,"data-popper-escaped":L})}const Ga={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Xa};function Za(e,s,t){var a=st(e),l=[Pe,De].indexOf(a)>=0?-1:1,g=typeof t=="function"?t(Object.assign({},s,{placement:e})):t,T=g[0],E=g[1];return T=T||0,E=(E||0)*l,[Pe,Ne].indexOf(a)>=0?{x:E,y:T}:{x:T,y:E}}function Qa(e){var s=e.state,t=e.options,a=e.name,l=t.offset,g=l===void 0?[0,0]:l,T=Ns.reduce(function(P,L){return P[L]=Za(L,s.rects,g),P},{}),E=T[s.placement],w=E.x,C=E.y;s.modifiersData.popperOffsets!=null&&(s.modifiersData.popperOffsets.x+=w,s.modifiersData.popperOffsets.y+=C),s.modifiersData[a]=T}const er={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Qa};function tr(e){var s=e.state,t=e.name;s.modifiersData[t]=Xs({reference:s.rects.reference,element:s.rects.popper,strategy:"absolute",placement:s.placement})}const sr={name:"popperOffsets",enabled:!0,phase:"read",fn:tr,data:{}};function nr(e){return e==="x"?"y":"x"}function ar(e){var s=e.state,t=e.options,a=e.name,l=t.mainAxis,g=l===void 0?!0:l,T=t.altAxis,E=T===void 0?!1:T,w=t.boundary,C=t.rootBoundary,P=t.altBoundary,L=t.padding,V=t.tether,I=V===void 0?!0:V,B=t.tetherOffset,Y=B===void 0?0:B,N=Wt(s,{boundary:w,rootBoundary:C,padding:L,altBoundary:P}),se=st(s.placement),ne=Pt(s.placement),oe=!ne,u=ds(se),ee=nr(u),D=s.modifiersData.popperOffsets,le=s.rects.reference,de=s.rects.popper,h=typeof Y=="function"?Y(Object.assign({},s.rects,{placement:s.placement})):Y,b=typeof h=="number"?{mainAxis:h,altAxis:h}:Object.assign({mainAxis:0,altAxis:0},h),x=s.modifiersData.offset?s.modifiersData.offset[s.placement]:null,k={x:0,y:0};if(D){if(g){var $,m=u==="y"?De:Pe,U=u==="y"?Be:Ne,_=u==="y"?"height":"width",K=D[u],ve=K+N[m],me=K-N[U],Ee=I?-de[_]/2:0,Ue=ne===At?le[_]:de[_],Ae=ne===At?-de[_]:-le[_],Oe=s.elements.arrow,ke=I&&Oe?us(Oe):{width:0,height:0},Se=s.modifiersData["arrow#persistent"]?s.modifiersData["arrow#persistent"].padding:qs(),Me=Se[m],Le=Se[U],X=Ut(0,le[_],ke[_]),Ie=oe?le[_]/2-Ee-X-Me-b.mainAxis:Ue-X-Me-b.mainAxis,ye=oe?-le[_]/2+Ee+X+Le+b.mainAxis:Ae+X+Le+b.mainAxis,at=s.elements.arrow&&Nt(s.elements.arrow),Xe=at?u==="y"?at.clientTop||0:at.clientLeft||0:0,Fe=($=x==null?void 0:x[u])!=null?$:0,ge=K+Ie-Fe-Xe,dt=K+ye-Fe,Ge=Ut(I?Yt(ve,ge):ve,K,I?wt(me,dt):me);D[u]=Ge,k[u]=Ge-K}if(E){var xe,rt=u==="x"?De:Pe,Ve=u==="x"?Be:Ne,_e=D[ee],je=ee==="y"?"height":"width",Ze=_e+N[rt],He=_e-N[Ve],Qe=[De,Pe].indexOf(se)!==-1,ot=(xe=x==null?void 0:x[ee])!=null?xe:0,et=Qe?Ze:_e-le[je]-de[je]-ot+b.altAxis,pt=Qe?_e+le[je]+de[je]-ot-b.altAxis:He,it=I&&Qe?Ea(et,_e,pt):Ut(I?et:Ze,_e,I?pt:He);D[ee]=it,k[ee]=it-_e}s.modifiersData[a]=k}}const rr={name:"preventOverflow",enabled:!0,phase:"main",fn:ar,requiresIfExists:["offset"]};function or(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function ir(e){return e===Re(e)||!We(e)?ps(e):or(e)}function lr(e){var s=e.getBoundingClientRect(),t=Mt(s.width)/e.offsetWidth||1,a=Mt(s.height)/e.offsetHeight||1;return t!==1||a!==1}function cr(e,s,t){t===void 0&&(t=!1);var a=We(s),l=We(s)&&lr(s),g=vt(s),T=Dt(e,l,t),E={scrollLeft:0,scrollTop:0},w={x:0,y:0};return(a||!a&&!t)&&((nt(s)!=="body"||hs(g))&&(E=ir(s)),We(s)?(w=Dt(s,!0),w.x+=s.clientLeft,w.y+=s.clientTop):g&&(w.x=fs(g))),{x:T.left+E.scrollLeft-w.x,y:T.top+E.scrollTop-w.y,width:T.width,height:T.height}}function ur(e){var s=new Map,t=new Set,a=[];e.forEach(function(g){s.set(g.name,g)});function l(g){t.add(g.name);var T=[].concat(g.requires||[],g.requiresIfExists||[]);T.forEach(function(E){if(!t.has(E)){var w=s.get(E);w&&l(w)}}),a.push(g)}return e.forEach(function(g){t.has(g.name)||l(g)}),a}function dr(e){var s=ur(e);return Ta.reduce(function(t,a){return t.concat(s.filter(function(l){return l.phase===a}))},[])}function pr(e){var s;return function(){return s||(s=new Promise(function(t){Promise.resolve().then(function(){s=void 0,t(e())})})),s}}function fr(e){var s=e.reduce(function(t,a){var l=t[a.name];return t[a.name]=l?Object.assign({},l,a,{options:Object.assign({},l.options,a.options),data:Object.assign({},l.data,a.data)}):a,t},{});return Object.keys(s).map(function(t){return s[t]})}var _s={placement:"bottom",modifiers:[],strategy:"absolute"};function Es(){for(var e=arguments.length,s=new Array(e),t=0;t<e;t++)s[t]=arguments[t];return!s.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function hr(e){e===void 0&&(e={});var s=e,t=s.defaultModifiers,a=t===void 0?[]:t,l=s.defaultOptions,g=l===void 0?_s:l;return function(E,w,C){C===void 0&&(C=g);var P={placement:"bottom",orderedModifiers:[],options:Object.assign({},_s,g),modifiersData:{},elements:{reference:E,popper:w},attributes:{},styles:{}},L=[],V=!1,I={state:P,setOptions:function(se){var ne=typeof se=="function"?se(P.options):se;Y(),P.options=Object.assign({},g,P.options,ne),P.scrollParents={reference:xt(E)?Lt(E):E.contextElement?Lt(E.contextElement):[],popper:Lt(w)};var oe=dr(fr([].concat(a,P.options.modifiers)));return P.orderedModifiers=oe.filter(function(u){return u.enabled}),B(),I.update()},forceUpdate:function(){if(!V){var se=P.elements,ne=se.reference,oe=se.popper;if(Es(ne,oe)){P.rects={reference:cr(ne,Nt(oe),P.options.strategy==="fixed"),popper:us(oe)},P.reset=!1,P.placement=P.options.placement,P.orderedModifiers.forEach(function(b){return P.modifiersData[b.name]=Object.assign({},b.data)});for(var u=0;u<P.orderedModifiers.length;u++){if(P.reset===!0){P.reset=!1,u=-1;continue}var ee=P.orderedModifiers[u],D=ee.fn,le=ee.options,de=le===void 0?{}:le,h=ee.name;typeof D=="function"&&(P=D({state:P,options:de,name:h,instance:I})||P)}}}},update:pr(function(){return new Promise(function(N){I.forceUpdate(),N(P)})}),destroy:function(){Y(),V=!0}};if(!Es(E,w))return I;I.setOptions(C).then(function(N){!V&&C.onFirstUpdate&&C.onFirstUpdate(N)});function B(){P.orderedModifiers.forEach(function(N){var se=N.name,ne=N.options,oe=ne===void 0?{}:ne,u=N.effect;if(typeof u=="function"){var ee=u({state:P,name:se,instance:I,options:oe}),D=function(){};L.push(ee||D)}})}function Y(){L.forEach(function(N){return N()}),L=[]}return I}}var vr=[Fa,sr,Ua,Vs,er,Ka,rr,Pa,Ga],gr=hr({defaultModifiers:vr}),mr="tippy-box",Gs="tippy-content",br="tippy-backdrop",Zs="tippy-arrow",Qs="tippy-svg-arrow",bt={passive:!0,capture:!0},en=function(){return document.body};function es(e,s,t){if(Array.isArray(e)){var a=e[s];return a??(Array.isArray(t)?t[s]:t)}return e}function vs(e,s){var t={}.toString.call(e);return t.indexOf("[object")===0&&t.indexOf(s+"]")>-1}function tn(e,s){return typeof e=="function"?e.apply(void 0,s):e}function As(e,s){if(s===0)return e;var t;return function(a){clearTimeout(t),t=setTimeout(function(){e(a)},s)}}function wr(e){return e.split(/\s+/).filter(Boolean)}function Et(e){return[].concat(e)}function Ms(e,s){e.indexOf(s)===-1&&e.push(s)}function yr(e){return e.filter(function(s,t){return e.indexOf(s)===t})}function xr(e){return e.split("-")[0]}function Kt(e){return[].slice.call(e)}function Ds(e){return Object.keys(e).reduce(function(s,t){return e[t]!==void 0&&(s[t]=e[t]),s},{})}function Ft(){return document.createElement("div")}function Gt(e){return["Element","Fragment"].some(function(s){return vs(e,s)})}function kr(e){return vs(e,"NodeList")}function Tr(e){return vs(e,"MouseEvent")}function Cr(e){return!!(e&&e._tippy&&e._tippy.reference===e)}function $r(e){return Gt(e)?[e]:kr(e)?Kt(e):Array.isArray(e)?e:Kt(document.querySelectorAll(e))}function ts(e,s){e.forEach(function(t){t&&(t.style.transitionDuration=s+"ms")})}function Ps(e,s){e.forEach(function(t){t&&t.setAttribute("data-state",s)})}function Sr(e){var s,t=Et(e),a=t[0];return a!=null&&(s=a.ownerDocument)!=null&&s.body?a.ownerDocument:document}function _r(e,s){var t=s.clientX,a=s.clientY;return e.every(function(l){var g=l.popperRect,T=l.popperState,E=l.props,w=E.interactiveBorder,C=xr(T.placement),P=T.modifiersData.offset;if(!P)return!0;var L=C==="bottom"?P.top.y:0,V=C==="top"?P.bottom.y:0,I=C==="right"?P.left.x:0,B=C==="left"?P.right.x:0,Y=g.top-a+L>w,N=a-g.bottom-V>w,se=g.left-t+I>w,ne=t-g.right-B>w;return Y||N||se||ne})}function ss(e,s,t){var a=s+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(l){e[a](l,t)})}function Os(e,s){for(var t=s;t;){var a;if(e.contains(t))return!0;t=t.getRootNode==null||(a=t.getRootNode())==null?void 0:a.host}return!1}var tt={isTouch:!1},Is=0;function Er(){tt.isTouch||(tt.isTouch=!0,window.performance&&document.addEventListener("mousemove",sn))}function sn(){var e=performance.now();e-Is<20&&(tt.isTouch=!1,document.removeEventListener("mousemove",sn)),Is=e}function Ar(){var e=document.activeElement;if(Cr(e)){var s=e._tippy;e.blur&&!s.state.isVisible&&e.blur()}}function Mr(){document.addEventListener("touchstart",Er,bt),window.addEventListener("blur",Ar)}var Dr=typeof window<"u"&&typeof document<"u",Pr=Dr?!!window.msCrypto:!1,Or={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ir={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},Ke=Object.assign({appendTo:en,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Or,Ir),Rr=Object.keys(Ke),Ur=function(s){var t=Object.keys(s);t.forEach(function(a){Ke[a]=s[a]})};function nn(e){var s=e.plugins||[],t=s.reduce(function(a,l){var g=l.name,T=l.defaultValue;if(g){var E;a[g]=e[g]!==void 0?e[g]:(E=Ke[g])!=null?E:T}return a},{});return Object.assign({},e,t)}function Lr(e,s){var t=s?Object.keys(nn(Object.assign({},Ke,{plugins:s}))):Rr,a=t.reduce(function(l,g){var T=(e.getAttribute("data-tippy-"+g)||"").trim();if(!T)return l;if(g==="content")l[g]=T;else try{l[g]=JSON.parse(T)}catch{l[g]=T}return l},{});return a}function Rs(e,s){var t=Object.assign({},s,{content:tn(s.content,[e])},s.ignoreAttributes?{}:Lr(e,s.plugins));return t.aria=Object.assign({},Ke.aria,t.aria),t.aria={expanded:t.aria.expanded==="auto"?s.interactive:t.aria.expanded,content:t.aria.content==="auto"?s.interactive?null:"describedby":t.aria.content},t}var Fr=function(){return"innerHTML"};function os(e,s){e[Fr()]=s}function Us(e){var s=Ft();return e===!0?s.className=Zs:(s.className=Qs,Gt(e)?s.appendChild(e):os(s,e)),s}function Ls(e,s){Gt(s.content)?(os(e,""),e.appendChild(s.content)):typeof s.content!="function"&&(s.allowHTML?os(e,s.content):e.textContent=s.content)}function is(e){var s=e.firstElementChild,t=Kt(s.children);return{box:s,content:t.find(function(a){return a.classList.contains(Gs)}),arrow:t.find(function(a){return a.classList.contains(Zs)||a.classList.contains(Qs)}),backdrop:t.find(function(a){return a.classList.contains(br)})}}function an(e){var s=Ft(),t=Ft();t.className=mr,t.setAttribute("data-state","hidden"),t.setAttribute("tabindex","-1");var a=Ft();a.className=Gs,a.setAttribute("data-state","hidden"),Ls(a,e.props),s.appendChild(t),t.appendChild(a),l(e.props,e.props);function l(g,T){var E=is(s),w=E.box,C=E.content,P=E.arrow;T.theme?w.setAttribute("data-theme",T.theme):w.removeAttribute("data-theme"),typeof T.animation=="string"?w.setAttribute("data-animation",T.animation):w.removeAttribute("data-animation"),T.inertia?w.setAttribute("data-inertia",""):w.removeAttribute("data-inertia"),w.style.maxWidth=typeof T.maxWidth=="number"?T.maxWidth+"px":T.maxWidth,T.role?w.setAttribute("role",T.role):w.removeAttribute("role"),(g.content!==T.content||g.allowHTML!==T.allowHTML)&&Ls(C,e.props),T.arrow?P?g.arrow!==T.arrow&&(w.removeChild(P),w.appendChild(Us(T.arrow))):w.appendChild(Us(T.arrow)):P&&w.removeChild(P)}return{popper:s,onUpdate:l}}an.$$tippy=!0;var jr=1,zt=[],ns=[];function Wr(e,s){var t=Rs(e,Object.assign({},Ke,nn(Ds(s)))),a,l,g,T=!1,E=!1,w=!1,C=!1,P,L,V,I=[],B=As(ge,t.interactiveDebounce),Y,N=jr++,se=null,ne=yr(t.plugins),oe={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},u={id:N,reference:e,popper:Ft(),popperInstance:se,props:t,state:oe,plugins:ne,clearDelayTimeouts:et,setProps:pt,setContent:it,show:Ot,hide:kt,hideWithInteractivity:Tt,enable:Qe,disable:ot,unmount:Ct,destroy:ft};if(!t.render)return u;var ee=t.render(u),D=ee.popper,le=ee.onUpdate;D.setAttribute("data-tippy-root",""),D.id="tippy-"+u.id,u.popper=D,e._tippy=u,D._tippy=u;var de=ne.map(function(y){return y.fn(u)}),h=e.hasAttribute("aria-expanded");return at(),Ee(),K(),ve("onCreate",[u]),t.showOnCreate&&Ze(),D.addEventListener("mouseenter",function(){u.props.interactive&&u.state.isVisible&&u.clearDelayTimeouts()}),D.addEventListener("mouseleave",function(){u.props.interactive&&u.props.trigger.indexOf("mouseenter")>=0&&m().addEventListener("mousemove",B)}),u;function b(){var y=u.props.touch;return Array.isArray(y)?y:[y,0]}function x(){return b()[0]==="hold"}function k(){var y;return!!((y=u.props.render)!=null&&y.$$tippy)}function $(){return Y||e}function m(){var y=$().parentNode;return y?Sr(y):document}function U(){return is(D)}function _(y){return u.state.isMounted&&!u.state.isVisible||tt.isTouch||P&&P.type==="focus"?0:es(u.props.delay,y?0:1,Ke.delay)}function K(y){y===void 0&&(y=!1),D.style.pointerEvents=u.props.interactive&&!y?"":"none",D.style.zIndex=""+u.props.zIndex}function ve(y,W,H){if(H===void 0&&(H=!0),de.forEach(function(re){re[y]&&re[y].apply(re,W)}),H){var ie;(ie=u.props)[y].apply(ie,W)}}function me(){var y=u.props.aria;if(y.content){var W="aria-"+y.content,H=D.id,ie=Et(u.props.triggerTarget||e);ie.forEach(function(re){var be=re.getAttribute(W);if(u.state.isVisible)re.setAttribute(W,be?be+" "+H:H);else{var Te=be&&be.replace(H,"").trim();Te?re.setAttribute(W,Te):re.removeAttribute(W)}})}}function Ee(){if(!(h||!u.props.aria.expanded)){var y=Et(u.props.triggerTarget||e);y.forEach(function(W){u.props.interactive?W.setAttribute("aria-expanded",u.state.isVisible&&W===$()?"true":"false"):W.removeAttribute("aria-expanded")})}}function Ue(){m().removeEventListener("mousemove",B),zt=zt.filter(function(y){return y!==B})}function Ae(y){if(!(tt.isTouch&&(w||y.type==="mousedown"))){var W=y.composedPath&&y.composedPath()[0]||y.target;if(!(u.props.interactive&&Os(D,W))){if(Et(u.props.triggerTarget||e).some(function(H){return Os(H,W)})){if(tt.isTouch||u.state.isVisible&&u.props.trigger.indexOf("click")>=0)return}else ve("onClickOutside",[u,y]);u.props.hideOnClick===!0&&(u.clearDelayTimeouts(),u.hide(),E=!0,setTimeout(function(){E=!1}),u.state.isMounted||Me())}}}function Oe(){w=!0}function ke(){w=!1}function Se(){var y=m();y.addEventListener("mousedown",Ae,!0),y.addEventListener("touchend",Ae,bt),y.addEventListener("touchstart",ke,bt),y.addEventListener("touchmove",Oe,bt)}function Me(){var y=m();y.removeEventListener("mousedown",Ae,!0),y.removeEventListener("touchend",Ae,bt),y.removeEventListener("touchstart",ke,bt),y.removeEventListener("touchmove",Oe,bt)}function Le(y,W){Ie(y,function(){!u.state.isVisible&&D.parentNode&&D.parentNode.contains(D)&&W()})}function X(y,W){Ie(y,W)}function Ie(y,W){var H=U().box;function ie(re){re.target===H&&(ss(H,"remove",ie),W())}if(y===0)return W();ss(H,"remove",L),ss(H,"add",ie),L=ie}function ye(y,W,H){H===void 0&&(H=!1);var ie=Et(u.props.triggerTarget||e);ie.forEach(function(re){re.addEventListener(y,W,H),I.push({node:re,eventType:y,handler:W,options:H})})}function at(){x()&&(ye("touchstart",Fe,{passive:!0}),ye("touchend",dt,{passive:!0})),wr(u.props.trigger).forEach(function(y){if(y!=="manual")switch(ye(y,Fe),y){case"mouseenter":ye("mouseleave",dt);break;case"focus":ye(Pr?"focusout":"blur",Ge);break;case"focusin":ye("focusout",Ge);break}})}function Xe(){I.forEach(function(y){var W=y.node,H=y.eventType,ie=y.handler,re=y.options;W.removeEventListener(H,ie,re)}),I=[]}function Fe(y){var W,H=!1;if(!(!u.state.isEnabled||xe(y)||E)){var ie=((W=P)==null?void 0:W.type)==="focus";P=y,Y=y.currentTarget,Ee(),!u.state.isVisible&&Tr(y)&&zt.forEach(function(re){return re(y)}),y.type==="click"&&(u.props.trigger.indexOf("mouseenter")<0||T)&&u.props.hideOnClick!==!1&&u.state.isVisible?H=!0:Ze(y),y.type==="click"&&(T=!H),H&&!ie&&He(y)}}function ge(y){var W=y.target,H=$().contains(W)||D.contains(W);if(!(y.type==="mousemove"&&H)){var ie=je().concat(D).map(function(re){var be,Te=re._tippy,ze=(be=Te.popperInstance)==null?void 0:be.state;return ze?{popperRect:re.getBoundingClientRect(),popperState:ze,props:t}:null}).filter(Boolean);_r(ie,y)&&(Ue(),He(y))}}function dt(y){var W=xe(y)||u.props.trigger.indexOf("click")>=0&&T;if(!W){if(u.props.interactive){u.hideWithInteractivity(y);return}He(y)}}function Ge(y){u.props.trigger.indexOf("focusin")<0&&y.target!==$()||u.props.interactive&&y.relatedTarget&&D.contains(y.relatedTarget)||He(y)}function xe(y){return tt.isTouch?x()!==y.type.indexOf("touch")>=0:!1}function rt(){Ve();var y=u.props,W=y.popperOptions,H=y.placement,ie=y.offset,re=y.getReferenceClientRect,be=y.moveTransition,Te=k()?is(D).arrow:null,ze=re?{getBoundingClientRect:re,contextElement:re.contextElement||$()}:e,S={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(G){var ce=G.state;if(k()){var n=U(),i=n.box;["placement","reference-hidden","escaped"].forEach(function(r){r==="placement"?i.setAttribute("data-placement",ce.placement):ce.attributes.popper["data-popper-"+r]?i.setAttribute("data-"+r,""):i.removeAttribute("data-"+r)}),ce.attributes.popper={}}}},f=[{name:"offset",options:{offset:ie}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!be}},S];k()&&Te&&f.push({name:"arrow",options:{element:Te,padding:3}}),f.push.apply(f,(W==null?void 0:W.modifiers)||[]),u.popperInstance=gr(ze,D,Object.assign({},W,{placement:H,onFirstUpdate:V,modifiers:f}))}function Ve(){u.popperInstance&&(u.popperInstance.destroy(),u.popperInstance=null)}function _e(){var y=u.props.appendTo,W,H=$();u.props.interactive&&y===en||y==="parent"?W=H.parentNode:W=tn(y,[H]),W.contains(D)||W.appendChild(D),u.state.isMounted=!0,rt()}function je(){return Kt(D.querySelectorAll("[data-tippy-root]"))}function Ze(y){u.clearDelayTimeouts(),y&&ve("onTrigger",[u,y]),Se();var W=_(!0),H=b(),ie=H[0],re=H[1];tt.isTouch&&ie==="hold"&&re&&(W=re),W?a=setTimeout(function(){u.show()},W):u.show()}function He(y){if(u.clearDelayTimeouts(),ve("onUntrigger",[u,y]),!u.state.isVisible){Me();return}if(!(u.props.trigger.indexOf("mouseenter")>=0&&u.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(y.type)>=0&&T)){var W=_(!1);W?l=setTimeout(function(){u.state.isVisible&&u.hide()},W):g=requestAnimationFrame(function(){u.hide()})}}function Qe(){u.state.isEnabled=!0}function ot(){u.hide(),u.state.isEnabled=!1}function et(){clearTimeout(a),clearTimeout(l),cancelAnimationFrame(g)}function pt(y){if(!u.state.isDestroyed){ve("onBeforeUpdate",[u,y]),Xe();var W=u.props,H=Rs(e,Object.assign({},W,Ds(y),{ignoreAttributes:!0}));u.props=H,at(),W.interactiveDebounce!==H.interactiveDebounce&&(Ue(),B=As(ge,H.interactiveDebounce)),W.triggerTarget&&!H.triggerTarget?Et(W.triggerTarget).forEach(function(ie){ie.removeAttribute("aria-expanded")}):H.triggerTarget&&e.removeAttribute("aria-expanded"),Ee(),K(),le&&le(W,H),u.popperInstance&&(rt(),je().forEach(function(ie){requestAnimationFrame(ie._tippy.popperInstance.forceUpdate)})),ve("onAfterUpdate",[u,y])}}function it(y){u.setProps({content:y})}function Ot(){var y=u.state.isVisible,W=u.state.isDestroyed,H=!u.state.isEnabled,ie=tt.isTouch&&!u.props.touch,re=es(u.props.duration,0,Ke.duration);if(!(y||W||H||ie)&&!$().hasAttribute("disabled")&&(ve("onShow",[u],!1),u.props.onShow(u)!==!1)){if(u.state.isVisible=!0,k()&&(D.style.visibility="visible"),K(),Se(),u.state.isMounted||(D.style.transition="none"),k()){var be=U(),Te=be.box,ze=be.content;ts([Te,ze],0)}V=function(){var f;if(!(!u.state.isVisible||C)){if(C=!0,D.offsetHeight,D.style.transition=u.props.moveTransition,k()&&u.props.animation){var J=U(),G=J.box,ce=J.content;ts([G,ce],re),Ps([G,ce],"visible")}me(),Ee(),Ms(ns,u),(f=u.popperInstance)==null||f.forceUpdate(),ve("onMount",[u]),u.props.animation&&k()&&X(re,function(){u.state.isShown=!0,ve("onShown",[u])})}},_e()}}function kt(){var y=!u.state.isVisible,W=u.state.isDestroyed,H=!u.state.isEnabled,ie=es(u.props.duration,1,Ke.duration);if(!(y||W||H)&&(ve("onHide",[u],!1),u.props.onHide(u)!==!1)){if(u.state.isVisible=!1,u.state.isShown=!1,C=!1,T=!1,k()&&(D.style.visibility="hidden"),Ue(),Me(),K(!0),k()){var re=U(),be=re.box,Te=re.content;u.props.animation&&(ts([be,Te],ie),Ps([be,Te],"hidden"))}me(),Ee(),u.props.animation?k()&&Le(ie,u.unmount):u.unmount()}}function Tt(y){m().addEventListener("mousemove",B),Ms(zt,B),B(y)}function Ct(){u.state.isVisible&&u.hide(),u.state.isMounted&&(Ve(),je().forEach(function(y){y._tippy.unmount()}),D.parentNode&&D.parentNode.removeChild(D),ns=ns.filter(function(y){return y!==u}),u.state.isMounted=!1,ve("onHidden",[u]))}function ft(){u.state.isDestroyed||(u.clearDelayTimeouts(),u.unmount(),Xe(),delete e._tippy,u.state.isDestroyed=!0,ve("onDestroy",[u]))}}function yt(e,s){s===void 0&&(s={});var t=Ke.plugins.concat(s.plugins||[]);Mr();var a=Object.assign({},s,{plugins:t}),l=$r(e),g=l.reduce(function(T,E){var w=E&&Wr(E,a);return w&&T.push(w),T},[]);return Gt(e)?g[0]:g}yt.defaultProps=Ke;yt.setDefaultProps=Ur;yt.currentInput=tt;Object.assign({},Vs,{effect:function(s){var t=s.state,a={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,a.popper),t.styles=a,t.elements.arrow&&Object.assign(t.elements.arrow.style,a.arrow)}});yt.setDefaultProps({render:an});const Br={class:"task-pane"},Nr={key:0,class:"loading-overlay"},Vr={key:1,class:"format-error-overlay"},Hr={class:"format-error-content"},zr={class:"format-error-message"},qr={class:"format-error-actions"},Jr={class:"doc-header"},Yr={class:"doc-title"},Kr={class:"action-area"},Xr={class:"select-container"},Gr={class:"select-group"},Zr=["disabled"],Qr=["value"],eo={class:"select-group"},to=["disabled"],so=["value"],no=["title"],ao={key:0,class:"science-warning"},ro={class:"action-buttons"},oo=["disabled"],io={class:"btn-content"},lo={key:0,class:"button-loader"},co=["disabled"],uo={class:"btn-content"},po={key:0,class:"button-loader"},fo=["disabled"],ho={class:"btn-content"},vo={key:0,class:"button-loader"},go={class:"content-area"},mo={class:"modal-header"},bo={class:"modal-body"},wo={class:"selection-content"},yo={class:"modal-header"},xo={class:"modal-body"},ko={class:"alert-message"},To={class:"alert-actions"},Co={key:2,class:"modal-overlay"},$o={class:"modal-header"},So={class:"modal-body"},_o={class:"confirm-message"},Eo={class:"confirm-actions"},Ao={class:"modal-header"},Mo={class:"modal-title"},Do={class:"modal-body"},Po={class:"alert-message"},Oo={class:"alert-actions"},Io={class:"task-queue"},Ro={class:"queue-header"},Uo={class:"queue-status-filter"},Lo=["value"],Fo={class:"queue-actions"},jo=["disabled","title"],Wo={class:"task-count"},Bo={key:0,class:"queue-table-container"},No={class:"col-id"},Vo={class:"id-header"},Ho={key:0,class:"col-subject"},zo={class:"subject-header"},qo={class:"switch"},Jo=["title"],Yo={key:1,class:"col-status"},Ko=["onClick"],Xo={class:"col-id"},Go={class:"id-cell group-cell"},Zo={class:"group-toggle-icon"},Qo={class:"group-label"},ei={key:0,class:"col-subject"},ti={class:"subject-cell group-subject"},si={key:1,class:"col-status"},ni={class:"col-actions"},ai={class:"task-actions"},ri={class:"group-action-text"},oi=["onClick"],ii={class:"col-id"},li={class:"id-content"},ci={class:"task-id"},ui={key:0,class:"analysis-task-icon",title:"解析任务"},di={key:1,class:"enhance-task-icon",title:"增强解析任务"},pi={key:2,class:"check-task-icon",title:"校对任务"},fi={key:0,class:"status-in-id"},hi={key:0,class:"col-subject"},vi=["onMouseenter"],gi={key:1,class:"col-status"},mi={class:"status-cell"},bi=["onClick"],wi={class:"col-id"},yi={class:"id-content"},xi={class:"task-id"},ki={key:0,class:"analysis-task-icon",title:"解析任务"},Ti={key:1,class:"enhance-task-icon",title:"增强解析任务"},Ci={key:2,class:"check-task-icon",title:"校对任务"},$i={key:0,class:"status-in-id"},Si={key:0,class:"col-subject"},_i=["onMouseenter"],Ei={key:1,class:"col-status"},Ai={class:"status-cell"},Mi={class:"col-actions"},Di={class:"task-actions"},Pi=["onClick"],Oi=["onClick"],Ii={key:2,class:"no-action-icon",title:"无可用操作"},Ri={key:1,class:"empty-queue"},Ui={key:4,class:"log-container"},Li={class:"log-actions"},Fi={class:"toggle-icon"},ji=["innerHTML"],Wi={__name:"TaskPane",setup(e){const s=pe(!1),t=pe(!1),a=pe(!1),l=pe(""),g=pe(!1),T=pe(!1),E=pe(!1),w=pe(!1),C=pe(!0),P=pe(""),L=pe(!1),V=pe(window.innerWidth),I=ct(()=>V.value<750),B=ct(()=>V.value<380),Y=()=>{V.value=window.innerWidth},N=pe(null),se=pe(!1),ne=pe(""),oe=pe(new Set);pe(!1);const u={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map},ee=pe(!1),D=pe([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"}]),{docName:le,selected:de,logger:h,map:b,subject:x,stage:k,subjectOptions:$,stageOptions:m,appConfig:U,clearLog:_,checkDocumentFormat:K,getTaskStatusClass:ve,getTaskStatusText:me,terminateTask:Ee,run1:Ue,runCheck:Ae,setupLifecycle:Oe,navigateToTaskControl:ke,isLoading:Se,tryRemoveTaskPlaceholderWithLoading:Me,confirmDialog:Le,handleConfirm:X,errorDialog:Ie,hideErrorDialog:ye,getCompletedTasksCount:at,getReleasableTasksCount:Xe,showConfirm:Fe}=Tn(),ge=pe(null);(()=>{var S;try{if((S=window.Application)!=null&&S.PluginStorage){const f=window.Application.PluginStorage.getItem("user_info");f?(ge.value=JSON.parse(f),console.log("用户信息已加载:",ge.value)):console.log("未找到用户信息")}}catch(f){console.error("解析用户信息时出错:",f)}})(),Jt(ge,S=>{S&&S.orgs&&S.orgs[0]&&console.log(`用户企业ID: ${S.orgs[0].orgId}, 校对功能${S.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const Ge=ct(()=>!ge.value||ge.value.isAdmin||ge.value.isOwner?$:ge.value.subject?$.filter(S=>S.value===ge.value.subject):$),xe=()=>{ge.value&&!ge.value.isAdmin&&!ge.value.isOwner&&ge.value.subject&&(x.value=ge.value.subject)},rt=ct(()=>["physics","chemistry","biology","math"].includes(x.value));Jt(U,S=>{S&&(console.log("TaskPane组件收到应用配置更新:",S),console.log("当前版本类型:",S.EDITION),console.log("当前年级选项:",m.value))},{deep:!0,immediate:!0});const Ve=()=>{try{const S=K();C.value=S.isValid,P.value=S.message,L.value=!S.isValid,S.isValid||console.warn("文档格式检查失败:",S.message)}catch(S){console.error("执行文档格式检查时出错:",S),C.value=!1,P.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",L.value=!0}},_e=ct(()=>{let S={};const f=b;if(ne.value==="")S={...f};else for(const J in f)if(Object.prototype.hasOwnProperty.call(f,J)){const G=f[J];G.status===ne.value&&(S[J]=G)}return se.value&&N.value?S[N.value]?{[N.value]:S[N.value]}:{}:S}),je=ct(()=>{const S=_e.value;return Object.entries(S).map(([J,G])=>({tid:J,...G})).sort((J,G)=>{const ce=J.startTime||0;return(G.startTime||0)-ce}).reduce((J,G)=>{const{tid:ce,...n}=G;return J[ce]=n,J},{})}),Ze=ct(()=>{const S=je.value,f=Object.entries(S).map(([n,i])=>({tid:n,...i})),J=f.filter(n=>n.status===3),G=f.filter(n=>n.status!==3),ce=[];if(G.forEach(n=>{ce.push({type:"task",...n})}),J.length>=2){const n="released-group-all",i=oe.value.has(n);ce.push({type:"group",groupId:n,isCollapsed:i,tasks:J,count:J.length})}else J.forEach(n=>{ce.push({type:"task",...n})});return ce}),He=S=>{oe.value.has(S)?oe.value.delete(S):oe.value.add(S)},Qe=(S="wps-analysis")=>{x.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(l.value="未选中内容",t.value=!0):(S==="wps-analysis"?T.value=!0:S==="wps-enhance_analysis"&&(E.value=!0),Ue(S).catch(f=>{console.log(f),f.message.includes("重叠")?(l.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("操作失败:",f),l.value=f.message,t.value=!0)}).finally(()=>{S==="wps-analysis"?T.value=!1:S==="wps-enhance_analysis"&&(E.value=!1)})):(l.value="请选择学科",t.value=!0)},ot=()=>{x.value?!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim()?(l.value="未选中内容",t.value=!0):(w.value=!0,Ae().catch(S=>{console.log(S),S.message.includes("重叠")?(l.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,t.value=!0):(console.error("校对操作失败:",S),l.value=S.message,t.value=!0)}).finally(()=>{w.value=!1})):(l.value="请选择学科",t.value=!0)},et=(S,f)=>{N.value=S,ke(S)},pt=S=>{b[S]&&(b[S].status=3),N.value===S&&(N.value=null),Me(S,!0)},it=async()=>{const S=Object.entries(b).filter(([f,J])=>J.status===2||J.status===4);if(S.length===0){l.value="没有可释放的任务",t.value=!0;return}try{if(await Fe(`确定要释放所有 ${S.length} 个可释放的任务吗？
此操作不可撤销。`)){let J=0;S.forEach(([G,ce])=>{b[G]&&(b[G].status=3,N.value===G&&(N.value=null),Me(G,!0),J++)}),l.value=`已成功释放 ${J} 个任务`,t.value=!0}}catch(f){console.error("释放任务时出错:",f),l.value="释放任务时出现错误",t.value=!0}},Ot=()=>{g.value=!g.value},kt=S=>S?S.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",Tt=S=>{const f=Ct(S);return f?kt(f):"无题目内容"},Ct=S=>{if(!S.selectedText)return"";const f=S.selectedText.split("\r").filter(G=>G.trim());if(f.length===1){const G=S.selectedText.split(`
`).filter(ce=>ce.trim());G.length>1&&f.splice(0,1,...G)}const J=f.map((G,ce)=>{const n=G.trim();return n.length>200,n});return J.length===1?f[0].trim():J.join(`
`)},ft=(S,f)=>{if(!ee.value)return;const J=S.target,G=Ct(f).toString();if(!G||G.trim()===""){console.log("题目内容为空，不显示tooltip");return}const ce=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${G.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,n=S.clientX,i=S.clientY;if(u.subjects.has(J)){const d=u.subjects.get(J);d.setContent(ce),d.setProps({getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:n,right:n})}),d.show();return}const r=yt(J,{content:ce,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:i,bottom:i,left:n,right:n}),onHidden:()=>{r.setProps({getReferenceClientRect:null})}});u.subjects.set(J,r),r.show()},y=S=>{const f=S.currentTarget,J=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,G=S.clientX,ce=S.clientY;if(u.enhance.has(f)){const i=u.enhance.get(f);i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ce,bottom:ce,left:G,right:G})}),i.show();return}const n=yt(f,{content:J,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});u.enhance.set(f,n),n.show()},W=()=>{u.subjects.forEach(S=>{S.destroy()}),u.subjects.clear(),u.enhance.forEach(S=>{S.destroy()}),u.enhance.clear(),u.softBreak.forEach(S=>{S.destroy()}),u.softBreak.clear(),document.removeEventListener("click",H),document.removeEventListener("mousemove",re)},H=S=>{const f=document.querySelector(".tippy-box");f&&!f.contains(S.target)&&(u.subjects.forEach(J=>J.hide()),u.enhance.forEach(J=>J.hide()),u.softBreak.forEach(J=>J.hide()))};let ie=0;const re=S=>{const f=Date.now();if(f-ie<100)return;ie=f;const J=document.querySelector(".tippy-box");if(!J)return;const G=J.getBoundingClientRect();!(S.clientX>=G.left-20&&S.clientX<=G.right+20&&S.clientY>=G.top-20&&S.clientY<=G.bottom+20)&&!J.matches(":hover")&&(u.subjects.forEach(n=>n.hide()),u.enhance.forEach(n=>n.hide()),u.softBreak.forEach(n=>n.hide()))},be=()=>{document.addEventListener("click",H),document.addEventListener("mousemove",re)};Fs(()=>{window.addEventListener("resize",Y),be(),xe(),setTimeout(()=>{Ve()},500);const S=document.createElement("style");S.id="tippy-custom-styles",S.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(S)}),un(()=>{window.removeEventListener("resize",Y),W();const S=document.getElementById("tippy-custom-styles");S&&S.remove()}),Oe();const Te=S=>S.selectedText?S.selectedText.includes("\v")||S.selectedText.includes("\v"):!1,ze=S=>{const f=S.currentTarget,J=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,G=S.clientX,ce=S.clientY;if(u.softBreak.has(f)){const i=u.softBreak.get(f);i.setProps({getReferenceClientRect:()=>({width:0,height:0,top:ce,bottom:ce,left:G,right:G})}),i.show();return}const n=yt(f,{content:J,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});u.softBreak.set(f,n),n.show()};return ct(()=>Ze.value.some(S=>S.type==="group")),ct(()=>oe.value.size>0),(S,f)=>{var J,G,ce,n,i;return F(),j("div",Br,[ue(Se)?(F(),j("div",Nr,f[31]||(f[31]=[c("div",{class:"loading-spinner"},null,-1),c("div",{class:"loading-text"},"处理中...",-1)]))):z("",!0),L.value?(F(),j("div",Vr,[c("div",Hr,[f[32]||(f[32]=c("div",{class:"format-error-icon"},"⚠️",-1)),f[33]||(f[33]=c("div",{class:"format-error-title"},"文档格式不支持",-1)),c("div",zr,ae(P.value),1),c("div",qr,[c("button",{class:"retry-check-btn",onClick:f[0]||(f[0]=r=>Ve())},"重新检查")])])])):z("",!0),c("div",Jr,[c("div",Yr,ae(ue(le)||"未选择文档"),1),c("button",{class:"settings-btn",onClick:f[1]||(f[1]=r=>a.value=!0)},f[34]||(f[34]=[c("i",{class:"icon-settings"},null,-1)]))]),c("div",Kr,[c("div",Xr,[c("div",Gr,[f[35]||(f[35]=c("label",{for:"stage-select"},"年级:",-1)),Je(c("select",{id:"stage-select","onUpdate:modelValue":f[2]||(f[2]=r=>gs(k)?k.value=r:null),class:"select-input",disabled:L.value},[(F(!0),j(mt,null,_t(ue(m),r=>(F(),j("option",{key:r.value,value:r.value},ae(r.label),9,Qr))),128))],8,Zr),[[Qt,ue(k)]])]),c("div",eo,[f[36]||(f[36]=c("label",{for:"subject-select"},"学科:",-1)),Je(c("select",{id:"subject-select","onUpdate:modelValue":f[3]||(f[3]=r=>gs(x)?x.value=r:null),class:"select-input",disabled:L.value},[(F(!0),j(mt,null,_t(Ge.value,r=>(F(),j("option",{key:r.value,value:r.value},ae(r.label),9,so))),128))],8,to),[[Qt,ue(x)]]),ge.value&&!ge.value.isAdmin&&!ge.value.isOwner&&ge.value.subject?(F(),j("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((J=Ge.value.find(r=>r.value===ge.value.subject))==null?void 0:J.label)||ge.value.subject} 学科`}," 🔒 ",8,no)):z("",!0)])]),rt.value?(F(),j("div",ao," 理科可使用增强模式以获取更精准的解析 ")):z("",!0),c("div",ro,[c("button",{class:"action-btn primary",onClick:f[4]||(f[4]=r=>Qe("wps-analysis")),disabled:T.value||L.value},[c("div",io,[T.value?(F(),j("span",lo)):z("",!0),f[37]||(f[37]=c("span",{class:"btn-text"},"解析",-1))])],8,oo),rt.value?(F(),j("button",{key:0,class:"action-btn enhance",onClick:f[5]||(f[5]=r=>Qe("wps-enhance_analysis")),disabled:E.value||L.value},[c("div",uo,[E.value?(F(),j("span",po)):z("",!0),f[38]||(f[38]=c("span",{class:"btn-text"},"增强解析",-1))])],8,co)):z("",!0),((ce=(G=ge.value)==null?void 0:G.orgs[0])==null?void 0:ce.orgId)===2?(F(),j("button",{key:1,class:"action-btn secondary",onClick:f[6]||(f[6]=r=>ot()),disabled:w.value||L.value},[c("div",ho,[w.value?(F(),j("span",vo)):z("",!0),f[39]||(f[39]=c("span",{class:"btn-text"},"校对",-1))])],8,fo)):z("",!0)])]),c("div",go,[s.value?(F(),j("div",{key:0,class:"modal-overlay",onClick:f[9]||(f[9]=r=>s.value=!1)},[c("div",{class:"modal-content",onClick:f[8]||(f[8]=ht(()=>{},["stop"]))},[c("div",mo,[f[40]||(f[40]=c("div",{class:"modal-title"},"选中内容",-1)),c("button",{class:"modal-close",onClick:f[7]||(f[7]=r=>s.value=!1)},"×")]),c("div",bo,[c("pre",wo,ae(ue(de)||"未选中内容"),1)])])])):z("",!0),t.value?(F(),j("div",{key:1,class:"modal-overlay",onClick:f[13]||(f[13]=r=>t.value=!1)},[c("div",{class:"modal-content alert-modal",onClick:f[12]||(f[12]=ht(()=>{},["stop"]))},[c("div",yo,[f[41]||(f[41]=c("div",{class:"modal-title"},"提示",-1)),c("button",{class:"modal-close",onClick:f[10]||(f[10]=r=>t.value=!1)},"×")]),c("div",xo,[c("div",ko,ae(l.value),1),c("div",To,[c("button",{class:"action-btn primary",onClick:f[11]||(f[11]=r=>t.value=!1)},"确定")])])])])):z("",!0),ue(Le).show?(F(),j("div",Co,[c("div",{class:"modal-content confirm-modal",onClick:f[17]||(f[17]=ht(()=>{},["stop"]))},[c("div",$o,[f[42]||(f[42]=c("div",{class:"modal-title"},"确认",-1)),c("button",{class:"modal-close",onClick:f[14]||(f[14]=r=>ue(X)(!1))},"×")]),c("div",So,[c("div",_o,ae(ue(Le).message),1),c("div",Eo,[c("button",{class:"action-btn secondary",onClick:f[15]||(f[15]=r=>ue(X)(!1))},"取消"),c("button",{class:"action-btn primary",onClick:f[16]||(f[16]=r=>ue(X)(!0))},"确定")])])])])):z("",!0),ue(Ie).show?(F(),j("div",{key:3,class:"modal-overlay",onClick:f[21]||(f[21]=r=>ue(ye)())},[c("div",{class:"modal-content alert-modal",onClick:f[20]||(f[20]=ht(()=>{},["stop"]))},[c("div",Ao,[c("div",Mo,ae(ue(Ie).title),1),c("button",{class:"modal-close",onClick:f[18]||(f[18]=r=>ue(ye)())},"×")]),c("div",Do,[c("div",Po,ae(ue(Ie).message),1),c("div",Oo,[c("button",{class:"action-btn primary",onClick:f[19]||(f[19]=r=>ue(ye)())},"确定")])])])])):z("",!0),c("div",Io,[c("div",Ro,[f[43]||(f[43]=c("div",{class:"queue-title"},"任务队列",-1)),c("div",Uo,[Je(c("select",{id:"status-filter-select","onUpdate:modelValue":f[22]||(f[22]=r=>ne.value=r),class:"status-filter-select-input"},[(F(!0),j(mt,null,_t(D.value,r=>(F(),j("option",{key:r.value,value:r.value},ae(r.label),9,Lo))),128))],512),[[Qt,ne.value]])]),c("div",Fo,[c("button",{class:"release-all-btn",onClick:it,disabled:ue(Xe)()===0,title:ue(Xe)()===0?"无可释放任务":`释放所有${ue(Xe)()}个可释放任务`}," 一键释放 ",8,jo)]),c("div",Wo,ae(Object.keys(_e.value).length)+"个任务",1)]),Object.keys(_e.value).length>0?(F(),j("div",Bo,[c("table",{class:$e(["queue-table",{"narrow-view":I.value,"ultra-narrow-view":B.value}])},[c("thead",null,[c("tr",null,[c("th",No,[c("div",Vo,[f[45]||(f[45]=c("span",null,"任务ID",-1)),c("span",{class:"help-icon",onMouseenter:f[23]||(f[23]=r=>y(r))},f[44]||(f[44]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),I.value?z("",!0):(F(),j("th",Ho,[c("div",zo,[f[46]||(f[46]=c("span",null,"题目内容",-1)),c("label",qo,[Je(c("input",{type:"checkbox","onUpdate:modelValue":f[24]||(f[24]=r=>ee.value=r)},null,512),[[dn,ee.value]]),c("span",{class:"slider round",title:ee.value?"关闭题目预览":"开启题目预览"},null,8,Jo)])])])),B.value?z("",!0):(F(),j("th",Yo,"状态")),f[47]||(f[47]=c("th",{class:"col-actions"},"操作",-1))])]),c("tbody",null,[(F(!0),j(mt,null,_t(Ze.value,r=>(F(),j(mt,{key:r.type==="group"?r.groupId:r.tid},[r.type==="group"?(F(),j("tr",{key:0,class:"group-row",onClick:d=>He(r.groupId)},[c("td",Xo,[c("div",Go,[c("span",Zo,ae(r.isCollapsed?"▶":"▼"),1),c("span",Qo,"已释放任务组 ("+ae(r.count)+"个)",1)])]),I.value?z("",!0):(F(),j("td",ei,[c("div",ti,ae(r.isCollapsed?"点击展开查看详情":"点击折叠隐藏详情"),1)])),B.value?z("",!0):(F(),j("td",si,f[48]||(f[48]=[c("div",{class:"status-cell"},[c("span",{class:"task-tag status-released"},"已释放")],-1)]))),c("td",ni,[c("div",ai,[c("span",ri,ae(r.isCollapsed?"展开":"折叠"),1)])])],8,Ko)):z("",!0),r.type==="group"&&!r.isCollapsed?(F(!0),j(mt,{key:1},_t(r.tasks,(d,o)=>(F(),j("tr",{key:d.tid,class:$e(["task-row group-task-row",[ue(ve)(d.status),{"selected-task-row":d.tid===N.value}]]),onClick:v=>et(d.tid)},[c("td",ii,[c("div",{class:$e(["id-cell",{"id-with-status":B.value}])},[c("div",li,[f[50]||(f[50]=c("span",{class:"group-indent"},"└─",-1)),c("span",ci,ae(d.tid.substring(0,8)),1),d.wordType==="wps-analysis"?(F(),j("span",ui," 解 ")):z("",!0),d.wordType==="wps-enhance_analysis"||d.isEnhanced?(F(),j("span",di," 解 ")):z("",!0),d.wordType==="wps-check"||d.isCheckTask?(F(),j("span",pi," 校 ")):z("",!0),Te(d)?(F(),j("span",{key:3,class:"soft-break-warning-icon",onMouseenter:f[25]||(f[25]=v=>ze(v))},f[49]||(f[49]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("title",null,"提示"),c("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),c("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):z("",!0)]),B.value?(F(),j("div",fi,[c("span",{class:$e(["task-tag compact",ue(ve)(d.status)])},ae(ue(me)(d.status)),3)])):z("",!0)],2)]),I.value?z("",!0):(F(),j("td",hi,[c("div",{class:"subject-cell",onMouseenter:v=>ft(v,d)},ae(Tt(d)),41,vi)])),B.value?z("",!0):(F(),j("td",gi,[c("div",mi,[c("span",{class:$e(["task-tag",ue(ve)(d.status)])},ae(ue(me)(d.status)),3)])])),f[51]||(f[51]=c("td",{class:"col-actions"},[c("div",{class:"task-actions"},[c("span",{class:"no-action-icon",title:"无可用操作"},[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),c("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})])])])],-1))],10,oi))),128)):z("",!0),r.type==="task"?(F(),j("tr",{key:2,class:$e(["task-row",[ue(ve)(r.status),{"selected-task-row":r.tid===N.value}]]),onClick:d=>et(r.tid)},[c("td",wi,[c("div",{class:$e(["id-cell",{"id-with-status":B.value}])},[c("div",yi,[c("span",xi,ae(r.tid.substring(0,8)),1),!r.isEnhanced&&!r.isCheckTask?(F(),j("span",ki," 解 ")):z("",!0),r.isEnhanced?(F(),j("span",Ti," 解 ")):z("",!0),r.isCheckTask?(F(),j("span",Ci," 校 ")):z("",!0),Te(r)?(F(),j("span",{key:3,class:"soft-break-warning-icon",onMouseenter:f[26]||(f[26]=d=>ze(d))},f[52]||(f[52]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[c("title",null,"提示"),c("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),c("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),c("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):z("",!0)]),B.value?(F(),j("div",$i,[c("span",{class:$e(["task-tag compact",ue(ve)(r.status)])},ae(ue(me)(r.status)),3)])):z("",!0)],2)]),I.value?z("",!0):(F(),j("td",Si,[c("div",{class:"subject-cell",onMouseenter:d=>ft(d,r)},ae(Tt(r)),41,_i)])),B.value?z("",!0):(F(),j("td",Ei,[c("div",Ai,[c("span",{class:$e(["task-tag",ue(ve)(r.status)])},ae(ue(me)(r.status)),3)])])),c("td",Mi,[c("div",Di,[r.status===1?(F(),j("button",{key:0,onClick:ht(d=>ue(Ee)(r.tid),["stop"]),class:"terminate-btn"}," 终止 ",8,Pi)):z("",!0),r.status===2||r.status===4?(F(),j("button",{key:1,onClick:ht(d=>pt(r.tid),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,Oi)):z("",!0),r.status!==1&&r.status!==2&&r.status!==4?(F(),j("span",Ii,f[53]||(f[53]=[c("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),c("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):z("",!0)])])],10,bi)):z("",!0)],64))),128))])],2)])):(F(),j("div",Ri,f[54]||(f[54]=[c("div",{class:"empty-text"},"暂无任务",-1)])))]),((i=(n=ge.value)==null?void 0:n.orgs[0])==null?void 0:i.orgId)===2?(F(),j("div",Ui,[c("div",{class:"log-header",onClick:Ot},[f[55]||(f[55]=c("div",{class:"log-title"},"执行日志",-1)),c("div",Li,[c("button",{class:"clear-btn",onClick:f[27]||(f[27]=ht(r=>ue(_)(),["stop"]))},"清空日志"),c("span",Fi,ae(g.value?"▼":"▶"),1)])]),g.value?(F(),j("div",{key:0,class:"log-content",innerHTML:ue(h)},null,8,ji)):z("",!0)])):z("",!0)]),a.value?(F(),j("div",{key:2,class:"modal-overlay",onClick:f[30]||(f[30]=r=>a.value=!1)},[c("div",{class:"modal-content",onClick:f[29]||(f[29]=ht(()=>{},["stop"]))},[pn(da,{onClose:f[28]||(f[28]=r=>a.value=!1)})])])):z("",!0)])}}},Ni=js(Wi,[["__scopeId","data-v-c27526fc"]]);export{Ni as default};
