import{U as $n,r as he,h as St,v as it,i as se,j as zt,k as en,m as rs,_ as tn,n as Sn,o as F,c as j,a as u,p as En,t as ae,f as z,q as _e,w as Ge,s as es,e as ps,F as Et,u as It,x as vt,y as _n,z as ie,A as Ds,B as ds,C as fs,D as Tt,E as An,G as ts}from"./index--1vStR4x.js";function Mn(t,n){switch(typeof window.Application.Enum!="object"&&(window.Application.Enum=$n.WPS_Enum),t){case"dockLeft":{let e=window.Application.PluginStorage.getItem("taskpane_id");if(e){let a=window.Application.GetTaskPane(e);a.DockPosition=window.Application.Enum.msoCTPDockPositionLeft}break}case"dockRight":{let e=window.Application.PluginStorage.getItem("taskpane_id");if(e){let a=window.Application.GetTaskPane(e);a.DockPosition=window.Application.Enum.msoCTPDockPositionRight}break}case"hideTaskPane":{let e=window.Application.PluginStorage.getItem("taskpane_id");if(e){let a=window.Application.GetTaskPane(e);a.Visible=!1}break}case"addString":{let e=window.Application.ActiveDocument;if(e){e.Range(0,0).Text="Hello, wps加载项!";let a=window.Application.Selection.Range;a&&a.Select()}break}case"getDocName":{let e=window.Application.ActiveDocument;return e?e.Name:"当前没有打开任何文档"}}}const Pn={onbuttonclick:Mn};var Dn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function On(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Rn(t){if(t.__esModule)return t;var n=t.default;if(typeof n=="function"){var e=function a(){return this instanceof a?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};e.prototype=n.prototype}else e={};return Object.defineProperty(e,"__esModule",{value:!0}),Object.keys(t).forEach(function(a){var i=Object.getOwnPropertyDescriptor(t,a);Object.defineProperty(e,a,i.get?i:{enumerable:!0,get:function(){return t[a]}})}),e}var sn={exports:{}};const In={},Un=Object.freeze(Object.defineProperty({__proto__:null,default:In},Symbol.toStringTag,{value:"Module"})),Os=Rn(Un);/*
 * [js-sha1]{@link https://github.com/emn178/js-sha1}
 *
 * @version 0.7.0
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2024
 * @license MIT
 */(function(t){(function(){var n="input is invalid type",e="finalize already called",a=typeof window=="object",i=a?window:{};i.JS_SHA1_NO_WINDOW&&(a=!1);var v=!a&&typeof self=="object",x=!i.JS_SHA1_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;x?i=Dn:v&&(i=self);var M=!i.JS_SHA1_NO_COMMON_JS&&!0&&t.exports,b=!i.JS_SHA1_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",T="0123456789abcdef".split(""),P=[-**********,8388608,32768,128],L=[24,16,8,0],q=["hex","array","digest","arrayBuffer"],R=[],V=Array.isArray;(i.JS_SHA1_NO_NODE_JS||!V)&&(V=function(f){return Object.prototype.toString.call(f)==="[object Array]"});var J=ArrayBuffer.isView;b&&(i.JS_SHA1_NO_ARRAY_BUFFER_IS_VIEW||!J)&&(J=function(f){return typeof f=="object"&&f.buffer&&f.buffer.constructor===ArrayBuffer});var H=function(f){var w=typeof f;if(w==="string")return[f,!0];if(w!=="object"||f===null)throw new Error(n);if(b&&f.constructor===ArrayBuffer)return[new Uint8Array(f),!1];if(!V(f)&&!J(f))throw new Error(n);return[f,!1]},oe=function(f){return function(w){return new I(!0).update(w)[f]()}},le=function(){var f=oe("hex");x&&(f=de(f)),f.create=function(){return new I},f.update=function(C){return f.create().update(C)};for(var w=0;w<q.length;++w){var E=q[w];f[E]=oe(E)}return f},de=function(f){var w=Os,E=Os.Buffer,C;E.from&&!i.JS_SHA1_NO_BUFFER_FROM?C=E.from:C=function(m){return new E(m)};var S=function(m){if(typeof m=="string")return w.createHash("sha1").update(m,"utf8").digest("hex");if(m==null)throw new Error(n);return m.constructor===ArrayBuffer&&(m=new Uint8Array(m)),V(m)||J(m)||m.constructor===E?w.createHash("sha1").update(C(m)).digest("hex"):f(m)};return S},d=function(f){return function(w,E){return new ce(w,!0).update(E)[f]()}},ne=function(){var f=d("hex");f.create=function(C){return new ce(C)},f.update=function(C,S){return f.create(C).update(S)};for(var w=0;w<q.length;++w){var E=q[w];f[E]=d(E)}return f};function I(f){f?(R[0]=R[16]=R[1]=R[2]=R[3]=R[4]=R[5]=R[6]=R[7]=R[8]=R[9]=R[10]=R[11]=R[12]=R[13]=R[14]=R[15]=0,this.blocks=R):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=1732584193,this.h1=4023233417,this.h2=2562383102,this.h3=271733878,this.h4=3285377520,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}I.prototype.update=function(f){if(this.finalized)throw new Error(e);var w=H(f);f=w[0];for(var E=w[1],C,S=0,m,U=f.length||0,A=this.blocks;S<U;){if(this.hashed&&(this.hashed=!1,A[0]=this.block,this.block=A[16]=A[1]=A[2]=A[3]=A[4]=A[5]=A[6]=A[7]=A[8]=A[9]=A[10]=A[11]=A[12]=A[13]=A[14]=A[15]=0),E)for(m=this.start;S<U&&m<64;++S)C=f.charCodeAt(S),C<128?A[m>>>2]|=C<<L[m++&3]:C<2048?(A[m>>>2]|=(192|C>>>6)<<L[m++&3],A[m>>>2]|=(128|C&63)<<L[m++&3]):C<55296||C>=57344?(A[m>>>2]|=(224|C>>>12)<<L[m++&3],A[m>>>2]|=(128|C>>>6&63)<<L[m++&3],A[m>>>2]|=(128|C&63)<<L[m++&3]):(C=65536+((C&1023)<<10|f.charCodeAt(++S)&1023),A[m>>>2]|=(240|C>>>18)<<L[m++&3],A[m>>>2]|=(128|C>>>12&63)<<L[m++&3],A[m>>>2]|=(128|C>>>6&63)<<L[m++&3],A[m>>>2]|=(128|C&63)<<L[m++&3]);else for(m=this.start;S<U&&m<64;++S)A[m>>>2]|=f[S]<<L[m++&3];this.lastByteIndex=m,this.bytes+=m-this.start,m>=64?(this.block=A[16],this.start=m-64,this.hash(),this.hashed=!0):this.start=m}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this},I.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var f=this.blocks,w=this.lastByteIndex;f[16]=this.block,f[w>>>2]|=P[w&3],this.block=f[16],w>=56&&(this.hashed||this.hash(),f[0]=this.block,f[16]=f[1]=f[2]=f[3]=f[4]=f[5]=f[6]=f[7]=f[8]=f[9]=f[10]=f[11]=f[12]=f[13]=f[14]=f[15]=0),f[14]=this.hBytes<<3|this.bytes>>>29,f[15]=this.bytes<<3,this.hash()}},I.prototype.hash=function(){var f=this.h0,w=this.h1,E=this.h2,C=this.h3,S=this.h4,m,U,A,K=this.blocks;for(U=16;U<80;++U)A=K[U-3]^K[U-8]^K[U-14]^K[U-16],K[U]=A<<1|A>>>31;for(U=0;U<20;U+=5)m=w&E|~w&C,A=f<<5|f>>>27,S=A+m+S+1518500249+K[U]<<0,w=w<<30|w>>>2,m=f&w|~f&E,A=S<<5|S>>>27,C=A+m+C+1518500249+K[U+1]<<0,f=f<<30|f>>>2,m=S&f|~S&w,A=C<<5|C>>>27,E=A+m+E+1518500249+K[U+2]<<0,S=S<<30|S>>>2,m=C&S|~C&f,A=E<<5|E>>>27,w=A+m+w+1518500249+K[U+3]<<0,C=C<<30|C>>>2,m=E&C|~E&S,A=w<<5|w>>>27,f=A+m+f+1518500249+K[U+4]<<0,E=E<<30|E>>>2;for(;U<40;U+=5)m=w^E^C,A=f<<5|f>>>27,S=A+m+S+1859775393+K[U]<<0,w=w<<30|w>>>2,m=f^w^E,A=S<<5|S>>>27,C=A+m+C+1859775393+K[U+1]<<0,f=f<<30|f>>>2,m=S^f^w,A=C<<5|C>>>27,E=A+m+E+1859775393+K[U+2]<<0,S=S<<30|S>>>2,m=C^S^f,A=E<<5|E>>>27,w=A+m+w+1859775393+K[U+3]<<0,C=C<<30|C>>>2,m=E^C^S,A=w<<5|w>>>27,f=A+m+f+1859775393+K[U+4]<<0,E=E<<30|E>>>2;for(;U<60;U+=5)m=w&E|w&C|E&C,A=f<<5|f>>>27,S=A+m+S-1894007588+K[U]<<0,w=w<<30|w>>>2,m=f&w|f&E|w&E,A=S<<5|S>>>27,C=A+m+C-1894007588+K[U+1]<<0,f=f<<30|f>>>2,m=S&f|S&w|f&w,A=C<<5|C>>>27,E=A+m+E-1894007588+K[U+2]<<0,S=S<<30|S>>>2,m=C&S|C&f|S&f,A=E<<5|E>>>27,w=A+m+w-1894007588+K[U+3]<<0,C=C<<30|C>>>2,m=E&C|E&S|C&S,A=w<<5|w>>>27,f=A+m+f-1894007588+K[U+4]<<0,E=E<<30|E>>>2;for(;U<80;U+=5)m=w^E^C,A=f<<5|f>>>27,S=A+m+S-899497514+K[U]<<0,w=w<<30|w>>>2,m=f^w^E,A=S<<5|S>>>27,C=A+m+C-899497514+K[U+1]<<0,f=f<<30|f>>>2,m=S^f^w,A=C<<5|C>>>27,E=A+m+E-899497514+K[U+2]<<0,S=S<<30|S>>>2,m=C^S^f,A=E<<5|E>>>27,w=A+m+w-899497514+K[U+3]<<0,C=C<<30|C>>>2,m=E^C^S,A=w<<5|w>>>27,f=A+m+f-899497514+K[U+4]<<0,E=E<<30|E>>>2;this.h0=this.h0+f<<0,this.h1=this.h1+w<<0,this.h2=this.h2+E<<0,this.h3=this.h3+C<<0,this.h4=this.h4+S<<0},I.prototype.hex=function(){this.finalize();var f=this.h0,w=this.h1,E=this.h2,C=this.h3,S=this.h4;return T[f>>>28&15]+T[f>>>24&15]+T[f>>>20&15]+T[f>>>16&15]+T[f>>>12&15]+T[f>>>8&15]+T[f>>>4&15]+T[f&15]+T[w>>>28&15]+T[w>>>24&15]+T[w>>>20&15]+T[w>>>16&15]+T[w>>>12&15]+T[w>>>8&15]+T[w>>>4&15]+T[w&15]+T[E>>>28&15]+T[E>>>24&15]+T[E>>>20&15]+T[E>>>16&15]+T[E>>>12&15]+T[E>>>8&15]+T[E>>>4&15]+T[E&15]+T[C>>>28&15]+T[C>>>24&15]+T[C>>>20&15]+T[C>>>16&15]+T[C>>>12&15]+T[C>>>8&15]+T[C>>>4&15]+T[C&15]+T[S>>>28&15]+T[S>>>24&15]+T[S>>>20&15]+T[S>>>16&15]+T[S>>>12&15]+T[S>>>8&15]+T[S>>>4&15]+T[S&15]},I.prototype.toString=I.prototype.hex,I.prototype.digest=function(){this.finalize();var f=this.h0,w=this.h1,E=this.h2,C=this.h3,S=this.h4;return[f>>>24&255,f>>>16&255,f>>>8&255,f&255,w>>>24&255,w>>>16&255,w>>>8&255,w&255,E>>>24&255,E>>>16&255,E>>>8&255,E&255,C>>>24&255,C>>>16&255,C>>>8&255,C&255,S>>>24&255,S>>>16&255,S>>>8&255,S&255]},I.prototype.array=I.prototype.digest,I.prototype.arrayBuffer=function(){this.finalize();var f=new ArrayBuffer(20),w=new DataView(f);return w.setUint32(0,this.h0),w.setUint32(4,this.h1),w.setUint32(8,this.h2),w.setUint32(12,this.h3),w.setUint32(16,this.h4),f};function ce(f,w){var E,C=H(f);if(f=C[0],C[1]){var S=[],m=f.length,U=0,A;for(E=0;E<m;++E)A=f.charCodeAt(E),A<128?S[U++]=A:A<2048?(S[U++]=192|A>>>6,S[U++]=128|A&63):A<55296||A>=57344?(S[U++]=224|A>>>12,S[U++]=128|A>>>6&63,S[U++]=128|A&63):(A=65536+((A&1023)<<10|f.charCodeAt(++E)&1023),S[U++]=240|A>>>18,S[U++]=128|A>>>12&63,S[U++]=128|A>>>6&63,S[U++]=128|A&63);f=S}f.length>64&&(f=new I(!0).update(f).array());var K=[],me=[];for(E=0;E<64;++E){var ye=f[E]||0;K[E]=92^ye,me[E]=54^ye}I.call(this,w),this.update(me),this.oKeyPad=K,this.inner=!0,this.sharedMemory=w}ce.prototype=new I,ce.prototype.finalize=function(){if(I.prototype.finalize.call(this),this.inner){this.inner=!1;var f=this.array();I.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(f),I.prototype.finalize.call(this)}};var ge=le();ge.sha1=ge,ge.sha1.hmac=ne(),M?t.exports=ge:i.sha1=ge})()})(sn);var Ln=sn.exports;const Fn=On(Ln);function Rs(){return"http://worksheet.hexinedu.com"}function Ot(){return"http://127.0.0.1:3000"}function Is(){let t=new Date().getTime();return typeof performance<"u"&&typeof performance.now=="function"&&(t+=performance.now()),"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){const e=(t+Math.random()*16)%16|0;return t=Math.floor(t/16),(n=="x"?e:e&3|8).toString(16)})}const Rt=async(t,n,e,a={},i=8e3)=>{try{return await Promise.race([t(),new Promise((v,x)=>setTimeout(()=>x(new Error("WebSocket请求超时，切换到HTTP")),i))])}catch{try{let x;return n==="get"?x=await zt.get(e,{params:a}):n==="post"?x=await zt.post(e,a):n==="delete"&&(x=await zt.delete(e)),x.data}catch(x){throw new Error(`请求失败: ${x.message||"未知错误"}`)}}};function jn(t,n,e,a){const i=[t,n,e,a].join(":");return Fn(i)}function Wn(){const t=he(""),n=he(""),e=he(""),a=St({}),i=he(""),v=he("");let x="",M=null;const b=he("c:\\Temp"),T=St({appKey:"",appSecret:""}),P=St({show:!1,message:"",resolveCallback:null,rejectCallback:null}),L=St({show:!1,title:"",message:"",type:"error"}),q=he(""),R=he("junior"),V=he(null),J=he(!1),H=he(!1),oe=async()=>{const s=Object.keys(a).filter(c=>a[c].status===5);if(s.length===0){e.value+='<span class="log-item warning">没有等待批量插入的任务</span><br/>';return}e.value+=`<span class="log-item info">开始批量插入 ${s.length} 个任务</span><br/>`,s.forEach(c=>{a[c]&&(a[c].resultFile||(a[c].resultFile="C:\\ww-wps-addon\\Downloads\\682ad7b4bde591ec60b7cf56.wps.docx"),a[c].status=1,e.value+=`<span class="log-item info">任务 ${c.substring(0,8)} 开始插入文档</span><br/>`)})},le=()=>Object.keys(a).filter(s=>a[s].status===5).length,de=[{value:"english",label:"英语"},{value:"chinese",label:"语文"},{value:"math",label:"数学"},{value:"physics",label:"物理"},{value:"chemistry",label:"化学"},{value:"biology",label:"生物"},{value:"daode_fazhi",label:"道德与法治"},{value:"history",label:"历史"},{value:"geography",label:"地理"}],d=()=>it.isSeniorEdition()?[{value:"senior",label:"高中"}]:[{value:"junior",label:"初中"}],ne=St(d()),I=async()=>{try{const s=await se.getWatcherStatus();s.data&&s.data.watchDir&&(b.value=s.data.watchDir,e.value+=`<span class="log-item info">已获取监控目录: ${b.value}</span><br/>`)}catch(s){e.value+=`<span class="log-item error">获取监控目录失败: ${s.message}</span><br/>`,console.error("获取监控目录失败:",s)}},ce=async()=>{var s,c,r;try{if(!T.appKey||!T.appSecret)throw new Error("未初始化app信息");const h=60,o=Date.now();let l;try{const X=window.Application.PluginStorage.getItem("token_info");X&&(l=JSON.parse(X))}catch(X){l=null,e.value+=`<span class="log-item warning">解析缓存token失败: ${X.message}</span><br/>`}if(l&&l.access_token&&l.expired_time>o+h*1e3)return l.access_token;const p=T.appKey,D="1234567",$=Math.floor(Date.now()/1e3),k=jn(p,D,T.appSecret,$),B=await zt.get(Rs()+"/api/open/account/v1/auth/token",{params:{app_key:p,app_nonstr:D,app_timestamp:$,app_signature:k}});if((c=(s=B.data)==null?void 0:s.data)!=null&&c.access_token){const X=B.data.data.access_token;let Y;if(B.data.data.expired_time){const re=parseInt(B.data.data.expired_time);Y=o+re*1e3,e.value+=`<span class="log-item info">token已更新，有效期${re}秒</span><br/>`}else Y=o+3600*1e3,e.value+='<span class="log-item warning">无法获取token过期时间，设置默认过期时间1小时</span><br/>';const Z={access_token:X,expired_time:Y};try{window.Application.PluginStorage.setItem("token_info",JSON.stringify(Z))}catch(re){e.value+=`<span class="log-item warning">保存token到PluginStorage失败: ${re.message}</span><br/>`}return X}else throw new Error(((r=B.data)==null?void 0:r.message)||"获取access_token失败")}catch(h){throw e.value+=`<span class="log-item error">获取access_token失败: ${h.message}</span><br/>`,h}},ge=()=>{e.value='<span class="log-item info">日志已清空</span><br/>'},f=()=>{const s=window.Application,c=s.Documents.Count;for(let r=1;r<=c;r++){const h=s.Documents.Item(r);if(h.DocID===i.value)return h}return null},w=()=>{try{const s=f();if(!s)return{isValid:!1,message:"未找到当前文档"};let c="";try{c=s.Name||""}catch{try{c=K("getDocName")||""}catch{c=""}}if(c){const r=c.toLowerCase();return r.endsWith(".docx")?{isValid:!0,message:"文档格式正确"}:r.endsWith(".doc")?{isValid:!1,message:`当前文档是 .doc 格式，该插件只能服务于 .docx 文件。

建议操作：
1. 点击"文件" → "另存为"
2. 在"保存类型"中选择"Word 文档(*.docx)"
3. 保存后重新打开 .docx 文件`}:{isValid:!1,message:`该插件只能服务于 .docx 文件，当前文档格式不支持。

请使用 .docx 格式的文档。`}}return{isValid:!1,message:"无法确定文档格式，请确保当前文档已保存为 .docx 格式。"}}catch(s){return console.error("检查文档格式时出错:",s),{isValid:!1,message:"检查文档格式时出错，请确保当前文档已保存为 .docx 格式。"}}},E=async(s,c="",r=0,h=null)=>await O("添加批注",async()=>{const o=window.Application,l=f();let p;if(h)p=h;else{const D=l.ActiveWindow.Selection;if(!D||D.Text==="")return e.value+='<span class="log-item error">请先选择文本</span><br/>',!1;p=D.Range}if(c){const D=p.Text,$=p.Find;$.ClearFormatting(),$.Text=c,$.Forward=!0,$.Wrap=0;let k=0,B=[];const X=p.Start,Y=p.End;for(;$.Execute()&&($.Found&&$.Parent.Start>=X&&$.Parent.End<=Y);){const Z=$.Parent.Start,re=$.Parent.End;if(B.some(we=>Z===we.start&&re===we.end)){const we=Math.min(re,Y);if(we>=Y)break;$.Parent.SetRange(we,Y)}else{if(B.push({start:Z,end:re}),r===-1||k===r){const Ce=l.Comments.Add($.Parent,s);try{Ce&&Ce.Range&&Ce.Range.ParagraphFormat&&(Ce.Range.ParagraphFormat.Reset(),Ce.Range.ParagraphFormat.LineSpacingRule=3,Ce.Range.ParagraphFormat.LineSpacing=10)}catch(Be){e.value+=`<span class="log-item warning">设置批注段落格式失败: ${Be.message}</span><br/>`}if(r!==-1&&k===r)return!0}k++;const we=Math.min(re,Y);if(console.log("nextStart",we),we>=Y)break;const gt=l.Range(we,Y);$.Parent.SetRange(we,Y)}}return r!==-1&&k<=r?!1:r===-1&&k>0?!0:!(r===-1&&k===0)}else{const D=l.Comments.Add(p,s);try{D&&D.Range&&D.Range.ParagraphFormat&&(D.Range.ParagraphFormat.Reset(),D.Range.ParagraphFormat.LineSpacingRule=3,D.Range.ParagraphFormat.LineSpacing=10)}catch($){e.value+=`<span class="log-item warning">设置批注段落格式失败: ${$.message}</span><br/>`}return e.value+=`<span class="log-item success">已为${h?"指定范围":"选中内容"}添加批注: "${s}"</span><br/>`,!0}}),C=s=>s===0?"status-preparing":s===1?"status-running":s===2?"status-completed":s===-1?"status-error":s===3?"status-released":s===4?"status-stopped":s===5?"status-batch-waiting":"",S=s=>s===0?"准备中":s===1?"进行中":s===2?"已完成":s===-1?"异常":s===3?"已释放":s===4?"已停止":s===5?"等待批量插入":"准备中",m=s=>{const c=Date.now()-s,r=Math.floor(c/1e3);return r<60?`${r}秒`:r<3600?`${Math.floor(r/60)}分${r%60}秒`:`${Math.floor(r/3600)}时${Math.floor(r%3600/60)}分`},U=async s=>{try{if(!a[s]){e.value+=`<span class="log-item error">找不到任务${s.substring(0,8)}的数据</span><br/>`;return}a[s].status=4,a[s].terminated=!0,a[s].errorMessage="用户选择不继续";try{const r=f();if(r&&r.ContentControls)for(let h=1;h<=r.ContentControls.Count;h++)try{const o=r.ContentControls.Item(h);if(o&&o.Title&&(o.Title===`任务_${s}`||o.Title===`任务增强_${s}`||o.Title===`校对_${s}`)){const l=o.Title===`任务增强_${s}`||a[s].isEnhanced,p=o.Title===`校对_${s}`||a[s].isCheckTask;p?o.Title=`已停止校对_${s}`:l?o.Title=`已停止增强_${s}`:o.Title=`已停止_${s}`;const D=p?"校对":l?"增强":"普通";e.value+=`<span class="log-item info">已将${D}任务${s.substring(0,8)}控件标记为已停止（保留控件）</span><br/>`;break}}catch{continue}}catch(r){e.value+=`<span class="log-item warning">更新控件标题失败: ${r.message}</span><br/>`}let c=null;if(Q[s]&&Q[s].urlId){c=Q[s].urlId;try{try{const r=await Rt(async()=>await se.getUrlMonitorStatus(),"get",`${Ot()}/api/url/status`),o=(Array.isArray(r)?r:r.data?Array.isArray(r.data)?r.data:[]:[]).find(l=>l.urlId===c);o&&o.downloadedPath&&(a[s].resultFile=o.downloadedPath,a[s].resultDownloaded=!0,e.value+=`<span class="log-item success">发现任务${s.substring(0,8)}已下载文件: ${o.downloadedPath}</span><br/>`)}catch(r){e.value+=`<span class="log-item warning">检查URL下载状态出错: ${r.message}</span><br/>`}e.value+=`<span class="log-item info">停止任务${s.substring(0,8)}的URL监控</span><br/>`,await Ue(c),delete Q[s]}catch(r){e.value+=`<span class="log-item warning">停止URL监控出错: ${r.message}，将重试</span><br/>`,delete Q[s],setTimeout(async()=>{try{c&&await Rt(async()=>await se.stopUrlMonitoring(c),"delete",`${Ot()}/api/url/monitor/${c}`)}catch(h){e.value+=`<span class="log-item warning">重试停止URL监控失败: ${h.message}</span><br/>`}},1e3)}}e.value+=`<span class="log-item success">任务${s.substring(0,8)}已停止（控件已保留）</span><br/>`}catch(c){e.value+=`<span class="log-item error">停止任务${s.substring(0,8)}出错: ${c.message}</span><br/>`,Q[s]&&delete Q[s]}},A=async s=>{if(!a[s]){e.value+=`<span class="log-item error">找不到任务${s.substring(0,8)}的数据</span><br/>`;return}a[s].status=4,a[s].terminated=!0,a[s].errorMessage="用户手动终止";const c=f();if(c&&c.ContentControls)for(let h=1;h<=c.ContentControls.Count;h++)try{const o=c.ContentControls.Item(h);if(o&&o.Title&&(o.Title===`任务_${s}`||o.Title===`任务增强_${s}`||o.Title===`校对_${s}`)){const l=o.Title===`任务增强_${s}`||a[s].isEnhanced,p=o.Title===`校对_${s}`||a[s].isCheckTask;p?o.Title=`已停止校对_${s}`:l?o.Title=`已停止增强_${s}`:o.Title=`已停止_${s}`,o.LockContents=!1;const D=p?"校对":l?"增强":"普通";e.value+=`<span class="log-item info">已将${D}任务${s.substring(0,8)}控件标记为已停止</span><br/>`;break}}catch{continue}let r=null;if(Q[s]&&Q[s].urlId){r=Q[s].urlId;try{const h=await se.getUrlMonitorStatus(),l=(Array.isArray(h)?h:h.data?Array.isArray(h.data)?h.data:[]:[]).find(p=>p.urlId===r);l&&l.downloadedPath&&(a[s].resultFile=l.downloadedPath,a[s].resultDownloaded=!0,e.value+=`<span class="log-item success">发现任务${s.substring(0,8)}已下载文件: ${l.downloadedPath}</span><br/>`),e.value+=`<span class="log-item info">停止任务${s.substring(0,8)}的URL监控</span><br/>`,await Ue(r),delete Q[s]}catch(h){e.value+=`<span class="log-item warning">停止URL监控出错: ${h.message}，将重试</span><br/>`,delete Q[s]}}},K=s=>Pn.onbuttonclick(s),me=()=>{try{t.value=K("getDocName")||"未命名文档"}catch{t.value="未命名文档"}},ye=()=>{f().ActiveWindow.Selection.Copy()},Oe=s=>{const c=window.Application.Documents.Add();c.Content.Paste(),c.SaveAs2(`${b.value}\\${s}`,12,"","",!1),c.Close(),f().ActiveWindow.Activate()},tt=s=>{const c=window.Application.Documents.Add("",!1,0,!1);c.Content.Paste(),c.SaveAs2(`${b.value}\\${s}`,12,"","",!1),c.Close(),f().ActiveWindow.Activate()},Re=s=>{try{const r=`${window.Application.Env.GetAppDataPath()}\\wps-addon-server\\temp_docx`,h=window.Application.Documents.Add("",!1,0,!1);h.Content.Paste();const o=`${r}\\${s}`;h.SaveAs2(o,12,"","",!1),h.Close(),f().ActiveWindow.Activate(),e.value+=`<span class="log-item success">文件已保存到中转目录: ${o}.docx</span><br/>`}catch(c){throw e.value+=`<span class="log-item error">方式三保存失败: ${c.message}</span><br/>`,c}},st=s=>{const c=window.Application.Documents.Add();c.Content.Paste(),c.SaveAs2(`${b.value}\\${s}`,12,"","",!1),f().ActiveWindow.Activate()},Ie=async s=>{try{e.value+=`<span class="log-item info">开始生成文档: ${s}</span><br/>`;let c="method3";try{const r=await se.getSaveMethod();if(r.success&&r.saveMethod){c=r.saveMethod;const h=c==="method1"?"方式一":c==="method2"?"方式二":c==="method3"?"方式三":"方式四";e.value+=`<span class="log-item info">使用保存方式: ${h}</span><br/>`}}catch(r){e.value+=`<span class="log-item warning">获取保存方式失败，使用默认方式三: ${r.message}</span><br/>`}c==="method1"?(Oe(s),e.value+=`<span class="log-item success">文件已通过方式一保存到监控目录: ${b.value}\\${s}.docx</span><br/>`):c==="method2"?(tt(s),e.value+=`<span class="log-item success">文件已通过方式二保存到监控目录: ${b.value}\\${s}.docx</span><br/>`):c==="method3"?(Re(s),e.value+='<span class="log-item success">文件已通过方式三保存到中转目录，等待后端转移到监控目录</span><br/>'):c==="method4"&&(st(s),e.value+=`<span class="log-item success">文件已通过方式四保存到监控目录: ${b.value}\\${s}.docx</span><br/>`),se.associateFileWithClient(`${s}.docx`).then(r=>{r.success?e.value+=`<span class="log-item info">文件 ${s}.docx 已关联到当前客户端</span><br/>`:e.value+=`<span class="log-item warning">关联文件失败: ${r.message||"未知错误"}</span><br/>`}).catch(r=>{e.value+=`<span class="log-item warning">关联文件时出错: ${r.message}</span><br/>`})}catch(c){e.value+=`<span class="log-item error">保存文件失败: ${c.message}</span><br/>`}},Ae=he(null),Se=St([]),Me=new Set,Pe=s=>!s||typeof s!="string"?"":s.replace(/<br\s*\/?>/gi,`
`).replace(/<[^>]*>/g,"").replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/\s+/g," ").replace(/\n\s*\n/g,`
`).trim(),Ye=async s=>{try{const c=s.slice(x.length);if(c.trim()){const r=se.getClientId();if(!r){console.warn("无法获取客户端ID，跳过日志同步");return}const h=Pe(c);if(!h.trim()){x=s;return}await se.sendRequest("logger","syncLog",{content:h,timestamp:new Date().toISOString(),clientId:r}),x=s}}catch(c){console.error("同步日志到服务端失败:",c)}},De=()=>{se.connect().then(()=>{I()}).catch(c=>{e.value+=`<span class="log-item warning">初始WebSocket连接失败，将自动重试: ${c.message}</span><br/>`});const s=()=>{e.value+='<span class="log-item success">WebSocket连接成功建立 (proactive)</span><br/>';const c=[];for(const r in a)if(a.hasOwnProperty(r)){const h=a[r];(h.status===0||h.status===1)&&!h.terminated&&(c.includes(r)||c.push(r))}if(c.length>0){let r=!1;try{const h=f();if(h){const l=`taskpane_id_${h.DocID}`,p=window.Application.PluginStorage.getItem(l);if(p){const D=window.Application.GetTaskPane(p);D&&(r=D.Visible)}}}catch(h){e.value+=`<span class="log-item warning">检查任务窗格可见性失败: ${h.message}</span><br/>`,r=!0}setTimeout(()=>{if(r){const h=`检测到 ${c.length} 个未完成的任务，是否继续？`;_(h).then(o=>{o?(e.value+=`<span class="log-item info">用户选择继续 ${c.length} 个进行中的任务 (by taskId)...</span><br/>`,se.sendRequest("urlMonitor","resumeUrlMonitors",{taskIds:c}).then(l=>{l&&l.success?e.value+=`<span class="log-item success">成功请求恢复任务。服务端响应: ${l.message||""}</span><br/>`:e.value+=`<span class="log-item warning">请求恢复任务可能失败或无明确成功响应: ${(l==null?void 0:l.message)||"未知错误"}</span><br/>`}).catch(l=>{e.value+=`<span class="log-item error">请求恢复任务出错: ${l.message}</span><br/>`})):(e.value+='<span class="log-item info">用户选择不继续未完成的任务，正在停止这些任务（保留控件）...</span><br/>',c.forEach(l=>{U(l)}),e.value+=`<span class="log-item success">${c.length} 个任务已停止（控件已保留）。</span><br/>`)}).catch(o=>{e.value+=`<span class="log-item error">弹窗错误: ${o.message}，默认停止任务（保留控件）</span><br/>`,c.forEach(l=>{U(l)})})}else e.value+='<span class="log-item info">任务窗格不可见，跳过恢复任务确认弹窗。</span><br/>'},0)}};se.setConnectionSuccessHandler(s),se.isConnected&&(e.value+='<span class="log-item info">WebSocket已经连接 (useTaskPane)，主动触发任务恢复处理。</span><br/>',s()),se.addEventListener("connection",c=>{c.status==="disconnected"&&(e.value+=`<span class="log-item warning">WebSocket连接关闭，原因: ${c.reason||"未知"}, 代码: ${c.code||"N/A"}，将自动重连</span><br/>`)}),se.addEventListener("watcher",c=>{var r,h;if(Se.push(c),c.type,c.eventType==="uploadSuccess"){const o=(r=c.data)==null?void 0:r.file,l=o==null?void 0:o.replace(/\.docx$/,""),p=`${c.eventType}_${o}_${Date.now()}`;if(Me.has(p)){e.value+=`<span class="log-item warning">忽略重复的上传事件: ${o}</span><br/>`;return}if(Me.add(p),Me.size>100){const $=Me.values();Me.delete($.next().value)}const D=l&&((h=a[l])==null?void 0:h.wordType);Le(c,D)}else c.eventType&&Le(c,null)}),se.addEventListener("urlMonitor",c=>{Se.push(c),c.type==="urlMonitor"&&(e.value+=`<span class="log-item info">收到URL监控事件: ${c.eventType||c.action}</span><br/>`),c.eventType&&Le(c,null)}),se.addEventListener("health",c=>{}),se.addEventListener("error",c=>{const r=c.error||"未知错误";e.value+=`<span class="log-item error">WebSocket错误: ${r}</span><br/>`,console.error("WebSocket错误:",c)})},Q=St({}),nt=async(s,c,r=!1,h=5e3,o={})=>{try{e.value+=`<span class="log-item info">开始监控URL: ${s}</span><br/>`;const l=await se.startUrlMonitoring(s,h,{downloadOnSuccess:o.downloadOnSuccess!==void 0?o.downloadOnSuccess:!0,appKey:o.appKey,filename:o.filename,taskId:c}),p=l.success||(l==null?void 0:l.success),D=l.urlId||(l==null?void 0:l.urlId);if(p&&D){Q[c]={urlId:D,url:s,isResultUrl:r,startTime:Date.now()},e.value+=`<span class="log-item success">URL监控已启动，ID: ${D}</span><br/>`;try{await se.startUrlChecking(D)}catch{}return D}else throw new Error("服务器返回失败")}catch(l){return e.value+=`<span class="log-item error">启动URL监控失败: ${l.message}</span><br/>`,null}},Ue=async s=>{if(!s)return e.value+='<span class="log-item warning">无效的URL监控ID</span><br/>',!1;try{Object.keys(Q).forEach(r=>{Q[r].urlId===s&&delete Q[r]}),e.value+=`<span class="log-item info">正在停止URL监控: ${s}</span><br/>`;const c=await Rt(async()=>await se.stopUrlMonitoring(s),"delete",`${Ot()}/api/url/monitor/${s}`);return c&&(c.success||c!=null&&c.success)?(e.value+=`<span class="log-item success">已停止URL监控: ${s}</span><br/>`,!0):(e.value+='<span class="log-item warning">服务端响应停止URL监控失败，但已在客户端停止</span><br/>',!0)}catch(c){e.value+=`<span class="log-item warning">停止URL监控API调用失败: ${c.message}，但已在客户端停止</span><br/>`;try{setTimeout(async()=>{try{await Rt(async()=>await se.stopUrlMonitoring(s),"delete",`${Ot()}/api/url/monitor/${s}`)}catch{}},1e3)}catch{}return!0}},at=async()=>{try{const s=await Rt(async()=>await se.getUrlMonitorStatus(),"get",`${Ot()}/api/url/status`);return s.data||s}catch(s){return e.value+=`<span class="log-item error">获取URL监控状态失败: ${s.message}</span><br/>`,[]}},bt=async s=>{try{return await se.forceUrlCheck(s)}catch{return!1}},Le=async(s,c)=>{var r;if(s.eventType==="uploadSuccess"){const h=s.data.file,o=h.replace(/\.docx$/,"");if(a[o]){if(a[o].terminated){e.value+=`<span class="log-item info">忽略已终止任务 ${o.substring(0,8)} 的上传通知</span><br/>`;return}if(a[o].uploadSuccess){e.value+=`<span class="log-item warning">任务 ${o.substring(0,8)} 已处理过上传成功事件，忽略重复通知</span><br/>`;return}e.value+=`<span class="log-item success">收到文件 ${h} 上传成功通知</span><br/>`,e.value+=`<span class="log-item info">使用 wordType: ${c||a[o].wordType||"wps-analysis"}</span><br/>`,a[o].status=1,a[o].uploadSuccess=!0,await ve(o,c||a[o].wordType||"wps-analysis")}}else if(s.eventType==="encryptedFileError"){const h=s.data.file,o=h.replace(/\.docx$/,""),l=s.data.message||"文件已加密，无法处理";e.value+=`<span class="log-item error">文件加密错误: ${h}</span><br/>`,await Je(o,l,"encryption")}else if(s.eventType==="uploadError"){const h=s.data.file||s.data,o=h.replace(/\.docx$/,"");let l="文件上传失败";typeof s.data=="object"&&s.data.message?l=s.data.message:typeof s.data=="string"&&(l=s.data),e.value+=`<span class="log-item error">文件上传错误: ${h}</span><br/>`,await Je(o,l,"upload")}else if(s.eventType!=="urlMonitorUpdate")if(s.eventType==="urlMonitorStopped"){const{urlId:h,url:o,taskId:l,downloadedPath:p}=s.data,D=Object.keys(Q).filter($=>Q[$].urlId===h);D.length>0&&D.forEach($=>{p&&a[$]&&(a[$].resultFile=p,a[$].resultDownloaded=!0),delete Q[$]})}else if(s.eventType==="urlFileDownloaded"){const{urlId:h,url:o,filePath:l,taskId:p}=s.data;if(!Object.values(Q).some($=>$.urlId===h)&&p&&((r=a[p])!=null&&r.terminated))return;if(p&&a[p]&&a[p].terminated){if(e.value+=`<span class="log-item info">忽略已终止任务 ${h} 的文件下载通知</span><br/>`,h)try{await se.stopUrlMonitoring(h),Q[p]&&delete Q[p]}catch{}return}if(p&&a[p]){if(e.value+=`<span class="log-item success">收到结果文件通知: ${l}</span><br/>`,a[p].isCheckTask&&l.endsWith(".wps.json")){e.value+=`<span class="log-item info">处理校对任务JSON文件: ${l}</span><br/>`;try{const k=window.Application.FileSystem.ReadFile(l),B=JSON.parse(k);if(await We(B,p)){a[p].status=2,e.value+=`<span class="log-item success">校对任务${p.substring(0,8)}已完成批注处理</span><br/>`;const Y=m(a[p].startTime);e.value+=`<span class="log-item success">校对任务${p.substring(0,8)}完成，总耗时${Y}</span><br/>`}}catch($){e.value+=`<span class="log-item error">处理校对JSON文件失败: ${$.message}</span><br/>`,$.message.includes("Unsupported protocol")&&(e.value+=`<span class="log-item error">文件路径格式错误，无法读取文件: ${l}</span><br/>`),a[p].status=-1,a[p].errorMessage=`JSON处理失败: ${$.message}`,e.value+=`<span class="log-item error">校对任务${p.substring(0,8)}处理失败</span><br/>`}}else{a[p].resultFile=l,a[p].resultDownloaded=!0;const $=m(a[p].startTime);e.value+=`<span class="log-item success">任务${p.substring(0,8)}完成，总耗时${$}</span><br/>`,await rt(p)}Q[p]&&Q[p].urlId&&(Ue(Q[p].urlId),e.value+='<span class="log-item info">已停止URL监控</span><br/>',delete Q[p])}else if(h){e.value+=`<span class="log-item info">URL文件已下载: ${l}</span><br/>`;const $=Object.keys(Q).filter(k=>{var B;return Q[k].urlId===h&&!((B=a[k])!=null&&B.terminated)});$.length>0&&$.forEach(async k=>{if(a[k]){if(e.value+=`<span class="log-item info">关联到任务: ${k.substring(0,8)}</span><br/>`,a[k].resultFile=l,a[k].resultDownloaded=!0,a[k].isCheckTask&&l.endsWith(".wps.json"))try{const X=window.Application.FileSystem.ReadFile(l),Y=JSON.parse(X);if(await We(Y,k)&&a[k].status===1){a[k].status=2,e.value+='<span class="log-item info">校对控件已删除，任务状态已更新为完成</span><br/>';const re=m(a[k].startTime);e.value+=`<span class="log-item success">校对任务${k.substring(0,8)}完成，总耗时${re}</span><br/>`}}catch(B){e.value+=`<span class="log-item error">处理校对JSON失败: ${B.message}</span><br/>`,a[k].status===1&&(a[k].status=-1,a[k].errorMessage=`JSON处理失败: ${B.message}`,e.value+=`<span class="log-item error">校对任务${k.substring(0,8)}处理失败</span><br/>`)}else if(a[k].status===1){a[k].status=2;const B=m(a[k].startTime);e.value+=`<span class="log-item success">任务${k.substring(0,8)}完成，总耗时${B}</span><br/>`}}})}}else if(s.eventType==="urlFileDownloadError"){const{urlId:h,url:o,error:l,taskId:p}=s.data;if(p&&a[p]&&a[p].terminated){e.value+=`<span class="log-item info">忽略已终止任务 ${p.substring(0,8)} 的下载失败通知</span><br/>`;return}if(e.value+=`<span class="log-item error">下载URL文件失败: ${l}</span><br/>`,p&&a[p]){a[p].status=-1,a[p].errorMessage=`下载失败: ${l}`;try{const D=f();if(D&&D.ContentControls)for(let $=1;$<=D.ContentControls.Count;$++)try{const k=D.ContentControls.Item($);if(k&&k.Title&&(k.Title===`任务_${p}`||k.Title===`任务增强_${p}`||k.Title===`校对_${p}`)){const B=k.Title===`任务增强_${p}`||a[p].isEnhanced,X=k.Title===`校对_${p}`||a[p].isCheckTask;X?k.Title=`异常校对_${p}`:B?k.Title=`异常增强_${p}`:k.Title=`异常_${p}`;const Y=X?"校对":B?"增强":"普通";e.value+=`<span class="log-item info">已将${Y}任务${p.substring(0,8)}控件标记为异常</span><br/>`;break}}catch{continue}}catch(D){e.value+=`<span class="log-item warning">更新控件标题失败: ${D.message}</span><br/>`}xe(p),Q[p]&&delete Q[p]}if(h)try{e.value+=`<span class="log-item info">尝试停止URL监控: ${h}</span><br/>`,await Rt(async()=>await se.stopUrlMonitoring(h),"delete",`${Ot()}/api/url/monitor/${h}`)}catch{}}else s.eventType==="resumeUrlMonitors"&&console.log(s.data)},ve=async(s,c="wps-analysis")=>{try{if(a[s]&&a[s].terminated)return e.value+=`<span class="log-item info">任务 ${s.substring(0,8)} 已被终止，跳过API调用</span><br/>`,!1;if(!T.appKey)throw new Error("未初始化appKey信息");const r=await ce(),h=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/temp_docx/${s}.docx`,o=await zt.post(Rs()+"/api/open/ticket/v1/ai_comment/create",{access_token:r,data:{app_key:T.appKey,subject:q.value,stage:R.value,file_name:`${s}`,word_url:h,word_type:c,is_ai_auto:!0,is_ai_edit:!0,create_user_id:T.userId,create_username:T.userName,callback_data:{callback_url:"http://worksheet.hexinedu.com/api/open/ticket/v1/callback/test"}}}),l=o.data.data.ticket_id;if(!l)return a[s]&&(a[s].status=-1,a[s].errorMessage="无法获取ticket_id",xe(s)),!1;e.value+=`<span class="log-item info">获取到ticket_id: ${l}，开始监控结果文件</span><br/>`;let p,D;c==="wps-check"?(p=`https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/${T.appKey}/ai/${l}.wps.json`,D=`${l}.wps.json`,e.value+=`<span class="log-item info">校对任务，监控JSON文件: ${D}</span><br/>`):(p=`https://sigma-temp.oss-cn-shanghai.aliyuncs.com/docx/${l}.wps.docx`,D=`${l}.wps.docx`,e.value+=`<span class="log-item info">解析任务，监控DOCX文件: ${D}</span><br/>`);const $={downloadOnSuccess:!0,filename:D,appKey:T.appKey,ticketId:l,taskId:s},k=await nt(p,s,!0,3e3,$);return a[s]&&(a[s].ticketId=l,a[s].resultUrl=p,a[s].urlMonitorId=k,a[s].status=1),o.data}catch(r){return e.value+=`<span class="log-item error">API调用失败: ${r.message}</span><br/>`,a[s]&&(a[s].status=-1,a[s].errorMessage=`API调用失败: ${r.message}`,xe(s)),!1}},wt=async(s,c="wps-analysis")=>new Promise((r,h)=>{try{e.value+=`<span class="log-item info">监控目录: ${b.value}</span><br/>`,e.value+=`<span class="log-item info">使用文档类型: ${c}</span><br/>`,a[s]&&(a[s].status=0,a[s].wordType=c,a[s].uploadSuccess=!1),e.value+='<span class="log-item info">正在等待文件上传完成的通知...</span><br/>';const o=1e3,l=600;let p=0;const D=setInterval(()=>{if(p++,a[s]&&a[s].uploadSuccess){clearInterval(D),r(!0);return}if(a[s]&&a[s].status===-1){clearInterval(D),e.value+=`<span class="log-item error">任务${s.substring(0,8)}已异常，停止等待上传完成</span><br/>`,h(new Error(a[s].errorMessage||"任务已异常"));return}if(p>=l){clearInterval(D),e.value+=`<span class="log-item error">文件上传超时(${l}秒)，任务已终止</span><br/>`,a[s]&&(a[s].status=-1,a[s].terminated=!0,a[s].errorMessage=`文件上传超时(${l}秒)`,a[s].uploadFailed=!0),xe(s),h(new Error(`文件上传超时(${l}秒)，未收到完成通知`));return}},o)}catch(o){e.value+=`<span class="log-item error">任务处理异常: ${o.message}</span><br/>`,a[s]&&(a[s].status=-1,a[s].errorMessage=`任务处理异常: ${o.message}`),xe(s),h(o)}}),ft=async()=>{var r;e.value+='<span class="log-item info">扫描文档中已有的任务...</span><br/>';const s=window.wps,c=s.ActiveDocument;if(i.value=c.DocID,v.value=s.ActiveWindow.Index,c!=null&&c.ContentControls)for(let h=1;h<=c.ContentControls.Count;h++){const o=c.ContentControls.Item(h);if(o&&o.Title){let l=null,p=1,D=!1,$=!1;if(o.Title.startsWith("任务增强_")?(l=o.Title.substring(5),p=1,D=!0):o.Title.startsWith("任务_")?(l=o.Title.substring(3),p=1):o.Title.startsWith("校对_")?(l=o.Title.substring(3),p=1,$=!0):o.Title.startsWith("已完成增强_")?(l=o.Title.substring(6),p=2,D=!0):o.Title.startsWith("已完成校对_")?(l=o.Title.substring(6),p=2,$=!0):o.Title.startsWith("已完成_")?(l=o.Title.substring(4),p=2):o.Title.startsWith("异常增强_")?(l=o.Title.substring(5),p=-1,D=!0):o.Title.startsWith("异常校对_")?(l=o.Title.substring(5),p=-1,$=!0):o.Title.startsWith("异常_")?(l=o.Title.substring(3),p=-1):o.Title.startsWith("已停止增强_")?(l=o.Title.substring(6),p=4,D=!0):o.Title.startsWith("已停止校对_")?(l=o.Title.substring(6),p=4,$=!0):o.Title.startsWith("已停止_")&&(l=o.Title.substring(4),p=4),l&&!a[l]){let k="";try{k=((r=o.Range)==null?void 0:r.Text)||""}catch{}let B=Date.now()-24*60*60*1e3;try{if(l.length===24){const Z=l.substring(0,8),re=parseInt(Z,16);!isNaN(re)&&re>0&&re<2147483647&&(B=re*1e3)}else{const Z=Date.now()-864e5;p===2?B=Z-60*60*1e3:p===-1?B=Z-30*60*1e3:p===4?B=Z-45*60*1e3:B=Z}}catch{}a[l]={status:p,startTime:B,contentControlId:o.ID,isEnhanced:D,isCheckTask:$,selectedText:k};const X=p===1?"进行中":p===2?"已完成":p===-1?"异常":p===4?"已停止":"未知",Y=$?"校对":D?"增强":"普通";e.value+=`<span class="log-item info">发现已有${Y}任务: ${l.substring(0,8)}, 状态: ${X}</span><br/>`}}}},xe=async(s,c=!1)=>{try{if(!a[s]){e.value+=`<span class="log-item warning">找不到任务${s.substring(0,8)}的记录，无法清除控件</span><br/>`;return}c&&a[s].status===1&&(a[s].status=3,a[s].errorMessage="用户主动释放");const r=f();if(!r){e.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let h=!1;const o=a[s].isEnhanced;await O("删除内容控件",async()=>{if(r.ContentControls&&r.ContentControls.Count>0)for(let l=r.ContentControls.Count;l>=1;l--)try{const p=r.ContentControls.Item(l);p&&p.Title&&p.Title.includes(s)&&(p.LockContents=!1,p.Delete(!1),h=!0,console.log(p.Title),e.value+=`<span class="log-item success">已解锁并删除${o?"增强":"普通"}任务${s.substring(0,8)}的内容控件</span><br/>`)}catch(p){e.value+=`<span class="log-item warning">访问第${l}个控件时出错: ${p.message}</span><br/>`;continue}}),h?a[s].placeholderRemoved=!0:e.value+=`<span class="log-item warning">未能找到或删除${o?"增强":"普通"}任务${s.substring(0,8)}的内容控件</span><br/>`}catch(r){e.value+=`<span class="log-item error">删除内容控件失败: ${r.message}</span><br/>`}},He=s=>{var c;try{const r=window.wps,h=f();if(!h||!h.ContentControls){e.value+='<span class="log-item error">无法访问文档或内容控件</span><br/>';return}const o=Fe(s,h);if(!o){const D=a[s];D&&(D.status===2||D.status===-1)?e.value+=`<span class="log-item info">任务ID: ${s.substring(0,8)} 已完成，内容控件已不存在</span><br/>`:e.value+=`<span class="log-item error">找不到任务ID: ${s.substring(0,8)} 对应的内容控件</span><br/>`;return}o.Range.Select();const l=r.Windows.Item(v.value);l&&l.Selection&&l.Selection.Range&&l.ScrollIntoView(l.Selection.Range,!0);const p=(c=a[s])==null?void 0:c.isEnhanced;e.value+=`<span class="log-item success">已跳转到${p?"增强":"普通"}任务ID: ${s.substring(0,8)} 位置</span><br/>`}catch(r){e.value+=`<span class="log-item error">跳转到任务控件失败: ${r.message}</span><br/>`}},Je=async(s,c,r="general")=>{if(!a[s]){e.value+=`<span class="log-item warning">尝试处理未知任务${s.substring(0,8)}的错误</span><br/>`;return}if(a[s].terminated){e.value+=`<span class="log-item info">忽略已终止任务 ${s.substring(0,8)} 的错误通知</span><br/>`;return}switch(e.value+=`<span class="log-item error">任务${s.substring(0,8)}发生错误: ${c}</span><br/>`,a[s].status=-1,a[s].errorMessage=c,a[s].failureTime=Date.now(),r){case"upload":a[s].uploadFailed=!0;break;case"encryption":a[s].encryptionError=!0;break;case"processing":a[s].processingError=!0;break;default:a[s].generalError=!0}try{const h=f(),o=Fe(s,h);if(o){const l=a[s].isEnhanced,p=a[s].isCheckTask;p?o.Title=`异常校对_${s}`:l?o.Title=`异常增强_${s}`:o.Title=`异常_${s}`,o.LockContents=!1;const D=p?"校对":l?"增强":"普通";e.value+=`<span class="log-item info">已将${D}任务${s.substring(0,8)}控件标记为异常</span><br/>`}else e.value+=`<span class="log-item warning">无法找到任务${s.substring(0,8)}的控件进行状态更新</span><br/>`}catch(h){e.value+=`<span class="log-item warning">更新控件标题失败: ${h.message}</span><br/>`}if(Q[s]&&Q[s].urlId)try{await Ue(Q[s].urlId),delete Q[s],e.value+=`<span class="log-item info">已停止任务${s.substring(0,8)}的URL监控</span><br/>`}catch(h){e.value+=`<span class="log-item warning">停止URL监控失败: ${h.message}</span><br/>`}},Fe=(s,c)=>{if(!c||!c.ContentControls)return null;const r=a[s],h=r==null?void 0:r.isEnhanced,o=r==null?void 0:r.isCheckTask,l=[`任务_${s}`,`任务增强_${s}`,`校对_${s}`,`已完成_${s}`,`已完成增强_${s}`,`已完成校对_${s}`,`异常_${s}`,`异常增强_${s}`,`异常校对_${s}`,`已停止_${s}`,`已停止增强_${s}`,`已停止校对_${s}`];o?l.unshift(`校对_${s}`):h?l.unshift(`任务增强_${s}`):l.unshift(`任务_${s}`);let p=null,D=-1;for(let $=1;$<=c.ContentControls.Count;$++)try{const k=c.ContentControls.Item($);if(!k||!k.Title)continue;const B=l.indexOf(k.Title);B!==-1&&(p===null||B<D)&&(p=k,D=B)}catch(k){e.value+=`<span class="log-item warning">访问第${$}个控件时出错: ${k.message}</span><br/>`;continue}return p},rt=async s=>{const c=f(),r=a[s];if(!r){e.value+=`<span class="log-item error">找不到ID为${s.substring(0,8)}的任务, 现有任务ID: ${Object.keys(a).join(", ")}</span><br/>`;return}if(r.terminated||r.status!==1)return;if(r.documentInserted){e.value+=`<span class="log-item info">任务${s.substring(0,8)}已插入过文档，跳过重复插入</span><br/>`;return}const h=f();let o=Fe(s,h);if(!o){if(r.controlSearchRetryCount||(r.controlSearchRetryCount=0),r.controlSearchRetryCount++,e.value+=`<span class="log-item warning">找不到任务${s.substring(0,8)}的内容控件 (第${r.controlSearchRetryCount}次尝试)</span><br/>`,h&&h.ContentControls){const p=[],D=[];for(let $=1;$<=Math.min(h.ContentControls.Count,20);$++)try{const k=h.ContentControls.Item($);k&&k.Title&&(p.push(k.Title),k.Title.includes(s.substring(0,8))&&D.push(k.Title))}catch(k){p.push(`[访问错误:${k.message}]`)}e.value+=`<span class="log-item info">当前文档前20个控件标题: ${p.join(", ")}</span><br/>`,e.value+=`<span class="log-item info">总控件数量: ${h.ContentControls.Count}</span><br/>`,D.length>0&&(e.value+=`<span class="log-item info">包含任务ID的控件: ${D.join(", ")}</span><br/>`)}if(r.controlSearchRetryCount>=3){a[s].status=-1,a[s].errorMessage="无法找到对应的内容控件",e.value+=`<span class="log-item error">任务${s.substring(0,8)}重试3次后仍无法找到控件，标记为失败</span><br/>`;return}if(r.errorMessage&&r.status===1){a[s].status=-1,e.value+=`<span class="log-item error">任务${s.substring(0,8)}失败: ${r.errorMessage}</span><br/>`;return}return}if(r.controlSearchRetryCount&&(e.value+=`<span class="log-item success">任务${s.substring(0,8)}在第${r.controlSearchRetryCount+1}次尝试后找到控件</span><br/>`,delete r.controlSearchRetryCount),r.errorMessage&&r.status===1){a[s].status=-1,e.value+=`<span class="log-item error">任务${s.substring(0,8)}失败: ${r.errorMessage}</span><br/>`,xe(s);return}if(!r.resultFile)return;a[s].documentInserted=!0;const l=o.Range;o.LockContents=!1;try{await O("插入结果文件",async()=>{var we,gt,Ce;e.value+=`<span class="log-item info">正在自动插入结果文件${r.resultFile}...</span><br/>`;const k=c.Range(l.End+1,l.End+1);l.Text.includes("\v")&&k.InsertAfter("\v");let B=k.End;const X=(we=l.Tables)==null?void 0:we.Count;if(X){const Be=l.Tables.Item(X);((gt=Be==null?void 0:Be.Range)==null?void 0:gt.End)>B&&(Be.Range.InsertParagraphAfter(),B=(Ce=Be==null?void 0:Be.Range)==null?void 0:Ce.End)}c.Range(B,B).InsertFile(r.resultFile);const Z=Fe(s,h);Z&&(o=Z);const re=c.Range(o.Range.End-1,o.Range.End);re.Text.includes("\r")&&re.Delete()}),a[s].status=2,Q[s]&&Q[s].urlId&&se.sendRequest("urlMonitor","updateTaskStatus",{urlId:Q[s].urlId,status:"completed",taskId:s}).then(k=>{e.value+=`<span class="log-item info">已通知服务端更新任务${s.substring(0,8)}状态为完成</span><br/>`}).catch(k=>{e.value+=`<span class="log-item warning">通知服务端更新任务状态失败: ${k.message}</span><br/>`});const p=m(r.startTime);e.value+=`<span class="log-item success">任务${s.substring(0,8)}处理完成，总耗时${p}</span><br/>`;const D=o.Title===`任务增强_${s}`||r.isEnhanced,$=o.Title===`校对_${s}`||r.isCheckTask;if(o){$?o.Title=`已完成校对_${s}`:D?o.Title=`已完成增强_${s}`:o.Title=`已完成_${s}`;const k=$?"校对":D?"增强":"普通";e.value+=`<span class="log-item info">已将${k}任务${s.substring(0,8)}控件标记为已完成</span><br/>`}}catch(p){a[s].documentInserted=!1,a[s].status=-1;const D=o.Title===`任务增强_${s}`||r.isEnhanced,$=o.Title===`校对_${s}`||r.isCheckTask;if(o){$?o.Title=`异常校对_${s}`:D?o.Title=`异常增强_${s}`:o.Title=`异常_${s}`;const k=$?"校对":D?"增强":"普通";e.value+=`<span class="log-item info">已将${k}任务${s.substring(0,8)}控件标记为异常</span><br/>`}e.value+=`<span class="log-item error">插入文档失败: ${p.message}</span><br/>`}},je=new Set,ct=3,yt=async()=>{const s=(o=1e3)=>new Promise(l=>{setTimeout(()=>l(),o)}),c=new Map,r=async o=>{if(!je.has(o)&&!(je.size>=ct)){je.add(o);try{await rt(o)}catch(l){e.value+=`<span class="log-item error">处理任务${o.substring(0,8)}时出错: ${l.message}</span><br/>`}finally{je.delete(o)}}},h=Object.keys(a).filter(o=>a[o].status===1&&!a[o].terminated);if(!h.length)e.value+='<span class="log-item info">目前没有解析中的任务...</span><br/>';else{h.forEach(l=>{c.has(l)||c.set(l,Date.now())});const o=h.slice(0,ct).map(l=>r(l));await Promise.all(o)}for(;;){await s(3e3);const o=Object.keys(a).filter(l=>a[l].status===1&&!a[l].terminated);if(o.forEach(l=>{c.has(l)||c.set(l,Date.now());const p=c.get(l);(Date.now()-p)/1e3/60>=5e4&&a[l]&&!a[l].terminated&&(a[l].terminated=!0,a[l].status=-1,e.value+=`<span class="log-item warning">任务 ${l.substring(0,8)} 执行超过5分钟，已自动终止</span><br/>`,c.delete(l),je.delete(l))}),o.length){const p=o.filter(D=>!je.has(D)).slice(0,ct-je.size);if(p.length>0){const D=p.map($=>r($));await Promise.all(D)}}}},ht=async(s="wps-analysis",c=null)=>{const r=ze();if(r){const{primary:o,all:l}=r,{taskId:p,control:D,task:$,isEnhanced:k,overlapType:B}=o,X=l.filter(Y=>Y.task&&(Y.task.status===0||Y.task.status===1));if(X.length>0){const Y=X.map(Z=>Z.taskId.substring(0,8)).join(", ");return e.value+=`<span class="log-item warning">当前选中内容与正在处理中的任务重叠 (${Y})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的任务重叠"))}e.value+=`<span class="log-item info">发现选区与${l.length}个已有任务重叠，重叠类型: ${B}</span><br/>`,xt();try{await O("删除重叠控件",async()=>{for(const Y of l){const{taskId:Z,control:re,task:$e,isEnhanced:we}=Y;e.value+=`<span class="log-item info">删除重叠的${we?"增强":"普通"}任务 ${Z.substring(0,8)}，状态为 ${S(($e==null?void 0:$e.status)||0)}</span><br/>`,$e&&($e.status=3,$e.terminated=!0,$e.errorMessage="用户重新创建任务时删除");try{re.LockContents=!1,re.Delete(!1),a[Z]&&(a[Z].placeholderRemoved=!0)}catch(gt){e.value+=`<span class="log-item error">删除重叠任务控件失败: ${gt.message}</span><br/>`}}}),e.value+=`<span class="log-item success">已删除${l.length}个重叠的任务控件，准备创建新任务</span><br/>`}catch(Y){return Ee(),e.value+=`<span class="log-item error">删除重叠任务控件失败: ${Y.message}</span><br/>`,Promise.reject(Y)}Ee()}const h=Is().replace(/-/g,"").substring(0,8);return new Promise(async(o,l)=>{var p,D;try{const $=window.wps,k=f(),B=k.ActiveWindow.Selection,X=B.Range,Y=B.Text||"";if(n.value=Y,ye(),X){const Z=X.Paragraphs,re=Z.Item(1),$e=Z.Item(Z.Count),we=re.Range.Start;let gt=$e.Range.End;const Ce=X.Start,Be=X.End;let Qt=k.Range(Math.min(we,Ce),Math.max(gt,Be));await O("创建内容控件",async()=>{var Ms;let ot=k.ContentControls.Add($.Enum.wdContentControlRichText,Qt);if(!ot){ot=k.ContentControls.Add($.Enum.wdContentControlRichText),e.value+='<span class="log-item error">创建内容控件失败</span><br/>';const Ps=(Ms=Qt.Tables)==null?void 0:Ms.Count;if(Ps){const Dt=Qt.Tables.Item(Ps).Range.End;if(Math.abs(Be-Dt)<=1){e.value+='<span class="log-item info">检测到选区末尾是表格，使用特殊处理逻辑</span><br/>',e.value+='<span class="log-item info">已剪切原始选区内容</span><br/>',k.Range(Dt,Dt).InsertParagraphAfter(),e.value+='<span class="log-item info">已在表格下方插入段落</span><br/>';const Cn=k.Range(Dt+1,Dt+1),kn=k.Range(we,Dt+1);if(Cn.Select(),Qt.Cut(),ot=k.ContentControls.Add($.Enum.wdContentControlRichText),kn.Select(),!ot)throw new Error("在新段落位置创建内容控件失败");console.log(f().Application.Selection.Text),ot.Range.Paste(),e.value+='<span class="log-item info">已将内容粘贴到表格下方的新控件中</span><br/>'}}else{if(!ot)throw new Error("创建内容控件失败");X.Cut(),ot.Range.Paste()}}e.value+='<span class="log-item success">已将原始内容粘贴到控件中</span><br/>';const us=s==="wps-enhance_analysis";ot.Title=us?`任务增强_${h}`:`任务_${h}`,ot.LockContents=!0,a[h]||(a[h]={}),a[h].contentControlId=ot.ID,a[h].isEnhanced=us,e.value+=`<span class="log-item info">已创建${us?"增强":"普通"}内容控件并锁定选区</span><br/>`;const Tn=ot.Range.Text;e.value+=`<span class="log-item success">控件内容验证通过，长度: ${Tn.length}</span><br/>`})}if(a[h]={status:0,startTime:Date.now(),wordType:s,isEnhanced:s==="wps-enhance_analysis",selectedText:Y},e.value+=`<span class="log-item success">创建${s==="wps-enhanced"?"增强":"普通"}任务: ${h}，类型: ${s}</span><br/>`,H.value){let Z=null;try{const re=window.Application.PluginStorage.getItem("user_info");if(re){const $e=JSON.parse(re);Z=(D=(p=$e==null?void 0:$e.orgs)==null?void 0:p[0])==null?void 0:D.orgId}}catch(re){e.value+=`<span class="log-item warning">获取用户信息失败: ${re.message}</span><br/>`}if(Z===2){e.value+=`<span class="log-item info">批量测试模式：跳过文件生成和上传，任务${h.substring(0,8)}等待批量插入</span><br/>`,a[h].resultFile="C:\\ww-wps-addon\\Downloads\\682ad7b4bde591ec60b7cf56.wps.docx",a[h].status=5,c&&c(),o();return}else e.value+=`<span class="log-item warning">批量测试模式仅对 orgId 为 2 的用户可用，当前用户 orgId: ${Z}</span><br/>`}await Ie(h),c&&c(),a[h]&&(a[h].status=1);try{await wt(h,s)?o():(a[h]&&a[h].status===1&&(a[h].status=-1,a[h].terminated=!0,a[h].errorMessage="API调用失败或超时",e.value+=`<span class="log-item error">任务${h.substring(0,8)}失败</span><br/>`,Xe(h)),l(new Error("API调用失败或超时")))}catch(Z){a[h]&&(a[h].status=-1,a[h].terminated=!0,a[h].errorMessage=`执行错误: ${Z.message}`,e.value+=`<span class="log-item error">任务${h.substring(0,8)}执行出错: ${Z.message}</span><br/>`,Xe(h)),l(Z)}}catch($){l($)}})},Bt=async(s=null)=>{const c=ze();if(c){const{primary:h,all:o}=c,{taskId:l,control:p,task:D,isEnhanced:$,overlapType:k}=h,B=o.filter(X=>X.task&&(X.task.status===0||X.task.status===1));if(B.length>0){const X=B.map(Y=>Y.taskId.substring(0,8)).join(", ");return e.value+=`<span class="log-item warning">当前选中内容与正在处理中的校对任务重叠 (${X})，请等待任务完成后再操作</span><br/>`,Promise.reject(new Error("选中内容与正在处理中的校对任务重叠"))}e.value+=`<span class="log-item info">发现选区与${o.length}个已有任务重叠，重叠类型: ${k}</span><br/>`,xt();try{await O("删除重叠校对控件",async()=>{for(const X of o){const{taskId:Y,control:Z,task:re,isEnhanced:$e}=X;e.value+=`<span class="log-item info">删除重叠的校对任务 ${Y.substring(0,8)}，状态为 ${S((re==null?void 0:re.status)||0)}</span><br/>`,re&&(re.status=3,re.terminated=!0,re.errorMessage="用户重新创建校对任务时删除");try{Z.LockContents=!1,Z.Delete(!1),a[Y]&&(a[Y].placeholderRemoved=!0)}catch(we){e.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${we.message}</span><br/>`}}}),e.value+=`<span class="log-item success">已删除${o.length}个重叠的校对任务控件，准备创建新校对任务</span><br/>`}catch(X){return Ee(),e.value+=`<span class="log-item error">删除重叠校对任务控件失败: ${X.message}</span><br/>`,Promise.reject(X)}Ee()}const r=Is().replace(/-/g,"").substring(0,8);return new Promise(async(h,o)=>{try{const l=window.wps,p=f(),D=p.ActiveWindow.Selection,$=D.Range,k=D.Text||"";if(n.value=k,ye(),$){const B=$.Paragraphs,X=B.Item(1),Y=B.Item(B.Count),Z=X.Range.Start,re=Y.Range.End,$e=$.Start,we=$.End,gt=p.Range(Math.min(Z,$e),Math.max(re,we));await O("创建校对控件",async()=>{let Ce=p.ContentControls.Add(l.Enum.wdContentControlRichText,gt);if(!Ce){if(console.log("创建校对内容控件失败"),Ce=p.ContentControls.Add(l.Enum.wdContentControlRichText),!Ce)throw e.value+='<span class="log-item error">创建校对内容控件失败</span><br/>',new Error("创建校对内容控件失败");$.Cut(),Ce.Range.Paste()}e.value+='<span class="log-item success">已将原始内容粘贴到校对控件中</span><br/>',Ce.Title=`校对_${r}`,Ce.LockContents=!0,a[r]||(a[r]={}),a[r].contentControlId=Ce.ID,a[r].isCheckTask=!0,e.value+='<span class="log-item info">已创建校对内容控件并锁定选区</span><br/>';const Be=Ce.Range.Text;e.value+=`<span class="log-item success">校对控件内容验证通过，长度: ${Be.length}</span><br/>`})}a[r]={status:0,startTime:Date.now(),wordType:"wps-check",isCheckTask:!0,selectedText:k},e.value+=`<span class="log-item success">创建校对任务: ${r}，类型: wps-check</span><br/>`,await Ie(r),s&&s(),a[r]&&(a[r].status=1);try{await wt(r,"wps-check")?h():(a[r]&&a[r].status===1&&(a[r].status=-1,a[r].terminated=!0,a[r].errorMessage="校对API调用失败或超时",e.value+=`<span class="log-item error">校对任务${r.substring(0,8)}失败</span><br/>`,Xe(r)),o(new Error("校对API调用失败或超时")))}catch(B){a[r]&&(a[r].status=-1,a[r].terminated=!0,a[r].errorMessage=`校对执行错误: ${B.message}`,e.value+=`<span class="log-item error">校对任务${r.substring(0,8)}执行出错: ${B.message}</span><br/>`,Xe(r)),o(B)}}catch(l){o(l)}})},Nt=async()=>{},Vt=()=>W()>0?"status-error":Pt()>0?"status-running":$t()>0?"status-completed":"",Pt=()=>Object.values(a).filter(s=>s.status===0||s.status===1).length,$t=()=>Object.values(a).filter(s=>s.status===2).length,y=()=>Object.values(a).filter(s=>s.status===2||s.status===4).length,W=()=>Object.values(a).filter(s=>s.status===-1).length,G=()=>{try{e.value+='<span class="log-item info">开始强制清除所有任务控件...</span><br/>';const s=window.wps,c=f();if(!c){e.value+='<span class="log-item error">无法获取当前文档</span><br/>';return}let r=0;if(Object.keys(a).forEach(h=>{try{a[h].status===1&&(a[h].status=3,a[h].terminated=!0,a[h].errorMessage="强制清除"),xe(h),r++}catch(o){e.value+=`<span class="log-item warning">清除任务${h.substring(0,8)}失败: ${o.message}</span><br/>`}}),c.ContentControls&&c.ContentControls.Count>0)for(let h=c.ContentControls.Count;h>=1;h--)try{const o=c.ContentControls.Item(h);if(o&&o.Title&&(o.Title.startsWith("任务_")||o.Title.startsWith("任务增强_")||o.Title.startsWith("已完成_")||o.Title.startsWith("已完成增强_")||o.Title.startsWith("异常_")||o.Title.startsWith("异常增强_")||o.Title.startsWith("已停止_")||o.Title.startsWith("已停止增强_")))try{o.LockContents=!1,o.Delete(!1);let l;o.Title.startsWith("任务增强_")?l=o.Title.substring(5):o.Title.startsWith("任务_")?l=o.Title.substring(3):o.Title.startsWith("已完成增强_")?l=o.Title.substring(6):o.Title.startsWith("已完成_")?l=o.Title.substring(4):o.Title.startsWith("异常增强_")?l=o.Title.substring(5):o.Title.startsWith("异常_")?l=o.Title.substring(3):o.Title.startsWith("已停止增强_")?l=o.Title.substring(6):o.Title.startsWith("已停止_")&&(l=o.Title.substring(4)),a[l]?(a[l].placeholderRemoved=!0,a[l].status=3):a[l]={status:3,terminated:!0,errorMessage:"强制清除",placeholderRemoved:!0},r++,e.value+=`<span class="log-item success">已删除任务${l.substring(0,8)}的内容控件</span><br/>`}catch(l){e.value+=`<span class="log-item error">删除控件失败: ${l.message}</span><br/>`}}catch(o){e.value+=`<span class="log-item warning">访问控件时出错: ${o.message}</span><br/>`}r>0?e.value+=`<span class="log-item success">已清除${r}个任务控件</span><br/>`:e.value+='<span class="log-item info">未发现任何任务控件</span><br/>'}catch(s){e.value+=`<span class="log-item error">强制清除任务控件时出错: ${s.message}</span><br/>`}},ue=async()=>{try{const s=Object.values(Q).map(c=>c.urlId);if(s.length>0){e.value+=`<span class="log-item info">清理${s.length}个URL监控任务...</span><br/>`;for(const c of s)await Ue(c)}}catch(s){e.value+=`<span class="log-item error">清理URL监控任务失败: ${s.message}</span><br/>`}},pe=()=>{en(async()=>{try{const c=window.Application.PluginStorage.getItem("user_info");if(!c)throw new Error("未找到用户信息");const r=JSON.parse(c);if(!r.orgs||!r.orgs[0])throw new Error("未找到组织信息");T.appKey=r.appKey,T.userName=r.nickname,T.userId=r.userId,T.appSecret=r.appSecret}catch(c){e.value+=`<span class="log-item error">初始化appKey失败: ${c.message}</span><br/>`}await I(),rs([q,R],async()=>{await te()},{immediate:!1}),await fe();const s=it.onVersionChange(()=>{const c=d();ne.splice(0,ne.length,...c),it.isSeniorEdition()&&R.value==="junior"?R.value="senior":!it.isSeniorEdition()&&R.value==="senior"&&(R.value="junior"),e.value+=`<span class="log-item info">版本变更，已更新年级选项为: ${it.isSeniorEdition()?"高中":"初中"}</span><br/>`});return e.value='<span class="log-item">已加载任务窗格...</span><br/>',me(),await ft(),De(),Te(),yt(),window.addEventListener("beforeunload",ue),()=>{M&&clearTimeout(M),s&&s()}}),rs(e,s=>{M&&clearTimeout(M),M=setTimeout(()=>{Ye(s)},10)},{immediate:!1})},Te=()=>{se.addEventListener("config",s=>{s.eventType==="subjectAndStageChanged"&&s.data&&(s.data.subject!==q.value&&(q.value=s.data.subject,e.value+=`<span class="log-item info">学科设置已从服务器同步: ${q.value}</span><br/>`),s.data.stage!==R.value&&(R.value=s.data.stage,e.value+=`<span class="log-item info">年级设置已从服务器同步: ${R.value}</span><br/>`))})},ke=he(!1),ze=()=>{try{const s=f(),c=s.ActiveWindow.Selection;if(!c||!s||!s.ContentControls)return null;const r=c.Range,h=[];for(let o=1;o<=s.ContentControls.Count;o++)try{const l=s.ContentControls.Item(o);if(!l)continue;const p=(l.Title||"").trim(),D=l.Range;if(r.Start<D.End&&r.End>D.Start){let $=null,k=!1,B=!1;if(!p)B=!0,$=`empty_${l.ID||Date.now()}`;else if(p.startsWith("任务_")||p.startsWith("任务增强_")||p.startsWith("校对_")||p.startsWith("已完成_")||p.startsWith("已完成增强_")||p.startsWith("已完成校对_")||p.startsWith("异常_")||p.startsWith("异常增强_")||p.startsWith("异常校对_")||p.startsWith("已停止_")||p.startsWith("已停止增强_")||p.startsWith("已停止校对_"))p.startsWith("任务增强_")?($=p.substring(5),k=!0):p.startsWith("任务_")||p.startsWith("校对_")?$=p.substring(3):p.startsWith("已完成增强_")?($=p.substring(6),k=!0):p.startsWith("已完成校对_")?$=p.substring(6):p.startsWith("已完成_")?$=p.substring(4):p.startsWith("异常增强_")?($=p.substring(5),k=!0):p.startsWith("异常校对_")?$=p.substring(5):p.startsWith("异常_")?$=p.substring(3):p.startsWith("已停止增强_")?($=p.substring(6),k=!0):p.startsWith("已停止校对_")?$=p.substring(6):p.startsWith("已停止_")&&($=p.substring(4));else continue;if($){let X;r.Start>=D.Start&&r.End<=D.End?X="completely_within":r.Start<=D.Start&&r.End>=D.End?X="completely_contains":X="partial_overlap",h.push({taskId:$,control:l,task:B?null:a[$]||null,isEnhanced:k,isEmptyTitle:B,overlapType:X,controlRange:{start:D.Start,end:D.End},selectionRange:{start:r.Start,end:r.End}})}}}catch{continue}return h.length===0?null:{primary:h[0],all:h}}catch(s){return e.value+=`<span class="log-item error">检查选区位置出错: ${s.message}</span><br/>`,null}},xt=()=>{ke.value=!0},Ee=()=>{ke.value=!1},Xe=async(s,c=!1)=>{xt();try{await xe(s,c)}finally{Ee()}},_=s=>new Promise((c,r)=>{P.show=!0,P.message=s,P.resolveCallback=c,P.rejectCallback=r}),g=s=>{P.show=!1,s&&P.resolveCallback?P.resolveCallback(!0):P.resolveCallback&&P.resolveCallback(!1),P.resolveCallback=null,P.rejectCallback=null},N=(s,c,r="error")=>{L.show=!0,L.title=s,L.message=c,L.type=r},ee=()=>{L.show=!1,L.title="",L.message="",L.type="error"},fe=async()=>{try{e.value+='<span class="log-item info">正在从服务器加载学科和年级选择...</span><br/>';const s=await se.getSubjectAndStage();s.success&&s.data?(s.data.subject&&(q.value=s.data.subject),s.data.stage&&(R.value=s.data.stage),e.value+=`<span class="log-item success">已从服务器加载学科(${q.value})和年级(${R.value})设置</span><br/>`):e.value+='<span class="log-item warning">未找到保存的学科和年级设置，使用默认值</span><br/>'}catch(s){console.error("从服务器加载学科和年级设置失败:",s),e.value+=`<span class="log-item error">从服务器加载设置失败: ${s.message}</span><br/>`}},te=async()=>{try{if(q.value||R.value){e.value+=`<span class="log-item info">正在保存学科(${q.value})和年级(${R.value})设置到服务器...</span><br/>`;const s=await se.setSubjectAndStage(q.value,R.value);s&&s.success?e.value+='<span class="log-item success">学科和年级设置已保存到服务器</span><br/>':e.value+=`<span class="log-item warning">保存学科和年级设置失败: ${(s==null?void 0:s.message)||"未知错误"}</span><br/>`}}catch(s){console.error("保存学科和年级到服务器失败:",s),e.value+=`<span class="log-item error">保存设置到服务器失败: ${s.message}</span><br/>`}},be=async()=>{try{V.value=2,J.value=V.value===2,J.value?e.value+=`<span class="log-item info">校对功能已启用（企业ID: ${V.value}）</span><br/>`:e.value+=`<span class="log-item info">校对功能不可用（企业ID: ${V.value}）</span><br/>`}catch(s){console.error("检查企业ID失败:",s),e.value+=`<span class="log-item error">检查企业ID失败: ${s.message}</span><br/>`,J.value=!1}},Ke=async(s,c)=>await O("为范围添加批注",async()=>{const r=f();if(!r||!s)return!1;const h=r.Comments.Add(s,c);try{h!=null&&h.Range&&(h.Range.ParagraphFormat.Reset(),h.Range.ParagraphFormat.LineSpacingRule=3,h.Range.ParagraphFormat.LineSpacing=10,h.Edit())}catch(o){e.value+=`<span class="log-item warning">设置批注段落格式失败: ${o.message}</span><br/>`}return!0}),We=async(s,c)=>{try{if(!s||!Array.isArray(s))return e.value+='<span class="log-item error">校对JSON数据格式错误：数据不是数组格式</span><br/>',!1;const r=f();let h=null,o=null;if(r&&r.ContentControls)for(let $=1;$<=r.ContentControls.Count;$++)try{const k=r.ContentControls.Item($);if((k==null?void 0:k.Title)===`校对_${c}`){h=k,o=k.Range,e.value+='<span class="log-item info">找到校对控件，准备添加批注</span><br/>';break}}catch{continue}if(!o){e.value+='<span class="log-item warning">未找到校对控件，尝试从任务信息恢复范围</span><br/>';const $=a[c];if($&&$.selectedText)try{const k=r.Range().Find;if(k.ClearFormatting(),k.Text=$.selectedText.substring(0,Math.min($.selectedText.length,100)),k.Forward=!0,k.Wrap=1,k.Execute()){const B=k.Parent;if($.selectedText.length>100){const X=B.Start+$.selectedText.length;o=r.Range(B.Start,Math.min(X,r.Range().End))}else o=B;e.value+='<span class="log-item info">通过文本查找定位到原始范围</span><br/>'}else return e.value+='<span class="log-item error">无法定位到原始控件范围</span><br/>',!1}catch(k){return e.value+=`<span class="log-item error">查找原始控件范围失败: ${k.message}</span><br/>`,!1}else return e.value+='<span class="log-item error">无法获取任务信息或选中文本</span><br/>',!1}let l=0,p=0,D=[];for(const $ of s){if(!$.mode1||!Array.isArray($.mode1)){e.value+='<span class="log-item warning">跳过无效数据项：缺少mode1数组</span><br/>';continue}for(const k of $.mode1){if(!k.error_info||!Array.isArray(k.error_info)){e.value+='<span class="log-item warning">跳过无error_info的题目</span><br/>';continue}const B=k.quest_html||"",X=k.quest_type||"";e.value+=`<span class="log-item info">处理${X}题目，发现${k.error_info.length}个错误信息</span><br/>`;for(const Y of k.error_info)try{let Z="";if(Y.error_info&&(Z+=`【错误类型】${Y.error_info}\r`),Y.fix_info&&(Z+=`【建议】${Y.fix_info}`),Y.keywords&&Y.keywords.trim()){let re=h?h.Range:o;h.LockContents=!1,E(Z,Y.keywords.trim(),0,h.Range)?(l++,e.value+=`<span class="log-item success">已为关键词"${Y.keywords.trim()}"添加批注: ${Z}</span><br/>`):(D.push({comment:Z,keyword:Y.keywords.trim()}),e.value+=`<span class="log-item warning">关键词"${Y.keywords.trim()}"未找到，将为整个范围添加批注</span><br/>`)}else D.push({comment:Z,keyword:null})}catch(Z){p++,e.value+=`<span class="log-item error">处理单个错误信息失败: ${Z.message}</span><br/>`}}}if(D.length>0){e.value+=`<span class="log-item info">为整个控件范围添加${D.length}个批注</span><br/>`;for(const $ of D)try{let k=$.comment,B=h?h.Range:o;h.LockContents=!1,Ke(h.Range,k)?(l++,e.value+=`<span class="log-item success">已为整个范围添加批注${$.keyword?`（关键词：${$.keyword}）`:""}</span><br/>`):p++}catch(k){p++,e.value+=`<span class="log-item error">为整个范围添加批注失败: ${k.message}</span><br/>`}}return l>0?(e.value+=`<span class="log-item success">校对任务${c.substring(0,8)}处理完成：成功添加${l}个批注</span><br/>`,p>0&&(e.value+=`<span class="log-item warning">校对任务${c.substring(0,8)}：${p}个批注添加失败</span><br/>`),h.Title=`已完成校对_${c}`,!0):(e.value+=`<span class="log-item error">校对任务${c.substring(0,8)}：未能成功添加任何批注</span><br/>`,!1)}catch(r){return e.value+=`<span class="log-item error">处理校对JSON数据失败: ${r.message}</span><br/>`,!1}},O=async(s,c)=>{const r=window.Application;if(!r)throw new Error("无法获取 Application 对象");const h=r.ScreenUpdating;let o=null;try{r.ScreenUpdating=!1,o=setTimeout(()=>{try{window.Application&&(window.Application.ScreenUpdating=!0,e.value+=`<span class="log-item warning">兜底策略：强制恢复屏幕更新（${s}）</span><br/>`)}catch(D){console.error("兜底恢复屏幕更新失败:",D)}},3e3);const l=f();return l&&l.UndoRecord&&l.UndoRecord.StartCustomRecord(s),await c()}catch(l){throw e.value+=`<span class="log-item error">${s}操作失败: ${l.message}</span><br/>`,l}finally{o&&(clearTimeout(o),o=null);try{window.Application&&(window.Application.ScreenUpdating=h!==!1);const l=f();l&&l.UndoRecord&&l.UndoRecord.EndCustomRecord()}catch(l){e.value+=`<span class="log-item warning">清理VBA操作状态时出错: ${l.message}</span><br/>`;try{window.Application&&(window.Application.ScreenUpdating=!0,e.value+='<span class="log-item info">强制恢复屏幕更新成功</span><br/>')}catch(p){e.value+=`<span class="log-item error">强制恢复屏幕更新也失败: ${p.message}</span><br/>`,console.error("强制恢复屏幕更新失败:",p)}}setTimeout(()=>{try{window.Application&&window.Application.ScreenUpdating===!1&&(window.Application.ScreenUpdating=!0,e.value+='<span class="log-item warning">延迟检查发现屏幕更新仍被关闭，已强制开启</span><br/>')}catch(l){console.error("延迟检查屏幕更新状态失败:",l)}},100)}};return{docName:t,selected:n,logger:e,map:a,watchedDir:b,subject:q,stage:R,subjectOptions:de,stageOptions:ne,fetchWatchedDir:I,clearLog:ge,getCurrentDocument:f,checkDocumentFormat:w,getTaskStatusClass:C,getTaskStatusText:S,getElapsedTime:m,terminateTask:A,stopTaskWithoutRemovingControl:U,run1:ht,run2:Nt,runCheck:Bt,getHeaderStatusClass:Vt,getRunningTasksCount:Pt,getCompletedTasksCount:$t,getReleasableTasksCount:y,getErrorTasksCount:W,setupLifecycle:pe,navigateToTaskControl:He,forceCleanAllTasks:G,ws:Ae,wsMessages:Se,initWebSocket:De,handleWatcherEvent:Le,urlMonitorTasks:Q,monitorUrlForTask:nt,stopUrlMonitoring:Ue,getUrlMonitorStatus:at,forceUrlCheck:bt,cleanupUrlMonitoringTasks:ue,tryRemoveTaskPlaceholder:xe,isLoading:ke,isSelectionInTaskControl:ze,tryRemoveTaskPlaceholderWithLoading:Xe,showConfirm:_,handleConfirm:g,confirmDialog:P,errorDialog:L,showErrorDialog:N,hideErrorDialog:ee,loadSubjectAndStage:fe,saveSubjectAndStage:te,enterpriseId:V,isCheckingVisible:J,checkEnterpriseAndSetCheckingVisibility:be,processCheckingJson:We,isBatchTestMode:H,batchInsertAllTasks:oe,getBatchWaitingTasksCount:le}}const Bn={name:"FileWatcher",data(){return{showSettings:!1,status:{status:"stopped",startTime:null,watchDir:"C:\\Temp",processedFiles:0,lastError:null},recentEvents:[],newWatchDir:"",isUpdating:!1,updateMessage:"",updateSuccess:!1,wasRunningBeforeUpdate:!1,downloadPath:"C:\\Temp\\Downloads",newDownloadPath:"",isUpdatingDownloadPath:!1,downloadPathUpdateMessage:"",downloadPathUpdateSuccess:!1,addonConfigPath:"C:\\ww-wps-addon\\cfg",newAddonConfigPath:"",isUpdatingAddonConfigPath:!1,addonConfigPathUpdateMessage:"",addonConfigPathUpdateSuccess:!1,versionConfig:it.getVersionConfig(),selectedEdition:it.getEdition(),isSwitchingEdition:!1,editionSwitchMessage:"",editionSwitchSuccess:!1,showTooltip:!1,tooltipUpdateMessage:"",tooltipUpdateSuccess:!1,currentSaveMethod:"method3",saveMethodUpdateMessage:"",saveMethodUpdateSuccess:!1}},computed:{formatDuration(){if(!this.status.startTime)return"未启动";const t=new Date(this.status.startTime),e=new Date-t,a=Math.floor(e/(1e3*60*60)),i=Math.floor(e%(1e3*60*60)/(1e3*60)),v=Math.floor(e%(1e3*60)/1e3);return`${a}小时 ${i}分 ${v}秒`},userInfo(){var n,e;const t=(e=(n=window.Application)==null?void 0:n.PluginStorage)==null?void 0:e.getItem("user_info");return t?JSON.parse(t):null}},methods:{async fetchStatus(){try{const t=await se.getWatcherStatus();t.success&&(this.status=t.data,t.data.addonConfigPath&&(this.addonConfigPath=t.data.addonConfigPath))}catch(t){console.error("获取状态失败:",t)}},async controlService(){try{const t=this.status.status==="running"?"stopWatcher":"startWatcher";await se[t](),await this.fetchStatus()}catch(t){console.error("控制服务失败:",t)}},async updateWatchDir(){if(!(!this.newWatchDir||this.isUpdating)){this.isUpdating=!0,this.updateMessage="";try{if(this.wasRunningBeforeUpdate=this.status.status==="running",this.wasRunningBeforeUpdate&&(this.updateMessage="正在停止服务以更新目录...",await se.stopWatcher(),await new Promise(n=>setTimeout(n,1e3)),await this.fetchStatus(),this.status.status==="running"))throw new Error("无法停止服务，目录更新失败");const t=await se.setWatchDirectory(this.newWatchDir);t.success?(this.updateSuccess=!0,this.updateMessage=`上传目录已更新为: ${this.newWatchDir}`,this.wasRunningBeforeUpdate&&(this.updateMessage+="，正在重新启动服务...",await se.startWatcher()),await this.fetchStatus(),this.newWatchDir=""):(this.updateSuccess=!1,this.updateMessage=`目录更新失败: ${t.message||"未知错误"}`)}catch(t){this.updateSuccess=!1,this.updateMessage=`发生错误: ${t.message}`,console.error("更新上传目录失败:",t)}finally{this.isUpdating=!1}}},async fetchDownloadPath(){try{const t=await se.getDownloadPath();t.success&&t.downloadPath&&(this.downloadPath=t.downloadPath)}catch(t){console.error("获取下载路径失败:",t)}},async updateDownloadPath(){if(!(!this.newDownloadPath||this.isUpdatingDownloadPath)){this.isUpdatingDownloadPath=!0,this.downloadPathUpdateMessage="";try{const t=await se.setDownloadPath(this.newDownloadPath);t.success?(this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已更新为: ${this.newDownloadPath}`,this.downloadPath=this.newDownloadPath,this.newDownloadPath=""):(this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`下载路径更新失败: ${t.message||"未知错误"}`)}catch(t){this.downloadPathUpdateSuccess=!1,this.downloadPathUpdateMessage=`发生错误: ${t.message}`,console.error("更新下载路径失败:",t)}finally{this.isUpdatingDownloadPath=!1}}},async fetchAddonConfigPath(){try{const t=await se.getAddonConfigPath();t.success&&t.addonConfigPath&&(this.addonConfigPath=t.addonConfigPath)}catch(t){console.error("获取配置路径失败:",t)}},async updateAddonConfigPath(){if(!(!this.newAddonConfigPath||this.isUpdatingAddonConfigPath)){this.isUpdatingAddonConfigPath=!0,this.addonConfigPathUpdateMessage="";try{const t=await se.setAddonConfigPath(this.newAddonConfigPath);t.success?(this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已更新为: ${this.newAddonConfigPath}`,this.addonConfigPath=this.newAddonConfigPath,this.newAddonConfigPath=""):(this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`配置路径更新失败: ${t.message||"未知错误"}`)}catch(t){this.addonConfigPathUpdateSuccess=!1,this.addonConfigPathUpdateMessage=`发生错误: ${t.message}`,console.error("更新配置路径失败:",t)}finally{this.isUpdatingAddonConfigPath=!1}}},handleWatcherEvent(t){t.eventType==="start"?(this.status.status="running",this.status.startTime=t.data.startTime):t.eventType==="stop"?(this.status.status="stopped",this.status.processedFiles=t.data.processedFiles):t.eventType==="uploadSuccess"?this.status.processedFiles=t.data.totalProcessed:t.eventType==="urlDownloadPathChanged"?(this.downloadPath=t.data.path,this.downloadPathUpdateSuccess=!0,this.downloadPathUpdateMessage=`下载路径已变更为: ${t.data.path}`):t.eventType==="addonConfigPathChanged"?(this.addonConfigPath=t.data.path,this.addonConfigPathUpdateSuccess=!0,this.addonConfigPathUpdateMessage=`配置路径已变更为: ${t.data.path}`):t.eventType==="urlFileDownloaded"&&this.recentEvents.unshift({...t,timestamp:new Date().toISOString()}),this.recentEvents.unshift(t),this.recentEvents.length>50&&this.recentEvents.pop()},handleUrlMonitorEvent(t){console.log("URL监控事件:",t)},async switchEdition(){if(!(this.isSwitchingEdition||this.selectedEdition===this.versionConfig.edition)){this.isSwitchingEdition=!0,this.editionSwitchMessage="",this.editionSwitchSuccess=!1;try{await it.setEdition(this.selectedEdition),this.editionSwitchSuccess=!0,this.editionSwitchMessage=`版本已成功切换到: ${this.selectedEdition==="wanwei"?"万唯版本":"合心版本"}`}catch(t){this.editionSwitchSuccess=!1,this.editionSwitchMessage=`版本切换失败: ${t.message}`,console.error("版本切换失败:",t)}finally{this.isSwitchingEdition=!1}}},formatTime(t){return new Date(t).toLocaleTimeString()},async handleLogout(){try{await Sn()?window.location.hash="#/login":alert("退出登录失败，请稍后重试")}catch(t){console.error("Logout error:",t),alert("退出登录失败，请稍后重试")}},getEventTypeText(t){return{start:"启动",stop:"停止",filesFound:"发现文件",uploadStart:"开始上传",uploadSuccess:"上传成功",uploadError:"上传失败",deleteError:"删除失败",error:"错误",urlMonitorUpdate:"URL状态",urlFileDownloaded:"文件下载",urlFileDownloadError:"下载失败",urlDownloadPathChanged:"下载路径更新",addonConfigPathChanged:"配置路径更新"}[t]||t},getEventMessage(t){switch(t.eventType){case"start":return`服务已启动，上传目录: ${t.data.watchDir}`;case"stop":return`服务已停止，处理了 ${t.data.processedFiles} 个文件`;case"filesFound":return`发现 ${t.data.count} 个新文件`;case"uploadStart":return`正在上传: ${t.data.file}`;case"uploadSuccess":return`文件 ${t.data.file} 上传成功`;case"uploadError":return`文件 ${t.data.file} 上传失败`;case"deleteError":return`无法删除文件: ${t.data.file}`;case"dirCreated":return`创建目录: ${t.data.dir}`;case"urlMonitorUpdate":const n=t.data.status==="accessible"?"可访问":t.data.status==="inaccessible"?"不可访问":t.data.status==="error"?"检查出错":"未知状态";return`URL "${t.data.url.substring(0,40)}..." ${n}`;case"urlFileDownloaded":return`文件已下载: ${t.data.filePath}`;case"urlFileDownloadError":return`下载失败: ${t.data.error}`;case"urlDownloadPathChanged":return`下载路径已更新: ${t.data.path}`;case"addonConfigPathChanged":return`配置路径已更新: ${t.data.path}`;case"error":return`${t.data.error||"错误"}: ${t.data.message}`;default:if(t.data){const e=Object.keys(t.data)[0];return e?`${e}: ${t.data[e]}`:"事件通知"}return"系统事件"}},async fetchTooltipSetting(){try{const t=await se.sendRequest("config","getShowTooltip");t.success&&t.showTooltip!==void 0&&(this.showTooltip=t.showTooltip)}catch(t){console.error("获取Tooltip设置失败:",t)}},async updateTooltipSetting(){this.tooltipUpdateMessage="",this.tooltipUpdateSuccess=!1;try{console.log("发送tooltip设置:",this.showTooltip);const t=!!this.showTooltip,n=await se.sendRequest("config","setShowTooltip",{showTooltip:t});n.success?(this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage="Tooltip设置已更新",console.log("Tooltip设置更新成功")):(this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`Tooltip设置更新失败: ${n.message||"未知错误"}`,console.error("Tooltip设置更新失败:",n.message))}catch(t){this.tooltipUpdateSuccess=!1,this.tooltipUpdateMessage=`发生错误: ${t.message}`,console.error("更新Tooltip设置失败:",t)}},async fetchSaveMethod(){try{const t=await se.sendRequest("config","getSaveMethod");t.success&&t.saveMethod?(this.currentSaveMethod=t.saveMethod,t.saveMethod!=="method3"&&(console.log(`当前保存方式为${t.saveMethod}，自动切换到方式三`),await this.setSaveMethodToThree())):(console.log("未检测到保存方式设置，自动设置为方式三"),await this.setSaveMethodToThree())}catch(t){console.error("获取保存方式设置失败:",t),await this.setSaveMethodToThree()}},async setSaveMethodToThree(){try{const t=await se.sendRequest("config","setSaveMethod",{saveMethod:"method3"});t.success?(this.currentSaveMethod="method3",console.log("已自动设置保存方式为方式三")):console.error("自动设置保存方式为方式三失败:",t.message)}catch(t){console.error("自动设置保存方式失败:",t)}},async updateSaveMethod(){this.saveMethodUpdateMessage="",this.saveMethodUpdateSuccess=!1;try{const t=await se.sendRequest("config","setSaveMethod",{saveMethod:this.currentSaveMethod});t.success?(this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage="保存方式设置已更新",console.log("保存方式设置更新成功:",this.currentSaveMethod)):(this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`保存方式设置更新失败: ${t.message||"未知错误"}`,console.error("保存方式设置更新失败:",t.message))}catch(t){this.saveMethodUpdateSuccess=!1,this.saveMethodUpdateMessage=`发生错误: ${t.message}`,console.error("更新保存方式设置失败:",t)}},handleConfigEvent(t){console.log("配置事件:",t),t.eventType==="tooltipSettingChanged"&&(this.showTooltip=t.data.showTooltip,this.tooltipUpdateSuccess=!0,this.tooltipUpdateMessage=`Tooltip设置已变更为: ${t.data.showTooltip?"显示":"隐藏"}`),t.eventType==="saveMethodChanged"&&(this.currentSaveMethod=t.data.saveMethod,this.saveMethodUpdateSuccess=!0,this.saveMethodUpdateMessage=`保存方式已变更为: ${t.data.saveMethod==="method1"?"方式一":t.data.saveMethod==="method2"?"方式二":t.data.saveMethod==="method3"?"方式三":"方式四"}`)},setupEventListeners(){this.removeWatcherListener=se.addEventListener("watcher",this.handleWatcherEvent),this.removeUrlMonitorListener=se.addEventListener("urlMonitor",this.handleUrlMonitorEvent),this.removeConfigListener=se.addEventListener("config",this.handleConfigEvent)}},async mounted(){this.removeVersionListener=it.onVersionChange(t=>{this.versionConfig=it.getVersionConfig(),this.selectedEdition=it.getEdition()}),await se.connect(),this.setupEventListeners(),await this.fetchStatus(),await this.fetchDownloadPath(),await this.fetchAddonConfigPath(),await this.fetchTooltipSetting(),await this.fetchSaveMethod()},beforeDestroy(){this.removeWatcherListener&&this.removeWatcherListener(),this.removeUrlMonitorListener&&this.removeUrlMonitorListener(),this.removeConfigListener&&this.removeConfigListener(),this.removeVersionListener&&this.removeVersionListener()}},Nn={class:"file-watcher"},Vn={class:"settings-modal"},Hn={class:"modal-content"},zn={class:"modal-header"},qn={class:"version-tag"},Yn={key:0,class:"user-info"},Jn={key:1,class:"user-info inner-tag"},Xn={class:"header-actions"},Kn={class:"modal-body"},Gn={class:"status-section"},Zn={class:"status-item"},Qn={key:0,class:"status-item"},ea={class:"directory-section"},ta={class:"directory-form"},sa={class:"radio-group"},na={class:"radio-item"},aa={class:"radio-item"},ra={class:"radio-item"},oa={key:0,class:"radio-item"},ia={key:1,class:"directory-section"},la={class:"directory-form"},ca={class:"form-group"},ua=["placeholder"],pa=["disabled"],da={key:2,class:"directory-section"},fa={class:"directory-form"},ha={class:"form-group"},ga=["placeholder"],va=["disabled"],ma={key:3,class:"directory-section"},ba={class:"directory-form"},wa={class:"form-group"},ya=["placeholder"],xa=["disabled"],Ta={key:4,class:"events-section"},Ca={class:"events-list"},ka={class:"event-time"},$a={class:"event-message"},Sa={key:1,class:"modal-footer"};function Ea(t,n,e,a,i,v){var x,M,b,T,P,L,q,R,V,J,H,oe,le,de,d,ne,I,ce,ge,f,w,E,C,S;return F(),j("div",Nn,[u("div",Vn,[u("div",Hn,[u("div",zn,[u("h3",null,[En(ae(i.versionConfig.shortName)+"设置 ",1),u("span",qn,ae(i.versionConfig.appVersion),1),v.userInfo?(F(),j("span",Yn,"欢迎您，"+ae(v.userInfo.nickname),1)):z("",!0),((b=(M=(x=v.userInfo)==null?void 0:x.orgs)==null?void 0:M[0])==null?void 0:b.orgId)===2?(F(),j("span",Jn,"内部版本号：1.1.28")):z("",!0)]),u("div",Xn,[u("button",{class:"logout-btn",onClick:n[0]||(n[0]=(...m)=>v.handleLogout&&v.handleLogout(...m)),title:"退出登录"},n[21]||(n[21]=[u("span",{class:"icon-logout"},null,-1)])),u("button",{class:"close-btn",onClick:n[1]||(n[1]=()=>t.$emit("close"))},"×")])]),u("div",Kn,[u("div",Gn,[u("div",Zn,[n[22]||(n[22]=u("span",{class:"label"},"状态：",-1)),u("span",{class:_e(["status-badge",i.status.status])},ae(i.status.status==="running"?"运行中":"已停止"),3)]),((L=(P=(T=v.userInfo)==null?void 0:T.orgs)==null?void 0:P[0])==null?void 0:L.orgId)===2?(F(),j("div",Qn,[n[23]||(n[23]=u("span",{class:"label"},"本次上传：",-1)),u("span",null,ae(i.status.processedFiles||0)+" 个文件",1)])):z("",!0),u("div",ea,[n[28]||(n[28]=u("h4",null,"保存方式设置",-1)),u("div",ta,[u("div",sa,[u("label",na,[Ge(u("input",{type:"radio","onUpdate:modelValue":n[2]||(n[2]=m=>i.currentSaveMethod=m),value:"method1",onChange:n[3]||(n[3]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[es,i.currentSaveMethod]]),n[24]||(n[24]=u("span",{class:"radio-label"},"方式一",-1))]),u("label",aa,[Ge(u("input",{type:"radio","onUpdate:modelValue":n[4]||(n[4]=m=>i.currentSaveMethod=m),value:"method2",onChange:n[5]||(n[5]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[es,i.currentSaveMethod]]),n[25]||(n[25]=u("span",{class:"radio-label"},"方式二",-1))]),u("label",ra,[Ge(u("input",{type:"radio","onUpdate:modelValue":n[6]||(n[6]=m=>i.currentSaveMethod=m),value:"method3",onChange:n[7]||(n[7]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[es,i.currentSaveMethod]]),n[26]||(n[26]=u("span",{class:"radio-label"},"方式三 (默认)",-1))]),((V=(R=(q=v.userInfo)==null?void 0:q.orgs)==null?void 0:R[0])==null?void 0:V.orgId)===2?(F(),j("label",oa,[Ge(u("input",{type:"radio","onUpdate:modelValue":n[8]||(n[8]=m=>i.currentSaveMethod=m),value:"method4",onChange:n[9]||(n[9]=(...m)=>v.updateSaveMethod&&v.updateSaveMethod(...m))},null,544),[[es,i.currentSaveMethod]]),n[27]||(n[27]=u("span",{class:"radio-label"},"方式四",-1))])):z("",!0)]),i.saveMethodUpdateMessage?(F(),j("div",{key:0,class:_e(["update-message",i.saveMethodUpdateSuccess?"success":"error"])},ae(i.saveMethodUpdateMessage),3)):z("",!0)])])]),z("",!0),((oe=(H=(J=v.userInfo)==null?void 0:J.orgs)==null?void 0:H[0])==null?void 0:oe.orgId)===2?(F(),j("div",ia,[n[32]||(n[32]=u("h4",null,"上传目录设置",-1)),u("div",la,[u("div",ca,[n[31]||(n[31]=u("span",{class:"label"},"路径：",-1)),Ge(u("input",{type:"text",class:"directory-input","onUpdate:modelValue":n[12]||(n[12]=m=>i.newWatchDir=m),placeholder:i.status.watchDir||"C:\\Temp"},null,8,ua),[[ps,i.newWatchDir]]),u("button",{class:"action-btn",onClick:n[13]||(n[13]=(...m)=>v.updateWatchDir&&v.updateWatchDir(...m)),disabled:i.isUpdating||!i.newWatchDir},ae(i.isUpdating?"更新中...":"更新目录"),9,pa)]),i.updateMessage?(F(),j("div",{key:0,class:_e(["update-message",i.updateSuccess?"success":"error"])},ae(i.updateMessage),3)):z("",!0)])])):z("",!0),((d=(de=(le=v.userInfo)==null?void 0:le.orgs)==null?void 0:de[0])==null?void 0:d.orgId)===2?(F(),j("div",da,[n[34]||(n[34]=u("h4",null,"下载目录设置",-1)),u("div",fa,[u("div",ha,[n[33]||(n[33]=u("span",{class:"label"},"路径：",-1)),Ge(u("input",{type:"text",class:"directory-input","onUpdate:modelValue":n[14]||(n[14]=m=>i.newDownloadPath=m),placeholder:i.downloadPath||"C:\\Temp\\Downloads"},null,8,ga),[[ps,i.newDownloadPath]]),u("button",{class:"action-btn",onClick:n[15]||(n[15]=(...m)=>v.updateDownloadPath&&v.updateDownloadPath(...m)),disabled:i.isUpdatingDownloadPath||!i.newDownloadPath},ae(i.isUpdatingDownloadPath?"更新中...":"更新路径"),9,va)]),i.downloadPathUpdateMessage?(F(),j("div",{key:0,class:_e(["update-message",i.downloadPathUpdateSuccess?"success":"error"])},ae(i.downloadPathUpdateMessage),3)):z("",!0)])])):z("",!0),((ce=(I=(ne=v.userInfo)==null?void 0:ne.orgs)==null?void 0:I[0])==null?void 0:ce.orgId)===2?(F(),j("div",ma,[n[36]||(n[36]=u("h4",null,"配置目录设置",-1)),u("div",ba,[u("div",wa,[n[35]||(n[35]=u("span",{class:"label"},"路径：",-1)),Ge(u("input",{type:"text",class:"directory-input","onUpdate:modelValue":n[16]||(n[16]=m=>i.newAddonConfigPath=m),placeholder:i.addonConfigPath||"C:\\ww-wps-addon\\cfg"},null,8,ya),[[ps,i.newAddonConfigPath]]),u("button",{class:"action-btn",onClick:n[17]||(n[17]=(...m)=>v.updateAddonConfigPath&&v.updateAddonConfigPath(...m)),disabled:i.isUpdatingAddonConfigPath||!i.newAddonConfigPath},ae(i.isUpdatingAddonConfigPath?"更新中...":"更新路径"),9,xa)]),i.addonConfigPathUpdateMessage?(F(),j("div",{key:0,class:_e(["update-message",i.addonConfigPathUpdateSuccess?"success":"error"])},ae(i.addonConfigPathUpdateMessage),3)):z("",!0)])])):z("",!0),((w=(f=(ge=v.userInfo)==null?void 0:ge.orgs)==null?void 0:f[0])==null?void 0:w.orgId)===2?(F(),j("div",Ta,[n[37]||(n[37]=u("h4",null,"最近事件",-1)),u("div",Ca,[(F(!0),j(Et,null,It(i.recentEvents,(m,U)=>(F(),j("div",{key:U,class:"event-item"},[u("span",ka,ae(v.formatTime(m.timestamp)),1),u("span",{class:_e(["event-type",m.eventType])},ae(v.getEventTypeText(m.eventType)),3),u("span",$a,ae(v.getEventMessage(m)),1)]))),128))])])):z("",!0)]),z("",!0),((S=(C=(E=v.userInfo)==null?void 0:E.orgs)==null?void 0:C[0])==null?void 0:S.orgId)===2?(F(),j("div",Sa,[u("button",{class:_e(["control-btn",i.status.status==="running"?"stop":"start"]),onClick:n[20]||(n[20]=(...m)=>v.controlService&&v.controlService(...m))},ae(i.status.status==="running"?"停止服务":"启动服务"),3)])):z("",!0)])])])}const _a=tn(Bn,[["render",Ea],["__scopeId","data-v-06b66fef"]]);var Ne="top",Qe="bottom",et="right",Ve="left",Ts="auto",Gt=[Ne,Qe,et,Ve],Lt="start",Xt="end",Aa="clippingParents",nn="viewport",Ht="popper",Ma="reference",Us=Gt.reduce(function(t,n){return t.concat([n+"-"+Lt,n+"-"+Xt])},[]),an=[].concat(Gt,[Ts]).reduce(function(t,n){return t.concat([n,n+"-"+Lt,n+"-"+Xt])},[]),Pa="beforeRead",Da="read",Oa="afterRead",Ra="beforeMain",Ia="main",Ua="afterMain",La="beforeWrite",Fa="write",ja="afterWrite",Wa=[Pa,Da,Oa,Ra,Ia,Ua,La,Fa,ja];function dt(t){return t?(t.nodeName||"").toLowerCase():null}function qe(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var n=t.ownerDocument;return n&&n.defaultView||window}return t}function Mt(t){var n=qe(t).Element;return t instanceof n||t instanceof Element}function Ze(t){var n=qe(t).HTMLElement;return t instanceof n||t instanceof HTMLElement}function Cs(t){if(typeof ShadowRoot>"u")return!1;var n=qe(t).ShadowRoot;return t instanceof n||t instanceof ShadowRoot}function Ba(t){var n=t.state;Object.keys(n.elements).forEach(function(e){var a=n.styles[e]||{},i=n.attributes[e]||{},v=n.elements[e];!Ze(v)||!dt(v)||(Object.assign(v.style,a),Object.keys(i).forEach(function(x){var M=i[x];M===!1?v.removeAttribute(x):v.setAttribute(x,M===!0?"":M)}))})}function Na(t){var n=t.state,e={popper:{position:n.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(n.elements.popper.style,e.popper),n.styles=e,n.elements.arrow&&Object.assign(n.elements.arrow.style,e.arrow),function(){Object.keys(n.elements).forEach(function(a){var i=n.elements[a],v=n.attributes[a]||{},x=Object.keys(n.styles.hasOwnProperty(a)?n.styles[a]:e[a]),M=x.reduce(function(b,T){return b[T]="",b},{});!Ze(i)||!dt(i)||(Object.assign(i.style,M),Object.keys(v).forEach(function(b){i.removeAttribute(b)}))})}}const rn={name:"applyStyles",enabled:!0,phase:"write",fn:Ba,effect:Na,requires:["computeStyles"]};function pt(t){return t.split("-")[0]}var At=Math.max,os=Math.min,Ft=Math.round;function bs(){var t=navigator.userAgentData;return t!=null&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(n){return n.brand+"/"+n.version}).join(" "):navigator.userAgent}function on(){return!/^((?!chrome|android).)*safari/i.test(bs())}function jt(t,n,e){n===void 0&&(n=!1),e===void 0&&(e=!1);var a=t.getBoundingClientRect(),i=1,v=1;n&&Ze(t)&&(i=t.offsetWidth>0&&Ft(a.width)/t.offsetWidth||1,v=t.offsetHeight>0&&Ft(a.height)/t.offsetHeight||1);var x=Mt(t)?qe(t):window,M=x.visualViewport,b=!on()&&e,T=(a.left+(b&&M?M.offsetLeft:0))/i,P=(a.top+(b&&M?M.offsetTop:0))/v,L=a.width/i,q=a.height/v;return{width:L,height:q,top:P,right:T+L,bottom:P+q,left:T,x:T,y:P}}function ks(t){var n=jt(t),e=t.offsetWidth,a=t.offsetHeight;return Math.abs(n.width-e)<=1&&(e=n.width),Math.abs(n.height-a)<=1&&(a=n.height),{x:t.offsetLeft,y:t.offsetTop,width:e,height:a}}function ln(t,n){var e=n.getRootNode&&n.getRootNode();if(t.contains(n))return!0;if(e&&Cs(e)){var a=n;do{if(a&&t.isSameNode(a))return!0;a=a.parentNode||a.host}while(a)}return!1}function mt(t){return qe(t).getComputedStyle(t)}function Va(t){return["table","td","th"].indexOf(dt(t))>=0}function kt(t){return((Mt(t)?t.ownerDocument:t.document)||window.document).documentElement}function ls(t){return dt(t)==="html"?t:t.assignedSlot||t.parentNode||(Cs(t)?t.host:null)||kt(t)}function Ls(t){return!Ze(t)||mt(t).position==="fixed"?null:t.offsetParent}function Ha(t){var n=/firefox/i.test(bs()),e=/Trident/i.test(bs());if(e&&Ze(t)){var a=mt(t);if(a.position==="fixed")return null}var i=ls(t);for(Cs(i)&&(i=i.host);Ze(i)&&["html","body"].indexOf(dt(i))<0;){var v=mt(i);if(v.transform!=="none"||v.perspective!=="none"||v.contain==="paint"||["transform","perspective"].indexOf(v.willChange)!==-1||n&&v.willChange==="filter"||n&&v.filter&&v.filter!=="none")return i;i=i.parentNode}return null}function Zt(t){for(var n=qe(t),e=Ls(t);e&&Va(e)&&mt(e).position==="static";)e=Ls(e);return e&&(dt(e)==="html"||dt(e)==="body"&&mt(e).position==="static")?n:e||Ha(t)||n}function $s(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function qt(t,n,e){return At(t,os(n,e))}function za(t,n,e){var a=qt(t,n,e);return a>e?e:a}function cn(){return{top:0,right:0,bottom:0,left:0}}function un(t){return Object.assign({},cn(),t)}function pn(t,n){return n.reduce(function(e,a){return e[a]=t,e},{})}var qa=function(n,e){return n=typeof n=="function"?n(Object.assign({},e.rects,{placement:e.placement})):n,un(typeof n!="number"?n:pn(n,Gt))};function Ya(t){var n,e=t.state,a=t.name,i=t.options,v=e.elements.arrow,x=e.modifiersData.popperOffsets,M=pt(e.placement),b=$s(M),T=[Ve,et].indexOf(M)>=0,P=T?"height":"width";if(!(!v||!x)){var L=qa(i.padding,e),q=ks(v),R=b==="y"?Ne:Ve,V=b==="y"?Qe:et,J=e.rects.reference[P]+e.rects.reference[b]-x[b]-e.rects.popper[P],H=x[b]-e.rects.reference[b],oe=Zt(v),le=oe?b==="y"?oe.clientHeight||0:oe.clientWidth||0:0,de=J/2-H/2,d=L[R],ne=le-q[P]-L[V],I=le/2-q[P]/2+de,ce=qt(d,I,ne),ge=b;e.modifiersData[a]=(n={},n[ge]=ce,n.centerOffset=ce-I,n)}}function Ja(t){var n=t.state,e=t.options,a=e.element,i=a===void 0?"[data-popper-arrow]":a;i!=null&&(typeof i=="string"&&(i=n.elements.popper.querySelector(i),!i)||ln(n.elements.popper,i)&&(n.elements.arrow=i))}const Xa={name:"arrow",enabled:!0,phase:"main",fn:Ya,effect:Ja,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Wt(t){return t.split("-")[1]}var Ka={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ga(t,n){var e=t.x,a=t.y,i=n.devicePixelRatio||1;return{x:Ft(e*i)/i||0,y:Ft(a*i)/i||0}}function Fs(t){var n,e=t.popper,a=t.popperRect,i=t.placement,v=t.variation,x=t.offsets,M=t.position,b=t.gpuAcceleration,T=t.adaptive,P=t.roundOffsets,L=t.isFixed,q=x.x,R=q===void 0?0:q,V=x.y,J=V===void 0?0:V,H=typeof P=="function"?P({x:R,y:J}):{x:R,y:J};R=H.x,J=H.y;var oe=x.hasOwnProperty("x"),le=x.hasOwnProperty("y"),de=Ve,d=Ne,ne=window;if(T){var I=Zt(e),ce="clientHeight",ge="clientWidth";if(I===qe(e)&&(I=kt(e),mt(I).position!=="static"&&M==="absolute"&&(ce="scrollHeight",ge="scrollWidth")),I=I,i===Ne||(i===Ve||i===et)&&v===Xt){d=Qe;var f=L&&I===ne&&ne.visualViewport?ne.visualViewport.height:I[ce];J-=f-a.height,J*=b?1:-1}if(i===Ve||(i===Ne||i===Qe)&&v===Xt){de=et;var w=L&&I===ne&&ne.visualViewport?ne.visualViewport.width:I[ge];R-=w-a.width,R*=b?1:-1}}var E=Object.assign({position:M},T&&Ka),C=P===!0?Ga({x:R,y:J},qe(e)):{x:R,y:J};if(R=C.x,J=C.y,b){var S;return Object.assign({},E,(S={},S[d]=le?"0":"",S[de]=oe?"0":"",S.transform=(ne.devicePixelRatio||1)<=1?"translate("+R+"px, "+J+"px)":"translate3d("+R+"px, "+J+"px, 0)",S))}return Object.assign({},E,(n={},n[d]=le?J+"px":"",n[de]=oe?R+"px":"",n.transform="",n))}function Za(t){var n=t.state,e=t.options,a=e.gpuAcceleration,i=a===void 0?!0:a,v=e.adaptive,x=v===void 0?!0:v,M=e.roundOffsets,b=M===void 0?!0:M,T={placement:pt(n.placement),variation:Wt(n.placement),popper:n.elements.popper,popperRect:n.rects.popper,gpuAcceleration:i,isFixed:n.options.strategy==="fixed"};n.modifiersData.popperOffsets!=null&&(n.styles.popper=Object.assign({},n.styles.popper,Fs(Object.assign({},T,{offsets:n.modifiersData.popperOffsets,position:n.options.strategy,adaptive:x,roundOffsets:b})))),n.modifiersData.arrow!=null&&(n.styles.arrow=Object.assign({},n.styles.arrow,Fs(Object.assign({},T,{offsets:n.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:b})))),n.attributes.popper=Object.assign({},n.attributes.popper,{"data-popper-placement":n.placement})}const Qa={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Za,data:{}};var ss={passive:!0};function er(t){var n=t.state,e=t.instance,a=t.options,i=a.scroll,v=i===void 0?!0:i,x=a.resize,M=x===void 0?!0:x,b=qe(n.elements.popper),T=[].concat(n.scrollParents.reference,n.scrollParents.popper);return v&&T.forEach(function(P){P.addEventListener("scroll",e.update,ss)}),M&&b.addEventListener("resize",e.update,ss),function(){v&&T.forEach(function(P){P.removeEventListener("scroll",e.update,ss)}),M&&b.removeEventListener("resize",e.update,ss)}}const tr={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:er,data:{}};var sr={left:"right",right:"left",bottom:"top",top:"bottom"};function as(t){return t.replace(/left|right|bottom|top/g,function(n){return sr[n]})}var nr={start:"end",end:"start"};function js(t){return t.replace(/start|end/g,function(n){return nr[n]})}function Ss(t){var n=qe(t),e=n.pageXOffset,a=n.pageYOffset;return{scrollLeft:e,scrollTop:a}}function Es(t){return jt(kt(t)).left+Ss(t).scrollLeft}function ar(t,n){var e=qe(t),a=kt(t),i=e.visualViewport,v=a.clientWidth,x=a.clientHeight,M=0,b=0;if(i){v=i.width,x=i.height;var T=on();(T||!T&&n==="fixed")&&(M=i.offsetLeft,b=i.offsetTop)}return{width:v,height:x,x:M+Es(t),y:b}}function rr(t){var n,e=kt(t),a=Ss(t),i=(n=t.ownerDocument)==null?void 0:n.body,v=At(e.scrollWidth,e.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),x=At(e.scrollHeight,e.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),M=-a.scrollLeft+Es(t),b=-a.scrollTop;return mt(i||e).direction==="rtl"&&(M+=At(e.clientWidth,i?i.clientWidth:0)-v),{width:v,height:x,x:M,y:b}}function _s(t){var n=mt(t),e=n.overflow,a=n.overflowX,i=n.overflowY;return/auto|scroll|overlay|hidden/.test(e+i+a)}function dn(t){return["html","body","#document"].indexOf(dt(t))>=0?t.ownerDocument.body:Ze(t)&&_s(t)?t:dn(ls(t))}function Yt(t,n){var e;n===void 0&&(n=[]);var a=dn(t),i=a===((e=t.ownerDocument)==null?void 0:e.body),v=qe(a),x=i?[v].concat(v.visualViewport||[],_s(a)?a:[]):a,M=n.concat(x);return i?M:M.concat(Yt(ls(x)))}function ws(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function or(t,n){var e=jt(t,!1,n==="fixed");return e.top=e.top+t.clientTop,e.left=e.left+t.clientLeft,e.bottom=e.top+t.clientHeight,e.right=e.left+t.clientWidth,e.width=t.clientWidth,e.height=t.clientHeight,e.x=e.left,e.y=e.top,e}function Ws(t,n,e){return n===nn?ws(ar(t,e)):Mt(n)?or(n,e):ws(rr(kt(t)))}function ir(t){var n=Yt(ls(t)),e=["absolute","fixed"].indexOf(mt(t).position)>=0,a=e&&Ze(t)?Zt(t):t;return Mt(a)?n.filter(function(i){return Mt(i)&&ln(i,a)&&dt(i)!=="body"}):[]}function lr(t,n,e,a){var i=n==="clippingParents"?ir(t):[].concat(n),v=[].concat(i,[e]),x=v[0],M=v.reduce(function(b,T){var P=Ws(t,T,a);return b.top=At(P.top,b.top),b.right=os(P.right,b.right),b.bottom=os(P.bottom,b.bottom),b.left=At(P.left,b.left),b},Ws(t,x,a));return M.width=M.right-M.left,M.height=M.bottom-M.top,M.x=M.left,M.y=M.top,M}function fn(t){var n=t.reference,e=t.element,a=t.placement,i=a?pt(a):null,v=a?Wt(a):null,x=n.x+n.width/2-e.width/2,M=n.y+n.height/2-e.height/2,b;switch(i){case Ne:b={x,y:n.y-e.height};break;case Qe:b={x,y:n.y+n.height};break;case et:b={x:n.x+n.width,y:M};break;case Ve:b={x:n.x-e.width,y:M};break;default:b={x:n.x,y:n.y}}var T=i?$s(i):null;if(T!=null){var P=T==="y"?"height":"width";switch(v){case Lt:b[T]=b[T]-(n[P]/2-e[P]/2);break;case Xt:b[T]=b[T]+(n[P]/2-e[P]/2);break}}return b}function Kt(t,n){n===void 0&&(n={});var e=n,a=e.placement,i=a===void 0?t.placement:a,v=e.strategy,x=v===void 0?t.strategy:v,M=e.boundary,b=M===void 0?Aa:M,T=e.rootBoundary,P=T===void 0?nn:T,L=e.elementContext,q=L===void 0?Ht:L,R=e.altBoundary,V=R===void 0?!1:R,J=e.padding,H=J===void 0?0:J,oe=un(typeof H!="number"?H:pn(H,Gt)),le=q===Ht?Ma:Ht,de=t.rects.popper,d=t.elements[V?le:q],ne=lr(Mt(d)?d:d.contextElement||kt(t.elements.popper),b,P,x),I=jt(t.elements.reference),ce=fn({reference:I,element:de,strategy:"absolute",placement:i}),ge=ws(Object.assign({},de,ce)),f=q===Ht?ge:I,w={top:ne.top-f.top+oe.top,bottom:f.bottom-ne.bottom+oe.bottom,left:ne.left-f.left+oe.left,right:f.right-ne.right+oe.right},E=t.modifiersData.offset;if(q===Ht&&E){var C=E[i];Object.keys(w).forEach(function(S){var m=[et,Qe].indexOf(S)>=0?1:-1,U=[Ne,Qe].indexOf(S)>=0?"y":"x";w[S]+=C[U]*m})}return w}function cr(t,n){n===void 0&&(n={});var e=n,a=e.placement,i=e.boundary,v=e.rootBoundary,x=e.padding,M=e.flipVariations,b=e.allowedAutoPlacements,T=b===void 0?an:b,P=Wt(a),L=P?M?Us:Us.filter(function(V){return Wt(V)===P}):Gt,q=L.filter(function(V){return T.indexOf(V)>=0});q.length===0&&(q=L);var R=q.reduce(function(V,J){return V[J]=Kt(t,{placement:J,boundary:i,rootBoundary:v,padding:x})[pt(J)],V},{});return Object.keys(R).sort(function(V,J){return R[V]-R[J]})}function ur(t){if(pt(t)===Ts)return[];var n=as(t);return[js(t),n,js(n)]}function pr(t){var n=t.state,e=t.options,a=t.name;if(!n.modifiersData[a]._skip){for(var i=e.mainAxis,v=i===void 0?!0:i,x=e.altAxis,M=x===void 0?!0:x,b=e.fallbackPlacements,T=e.padding,P=e.boundary,L=e.rootBoundary,q=e.altBoundary,R=e.flipVariations,V=R===void 0?!0:R,J=e.allowedAutoPlacements,H=n.options.placement,oe=pt(H),le=oe===H,de=b||(le||!V?[as(H)]:ur(H)),d=[H].concat(de).reduce(function(Ie,Ae){return Ie.concat(pt(Ae)===Ts?cr(n,{placement:Ae,boundary:P,rootBoundary:L,padding:T,flipVariations:V,allowedAutoPlacements:J}):Ae)},[]),ne=n.rects.reference,I=n.rects.popper,ce=new Map,ge=!0,f=d[0],w=0;w<d.length;w++){var E=d[w],C=pt(E),S=Wt(E)===Lt,m=[Ne,Qe].indexOf(C)>=0,U=m?"width":"height",A=Kt(n,{placement:E,boundary:P,rootBoundary:L,altBoundary:q,padding:T}),K=m?S?et:Ve:S?Qe:Ne;ne[U]>I[U]&&(K=as(K));var me=as(K),ye=[];if(v&&ye.push(A[C]<=0),M&&ye.push(A[K]<=0,A[me]<=0),ye.every(function(Ie){return Ie})){f=E,ge=!1;break}ce.set(E,ye)}if(ge)for(var Oe=V?3:1,tt=function(Ae){var Se=d.find(function(Me){var Pe=ce.get(Me);if(Pe)return Pe.slice(0,Ae).every(function(Ye){return Ye})});if(Se)return f=Se,"break"},Re=Oe;Re>0;Re--){var st=tt(Re);if(st==="break")break}n.placement!==f&&(n.modifiersData[a]._skip=!0,n.placement=f,n.reset=!0)}}const dr={name:"flip",enabled:!0,phase:"main",fn:pr,requiresIfExists:["offset"],data:{_skip:!1}};function Bs(t,n,e){return e===void 0&&(e={x:0,y:0}),{top:t.top-n.height-e.y,right:t.right-n.width+e.x,bottom:t.bottom-n.height+e.y,left:t.left-n.width-e.x}}function Ns(t){return[Ne,et,Qe,Ve].some(function(n){return t[n]>=0})}function fr(t){var n=t.state,e=t.name,a=n.rects.reference,i=n.rects.popper,v=n.modifiersData.preventOverflow,x=Kt(n,{elementContext:"reference"}),M=Kt(n,{altBoundary:!0}),b=Bs(x,a),T=Bs(M,i,v),P=Ns(b),L=Ns(T);n.modifiersData[e]={referenceClippingOffsets:b,popperEscapeOffsets:T,isReferenceHidden:P,hasPopperEscaped:L},n.attributes.popper=Object.assign({},n.attributes.popper,{"data-popper-reference-hidden":P,"data-popper-escaped":L})}const hr={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:fr};function gr(t,n,e){var a=pt(t),i=[Ve,Ne].indexOf(a)>=0?-1:1,v=typeof e=="function"?e(Object.assign({},n,{placement:t})):e,x=v[0],M=v[1];return x=x||0,M=(M||0)*i,[Ve,et].indexOf(a)>=0?{x:M,y:x}:{x,y:M}}function vr(t){var n=t.state,e=t.options,a=t.name,i=e.offset,v=i===void 0?[0,0]:i,x=an.reduce(function(P,L){return P[L]=gr(L,n.rects,v),P},{}),M=x[n.placement],b=M.x,T=M.y;n.modifiersData.popperOffsets!=null&&(n.modifiersData.popperOffsets.x+=b,n.modifiersData.popperOffsets.y+=T),n.modifiersData[a]=x}const mr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:vr};function br(t){var n=t.state,e=t.name;n.modifiersData[e]=fn({reference:n.rects.reference,element:n.rects.popper,strategy:"absolute",placement:n.placement})}const wr={name:"popperOffsets",enabled:!0,phase:"read",fn:br,data:{}};function yr(t){return t==="x"?"y":"x"}function xr(t){var n=t.state,e=t.options,a=t.name,i=e.mainAxis,v=i===void 0?!0:i,x=e.altAxis,M=x===void 0?!1:x,b=e.boundary,T=e.rootBoundary,P=e.altBoundary,L=e.padding,q=e.tether,R=q===void 0?!0:q,V=e.tetherOffset,J=V===void 0?0:V,H=Kt(n,{boundary:b,rootBoundary:T,padding:L,altBoundary:P}),oe=pt(n.placement),le=Wt(n.placement),de=!le,d=$s(oe),ne=yr(d),I=n.modifiersData.popperOffsets,ce=n.rects.reference,ge=n.rects.popper,f=typeof J=="function"?J(Object.assign({},n.rects,{placement:n.placement})):J,w=typeof f=="number"?{mainAxis:f,altAxis:f}:Object.assign({mainAxis:0,altAxis:0},f),E=n.modifiersData.offset?n.modifiersData.offset[n.placement]:null,C={x:0,y:0};if(I){if(v){var S,m=d==="y"?Ne:Ve,U=d==="y"?Qe:et,A=d==="y"?"height":"width",K=I[d],me=K+H[m],ye=K-H[U],Oe=R?-ge[A]/2:0,tt=le===Lt?ce[A]:ge[A],Re=le===Lt?-ge[A]:-ce[A],st=n.elements.arrow,Ie=R&&st?ks(st):{width:0,height:0},Ae=n.modifiersData["arrow#persistent"]?n.modifiersData["arrow#persistent"].padding:cn(),Se=Ae[m],Me=Ae[U],Pe=qt(0,ce[A],Ie[A]),Ye=de?ce[A]/2-Oe-Pe-Se-w.mainAxis:tt-Pe-Se-w.mainAxis,De=de?-ce[A]/2+Oe+Pe+Me+w.mainAxis:Re+Pe+Me+w.mainAxis,Q=n.elements.arrow&&Zt(n.elements.arrow),nt=Q?d==="y"?Q.clientTop||0:Q.clientLeft||0:0,Ue=(S=E==null?void 0:E[d])!=null?S:0,at=K+Ye-Ue-nt,bt=K+De-Ue,Le=qt(R?os(me,at):me,K,R?At(ye,bt):ye);I[d]=Le,C[d]=Le-K}if(M){var ve,wt=d==="x"?Ne:Ve,ft=d==="x"?Qe:et,xe=I[ne],He=ne==="y"?"height":"width",Je=xe+H[wt],Fe=xe-H[ft],rt=[Ne,Ve].indexOf(oe)!==-1,je=(ve=E==null?void 0:E[ne])!=null?ve:0,ct=rt?Je:xe-ce[He]-ge[He]-je+w.altAxis,yt=rt?xe+ce[He]+ge[He]-je-w.altAxis:Fe,ht=R&&rt?za(ct,xe,yt):qt(R?ct:Je,xe,R?yt:Fe);I[ne]=ht,C[ne]=ht-xe}n.modifiersData[a]=C}}const Tr={name:"preventOverflow",enabled:!0,phase:"main",fn:xr,requiresIfExists:["offset"]};function Cr(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function kr(t){return t===qe(t)||!Ze(t)?Ss(t):Cr(t)}function $r(t){var n=t.getBoundingClientRect(),e=Ft(n.width)/t.offsetWidth||1,a=Ft(n.height)/t.offsetHeight||1;return e!==1||a!==1}function Sr(t,n,e){e===void 0&&(e=!1);var a=Ze(n),i=Ze(n)&&$r(n),v=kt(n),x=jt(t,i,e),M={scrollLeft:0,scrollTop:0},b={x:0,y:0};return(a||!a&&!e)&&((dt(n)!=="body"||_s(v))&&(M=kr(n)),Ze(n)?(b=jt(n,!0),b.x+=n.clientLeft,b.y+=n.clientTop):v&&(b.x=Es(v))),{x:x.left+M.scrollLeft-b.x,y:x.top+M.scrollTop-b.y,width:x.width,height:x.height}}function Er(t){var n=new Map,e=new Set,a=[];t.forEach(function(v){n.set(v.name,v)});function i(v){e.add(v.name);var x=[].concat(v.requires||[],v.requiresIfExists||[]);x.forEach(function(M){if(!e.has(M)){var b=n.get(M);b&&i(b)}}),a.push(v)}return t.forEach(function(v){e.has(v.name)||i(v)}),a}function _r(t){var n=Er(t);return Wa.reduce(function(e,a){return e.concat(n.filter(function(i){return i.phase===a}))},[])}function Ar(t){var n;return function(){return n||(n=new Promise(function(e){Promise.resolve().then(function(){n=void 0,e(t())})})),n}}function Mr(t){var n=t.reduce(function(e,a){var i=e[a.name];return e[a.name]=i?Object.assign({},i,a,{options:Object.assign({},i.options,a.options),data:Object.assign({},i.data,a.data)}):a,e},{});return Object.keys(n).map(function(e){return n[e]})}var Vs={placement:"bottom",modifiers:[],strategy:"absolute"};function Hs(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];return!n.some(function(a){return!(a&&typeof a.getBoundingClientRect=="function")})}function Pr(t){t===void 0&&(t={});var n=t,e=n.defaultModifiers,a=e===void 0?[]:e,i=n.defaultOptions,v=i===void 0?Vs:i;return function(M,b,T){T===void 0&&(T=v);var P={placement:"bottom",orderedModifiers:[],options:Object.assign({},Vs,v),modifiersData:{},elements:{reference:M,popper:b},attributes:{},styles:{}},L=[],q=!1,R={state:P,setOptions:function(oe){var le=typeof oe=="function"?oe(P.options):oe;J(),P.options=Object.assign({},v,P.options,le),P.scrollParents={reference:Mt(M)?Yt(M):M.contextElement?Yt(M.contextElement):[],popper:Yt(b)};var de=_r(Mr([].concat(a,P.options.modifiers)));return P.orderedModifiers=de.filter(function(d){return d.enabled}),V(),R.update()},forceUpdate:function(){if(!q){var oe=P.elements,le=oe.reference,de=oe.popper;if(Hs(le,de)){P.rects={reference:Sr(le,Zt(de),P.options.strategy==="fixed"),popper:ks(de)},P.reset=!1,P.placement=P.options.placement,P.orderedModifiers.forEach(function(w){return P.modifiersData[w.name]=Object.assign({},w.data)});for(var d=0;d<P.orderedModifiers.length;d++){if(P.reset===!0){P.reset=!1,d=-1;continue}var ne=P.orderedModifiers[d],I=ne.fn,ce=ne.options,ge=ce===void 0?{}:ce,f=ne.name;typeof I=="function"&&(P=I({state:P,options:ge,name:f,instance:R})||P)}}}},update:Ar(function(){return new Promise(function(H){R.forceUpdate(),H(P)})}),destroy:function(){J(),q=!0}};if(!Hs(M,b))return R;R.setOptions(T).then(function(H){!q&&T.onFirstUpdate&&T.onFirstUpdate(H)});function V(){P.orderedModifiers.forEach(function(H){var oe=H.name,le=H.options,de=le===void 0?{}:le,d=H.effect;if(typeof d=="function"){var ne=d({state:P,name:oe,instance:R,options:de}),I=function(){};L.push(ne||I)}})}function J(){L.forEach(function(H){return H()}),L=[]}return R}}var Dr=[tr,wr,Qa,rn,mr,dr,Tr,Xa,hr],Or=Pr({defaultModifiers:Dr}),Rr="tippy-box",hn="tippy-content",Ir="tippy-backdrop",gn="tippy-arrow",vn="tippy-svg-arrow",_t={passive:!0,capture:!0},mn=function(){return document.body};function hs(t,n,e){if(Array.isArray(t)){var a=t[n];return a??(Array.isArray(e)?e[n]:e)}return t}function As(t,n){var e={}.toString.call(t);return e.indexOf("[object")===0&&e.indexOf(n+"]")>-1}function bn(t,n){return typeof t=="function"?t.apply(void 0,n):t}function zs(t,n){if(n===0)return t;var e;return function(a){clearTimeout(e),e=setTimeout(function(){t(a)},n)}}function Ur(t){return t.split(/\s+/).filter(Boolean)}function Ut(t){return[].concat(t)}function qs(t,n){t.indexOf(n)===-1&&t.push(n)}function Lr(t){return t.filter(function(n,e){return t.indexOf(n)===e})}function Fr(t){return t.split("-")[0]}function is(t){return[].slice.call(t)}function Ys(t){return Object.keys(t).reduce(function(n,e){return t[e]!==void 0&&(n[e]=t[e]),n},{})}function Jt(){return document.createElement("div")}function cs(t){return["Element","Fragment"].some(function(n){return As(t,n)})}function jr(t){return As(t,"NodeList")}function Wr(t){return As(t,"MouseEvent")}function Br(t){return!!(t&&t._tippy&&t._tippy.reference===t)}function Nr(t){return cs(t)?[t]:jr(t)?is(t):Array.isArray(t)?t:is(document.querySelectorAll(t))}function gs(t,n){t.forEach(function(e){e&&(e.style.transitionDuration=n+"ms")})}function Js(t,n){t.forEach(function(e){e&&e.setAttribute("data-state",n)})}function Vr(t){var n,e=Ut(t),a=e[0];return a!=null&&(n=a.ownerDocument)!=null&&n.body?a.ownerDocument:document}function Hr(t,n){var e=n.clientX,a=n.clientY;return t.every(function(i){var v=i.popperRect,x=i.popperState,M=i.props,b=M.interactiveBorder,T=Fr(x.placement),P=x.modifiersData.offset;if(!P)return!0;var L=T==="bottom"?P.top.y:0,q=T==="top"?P.bottom.y:0,R=T==="right"?P.left.x:0,V=T==="left"?P.right.x:0,J=v.top-a+L>b,H=a-v.bottom-q>b,oe=v.left-e+R>b,le=e-v.right-V>b;return J||H||oe||le})}function vs(t,n,e){var a=n+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(i){t[a](i,e)})}function Xs(t,n){for(var e=n;e;){var a;if(t.contains(e))return!0;e=e.getRootNode==null||(a=e.getRootNode())==null?void 0:a.host}return!1}var ut={isTouch:!1},Ks=0;function zr(){ut.isTouch||(ut.isTouch=!0,window.performance&&document.addEventListener("mousemove",wn))}function wn(){var t=performance.now();t-Ks<20&&(ut.isTouch=!1,document.removeEventListener("mousemove",wn)),Ks=t}function qr(){var t=document.activeElement;if(Br(t)){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}function Yr(){document.addEventListener("touchstart",zr,_t),window.addEventListener("blur",qr)}var Jr=typeof window<"u"&&typeof document<"u",Xr=Jr?!!window.msCrypto:!1,Kr={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Gr={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},lt=Object.assign({appendTo:mn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Kr,Gr),Zr=Object.keys(lt),Qr=function(n){var e=Object.keys(n);e.forEach(function(a){lt[a]=n[a]})};function yn(t){var n=t.plugins||[],e=n.reduce(function(a,i){var v=i.name,x=i.defaultValue;if(v){var M;a[v]=t[v]!==void 0?t[v]:(M=lt[v])!=null?M:x}return a},{});return Object.assign({},t,e)}function eo(t,n){var e=n?Object.keys(yn(Object.assign({},lt,{plugins:n}))):Zr,a=e.reduce(function(i,v){var x=(t.getAttribute("data-tippy-"+v)||"").trim();if(!x)return i;if(v==="content")i[v]=x;else try{i[v]=JSON.parse(x)}catch{i[v]=x}return i},{});return a}function Gs(t,n){var e=Object.assign({},n,{content:bn(n.content,[t])},n.ignoreAttributes?{}:eo(t,n.plugins));return e.aria=Object.assign({},lt.aria,e.aria),e.aria={expanded:e.aria.expanded==="auto"?n.interactive:e.aria.expanded,content:e.aria.content==="auto"?n.interactive?null:"describedby":e.aria.content},e}var to=function(){return"innerHTML"};function ys(t,n){t[to()]=n}function Zs(t){var n=Jt();return t===!0?n.className=gn:(n.className=vn,cs(t)?n.appendChild(t):ys(n,t)),n}function Qs(t,n){cs(n.content)?(ys(t,""),t.appendChild(n.content)):typeof n.content!="function"&&(n.allowHTML?ys(t,n.content):t.textContent=n.content)}function xs(t){var n=t.firstElementChild,e=is(n.children);return{box:n,content:e.find(function(a){return a.classList.contains(hn)}),arrow:e.find(function(a){return a.classList.contains(gn)||a.classList.contains(vn)}),backdrop:e.find(function(a){return a.classList.contains(Ir)})}}function xn(t){var n=Jt(),e=Jt();e.className=Rr,e.setAttribute("data-state","hidden"),e.setAttribute("tabindex","-1");var a=Jt();a.className=hn,a.setAttribute("data-state","hidden"),Qs(a,t.props),n.appendChild(e),e.appendChild(a),i(t.props,t.props);function i(v,x){var M=xs(n),b=M.box,T=M.content,P=M.arrow;x.theme?b.setAttribute("data-theme",x.theme):b.removeAttribute("data-theme"),typeof x.animation=="string"?b.setAttribute("data-animation",x.animation):b.removeAttribute("data-animation"),x.inertia?b.setAttribute("data-inertia",""):b.removeAttribute("data-inertia"),b.style.maxWidth=typeof x.maxWidth=="number"?x.maxWidth+"px":x.maxWidth,x.role?b.setAttribute("role",x.role):b.removeAttribute("role"),(v.content!==x.content||v.allowHTML!==x.allowHTML)&&Qs(T,t.props),x.arrow?P?v.arrow!==x.arrow&&(b.removeChild(P),b.appendChild(Zs(x.arrow))):b.appendChild(Zs(x.arrow)):P&&b.removeChild(P)}return{popper:n,onUpdate:i}}xn.$$tippy=!0;var so=1,ns=[],ms=[];function no(t,n){var e=Gs(t,Object.assign({},lt,yn(Ys(n)))),a,i,v,x=!1,M=!1,b=!1,T=!1,P,L,q,R=[],V=zs(at,e.interactiveDebounce),J,H=so++,oe=null,le=Lr(e.plugins),de={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},d={id:H,reference:t,popper:Jt(),popperInstance:oe,props:e,state:de,plugins:le,clearDelayTimeouts:ct,setProps:yt,setContent:ht,show:Bt,hide:Nt,hideWithInteractivity:Vt,enable:rt,disable:je,unmount:Pt,destroy:$t};if(!e.render)return d;var ne=e.render(d),I=ne.popper,ce=ne.onUpdate;I.setAttribute("data-tippy-root",""),I.id="tippy-"+d.id,d.popper=I,t._tippy=d,I._tippy=d;var ge=le.map(function(y){return y.fn(d)}),f=t.hasAttribute("aria-expanded");return Q(),Oe(),K(),me("onCreate",[d]),e.showOnCreate&&Je(),I.addEventListener("mouseenter",function(){d.props.interactive&&d.state.isVisible&&d.clearDelayTimeouts()}),I.addEventListener("mouseleave",function(){d.props.interactive&&d.props.trigger.indexOf("mouseenter")>=0&&m().addEventListener("mousemove",V)}),d;function w(){var y=d.props.touch;return Array.isArray(y)?y:[y,0]}function E(){return w()[0]==="hold"}function C(){var y;return!!((y=d.props.render)!=null&&y.$$tippy)}function S(){return J||t}function m(){var y=S().parentNode;return y?Vr(y):document}function U(){return xs(I)}function A(y){return d.state.isMounted&&!d.state.isVisible||ut.isTouch||P&&P.type==="focus"?0:hs(d.props.delay,y?0:1,lt.delay)}function K(y){y===void 0&&(y=!1),I.style.pointerEvents=d.props.interactive&&!y?"":"none",I.style.zIndex=""+d.props.zIndex}function me(y,W,G){if(G===void 0&&(G=!0),ge.forEach(function(pe){pe[y]&&pe[y].apply(pe,W)}),G){var ue;(ue=d.props)[y].apply(ue,W)}}function ye(){var y=d.props.aria;if(y.content){var W="aria-"+y.content,G=I.id,ue=Ut(d.props.triggerTarget||t);ue.forEach(function(pe){var Te=pe.getAttribute(W);if(d.state.isVisible)pe.setAttribute(W,Te?Te+" "+G:G);else{var ke=Te&&Te.replace(G,"").trim();ke?pe.setAttribute(W,ke):pe.removeAttribute(W)}})}}function Oe(){if(!(f||!d.props.aria.expanded)){var y=Ut(d.props.triggerTarget||t);y.forEach(function(W){d.props.interactive?W.setAttribute("aria-expanded",d.state.isVisible&&W===S()?"true":"false"):W.removeAttribute("aria-expanded")})}}function tt(){m().removeEventListener("mousemove",V),ns=ns.filter(function(y){return y!==V})}function Re(y){if(!(ut.isTouch&&(b||y.type==="mousedown"))){var W=y.composedPath&&y.composedPath()[0]||y.target;if(!(d.props.interactive&&Xs(I,W))){if(Ut(d.props.triggerTarget||t).some(function(G){return Xs(G,W)})){if(ut.isTouch||d.state.isVisible&&d.props.trigger.indexOf("click")>=0)return}else me("onClickOutside",[d,y]);d.props.hideOnClick===!0&&(d.clearDelayTimeouts(),d.hide(),M=!0,setTimeout(function(){M=!1}),d.state.isMounted||Se())}}}function st(){b=!0}function Ie(){b=!1}function Ae(){var y=m();y.addEventListener("mousedown",Re,!0),y.addEventListener("touchend",Re,_t),y.addEventListener("touchstart",Ie,_t),y.addEventListener("touchmove",st,_t)}function Se(){var y=m();y.removeEventListener("mousedown",Re,!0),y.removeEventListener("touchend",Re,_t),y.removeEventListener("touchstart",Ie,_t),y.removeEventListener("touchmove",st,_t)}function Me(y,W){Ye(y,function(){!d.state.isVisible&&I.parentNode&&I.parentNode.contains(I)&&W()})}function Pe(y,W){Ye(y,W)}function Ye(y,W){var G=U().box;function ue(pe){pe.target===G&&(vs(G,"remove",ue),W())}if(y===0)return W();vs(G,"remove",L),vs(G,"add",ue),L=ue}function De(y,W,G){G===void 0&&(G=!1);var ue=Ut(d.props.triggerTarget||t);ue.forEach(function(pe){pe.addEventListener(y,W,G),R.push({node:pe,eventType:y,handler:W,options:G})})}function Q(){E()&&(De("touchstart",Ue,{passive:!0}),De("touchend",bt,{passive:!0})),Ur(d.props.trigger).forEach(function(y){if(y!=="manual")switch(De(y,Ue),y){case"mouseenter":De("mouseleave",bt);break;case"focus":De(Xr?"focusout":"blur",Le);break;case"focusin":De("focusout",Le);break}})}function nt(){R.forEach(function(y){var W=y.node,G=y.eventType,ue=y.handler,pe=y.options;W.removeEventListener(G,ue,pe)}),R=[]}function Ue(y){var W,G=!1;if(!(!d.state.isEnabled||ve(y)||M)){var ue=((W=P)==null?void 0:W.type)==="focus";P=y,J=y.currentTarget,Oe(),!d.state.isVisible&&Wr(y)&&ns.forEach(function(pe){return pe(y)}),y.type==="click"&&(d.props.trigger.indexOf("mouseenter")<0||x)&&d.props.hideOnClick!==!1&&d.state.isVisible?G=!0:Je(y),y.type==="click"&&(x=!G),G&&!ue&&Fe(y)}}function at(y){var W=y.target,G=S().contains(W)||I.contains(W);if(!(y.type==="mousemove"&&G)){var ue=He().concat(I).map(function(pe){var Te,ke=pe._tippy,ze=(Te=ke.popperInstance)==null?void 0:Te.state;return ze?{popperRect:pe.getBoundingClientRect(),popperState:ze,props:e}:null}).filter(Boolean);Hr(ue,y)&&(tt(),Fe(y))}}function bt(y){var W=ve(y)||d.props.trigger.indexOf("click")>=0&&x;if(!W){if(d.props.interactive){d.hideWithInteractivity(y);return}Fe(y)}}function Le(y){d.props.trigger.indexOf("focusin")<0&&y.target!==S()||d.props.interactive&&y.relatedTarget&&I.contains(y.relatedTarget)||Fe(y)}function ve(y){return ut.isTouch?E()!==y.type.indexOf("touch")>=0:!1}function wt(){ft();var y=d.props,W=y.popperOptions,G=y.placement,ue=y.offset,pe=y.getReferenceClientRect,Te=y.moveTransition,ke=C()?xs(I).arrow:null,ze=pe?{getBoundingClientRect:pe,contextElement:pe.contextElement||S()}:t,xt={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(_){var g=_.state;if(C()){var N=U(),ee=N.box;["placement","reference-hidden","escaped"].forEach(function(fe){fe==="placement"?ee.setAttribute("data-placement",g.placement):g.attributes.popper["data-popper-"+fe]?ee.setAttribute("data-"+fe,""):ee.removeAttribute("data-"+fe)}),g.attributes.popper={}}}},Ee=[{name:"offset",options:{offset:ue}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!Te}},xt];C()&&ke&&Ee.push({name:"arrow",options:{element:ke,padding:3}}),Ee.push.apply(Ee,(W==null?void 0:W.modifiers)||[]),d.popperInstance=Or(ze,I,Object.assign({},W,{placement:G,onFirstUpdate:q,modifiers:Ee}))}function ft(){d.popperInstance&&(d.popperInstance.destroy(),d.popperInstance=null)}function xe(){var y=d.props.appendTo,W,G=S();d.props.interactive&&y===mn||y==="parent"?W=G.parentNode:W=bn(y,[G]),W.contains(I)||W.appendChild(I),d.state.isMounted=!0,wt()}function He(){return is(I.querySelectorAll("[data-tippy-root]"))}function Je(y){d.clearDelayTimeouts(),y&&me("onTrigger",[d,y]),Ae();var W=A(!0),G=w(),ue=G[0],pe=G[1];ut.isTouch&&ue==="hold"&&pe&&(W=pe),W?a=setTimeout(function(){d.show()},W):d.show()}function Fe(y){if(d.clearDelayTimeouts(),me("onUntrigger",[d,y]),!d.state.isVisible){Se();return}if(!(d.props.trigger.indexOf("mouseenter")>=0&&d.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(y.type)>=0&&x)){var W=A(!1);W?i=setTimeout(function(){d.state.isVisible&&d.hide()},W):v=requestAnimationFrame(function(){d.hide()})}}function rt(){d.state.isEnabled=!0}function je(){d.hide(),d.state.isEnabled=!1}function ct(){clearTimeout(a),clearTimeout(i),cancelAnimationFrame(v)}function yt(y){if(!d.state.isDestroyed){me("onBeforeUpdate",[d,y]),nt();var W=d.props,G=Gs(t,Object.assign({},W,Ys(y),{ignoreAttributes:!0}));d.props=G,Q(),W.interactiveDebounce!==G.interactiveDebounce&&(tt(),V=zs(at,G.interactiveDebounce)),W.triggerTarget&&!G.triggerTarget?Ut(W.triggerTarget).forEach(function(ue){ue.removeAttribute("aria-expanded")}):G.triggerTarget&&t.removeAttribute("aria-expanded"),Oe(),K(),ce&&ce(W,G),d.popperInstance&&(wt(),He().forEach(function(ue){requestAnimationFrame(ue._tippy.popperInstance.forceUpdate)})),me("onAfterUpdate",[d,y])}}function ht(y){d.setProps({content:y})}function Bt(){var y=d.state.isVisible,W=d.state.isDestroyed,G=!d.state.isEnabled,ue=ut.isTouch&&!d.props.touch,pe=hs(d.props.duration,0,lt.duration);if(!(y||W||G||ue)&&!S().hasAttribute("disabled")&&(me("onShow",[d],!1),d.props.onShow(d)!==!1)){if(d.state.isVisible=!0,C()&&(I.style.visibility="visible"),K(),Ae(),d.state.isMounted||(I.style.transition="none"),C()){var Te=U(),ke=Te.box,ze=Te.content;gs([ke,ze],0)}q=function(){var Ee;if(!(!d.state.isVisible||T)){if(T=!0,I.offsetHeight,I.style.transition=d.props.moveTransition,C()&&d.props.animation){var Xe=U(),_=Xe.box,g=Xe.content;gs([_,g],pe),Js([_,g],"visible")}ye(),Oe(),qs(ms,d),(Ee=d.popperInstance)==null||Ee.forceUpdate(),me("onMount",[d]),d.props.animation&&C()&&Pe(pe,function(){d.state.isShown=!0,me("onShown",[d])})}},xe()}}function Nt(){var y=!d.state.isVisible,W=d.state.isDestroyed,G=!d.state.isEnabled,ue=hs(d.props.duration,1,lt.duration);if(!(y||W||G)&&(me("onHide",[d],!1),d.props.onHide(d)!==!1)){if(d.state.isVisible=!1,d.state.isShown=!1,T=!1,x=!1,C()&&(I.style.visibility="hidden"),tt(),Se(),K(!0),C()){var pe=U(),Te=pe.box,ke=pe.content;d.props.animation&&(gs([Te,ke],ue),Js([Te,ke],"hidden"))}ye(),Oe(),d.props.animation?C()&&Me(ue,d.unmount):d.unmount()}}function Vt(y){m().addEventListener("mousemove",V),qs(ns,V),V(y)}function Pt(){d.state.isVisible&&d.hide(),d.state.isMounted&&(ft(),He().forEach(function(y){y._tippy.unmount()}),I.parentNode&&I.parentNode.removeChild(I),ms=ms.filter(function(y){return y!==d}),d.state.isMounted=!1,me("onHidden",[d]))}function $t(){d.state.isDestroyed||(d.clearDelayTimeouts(),d.unmount(),nt(),delete t._tippy,d.state.isDestroyed=!0,me("onDestroy",[d]))}}function Ct(t,n){n===void 0&&(n={});var e=lt.plugins.concat(n.plugins||[]);Yr();var a=Object.assign({},n,{plugins:e}),i=Nr(t),v=i.reduce(function(x,M){var b=M&&no(M,a);return b&&x.push(b),x},[]);return cs(t)?v[0]:v}Ct.defaultProps=lt;Ct.setDefaultProps=Qr;Ct.currentInput=ut;Object.assign({},rn,{effect:function(n){var e=n.state,a={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,a.popper),e.styles=a,e.elements.arrow&&Object.assign(e.elements.arrow.style,a.arrow)}});Ct.setDefaultProps({render:xn});const ao={class:"task-pane"},ro={key:0,class:"loading-overlay"},oo={key:1,class:"format-error-overlay"},io={class:"format-error-content"},lo={class:"format-error-message"},co={class:"format-error-actions"},uo={class:"doc-header"},po={class:"doc-title"},fo={class:"header-controls"},ho={key:0,class:"batch-test-header-control"},go={class:"batch-toggle-label-compact"},vo=["disabled"],mo=["disabled","title"],bo={class:"action-area"},wo={class:"select-container"},yo={class:"select-group"},xo=["disabled"],To=["value"],Co={class:"select-group"},ko=["disabled"],$o=["value"],So=["title"],Eo={class:"action-buttons"},_o=["disabled"],Ao={class:"btn-content"},Mo={key:0,class:"button-loader"},Po=["disabled"],Do={class:"btn-content"},Oo={key:0,class:"button-loader"},Ro={class:"content-area"},Io={class:"modal-header"},Uo={class:"modal-body"},Lo={class:"selection-content"},Fo={class:"modal-header"},jo={class:"modal-body"},Wo={class:"alert-message"},Bo={class:"alert-actions"},No={key:2,class:"modal-overlay"},Vo={class:"modal-header"},Ho={class:"modal-body"},zo={class:"confirm-message"},qo={class:"confirm-actions"},Yo={class:"modal-header"},Jo={class:"modal-title"},Xo={class:"modal-body"},Ko={class:"alert-message"},Go={class:"alert-actions"},Zo={class:"task-queue"},Qo={class:"queue-header"},ei={class:"queue-status-filter"},ti=["value"],si={class:"queue-actions"},ni=["disabled","title"],ai={class:"task-count"},ri={key:0,class:"queue-table-container"},oi={class:"col-id"},ii={class:"id-header"},li={key:0,class:"col-subject"},ci={class:"subject-header"},ui={class:"switch"},pi=["title"],di={key:1,class:"col-status"},fi=["onClick"],hi={class:"col-id"},gi={class:"id-cell group-cell"},vi={class:"group-toggle-icon"},mi={class:"group-label"},bi={key:0,class:"col-subject"},wi={class:"subject-cell group-subject"},yi={key:1,class:"col-status"},xi={class:"col-actions"},Ti={class:"task-actions"},Ci={class:"group-action-text"},ki=["onClick"],$i={class:"col-id"},Si={class:"id-content"},Ei={class:"task-id"},_i={key:0,class:"analysis-task-icon",title:"解析任务"},Ai={key:1,class:"enhance-task-icon",title:"增强解析任务"},Mi={key:2,class:"check-task-icon",title:"校对任务"},Pi={key:0,class:"status-in-id"},Di=["onMouseenter"],Oi={key:0,class:"col-subject"},Ri=["onMouseenter"],Ii={key:1,class:"col-status"},Ui={class:"status-cell"},Li=["onMouseenter"],Fi=["onClick"],ji={class:"col-id"},Wi={class:"id-content"},Bi={class:"task-id"},Ni={key:0,class:"analysis-task-icon",title:"解析任务"},Vi={key:1,class:"enhance-task-icon",title:"增强解析任务"},Hi={key:2,class:"check-task-icon",title:"校对任务"},zi={key:0,class:"status-in-id"},qi=["onMouseenter"],Yi={key:0,class:"col-subject"},Ji=["onMouseenter"],Xi={key:1,class:"col-status"},Ki={class:"status-cell"},Gi=["onMouseenter"],Zi={class:"col-actions"},Qi={class:"task-actions"},el=["onClick"],tl=["onClick"],sl={key:2,class:"no-action-icon",title:"无可用操作"},nl={key:1,class:"empty-queue"},al={key:4,class:"log-container"},rl={class:"log-actions"},ol={class:"toggle-icon"},il=["innerHTML"],ll={__name:"TaskPane",setup(t){const n=he(!1),e=he(!1),a=he(!1),i=he(""),v=he(!1),x=he(!1),M=he(!1),b=he(!1),T=he(!0),P=he(""),L=he(!1),q=he(window.innerWidth),R=vt(()=>q.value<750),V=vt(()=>q.value<380),J=()=>{q.value=window.innerWidth},H=he(null),oe=he(!1),le=he(""),de=he(new Set);he(!1);const d={subjects:new Map,enhance:new Map,switch:null,softBreak:new Map,error:new Map},ne=he(!1),I=he([{value:"",label:"所有状态"},{value:1,label:"进行中"},{value:2,label:"完成"},{value:4,label:"已停止"},{value:5,label:"等待批量插入"}]),{docName:ce,selected:ge,logger:f,map:w,subject:E,stage:C,subjectOptions:S,stageOptions:m,appConfig:U,clearLog:A,checkDocumentFormat:K,getTaskStatusClass:me,getTaskStatusText:ye,terminateTask:Oe,run1:tt,runCheck:Re,setupLifecycle:st,navigateToTaskControl:Ie,isLoading:Ae,tryRemoveTaskPlaceholderWithLoading:Se,confirmDialog:Me,handleConfirm:Pe,errorDialog:Ye,hideErrorDialog:De,getCompletedTasksCount:Q,getReleasableTasksCount:nt,showConfirm:Ue,isBatchTestMode:at,batchInsertAllTasks:bt,getBatchWaitingTasksCount:Le}=Wn(),ve=he(null);(()=>{var _;try{if((_=window.Application)!=null&&_.PluginStorage){const g=window.Application.PluginStorage.getItem("user_info");g?(ve.value=JSON.parse(g),console.log("用户信息已加载:",ve.value)):console.log("未找到用户信息")}}catch(g){console.error("解析用户信息时出错:",g)}})(),rs(ve,_=>{_&&_.orgs&&_.orgs[0]&&console.log(`用户企业ID: ${_.orgs[0].orgId}, 校对功能${_.orgs[0].orgId===2?"可用":"不可用"}`)},{immediate:!0});const ft=vt(()=>!ve.value||ve.value.isAdmin||ve.value.isOwner?S:ve.value.subject?S.filter(_=>_.value===ve.value.subject):S),xe=()=>{ve.value&&!ve.value.isAdmin&&!ve.value.isOwner&&ve.value.subject&&(E.value=ve.value.subject)};vt(()=>["physics","chemistry","biology","math"].includes(E.value)),rs(U,_=>{_&&(console.log("TaskPane组件收到应用配置更新:",_),console.log("当前版本类型:",_.EDITION),console.log("当前年级选项:",m.value))},{deep:!0,immediate:!0});const He=()=>{try{const _=K();T.value=_.isValid,P.value=_.message,L.value=!_.isValid,_.isValid||console.warn("文档格式检查失败:",_.message)}catch(_){console.error("执行文档格式检查时出错:",_),T.value=!1,P.value="检查文档格式时出错，请确保当前文档已保存为 .docx 格式。",L.value=!0}},Je=vt(()=>{let _={};const g=w;if(le.value==="")_={...g};else for(const N in g)if(Object.prototype.hasOwnProperty.call(g,N)){const ee=g[N];ee.status===le.value&&(_[N]=ee)}return oe.value&&H.value?_[H.value]?{[H.value]:_[H.value]}:{}:_}),Fe=vt(()=>{const _=Je.value;return Object.entries(_).map(([N,ee])=>({tid:N,...ee})).sort((N,ee)=>{const fe=N.startTime||0;return(ee.startTime||0)-fe}).reduce((N,ee)=>{const{tid:fe,...te}=ee;return N[fe]=te,N},{})}),rt=vt(()=>{const _=Fe.value,g=Object.entries(_).map(([te,be])=>({tid:te,...be})),N=g.filter(te=>te.status===3),ee=g.filter(te=>te.status!==3),fe=[];if(ee.forEach(te=>{fe.push({type:"task",...te})}),N.length>=2){const te="released-group-all",be=de.value.has(te);fe.push({type:"group",groupId:te,isCollapsed:be,tasks:N,count:N.length})}else N.forEach(te=>{fe.push({type:"task",...te})});return fe}),je=_=>{de.value.has(_)?de.value.delete(_):de.value.add(_)},ct=(_="wps-analysis")=>{if(!E.value)i.value="请选择学科",e.value=!0;else if(!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim())i.value="未选中内容",e.value=!0;else{_==="wps-analysis"?x.value=!0:_==="wps-enhance_analysis"&&(M.value=!0);const g=()=>{_==="wps-analysis"?x.value=!1:_==="wps-enhance_analysis"&&(M.value=!1)};tt(_,g).catch(N=>{console.log(N),N.message.includes("重叠")?(i.value=`当前选中内容已有正在处理中的任务，
            请等待任务完成或终止当前任务后再试`,e.value=!0):(console.error("操作失败:",N),i.value=N.message,e.value=!0),g()})}},yt=()=>{if(!E.value)i.value="请选择学科",e.value=!0;else if(!window.Application||!window.Application.Selection||!window.Application.Selection.Text.trim())i.value="未选中内容",e.value=!0;else{b.value=!0;const _=()=>{b.value=!1};Re(_).catch(g=>{console.log(g),g.message.includes("重叠")?(i.value=`当前选中内容已有正在处理中的校对任务，
            请等待任务完成或终止当前任务后再试`,e.value=!0):(console.error("校对操作失败:",g),i.value=g.message,e.value=!0),_()})}},ht=(_,g)=>{H.value=_,Ie(_)},Bt=_=>{w[_]&&(w[_].status=3),H.value===_&&(H.value=null),Se(_,!0)},Nt=async()=>{const _=Object.entries(w).filter(([g,N])=>N.status===2||N.status===4);if(_.length===0){i.value="没有可释放的任务",e.value=!0;return}try{if(await Ue(`确定要释放所有 ${_.length} 个可释放的任务吗？
此操作不可撤销。`)){let N=0;_.forEach(([ee,fe])=>{w[ee]&&(w[ee].status=3,H.value===ee&&(H.value=null),Se(ee,!0),N++)}),i.value=`已成功释放 ${N} 个任务`,e.value=!0}}catch(g){console.error("释放任务时出错:",g),i.value="释放任务时出现错误",e.value=!0}},Vt=()=>{v.value=!v.value},Pt=_=>_?_.toString().replace(/[\r\n\t\f\v]/g," ").replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\s+/g," ").trim():"",$t=_=>{const g=y(_);return g?Pt(g):"无题目内容"},y=_=>{if(!_.selectedText)return"";const g=_.selectedText.split("\r").filter(ee=>ee.trim());if(g.length===1){const ee=_.selectedText.split(`
`).filter(fe=>fe.trim());ee.length>1&&g.splice(0,1,...ee)}const N=g.map((ee,fe)=>{const te=ee.trim();return te.length>200,te});return N.length===1?g[0].trim():N.join(`
`)},W=(_,g)=>{if(!ne.value)return;const N=_.target,ee=y(g).toString();if(!ee||ee.trim()===""){console.log("题目内容为空，不显示tooltip");return}const fe=`
    <div class="subject-tooltip">
      <div class="subject-tooltip-title">题目内容</div>
      <div class="subject-tooltip-content">${ee.replace(/(\S{40})(?=\S)/g,"$1<wbr>")}</div>
    </div>
  `,te=_.clientX,be=_.clientY;if(d.subjects.has(N)){const We=d.subjects.get(N);We.setContent(fe),We.setProps({getReferenceClientRect:()=>({width:0,height:0,top:be,bottom:be,left:te,right:te})}),We.show();return}const Ke=Ct(N,{content:fe,allowHTML:!0,placement:"right",theme:"light",interactive:!0,appendTo:document.body,maxWidth:280,animation:"scale",duration:[200,0],trigger:"manual",hideOnClick:!1,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:be,bottom:be,left:te,right:te}),onHidden:()=>{Ke.setProps({getReferenceClientRect:null})}});d.subjects.set(N,Ke),Ke.show()},G=_=>{const g=_.currentTarget,N=`
    <div class="enhance-tooltip">
      <div class="enhance-tooltip-title">增强模式</div>
      <div class="enhance-tooltip-content">使用更精确的AI模型进行解析，适用于理科学科题目</div>
    </div>
  `,ee=_.clientX,fe=_.clientY;if(d.enhance.has(g)){const be=d.enhance.get(g);be.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:ee,right:ee})}),be.show();return}const te=Ct(g,{content:N,allowHTML:!0,placement:"bottom",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!1,hideOnClick:!0,maxWidth:200});d.enhance.set(g,te),te.show()},ue=(_,g)=>{const N=_.currentTarget,fe=`
    <div class="error-tooltip">
      <div class="error-tooltip-title">错误详情</div>
      <div class="error-tooltip-content">${g.errorMessage||"未知错误"}</div>
    </div>
  `,te=_.clientX,be=_.clientY;if(d.error.has(N)){const We=d.error.get(N);We.setContent(fe),We.setProps({getReferenceClientRect:()=>({width:0,height:0,top:be,bottom:be,left:te,right:te})}),We.show();return}const Ke=Ct(N,{content:fe,allowHTML:!0,placement:"top",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:300,appendTo:document.body,interactiveBorder:30,popperOptions:{modifiers:[{name:"preventOverflow",options:{boundary:document.body,padding:10}}]},getReferenceClientRect:()=>({width:0,height:0,top:be,bottom:be,left:te,right:te}),onHidden:()=>{Ke.setProps({getReferenceClientRect:null})}});d.error.set(N,Ke),Ke.show()},pe=()=>{d.subjects.forEach(_=>{_.destroy()}),d.subjects.clear(),d.enhance.forEach(_=>{_.destroy()}),d.enhance.clear(),d.softBreak.forEach(_=>{_.destroy()}),d.softBreak.clear(),document.removeEventListener("click",Te),document.removeEventListener("mousemove",ze)},Te=_=>{const g=document.querySelector(".tippy-box");g&&!g.contains(_.target)&&(d.subjects.forEach(N=>N.hide()),d.enhance.forEach(N=>N.hide()),d.softBreak.forEach(N=>N.hide()))};let ke=0;const ze=_=>{const g=Date.now();if(g-ke<100)return;ke=g;const N=document.querySelector(".tippy-box");if(!N)return;const ee=N.getBoundingClientRect();!(_.clientX>=ee.left-20&&_.clientX<=ee.right+20&&_.clientY>=ee.top-20&&_.clientY<=ee.bottom+20)&&!N.matches(":hover")&&(d.subjects.forEach(te=>te.hide()),d.enhance.forEach(te=>te.hide()),d.softBreak.forEach(te=>te.hide()))},xt=()=>{document.addEventListener("click",Te),document.addEventListener("mousemove",ze)};en(()=>{window.addEventListener("resize",J),xt(),xe(),setTimeout(()=>{He()},500);const _=document.createElement("style");_.id="tippy-custom-styles",_.textContent=`
    /* 题目内容提示样式 */
    .subject-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .subject-tooltip-title {
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      font-size: 14px;
      border-bottom: 1px solid #e8eaed;
      padding-bottom: 8px;
      text-align: center;
    }

    .subject-tooltip-content {
      color: #555;
      white-space: pre-wrap;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px 6px;
      font-size: 13px;
      line-height: 1.6;
      background-color: #fafafa;
      border-radius: 4px;
      width: 280px;
      max-width: 280px;
      box-sizing: border-box;
    }

    /* 任务ID说明提示样式 */
    .enhance-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .enhance-tooltip-title {
      font-weight: 600;
      color: #4285f4;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
    }

    .enhance-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 200px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 错误信息提示样式 */
    .error-tooltip {
      padding: 12px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 300px;
      box-sizing: border-box;
    }

    .error-tooltip-title {
      font-weight: 600;
      color: #d32f2f;
      margin-bottom: 8px;
      font-size: 14px;
      display: flex;
      align-items: center;
    }

    .error-tooltip-title::before {
      content: '⚠';
      margin-right: 6px;
      font-size: 16px;
      color: #ff9800;
    }

    .error-tooltip-content {
      color: #555;
      font-size: 13px;
      line-height: 1.5;
      word-wrap: break-word;
      word-break: break-all;
      text-align: left;
      background-color: #fafafa;
      padding: 8px;
      border-radius: 4px;
      border-left: 3px solid #d32f2f;
      max-height: 200px;
      overflow-y: auto;
      overflow-x: hidden;
    }

    /* 软换行警告提示样式 */
    .soft-break-tooltip {
      padding: 10px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      max-width: 100%;
      box-sizing: border-box;
    }

    .soft-break-tooltip-title {
      font-weight: 600;
      color: #ff9800;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .soft-break-tooltip-title::before {
      content: "⚠";
      font-size: 16px;
    }

    .soft-break-tooltip-content {
      color: #333;
      font-size: 13px;
      line-height: 1.6;
      word-wrap: break-word;
      word-break: break-word;
      max-height: 250px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 260px;
      max-width: 260px;
      box-sizing: border-box;
      background-color: #fff8f0;
      padding: 8px 10px;
      border-radius: 4px;
      border-left: 3px solid #ff9800;
    }

    /* 滚动条样式 */
    .subject-tooltip-content::-webkit-scrollbar {
      width: 4px;
    }

    .subject-tooltip-content::-webkit-scrollbar-track {
      background: #f1f3f4;
      border-radius: 2px;
    }

    .subject-tooltip-content::-webkit-scrollbar-thumb {
      background: #c1c8d1;
      border-radius: 2px;
    }

    /* Tippy主题覆盖 */
    .tippy-box[data-theme~='light'] {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    /* 确保tippy内容不超过屏幕边界 */
    .tippy-box {
      max-width: 300px !important;
      width: 300px !important;
      overflow: hidden;
    }

    .tippy-content {
      max-width: 100%;
      width: 100%;
      box-sizing: border-box;
      padding: 0 !important;
      overflow: hidden;
    }

    .switch-tooltip {
      padding: 6px 8px;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .switch-tooltip-content {
      color: #333;
      font-size: 11px;
      line-height: 1.3;
      white-space: nowrap;
    }

    .slider.round:before {
      border-radius: 50%;
    }
  `,document.head.appendChild(_)}),_n(()=>{window.removeEventListener("resize",J),pe();const _=document.getElementById("tippy-custom-styles");_&&_.remove()}),st();const Ee=_=>_.selectedText?_.selectedText.includes("\v")||_.selectedText.includes("\v"):!1,Xe=_=>{const g=_.currentTarget,N=`
    <div class="soft-break-tooltip">
      <div class="soft-break-tooltip-content">
        检测到题目内容包含软换行符（Shift + Enter）。<br>
        建议重新整理题目格式，使用正常换行替代软换行，以确保最佳的显示效果。
      </div>
    </div>
  `,ee=_.clientX,fe=_.clientY;if(d.softBreak.has(g)){const be=d.softBreak.get(g);be.setProps({getReferenceClientRect:()=>({width:0,height:0,top:fe,bottom:fe,left:ee,right:ee})}),be.show();return}const te=Ct(g,{content:N,allowHTML:!0,placement:"bottom-start",theme:"light",animation:"scale",duration:[200,0],trigger:"manual",interactive:!0,hideOnClick:!0,maxWidth:280});d.softBreak.set(g,te),te.show()};return vt(()=>rt.value.some(_=>_.type==="group")),vt(()=>de.value.size>0),(_,g)=>{var N,ee,fe,te,be,Ke,We;return F(),j("div",ao,[ie(Ae)?(F(),j("div",ro,g[33]||(g[33]=[u("div",{class:"loading-spinner"},null,-1),u("div",{class:"loading-text"},"处理中...",-1)]))):z("",!0),L.value?(F(),j("div",oo,[u("div",io,[g[34]||(g[34]=u("div",{class:"format-error-icon"},"⚠️",-1)),g[35]||(g[35]=u("div",{class:"format-error-title"},"文档格式不支持",-1)),u("div",lo,ae(P.value),1),u("div",co,[u("button",{class:"retry-check-btn",onClick:g[0]||(g[0]=O=>He())},"重新检查")])])])):z("",!0),u("div",uo,[u("div",po,ae(ie(ce)||"未选择文档"),1),u("div",fo,[((ee=(N=ve.value)==null?void 0:N.orgs[0])==null?void 0:ee.orgId)===2e7?(F(),j("div",ho,[u("label",go,[Ge(u("input",{type:"checkbox","onUpdate:modelValue":g[1]||(g[1]=O=>ds(at)?at.value=O:null),class:"batch-toggle-input",disabled:L.value},null,8,vo),[[Ds,ie(at)]]),g[36]||(g[36]=u("span",{class:"batch-toggle-slider-compact"},null,-1)),g[37]||(g[37]=u("span",{class:"batch-toggle-text-compact"},"批量测试",-1))]),ie(at)&&ie(Le)()>0?(F(),j("button",{key:0,class:"batch-insert-btn-compact",onClick:g[2]||(g[2]=O=>ie(bt)()),disabled:L.value,title:`批量插入 ${ie(Le)()} 个任务`}," 插入("+ae(ie(Le)())+") ",9,mo)):z("",!0)])):z("",!0),u("button",{class:"settings-btn",onClick:g[3]||(g[3]=O=>a.value=!0)},g[38]||(g[38]=[u("i",{class:"icon-settings"},null,-1)]))])]),u("div",bo,[u("div",wo,[u("div",yo,[g[39]||(g[39]=u("label",{for:"stage-select"},"年级:",-1)),Ge(u("select",{id:"stage-select","onUpdate:modelValue":g[4]||(g[4]=O=>ds(C)?C.value=O:null),class:"select-input",disabled:L.value},[(F(!0),j(Et,null,It(ie(m),O=>(F(),j("option",{key:O.value,value:O.value},ae(O.label),9,To))),128))],8,xo),[[fs,ie(C)]])]),u("div",Co,[g[40]||(g[40]=u("label",{for:"subject-select"},"学科:",-1)),Ge(u("select",{id:"subject-select","onUpdate:modelValue":g[5]||(g[5]=O=>ds(E)?E.value=O:null),class:"select-input",disabled:L.value},[(F(!0),j(Et,null,It(ft.value,O=>(F(),j("option",{key:O.value,value:O.value},ae(O.label),9,$o))),128))],8,ko),[[fs,ie(E)]]),ve.value&&!ve.value.isAdmin&&!ve.value.isOwner&&ve.value.subject?(F(),j("span",{key:0,class:"subject-hint",title:`当前用户只能使用 ${((fe=ft.value.find(O=>O.value===ve.value.subject))==null?void 0:fe.label)||ve.value.subject} 学科`}," 🔒 ",8,So)):z("",!0)])]),z("",!0),u("div",Eo,[u("button",{class:"action-btn primary",onClick:g[6]||(g[6]=O=>ct("wps-analysis")),disabled:x.value||L.value},[u("div",Ao,[x.value?(F(),j("span",Mo)):z("",!0),g[41]||(g[41]=u("span",{class:"btn-text"},"解析",-1))])],8,_o),z("",!0),((be=(te=ve.value)==null?void 0:te.orgs[0])==null?void 0:be.orgId)===2?(F(),j("button",{key:1,class:"action-btn secondary",onClick:g[8]||(g[8]=O=>yt()),disabled:b.value||L.value},[u("div",Do,[b.value?(F(),j("span",Oo)):z("",!0),g[43]||(g[43]=u("span",{class:"btn-text"},"校对",-1))])],8,Po)):z("",!0)])]),u("div",Ro,[n.value?(F(),j("div",{key:0,class:"modal-overlay",onClick:g[11]||(g[11]=O=>n.value=!1)},[u("div",{class:"modal-content",onClick:g[10]||(g[10]=Tt(()=>{},["stop"]))},[u("div",Io,[g[44]||(g[44]=u("div",{class:"modal-title"},"选中内容",-1)),u("button",{class:"modal-close",onClick:g[9]||(g[9]=O=>n.value=!1)},"×")]),u("div",Uo,[u("pre",Lo,ae(ie(ge)||"未选中内容"),1)])])])):z("",!0),e.value?(F(),j("div",{key:1,class:"modal-overlay",onClick:g[15]||(g[15]=O=>e.value=!1)},[u("div",{class:"modal-content alert-modal",onClick:g[14]||(g[14]=Tt(()=>{},["stop"]))},[u("div",Fo,[g[45]||(g[45]=u("div",{class:"modal-title"},"提示",-1)),u("button",{class:"modal-close",onClick:g[12]||(g[12]=O=>e.value=!1)},"×")]),u("div",jo,[u("div",Wo,ae(i.value),1),u("div",Bo,[u("button",{class:"action-btn primary",onClick:g[13]||(g[13]=O=>e.value=!1)},"确定")])])])])):z("",!0),ie(Me).show?(F(),j("div",No,[u("div",{class:"modal-content confirm-modal",onClick:g[19]||(g[19]=Tt(()=>{},["stop"]))},[u("div",Vo,[g[46]||(g[46]=u("div",{class:"modal-title"},"确认",-1)),u("button",{class:"modal-close",onClick:g[16]||(g[16]=O=>ie(Pe)(!1))},"×")]),u("div",Ho,[u("div",zo,ae(ie(Me).message),1),u("div",qo,[u("button",{class:"action-btn secondary",onClick:g[17]||(g[17]=O=>ie(Pe)(!1))},"取消"),u("button",{class:"action-btn primary",onClick:g[18]||(g[18]=O=>ie(Pe)(!0))},"确定")])])])])):z("",!0),ie(Ye).show?(F(),j("div",{key:3,class:"modal-overlay",onClick:g[23]||(g[23]=O=>ie(De)())},[u("div",{class:"modal-content alert-modal",onClick:g[22]||(g[22]=Tt(()=>{},["stop"]))},[u("div",Yo,[u("div",Jo,ae(ie(Ye).title),1),u("button",{class:"modal-close",onClick:g[20]||(g[20]=O=>ie(De)())},"×")]),u("div",Xo,[u("div",Ko,ae(ie(Ye).message),1),u("div",Go,[u("button",{class:"action-btn primary",onClick:g[21]||(g[21]=O=>ie(De)())},"确定")])])])])):z("",!0),u("div",Zo,[u("div",Qo,[g[47]||(g[47]=u("div",{class:"queue-title"},"任务队列",-1)),u("div",ei,[Ge(u("select",{id:"status-filter-select","onUpdate:modelValue":g[24]||(g[24]=O=>le.value=O),class:"status-filter-select-input"},[(F(!0),j(Et,null,It(I.value,O=>(F(),j("option",{key:O.value,value:O.value},ae(O.label),9,ti))),128))],512),[[fs,le.value]])]),u("div",si,[u("button",{class:"release-all-btn",onClick:Nt,disabled:ie(nt)()===0,title:ie(nt)()===0?"无可释放任务":`释放所有${ie(nt)()}个可释放任务`}," 一键释放 ",8,ni)]),u("div",ai,ae(Object.keys(Je.value).length)+"个任务",1)]),Object.keys(Je.value).length>0?(F(),j("div",ri,[u("table",{class:_e(["queue-table",{"narrow-view":R.value,"ultra-narrow-view":V.value}])},[u("thead",null,[u("tr",null,[u("th",oi,[u("div",ii,[g[49]||(g[49]=u("span",null,"任务ID",-1)),u("span",{class:"help-icon",onMouseenter:g[25]||(g[25]=O=>G(O))},g[48]||(g[48]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"#666","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[u("circle",{cx:"12",cy:"12",r:"10"}),u("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),u("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)])]),R.value?z("",!0):(F(),j("th",li,[u("div",ci,[g[50]||(g[50]=u("span",null,"题目内容",-1)),u("label",ui,[Ge(u("input",{type:"checkbox","onUpdate:modelValue":g[26]||(g[26]=O=>ne.value=O)},null,512),[[Ds,ne.value]]),u("span",{class:"slider round",title:ne.value?"关闭题目预览":"开启题目预览"},null,8,pi)])])])),V.value?z("",!0):(F(),j("th",di,"状态")),g[51]||(g[51]=u("th",{class:"col-actions"},"操作",-1))])]),u("tbody",null,[(F(!0),j(Et,null,It(rt.value,O=>(F(),j(Et,{key:O.type==="group"?O.groupId:O.tid},[O.type==="group"?(F(),j("tr",{key:0,class:"group-row",onClick:s=>je(O.groupId)},[u("td",hi,[u("div",gi,[u("span",vi,ae(O.isCollapsed?"▶":"▼"),1),u("span",mi,"已释放任务组 ("+ae(O.count)+"个)",1)])]),R.value?z("",!0):(F(),j("td",bi,[u("div",wi,ae(O.isCollapsed?"点击展开查看详情":"点击折叠隐藏详情"),1)])),V.value?z("",!0):(F(),j("td",yi,g[52]||(g[52]=[u("div",{class:"status-cell"},[u("span",{class:"task-tag status-released"},"已释放")],-1)]))),u("td",xi,[u("div",Ti,[u("span",Ci,ae(O.isCollapsed?"展开":"折叠"),1)])])],8,fi)):z("",!0),O.type==="group"&&!O.isCollapsed?(F(!0),j(Et,{key:1},It(O.tasks,(s,c)=>(F(),j("tr",{key:s.tid,class:_e(["task-row group-task-row",[ie(me)(s.status),{"selected-task-row":s.tid===H.value}]]),onClick:r=>ht(s.tid)},[u("td",$i,[u("div",{class:_e(["id-cell",{"id-with-status":V.value}])},[u("div",Si,[g[54]||(g[54]=u("span",{class:"group-indent"},"└─",-1)),u("span",Ei,ae(s.tid.substring(0,8)),1),s.wordType==="wps-analysis"?(F(),j("span",_i," 解 ")):z("",!0),s.wordType==="wps-enhance_analysis"||s.isEnhanced?(F(),j("span",Ai," 解 ")):z("",!0),s.wordType==="wps-check"||s.isCheckTask?(F(),j("span",Mi," 校 ")):z("",!0),Ee(s)?(F(),j("span",{key:3,class:"soft-break-warning-icon",onMouseenter:g[27]||(g[27]=r=>Xe(r))},g[53]||(g[53]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[u("title",null,"提示"),u("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),u("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),u("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):z("",!0)]),V.value?(F(),j("div",Pi,[u("span",{class:_e(["task-tag compact",ie(me)(s.status)]),onMouseenter:r=>s.status===-1&&s.errorMessage?ue(r,s):null,style:ts({cursor:s.status===-1&&s.errorMessage?"help":"default"})},ae(ie(ye)(s.status)),47,Di)])):z("",!0)],2)]),R.value?z("",!0):(F(),j("td",Oi,[u("div",{class:"subject-cell",onMouseenter:r=>W(r,s)},ae($t(s)),41,Ri)])),V.value?z("",!0):(F(),j("td",Ii,[u("div",Ui,[u("span",{class:_e(["task-tag",ie(me)(s.status)]),onMouseenter:r=>s.status===-1&&s.errorMessage?ue(r,s):null,style:ts({cursor:s.status===-1&&s.errorMessage?"help":"default"})},ae(ie(ye)(s.status)),47,Li)])])),g[55]||(g[55]=u("td",{class:"col-actions"},[u("div",{class:"task-actions"},[u("span",{class:"no-action-icon",title:"无可用操作"},[u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[u("circle",{cx:"12",cy:"12",r:"10"}),u("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),u("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})])])])],-1))],10,ki))),128)):z("",!0),O.type==="task"?(F(),j("tr",{key:2,class:_e(["task-row",[ie(me)(O.status),{"selected-task-row":O.tid===H.value}]]),onClick:s=>ht(O.tid)},[u("td",ji,[u("div",{class:_e(["id-cell",{"id-with-status":V.value}])},[u("div",Wi,[u("span",Bi,ae(O.tid.substring(0,8)),1),!O.isEnhanced&&!O.isCheckTask?(F(),j("span",Ni," 解 ")):z("",!0),O.isEnhanced?(F(),j("span",Vi," 解 ")):z("",!0),O.isCheckTask?(F(),j("span",Hi," 校 ")):z("",!0),Ee(O)?(F(),j("span",{key:3,class:"soft-break-warning-icon",onMouseenter:g[28]||(g[28]=s=>Xe(s))},g[56]||(g[56]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#ff9800","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[u("title",null,"提示"),u("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),u("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),u("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})],-1)]),32)):z("",!0)]),V.value?(F(),j("div",zi,[u("span",{class:_e(["task-tag compact",ie(me)(O.status)]),onMouseenter:s=>O.status===-1&&O.errorMessage?ue(s,O):null,style:ts({cursor:O.status===-1&&O.errorMessage?"help":"default"})},ae(ie(ye)(O.status)),47,qi)])):z("",!0)],2)]),R.value?z("",!0):(F(),j("td",Yi,[u("div",{class:"subject-cell",onMouseenter:s=>W(s,O)},ae($t(O)),41,Ji)])),V.value?z("",!0):(F(),j("td",Xi,[u("div",Ki,[u("span",{class:_e(["task-tag",ie(me)(O.status)]),onMouseenter:s=>O.status===-1&&O.errorMessage?ue(s,O):null,style:ts({cursor:O.status===-1&&O.errorMessage?"help":"default"})},ae(ie(ye)(O.status)),47,Gi)])])),u("td",Zi,[u("div",Qi,[O.status===1?(F(),j("button",{key:0,onClick:Tt(s=>ie(Oe)(O.tid),["stop"]),class:"terminate-btn"}," 终止 ",8,el)):z("",!0),O.status===2||O.status===4?(F(),j("button",{key:1,onClick:Tt(s=>Bt(O.tid),["stop"]),class:"release-btn",title:"释放任务控件"}," 释放 ",8,tl)):z("",!0),O.status!==1&&O.status!==2&&O.status!==4?(F(),j("span",sl,g[57]||(g[57]=[u("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#9e9e9e","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"},[u("circle",{cx:"12",cy:"12",r:"10"}),u("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),u("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})],-1)]))):z("",!0)])])],10,Fi)):z("",!0)],64))),128))])],2)])):(F(),j("div",nl,g[58]||(g[58]=[u("div",{class:"empty-text"},"暂无任务",-1)])))]),((We=(Ke=ve.value)==null?void 0:Ke.orgs[0])==null?void 0:We.orgId)===2?(F(),j("div",al,[u("div",{class:"log-header",onClick:Vt},[g[59]||(g[59]=u("div",{class:"log-title"},"执行日志",-1)),u("div",rl,[u("button",{class:"clear-btn",onClick:g[29]||(g[29]=Tt(O=>ie(A)(),["stop"]))},"清空日志"),u("span",ol,ae(v.value?"▼":"▶"),1)])]),v.value?(F(),j("div",{key:0,class:"log-content",innerHTML:ie(f)},null,8,il)):z("",!0)])):z("",!0)]),a.value?(F(),j("div",{key:2,class:"modal-overlay",onClick:g[32]||(g[32]=O=>a.value=!1)},[u("div",{class:"modal-content",onClick:g[31]||(g[31]=Tt(()=>{},["stop"]))},[An(_a,{onClose:g[30]||(g[30]=O=>a.value=!1)})])])):z("",!0)])}}},dl=tn(ll,[["__scopeId","data-v-008945e2"]]);export{dl as default};
