# 图片分割专用事件类型修复说明

## 问题描述
前端无法接收到图片分割相关的WebSocket事件，原因是事件类型冲突：
- 多个组件都在监听 `watcher` 类型的事件
- 事件可能被其他监听器拦截或处理
- 导致图片分割组件无法收到上传成功通知

## 解决方案
为图片分割功能创建专用的事件类型 `imageSplit`，避免与其他功能的事件监听冲突。

## 实现细节

### 1. 前端修改

#### WebSocket客户端事件类型支持
```javascript
// src/components/js/wsClient.js
this.eventListeners = {
  watcher: new Set(),
  urlMonitor: new Set(),
  config: new Set(),
  auth: new Set(),
  health: new Set(),
  connection: new Set(),
  error: new Set(),
  version: new Set(),
  imageSplit: new Set()  // 新增图片分割专用事件类型
};
```

#### 图片分割组件事件监听
```javascript
// src/components/ImageSplit.vue
// 监听imageSplit专用事件类型
wsClient.addEventListener('imageSplit', (message) => {
  console.log('收到imageSplit事件:', message)
  const { eventType, data } = message

  if (eventType === 'imageSplitUploadSuccess') {
    handleImageUploadSuccess(data)
  }
  else if (eventType === 'imageSplitUploadError') {
    handleImageUploadError(data)
  }
  // ... 其他事件类型
})
```

### 2. 后端修改

#### WebSocket服务器新增专用方法
```javascript
// run-server/services/wsServer.js
/**
 * 向特定客户端发送图片分割事件
 */
sendImageSplitEventToClient(eventType, data, clientId, messageId = null) {
  // 查找目标客户端
  let targetClient = null;
  for (const [client, info] of this.clients.entries()) {
    if (info.id === clientId) {
      targetClient = client;
      break;
    }
  }
  
  if (!targetClient) {
    defaultLogger.warn(`未找到客户端ID为 ${clientId} 的连接`);
    return false;
  }
  
  // 发送imageSplit类型的消息
  const message = {
    type: 'imageSplit',    // 专用事件类型
    eventType,
    data,
    messageId,
    timestamp: new Date().toISOString()
  };
  return this.sendToClient(targetClient, message);
}
```

#### 图片分割监控服务使用新方法
```javascript
// run-server/services/imageSplitWatcher.js
sendImageSplitEvent(eventType, data, fileName = null) {
  const clientId = fileName ? this.getClientForFile(fileName) : null;
  if (clientId) {
    // 使用新的专用方法发送imageSplit类型事件
    wsServer.sendImageSplitEventToClient(eventType, data, clientId);
  } else {
    // 广播时暂时还是使用watcher类型
    wsServer.sendWatcherEvent(eventType, data);
  }
}
```

## 事件流程

### 修复前的问题流程
```
后端发送: { type: 'watcher', eventType: 'imageSplitUploadSuccess', ... }
         ↓
多个组件监听: wsClient.addEventListener('watcher', ...)
         ↓
事件冲突: useTaskPane.js 和 ImageSplit.vue 都在监听
         ↓
结果: ImageSplit.vue 可能收不到事件
```

### 修复后的正确流程
```
后端发送: { type: 'imageSplit', eventType: 'imageSplitUploadSuccess', ... }
         ↓
专用监听: wsClient.addEventListener('imageSplit', ...)
         ↓
独立处理: 只有 ImageSplit.vue 监听此类型
         ↓
结果: 确保事件正确到达图片分割组件
```

## 支持的事件类型

### 图片分割专用事件
- **imageSplitUploadStart**: 图片开始上传
- **imageSplitUploadSuccess**: 图片上传成功
- **imageSplitUploadError**: 图片上传失败
- **imageSplitError**: 图片分割服务错误
- **imageSplitFilesFound**: 发现新的图片文件

### 事件数据格式
```javascript
{
  type: 'imageSplit',                    // 专用事件类型
  eventType: 'imageSplitUploadSuccess',  // 具体事件
  data: {
    file: 'image_split_1234567890.png',
    ossUrl: 'https://...',
    fileSize: 12345,
    totalProcessed: 1
  },
  messageId: null,
  timestamp: '2024-01-01T00:00:00.000Z'
}
```

## 技术优势

### 1. 事件隔离
- 避免不同功能模块之间的事件冲突
- 每个功能使用专用的事件类型
- 提高系统的可维护性

### 2. 调试便利
- 清晰的事件来源和目标
- 便于问题排查和日志分析
- 减少事件处理的复杂性

### 3. 扩展性
- 可以为其他功能创建专用事件类型
- 支持更细粒度的事件控制
- 便于添加新的事件处理逻辑

## 兼容性考虑

### 向后兼容
- 保留原有的 `watcher` 事件类型
- 其他功能继续使用原有的事件监听
- 不影响现有的文件监控功能

### 渐进式迁移
- 可以逐步将其他功能迁移到专用事件类型
- 支持混合使用不同的事件类型
- 降低系统重构的风险

## 测试验证

### 验证步骤
1. 选择图片并打开分割工具
2. 观察Console中的事件日志
3. 验证是否收到 `imageSplit` 类型的事件
4. 检查图片是否正确加载到Canvas

### 预期结果
- Console显示: "收到imageSplit事件: ..."
- 图片上传成功后自动加载真实OSS图片
- Loading状态正确切换
- 不再出现事件冲突问题

## 相关文件

### 前端文件
- `src/components/js/wsClient.js`: 添加imageSplit事件类型支持
- `src/components/ImageSplit.vue`: 修改事件监听器

### 后端文件
- `run-server/services/wsServer.js`: 添加专用事件发送方法
- `run-server/services/imageSplitWatcher.js`: 使用新的事件发送方法

## 注意事项

1. **事件类型命名**: 使用清晰的命名规范，避免冲突
2. **错误处理**: 确保事件发送失败时有适当的降级处理
3. **日志记录**: 保留详细的事件发送和接收日志
4. **性能考虑**: 避免创建过多的事件监听器
