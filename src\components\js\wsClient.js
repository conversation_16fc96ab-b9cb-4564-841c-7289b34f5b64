import logger from './logger';

class WsClient {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectTimer = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000; // 3秒重连间隔
    this.messageQueue = [];
    this.messageCallbacks = new Map(); // 存储消息回调
    this.messageId = 0; // 消息ID计数器
    this.connectionSuccessHandler = null; // Added
    this.eventListeners = {
      watcher: new Set(),
      urlMonitor: new Set(),
      config: new Set(),
      auth: new Set(),
      health: new Set(),
      connection: new Set(),
      error: new Set(),
      version: new Set(),
      imageSplit: new Set()
    };
    // 特殊处理的消息类型，这些类型的消息即使失败也不会影响系统其它功能
    this.nonCriticalTypes = new Set(['health', 'auth.refreshSession', 'auth.getOwn', 'logger']);

    // 添加客户端ID
    this.clientId = null;

    // 尝试从localStorage加载客户端ID
    try {
      this.clientId = localStorage.getItem('wsClientId');
    } catch (error) {
      logger.warn('无法从localStorage加载客户端ID', error);
    }
  }

  /**
   * 获取客户端ID
   * @returns {string} 客户端ID
   */
  getClientId() {
    return this.clientId;
  }

  /**
   * 保存客户端ID到localStorage
   * @param {string} clientId 客户端ID
   * @private
   */
  _saveClientId(clientId) {
    this.clientId = clientId;
    try {
      localStorage.setItem('wsClientId', clientId);
    } catch (error) {
      logger.warn('无法保存客户端ID到localStorage', error);
    }
  }

  /**
   * 连接到WebSocket服务器
   * @returns {Promise<boolean>} 连接是否成功
   */
  connect() {
    return new Promise((resolve) => {
      if (this.isConnected) {
        resolve(true);
        return;
      }

      if (this.isConnecting) {
        // 如果已经在连接中，把回调添加到队列
        const checkConnected = setInterval(() => {
          if (this.isConnected) {
            clearInterval(checkConnected);
            resolve(true);
          }
        }, 100);
        return;
      }

      this.isConnecting = true;
      const wsUrl = 'ws://127.0.0.1:3000';
      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          logger.success('WebSocket连接成功');

          // 连接建立后，发送任何排队的消息
          this._processQueue();

          if (this.connectionSuccessHandler) {
            this.connectionSuccessHandler();
          }

          // Notify general event listeners (e.g., for App.vue UI updates)
          this._notifyEventListeners('connection', { status: 'connected' });

          resolve(true);
        };

        this.ws.onmessage = (event) => {
          try {
            //
            const data = JSON.parse(event.data);

            // 处理连接成功消息，保存客户端ID
            if (data.type === 'connection' && data.action === 'connected' && data.clientId) {
              this._saveClientId(data.clientId);
            }

            // 处理消息ID回调
            if (data.messageId && this.messageCallbacks.has(data.messageId)) {
              const callback = this.messageCallbacks.get(data.messageId);
              callback(data);
              this.messageCallbacks.delete(data.messageId);
            }

            // 通知事件监听器
            if (data.type && this.eventListeners[data.type]) {
              this._notifyEventListeners(data.type, data);
            }
          } catch (error) {
            logger.error('处理WebSocket消息时出错', error);
          }
        };

        this.ws.onerror = (error) => {
          logger.error('WebSocket错误', error);
          this._notifyEventListeners('error', { error: 'WebSocket连接错误' });

          if (!this.isConnected) {
            this.isConnecting = false;
            resolve(false);
          }
        };

        this.ws.onclose = (event) => {
          this.isConnected = false;
          this.isConnecting = false;
          logger.warn('WebSocket连接关闭', { code: event.code, reason: event.reason });

          this._notifyEventListeners('connection', {
            status: 'disconnected',
            code: event.code,
            reason: event.reason
          });

          // 尝试重新连接
          this._reconnect();
        };
      } catch (error) {
        this.isConnecting = false;
        logger.error('创建WebSocket连接时出错', error);
        this._notifyEventListeners('error', { error: '创建WebSocket连接时出错' });
        resolve(false);

        // 也尝试重新连接
        this._reconnect();
      }
    });
  }

  /**
   * 关闭连接
   */
  disconnect() {
    if (this.ws) {

      this.ws.close();
      this.ws = null;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.isConnected = false;
    this.isConnecting = false;
    this.messageCallbacks.clear();
  }

  /**
   * 发送消息并等待响应
   * @param {string} type 消息类型
   * @param {string} action 执行的动作
   * @param {object} data 消息数据
   * @param {number} timeout 超时时间(毫秒)
   * @returns {Promise<object>} 服务器响应
   */
  async sendRequest(type, action, data = {}, timeout = 10000) {
    // 对于非关键请求（如健康检查），使用较短的超时时间
    const isNonCritical = this.nonCriticalTypes.has(type) || this.nonCriticalTypes.has(`${type}.${action}`);
    const actualTimeout = isNonCritical ? Math.min(timeout, 5000) : timeout;

    if (!this.isConnected) {
      try {
        const connected = await this.connect();
        if (!connected) {
          if (isNonCritical) {
            // 对于非关键请求，返回一个带错误标记的响应，而不是抛出异常
            return {
              success: false,
              error: '无法连接到WebSocket服务器',
              data: null
            };
          }
          throw new Error('无法连接到WebSocket服务器');
        }
      } catch (error) {
        if (isNonCritical) {
          // 对于非关键请求，返回一个带错误标记的响应
          return {
            success: false,
            error: error.message,
            data: null
          };
        }
        throw error;
      }
    }

    return new Promise((resolve, reject) => {
      // 创建唯一消息ID
      const messageId = this._generateMessageId();

      // 创建完整消息
      const message = {
        messageId,
        type,
        action,
        data
      };

      // 设置超时
      const timeoutId = setTimeout(() => {
        this.messageCallbacks.delete(messageId);

        if (isNonCritical) {
          // 对于非关键请求，我们返回一个标记为超时的响应，而不是抛出异常
          logger.warn(`非关键请求超时: ${type}.${action}，返回降级响应`);
          resolve({
            success: false,
            error: `请求超时: ${type}.${action}`,
            data: null
          });
        } else {
          reject(new Error(`请求超时: ${type}.${action}`));
        }
      }, actualTimeout);

      // 存储回调
      this.messageCallbacks.set(messageId, (response) => {
        clearTimeout(timeoutId);

        if (response.success === false && !isNonCritical) {
          reject(new Error(response.message || '请求失败'));
        } else {
          resolve(response);
        }
      });

      // 发送消息
      try {
        this._sendMessage(message);
      } catch (error) {
        clearTimeout(timeoutId);
        this.messageCallbacks.delete(messageId);

        if (isNonCritical) {
          // 非关键请求发送失败时返回一个错误响应而不是抛出异常
          logger.warn(`非关键请求发送失败: ${type}.${action}，返回降级响应`, error);
          resolve({
            success: false,
            error: `发送请求失败: ${error.message}`,
            data: null
          });
        } else {
          reject(error);
        }
      }
    });
  }

  /**
   * 监听特定类型的事件
   * @param {string} type 事件类型
   * @param {Function} callback 回调函数
   * @returns {Function} 用于移除监听器的函数
   */
  addEventListener(type, callback) {
    if (!this.eventListeners[type]) {
      this.eventListeners[type] = new Set();
    }

    this.eventListeners[type].add(callback);

    return () => {
      this.eventListeners[type].delete(callback);
    };
  }

  /**
   * 内部方法：生成唯一消息ID
   * @returns {string} 唯一ID
   */
  _generateMessageId() {
    return `${Date.now()}-${++this.messageId}`;
  }

  /**
   * 内部方法：发送WebSocket消息
   * @param {object} message 要发送的消息
   */
  _sendMessage(message) {
    if (!this.isConnected) {
      // 如果未连接，将消息添加到队列

      this.messageQueue.push(message);
      this.connect(); // 尝试连接
      return;
    }

    try {
      const messageStr = JSON.stringify(message);
      this.ws.send(messageStr);
      // 对于非关键消息类型（如健康检查），使用更低级别的日志记录
      const isNonCritical = this.nonCriticalTypes.has(message.type) ||
                           this.nonCriticalTypes.has(`${message.type}.${message.action}`);

      if (!isNonCritical) {
        logger.info('发送WebSocket消息', {
          type: message.type,
          action: message.action,
          messageId: message.messageId
        });
      }
    } catch (error) {
      logger.error('发送WebSocket消息时出错', error);

      // 重新加入队列？
      if (message.messageId && this.messageCallbacks.has(message.messageId)) {
        this.messageQueue.push(message);
      }

      // 尝试重新连接
      this.isConnected = false;
      this._reconnect();

      // 将错误向上抛出，以便调用者能够处理
      throw error;
    }
  }

  /**
   * 内部方法：处理消息队列
   */
  _processQueue() {
    if (this.messageQueue.length === 0) return;



    // 复制队列并清空原队列
    const queue = [...this.messageQueue];
    this.messageQueue = [];

    // 发送所有排队的消息
    queue.forEach(message => {
      this._sendMessage(message);
    });
  }

  /**
   * 内部方法：重新连接
   */
  _reconnect() {
    if (this.reconnectTimer || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.reconnectAttempts++;

    const delay = this.reconnectInterval * Math.min(this.reconnectAttempts, 3);


    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.connect();
    }, delay);
  }

  /**
   * 内部方法：通知事件监听器
   * @param {string} type 事件类型
   * @param {object} data 事件数据
   */
  _notifyEventListeners(type, data) {
    if (!this.eventListeners[type]) return;

    this.eventListeners[type].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        logger.error(`执行 ${type} 事件监听器时出错`, error);
      }
    });
  }

  setConnectionSuccessHandler(handler) {
    this.connectionSuccessHandler = handler;
  }

  // === 文件监控服务方法 ===
  async getWatcherStatus() {
    return this.sendRequest('watcher', 'getStatus');
  }

  async setWatchDirectory(directory) {
    return this.sendRequest('watcher', 'setDirectory', { directory });
  }

  async startWatcher() {
    return this.sendRequest('watcher', 'start');
  }

  async stopWatcher() {
    return this.sendRequest('watcher', 'stop');
  }

  /**
   * 将文件与当前客户端关联，确保文件上传通知只发送给此客户端
   * @param {string} fileName - 文件名
   * @returns {Promise<Object>} 请求响应
   */
  async associateFileWithClient(fileName) {
    if (!fileName) {
      logger.warn('关联文件时必须提供文件名');
      return {
        success: false,
        message: '必须提供文件名'
      };
    }

    return this.sendRequest('watcher', 'associateFile', {
      fileName,
      clientId: this.clientId
    });
  }

  // === URL监控服务方法 ===
  async getUrlMonitorStatus() {
    return this.sendRequest('urlMonitor', 'getStatus', {
      clientId: this.clientId
    });
  }

  async startUrlMonitoring(url, interval, options = {}) {
    return this.sendRequest('urlMonitor', 'startMonitoring', {
      url,
      interval,
      clientId: this.clientId,
      ...options
    });
  }

  async stopUrlMonitoring(urlId) {
    return this.sendRequest('urlMonitor', 'stopMonitoring', {
      urlId,
      clientId: this.clientId
    });
  }

  async forceUrlCheck(urlId) {
    return this.sendRequest('urlMonitor', 'forceCheck', {
      urlId,
      clientId: this.clientId
    });
  }

  async startUrlChecking(urlId) {
    return this.sendRequest('urlMonitor', 'startChecking', {
      urlId,
      clientId: this.clientId
    });
  }

  async forceUrlDownload(urlId) {
    return this.sendRequest('urlMonitor', 'forceDownload', {
      urlId,
      clientId: this.clientId
    });
  }

  async getDownloadPath() {
    return this.sendRequest('urlMonitor', 'getDownloadPath', {
      clientId: this.clientId
    });
  }

  async setDownloadPath(path) {
    return this.sendRequest('urlMonitor', 'setDownloadPath', {
      path,
      clientId: this.clientId
    });
  }

  async updateTaskStatus(urlId, status, taskId) {
    return this.sendRequest('urlMonitor', 'updateTaskStatus', {
      urlId,
      status,
      taskId,
      clientId: this.clientId
    });
  }

  // === 配置服务方法 ===
  async getAddonConfigPath() {
    return this.sendRequest('config', 'getAddonConfigPath');
  }

  async setAddonConfigPath(path) {
    return this.sendRequest('config', 'setAddonConfigPath', { path });
  }

  // 添加获取学科和年级选择的方法
  async getSubjectAndStage() {
    return this.sendRequest('config', 'getSubjectAndStage');
  }
  
  // 添加设置学科和年级选择的方法
  async setSubjectAndStage(subject, stage) {
    return this.sendRequest('config', 'setSubjectAndStage', { subject, stage });
  }

  // 添加获取保存方式的方法
  async getSaveMethod() {
    return this.sendRequest('config', 'getSaveMethod');
  }

  // 添加设置保存方式的方法
  async setSaveMethod(saveMethod) {
    return this.sendRequest('config', 'setSaveMethod', { saveMethod });
  }

  // === 认证服务方法 ===
  async login(username, password) {
    return this.sendRequest('auth', 'login', { username, password });
  }

  async logout() {
    return this.sendRequest('auth', 'logout');
  }

  async getAuthUserInfo() {
    return this.sendRequest('auth', 'getOwn');
  }

  async refreshSession() {
    return this.sendRequest('auth', 'refreshSession');
  }

  // === 健康检查方法 ===
  async getHealthStatus() {
    try {
      // 将超时降低到3秒，避免长时间等待
      return await this.sendRequest('health', 'getStatus', {}, 3000);
    } catch (error) {
      // 健康检查失败不应影响系统其他功能
      logger.warn('健康检查请求失败，但不影响其他功能', error);
      return {
        success: false,
        error: error.message,
        data: { status: 'error' }
      };
    }
  }

  // === 日志同步方法 ===
  /**
   * 同步日志到服务端
   * @param {string} content 日志内容
   * @param {string} timestamp 时间戳
   * @param {string} clientId 客户端ID
   * @returns {Promise<object>} 响应结果
   */
  async syncLog(content, timestamp, clientId) {
    return this.sendRequest('logger', 'syncLog', {
      content,
      timestamp,
      clientId
    });
  }

  /**
   * 获取日志统计信息
   * @returns {Promise<object>} 统计信息
   */
  async getLogStats() {
    return this.sendRequest('logger', 'getStats');
  }

  /**
   * 读取客户端日志
   * @param {string} clientId 客户端ID
   * @param {number} lines 读取行数
   * @returns {Promise<object>} 日志内容
   */
  async readClientLog(clientId, lines = 100) {
    return this.sendRequest('logger', 'readLog', {
      clientId,
      lines
    });
  }
}

// 导出单例实例
const wsClient = new WsClient();
export default wsClient;
