// wps-utils.js: Utility functions for WPS integration

/**
 * Logs messages to the console with a UI prefix and timestamp.
 * @param {string} message - The message to log.
 */
function logToUI(message) {
  console.log(`[UI LOG] ${new Date().toLocaleTimeString()}: ${message}`);
}

/**
 * Returns an appropriate XHR object based on the browser (IE or modern).
 * @returns {XMLHttpRequest|XDomainRequest} The XHR object.
 */
function getHttpObj() {
  var httpobj = null;
  if (IEVersion() < 10) {
    try {
      httpobj = new XDomainRequest(); // For IE 8, 9
    } catch (e1) {
      httpobj = new createXHR(); // Fallback for other IE versions or if XDomainRequest fails
    }
  } else {
    httpobj = new createXHR(); // For modern browsers
  }
  return httpobj;
}

/**
 * Creates a standard XMLHttpRequest object.
 * Handles different ways XMLHttpRequest might be available in older IE versions.
 * @returns {XMLHttpRequest} The XMLHttpRequest object.
 * @throws {Error} If XHR is not supported.
 */
function createXHR() {
  if (typeof XMLHttpRequest !== 'undefined') {
    return new XMLHttpRequest();
  } else if (typeof ActiveXObject !== 'undefined') {
    // For IE versions prior to 7
    var versions = ['MSXML2.XMLHttp.6.0', 'MSXML2.XMLHttp.3.0', 'MSXML2.XMLHttp'];
    for (var i = 0; i < versions.length; i++) {
      try {
        return new ActiveXObject(versions[i]);
      } catch (e) { /* Do nothing, try next version */ }
    }
  }
  throw new Error('您的浏览器不支持XHR对象 (Your browser does not support XHR objects)');
}

// Base64 encoding utilities (ponyfill for btoa if not available)
var fromCharCode = String.fromCharCode;
var cb_utob = function(c) {
  if (c.length < 2) {
    var cc = c.charCodeAt(0);
    return cc < 0x80 ? c :
      cc < 0x800 ? (fromCharCode(0xc0 | (cc >>> 6)) +
          fromCharCode(0x80 | (cc & 0x3f))) :
        (fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +
          fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
          fromCharCode(0x80 | (cc & 0x3f)));
  } else {
    var cc = 0x10000 + (c.charCodeAt(0) - 0xD800) * 0x400 + (c.charCodeAt(1) - 0xDC00);
    return (fromCharCode(0xf0 | ((cc >>> 18) & 0x07)) +
      fromCharCode(0x80 | ((cc >>> 12) & 0x3f)) +
      fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
      fromCharCode(0x80 | (cc & 0x3f)));
  }
};
var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
var utob = function(u) {
  return u.replace(re_utob, cb_utob);
};
var _encode = function(u) {
  var isUint8Array = Object.prototype.toString.call(u) === '[object Uint8Array]';
  // Assuming 'btoa' is available or polyfilled
  return isUint8Array ? u.toString('base64') : btoa(utob(String(u)));
};

if (typeof btoa !== 'function') {
  window.btoa = function func_btoa(input) {
    var str = String(input);
    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    for (
      var block, charCode, idx = 0, map = chars, output = '';
      // eslint-disable-next-line no-cond-assign
      str.charAt(idx | 0) || (map = '=', idx % 1);
      output += map.charAt(63 & block >> 8 - idx % 1 * 8)
    ) {
      charCode = str.charCodeAt(idx += 3 / 4);
      if (charCode > 0xFF) {
        throw new Error('\'btoa\' failed: The string to be encoded contains characters outside of the Latin1 range.');
      }
      block = block << 8 | charCode;
    }
    return output;
  };
}

/**
 * Encodes a string to Base64.
 * @param {string|Uint8Array} u - The string or Uint8Array to encode.
 * @param {boolean} [urisafe=false] - If true, makes the output URL-safe.
 * @returns {string} The Base64 encoded string.
 */
function encode(u, urisafe) {
  return !urisafe ?
    _encode(u) :
    _encode(String(u)).replace(/[+\/]/g, function(m0) {
      return m0 === '+' ? '-' : '_';
    }).replace(/=/g, '');
}

/**
 * Detects the version of Internet Explorer.
 * @returns {number} IE version (e.g., 7, 8, 9, 10, 11), 20 for Edge, or 30 for other modern browsers.
 */
function IEVersion() {
  var userAgent = navigator.userAgent;
  var isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1; // IE 10 or older
  var isEdge = userAgent.indexOf('Edge') > -1 && !isIE; // Edge (Legacy)
  var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1; // IE 11

  if (isIE) {
    var reIE = new RegExp('MSIE (\\d+\\.\\d+);');
    reIE.test(userAgent);
    var fIEVersion = parseFloat(RegExp['$1']);
    if (fIEVersion === 7) return 7;
    else if (fIEVersion === 8) return 8;
    else if (fIEVersion === 9) return 9;
    else if (fIEVersion === 10) return 10;
    else return 6; // Default for older IE
  } else if (isEdge) {
    return 20; // Edge
  } else if (isIE11) {
    return 11; // IE 11
  } else {
    return 30; // Modern browser
  }
}

/**
 * Generates a GUID (Globally Unique Identifier).
 * @returns {string} A GUID string.
 */
function guid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0,
      v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Attempts to start the WPS Cloud Server by navigating to its custom URL scheme.
 */
function InitWpsCloudSvr() {
  logToUI('尝试启动WPS Cloud Server (Attempting to start WPS Cloud Server)');
  const url = serverId ?
    'ksoWPSCloudSvr://start=RelayHttpServer' + '&serverId=' + serverId :
    'ksoWPSCloudSvr://start=RelayHttpServer';
  window.location.href = url; // This will try to open the custom protocol
}

/**
 * Retrieves or generates and stores a serverId in localStorage.
 * Falls back to a temporary window variable if localStorage is not available.
 * @returns {string} The serverId.
 */
function getServerId() {
  if (window.localStorage) {
    if (!localStorage.getItem('serverId')) {
      localStorage.setItem('serverId', guid());
    }
    return localStorage.getItem('serverId');
  } else {
    // Fallback for environments where localStorage is not available (e.g., some sandboxed iframes)
    if (!window._tempServerId) {
      window._tempServerId = guid();
    }
    return window._tempServerId;
  }
}

/**
 * Sends a request to the local WPS service.
 * Handles retries and attempts to start the WPS Cloud Server if needed.
 * @param {object} reqInner - Request object { url: string, type: string (e.g., 'POST') }.
 * @param {string|object} t - Data to send with the request.
 * @param {function} callback - Callback function to handle the response { status: number, message?: string, res?: object }.
 */
function startWps(reqInner, t, callback) {
  /**
   * Inner function to handle the actual request sending and retries.
   * @param {object} currentReq - The request object.
   * @param {number} tryCount - Remaining retry attempts.
   * @param {boolean} bPop - Whether to attempt InitWpsCloudSvr on first failure.
   */
  function startWpsInner(currentReq, tryCount, bPop) {
    if (tryCount < 1) {
      logToUI('startWpsInner: 尝试次数用尽 (Retry attempts exhausted)');
      if (callback) callback({ status: 2, message: '请允许浏览器打开WPS Office或检查WPS服务是否运行 (Please allow the browser to open WPS Office or check if the WPS service is running)' });
      return;
    }

    var bRetry = true; // Flag to control retry logic
    var xmlReq = getHttpObj();
    logToUI(`WPS请求 (WPS Request) [${currentReq.type}] 到 (to) ${currentReq.url} (尝试 (attempt) ${5 - tryCount}/4)`);
    xmlReq.open(currentReq.type, currentReq.url);

    // Add referer header
    var refererValue = 'https://www.wanweiedu.com/'; // Default referer
    // Example of how you might get dynamic user data if needed, though not directly used here for referer
    // if (window.ipcRenderer) {
    //   const userInfoSync = window.ipcRenderer.sendSync('get-user-info-sync');
    //   if (userInfoSync && userInfoSync.data && userInfoSync.data.nickname) {
    //      // refererValue could be modified here if needed
    //   }
    // }
    xmlReq.setRequestHeader('Referer', refererValue);


    xmlReq.onload = function(res) {
      logToUI(`WPS请求 (WPS Request) ${currentReq.url} onload: status=${res.target.status}, responseText=${res.target.responseText ? res.target.responseText.substring(0, 100) : 'N/A'}`);
      if (res.target.status >= 200 && res.target.status < 300) {
        if (callback) callback({ status: 0, res: res });
      } else {
        var responseStr = IEVersion() < 10 ? xmlReq.responseText : res.target.response;
        var errorMessageText = '请求失败 (Request failed)';
        try {
          var errorData = JSON.parse(responseStr);
          errorMessageText = errorData.message || errorData.data || errorMessageText;
          // If subserver is not available on the first try, attempt to start WPS service
          if (errorData.data === 'Subserver not available.' && tryCount === 4 && bPop) {
            logToUI('WPS子服务不可用，尝试启动WPS服务 (WPS subservice unavailable, attempting to start WPS service)');
            InitWpsCloudSvr();
            setTimeout(function() {
              if (bRetry) {
                bRetry = false;
                startWpsInner(currentReq, --tryCount, false); // Retry without popping InitWpsCloudSvr again
              }
            }, 3000); // Wait 3 seconds for WPS service to potentially start
            return;
          }
        } catch (e) {
          logToUI('解析WPS错误响应失败 (Failed to parse WPS error response): ' + e.message + ', 原始响应 (original response): ' + responseStr);
          errorMessageText = responseStr || errorMessageText;
        }
        if (callback) callback({
          status: 1,
          message: `WPS服务请求错误 (WPS service request error): ${res.target.status} - ${errorMessageText}`,
          res: res
        });
      }
    };

    xmlReq.ontimeout = xmlReq.onerror = function(resEvent) {
      logToUI(`WPS请求 (WPS Request) ${currentReq.url} onerror/ontimeout. Type: ${resEvent.type}`);
      xmlReq.bTimeout = true; // Custom flag for IE handling
      // On the first failure (tryCount > 1 implies not the last try), and if bPop is true, try to start WPS
      if (bPop && tryCount > 1) {
        logToUI('WPS请求失败或超时，尝试启动WPS服务 (WPS request failed or timed out, attempting to start WPS service)');
        InitWpsCloudSvr();
      }
      // Retry logic
      setTimeout(function() {
        if (bRetry) {
          bRetry = false;
          startWpsInner(currentReq, --tryCount, false); // Next retry, don't pop InitWpsCloudSvr
        }
      }, tryCount === 4 ? 3000 : 1000); // Longer delay on first retry, shorter for subsequent
    };

    // Specific handling for IE versions < 10 (XDomainRequest or older ActiveX)
    if (IEVersion() < 10) {
      xmlReq.onreadystatechange = function() {
        if (xmlReq.readyState !== 4) return; // Only process when request is complete
        if (xmlReq.bTimeout) return; // If manually flagged as timeout, skip
        if (xmlReq.status === 200) {
          xmlReq.onload({ target: xmlReq }); // Simulate onload for consistent handling
        } else {
          xmlReq.onerror({ type: 'error' }); // Simulate onerror
        }
      };
    }
    xmlReq.timeout = 3000; // Set a timeout for the request

    try {
      xmlReq.send(t);
    } catch (e) {
      logToUI('xmlReq.send 异常 (exception): ' + e.message);
      if (callback) callback({ status: 3, message: '发送请求到WPS服务失败 (Failed to send request to WPS service): ' + e.message });
    }
  }

  startWpsInner(reqInner, 4, true); // Initial call: 4 retries, pop InitWpsCloudSvr on first fail
}


/**
 * Formats data for sending to the WPS service.
 * Encodes data to Base64 if serverVersion is appropriate.
 * @param {object} element - Addon information or command details.
 * @param {string} cmd - The command to be executed (e.g., 'enable', 'disable', 'disableall').
 * @returns {string} JSON string of the formatted data, possibly Base64 encoded.
 */
function FormartData(element, cmd) {
  var data = {
    'cmd': cmd,
    'name': element.name, // Addon name
    'url': element.url, // Addon URL
    'addonType': element.addonType, // e.g., 'wps'
    'online': element.online, // 'true' or 'false'
    'version': element.version || '', // Optional addon version
    'customDomain': element.customDomain || '' // Optional custom domain
  };
  return FormatSendData(data);
}

/**
 * Prepares the final data payload for WPS service requests.
 * If serverVersion >= 1.0.2 and serverId is defined, it wraps the Base64 encoded data.
 * Otherwise, it might return just Base64 or raw JSON string based on serverVersion.
 * @param {object} data - The data object to format.
 * @returns {string} The formatted data string for the WPS request.
 */
function FormatSendData(data) {
  var strData = JSON.stringify(data);
  // Ensure serverVersion and serverId are accessible in this scope
  // These are expected to be global or passed in if this function is moved.
  // For now, assuming they are global as in the original script.
  if (typeof serverVersion !== 'undefined' && serverVersion >= '1.0.2' && typeof serverId !== 'undefined') {
    var base64Data = encode(strData); // Base64 encode the JSON string
    logToUI('FormatSendData: 使用Base64编码 (Using Base64 encoding) (serverVersion >= 1.0.2)');
    return JSON.stringify({ serverId: serverId, data: base64Data }); // Wrap with serverId
  } else {
    logToUI('FormatSendData: serverVersion < 1.0.2 或 serverId 未定义 (or serverId undefined)');
    if (typeof serverVersion !== 'undefined' && serverVersion < '1.0.2') {
      logToUI('FormatSendData: serverVersion < 1.0.2, 返回纯Base64编码数据 (returning pure Base64 encoded data)');
      return encode(strData); // Older versions might expect raw Base64
    }
    // Fallback or error case: should ideally not happen if serverVersion is always determined.
    logToUI('FormatSendData: serverVersion 未达到1.0.2或serverId未定义，返回原始JSON字符串 (serverVersion not >= 1.0.2 or serverId undefined, returning raw JSON string).');
    return strData;
  }
}

// Globals expected by WPS functions (ensure they are initialized in script.js or passed appropriately)
// var serverId; // Initialized in script.js
// var serverVersion = 'wait'; // Initialized in script.js
// var addonInstalled = false; // Initialized in script.js
