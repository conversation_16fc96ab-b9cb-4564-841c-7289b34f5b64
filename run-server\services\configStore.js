const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// SQLite 数据库文件名
const DB_NAME = 'app_config.sqlite';

class ConfigStore {
    constructor() {
        // 数据库目录 - 使用 Electron 的 userData 目录
        const dataDir = path.join(app.getPath('userData'), 'db');
        this.dbPath = path.join(dataDir, DB_NAME);
        this.db = null;
        this.configCache = {}; // 内存缓存，用于同步访问
        this.initialized = false;

        // 确保目录存在
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // 默认配置值
        this.defaultConfig = {
            watchDir: 'C:\\ww-wps-addon\\Temp',
            urlDownloadPath: 'c:\\ww-wps-addon\\Downloads',
            addonConfigPath: 'C:\\ww-wps-addon\\cfg',
            dbPath: 'C:\\ww-wps-addon\\db',
            lastSelectedSubject: '',  // 添加默认学科
            lastSelectedStage: 'junior',  // 添加默认年级
            showTooltip: 'true',  // 默认显示tooltip
        };

        // 立即开始初始化（异步）
        this.initPromise = this.init();
    }

    /**
     * 初始化 SQLite 数据库和表结构
     */
    async init() {
        if (this.initialized) {
            return;
        }

        try {
            this.db = new Database(this.dbPath);
            console.info('配置数据库实例已创建。');

            // 创建配置表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS app_config (
                    configKey TEXT PRIMARY KEY,
                    configValue TEXT NOT NULL,
                    updatedAt TEXT NOT NULL
                );
            `);

            console.info('配置数据库表已成功创建/加载。');

            // 初始化默认配置
            await this._initializeDefaultConfig();

            // 加载所有配置到内存缓存
            await this._loadConfigToCache();

            this.initialized = true;
            console.info('配置存储初始化完成。');
        } catch (error) {
            console.error(`初始化配置数据库失败: ${error.message}`);
            // 如果数据库初始化失败，使用默认配置
            this.configCache = { ...this.defaultConfig };
            this.initialized = true;
        }
    }

    /**
     * 确保初始化完成
     * @private
     */
    async _ensureInitialized() {
        if (!this.initialized) {
            await this.initPromise;
        }
    }

    /**
     * 初始化默认配置
     * @private
     */
    async _initializeDefaultConfig() {
        try {
            for (const [key, value] of Object.entries(this.defaultConfig)) {
                const stmt = this.db.prepare('SELECT configValue FROM app_config WHERE configKey = ?');
                const existing = stmt.get(key);

                if (!existing) {
                    const insertStmt = this.db.prepare(
                        'INSERT INTO app_config (configKey, configValue, updatedAt) VALUES (?, ?, ?)'
                    );
                    insertStmt.run(key, value, new Date().toISOString());
                    console.info(`默认配置 ${key} 已初始化为: ${value}`);

                    // 确保目录存在
                    if (!fs.existsSync(value)) {
                        fs.mkdirSync(value, { recursive: true });
                        console.info(`已创建目录: ${value}`);
                    }
                }
            }
        } catch (error) {
            console.error(`初始化默认配置失败: ${error.message}`);
        }
    }

    /**
     * 将数据库配置加载到内存缓存
     * @private
     */
    async _loadConfigToCache() {
        try {
            const stmt = this.db.prepare('SELECT configKey, configValue FROM app_config');
            const rows = stmt.all();

            this.configCache = { ...this.defaultConfig };
            rows.forEach(row => {
                this.configCache[row.configKey] = row.configValue;
            });
        } catch (error) {
            console.error(`加载配置到缓存失败: ${error.message}`);
            this.configCache = { ...this.defaultConfig };
        }
    }

    /**
     * 获取配置值（同步方法，从缓存读取）
     * @param {string} key - 配置键
     * @returns {string|null} 配置值
     * @private
     */
    _getConfigSync(key) {
        return this.configCache[key] || this.defaultConfig[key] || null;
    }

    /**
     * 设置配置值（异步方法，同时更新数据库和缓存）
     * @param {string} key - 配置键
     * @param {string} value - 配置值
     * @returns {Promise<boolean>} 是否成功设置
     * @private
     */
    async _setConfig(key, value) {
        await this._ensureInitialized();

        try {
            // 更新缓存
            this.configCache[key] = value;

            // 如果数据库可用，更新数据库
            if (this.db) {
                const stmt = this.db.prepare(`
                    INSERT OR REPLACE INTO app_config (configKey, configValue, updatedAt) 
                    VALUES (?, ?, ?)
                `);
                stmt.run(key, value, new Date().toISOString());
            }

            console.info(`配置 ${key} 已更新为: ${value}`);
            return true;
        } catch (error) {
            console.error(`设置配置 ${key} 失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 确保目录存在（兼容原有方法）
     */
    ensureDirectoriesExist() {
        try {
            const directoriesToCheck = [
                'watchDir',
                'urlDownloadPath',
                'addonConfigPath',
                'dbPath'
            ];

            directoriesToCheck.forEach(dirKey => {
                const dirPath = this._getConfigSync(dirKey);
                if (dirPath && !fs.existsSync(dirPath)) {
                    fs.mkdirSync(dirPath, { recursive: true });
                    console.info(`已创建目录: ${dirPath}`);
                }
            });
        } catch (error) {
            console.info('创建目录失败', error);
        }
    }

    /**
     * 获取监控目录（同步方法）
     * @returns {string}
     */
    getWatchDir() {
        return this._getConfigSync('watchDir');
    }

    /**
     * 设置监控目录（异步方法）
     * @param {string} dir - 目录路径
     * @returns {Promise<boolean>}
     */
    async setWatchDir(dir) {
        // 确保目录存在
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.info(`已创建目录: ${dir}`);
        }
        return await this._setConfig('watchDir', dir);
    }

    /**
     * 获取下载路径（同步方法）
     * @returns {string}
     */
    getUrlDownloadPath() {
        return this._getConfigSync('urlDownloadPath');
    }

    /**
     * 设置下载路径（异步方法）
     * @param {string} dirPath - 目录路径
     * @returns {Promise<boolean>}
     */
    async setUrlDownloadPath(dirPath) {
        // 确保目录存在
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.info(`已创建目录: ${dirPath}`);
        }
        return await this._setConfig('urlDownloadPath', dirPath);
    }

    /**
     * 获取插件配置路径（同步方法）
     * @returns {string}
     */
    getAddonConfigPath() {
        return this._getConfigSync('addonConfigPath');
    }

    /**
     * 设置插件配置路径（异步方法）
     * @param {string} dirPath - 目录路径
     * @returns {Promise<boolean>}
     */
    async setAddonConfigPath(dirPath) {
        // 确保目录存在
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.info(`已创建目录: ${dirPath}`);
        }
        return await this._setConfig('addonConfigPath', dirPath);
    }

    /**
     * 获取数据库路径（同步方法）
     * @returns {string}
     */
    getDbPath() {
        const dbPath = this._getConfigSync('dbPath');
        return dbPath || 'C:\\ww-wps-addon\\db\\urlMonitor';
    }

    /**
     * 设置数据库路径（异步方法）
     * @param {string} dirPath - 目录路径
     * @returns {Promise<boolean>}
     */
    async setDbPath(dirPath) {
        // 确保目录存在
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.info(`已创建目录: ${dirPath}`);
        }
        return await this._setConfig('dbPath', dirPath);
    }

    /**
     * 获取所有配置（同步方法）
     * @returns {Object} 所有配置的对象
     */
    getAllConfig() {
        return { ...this.configCache };
    }

    /**
     * 异步方法：获取所有配置（与上面的同步方法一致，保持接口兼容性）
     * @returns {Promise<Object>} 所有配置的对象
     */
    async getAllConfigAsync() {
        await this._ensureInitialized();
        return this.getAllConfig();
    }

    /**
     * 等待初始化完成
     * @returns {Promise<void>}
     */
    async waitForInit() {
        await this._ensureInitialized();
    }

    /**
     * 关闭数据库连接
     */
    async destroy() {
        if (this.db) {
            try {
                this.db.close();
                console.info('配置数据库已成功关闭。');
                this.db = null;
            } catch (error) {
                console.error(`关闭配置数据库失败: ${error.message}`);
            }
        }
    }

    /**
     * 获取上次选择的学科（同步方法）
     * @returns {string}
     */
    getLastSelectedSubject() {
        return this._getConfigSync('lastSelectedSubject');
    }

    /**
     * 设置上次选择的学科（异步方法）
     * @param {string} subject - 学科值
     * @returns {Promise<boolean>}
     */
    async setLastSelectedSubject(subject) {
        return await this._setConfig('lastSelectedSubject', subject);
    }

    /**
     * 获取上次选择的年级（同步方法）
     * @returns {string}
     */
    getLastSelectedStage() {
        return this._getConfigSync('lastSelectedStage');
    }

    /**
     * 设置上次选择的年级（异步方法）
     * @param {string} stage - 年级值
     * @returns {Promise<boolean>}
     */
    async setLastSelectedStage(stage) {
        return await this._setConfig('lastSelectedStage', stage);
    }

    /**
     * 获取学科和年级选择（同步方法）
     * @returns {Object}
     */
    getSubjectAndStage() {
        return {
            subject: this._getConfigSync('lastSelectedSubject'),
            stage: this._getConfigSync('lastSelectedStage')
        };
    }

    /**
     * 设置学科和年级选择（异步方法）
     * @param {string} subject - 学科值
     * @param {string} stage - 年级值
     * @returns {Promise<boolean>}
     */
    async setSubjectAndStage(subject, stage) {
        try {
            const subjectResult = await this._setConfig('lastSelectedSubject', subject);
            const stageResult = await this._setConfig('lastSelectedStage', stage);
            return subjectResult && stageResult;
        } catch (error) {
            console.error(`设置学科和年级失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 获取是否显示tooltip（同步方法）
     * @returns {boolean}
     */
    getShowTooltip() {
        const setting = this._getConfigSync('showTooltip');
        return setting === 'true'; // 将字符串转换为布尔值
    }

    /**
     * 设置是否显示tooltip（异步方法）
     * @param {boolean} show - 是否显示tooltip
     * @returns {Promise<boolean>}
     */
    async setShowTooltip(show) {
        try {
            // 将布尔值转换为字符串存储
            return await this._setConfig('showTooltip', show.toString());
        } catch (error) {
            console.error(`设置tooltip显示失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 获取保存方式设置（同步方法）
     * @returns {string} - 保存方式 ('method1' 或 'method2')
     */
    getSaveMethod() {
        return this._getConfigSync('saveMethod') || 'method3'; // 默认使用方式三
    }

    /**
     * 设置保存方式（异步方法）
     * @param {string} method - 保存方式 ('method1' 或 'method2')
     * @returns {Promise<boolean>} - 操作是否成功
     */
    async setSaveMethod(method) {
        try {
            // 验证方式是否有效
            if (!method.includes('method')) {
                console.error('无效的保存方式:', method);
                return false;
            }
            
            const result = await this._setConfig('saveMethod', method);
            
            if (result) {
                console.log(`保存方式已更新为: ${method}`);
            }
            
            return result;
        } catch (error) {
            console.error(`设置保存方式失败: ${error.message}`);
            return false;
        }
    }
}

// 创建单例实例
const configStoreInstance = new ConfigStore();

// 异步获取实例并确保初始化
async function getConfigStore() {
    await configStoreInstance._ensureInitialized();
    return configStoreInstance;
}

module.exports = configStoreInstance;
module.exports.getConfigStore = getConfigStore;
