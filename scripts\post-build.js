import fs from 'fs';
import path from 'path';

/**
 * 递归复制文件夹
 * @param {string} src 源文件夹路径
 * @param {string} dest 目标文件夹路径
 */
function copyFolderSync(src, dest) {
  if (!fs.existsSync(src)) {
    console.error(`源文件夹不存在: ${src}`);
    return;
  }

  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const files = fs.readdirSync(src);

  files.forEach(file => {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);

    if (fs.lstatSync(srcPath).isDirectory()) {
      copyFolderSync(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  });
}

/**
 * 替换文件中的文本
 * @param {string} filePath 文件路径
 * @param {string} searchText 要搜索的文本
 * @param {string} replaceText 要替换成的文本
 */
function replaceTextInFile(filePath, searchText, replaceText) {
  if (!fs.existsSync(filePath)) {
    console.error(`文件不存在: ${filePath}`);
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const updatedContent = content.replace(new RegExp(searchText, 'g'), replaceText);
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    console.log(`✅ 已替换 ${filePath} 中的 "${searchText}" 为 "${replaceText}"`);
  } catch (error) {
    console.error(`替换文件内容时出错: ${error.message}`);
  }
}

function main() {
  console.log('🚀 开始后构建处理...');

  const srcFolder = 'wps-addon-build';
  const destFolder = 'hexin-wps-addon-build';

  // 1. 复制文件夹
  console.log(`📁 复制 ${srcFolder} 到 ${destFolder}...`);
  copyFolderSync(srcFolder, destFolder);
  console.log('✅ 文件夹复制完成');

  // 2. 替换 ribbon.xml 中的文本
  const ribbonPath = path.join(destFolder, 'ribbon.xml');
  console.log(`📝 替换 ${ribbonPath} 中的文本...`);
  replaceTextInFile(ribbonPath, '万唯AI编辑', '合心AI编辑');

  console.log('🎉 后构建处理完成!');
}

// 执行主函数
main(); 