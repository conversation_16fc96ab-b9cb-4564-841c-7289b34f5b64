<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>WPS加载项配置</title>
    <link rel="icon" type="image/png" sizes="32x32"
        href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAAPnFJREFUeAHdfXmYbFV176nqvt0XZVLAIWpiRL0gaBAlahiCiHkvQc2LMaJRQTSJ+cz3mZDETPo90UAMJnny/ngKTowxIhg/YzRfFNEIKMjoFcQLqBAxGBACXBC6b3ed9xvW2mdXdXV13xHIrq6z91l7/dZvDfucqjo1dK/5b9gOOL3dff7+TevaXoN7u65tm6f32na3puntgnB3aQbtLr0+xi3GbL1mYztoNza93kbI0Lcbe23vnl6/uakd9Dag3zDz6DUbrjmud7f0/xtteo/0WPb7ZLtz++O5QwdN74i2aV+AeNahwI9DIVFYhIfqs/Uw9rBFvR12C4HHUpaeNoHhyoABiaiHBXU7FhIWRP8yyC/s9Wcvuu73evd1wEfe6BG3AFDwmebOhUMGzeCIweLgCBTmIBRmOgvtEjisDI6FZjFdziw610ZXXBXau7FwXH7aw8Lq8Fw0WliaWMDZ4vJ+r39h0x9c2Dx+7cXXvbo3T8wjpWWOHvb+7vuB+RcOeoM34NT8mkE7eKzLCbddG/ufBaRQcoTHgvHoDZnPBLEIvCKWxh4LQ0sgCl4vFtoq/BUavt2Fh5xPrOn3z17/tplLq6mH7fBhvQD2+WD71Ladfz2cfAOS/kwVNVLp0zd36ipizOLhCJVUC2JMsbQ+KDe+2CoPGYHndCwAqcqeRhBXeC0Iymv+3g04M5092w7Oueb4nW426uG3dQ4eZn6tO3X++XgcfweK/qtwUA/emXsdzUNFcYF1hEOVAak4esyv5sLA0BlAcXMiCke8hoELHtrLNo4/QLKzhL/XB7j9TL/tn3TtH85ckXYeLj39fdi0Z31g06GLzeI74dAvRVniSEs3s1jpcmqphNLljIqkvlsMQrCQqrD2dHRrP3ZhQKMleC0rTlX8rjRk6RunjU8OqVQPF5j9wppm6sT1f7jmIlp7OLTK+4fOnWee+sBL8BT7XTjqD2XynNvu6HVBqqMSrpZn9SoqfQdKBQh8JH7c2cBBR+hZtMSvkj+L7EUxwi9XYrHUfoBDzx96zUXTU9PvXv/7a75E5EPZIgsPjQvP+XD75LmFufcjK69KD3QMoSgqYyTSJYVGHGDW7RYIi5+BuJ6BJ6SyNYSnos/30KrwNF5hCp5ytlLXMoDQeLlnB4rFgqeDUqARNHL0++dPTc8cv/5tvVst3PHbzNsOZT78y+30jzbM/QEusryrbQY7M4E+elkTu5SJU18KBTfLOFxXwjl2drOUiUs7nKeq7CdGMOICn/O0Bh3a0hHLXpgRPPTCqHtZsl+j/H6OQibMV/zYuw/3d+954OwpX3lxb4Emd2SztzuQcd9TNx22OFj8AOq4X0mqEh1liMTbpSwO9pg0FZ8zUZyqKJrWTHf0l0QTogUS4ZIDtraGX96O8IsmeMiUi0CiEf5uIXtRtL3edVNT/bd+6w/WfNV2dsx2hy0AHvW3XT9/Il4n/wlyUXjrRClhWgypYLWUq4haE5DH2ih44qrCGlnhy5mDibU8R8M4H/Weq/DCkNQzMbCUvLjVdqwFPP5wmdlnHjjNNZNnqbRBoRYkVNG/b68DZ965o84GXSaKN9t+sP/H2qdsmp/7B8R/cBYzT61ko4xtSWIojLmcTVwmmwth6PQqjIxxggbccwFol0KOXTSLpCj5aELsL7VyJvBZNEFH8KFiavJUeMXT8Rte4THVa5tL+rOzr9kRzw0yKrm4PTb7nvbgUXisP3PQtntEJUDj5JfCl1M7PcijJL3BsoCXyhs3sVNO4RJ1YRSbtFTPKcfcUHd5fi0m4qTFHqPcGccvi1RwG+KHqCzqEf5ilFyISaahX/h7zZ3AHnvt29d+Lkxvl66/XazC6Alt21932tzJi23vs7h0u0emyH1sWcxoPopdICZEhY75TCpVC15Fop4NSAdZZMKddGzTfma3xstYgDEmP29CB/8QXvpJ54LlHqfIz7vZadcjzpXqSkpB8mZP2Qh/09sDjwefffbfzp3MXFJje7Tag21mn2/YLNz14FntoDmaRr3CXVwmo9t3mhi8kqJCwSX2VWMqvQjoboXnImCxApcFsD2p2krqKPEuku2ZPwsvfNGtHCCu8q34L7c9l7LSk57+0Uyxyb1hfmt0siE8obr3z933qTPHnLcd3miSf+DYZm3dR9td2vm5f4TBIxVuJCGTTKKuUB1tl/xO5nTFvhYFl0LgmdSqjbOZ/FZTtTo8R1KIWdhn8nMtqq/sq5oAGNLZSpVx/JqjahkQjZaxxC65uvixk35YW1uoXrDbHrOvvOTN+MzCNmzpwjYx+Zyz2sfN3T//+bYdPM8Gad4Z0FFMIROtNGoHG+gwIXVBuU+YEhF4jJU3TCS+S7qUadDNihgvwy9bYRc6Jfnj8GHLRyYV4milg/jrjtgqlZvLX8UkF4B3ROEbhfAZtyvXrJ355avf1rtDettgU3m9ddb2/Uj7M4NNc19EMp/hwjn9tFonuIyZPwQqB6pC2wu7lc5Rj9a4P3omoX7OKGsSQJPF4RitS2dwis9jzvvIJ4dgYU+73mwGvzwN++HwkNWh+Okk/Yz47UHGswx/r7lxpj/70qv/uHdLpbHFw8zRFhsgUEf+T+YvxuvdZ9iQQ2FhMmAX0cHVRWQCqkoZHonxQppwxNH7zFog1algHEGBiwwJXpZ/CJc7MCq/As84wqe0pUW3Gn4bkh/2yLa8aJMv+hLLMH/mwVokbW6c6s8esv7tvdsDucXdVj+75GP+3H1zn28HAxz59EOZw5CBdo2Jy6A1xyJloUbH1MyjKEwITRma8CqIGVTc2pY0rBtw2SOu4JNTboz4MsJPFmEn8We08gPaoVvzdyvdCzIXZe2/ZYF3eEqpffcWVM8YLM59/uCTW3+mMUm2oN+qBcBn++0cnvD1Gj/mw2G7GJ4jGS4kjh+MdRTBSY4lV5J8hKfvqe99zwlJPO5s3M+EcK8km/xh04qeE/ck/qpY4/jFSO5J/HSN9+CXKjbyE719pwIa5dDjPXMRM/af+lb0PvaEh9wNPXJ+bzv3j7/Bj8htRdviBXACXpsu/PjBs+DIkfQrE5/FoasZIL31zZ46KRgrIJ8pLHOgHMumeidRCYtApctx4CUuibd+zU9932xgCZ7K0KA9JVqFsdcZj+NLvHsXNc40wV/7NBq/UcxLh5cvzF/wC0+F8Ck5an6hMT9o2iOv//78WSdsxXWCLV4A//Ch+ffiQsXRDpiJg1vhtB3EjvYjWO14xXPCSXDSud+pRnYqCS1kItyHthJFHt6pRM2S3dinjENOdvwSSrcALYLMFmqfgAcHZ8wfaPBrP/klDjxkFLPRHq0lvpMDL79CsWhz0MmEByjxUsNG+4PB0Z86ee69KdvcPrK1eTBe3l0cNJ8FGJ+StmNpQW6nDApD811MUI+AFGgkjVIbUHC0WfDKAiXRqJgLoGjTKtpy/JrDRraomUbNn1iqpUebwy88PZDpsKmA0hotuxW7EX/KIwHYHcErl/aYusJn/P7Y2cuv/bPNv2y82WcAvrEzWGzPhN8ovt1mR4ccFJzkkcHANGEdBwYBc64J6sSQqjUeStwfajEvmeZkqKiQbyx/aKR98df4oBEf5Ew7b6P8wttjOgurS/kpFzoLE7apWfhjTD21DJM2w67mMM4pm+OeJbSVDwmStPj4adueedDftU+x0dVvN2sB8C1dvqsH0j3oVDqkIQTlMQ8KDrBzVC4pEjtnuAOyLSK4KFIGDQaKW7YMmvvCKGFZCmhCqMLV/IGnFeFt3iZH8LSkhRS6daKFr3zRcBQf/BRnLOl/zV8iGsXbqDJgXyN/4XNtiwHIP0dCj/d4ALVhjYpoFYPNWgB6P3/QHqxEySm65KQzKDuURaSCQ1VROGbACpoYTgMdSRvCV3qMYVm82MfzeyGN4Xd6aXYJP51SktFnLFQr/FwedhxCGaCR0JWi8dSTwlL+Ws74eTdXhQ9+2mYjRn5xVPhTBh3JhD/4zkvnTxRolRszrEKZn+TBt3G+gtf7LFvlVCaI7o4xl851IWikoJkArglu0ApeiwMCiaWgREkpE4DJ1HdShixIlZuSXGi7hXHY8eKTVsyFxYrfyed04Ef47aK20mFIEQ5BUbAqNk0y7gn8QhLMP9vOWF3sJAmfrAgAtPr4NEHTP/xbf7a6Txat6gzA08riYPABFp8kTopXbhayOMhZJinvdEpZoYjBxIqHjPvCUwc3NskUUNihPBYKJoVnWqhf8JhPC/SNcuqmbeFt3HjM5ZknbRU8cckfPPSZItrUWDvgZx+2KGcE6RMByZ/xFzztRPzUs81hvOxgzn51uan5hS0+BZ41wkfuVvtQsKoFcNv1c8cj3P3gqYIsWxYmmpIRwShgzsU8g8lmBLbMAa1ZoGQxQV3AGKV9JcwWCp67OY8h+XkTPriH8NJPOi6eELBDy8IN8XfOJTAk4XTO28IwPz1J/zL+hBFX8UsMnbJgiI1bOKdOet0GMu3Etosfpve769K54wVaYWMLE5T40e35hfnr8aGO8uldBpZFpaOOBr0KFX1lkzrWt27BcxEwSYHLBER2SqdCK4mBL/aYAtiOwguf9ip+JaryreZP2ykrPaOif7RTbC7lt4YXXvI7DrsvPK2M45ePniu8kVsxVfzDuQl7iR+Jn7rw677+9Oy+6/908kfOVzwDzC3MnwKDKL5DLY4q9XSTLQqsRHmfMi8OFqhrKhg/JMkb9dlK0J094TXPYK0mVeAyGYmXTQStfcVOO/U98JhL3bQvrZo/dHJedip+7keCxUcX06b5ww9J04fO/4zVKCIxGuJ3bjRfxU89N4xiMRE3yq/cUN40OzeD+VMStVzf2R2j8cxTN72kGSxcwCkFyQETLbe1o5l0iBI1Osik0TuONWTiOOrwWchQ5qRbYMjquRF+2HURQg59JX8cfhl+njX4N5T8cXgFsgJ/FZNMgJOhKmf0jULlggPPqF+Gfyg22ZYBbgATyL0EmaOqKhmzPThy/Tt2WvYbSBPPAL128V0yS5/RFEg1EE/tEMcSQtOrsBRGarIyvMlUOSGBZ7LkfG412/HnrNSj+MHt5BkvJtmyW8X/xKMfWoSwIXzwm9AopmAiPrDJn+UoCxMGEs8+55OfedANdnRkU1t/iQIoWo1nvl0j+5720j6MvStx4/ql1kMrvqj5VRlEQhyYHaejhaDODMdqGEgO8+EgMfwbOuLITr3RBoxb4Cfx19iEBRfItp5fDtoOqRj3UPyr4E8/rNrFxH2GCslwq+Mf4bc2A3UFPAp4Hb9EwdWfPmz9X4z/QuqyZ4ABvqVb7JEOycyiKwl0Mh0dHVNTyQ/HiE9ZjL0gzKDFVduydgcmZjl+uTHiS3LZfKQQyaBPtIXbEn5pYVJ+hP/S9kbxABd7UIOF8Ln237KR+OEHOXPLPvGUGmNnE5++BqHzGVaIB0gxFHz6EtFax+h2oG9cp6mhnvglzd/PH1xOZ1xIpUt6Sl7KKeGYAQVxOkSc8DkXxajlJuaEA2KvdZM2g8dJsfY4/gDJztby0xXGlD6lv2aXh44rFlPmKGOVHvEORPmT/6Pxax5UuJWYSL6N+dOP6WbqoKvfufT3CcafAfTjDIzBRXTYdNNNIwYUiXK6MIv9TIRyAETu09YQnqYCL6tMCNMBmRKCveTnvm/S1FijxCu5HT+DLvyRYNrKliMVBtbUgr/2Kfkz/oIPSPoKCvtEUhFDIXxKjppfdqgavnHf84GvfCr4lElZFpwr+g0Y8yPuwk8HQoZ+ode+w6jhLTSGm36WpZn/3ugl33TWQdthcjlg2ujIaHRIXwXJwrIPfXYyEvvhvPAYM6hiJ5KV/CwyA48Neu47aE3FmPOyM2Qv+IiWPPcDH3LZTHz2suv4yW//Ap+cDgBCDZbwW5tzw/wZa0yQhApaHPYzD8jx/KljWsdCD53Hpp2amn3aNX/eu1n2Y7PknSP+Jg8+1g1qkNt/qWofNtVDkmR4IxItpEAxB0OBcJo4zD0Jn2B7x2FrmoOe1G92nhGQs9ulbVpsm+/d1TanX7nQfPbbTqL8UFCgpKOOEjv2n6mS95ji3GsP6OM+1fzMY/AzZP3t6+89D7bN178/aP76i5uaO+83v70JV+Gb3bIfmX/vxRY+RwG6mABijfi3uDj/ekwMvVlkJKTZ9vng3A0w/owkUJ+GqVTGAc1VL/dIZHni0s5Tdm2as1450zxhlyWUSb3d+lMuXmg+9A1/9V7+pc90Jcf0H395qv6Tw6eaY5635PjYbj6m4e/fOWje/PFNzR33qdx0UDllHtnoX+aU+5lnxyGNKFHggSuYXu+G9e9cu45a2YaeA+in2PjpXiZChYwjJ7VVfO+ojMUpyojgKu0cpVO20zR/euj0Q1J8evZ7L5pqnryr/eO+Wik+PWfDNpK7z1695g0HTkm6ozc/u0e/+T3kygsxih9OSOb0SuKFkB4yCgWltaC8l+KHTts888AT51+YCPZDC0C/wwcjLmKsICh59cE4C66icxViAgkrqyv0SCz90INY+y948hAVxTusrZnqNceooIgs/VIinbSSSMzR/zcdNBUF2GEuDhEd9NORQ/iiFn4x6bypuOx5QOqgRDBSVVFUoxKnDQhD7MJg8IaarFSFH/HmjzBy0gQ1uSGSk1O7dARNiWQXDkFkvcBTDtmmRSo/dO2Vz55qdp11ZPYi/A+XMmFPxEPV/1hX0vKQOLygetpX+wU3lM7K/8h75p+LIyvHRcHFQUjBxyzq9Jr6o+RdpPj5VSg/VqWksbyznFxlxZhXZx75JCCV6UzIxaDVGk7RoYtuGcjGQ7V51Jpec/TPMVylRX366fgcwTHPm2qmtvMTvpVycPFN+MA3b5lb5p8p5Qb+q6iScd+yLL7mgGPP22htsP/YDRsePCR9KAtgoVk4Io0pISTgnRQqpCGW2CGlzALrpMNKMmYDj8nmY1c/xKcAuP+65043M3poD6flp+Ni7DvPts2v40zxUDa+ejn7ikUXrsoffeq8dv5TIjkLzjoJk2U3IuunPW36qLVbeZrbG/SPwMs/26ShqtGcjQwf/Vx8KjL02VMnKI1OM5jbcMeg+dJ3F5uX7D05wXf+pG0e9BP2yoPhITnSdM5QxtifOOFVxl6P7jUv27fffPpaHGElxvS717wGZ4hHrfDy9D/uGWVOD1bud1rTNI95lDO0nPY/fWvQ/GiEI/OfuVagtf8uBI9U5d/HPhjC1a424BauVxaAvMHj/84Lt8/9F9D41e3ONU6WXQNNQCKttE7XxR8OTvjOQLP/4/rNea+Z/E2mM69aaE6+CGeLgguOml+TTEvXkv/8169p9gXPcu27eJn1itP5g97ApwFwTeOjdF98y2yz184pXGrhErxOf8v5mzKJY/nteGWjxNE27335muZl+y1/ACzicxIvO3W++eE94CaOZhLP+NPhyAU9TCYvaAOGMhP4fDggBjYXHrvn7GO+gp+6V6b4e/uw5LOBLAIFEmFJhnsWXIbCEZFSifPFFe8LbwNi5Py1t7fNRTdPfih4NU7Bj1kro8AROoZfXPCEc1Lp+E/H6XNS2xsvsw7XWSjwNI8bCzOp+LR5Oq8liNOJHsfPklCeuaFtMjx5917zyzj7TGqfu26g4gtPHP8ifhVfprgxPxUssoxlYZ4pHeV3faBHe6j1Paw5mjziP1vQRJKRANZIUwovHCHDzS6EQ4nXAgm8rCgWjT4YF2SGrXR7O+HJ2nEHci3SJjv39tsiidMuJurg/uU7i81/3MuELN8ehZOQMU4wx8c9f/kjk5a+85+D5rJbbFd5GeFX0iPxzJnsQzF5fucX8HbMhCeX+BGt5iNfW1DhjHbcmX9V2qJgxg7dYfzKkYvOwhd8xe9shE3Cen4Y8BmA/2kjiubEwzIM2X6X4DSeq6sEKutkizuw9G4IT3u4X3Nb01z6g8lH6Wt/Di/ZZoimmc3jx2Juzsbl3+XabVgc/4pFwqZEwedDf7bf7L3n5KPz9MtRHEXkI1z4krOwRYuURfxSx+aJuAj1sv0m2//X6wfNzXfaDnPnOzuMxYNedsfwUyVv0GWe5SshshU+hS3O41kQ/7tKXAhq23U2HoQlEKq4ZbK4x7EJwIDmBeFxOkmdutWL5YOXTV4Aj8YTsWN5GTZNwpDsKRHL8BfltjkfT6TuxbX1ce2sK/F75PomFSJQQpvmuIMmF0eL5nray6giycAzStsxn8eQhq/04c0vmm54MWq5RsxHLvGiLQWj5QwBvZmDn7aBSf/ZG2dfqOWzAnHR5E/uYX6AmqP1+Q+WcAHocZ2ejXvfR5/M14Sgsys0iHEGS0c1Ds+B4b4crJz8xq2D5qr/mHxd4HU4CzwaZwHzRLCwwUZ/lvBzinfw/WRT23xy/dJFxkXxKcrDL/q2LyL/+Z+efPo/B2eUBTxBI87NcdEH+aIYzV/ilz9t83g8qfy1Z09eYF+6YdDccIdtK66Kh/EoYspwlxZ68jgHHnvC+ZeW1Kkd9ZRNChkBNr3e4w54f7t7X/9dS2pWZLEUGHVwy30TEmy5BjJKB9EqR4WTqMLLmjQ1+sBly5+mqbXrWlyPP4AvSuyPE5t49/SN3qgFv4vUa865ahFXHxWt57H95DcXsTjCVewT/6afL6+Ei1492DjXNud9Ew8ssl/NwHT6RqnnKRz26Y0vmGpmpsPHCl4PP3yxX/cLSzy50k72wV8sjeFXPqA/vDDCnxE8pYONm9b1cRzu41DSpaSgStcy2e5jLh2lkxRJHPiUhQmvXS8IKn7t39tm/Y8mnwV4/X6nqA95c0GmV0x6Bm1uzjh8vpv2z3hczTaPxfD3WBTUp2tsT8Jj8y89c/LRed41i80DeNU4lh9cNb/NdvHv8aimeRXeTp7U/u2mxeZ6PMFkG8JXIFmk0xlvzGU+7EMCkj/30VOkoNNfi+AZL3ov4rHASctVXq9smRG3DYdmGEySETzIJAlfqGWN6GPhrPRcYPed8J48HgqYmfRtlL/2itaprISA48zqJeHnsRjuwPvstJML5xg885/0zJxnkHOu4DNz49ApDvogDu1jQ179mT8ADY/+tXhVM6l9GG9VC8wt88Jgo1hCcjfnMCD3KD/3/VfhGSdwaoHTuIofMezDf2iztzmdOIJqAlvAFk5l0OlgmeM0byq8A+E+/7JxTnj5yIle82+4sPIdXCGc1N6IJ4OzfD5IfBrEWBFXQPNVDxfQuenHuO7wPT8X0LN44WxrN7wxxDeIJjUumtvxXwFZF/ke/Bzngiwh0rbsI3sA7IZrGa9+7mT7X//eoPnmD8PCCB7GMloVcnP56YvciQC7/DgWLY+22RvfJe3tzgCZUBU+aBUgLEgxjGTQ0i6FzFJAE4bkKGIyrsNT3TbDGDvY/+Clk58L7InLt7+BQgmvbeDH8INA/Aw8Y+GFoYux0L6LxZD8hB6NU/NKl33PwDWLxJA1c0E8mxaCh9hCykSCnPzH4rnFSvZPu3hTFRGt8m68uGDIOTVJ8ntvMr98ga2CYU7kX6LFtHsfwl0yEPdZRCDCPRWVY0bGO8bqZN76cp0Ocy5uY/GYSzmdvOC7bXPjjyefBd6EU7VfRjE5gWcHLvtCXrTgV2jB8w081zjpAjzzowbmeaOt163wgY+Lcea4MX6PkxgnsuZPme2SXklBTLvgTaXX4l3FSe0KvDt61a3QiEUTAVBgPzNOGanymjFmL6CSUfgx8NhGNfZCcvwZC2vPhwB9709B0hm5gD7GNGYpJ8oIO5Wce5XDJDCJ5R2qG7lYnG+a01a4Ovh4vMHzSl1ISasgXI4fFMlPNvr173dHgsKrlz+r3+w54Zo/rZ9xOZ+ZG899t5o/ZeitqAGHr3v+NN5Z5Gj5dpoe+zGfrkX+hEKeWTCbTQXHJYuY6KTBUeMVp/ldF+vU3lMdfzv39Y+URwBcDKxMKSoLT8Yg4Q6NcZ53OxsOY064nINeWTiJD4zt48rcDW1zy39NPgv8Fk6pfMMmsq0+/SR/pid9oo/yjZ5qvvP/WHziZ1LjZd9Lb/Z78ozKkTH8YX5yQCju5N9pTdu8fgX738R1EF1WFnYYLw7IFUfkuebnOOPKGJ0T5jl8JD5940KK+lFW8JS37S79pu9fm0xiKvCmgOrEUSRpty2FhciUJhBhElNdgaK3jxpYZKO8xnLqCtcFfgov2V7xLD8XcMA0HE5xGB7Ie/mNUe0/VaD+i0/DZV+8ITSpnfGNeD8+7Yf/Yus2Nli2zsBr8T7Gbnj1Mqmdhtf9rE/6L20WjTzpO7hdB6ZPyvYGyimvRzIoPSoYy/h5zAzhxZr+4SGg1/bxb9R9FJuGjnEkJNF2Kox7FYVR4nAjgZxKA9TNu2yBkPupK/7QCdnnvtM2t96z8lmgh4gMH8bX/AoYfA7cvMn/xhWOzh/hvYJ/uZ6vHAJPv5FI28o4OL2Ufy1OLMfgpd+k9u3bBs3FePZP+77DNscqPDPERon5ldsV+DM2IjM3xOkOO1kjzioOKnG+4RlAqKqIFmALmJxi0BLGTKebR1gSaJ+6iZM7DklgxplOhH3OEsdLrR/GkTep8fP5v7LPlINIPAykTfMj4OCnnLPZ9n9Crznopycf/Wfjdf9AX3YAOuOQ32kF9mJRcJ4qyf9qvGR97Aof+DjtEh79SpJ8s4dhE5ZUcFk0P23X+Sebi0hi8yvGsMmyyi/a4DzxVaNMeMl7ejNoIx0aUqQx3GVT7N28HKQR3qygnobV2EnOIGOM/ZyXgyIfwUP2Gbwf/qONgTN6yfZ3cIThpQsNm5+u4pb2MbDvkLHJR/oD+UpHPy/7no/LvsQYZ9vcKsk2KFtQCD3r8HIvL/xMajfcPmi+vIGL3P4X6/SPFYCYdWA8o/yuD/Sgq1iJkQ9kxBhgWhWecsqkgg0GNSZt4ceENvZBhP9AEQ6FUXkiUzYYFNLimE1GgqA2LselSZtUdE/TIbE49qifDvHK20dWeEXAx29evjWGOQg8eo6TP+dzYfwUvpX00hUu+34Sn1u8H4uAGOHhcNqR07FhHEUe/L+Oj5Ot9IGSD/HoFza2qk0UhyUL/80u8o5nGFmsqLjQAngpnlqIBVNU4w422YjpbUQm81+QkDDugbB/wwmmc06yjWeCbdyui002QIbeybKDBQ/jJdHyydyfwuf17rifzMu333mhjzSxZVDo8zbKT9+OwWP/Spd9/x5vFSuezJjc4IbJQi+547fUPk/jUeVNeMt3Uvs+rnV88ds++uv82Ta3cYOvWgg6fBnJCH/Mk4sI4bmFPK0QYxxd7hZY+u9eGCyAttkohxQcbESQNo79aHWxOFcwyksmB8qJp0P0nSLcZU9OeiwHg1O2QnkeH4o/Ha/BJ7V1e/WbF+/dLcTiC7nG8O+KTwCteNn321h4uOybeC6aunUJppSxOcnUegXe7n0CXqVMah/G0Z+HgmwTX8U/VDBmq+QqrGo/OTp+ztIO75ylHeZa+ca+ECoAR9pDh14YPATgdcLGJGMNPM7KefUkwZCTNCKnrAPrasJHYHTHjthBkrKlk3Yo8JziHTY/+c2F5r/w6eBJ7XdxxJFLgbMP5eT3vm0f/dyVP+3Ly740UuKnASU1kyWBWSgHJ288gn4LH/ea1H6AaxyfvxaLGjj5VeFJap9tgWNpQZGxKUfsNWZPvY5fPgKTjT4ZF3jNAcTX2gZzwv7zIQDvBfAzqEo8FZRQGpHIxhwqt25lVBykvEoUg4ABOUNneRty0nZKgILTOkAAPrDQa87A6XhS2+8J/eaQp8qyYByJVMQcyyi+1dvisu/kAvFl2U0/pr756flL+Uap4qCh4KEKG11Fo/8vw/sU/MDnpPYRHP38qBpb0SQVbvKb8uSCTctcJMVEVOQv8fRKtaIcO8R4eXU42WE+Ei897KJRjH/jew+eUA++6z1KORUU0uC+m417QYQiJqhLkIBWhMwWjAihncWMi25pHXRnwviP4+Ph/Mr0pMazQAbd6YX/YfDluHi04mVfXoQCldl6eK+gaf7ipdPN4U8vr5LFoyRXeeFT0d/CF08ntdvwGf9/xkfUlOMscgDSnnJSQs38V1aVZipE/jCkqODDc3cZf4XnkH5X/Nbq34RXAVPfsSnOW6yVRYrKlssSmlp1xeNwhR0BKAnIqC841cRtY5ZiTIfQaqn3jP8JzgJnr3AWOAC/M/BCva63leS3bTOtdNn3enwoRZdlYcIIHNXxXkG+rFPuMEuPmaNcuP8Tek/Fq5JJ7aP4pO8m1F+5ZR7ilgcCPRdvxZ/FiqicP2ipVfzJa7+Tw/mjVTWnufCnBzxw4NOGfq/fbKAiBS6cDXGff9k4J6dpV8UbJtAc5KMBDuHTYI0PBfMFPhYYP8FzH16WTWo8C6jRZthVQjA+bO9VfNo3X3ZW+GPj9fzzntJv9n+i05vxkYtjSn/74MlH/+24pvFpfKLIKWMcjkV4HSwpkdHO/4hf4ZAQrcsP1cwfE+oYu3yMBVJZtpqMEWc8F0Ifte/PPHrNBpcSIQEsI/Azwo6e+14YWcOwqi7x1KKjDDNtaeVzX3drFlBEWPDBTzH570XxP77Cdwp5Ze95T7J1s3T8x/385ALx9PyF8rEx2AD/YU/rDX1E/LgXdl+WUiyuYXMkniM8Ha9GJrXTcfQv4LebI0zFVOu7kCkxvwsJGXxhEV0HDZVTJTcgwlNNjZrYIZl8jDHthM/FFvQJWxys2dC/5rje3TB0OwV2KItIFKWWa6xIOjmdLIW2YnEibQ3h4QnltsUZ2veiExPm7GRZRg0/xv2T+YiAkDHtLb8QbxIlHv1+j2+a5+MIntT4JUx+RFxJoyJwb0TB6/YSXDx68m700nr0jOOVjv47cS3j/KvzvQ1jFFXlo2Nl5BEf5rLw9klZkV+Z5+QnxgdX+M9OdmKfttBsgeOoK/m9uG6/5oTe3coQ9jeUwkiVrgVUDtMUm2Uahjx4TC7D1MItxloIBZZWaYFJ6Vrhh1B4bRu9HDwXp9FJ7ReeOoVTNTSSH/0bXzBcyFG8PiKOTwmr0REEwlcWo+8V8OLRMTyTyFn7fxieHO4L3UntzEsXm3k+t8wEQblED1tR8s6E9FLDGeDkWLzkHVQj4AsK8efiSDzdT+vUB78e+r0A+v3LEpyrlAkhWKBILGXKBOWQ2XgS2zxlWonRWxoRJ17he0XSZjpLJ4WXXsd/Bo7UB/FZ/0ntd/FaPPE/hR95WOmyLz/tqzNL8QlfD4srjKM8/wsfTN1tLZPm+N+ywmP/3biGcS4/TIqb80rPHBv3S16pUfEzAO1DJi7OZTExTHvCsybUq/HB0cntrzhH+Kd6/cvoUyzj9kJThsM0nsTUEgl6irmvINxrFxs6Iscwxxt13GsygZopO7FnG/ZAKPITrb7Br2bhdDrmix7GefuL+MLnur3MesxBk7+Hx/cc/l6fGM5o8BFxnOb5uD6u8fuKR+MjZNR+0c/2mmfj1cekdg7e1XxwUxe/ixRRw0jJSzVieZ1nKhjL+Fd4P18+yRfWiHflLPBhn5l1pNAMfvh0IXFxBpi9CJ8M4ZJ1S2PsJQRK41i9shaEsKjCixjqVBXGC6I4lXjMGT6CN1D8Shj0nTj60Gs+htfqo1/0kHK1eQvOArvu1Kz4TRx+C/cOvuuYPqH/NRzlk94reC0+lziDzxK+5ZDJDy0bce3i4/E9QvqfuXEsmUc4Te7gL70i9VximUuWM/HqmUBhnRvsROGVfk76lvyBTztYWAu79WcvgqIXwHX4nji+Lni5jjjZJEN9pyoaeSpntEIhZk/HOJc60ozVSFnXumCEByRtmh92qlXMWTZ+PPsfeUFlQjsST9j+4siVP417Ji/8gKPmvxSfHJ7U9sCnk9991HRz4ApPLD+O9zHun2NEYZ/+j4ufMcoHslJHiYeq4x/CV47R51wMxDk7FT7qkHjZrvFA4IdALv/KCT1ktDwEYNDr65Qgi3ImvOaYRukwWq5IjaTH6Si+NKhkjAOnnWq+2IIl6clSsc+kEM8bm3Uwhvyj+Ai5vqOnmaUb+ngUrvxNahfhV0puwrdwxUEeuorbFT9oG35aZ1I7av/Jtvmc4hx88VXeV7GRwaUiWeRS8+bnPONjhrllzLzpj3oYKP+BKbasJZtCwoDwlAvHaIJT1kOetcZs92DWH1xI5wy0K4SrIOhoj9JsdoLy4eCEZxGpLYBRDMMjW1iCD+7kz/lceIzjtnub5rPXTX5FkP4t15+BAtGPtM/ANEb/sRW+o7CczZR/As8r7nnA9mQTRIVHSs5AvWWBkp+xZz4Tx/ghVh2zFuaDFcmBV76NTXz6RDtUc7PVfj8Odgi7BfD4tRfj2tBdVgFErNBAn7I0zp5WSew5mme02KMz6bGYubFsCC9p6AtuW7ZH+wK7zwBB+qGv47W73tkiaPPat3HZ9xu3RKIAFZds03V8RwGfS/xh+Qj55tl+AK9SzsZLP9phnLz5r4tfMcU8rTtW5IZj+eEcGO34lTPN0V5lq8JQnLaSX0YJwc1zGFGx379r72b6Ys1jUxbAda/uzePdoU8kiRwiHxo7GZEjHjtAK8iwtKgc2qFLPFsGGHtyxjhCgBEuxmQj3ubVZ4C33t3gQ5uTT9XmWLrNo58RkM/JCU7sc12dnZeGl8InSs7DZeu7fmJb9NXxisk45cPFrvk5yfh556x9kgXhhFBKOQq8cmOMlIQKvOyM4adPuOHt30+cd0KPP5KkVhYA99b0+2c78V3mCZJbMFyKBELLPVeKVRylc4SFHnoXO+xSjiB4V+Ds6QBa8nvfeqP8H/o6P7iZCONW2vLXvb6gT/tCM/kRxSj/p/GZwHtxGt+cxg+xnInLvmyKidHwTz4iR+w1tpyTip+5DV+Sj3k1jlPQZ7FpbNz7+cTTluzYwnL8tkl7/bOTi/3QAlj/tplLwXkDScVNZ4JAhQkkadlKgNxJRwmUYxRlMHQxUaEa+HTePbAZNM2QP+zV/N+7E7/otWHzzgLn4Nk5Xv67hSu0LbuQJ/8DeCJ3Lo7mzWmfvmbQ/Pj+8JU2FWvaJikIlR+NZJrSUX7mWgWlbtqJ+IWXn4IrTVkbSjLXsjnKD3uKEz8WfdUJM5fagrdDC0Cifu9s58fupHIaZp8tScObEDt8ylKV9tJZ4ju5Fxhnu2b2WiY8QLSc/B/6mr/r3+GWH/Gy7/nX4IueiQ+6sh/8Fveaj+PJHI/q1TRem/joJfzpuNQO/8s+5A5AA/mPOYqSv2RamMCnueyZtHJAGc+t84k5YW0zIUO98zd09HN+yQKYbQfn0Bydkys0jLtWEIaWYiaqaHdDVwgWWAD6q0YTRSNWYxYy8VIUANqy3fHbHBeL7XBmAz9ijZ9UXU3jp315ZU74CfyOsW3uxFWRz+HDqatpn8X7Cf95L6LQH6MJ/8mTBiAqeyP8VMk58SNY5ybQdJo6zI3G1GYsPCDcNIcd2xnBU0W4fjs7NXNOQEq3ZAFcc/xON8PQZ7JAqcl93tSiQDnHXnOQyz05C91Qp6PCC+exnRWQcAp9twWNA66FZH8s4ZhB8yywUuMRyoszwo/hD9EwP2yfgZeE5lyegdck+HEvtdr/iL/YhkKXH1I5T8YZzvjFB6wKmskr04zdRbdfWX6JhXV9Ai9/Asyu137m0hOG/1sIxUsWgIRt7yQ6IUcZDBq3pWgpiAiLRmAopi4dSozwtKU5GkAbxUObCgxVScC8eyp3/OZrmmv1NavJi4BHMn8ZRPjg9x7pndCYRRf8cIA/2XbRCmeYz+PKZPeyEVjZZyFpyrYyfsU9jj+DUaawQzDx9IXjClNspacioi61fSv44Jcdzk/1T5LiyGbsArj2D2euAPUXkrBbcQyO3tE5WqL3sVC4izljWMTQkzgxw3IFB0dLIRKPvvAQj1uxW/gtOzWPQPKPNL5SOJNfN6vtVra0uJRg+MdQ0ufgPx3XHJZrvBbho59AJUM8WXhyhlHzR5x1LOInMXXZVfzGpwXb4rxyFT675D7Q6KdyxjnaEX/imy9cfcLS/xhGzNgFwIk1zdSJ7E3IEVsuCY7ro8d6ogN/OiZXwhEihvCQc14xq2MSJEEHC7xTvhyeWDT+xMp5eII3rp2N4t+UP0Ej05X/zClb6TmI+eC/Cj8u8U/LvAvJ4t+CVyOlyc+0z96x1P6njJihsEJQUMFPfxJPa0N4YkzhTvkMgSRGEN/vuZaEjLZEjMq1/6z/88BX8XTwUO0oVifJWSMU+/hjsRiQJSlzkHI6VK2EHSlHL+NUCDznqjYUNOa8T50wSl7wvxnv5fMt28fjK2B8zc+3e7kAav48+pgUYuSHuJbnn4Lebx8y1bwKtvfCT2n8EBeizsLzg3Ov4JNEHpHm10KSSxBU7oFInlI2jt+q5pcrI/EzTpnlZMSfdpbllyHqA93vXXT1X649LEWjfcU8OtU0z/m/m16ysLB4QV0wukOnS4tkdit1xKSUHaY8YjjDBhTisngSOctVX9gl8wK0URaMr/eLFxJzQ0n0kqUNL6rkZ8GUuJwmKmLkl0D0+f4aX/smjJlr/rpg8qN2p/gURsfxQ0d5Dz8ESf+W4ee0fOhPHXn1e9Z8KdVH+2UfAqi4/vfXfAkfSDjfSbHXDgaTJOYdNEoQ6SIGJRNzTqp1Upc9Y7Ryhbcw8kN54MmBOdsiB+c4jV7jYX5fMDOWGN7SRxunPRmgEc+xD35NCdXZyAWmr42P4U8/aNWmOyz5s3icVRxUkh3zy49J/IHr7JDJHGKMPFCa/LaPjyVOKD71Jy4AKkzNzB4PYr13zP0SBMOC0/Q7HcvTqnpJ6Y5cElIbALKYxEkjZNpDMCGVfeGzZjbg5IkbmiP8tC2/aIU6Yqi8IKAkzLPahkx4oSBNPOtUWhaRxNThRGdTBQ9cwRcsIV38xAle4yN+zlBXtms85CV/nJcBbryYkh/n/vtmpmePr6BjhysugPVv690Kynenq0yeHYsVCN5CSgo6BR3e2OQskyu5BNjYYQdinWI/AjTOtrmNSKnccZAHu+KifWqhJ1YyzkkhMVDgfvAH0jqQ0QT9ENqGbQdb+yeD4pAd8tAc5gmmB8JTTpnUqRA5k83KlrUwTR0gR/khDSOFn/Eo/2GLHNIJjeSHzXdfdkLvVs5OaisuAIL3PHD2FPy+/HVOsc05XPoeAaFX4umYnLG2FwtEECee/SjeKbMNYjQPxdQzq7c1nsmTTsVvtNlqfiiqxYzzRjwzz2KEjYKHeJi/IKHvMXEr8cOsucQjF4yXfDx/p8UFma3yptji7Ij/vf51u6+ZPSVRk/pVLYCvvLi3MNX23woqXSJmotiUqHQEfd4UbeiwQFraTLBC8REqvLCZ1EBTlhkTDTchkzyPcPPbNimS3clwxgGt+G13DD9dzBvsaCHQV/2N8Me8/Kdfups/rThS4kjfFTj53XcYuRi2GEfmN/npG52RHPPCw7zlnOvGeMMXiv234iNf418bS7vbrGoBUP1bf7zmq3DufYZGklQQB5lB2Xm4JkeDqHI23aUe7wpNdrqEMZwsbFiwvQhZ88TjVvCyoTQBQinuNqk+k8UZ2SY+MWGLU/Yp/KcqhWyMp+zRtvk5NRSLfCKbkdrSjxofRUx+OljHIrT0O0aO0qalHb98gT/i6rXvu/qkNV+lX6tpq14ANLbXgTPvBMklJNQRByeVMPbB5sLTOTbrKUBgiHMLPFxW4LIjddmlvgwoKDCid7ICTzkwvC3hZxoqHic+8LRDByo8ierFyrG06ILshD2Nw6/AqCBhy3G5TMYFHvbIsc3ez3cAil0LoebvNZfsNj37zvRlNT2926y2/9+1T2kHD14N8j2UfCY8m5KEHQYtRznBgdduKRadxo2J9gz10BIPaWmSBT70E5f2RFHjxW/e5Jc98rLAy/BbTk3yBz74Ler8lk7YS377FfjEpR2KQ588yVHHb3nJlnWoq0UUeDiiBUqHwh4pYOdOfHD9uZef1PsBNFfdNusMQKvX/lHvByA7Fk75+YACZEDZsnidTOmks3RewVu37FPmP0wEvtZjrLh1+iN4+SATHd4q2soi7UXxcyrtyafibvKnFnqK5E/4T1MSe98FoQ7u6T+HdRvhN0vgORf89GlsG8NPLvpOdG+qPXZzi0+ezV4ABF379rWfA+ffcJyBcOyVSncYTYQSuz61WpqJF4QbWtEfrRVA2LY5YaiJIuZRHEjjY67ga/6cg2nakS35KFprTuCnS8SojfBTlnP0i7F7kYc+sNLRnEbS94Ku5ir+ITxVZGMpP02y9fu9v7nyPazJ5rctWgCk+Y0/mv1zBH6unYOACS2Fh2dMRPjjvHDPEgaoZBXJCL4k0gYYp5Ii+x5n0kMkvsKvzFT8id9Cfnoha/Q7bCV/CRJz8hHcji2jJzr8h3LivfQ8RxvE2uPAM7AhE1IaxkMEunNfMTXz52Fpszv6s8WN/4X6+pvnuPKOpL9uHDg9pdD0HbcuafZc8wJVbsgQk+CkECPTkPsI62wVvJSDXtrD/Pat40+/Cl5pDXzwS0TOlfjlOp0d5tcebRXfxvNTz94GfoSfs24jvlh+wdPXzB5Vf8o3lFfdpfVVA0YVD/5ou8s9d859GQl4Xp1Y6yE05iYSqdIxJ3KeGQv6kqhMGOfYQrPGd1KNqKUj30SWKYmcGObXETaOPxbNsJ3AoxuNazn/S5yFHx7AL8odK3ry177SPm60mTzyg/RoKY9EWui4rnzULrMvvuRP83ceY2ozuy1+CEieS97Mnxqb/RU8I7xRTmKCAeqOsYNPbacu90rPdRBJ8pKgnnULXhMVnjllKz0HMb8Mf0HDVsJkQ3YqfGEf9r/gSTtqAALO2030LDL20n/vaVaUgseuusBrUlaMGMLLpvJ7407Ts7+ytcUnV+eRmbd4+9y/bX9m02D+i+1g8IzayHDS8jToZKsMGsIN9HQmj9LYQQepJzTHfSalO4rIRoVQG6nMOP484sRGXia2uIRBGdNoWJB46ZFqVfPLiRF+ehXuw27gQZBSqi/hlyFsZDw0OSau17+xaWdeevVJvVtSbWv6rT4DJPnVf9y7pd+bOQRfM7+SLudN85EUpanbYEo7sWVZIjFKOizEis/DzdqRUiWkwoNDR4swye75+igiGWfdupGyLT+pEHj24BnCy+sxeGJ5H+GnpuMqlCvzyz9wy5b5aQf7V+40PXPItio+PdpmC4DG1r+9d/uuvZkXY3iBwq6S4iQiDCUZGjmHYDmnUqionqNMd6SPJRvCMxuSxxzHkSxNKeWBp23MDeEFB4Y+MNnZe0/7NT99S7x6kggT+JX4A9/ZkQPF5jh+ysSFXrlBTvmY/7UTercTva3aNl0AdIqPS/s+bfYovB99rlcwE6w0h8+RcBSF86xbJiYLJTmlnMeNzVsOAi+JZ7WNIjJpRmGbeNaptCimiKnDic6mjtbAFXzBkr5bDMTZrwo/wi/bNR6IXEy0ZQO04sWU/LIt5xTHuXy2vy0e8ytXNNzmC4BWz8MXTV/1JzO/CdffhyeHiMzBiZGFYmDomQjuMuioREmwZJyTQmJggfvKGntpWQcy55OEuNlwanBW+sQr8WFX5qBFMDW4lV+U8U8Kw5hiK6zTppAwIDzlNtxxyrrlNT85RCRkx6/Y8G1dTL7vFdMzv7k1L/XIsFxjzNu17f/XDx6FbJyJ0PYQERLjBChExc79LmFOwpBTkWDn0Ee486at7BU8RGk/cg5T1ItQg9/1CTzmCl6aw2nhnIpOcfqCocfmK/jgF0YLneyBTwzlOdZAhu0m9mOB3dmf6h27pVf4ZHYVm+FIVwHYEpWD8AbSA3MP/gOwB7sQLkiEnaUpCegG0GDClUgXS/iQCc/pEaeyGHk0KqEqHBSLrUgzRYGXWYxZMDbhISxHbEjTp+S3rr22rMKX4vPoJz005EvHX/ARCw78S9a0a1+7Jdf25eJmbLbLQ8Ao/+V4A2nPF609HC9hTkYCHCaS4COD+YBISWESMVZJmKiwJART66RlEbPQ7I2zLWop0WFJVnTU0Qabi1BjaIOzlAmvPXsiP2p8FDH5jQq87IzhD6uiL0wRIP03jkZO3n167eE7oviRCbu0o7bP/utNhzWDxQ+Abz9yKnAkNBJgN5CMLHLpczFE8n0kdQVLO8UmMllktFeay8wis6VOzT8kCz4vAiK8eDaXnx6YseOXVyw842+a63rt1Fs358Mc9GZrm33aWiubiT/8y+30XZfOHT8YNP8bse+s5NJGFiqTrpR1qeO8ksUetzxak95F5J6TnMVSL1GHy7ksJFG2F3j5UtmRQsefHLmQPK2SyjdaSw4t4sSH3znX6/fvwz9wfM+u07PvX+3HuGhqW7WHZAGk8885uX3yYGHuFJTy15mQchRGseokc66cDWAgEy957A8tiFq/XlAqLE2BBCaJYdOCwpzkFEivWgC1vdQveoGHrfRryQJIe+qTv/epmTUzf7CaT++Sanu0h3QBZEDPOWnTS5re4ruQm0PLWSBSmYXPNZEOU+4ixpGWtXJVmWFYYB09X6YhS7mKPlQ0AbQkSiGJL7bssW252MUPTmktYUkJQi3KzO8db/Hzwxfhid67r37PTst+Y6fW357j8HJ7Uqze9nP+atOh7WDxncj3L2XhmEAW2vssXpwpIGNTAVxSF6AUKxNf9VGYDhN42gKPNMMu97VU2Mc8+ZK/WxSxEFx9zVOv4GM5hXNf4Bc1r/rLNRdJ52GweVgtgMzHc0+cf/6g174DX+/+VTiIh0jOONEaQZCLwgFUYWThKKIeboYbw518qNBCEBQbKZlHhTdRFN/yPCsYN7woqaE2wo+r7XTiM72p3knLfUU7oQ9FX2XuoaCfzHnAe9untu3869vF9hhoPkPHKArlQrBi6X6MmfzqaC2aXAiQs3BsS/CBK2eG0JNuwZELeFFwwdCW+YcXlOfAdyPmz+LPsoz7ZQ7afjg0R/Bw8GQFHw48cf6F+HG4N7SD9jVI7mNLsYRzYVTaqA0DY4lcKMxoYVAWZwUUULJKR/jACW8D1IhFQ3PdYkqbYtJ66N/V7+O3FvFTbKO/xiUjD8PNI2YBZO74MbQNGx48BKfWI5p2gHvvIFTH/9clq6ZiAKE+TtwYLyk+imnVKg06spONfXe+4GLiXtrBmlrAgrgcBb+QP7/KX+DcXtfsa4+25biKfFua3XG2Dv9/7c733D13aLvYHIF/+/wCfFh9HYr6uBJYVfhYEaVT9WMJ5JnCDyEse9ey4Ohvx7+K24Aj/zIU/kL+5Hr+6nan/cga1XE+sjyf4O0B7293H2zctA5Pv/bBv0Zah+/K7Y3i7Y7C7YInljvj+jd+R6TdBcczeh7TA/4D7Y3toNmIj1jfh+Jiv70ba+O7+IL8hqnp5jv8B0v8HzsTaB+RU/8fkqGiqm2YS3gAAAAASUVORK5CYII=">
    <style>
        body {
            margin: 30px;
        }

        .addonList {
            max-width: 80%;
            flex-direction: column;
            padding: 18px;
            border-radius: 4px;
            border: 1px solid silver;
        }

        .addonItem {
            font-size: 16px;
            line-height: 36px;
            margin-bottom: 4px;
        }

        .addonItem:hover {
            border-radius: 2px;
            border: 1px dashed silver;
        }

        .addonItemName1 {
            display: inline-block;
            width: 15%;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
        }

        .addonItemName2 {
            display: inline-block;
            width: 10%;
            vertical-align: middle;
            text-align: left;
            word-wrap: break-word;
        }

        .addonItemName3 {
            display: inline-block;
            width: 10%;
            vertical-align: middle;
            text-align: left;
            word-wrap: break-word;
        }

        .addonItemName4 {
            display: inline-block;
            width: 25%;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
        }

        .addonItemName5 {
            display: inline-block;
            width: 10%;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
        }

        .addonItemName6 {
            display: inline-block;
            width: 5%;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
        }

        .addonItemButton {
            padding: 4px 8px;
            background-color: #417ff9;
            display: inline-block;
            cursor: pointer;
            box-sizing: border-box;
            border-radius: 4px;
            text-align: center;
            color: #fff;
        }

        .addonItemButton:hover {
            background-color: #5696ff;
        }

        .addonItemTitle {
            padding: 0px;
            border-bottom: 1px solid silver;
        }

        .addonItemTitle:hover {
            border-radius: 0px;
            border: 0px;
        }

        .ClearAll {
            max-width: 80%;
            margin-top: 20px;
            font-size: 16px;
            line-height: 36px;
            text-align: center;
            cursor: pointer;
            border: 1px dashed silver;
            padding: 0px 18px;
        }

        .ClearAll:hover {
            border-radius: 2px;
            background-color: silver;
        }

        .divTitle {
            font-size: 30px;
            font-weight: bolder;
            margin-bottom: 20px;
        }
    </style>
    <script>
        function getHttpObj() {
            var httpobj = null;
            if (IEVersion() < 10) {
                try {
                    httpobj = new XDomainRequest();
                } catch (e1) {
                    httpobj = new createXHR();
                }
            } else {
                httpobj = new createXHR();
            }
            return httpobj;
        }
        //兼容IE低版本的创建xmlhttprequest对象的方法
        function createXHR() {
            if (typeof XMLHttpRequest != 'undefined') { //兼容高版本浏览器
                return new XMLHttpRequest();
            } else if (typeof ActiveXObject != 'undefined') { //IE6 采用 ActiveXObject， 兼容IE6
                var versions = [ //由于MSXML库有3个版本，因此都要考虑
                    'MSXML2.XMLHttp.6.0',
                    'MSXML2.XMLHttp.3.0',
                    'MSXML2.XMLHttp'
                ];

                for (var i = 0; i < versions.length; i++) {
                    try {
                        return new ActiveXObject(versions[i]);
                    } catch (e) {
                        //跳过
                    }
                }
            } else {
                throw new Error('您的浏览器不支持XHR对象');
            }
        }

        var fromCharCode = String.fromCharCode;
        // encoder stuff
        var cb_utob = function (c) {
            if (c.length < 2) {
                var cc = c.charCodeAt(0);
                return cc < 0x80 ? c :
                    cc < 0x800 ? (fromCharCode(0xc0 | (cc >>> 6)) +
                        fromCharCode(0x80 | (cc & 0x3f))) :
                        (fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +
                            fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
                            fromCharCode(0x80 | (cc & 0x3f)));
            } else {
                var cc = 0x10000 +
                    (c.charCodeAt(0) - 0xD800) * 0x400 +
                    (c.charCodeAt(1) - 0xDC00);
                return (fromCharCode(0xf0 | ((cc >>> 18) & 0x07)) +
                    fromCharCode(0x80 | ((cc >>> 12) & 0x3f)) +
                    fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +
                    fromCharCode(0x80 | (cc & 0x3f)));
            }
        };
        var re_utob = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
        var utob = function (u) {
            return u.replace(re_utob, cb_utob);
        };
        var _encode = function (u) {
            var isUint8Array = Object.prototype.toString.call(u) === '[object Uint8Array]';
            if (isUint8Array)
                return u.toString('base64')
            else
                return btoa(utob(String(u)));
        }

        if (typeof btoa !== 'function') btoa = func_btoa;

        function func_btoa(input) {
            var str = String(input);
            var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
            for (
                // initialize result and counter
                var block, charCode, idx = 0, map = chars, output = '';
                // if the next str index does not exist:
                //   change the mapping table to "="
                //   check if d has no fractional digits
                str.charAt(idx | 0) || (map = '=', idx % 1);
                // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8
                output += map.charAt(63 & block >> 8 - idx % 1 * 8)
            ) {
                charCode = str.charCodeAt(idx += 3 / 4);
                if (charCode > 0xFF) {
                    throw new InvalidCharacterError("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");
                }
                block = block << 8 | charCode;
            }
            return output;
        }

        function encode(u, urisafe) {
            return !urisafe ?
                _encode(u) :
                _encode(String(u)).replace(/[+\/]/g, function (m0) {
                    return m0 == '+' ? '-' : '_';
                }).replace(/=/g, '');
        }

        function IEVersion() {
            var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串  
            var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器  
            var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器  
            var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
            if (isIE) {
                var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                reIE.test(userAgent);
                var fIEVersion = parseFloat(RegExp["$1"]);
                if (fIEVersion == 7) {
                    return 7;
                } else if (fIEVersion == 8) {
                    return 8;
                } else if (fIEVersion == 9) {
                    return 9;
                } else if (fIEVersion == 10) {
                    return 10;
                } else {
                    return 6; //IE版本<=7
                }
            } else if (isEdge) {
                return 20; //edge
            } else if (isIE11) {
                return 11; //IE11  
            } else {
                return 30; //不是ie浏览器
            }
        }

        /**
         * 生成guid的接口
         * @returns guid
         */
        function guid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        /**
         * 自定义协议启动服务端
         * 默认不带参数serverId，linux未升级之前不要使用多用户
         */
        function InitWpsCloudSvr() {
            if (serverId == undefined)
                window.location.href = "ksoWPSCloudSvr://start=RelayHttpServer"//是否启动wps弹框
            else
                window.location.href = "ksoWPSCloudSvr://start=RelayHttpServer" + "&serverId=" + serverId //是否启动wps弹框
        }

        var serverId = undefined;

        /**
         * 获取serverId的接口
         * @returns serverId
         */
        function getServerId() {
            if (window.localStorage) {
                if (localStorage.getItem("serverId")) {
                    //
                }
                else {
                    localStorage.setItem("serverId", guid());
                }
                return localStorage.getItem("serverId");
            }
            else {
                return guid();
            }
        }

        function startWps(req, t, callback) {
            function startWpsInnder(reqInner, tryCount, bPop) {
                if (tryCount < 1) {
                    if (callback)
                        callback({
                            status: 2,
                            message: "请允许浏览器打开WPS Office"
                        });
                    return;
                }
                var bRetry = true;
                var xmlReq = getHttpObj();
                //WPS客户端提供的接收参数的本地服务，HTTP服务端口为58890，HTTPS服务端口为58891
                //这俩配置，取一即可，不可同时启用
                xmlReq.open(reqInner.type, reqInner.url);
                xmlReq.onload = function (res) {
                    if (res.target.status != 200) {
                        var responseStr = IEVersion() < 10 ? xmlReq.responseText : res.target.response;
                        var errorMessage = JSON.parse(responseStr)
                        if (errorMessage.data == "Subserver not available." && tryCount == 4 && bPop) {
                            InitWpsCloudSvr();
                            setTimeout(function () {
                                if (bRetry) {
                                    bRetry = false;
                                    startWpsInnder(reqInner, --tryCount, false);
                                }
                            }, 3000);
                        }
                    }
                    if (callback)
                        callback({
                            status: 0,
                            res: res
                        });
                }
                xmlReq.ontimeout = xmlReq.onerror = function (res) {
                    xmlReq.bTimeout = true;
                    if (bPop) { //打开wps并传参
                        InitWpsCloudSvr()
                    }
                    setTimeout(function () {
                        if (bRetry) {
                            bRetry = false;
                            startWpsInnder(reqInner, --tryCount, false);
                        }
                    }, 1000);
                }
                if (IEVersion() < 10) {
                    xmlReq.onreadystatechange = function () {
                        if (xmlReq.readyState != 4)
                            return;
                        if (xmlReq.bTimeout) {
                            return;
                        }
                        if (xmlReq.status === 200)
                            xmlReq.onload();
                        else
                            xmlReq.onerror();
                    }
                }
                xmlReq.timeout = 3000;
                xmlReq.send(t)
            }
            startWpsInnder(req, 4, true);
            return;
        }

        function CheckPlugin(element) {
            var id = GetAddonId(element);
            var ele = document.getElementById(id + "_status");
            var xmlReq = getHttpObj();
            var offline = element.online === "false";
            var url = offline ? element.url : element.url + "ribbon.xml";
            xmlReq.open("POST", "http://localhost:58890/redirect/runParams");
            xmlReq.onload = function (res) {
                if ((offline && res.target.response.startsWith("7z"))
                    || !offline && res.target.response.startsWith("<customUI")) {
                    ele.style.color = "green";
                    ele.style.textAlign = "center";
                    ele.innerHTML = "正常";
                } else {
                    ele.style.color = "white";
                    ele.style.backgroundColor = "gray";
                    ele.style.textAlign = "center";
                    ele.innerHTML = "无效";
                    ele.title = offline ? ("不是有效的7z格式" + url) : ("不是有效的ribbon.xml，" + url);
                }
            }
            xmlReq.onerror = function (res) {
                xmlReq.bTimeout = true;
                ele.style.color = "white";
                ele.style.backgroundColor = "gray";
                ele.style.textAlign = "center";
                ele.innerHTML = "无效";
                ele.title = "网页路径不可访问，如果是跨域问题，不影响使用：" + url;
            }
            xmlReq.ontimeout = function (res) {
                xmlReq.bTimeout = true;
                ele.style.color = "white";
                ele.style.backgroundColor = "gray";
                ele.style.textAlign = "center";
                ele.innerHTML = "异常";
                ele.title = "访问超时，" + url;
            }
            if (IEVersion() < 10) {
                xmlReq.onreadystatechange = function () {
                    if (xmlReq.readyState != 4)
                        return;
                    if (xmlReq.bTimeout) {
                        return;
                    }
                    if (xmlReq.status === 200)
                        xmlReq.onload();
                    else
                        xmlReq.onerror();
                }
            }
            xmlReq.timeout = 5000;
            var data = {
                method: "get",
                url: url,
                data: ""
            }
            var sendData = FormatSendData(data)
            xmlReq.send(sendData);
        }

        function GetAddonId(element) {
            return element.name + "/" + element.addonType;
        }

        function UpdateElement(element, cmd) {
            if (typeof element.name === 'undefined')
                return
            var id = GetAddonId(element);
            var addonList = document.getElementById("addonList");
            //var param = JSON.stringify(element).replace(/"/g, "\'");
            var buttonLabel = cmd === 'enable' ? "安装" : "卸载";
            var des = "文字";
            if (element.addonType == "et")
                des = "电子表格";
            else if (element.addonType == "wpp")
                des = "演示";
            var loadType = "在线";
            if (element.online == "false")
                loadType = "离线";
            var old = document.getElementById(id);
            if (old !== null) {
                var oldOnline = old.wpsaddon.online === "false";
                var newOnline = element.online === "false";
                if (cmd === 'disable'
                    && (oldOnline !== newOnline
                        || old.wpsaddon.url !== element.url
                        || (oldOnline && old.wpsaddon.version !== element.version))) {
                    buttonLabel = "更新/卸载";
                    cmd = "choose";
                }
                old.wpsaddoncmd = cmd;
                document.getElementById(id + '_button').innerHTML = buttonLabel;
                document.getElementById(id + '_domain').innerHTML = element.customDomain;
            } else {
                var ele = document.createElement("div");
                ele.className = "addonItem";
                ele.id = id;
                ele.wpsaddon = element;
                ele.wpsaddoncmd = cmd;
                ele.innerHTML =
                    '<div class="addonItemName1">' + element.name + '</div>\n' +
                    '<div class="addonItemName2">' + des + '</div>\n' +
                    '<div class="addonItemName3">' + loadType + '</div>\n' +
                    '<div class="addonItemName4">' + element.url + '</div>\n' +
                    '<div class="addonItemName1" id="' + id + '_domain' + '">' + element.customDomain + '</div>\n' +
                    '<div class="addonItemName5"><div class="addonItemButton" id="' + id + '_button' + '" onclick="WpsAddonHandle(\'' + id + '\')">' + buttonLabel + '</div></div>\n' +
                    '<div class="addonItemName6" id="' + id + '_status' + '">验证中...</div>\n';
                addonList.appendChild(ele);
                CheckPlugin(element);
            }
        }

        function WpsAddonHandle(id) {
            var ele = document.getElementById(id);
            var element = ele.wpsaddon;
            var cmd = ele.wpsaddoncmd;
            WpsAddonHandleEx(element, cmd)
        }

        function WpsAddonHandleEx(element, cmd) {
            if (cmd === "choose") {
                if (confirm("点击确定将更新 WPS 加载项，或点击取消完成卸载")) {
                    cmd = "enable";
                } else {
                    cmd = "disable";
                }
            }
            var data = FormartData(element, cmd);
            var req = { url: "http://localhost:58890/deployaddons/runParams", type: "POST" };
            startWps(req, data, function (res) {
                if (res.status == 0) {
                    if (cmd === "disableall") {
                        window.location.reload();
                    } else {
                        if (res.res.target.response == "OK"
                            || (res.res.target.response == "" && res.res.target.status == 200)) {
                            var newCmd = 'disable';
                            if (cmd === 'disable')
                                newCmd = 'enable';
                            UpdateElement(element, newCmd)
                            alert("配置成功！");
                        }
                        else {
                            alert("配置失败！");
                        }
                    }
                } else {
                    alert(res.message);
                }
            });
        }

        function FormartData(element, cmd) {
            var data = {
                "cmd": cmd, //"enable", 启用， "disable", 禁用, "disableall", 禁用所有
                "name": element.name,
                "url": element.url,
                "addonType": element.addonType,
                "online": element.online,
                "version": element.version,
                "customDomain": element.customDomain
            }
            return FormatSendData(data);
        }

        function FormatSendData(data) {
            var strData = JSON.stringify(data);
            if (IEVersion() < 10)
                eval("strData = '" + JSON.stringify(strData) + "';");

            if (serverVersion >= "1.0.2" && serverId != undefined) {
                var base64Data = encode(strData);
                return JSON.stringify({
                    serverId: serverId,
                    data: base64Data
                })
            }
            else {
                return encode(strData);
            }
        }

        function LoadLocalAddons() {
            var baseData
            if (serverVersion >= "1.0.2" && serverId != undefined)
                baseData = JSON.stringify({ serverId: serverId });
            var req = { url: "http://127.0.0.1:58890/publishlist", type: "POST" };
            startWps(req, baseData, function (res) {
                if (res.status == 0) {
                    var addonList = document.getElementById("addonList");
                    var curList = JSON.parse(res.res.target.response);
                    curList.forEach(function (element) {
                        if (element.enable === "false")
                            return;
                        UpdateElement(element, 'disable')
                    });
                } else {
                    alert(res.message);
                }
            });
        }

        function LoadPublishAddons() {
            var addonList = document.getElementById("addonList");
            var curList = [{"name":"wps-analysis-addon","addonType":"wps","online":"false","multiUser":"false","url":"https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/exe/wps-addon/wps-analysis-addon.7z","version":"1.0.0"}];
            curList.forEach(function (element) {
                var param = JSON.stringify(element).replace("\"", "\'");
                UpdateElement(element, 'enable')
            });
        }

        var serverVersion = "wait";
        function InitSdk() {
            var req = { url: "http://127.0.0.1:58890/version", type: "POST" };
            startWps(
                req,
                JSON.stringify({ serverId: serverId }),
                function (res) {
                    if (res.status !== 0) {
                        return;
                    }
                    if (serverVersion == "wait") {
                        serverVersion = res.res.target.response;
                        LoadPublishAddons();
                        LoadLocalAddons();
                    }
                },
            );
        }

        function LoadAddons() {
            var addonList = document.getElementById("addonList");
            addonList.style.maxWidth = 800 * window.devicePixelRatio + "px";
            var ClearAll = document.getElementById("ClearAll");
            ClearAll.style.maxWidth = 800 * window.devicePixelRatio + "px";
            InitSdk();
        }

        function ClearAll() {
            if (confirm('确定要禁用所有WPS加载项吗？')) {
                var element = {};
                WpsAddonHandleEx(element, 'disableall');
            }
        }
    </script>
</head>

<body onload="LoadAddons()">
    <div class="divTitle">WPS加载项配置</div>
    <div class="addonList" id="addonList">
        <div class="addonItem addonItemTitle">
            <div class="addonItemName1">加载项名称</div>
            <div class="addonItemName2">类型</div>
            <div class="addonItemName3">加载方式</div>
            <div class="addonItemName4">URL</div>
            <div class="addonItemName1">自定义域名</div>
            <div class="addonItemName5">管理</div>
            <div class="addonItemName6">状态</div>
        </div>
    </div>
    <div class="ClearAll" onclick="ClearAll()" id="ClearAll">禁用所有 WPS 加载项</div>
</body>

</html>