const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Login-MtSME_hR.js","./Login-CkUyBrEg.css","./TaskPane-BTyRbBBz.js","./TaskPane-C064YbTk.css","./Dialog-BGVI8nNN.js","./Dialog-BUGQnbU7.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Sr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const oe={},Xt=[],lt=()=>{},hl=()=>!1,fs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),vr=e=>e.startsWith("onUpdate:"),we=Object.assign,_r=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},pl=Object.prototype.hasOwnProperty,te=(e,t)=>pl.call(e,t),B=Array.isArray,Yt=e=>Mn(e)==="[object Map]",cn=e=>Mn(e)==="[object Set]",Kr=e=>Mn(e)==="[object Date]",W=e=>typeof e=="function",he=e=>typeof e=="string",at=e=>typeof e=="symbol",ie=e=>e!==null&&typeof e=="object",fi=e=>(ie(e)||W(e))&&W(e.then)&&W(e.catch),di=Object.prototype.toString,Mn=e=>di.call(e),gl=e=>Mn(e).slice(8,-1),hi=e=>Mn(e)==="[object Object]",Er=e=>he(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,wn=Sr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ds=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ml=/-(\w)/g,ze=ds(e=>e.replace(ml,(t,n)=>n?n.toUpperCase():"")),yl=/\B([A-Z])/g,Ot=ds(e=>e.replace(yl,"-$1").toLowerCase()),hs=ds(e=>e.charAt(0).toUpperCase()+e.slice(1)),ks=ds(e=>e?`on${hs(e)}`:""),xt=(e,t)=>!Object.is(e,t),zn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},pi=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ts=e=>{const t=parseFloat(e);return isNaN(t)?e:t},wl=e=>{const t=he(e)?Number(e):NaN;return isNaN(t)?e:t};let zr;const ps=()=>zr||(zr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function gs(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=he(s)?_l(s):gs(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(he(e)||ie(e))return e}const bl=/;(?![^(]*\))/g,Sl=/:([^]+)/,vl=/\/\*[^]*?\*\//g;function _l(e){const t={};return e.replace(vl,"").split(bl).forEach(n=>{if(n){const s=n.split(Sl);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function ln(e){let t="";if(he(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=ln(e[n]);s&&(t+=s+" ")}else if(ie(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const El="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Al=Sr(El);function gi(e){return!!e||e===""}function Cl(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Vt(e[s],t[s]);return n}function Vt(e,t){if(e===t)return!0;let n=Kr(e),s=Kr(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=at(e),s=at(t),n||s)return e===t;if(n=B(e),s=B(t),n||s)return n&&s?Cl(e,t):!1;if(n=ie(e),s=ie(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const c=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(c&&!l||!c&&l||!Vt(e[i],t[i]))return!1}}return String(e)===String(t)}function Ar(e,t){return e.findIndex(n=>Vt(n,t))}const mi=e=>!!(e&&e.__v_isRef===!0),bt=e=>he(e)?e:e==null?"":B(e)||ie(e)&&(e.toString===di||!W(e.toString))?mi(e)?bt(e.value):JSON.stringify(e,yi,2):String(e),yi=(e,t)=>mi(t)?yi(e,t.value):Yt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Ns(s,o)+" =>"]=r,n),{})}:cn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Ns(n))}:at(t)?Ns(t):ie(t)&&!B(t)&&!hi(t)?String(t):t,Ns=(e,t="")=>{var n;return at(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Be;class Rl{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Be,!t&&Be&&(this.index=(Be.scopes||(Be.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Be;try{return Be=this,t()}finally{Be=n}}}on(){Be=this}off(){Be=this.parent}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Tl(){return Be}let ae;const Ds=new WeakSet;class wi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Be&&Be.active&&Be.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Ds.has(this)&&(Ds.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Si(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Jr(this),vi(this);const t=ae,n=Ge;ae=this,Ge=!0;try{return this.fn()}finally{_i(this),ae=t,Ge=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Tr(t);this.deps=this.depsTail=void 0,Jr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Ds.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Zs(this)&&this.run()}get dirty(){return Zs(this)}}let bi=0,bn,Sn;function Si(e,t=!1){if(e.flags|=8,t){e.next=Sn,Sn=e;return}e.next=bn,bn=e}function Cr(){bi++}function Rr(){if(--bi>0)return;if(Sn){let t=Sn;for(Sn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;bn;){let t=bn;for(bn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function vi(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function _i(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Tr(s),xl(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Zs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ei(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ei(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Rn))return;e.globalVersion=Rn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Zs(e)){e.flags&=-3;return}const n=ae,s=Ge;ae=e,Ge=!0;try{vi(e);const r=e.fn(e._value);(t.version===0||xt(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ae=n,Ge=s,_i(e),e.flags&=-3}}function Tr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Tr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function xl(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ge=!0;const Ai=[];function Lt(){Ai.push(Ge),Ge=!1}function kt(){const e=Ai.pop();Ge=e===void 0?!0:e}function Jr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ae;ae=void 0;try{t()}finally{ae=n}}}let Rn=0;class Il{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class xr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!Ge||ae===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ae)n=this.activeLink=new Il(ae,this),ae.deps?(n.prevDep=ae.depsTail,ae.depsTail.nextDep=n,ae.depsTail=n):ae.deps=ae.depsTail=n,Ci(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ae.depsTail,n.nextDep=void 0,ae.depsTail.nextDep=n,ae.depsTail=n,ae.deps===n&&(ae.deps=s)}return n}trigger(t){this.version++,Rn++,this.notify(t)}notify(t){Cr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Rr()}}}function Ci(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Ci(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const er=new WeakMap,jt=Symbol(""),tr=Symbol(""),Tn=Symbol("");function _e(e,t,n){if(Ge&&ae){let s=er.get(e);s||er.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new xr),r.map=s,r.key=n),r.track()}}function gt(e,t,n,s,r,o){const i=er.get(e);if(!i){Rn++;return}const c=l=>{l&&l.trigger()};if(Cr(),t==="clear")i.forEach(c);else{const l=B(e),u=l&&Er(n);if(l&&n==="length"){const a=Number(s);i.forEach((f,p)=>{(p==="length"||p===Tn||!at(p)&&p>=a)&&c(f)})}else switch((n!==void 0||i.has(void 0))&&c(i.get(n)),u&&c(i.get(Tn)),t){case"add":l?u&&c(i.get("length")):(c(i.get(jt)),Yt(e)&&c(i.get(tr)));break;case"delete":l||(c(i.get(jt)),Yt(e)&&c(i.get(tr)));break;case"set":Yt(e)&&c(i.get(jt));break}}Rr()}function Jt(e){const t=X(e);return t===e?t:(_e(t,"iterate",Tn),We(e)?t:t.map(Ee))}function ms(e){return _e(e=X(e),"iterate",Tn),e}const Pl={__proto__:null,[Symbol.iterator](){return Ms(this,Symbol.iterator,Ee)},concat(...e){return Jt(this).concat(...e.map(t=>B(t)?Jt(t):t))},entries(){return Ms(this,"entries",e=>(e[1]=Ee(e[1]),e))},every(e,t){return ft(this,"every",e,t,void 0,arguments)},filter(e,t){return ft(this,"filter",e,t,n=>n.map(Ee),arguments)},find(e,t){return ft(this,"find",e,t,Ee,arguments)},findIndex(e,t){return ft(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ft(this,"findLast",e,t,Ee,arguments)},findLastIndex(e,t){return ft(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ft(this,"forEach",e,t,void 0,arguments)},includes(...e){return Fs(this,"includes",e)},indexOf(...e){return Fs(this,"indexOf",e)},join(e){return Jt(this).join(e)},lastIndexOf(...e){return Fs(this,"lastIndexOf",e)},map(e,t){return ft(this,"map",e,t,void 0,arguments)},pop(){return dn(this,"pop")},push(...e){return dn(this,"push",e)},reduce(e,...t){return Gr(this,"reduce",e,t)},reduceRight(e,...t){return Gr(this,"reduceRight",e,t)},shift(){return dn(this,"shift")},some(e,t){return ft(this,"some",e,t,void 0,arguments)},splice(...e){return dn(this,"splice",e)},toReversed(){return Jt(this).toReversed()},toSorted(e){return Jt(this).toSorted(e)},toSpliced(...e){return Jt(this).toSpliced(...e)},unshift(...e){return dn(this,"unshift",e)},values(){return Ms(this,"values",Ee)}};function Ms(e,t,n){const s=ms(e),r=s[t]();return s!==e&&!We(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Ol=Array.prototype;function ft(e,t,n,s,r,o){const i=ms(e),c=i!==e&&!We(e),l=i[t];if(l!==Ol[t]){const f=l.apply(e,o);return c?Ee(f):f}let u=n;i!==e&&(c?u=function(f,p){return n.call(this,Ee(f),p,e)}:n.length>2&&(u=function(f,p){return n.call(this,f,p,e)}));const a=l.call(i,u,s);return c&&r?r(a):a}function Gr(e,t,n,s){const r=ms(e);let o=n;return r!==e&&(We(e)?n.length>3&&(o=function(i,c,l){return n.call(this,i,c,l,e)}):o=function(i,c,l){return n.call(this,i,Ee(c),l,e)}),r[t](o,...s)}function Fs(e,t,n){const s=X(e);_e(s,"iterate",Tn);const r=s[t](...n);return(r===-1||r===!1)&&Or(n[0])?(n[0]=X(n[0]),s[t](...n)):r}function dn(e,t,n=[]){Lt(),Cr();const s=X(e)[t].apply(e,n);return Rr(),kt(),s}const Ll=Sr("__proto__,__v_isRef,__isVue"),Ri=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(at));function kl(e){at(e)||(e=String(e));const t=X(this);return _e(t,"has",e),t.hasOwnProperty(e)}class Ti{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?Vl:Oi:o?Pi:Ii).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=B(t);if(!r){let l;if(i&&(l=Pl[n]))return l;if(n==="hasOwnProperty")return kl}const c=Reflect.get(t,n,Re(t)?t:s);return(at(n)?Ri.has(n):Ll(n))||(r||_e(t,"get",n),o)?c:Re(c)?i&&Er(n)?c:c.value:ie(c)?r?ki(c):Fn(c):c}}class xi extends Ti{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const l=qt(o);if(!We(s)&&!qt(s)&&(o=X(o),s=X(s)),!B(t)&&Re(o)&&!Re(s))return l?!1:(o.value=s,!0)}const i=B(t)&&Er(n)?Number(n)<t.length:te(t,n),c=Reflect.set(t,n,s,Re(t)?t:r);return t===X(r)&&(i?xt(s,o)&&gt(t,"set",n,s):gt(t,"add",n,s)),c}deleteProperty(t,n){const s=te(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&gt(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!at(n)||!Ri.has(n))&&_e(t,"has",n),s}ownKeys(t){return _e(t,"iterate",B(t)?"length":jt),Reflect.ownKeys(t)}}class Nl extends Ti{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Dl=new xi,Ml=new Nl,Fl=new xi(!0);const nr=e=>e,Vn=e=>Reflect.getPrototypeOf(e);function $l(e,t,n){return function(...s){const r=this.__v_raw,o=X(r),i=Yt(o),c=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,u=r[e](...s),a=n?nr:t?sr:Ee;return!t&&_e(o,"iterate",l?tr:jt),{next(){const{value:f,done:p}=u.next();return p?{value:f,done:p}:{value:c?[a(f[0]),a(f[1])]:a(f),done:p}},[Symbol.iterator](){return this}}}}function qn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Bl(e,t){const n={get(r){const o=this.__v_raw,i=X(o),c=X(r);e||(xt(r,c)&&_e(i,"get",r),_e(i,"get",c));const{has:l}=Vn(i),u=t?nr:e?sr:Ee;if(l.call(i,r))return u(o.get(r));if(l.call(i,c))return u(o.get(c));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&_e(X(r),"iterate",jt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=X(o),c=X(r);return e||(xt(r,c)&&_e(i,"has",r),_e(i,"has",c)),r===c?o.has(r):o.has(r)||o.has(c)},forEach(r,o){const i=this,c=i.__v_raw,l=X(c),u=t?nr:e?sr:Ee;return!e&&_e(l,"iterate",jt),c.forEach((a,f)=>r.call(o,u(a),u(f),i))}};return we(n,e?{add:qn("add"),set:qn("set"),delete:qn("delete"),clear:qn("clear")}:{add(r){!t&&!We(r)&&!qt(r)&&(r=X(r));const o=X(this);return Vn(o).has.call(o,r)||(o.add(r),gt(o,"add",r,r)),this},set(r,o){!t&&!We(o)&&!qt(o)&&(o=X(o));const i=X(this),{has:c,get:l}=Vn(i);let u=c.call(i,r);u||(r=X(r),u=c.call(i,r));const a=l.call(i,r);return i.set(r,o),u?xt(o,a)&&gt(i,"set",r,o):gt(i,"add",r,o),this},delete(r){const o=X(this),{has:i,get:c}=Vn(o);let l=i.call(o,r);l||(r=X(r),l=i.call(o,r)),c&&c.call(o,r);const u=o.delete(r);return l&&gt(o,"delete",r,void 0),u},clear(){const r=X(this),o=r.size!==0,i=r.clear();return o&&gt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=$l(r,e,t)}),n}function Ir(e,t){const n=Bl(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(te(n,r)&&r in s?n:s,r,o)}const Ul={get:Ir(!1,!1)},jl={get:Ir(!1,!0)},Hl={get:Ir(!0,!1)};const Ii=new WeakMap,Pi=new WeakMap,Oi=new WeakMap,Vl=new WeakMap;function ql(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Wl(e){return e.__v_skip||!Object.isExtensible(e)?0:ql(gl(e))}function Fn(e){return qt(e)?e:Pr(e,!1,Dl,Ul,Ii)}function Li(e){return Pr(e,!1,Fl,jl,Pi)}function ki(e){return Pr(e,!0,Ml,Hl,Oi)}function Pr(e,t,n,s,r){if(!ie(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=r.get(e);if(o)return o;const i=Wl(e);if(i===0)return e;const c=new Proxy(e,i===2?s:n);return r.set(e,c),c}function Zt(e){return qt(e)?Zt(e.__v_raw):!!(e&&e.__v_isReactive)}function qt(e){return!!(e&&e.__v_isReadonly)}function We(e){return!!(e&&e.__v_isShallow)}function Or(e){return e?!!e.__v_raw:!1}function X(e){const t=e&&e.__v_raw;return t?X(t):e}function Kl(e){return!te(e,"__v_skip")&&Object.isExtensible(e)&&pi(e,"__v_skip",!0),e}const Ee=e=>ie(e)?Fn(e):e,sr=e=>ie(e)?ki(e):e;function Re(e){return e?e.__v_isRef===!0:!1}function ge(e){return Ni(e,!1)}function zl(e){return Ni(e,!0)}function Ni(e,t){return Re(e)?e:new Jl(e,t)}class Jl{constructor(t,n){this.dep=new xr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:X(t),this._value=n?t:Ee(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||We(t)||qt(t);t=s?t:X(t),xt(t,n)&&(this._rawValue=t,this._value=s?t:Ee(t),this.dep.trigger())}}function en(e){return Re(e)?e.value:e}const Gl={get:(e,t,n)=>t==="__v_raw"?e:en(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Re(r)&&!Re(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Di(e){return Zt(e)?e:new Proxy(e,Gl)}class Ql{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new xr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Rn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return Si(this,!0),!0}get value(){const t=this.dep.track();return Ei(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Xl(e,t,n=!1){let s,r;return W(e)?s=e:(s=e.get,r=e.set),new Ql(s,r,n)}const Wn={},ns=new WeakMap;let $t;function Yl(e,t=!1,n=$t){if(n){let s=ns.get(n);s||ns.set(n,s=[]),s.push(e)}}function Zl(e,t,n=oe){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:c,call:l}=n,u=L=>r?L:We(L)||r===!1||r===0?mt(L,1):mt(L);let a,f,p,g,y=!1,b=!1;if(Re(e)?(f=()=>e.value,y=We(e)):Zt(e)?(f=()=>u(e),y=!0):B(e)?(b=!0,y=e.some(L=>Zt(L)||We(L)),f=()=>e.map(L=>{if(Re(L))return L.value;if(Zt(L))return u(L);if(W(L))return l?l(L,2):L()})):W(e)?t?f=l?()=>l(e,2):e:f=()=>{if(p){Lt();try{p()}finally{kt()}}const L=$t;$t=a;try{return l?l(e,3,[g]):e(g)}finally{$t=L}}:f=lt,t&&r){const L=f,j=r===!0?1/0:r;f=()=>mt(L(),j)}const S=Tl(),C=()=>{a.stop(),S&&S.active&&_r(S.effects,a)};if(o&&t){const L=t;t=(...j)=>{L(...j),C()}}let T=b?new Array(e.length).fill(Wn):Wn;const P=L=>{if(!(!(a.flags&1)||!a.dirty&&!L))if(t){const j=a.run();if(r||y||(b?j.some((z,J)=>xt(z,T[J])):xt(j,T))){p&&p();const z=$t;$t=a;try{const J=[j,T===Wn?void 0:b&&T[0]===Wn?[]:T,g];l?l(t,3,J):t(...J),T=j}finally{$t=z}}}else a.run()};return c&&c(P),a=new wi(f),a.scheduler=i?()=>i(P,!1):P,g=L=>Yl(L,!1,a),p=a.onStop=()=>{const L=ns.get(a);if(L){if(l)l(L,4);else for(const j of L)j();ns.delete(a)}},t?s?P(!0):T=a.run():i?i(P.bind(null,!0),!0):a.run(),C.pause=a.pause.bind(a),C.resume=a.resume.bind(a),C.stop=C,C}function mt(e,t=1/0,n){if(t<=0||!ie(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Re(e))mt(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)mt(e[s],t,n);else if(cn(e)||Yt(e))e.forEach(s=>{mt(s,t,n)});else if(hi(e)){for(const s in e)mt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&mt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function $n(e,t,n,s){try{return s?e(...s):e()}catch(r){ys(r,t,n)}}function Xe(e,t,n,s){if(W(e)){const r=$n(e,t,n,s);return r&&fi(r)&&r.catch(o=>{ys(o,t,n)}),r}if(B(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Xe(e[o],t,n,s));return r}}function ys(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||oe;if(t){let c=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;c;){const a=c.ec;if(a){for(let f=0;f<a.length;f++)if(a[f](e,l,u)===!1)return}c=c.parent}if(o){Lt(),$n(o,null,10,[e,l,u]),kt();return}}ea(e,n,r,s,i)}function ea(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const xe=[];let it=-1;const tn=[];let At=null,Gt=0;const Mi=Promise.resolve();let ss=null;function Lr(e){const t=ss||Mi;return e?t.then(this?e.bind(this):e):t}function ta(e){let t=it+1,n=xe.length;for(;t<n;){const s=t+n>>>1,r=xe[s],o=xn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function kr(e){if(!(e.flags&1)){const t=xn(e),n=xe[xe.length-1];!n||!(e.flags&2)&&t>=xn(n)?xe.push(e):xe.splice(ta(t),0,e),e.flags|=1,Fi()}}function Fi(){ss||(ss=Mi.then(Bi))}function na(e){B(e)?tn.push(...e):At&&e.id===-1?At.splice(Gt+1,0,e):e.flags&1||(tn.push(e),e.flags|=1),Fi()}function Qr(e,t,n=it+1){for(;n<xe.length;n++){const s=xe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;xe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function $i(e){if(tn.length){const t=[...new Set(tn)].sort((n,s)=>xn(n)-xn(s));if(tn.length=0,At){At.push(...t);return}for(At=t,Gt=0;Gt<At.length;Gt++){const n=At[Gt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}At=null,Gt=0}}const xn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Bi(e){try{for(it=0;it<xe.length;it++){const t=xe[it];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),$n(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;it<xe.length;it++){const t=xe[it];t&&(t.flags&=-2)}it=-1,xe.length=0,$i(),ss=null,(xe.length||tn.length)&&Bi()}}let ke=null,Ui=null;function rs(e){const t=ke;return ke=e,Ui=e&&e.type.__scopeId||null,t}function ji(e,t=ke,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&co(-1);const o=rs(t);let i;try{i=e(...r)}finally{rs(o),s._d&&co(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function _p(e,t){if(ke===null)return e;const n=As(ke),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,c,l=oe]=t[r];o&&(W(o)&&(o={mounted:o,updated:o}),o.deep&&mt(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:c,modifiers:l}))}return e}function Nt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const c=r[i];o&&(c.oldValue=o[i].value);let l=c.dir[s];l&&(Lt(),Xe(l,n,8,[e.el,c,e,t]),kt())}}const sa=Symbol("_vte"),Hi=e=>e.__isTeleport,Ct=Symbol("_leaveCb"),Kn=Symbol("_enterCb");function ra(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ss(()=>{e.isMounted=!0}),Xi(()=>{e.isUnmounting=!0}),e}const Ve=[Function,Array],Vi={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ve,onEnter:Ve,onAfterEnter:Ve,onEnterCancelled:Ve,onBeforeLeave:Ve,onLeave:Ve,onAfterLeave:Ve,onLeaveCancelled:Ve,onBeforeAppear:Ve,onAppear:Ve,onAfterAppear:Ve,onAppearCancelled:Ve},qi=e=>{const t=e.subTree;return t.component?qi(t.component):t},oa={name:"BaseTransition",props:Vi,setup(e,{slots:t}){const n=nu(),s=ra();return()=>{const r=t.default&&zi(t.default(),!0);if(!r||!r.length)return;const o=Wi(r),i=X(e),{mode:c}=i;if(s.isLeaving)return $s(o);const l=Xr(o);if(!l)return $s(o);let u=rr(l,i,s,n,f=>u=f);l.type!==Le&&In(l,u);let a=n.subTree&&Xr(n.subTree);if(a&&a.type!==Le&&!Bt(l,a)&&qi(n).type!==Le){let f=rr(a,i,s,n);if(In(a,f),c==="out-in"&&l.type!==Le)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,a=void 0},$s(o);c==="in-out"&&l.type!==Le?f.delayLeave=(p,g,y)=>{const b=Ki(s,a);b[String(a.key)]=a,p[Ct]=()=>{g(),p[Ct]=void 0,delete u.delayedLeave,a=void 0},u.delayedLeave=()=>{y(),delete u.delayedLeave,a=void 0}}:a=void 0}else a&&(a=void 0);return o}}};function Wi(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Le){t=n;break}}return t}const ia=oa;function Ki(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function rr(e,t,n,s,r){const{appear:o,mode:i,persisted:c=!1,onBeforeEnter:l,onEnter:u,onAfterEnter:a,onEnterCancelled:f,onBeforeLeave:p,onLeave:g,onAfterLeave:y,onLeaveCancelled:b,onBeforeAppear:S,onAppear:C,onAfterAppear:T,onAppearCancelled:P}=t,L=String(e.key),j=Ki(n,e),z=(q,Q)=>{q&&Xe(q,s,9,Q)},J=(q,Q)=>{const ce=Q[1];z(q,Q),B(q)?q.every(F=>F.length<=1)&&ce():q.length<=1&&ce()},fe={mode:i,persisted:c,beforeEnter(q){let Q=l;if(!n.isMounted)if(o)Q=S||l;else return;q[Ct]&&q[Ct](!0);const ce=j[L];ce&&Bt(e,ce)&&ce.el[Ct]&&ce.el[Ct](),z(Q,[q])},enter(q){let Q=u,ce=a,F=f;if(!n.isMounted)if(o)Q=C||u,ce=T||a,F=P||f;else return;let Y=!1;const me=q[Kn]=De=>{Y||(Y=!0,De?z(F,[q]):z(ce,[q]),fe.delayedLeave&&fe.delayedLeave(),q[Kn]=void 0)};Q?J(Q,[q,me]):me()},leave(q,Q){const ce=String(e.key);if(q[Kn]&&q[Kn](!0),n.isUnmounting)return Q();z(p,[q]);let F=!1;const Y=q[Ct]=me=>{F||(F=!0,Q(),me?z(b,[q]):z(y,[q]),q[Ct]=void 0,j[ce]===e&&delete j[ce])};j[ce]=e,g?J(g,[q,Y]):Y()},clone(q){const Q=rr(q,t,n,s,r);return r&&r(Q),Q}};return fe}function $s(e){if(ws(e))return e=It(e),e.children=null,e}function Xr(e){if(!ws(e))return Hi(e.type)&&e.children?Wi(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&W(n.default))return n.default()}}function In(e,t){e.shapeFlag&6&&e.component?(e.transition=t,In(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function zi(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const c=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===Je?(i.patchFlag&128&&r++,s=s.concat(zi(i.children,t,c))):(t||i.type!==Le)&&s.push(c!=null?It(i,{key:c}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Ji(e,t){return W(e)?we({name:e.name},t,{setup:e}):e}function Gi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function os(e,t,n,s,r=!1){if(B(e)){e.forEach((y,b)=>os(y,t&&(B(t)?t[b]:t),n,s,r));return}if(vn(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&os(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?As(s.component):s.el,i=r?null:o,{i:c,r:l}=e,u=t&&t.r,a=c.refs===oe?c.refs={}:c.refs,f=c.setupState,p=X(f),g=f===oe?()=>!1:y=>te(p,y);if(u!=null&&u!==l&&(he(u)?(a[u]=null,g(u)&&(f[u]=null)):Re(u)&&(u.value=null)),W(l))$n(l,c,12,[i,a]);else{const y=he(l),b=Re(l);if(y||b){const S=()=>{if(e.f){const C=y?g(l)?f[l]:a[l]:l.value;r?B(C)&&_r(C,o):B(C)?C.includes(o)||C.push(o):y?(a[l]=[o],g(l)&&(f[l]=a[l])):(l.value=[o],e.k&&(a[e.k]=l.value))}else y?(a[l]=i,g(l)&&(f[l]=i)):b&&(l.value=i,e.k&&(a[e.k]=i))};i?(S.id=-1,$e(S,n)):S()}}}ps().requestIdleCallback;ps().cancelIdleCallback;const vn=e=>!!e.type.__asyncLoader,ws=e=>e.type.__isKeepAlive;function ca(e,t){Qi(e,"a",t)}function la(e,t){Qi(e,"da",t)}function Qi(e,t,n=be){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(bs(t,s,n),n){let r=n.parent;for(;r&&r.parent;)ws(r.parent.vnode)&&aa(s,t,n,r),r=r.parent}}function aa(e,t,n,s){const r=bs(t,e,s,!0);vs(()=>{_r(s[t],r)},n)}function bs(e,t,n=be,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Lt();const c=Bn(n),l=Xe(t,n,e,i);return c(),kt(),l});return s?r.unshift(o):r.push(o),o}}const St=e=>(t,n=be)=>{(!On||e==="sp")&&bs(e,(...s)=>t(...s),n)},ua=St("bm"),Ss=St("m"),fa=St("bu"),da=St("u"),Xi=St("bum"),vs=St("um"),ha=St("sp"),pa=St("rtg"),ga=St("rtc");function ma(e,t=be){bs("ec",e,t)}const ya="components";function Yi(e,t){return ba(ya,e,!0,t)||e}const wa=Symbol.for("v-ndc");function ba(e,t,n=!0,s=!1){const r=ke||be;if(r){const o=r.type;{const c=cu(o,!1);if(c&&(c===t||c===ze(t)||c===hs(ze(t))))return o}const i=Yr(r[e]||o[e],t)||Yr(r.appContext[e],t);return!i&&s?o:i}}function Yr(e,t){return e&&(e[t]||e[ze(t)]||e[hs(ze(t))])}function Sa(e,t,n,s){let r;const o=n,i=B(e);if(i||he(e)){const c=i&&Zt(e);let l=!1;c&&(l=!We(e),e=ms(e)),r=new Array(e.length);for(let u=0,a=e.length;u<a;u++)r[u]=t(l?Ee(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let c=0;c<e;c++)r[c]=t(c+1,c,void 0,o)}else if(ie(e))if(e[Symbol.iterator])r=Array.from(e,(c,l)=>t(c,l,void 0,o));else{const c=Object.keys(e);r=new Array(c.length);for(let l=0,u=c.length;l<u;l++){const a=c[l];r[l]=t(e[a],a,l,o)}}else r=[];return r}const or=e=>e?vc(e)?As(e):or(e.parent):null,_n=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>or(e.parent),$root:e=>or(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ec(e),$forceUpdate:e=>e.f||(e.f=()=>{kr(e.update)}),$nextTick:e=>e.n||(e.n=Lr.bind(e.proxy)),$watch:e=>ja.bind(e)}),Bs=(e,t)=>e!==oe&&!e.__isScriptSetup&&te(e,t),va={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:c,appContext:l}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Bs(s,t))return i[t]=1,s[t];if(r!==oe&&te(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&te(u,t))return i[t]=3,o[t];if(n!==oe&&te(n,t))return i[t]=4,n[t];ir&&(i[t]=0)}}const a=_n[t];let f,p;if(a)return t==="$attrs"&&_e(e.attrs,"get",""),a(e);if((f=c.__cssModules)&&(f=f[t]))return f;if(n!==oe&&te(n,t))return i[t]=4,n[t];if(p=l.config.globalProperties,te(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Bs(r,t)?(r[t]=n,!0):s!==oe&&te(s,t)?(s[t]=n,!0):te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let c;return!!n[i]||e!==oe&&te(e,i)||Bs(t,i)||(c=o[0])&&te(c,i)||te(s,i)||te(_n,i)||te(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:te(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Zr(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let ir=!0;function _a(e){const t=ec(e),n=e.proxy,s=e.ctx;ir=!1,t.beforeCreate&&eo(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:c,provide:l,inject:u,created:a,beforeMount:f,mounted:p,beforeUpdate:g,updated:y,activated:b,deactivated:S,beforeDestroy:C,beforeUnmount:T,destroyed:P,unmounted:L,render:j,renderTracked:z,renderTriggered:J,errorCaptured:fe,serverPrefetch:q,expose:Q,inheritAttrs:ce,components:F,directives:Y,filters:me}=t;if(u&&Ea(u,s,null),i)for(const se in i){const Z=i[se];W(Z)&&(s[se]=Z.bind(n))}if(r){const se=r.call(n,n);ie(se)&&(e.data=Fn(se))}if(ir=!0,o)for(const se in o){const Z=o[se],ut=W(Z)?Z.bind(n,n):W(Z.get)?Z.get.bind(n,n):lt,vt=!W(Z)&&W(Z.set)?Z.set.bind(n):lt,et=qe({get:ut,set:vt});Object.defineProperty(s,se,{enumerable:!0,configurable:!0,get:()=>et.value,set:Ie=>et.value=Ie})}if(c)for(const se in c)Zi(c[se],s,n,se);if(l){const se=W(l)?l.call(n):l;Reflect.ownKeys(se).forEach(Z=>{Jn(Z,se[Z])})}a&&eo(a,e,"c");function de(se,Z){B(Z)?Z.forEach(ut=>se(ut.bind(n))):Z&&se(Z.bind(n))}if(de(ua,f),de(Ss,p),de(fa,g),de(da,y),de(ca,b),de(la,S),de(ma,fe),de(ga,z),de(pa,J),de(Xi,T),de(vs,L),de(ha,q),B(Q))if(Q.length){const se=e.exposed||(e.exposed={});Q.forEach(Z=>{Object.defineProperty(se,Z,{get:()=>n[Z],set:ut=>n[Z]=ut})})}else e.exposed||(e.exposed={});j&&e.render===lt&&(e.render=j),ce!=null&&(e.inheritAttrs=ce),F&&(e.components=F),Y&&(e.directives=Y),q&&Gi(e)}function Ea(e,t,n=lt){B(e)&&(e=cr(e));for(const s in e){const r=e[s];let o;ie(r)?"default"in r?o=Qe(r.from||s,r.default,!0):o=Qe(r.from||s):o=Qe(r),Re(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function eo(e,t,n){Xe(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Zi(e,t,n,s){let r=s.includes(".")?pc(n,s):()=>n[s];if(he(e)){const o=t[e];W(o)&&Gn(r,o)}else if(W(e))Gn(r,e.bind(n));else if(ie(e))if(B(e))e.forEach(o=>Zi(o,t,n,s));else{const o=W(e.handler)?e.handler.bind(n):t[e.handler];W(o)&&Gn(r,o,e)}}function ec(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,c=o.get(t);let l;return c?l=c:!r.length&&!n&&!s?l=t:(l={},r.length&&r.forEach(u=>is(l,u,i,!0)),is(l,t,i)),ie(t)&&o.set(t,l),l}function is(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&is(e,o,n,!0),r&&r.forEach(i=>is(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const c=Aa[i]||n&&n[i];e[i]=c?c(e[i],t[i]):t[i]}return e}const Aa={data:to,props:no,emits:no,methods:yn,computed:yn,beforeCreate:Te,created:Te,beforeMount:Te,mounted:Te,beforeUpdate:Te,updated:Te,beforeDestroy:Te,beforeUnmount:Te,destroyed:Te,unmounted:Te,activated:Te,deactivated:Te,errorCaptured:Te,serverPrefetch:Te,components:yn,directives:yn,watch:Ra,provide:to,inject:Ca};function to(e,t){return t?e?function(){return we(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function Ca(e,t){return yn(cr(e),cr(t))}function cr(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Te(e,t){return e?[...new Set([].concat(e,t))]:t}function yn(e,t){return e?we(Object.create(null),e,t):t}function no(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:we(Object.create(null),Zr(e),Zr(t??{})):t}function Ra(e,t){if(!e)return t;if(!t)return e;const n=we(Object.create(null),e);for(const s in t)n[s]=Te(e[s],t[s]);return n}function tc(){return{app:null,config:{isNativeTag:hl,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ta=0;function xa(e,t){return function(s,r=null){W(s)||(s=we({},s)),r!=null&&!ie(r)&&(r=null);const o=tc(),i=new WeakSet,c=[];let l=!1;const u=o.app={_uid:Ta++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:au,get config(){return o.config},set config(a){},use(a,...f){return i.has(a)||(a&&W(a.install)?(i.add(a),a.install(u,...f)):W(a)&&(i.add(a),a(u,...f))),u},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),u},component(a,f){return f?(o.components[a]=f,u):o.components[a]},directive(a,f){return f?(o.directives[a]=f,u):o.directives[a]},mount(a,f,p){if(!l){const g=u._ceVNode||ve(s,r);return g.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(g,a,p),l=!0,u._container=a,a.__vue_app__=u,As(g.component)}},onUnmount(a){c.push(a)},unmount(){l&&(Xe(c,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(a,f){return o.provides[a]=f,u},runWithContext(a){const f=nn;nn=u;try{return a()}finally{nn=f}}};return u}}let nn=null;function Jn(e,t){if(be){let n=be.provides;const s=be.parent&&be.parent.provides;s===n&&(n=be.provides=Object.create(s)),n[e]=t}}function Qe(e,t,n=!1){const s=be||ke;if(s||nn){const r=nn?nn._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&W(t)?t.call(s&&s.proxy):t}}const nc={},sc=()=>Object.create(nc),rc=e=>Object.getPrototypeOf(e)===nc;function Ia(e,t,n,s=!1){const r={},o=sc();e.propsDefaults=Object.create(null),oc(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:Li(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Pa(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,c=X(r),[l]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const a=e.vnode.dynamicProps;for(let f=0;f<a.length;f++){let p=a[f];if(_s(e.emitsOptions,p))continue;const g=t[p];if(l)if(te(o,p))g!==o[p]&&(o[p]=g,u=!0);else{const y=ze(p);r[y]=lr(l,c,y,g,e,!1)}else g!==o[p]&&(o[p]=g,u=!0)}}}else{oc(e,t,r,o)&&(u=!0);let a;for(const f in c)(!t||!te(t,f)&&((a=Ot(f))===f||!te(t,a)))&&(l?n&&(n[f]!==void 0||n[a]!==void 0)&&(r[f]=lr(l,c,f,void 0,e,!0)):delete r[f]);if(o!==c)for(const f in o)(!t||!te(t,f))&&(delete o[f],u=!0)}u&&gt(e.attrs,"set","")}function oc(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,c;if(t)for(let l in t){if(wn(l))continue;const u=t[l];let a;r&&te(r,a=ze(l))?!o||!o.includes(a)?n[a]=u:(c||(c={}))[a]=u:_s(e.emitsOptions,l)||(!(l in s)||u!==s[l])&&(s[l]=u,i=!0)}if(o){const l=X(n),u=c||oe;for(let a=0;a<o.length;a++){const f=o[a];n[f]=lr(r,l,f,u[f],e,!te(u,f))}}return i}function lr(e,t,n,s,r,o){const i=e[n];if(i!=null){const c=te(i,"default");if(c&&s===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&W(l)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const a=Bn(r);s=u[n]=l.call(null,t),a()}}else s=l;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!c?s=!1:i[1]&&(s===""||s===Ot(n))&&(s=!0))}return s}const Oa=new WeakMap;function ic(e,t,n=!1){const s=n?Oa:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},c=[];let l=!1;if(!W(e)){const a=f=>{l=!0;const[p,g]=ic(f,t,!0);we(i,p),g&&c.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!l)return ie(e)&&s.set(e,Xt),Xt;if(B(o))for(let a=0;a<o.length;a++){const f=ze(o[a]);so(f)&&(i[f]=oe)}else if(o)for(const a in o){const f=ze(a);if(so(f)){const p=o[a],g=i[f]=B(p)||W(p)?{type:p}:we({},p),y=g.type;let b=!1,S=!0;if(B(y))for(let C=0;C<y.length;++C){const T=y[C],P=W(T)&&T.name;if(P==="Boolean"){b=!0;break}else P==="String"&&(S=!1)}else b=W(y)&&y.name==="Boolean";g[0]=b,g[1]=S,(b||te(g,"default"))&&c.push(f)}}const u=[i,c];return ie(e)&&s.set(e,u),u}function so(e){return e[0]!=="$"&&!wn(e)}const cc=e=>e[0]==="_"||e==="$stable",Nr=e=>B(e)?e.map(ct):[ct(e)],La=(e,t,n)=>{if(t._n)return t;const s=ji((...r)=>Nr(t(...r)),n);return s._c=!1,s},lc=(e,t,n)=>{const s=e._ctx;for(const r in e){if(cc(r))continue;const o=e[r];if(W(o))t[r]=La(r,o,s);else if(o!=null){const i=Nr(o);t[r]=()=>i}}},ac=(e,t)=>{const n=Nr(t);e.slots.default=()=>n},uc=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},ka=(e,t,n)=>{const s=e.slots=sc();if(e.vnode.shapeFlag&32){const r=t._;r?(uc(s,t,n),n&&pi(s,"_",r,!0)):lc(t,s)}else t&&ac(e,t)},Na=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=oe;if(s.shapeFlag&32){const c=t._;c?n&&c===1?o=!1:uc(r,t,n):(o=!t.$stable,lc(t,r)),i=t}else t&&(ac(e,t),i={default:1});if(o)for(const c in r)!cc(c)&&i[c]==null&&delete r[c]},$e=Ja;function Da(e){return Ma(e)}function Ma(e,t){const n=ps();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:c,createComment:l,setText:u,setElementText:a,parentNode:f,nextSibling:p,setScopeId:g=lt,insertStaticContent:y}=e,b=(d,h,m,v=null,A=null,E=null,O=void 0,I=null,x=!!h.dynamicChildren)=>{if(d===h)return;d&&!Bt(d,h)&&(v=_(d),Ie(d,A,E,!0),d=null),h.patchFlag===-2&&(x=!1,h.dynamicChildren=null);const{type:R,ref:H,shapeFlag:N}=h;switch(R){case Es:S(d,h,m,v);break;case Le:C(d,h,m,v);break;case js:d==null&&T(h,m,v,O);break;case Je:F(d,h,m,v,A,E,O,I,x);break;default:N&1?j(d,h,m,v,A,E,O,I,x):N&6?Y(d,h,m,v,A,E,O,I,x):(N&64||N&128)&&R.process(d,h,m,v,A,E,O,I,x,$)}H!=null&&A&&os(H,d&&d.ref,E,h||d,!h)},S=(d,h,m,v)=>{if(d==null)s(h.el=c(h.children),m,v);else{const A=h.el=d.el;h.children!==d.children&&u(A,h.children)}},C=(d,h,m,v)=>{d==null?s(h.el=l(h.children||""),m,v):h.el=d.el},T=(d,h,m,v)=>{[d.el,d.anchor]=y(d.children,h,m,v,d.el,d.anchor)},P=({el:d,anchor:h},m,v)=>{let A;for(;d&&d!==h;)A=p(d),s(d,m,v),d=A;s(h,m,v)},L=({el:d,anchor:h})=>{let m;for(;d&&d!==h;)m=p(d),r(d),d=m;r(h)},j=(d,h,m,v,A,E,O,I,x)=>{h.type==="svg"?O="svg":h.type==="math"&&(O="mathml"),d==null?z(h,m,v,A,E,O,I,x):q(d,h,A,E,O,I,x)},z=(d,h,m,v,A,E,O,I)=>{let x,R;const{props:H,shapeFlag:N,transition:U,dirs:V}=d;if(x=d.el=i(d.type,E,H&&H.is,H),N&8?a(x,d.children):N&16&&fe(d.children,x,null,v,A,Us(d,E),O,I),V&&Nt(d,null,v,"created"),J(x,d,d.scopeId,O,v),H){for(const le in H)le!=="value"&&!wn(le)&&o(x,le,null,H[le],E,v);"value"in H&&o(x,"value",null,H.value,E),(R=H.onVnodeBeforeMount)&&rt(R,v,d)}V&&Nt(d,null,v,"beforeMount");const G=Fa(A,U);G&&U.beforeEnter(x),s(x,h,m),((R=H&&H.onVnodeMounted)||G||V)&&$e(()=>{R&&rt(R,v,d),G&&U.enter(x),V&&Nt(d,null,v,"mounted")},A)},J=(d,h,m,v,A)=>{if(m&&g(d,m),v)for(let E=0;E<v.length;E++)g(d,v[E]);if(A){let E=A.subTree;if(h===E||mc(E.type)&&(E.ssContent===h||E.ssFallback===h)){const O=A.vnode;J(d,O,O.scopeId,O.slotScopeIds,A.parent)}}},fe=(d,h,m,v,A,E,O,I,x=0)=>{for(let R=x;R<d.length;R++){const H=d[R]=I?Rt(d[R]):ct(d[R]);b(null,H,h,m,v,A,E,O,I)}},q=(d,h,m,v,A,E,O)=>{const I=h.el=d.el;let{patchFlag:x,dynamicChildren:R,dirs:H}=h;x|=d.patchFlag&16;const N=d.props||oe,U=h.props||oe;let V;if(m&&Dt(m,!1),(V=U.onVnodeBeforeUpdate)&&rt(V,m,h,d),H&&Nt(h,d,m,"beforeUpdate"),m&&Dt(m,!0),(N.innerHTML&&U.innerHTML==null||N.textContent&&U.textContent==null)&&a(I,""),R?Q(d.dynamicChildren,R,I,m,v,Us(h,A),E):O||Z(d,h,I,null,m,v,Us(h,A),E,!1),x>0){if(x&16)ce(I,N,U,m,A);else if(x&2&&N.class!==U.class&&o(I,"class",null,U.class,A),x&4&&o(I,"style",N.style,U.style,A),x&8){const G=h.dynamicProps;for(let le=0;le<G.length;le++){const ne=G[le],Me=N[ne],Pe=U[ne];(Pe!==Me||ne==="value")&&o(I,ne,Me,Pe,A,m)}}x&1&&d.children!==h.children&&a(I,h.children)}else!O&&R==null&&ce(I,N,U,m,A);((V=U.onVnodeUpdated)||H)&&$e(()=>{V&&rt(V,m,h,d),H&&Nt(h,d,m,"updated")},v)},Q=(d,h,m,v,A,E,O)=>{for(let I=0;I<h.length;I++){const x=d[I],R=h[I],H=x.el&&(x.type===Je||!Bt(x,R)||x.shapeFlag&70)?f(x.el):m;b(x,R,H,null,v,A,E,O,!0)}},ce=(d,h,m,v,A)=>{if(h!==m){if(h!==oe)for(const E in h)!wn(E)&&!(E in m)&&o(d,E,h[E],null,A,v);for(const E in m){if(wn(E))continue;const O=m[E],I=h[E];O!==I&&E!=="value"&&o(d,E,I,O,A,v)}"value"in m&&o(d,"value",h.value,m.value,A)}},F=(d,h,m,v,A,E,O,I,x)=>{const R=h.el=d?d.el:c(""),H=h.anchor=d?d.anchor:c("");let{patchFlag:N,dynamicChildren:U,slotScopeIds:V}=h;V&&(I=I?I.concat(V):V),d==null?(s(R,m,v),s(H,m,v),fe(h.children||[],m,H,A,E,O,I,x)):N>0&&N&64&&U&&d.dynamicChildren?(Q(d.dynamicChildren,U,m,A,E,O,I),(h.key!=null||A&&h===A.subTree)&&fc(d,h,!0)):Z(d,h,m,H,A,E,O,I,x)},Y=(d,h,m,v,A,E,O,I,x)=>{h.slotScopeIds=I,d==null?h.shapeFlag&512?A.ctx.activate(h,m,v,O,x):me(h,m,v,A,E,O,x):De(d,h,x)},me=(d,h,m,v,A,E,O)=>{const I=d.component=tu(d,v,A);if(ws(d)&&(I.ctx.renderer=$),su(I,!1,O),I.asyncDep){if(A&&A.registerDep(I,de,O),!d.el){const x=I.subTree=ve(Le);C(null,x,h,m)}}else de(I,d,h,m,A,E,O)},De=(d,h,m)=>{const v=h.component=d.component;if(Ka(d,h,m))if(v.asyncDep&&!v.asyncResolved){se(v,h,m);return}else v.next=h,v.update();else h.el=d.el,v.vnode=h},de=(d,h,m,v,A,E,O)=>{const I=()=>{if(d.isMounted){let{next:N,bu:U,u:V,parent:G,vnode:le}=d;{const nt=dc(d);if(nt){N&&(N.el=le.el,se(d,N,O)),nt.asyncDep.then(()=>{d.isUnmounted||I()});return}}let ne=N,Me;Dt(d,!1),N?(N.el=le.el,se(d,N,O)):N=le,U&&zn(U),(Me=N.props&&N.props.onVnodeBeforeUpdate)&&rt(Me,G,N,le),Dt(d,!0);const Pe=oo(d),tt=d.subTree;d.subTree=Pe,b(tt,Pe,f(tt.el),_(tt),d,A,E),N.el=Pe.el,ne===null&&za(d,Pe.el),V&&$e(V,A),(Me=N.props&&N.props.onVnodeUpdated)&&$e(()=>rt(Me,G,N,le),A)}else{let N;const{el:U,props:V}=h,{bm:G,m:le,parent:ne,root:Me,type:Pe}=d,tt=vn(h);Dt(d,!1),G&&zn(G),!tt&&(N=V&&V.onVnodeBeforeMount)&&rt(N,ne,h),Dt(d,!0);{Me.ce&&Me.ce._injectChildStyle(Pe);const nt=d.subTree=oo(d);b(null,nt,m,v,d,A,E),h.el=nt.el}if(le&&$e(le,A),!tt&&(N=V&&V.onVnodeMounted)){const nt=h;$e(()=>rt(N,ne,nt),A)}(h.shapeFlag&256||ne&&vn(ne.vnode)&&ne.vnode.shapeFlag&256)&&d.a&&$e(d.a,A),d.isMounted=!0,h=m=v=null}};d.scope.on();const x=d.effect=new wi(I);d.scope.off();const R=d.update=x.run.bind(x),H=d.job=x.runIfDirty.bind(x);H.i=d,H.id=d.uid,x.scheduler=()=>kr(H),Dt(d,!0),R()},se=(d,h,m)=>{h.component=d;const v=d.vnode.props;d.vnode=h,d.next=null,Pa(d,h.props,v,m),Na(d,h.children,m),Lt(),Qr(d),kt()},Z=(d,h,m,v,A,E,O,I,x=!1)=>{const R=d&&d.children,H=d?d.shapeFlag:0,N=h.children,{patchFlag:U,shapeFlag:V}=h;if(U>0){if(U&128){vt(R,N,m,v,A,E,O,I,x);return}else if(U&256){ut(R,N,m,v,A,E,O,I,x);return}}V&8?(H&16&&He(R,A,E),N!==R&&a(m,N)):H&16?V&16?vt(R,N,m,v,A,E,O,I,x):He(R,A,E,!0):(H&8&&a(m,""),V&16&&fe(N,m,v,A,E,O,I,x))},ut=(d,h,m,v,A,E,O,I,x)=>{d=d||Xt,h=h||Xt;const R=d.length,H=h.length,N=Math.min(R,H);let U;for(U=0;U<N;U++){const V=h[U]=x?Rt(h[U]):ct(h[U]);b(d[U],V,m,null,A,E,O,I,x)}R>H?He(d,A,E,!0,!1,N):fe(h,m,v,A,E,O,I,x,N)},vt=(d,h,m,v,A,E,O,I,x)=>{let R=0;const H=h.length;let N=d.length-1,U=H-1;for(;R<=N&&R<=U;){const V=d[R],G=h[R]=x?Rt(h[R]):ct(h[R]);if(Bt(V,G))b(V,G,m,null,A,E,O,I,x);else break;R++}for(;R<=N&&R<=U;){const V=d[N],G=h[U]=x?Rt(h[U]):ct(h[U]);if(Bt(V,G))b(V,G,m,null,A,E,O,I,x);else break;N--,U--}if(R>N){if(R<=U){const V=U+1,G=V<H?h[V].el:v;for(;R<=U;)b(null,h[R]=x?Rt(h[R]):ct(h[R]),m,G,A,E,O,I,x),R++}}else if(R>U)for(;R<=N;)Ie(d[R],A,E,!0),R++;else{const V=R,G=R,le=new Map;for(R=G;R<=U;R++){const Fe=h[R]=x?Rt(h[R]):ct(h[R]);Fe.key!=null&&le.set(Fe.key,R)}let ne,Me=0;const Pe=U-G+1;let tt=!1,nt=0;const fn=new Array(Pe);for(R=0;R<Pe;R++)fn[R]=0;for(R=V;R<=N;R++){const Fe=d[R];if(Me>=Pe){Ie(Fe,A,E,!0);continue}let st;if(Fe.key!=null)st=le.get(Fe.key);else for(ne=G;ne<=U;ne++)if(fn[ne-G]===0&&Bt(Fe,h[ne])){st=ne;break}st===void 0?Ie(Fe,A,E,!0):(fn[st-G]=R+1,st>=nt?nt=st:tt=!0,b(Fe,h[st],m,null,A,E,O,I,x),Me++)}const qr=tt?$a(fn):Xt;for(ne=qr.length-1,R=Pe-1;R>=0;R--){const Fe=G+R,st=h[Fe],Wr=Fe+1<H?h[Fe+1].el:v;fn[R]===0?b(null,st,m,Wr,A,E,O,I,x):tt&&(ne<0||R!==qr[ne]?et(st,m,Wr,2):ne--)}}},et=(d,h,m,v,A=null)=>{const{el:E,type:O,transition:I,children:x,shapeFlag:R}=d;if(R&6){et(d.component.subTree,h,m,v);return}if(R&128){d.suspense.move(h,m,v);return}if(R&64){O.move(d,h,m,$);return}if(O===Je){s(E,h,m);for(let N=0;N<x.length;N++)et(x[N],h,m,v);s(d.anchor,h,m);return}if(O===js){P(d,h,m);return}if(v!==2&&R&1&&I)if(v===0)I.beforeEnter(E),s(E,h,m),$e(()=>I.enter(E),A);else{const{leave:N,delayLeave:U,afterLeave:V}=I,G=()=>s(E,h,m),le=()=>{N(E,()=>{G(),V&&V()})};U?U(E,G,le):le()}else s(E,h,m)},Ie=(d,h,m,v=!1,A=!1)=>{const{type:E,props:O,ref:I,children:x,dynamicChildren:R,shapeFlag:H,patchFlag:N,dirs:U,cacheIndex:V}=d;if(N===-2&&(A=!1),I!=null&&os(I,null,m,d,!0),V!=null&&(h.renderCache[V]=void 0),H&256){h.ctx.deactivate(d);return}const G=H&1&&U,le=!vn(d);let ne;if(le&&(ne=O&&O.onVnodeBeforeUnmount)&&rt(ne,h,d),H&6)Hn(d.component,m,v);else{if(H&128){d.suspense.unmount(m,v);return}G&&Nt(d,null,h,"beforeUnmount"),H&64?d.type.remove(d,h,m,$,v):R&&!R.hasOnce&&(E!==Je||N>0&&N&64)?He(R,h,m,!1,!0):(E===Je&&N&384||!A&&H&16)&&He(x,h,m),v&&Kt(d)}(le&&(ne=O&&O.onVnodeUnmounted)||G)&&$e(()=>{ne&&rt(ne,h,d),G&&Nt(d,null,h,"unmounted")},m)},Kt=d=>{const{type:h,el:m,anchor:v,transition:A}=d;if(h===Je){zt(m,v);return}if(h===js){L(d);return}const E=()=>{r(m),A&&!A.persisted&&A.afterLeave&&A.afterLeave()};if(d.shapeFlag&1&&A&&!A.persisted){const{leave:O,delayLeave:I}=A,x=()=>O(m,E);I?I(d.el,E,x):x()}else E()},zt=(d,h)=>{let m;for(;d!==h;)m=p(d),r(d),d=m;r(h)},Hn=(d,h,m)=>{const{bum:v,scope:A,job:E,subTree:O,um:I,m:x,a:R}=d;ro(x),ro(R),v&&zn(v),A.stop(),E&&(E.flags|=8,Ie(O,d,h,m)),I&&$e(I,h),$e(()=>{d.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},He=(d,h,m,v=!1,A=!1,E=0)=>{for(let O=E;O<d.length;O++)Ie(d[O],h,m,v,A)},_=d=>{if(d.shapeFlag&6)return _(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const h=p(d.anchor||d.el),m=h&&h[sa];return m?p(m):h};let D=!1;const k=(d,h,m)=>{d==null?h._vnode&&Ie(h._vnode,null,null,!0):b(h._vnode||null,d,h,null,null,null,m),h._vnode=d,D||(D=!0,Qr(),$i(),D=!1)},$={p:b,um:Ie,m:et,r:Kt,mt:me,mc:fe,pc:Z,pbc:Q,n:_,o:e};return{render:k,hydrate:void 0,createApp:xa(k)}}function Us({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Dt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Fa(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function fc(e,t,n=!1){const s=e.children,r=t.children;if(B(s)&&B(r))for(let o=0;o<s.length;o++){const i=s[o];let c=r[o];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=r[o]=Rt(r[o]),c.el=i.el),!n&&c.patchFlag!==-2&&fc(i,c)),c.type===Es&&(c.el=i.el)}}function $a(e){const t=e.slice(),n=[0];let s,r,o,i,c;const l=e.length;for(s=0;s<l;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)c=o+i>>1,e[n[c]]<u?o=c+1:i=c;u<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function dc(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:dc(t)}function ro(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Ba=Symbol.for("v-scx"),Ua=()=>Qe(Ba);function Gn(e,t,n){return hc(e,t,n)}function hc(e,t,n=oe){const{immediate:s,deep:r,flush:o,once:i}=n,c=we({},n),l=t&&s||!t&&o!=="post";let u;if(On){if(o==="sync"){const g=Ua();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!l){const g=()=>{};return g.stop=lt,g.resume=lt,g.pause=lt,g}}const a=be;c.call=(g,y,b)=>Xe(g,a,y,b);let f=!1;o==="post"?c.scheduler=g=>{$e(g,a&&a.suspense)}:o!=="sync"&&(f=!0,c.scheduler=(g,y)=>{y?g():kr(g)}),c.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,a&&(g.id=a.uid,g.i=a))};const p=Zl(e,t,c);return On&&(u?u.push(p):l&&p()),p}function ja(e,t,n){const s=this.proxy,r=he(e)?e.includes(".")?pc(s,e):()=>s[e]:e.bind(s,s);let o;W(t)?o=t:(o=t.handler,n=t);const i=Bn(this),c=hc(r,o.bind(s),n);return i(),c}function pc(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const Ha=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ze(t)}Modifiers`]||e[`${Ot(t)}Modifiers`];function Va(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||oe;let r=n;const o=t.startsWith("update:"),i=o&&Ha(s,t.slice(7));i&&(i.trim&&(r=n.map(a=>he(a)?a.trim():a)),i.number&&(r=n.map(ts)));let c,l=s[c=ks(t)]||s[c=ks(ze(t))];!l&&o&&(l=s[c=ks(Ot(t))]),l&&Xe(l,e,6,r);const u=s[c+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Xe(u,e,6,r)}}function gc(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},c=!1;if(!W(e)){const l=u=>{const a=gc(u,t,!0);a&&(c=!0,we(i,a))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!c?(ie(e)&&s.set(e,null),null):(B(o)?o.forEach(l=>i[l]=null):we(i,o),ie(e)&&s.set(e,i),i)}function _s(e,t){return!e||!fs(t)?!1:(t=t.slice(2).replace(/Once$/,""),te(e,t[0].toLowerCase()+t.slice(1))||te(e,Ot(t))||te(e,t))}function oo(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:c,emit:l,render:u,renderCache:a,props:f,data:p,setupState:g,ctx:y,inheritAttrs:b}=e,S=rs(e);let C,T;try{if(n.shapeFlag&4){const L=r||s,j=L;C=ct(u.call(j,L,a,f,g,p,y)),T=c}else{const L=t;C=ct(L.length>1?L(f,{attrs:c,slots:i,emit:l}):L(f,null)),T=t.props?c:qa(c)}}catch(L){En.length=0,ys(L,e,1),C=ve(Le)}let P=C;if(T&&b!==!1){const L=Object.keys(T),{shapeFlag:j}=P;L.length&&j&7&&(o&&L.some(vr)&&(T=Wa(T,o)),P=It(P,T,!1,!0))}return n.dirs&&(P=It(P,null,!1,!0),P.dirs=P.dirs?P.dirs.concat(n.dirs):n.dirs),n.transition&&In(P,n.transition),C=P,rs(S),C}const qa=e=>{let t;for(const n in e)(n==="class"||n==="style"||fs(n))&&((t||(t={}))[n]=e[n]);return t},Wa=(e,t)=>{const n={};for(const s in e)(!vr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Ka(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:c,patchFlag:l}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?io(s,i,u):!!i;if(l&8){const a=t.dynamicProps;for(let f=0;f<a.length;f++){const p=a[f];if(i[p]!==s[p]&&!_s(u,p))return!0}}}else return(r||c)&&(!c||!c.$stable)?!0:s===i?!1:s?i?io(s,i,u):!0:!!i;return!1}function io(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!_s(n,o))return!0}return!1}function za({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const mc=e=>e.__isSuspense;function Ja(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):na(e)}const Je=Symbol.for("v-fgt"),Es=Symbol.for("v-txt"),Le=Symbol.for("v-cmt"),js=Symbol.for("v-stc"),En=[];let Ue=null;function Ae(e=!1){En.push(Ue=e?null:[])}function Ga(){En.pop(),Ue=En[En.length-1]||null}let Pn=1;function co(e,t=!1){Pn+=e,e<0&&Ue&&t&&(Ue.hasOnce=!0)}function yc(e){return e.dynamicChildren=Pn>0?Ue||Xt:null,Ga(),Pn>0&&Ue&&Ue.push(e),e}function Oe(e,t,n,s,r,o){return yc(pe(e,t,n,s,r,o,!0))}function wc(e,t,n,s,r){return yc(ve(e,t,n,s,r,!0))}function cs(e){return e?e.__v_isVNode===!0:!1}function Bt(e,t){return e.type===t.type&&e.key===t.key}const bc=({key:e})=>e??null,Qn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?he(e)||Re(e)||W(e)?{i:ke,r:e,k:t,f:!!n}:e:null);function pe(e,t=null,n=null,s=0,r=null,o=e===Je?0:1,i=!1,c=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&bc(t),ref:t&&Qn(t),scopeId:Ui,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ke};return c?(Dr(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=he(n)?8:16),Pn>0&&!i&&Ue&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&Ue.push(l),l}const ve=Qa;function Qa(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===wa)&&(e=Le),cs(e)){const c=It(e,t,!0);return n&&Dr(c,n),Pn>0&&!o&&Ue&&(c.shapeFlag&6?Ue[Ue.indexOf(e)]=c:Ue.push(c)),c.patchFlag=-2,c}if(lu(e)&&(e=e.__vccOpts),t){t=Xa(t);let{class:c,style:l}=t;c&&!he(c)&&(t.class=ln(c)),ie(l)&&(Or(l)&&!B(l)&&(l=we({},l)),t.style=gs(l))}const i=he(e)?1:mc(e)?128:Hi(e)?64:ie(e)?4:W(e)?2:0;return pe(e,t,n,s,r,i,o,!0)}function Xa(e){return e?Or(e)||rc(e)?we({},e):e:null}function It(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:c,transition:l}=e,u=t?Ya(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&bc(u),ref:t&&t.ref?n&&o?B(o)?o.concat(Qn(t)):[o,Qn(t)]:Qn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Je?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&It(e.ssContent),ssFallback:e.ssFallback&&It(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&In(a,l.clone(a)),a}function Sc(e=" ",t=0){return ve(Es,null,e,t)}function yt(e="",t=!1){return t?(Ae(),wc(Le,null,e)):ve(Le,null,e)}function ct(e){return e==null||typeof e=="boolean"?ve(Le):B(e)?ve(Je,null,e.slice()):cs(e)?Rt(e):ve(Es,null,String(e))}function Rt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:It(e)}function Dr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Dr(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!rc(t)?t._ctx=ke:r===3&&ke&&(ke.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:ke},n=32):(t=String(t),s&64?(n=16,t=[Sc(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ya(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=ln([t.class,s.class]));else if(r==="style")t.style=gs([t.style,s.style]);else if(fs(r)){const o=t[r],i=s[r];i&&o!==i&&!(B(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function rt(e,t,n,s=null){Xe(e,t,7,[n,s])}const Za=tc();let eu=0;function tu(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Za,o={uid:eu++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Rl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ic(s,r),emitsOptions:gc(s,r),emit:null,emitted:null,propsDefaults:oe,inheritAttrs:s.inheritAttrs,ctx:oe,data:oe,props:oe,attrs:oe,slots:oe,refs:oe,setupState:oe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Va.bind(null,o),e.ce&&e.ce(o),o}let be=null;const nu=()=>be||ke;let ls,ar;{const e=ps(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};ls=t("__VUE_INSTANCE_SETTERS__",n=>be=n),ar=t("__VUE_SSR_SETTERS__",n=>On=n)}const Bn=e=>{const t=be;return ls(e),e.scope.on(),()=>{e.scope.off(),ls(t)}},lo=()=>{be&&be.scope.off(),ls(null)};function vc(e){return e.vnode.shapeFlag&4}let On=!1;function su(e,t=!1,n=!1){t&&ar(t);const{props:s,children:r}=e.vnode,o=vc(e);Ia(e,s,o,t),ka(e,r,n);const i=o?ru(e,t):void 0;return t&&ar(!1),i}function ru(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,va);const{setup:s}=n;if(s){Lt();const r=e.setupContext=s.length>1?iu(e):null,o=Bn(e),i=$n(s,e,0,[e.props,r]),c=fi(i);if(kt(),o(),(c||e.sp)&&!vn(e)&&Gi(e),c){if(i.then(lo,lo),t)return i.then(l=>{ao(e,l)}).catch(l=>{ys(l,e,0)});e.asyncDep=i}else ao(e,i)}else _c(e)}function ao(e,t,n){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ie(t)&&(e.setupState=Di(t)),_c(e)}function _c(e,t,n){const s=e.type;e.render||(e.render=s.render||lt);{const r=Bn(e);Lt();try{_a(e)}finally{kt(),r()}}}const ou={get(e,t){return _e(e,"get",""),e[t]}};function iu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,ou),slots:e.slots,emit:e.emit,expose:t}}function As(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Di(Kl(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in _n)return _n[n](e)},has(t,n){return n in t||n in _n}})):e.proxy}function cu(e,t=!0){return W(e)?e.displayName||e.name:e.name||t&&e.__name}function lu(e){return W(e)&&"__vccOpts"in e}const qe=(e,t)=>Xl(e,t,On);function Mr(e,t,n){const s=arguments.length;return s===2?ie(t)&&!B(t)?cs(t)?ve(e,null,[t]):ve(e,t):ve(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&cs(n)&&(n=[n]),ve(e,t,n))}const au="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ur;const uo=typeof window<"u"&&window.trustedTypes;if(uo)try{ur=uo.createPolicy("vue",{createHTML:e=>e})}catch{}const Ec=ur?e=>ur.createHTML(e):e=>e,uu="http://www.w3.org/2000/svg",fu="http://www.w3.org/1998/Math/MathML",pt=typeof document<"u"?document:null,fo=pt&&pt.createElement("template"),du={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?pt.createElementNS(uu,e):t==="mathml"?pt.createElementNS(fu,e):n?pt.createElement(e,{is:n}):pt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>pt.createTextNode(e),createComment:e=>pt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>pt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{fo.innerHTML=Ec(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const c=fo.content;if(s==="svg"||s==="mathml"){const l=c.firstChild;for(;l.firstChild;)c.appendChild(l.firstChild);c.removeChild(l)}t.insertBefore(c,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},_t="transition",hn="animation",Ln=Symbol("_vtc"),Ac={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},hu=we({},Vi,Ac),pu=e=>(e.displayName="Transition",e.props=hu,e),gu=pu((e,{slots:t})=>Mr(ia,mu(e),t)),Mt=(e,t=[])=>{B(e)?e.forEach(n=>n(...t)):e&&e(...t)},ho=e=>e?B(e)?e.some(t=>t.length>1):e.length>1:!1;function mu(e){const t={};for(const F in e)F in Ac||(t[F]=e[F]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:c=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:u=i,appearToClass:a=c,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,y=yu(r),b=y&&y[0],S=y&&y[1],{onBeforeEnter:C,onEnter:T,onEnterCancelled:P,onLeave:L,onLeaveCancelled:j,onBeforeAppear:z=C,onAppear:J=T,onAppearCancelled:fe=P}=t,q=(F,Y,me,De)=>{F._enterCancelled=De,Ft(F,Y?a:c),Ft(F,Y?u:i),me&&me()},Q=(F,Y)=>{F._isLeaving=!1,Ft(F,f),Ft(F,g),Ft(F,p),Y&&Y()},ce=F=>(Y,me)=>{const De=F?J:T,de=()=>q(Y,F,me);Mt(De,[Y,de]),po(()=>{Ft(Y,F?l:o),dt(Y,F?a:c),ho(De)||go(Y,s,b,de)})};return we(t,{onBeforeEnter(F){Mt(C,[F]),dt(F,o),dt(F,i)},onBeforeAppear(F){Mt(z,[F]),dt(F,l),dt(F,u)},onEnter:ce(!1),onAppear:ce(!0),onLeave(F,Y){F._isLeaving=!0;const me=()=>Q(F,Y);dt(F,f),F._enterCancelled?(dt(F,p),wo()):(wo(),dt(F,p)),po(()=>{F._isLeaving&&(Ft(F,f),dt(F,g),ho(L)||go(F,s,S,me))}),Mt(L,[F,me])},onEnterCancelled(F){q(F,!1,void 0,!0),Mt(P,[F])},onAppearCancelled(F){q(F,!0,void 0,!0),Mt(fe,[F])},onLeaveCancelled(F){Q(F),Mt(j,[F])}})}function yu(e){if(e==null)return null;if(ie(e))return[Hs(e.enter),Hs(e.leave)];{const t=Hs(e);return[t,t]}}function Hs(e){return wl(e)}function dt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Ln]||(e[Ln]=new Set)).add(t)}function Ft(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Ln];n&&(n.delete(t),n.size||(e[Ln]=void 0))}function po(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let wu=0;function go(e,t,n,s){const r=e._endId=++wu,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:c,propCount:l}=bu(e,t);if(!i)return s();const u=i+"end";let a=0;const f=()=>{e.removeEventListener(u,p),o()},p=g=>{g.target===e&&++a>=l&&f()};setTimeout(()=>{a<l&&f()},c+1),e.addEventListener(u,p)}function bu(e,t){const n=window.getComputedStyle(e),s=y=>(n[y]||"").split(", "),r=s(`${_t}Delay`),o=s(`${_t}Duration`),i=mo(r,o),c=s(`${hn}Delay`),l=s(`${hn}Duration`),u=mo(c,l);let a=null,f=0,p=0;t===_t?i>0&&(a=_t,f=i,p=o.length):t===hn?u>0&&(a=hn,f=u,p=l.length):(f=Math.max(i,u),a=f>0?i>u?_t:hn:null,p=a?a===_t?o.length:l.length:0);const g=a===_t&&/\b(transform|all)(,|$)/.test(s(`${_t}Property`).toString());return{type:a,timeout:f,propCount:p,hasTransform:g}}function mo(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>yo(n)+yo(e[s])))}function yo(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function wo(){return document.body.offsetHeight}function Su(e,t,n){const s=e[Ln];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const bo=Symbol("_vod"),vu=Symbol("_vsh"),_u=Symbol(""),Eu=/(^|;)\s*display\s*:/;function Au(e,t,n){const s=e.style,r=he(n);let o=!1;if(n&&!r){if(t)if(he(t))for(const i of t.split(";")){const c=i.slice(0,i.indexOf(":")).trim();n[c]==null&&Xn(s,c,"")}else for(const i in t)n[i]==null&&Xn(s,i,"");for(const i in n)i==="display"&&(o=!0),Xn(s,i,n[i])}else if(r){if(t!==n){const i=s[_u];i&&(n+=";"+i),s.cssText=n,o=Eu.test(n)}}else t&&e.removeAttribute("style");bo in e&&(e[bo]=o?s.display:"",e[vu]&&(s.display="none"))}const So=/\s*!important$/;function Xn(e,t,n){if(B(n))n.forEach(s=>Xn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Cu(e,t);So.test(n)?e.setProperty(Ot(s),n.replace(So,""),"important"):e[s]=n}}const vo=["Webkit","Moz","ms"],Vs={};function Cu(e,t){const n=Vs[t];if(n)return n;let s=ze(t);if(s!=="filter"&&s in e)return Vs[t]=s;s=hs(s);for(let r=0;r<vo.length;r++){const o=vo[r]+s;if(o in e)return Vs[t]=o}return t}const _o="http://www.w3.org/1999/xlink";function Eo(e,t,n,s,r,o=Al(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(_o,t.slice(6,t.length)):e.setAttributeNS(_o,t,n):n==null||o&&!gi(n)?e.removeAttribute(t):e.setAttribute(t,o?"":at(n)?String(n):n)}function Ao(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Ec(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const c=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(c!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const c=typeof e[t];c==="boolean"?n=gi(n):n==null&&c==="string"?(n="",i=!0):c==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function wt(e,t,n,s){e.addEventListener(t,n,s)}function Ru(e,t,n,s){e.removeEventListener(t,n,s)}const Co=Symbol("_vei");function Tu(e,t,n,s,r=null){const o=e[Co]||(e[Co]={}),i=o[t];if(s&&i)i.value=s;else{const[c,l]=xu(t);if(s){const u=o[t]=Ou(s,r);wt(e,c,u,l)}else i&&(Ru(e,c,i,l),o[t]=void 0)}}const Ro=/(?:Once|Passive|Capture)$/;function xu(e){let t;if(Ro.test(e)){t={};let s;for(;s=e.match(Ro);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Ot(e.slice(2)),t]}let qs=0;const Iu=Promise.resolve(),Pu=()=>qs||(Iu.then(()=>qs=0),qs=Date.now());function Ou(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Xe(Lu(s,n.value),t,5,[s])};return n.value=e,n.attached=Pu(),n}function Lu(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const To=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,ku=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Su(e,s,i):t==="style"?Au(e,n,s):fs(t)?vr(t)||Tu(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Nu(e,t,s,i))?(Ao(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Eo(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!he(s))?Ao(e,ze(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Eo(e,t,s,i))};function Nu(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&To(t)&&W(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return To(t)&&he(n)?!1:t in e}const Pt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>zn(t,n):t};function Du(e){e.target.composing=!0}function xo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ke=Symbol("_assign"),Ep={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Ke]=Pt(r);const o=s||r.props&&r.props.type==="number";wt(e,t?"change":"input",i=>{if(i.target.composing)return;let c=e.value;n&&(c=c.trim()),o&&(c=ts(c)),e[Ke](c)}),n&&wt(e,"change",()=>{e.value=e.value.trim()}),t||(wt(e,"compositionstart",Du),wt(e,"compositionend",xo),wt(e,"change",xo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[Ke]=Pt(i),e.composing)return;const c=(o||e.type==="number")&&!/^0\d/.test(e.value)?ts(e.value):e.value,l=t??"";c!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===l)||(e.value=l))}},Ap={deep:!0,created(e,t,n){e[Ke]=Pt(n),wt(e,"change",()=>{const s=e._modelValue,r=sn(e),o=e.checked,i=e[Ke];if(B(s)){const c=Ar(s,r),l=c!==-1;if(o&&!l)i(s.concat(r));else if(!o&&l){const u=[...s];u.splice(c,1),i(u)}}else if(cn(s)){const c=new Set(s);o?c.add(r):c.delete(r),i(c)}else i(Cc(e,o))})},mounted:Io,beforeUpdate(e,t,n){e[Ke]=Pt(n),Io(e,t,n)}};function Io(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(B(t))r=Ar(t,s.props.value)>-1;else if(cn(t))r=t.has(s.props.value);else{if(t===n)return;r=Vt(t,Cc(e,!0))}e.checked!==r&&(e.checked=r)}const Cp={created(e,{value:t},n){e.checked=Vt(t,n.props.value),e[Ke]=Pt(n),wt(e,"change",()=>{e[Ke](sn(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[Ke]=Pt(s),t!==n&&(e.checked=Vt(t,s.props.value))}},Rp={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=cn(t);wt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?ts(sn(i)):sn(i));e[Ke](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,Lr(()=>{e._assigning=!1})}),e[Ke]=Pt(s)},mounted(e,{value:t}){Po(e,t)},beforeUpdate(e,t,n){e[Ke]=Pt(n)},updated(e,{value:t}){e._assigning||Po(e,t)}};function Po(e,t){const n=e.multiple,s=B(t);if(!(n&&!s&&!cn(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],c=sn(i);if(n)if(s){const l=typeof c;l==="string"||l==="number"?i.selected=t.some(u=>String(u)===String(c)):i.selected=Ar(t,c)>-1}else i.selected=t.has(c);else if(Vt(sn(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function sn(e){return"_value"in e?e._value:e.value}function Cc(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Mu=["ctrl","shift","alt","meta"],Fu={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Mu.some(n=>e[`${n}Key`]&&!t.includes(n))},Tp=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const c=Fu[t[i]];if(c&&c(r,t))return}return e(r,...o)})},$u={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},xp=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=Ot(r.key);if(t.some(i=>i===o||$u[i]===o))return e(r)})},Bu=we({patchProp:ku},du);let Oo;function Uu(){return Oo||(Oo=Da(Bu))}const ju=(...e)=>{const t=Uu().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Vu(s);if(!r)return;const o=t._component;!W(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Hu(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Hu(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Vu(e){return he(e)?document.querySelector(e):e}var qu={msoCTPDockPositionLeft:0,msoCTPDockPositionRight:2};function Wu(){if(window.location.protocol==="file:"){const r=window.location.href;return r.substring(0,r.lastIndexOf("/"))}const{protocol:e,hostname:t,port:n}=window.location,s=n?`:${n}`:"";return`${e}//${t}${s}`}function Ku(){return window.location.protocol==="file:"?"":"/#"}const Rc={WPS_Enum:qu,GetUrlPath:Wu,GetRouterHash:Ku};function zu(e){return typeof window.Application.ribbonUI!="object"&&(window.Application.ribbonUI=e),typeof window.Application.Enum!="object"&&(window.Application.Enum=Rc.WPS_Enum),window.Application.ApiEvent.AddApiEventListener("WindowActivate",function(t){const s=`taskpane_id_${t.DocID}`;let r=window.Application.PluginStorage.getItem(s);const o=window.Application.PluginStorage.length;if(o>0)for(let i=0;i<o;i++){const c=window.Application.PluginStorage.key(i);if(c.startsWith("taskpane_id_")){const l=window.Application.PluginStorage.getItem(c);if(l!==r){const u=window.Application.GetTaskPane(l);u&&(u.Visible=!1)}}}if(r){const i=window.Application.GetTaskPane(r);i&&(i.Visible=!0)}}),!0}var Ju=0;function Gu(e){switch((e==null?void 0:e.Id)||"btnShowTaskPane"){case"btnShowTaskPane":{const s=`taskpane_id_${window.Application.ActiveDocument.DocID}`;let r=window.Application.PluginStorage.getItem(s),o;if(r)o=window.Application.GetTaskPane(r);else{o=window.Application.CreateTaskPane("http://wwwps.hexinedu.com/wps-addon-build"+Rc.GetRouterHash()+"/taskpane");let i=o.ID;window.Application.PluginStorage.setItem(s,i)}o.Visible=!0,o.Width=900}break;case"btnApiEvent":{let s=!window.Application.PluginStorage.getItem("ApiEventFlag");window.Application.PluginStorage.setItem("ApiEventFlag",s),s?window.Application.ApiEvent.AddApiEventListener("DocumentNew","ribbon.OnNewDocumentApiEvent"):window.Application.ApiEvent.RemoveApiEventListener("DocumentNew","ribbon.OnNewDocumentApiEvent"),window.Application.ribbonUI.InvalidateControl("btnApiEvent")}break;case"btnWebNotify":{let n=new Date,s=n.getHours()+":"+n.getMinutes()+":"+n.getSeconds();window.Application.OAAssist.WebNotify("这行内容由wps加载项主动送达给业务系统，可以任意自定义, 比如时间值:"+s+"，次数："+ ++Ju,!0)}break}return!0}function Qu(e){switch(e.Id){case"btnShowMsg":return"images/1.svg";case"btnShowDialog":return"images/2.svg";case"btnShowTaskPane":return"images/2.svg"}return"images/newFromTemp.svg"}function Xu(e){switch(e.Id){case"btnShowMsg":return!0;case"btnShowDialog":return window.Application.PluginStorage.getItem("EnableFlag");case"btnShowTaskPane":return window.Application.PluginStorage.getItem("EnableFlag")}return!0}function Yu(e){const t=e.Id;return console.log(t),!0}function Zu(e){switch(e.Id){case"btnIsEnbable":return window.Application.PluginStorage.getItem("EnableFlag")?"按钮Disable":"按钮Enable";case"btnApiEvent":return window.Application.PluginStorage.getItem("ApiEventFlag")?"清除新建文件事件":"注册新建文件事件"}return""}function ef(e){alert("新建文件事件响应，取文件名: "+e.Name)}function tf(e){e.Id}const nf={OnAddinLoad:zu,OnAction:Gu,GetImage:Qu,OnGetEnabled:Xu,OnGetVisible:Yu,OnGetLabel:Zu,OnNewDocumentApiEvent:ef,OnTabActivate:tf};function Tc(e,t){return function(){return e.apply(t,arguments)}}const{toString:sf}=Object.prototype,{getPrototypeOf:Fr}=Object,Cs=(e=>t=>{const n=sf.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Ze=e=>(e=e.toLowerCase(),t=>Cs(t)===e),Rs=e=>t=>typeof t===e,{isArray:an}=Array,kn=Rs("undefined");function rf(e){return e!==null&&!kn(e)&&e.constructor!==null&&!kn(e.constructor)&&je(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const xc=Ze("ArrayBuffer");function of(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&xc(e.buffer),t}const cf=Rs("string"),je=Rs("function"),Ic=Rs("number"),Ts=e=>e!==null&&typeof e=="object",lf=e=>e===!0||e===!1,Yn=e=>{if(Cs(e)!=="object")return!1;const t=Fr(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},af=Ze("Date"),uf=Ze("File"),ff=Ze("Blob"),df=Ze("FileList"),hf=e=>Ts(e)&&je(e.pipe),pf=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||je(e.append)&&((t=Cs(e))==="formdata"||t==="object"&&je(e.toString)&&e.toString()==="[object FormData]"))},gf=Ze("URLSearchParams"),[mf,yf,wf,bf]=["ReadableStream","Request","Response","Headers"].map(Ze),Sf=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Un(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),an(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(s=0;s<i;s++)c=o[s],t.call(null,e[c],c,e)}}function Pc(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const Ut=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Oc=e=>!kn(e)&&e!==Ut;function fr(){const{caseless:e}=Oc(this)&&this||{},t={},n=(s,r)=>{const o=e&&Pc(t,r)||r;Yn(t[o])&&Yn(s)?t[o]=fr(t[o],s):Yn(s)?t[o]=fr({},s):an(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&Un(arguments[s],n);return t}const vf=(e,t,n,{allOwnKeys:s}={})=>(Un(t,(r,o)=>{n&&je(r)?e[o]=Tc(r,n):e[o]=r},{allOwnKeys:s}),e),_f=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ef=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Af=(e,t,n,s)=>{let r,o,i;const c={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&Fr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Cf=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Rf=e=>{if(!e)return null;if(an(e))return e;let t=e.length;if(!Ic(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Tf=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Fr(Uint8Array)),xf=(e,t)=>{const s=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},If=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Pf=Ze("HTMLFormElement"),Of=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),Lo=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Lf=Ze("RegExp"),Lc=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};Un(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},kf=e=>{Lc(e,(t,n)=>{if(je(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(je(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Nf=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return an(e)?s(e):s(String(e).split(t)),n},Df=()=>{},Mf=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t,Ws="abcdefghijklmnopqrstuvwxyz",ko="0123456789",kc={DIGIT:ko,ALPHA:Ws,ALPHA_DIGIT:Ws+Ws.toUpperCase()+ko},Ff=(e=16,t=kc.ALPHA_DIGIT)=>{let n="";const{length:s}=t;for(;e--;)n+=t[Math.random()*s|0];return n};function $f(e){return!!(e&&je(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Bf=e=>{const t=new Array(10),n=(s,r)=>{if(Ts(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const o=an(s)?[]:{};return Un(s,(i,c)=>{const l=n(i,r+1);!kn(l)&&(o[c]=l)}),t[r]=void 0,o}}return s};return n(e,0)},Uf=Ze("AsyncFunction"),jf=e=>e&&(Ts(e)||je(e))&&je(e.then)&&je(e.catch),Nc=((e,t)=>e?setImmediate:t?((n,s)=>(Ut.addEventListener("message",({source:r,data:o})=>{r===Ut&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),Ut.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",je(Ut.postMessage)),Hf=typeof queueMicrotask<"u"?queueMicrotask.bind(Ut):typeof process<"u"&&process.nextTick||Nc,w={isArray:an,isArrayBuffer:xc,isBuffer:rf,isFormData:pf,isArrayBufferView:of,isString:cf,isNumber:Ic,isBoolean:lf,isObject:Ts,isPlainObject:Yn,isReadableStream:mf,isRequest:yf,isResponse:wf,isHeaders:bf,isUndefined:kn,isDate:af,isFile:uf,isBlob:ff,isRegExp:Lf,isFunction:je,isStream:hf,isURLSearchParams:gf,isTypedArray:Tf,isFileList:df,forEach:Un,merge:fr,extend:vf,trim:Sf,stripBOM:_f,inherits:Ef,toFlatObject:Af,kindOf:Cs,kindOfTest:Ze,endsWith:Cf,toArray:Rf,forEachEntry:xf,matchAll:If,isHTMLForm:Pf,hasOwnProperty:Lo,hasOwnProp:Lo,reduceDescriptors:Lc,freezeMethods:kf,toObjectSet:Nf,toCamelCase:Of,noop:Df,toFiniteNumber:Mf,findKey:Pc,global:Ut,isContextDefined:Oc,ALPHABET:kc,generateString:Ff,isSpecCompliantForm:$f,toJSONObject:Bf,isAsyncFn:Uf,isThenable:jf,setImmediate:Nc,asap:Hf};function K(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}w.inherits(K,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:w.toJSONObject(this.config),code:this.code,status:this.status}}});const Dc=K.prototype,Mc={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Mc[e]={value:e}});Object.defineProperties(K,Mc);Object.defineProperty(Dc,"isAxiosError",{value:!0});K.from=(e,t,n,s,r,o)=>{const i=Object.create(Dc);return w.toFlatObject(e,i,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),K.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Vf=null;function dr(e){return w.isPlainObject(e)||w.isArray(e)}function Fc(e){return w.endsWith(e,"[]")?e.slice(0,-2):e}function No(e,t,n){return e?e.concat(t).map(function(r,o){return r=Fc(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function qf(e){return w.isArray(e)&&!e.some(dr)}const Wf=w.toFlatObject(w,{},null,function(t){return/^is[A-Z]/.test(t)});function xs(e,t,n){if(!w.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=w.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(b,S){return!w.isUndefined(S[b])});const s=n.metaTokens,r=n.visitor||a,o=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&w.isSpecCompliantForm(t);if(!w.isFunction(r))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(w.isDate(y))return y.toISOString();if(!l&&w.isBlob(y))throw new K("Blob is not supported. Use a Buffer instead.");return w.isArrayBuffer(y)||w.isTypedArray(y)?l&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function a(y,b,S){let C=y;if(y&&!S&&typeof y=="object"){if(w.endsWith(b,"{}"))b=s?b:b.slice(0,-2),y=JSON.stringify(y);else if(w.isArray(y)&&qf(y)||(w.isFileList(y)||w.endsWith(b,"[]"))&&(C=w.toArray(y)))return b=Fc(b),C.forEach(function(P,L){!(w.isUndefined(P)||P===null)&&t.append(i===!0?No([b],L,o):i===null?b:b+"[]",u(P))}),!1}return dr(y)?!0:(t.append(No(S,b,o),u(y)),!1)}const f=[],p=Object.assign(Wf,{defaultVisitor:a,convertValue:u,isVisitable:dr});function g(y,b){if(!w.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+b.join("."));f.push(y),w.forEach(y,function(C,T){(!(w.isUndefined(C)||C===null)&&r.call(t,C,w.isString(T)?T.trim():T,b,p))===!0&&g(C,b?b.concat(T):[T])}),f.pop()}}if(!w.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Do(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function $r(e,t){this._pairs=[],e&&xs(e,this,t)}const $c=$r.prototype;$c.append=function(t,n){this._pairs.push([t,n])};$c.toString=function(t){const n=t?function(s){return t.call(this,s,Do)}:Do;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function Kf(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Bc(e,t,n){if(!t)return e;const s=n&&n.encode||Kf;w.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=w.isURLSearchParams(t)?t.toString():new $r(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Mo{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){w.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Uc={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},zf=typeof URLSearchParams<"u"?URLSearchParams:$r,Jf=typeof FormData<"u"?FormData:null,Gf=typeof Blob<"u"?Blob:null,Qf={isBrowser:!0,classes:{URLSearchParams:zf,FormData:Jf,Blob:Gf},protocols:["http","https","file","blob","url","data"]},Br=typeof window<"u"&&typeof document<"u",hr=typeof navigator=="object"&&navigator||void 0,Xf=Br&&(!hr||["ReactNative","NativeScript","NS"].indexOf(hr.product)<0),Yf=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Zf=Br&&window.location.href||"http://localhost",ed=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Br,hasStandardBrowserEnv:Xf,hasStandardBrowserWebWorkerEnv:Yf,navigator:hr,origin:Zf},Symbol.toStringTag,{value:"Module"})),Ce={...ed,...Qf};function td(e,t){return xs(e,new Ce.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,o){return Ce.isNode&&w.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function nd(e){return w.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function sd(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function jc(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),l=o>=n.length;return i=!i&&w.isArray(r)?r.length:i,l?(w.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!c):((!r[i]||!w.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&w.isArray(r[i])&&(r[i]=sd(r[i])),!c)}if(w.isFormData(e)&&w.isFunction(e.entries)){const n={};return w.forEachEntry(e,(s,r)=>{t(nd(s),r,n,0)}),n}return null}function rd(e,t,n){if(w.isString(e))try{return(t||JSON.parse)(e),w.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(0,JSON.stringify)(e)}const jn={transitional:Uc,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=w.isObject(t);if(o&&w.isHTMLForm(t)&&(t=new FormData(t)),w.isFormData(t))return r?JSON.stringify(jc(t)):t;if(w.isArrayBuffer(t)||w.isBuffer(t)||w.isStream(t)||w.isFile(t)||w.isBlob(t)||w.isReadableStream(t))return t;if(w.isArrayBufferView(t))return t.buffer;if(w.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return td(t,this.formSerializer).toString();if((c=w.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return xs(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),rd(t)):t}],transformResponse:[function(t){const n=this.transitional||jn.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(w.isResponse(t)||w.isReadableStream(t))return t;if(t&&w.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?K.from(c,K.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ce.classes.FormData,Blob:Ce.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};w.forEach(["delete","get","head","post","put","patch"],e=>{jn.headers[e]={}});const od=w.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),id=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&od[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},Fo=Symbol("internals");function pn(e){return e&&String(e).trim().toLowerCase()}function Zn(e){return e===!1||e==null?e:w.isArray(e)?e.map(Zn):String(e)}function cd(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const ld=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ks(e,t,n,s,r){if(w.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!w.isString(t)){if(w.isString(s))return t.indexOf(s)!==-1;if(w.isRegExp(s))return s.test(t)}}function ad(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function ud(e,t){const n=w.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}class Ne{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(c,l,u){const a=pn(l);if(!a)throw new Error("header name must be a non-empty string");const f=w.findKey(r,a);(!f||r[f]===void 0||u===!0||u===void 0&&r[f]!==!1)&&(r[f||l]=Zn(c))}const i=(c,l)=>w.forEach(c,(u,a)=>o(u,a,l));if(w.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(w.isString(t)&&(t=t.trim())&&!ld(t))i(id(t),n);else if(w.isHeaders(t))for(const[c,l]of t.entries())o(l,c,s);else t!=null&&o(n,t,s);return this}get(t,n){if(t=pn(t),t){const s=w.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return cd(r);if(w.isFunction(n))return n.call(this,r,s);if(w.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=pn(t),t){const s=w.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Ks(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=pn(i),i){const c=w.findKey(s,i);c&&(!n||Ks(s,s[c],c,n))&&(delete s[c],r=!0)}}return w.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||Ks(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return w.forEach(this,(r,o)=>{const i=w.findKey(s,o);if(i){n[i]=Zn(r),delete n[o];return}const c=t?ad(o):String(o).trim();c!==o&&delete n[o],n[c]=Zn(r),s[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return w.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&w.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[Fo]=this[Fo]={accessors:{}}).accessors,r=this.prototype;function o(i){const c=pn(i);s[c]||(ud(r,i),s[c]=!0)}return w.isArray(t)?t.forEach(o):o(t),this}}Ne.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);w.reduceDescriptors(Ne.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});w.freezeMethods(Ne);function zs(e,t){const n=this||jn,s=t||n,r=Ne.from(s.headers);let o=s.data;return w.forEach(e,function(c){o=c.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function Hc(e){return!!(e&&e.__CANCEL__)}function un(e,t,n){K.call(this,e??"canceled",K.ERR_CANCELED,t,n),this.name="CanceledError"}w.inherits(un,K,{__CANCEL__:!0});function Vc(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new K("Request failed with status code "+n.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function fd(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function dd(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),a=s[o];i||(i=u),n[r]=l,s[r]=u;let f=o,p=0;for(;f!==r;)p+=n[f++],f=f%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),u-i<t)return;const g=a&&u-a;return g?Math.round(p*1e3/g):void 0}}function hd(e,t){let n=0,s=1e3/t,r,o;const i=(u,a=Date.now())=>{n=a,r=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const a=Date.now(),f=a-n;f>=s?i(u,a):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},s-f)))},()=>r&&i(r)]}const as=(e,t,n=3)=>{let s=0;const r=dd(50,250);return hd(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,l=i-s,u=r(l),a=i<=c;s=i;const f={loaded:i,total:c,progress:c?i/c:void 0,bytes:l,rate:u||void 0,estimated:u&&c&&a?(c-i)/u:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(f)},n)},$o=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Bo=e=>(...t)=>w.asap(()=>e(...t)),pd=Ce.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ce.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ce.origin),Ce.navigator&&/(msie|trident)/i.test(Ce.navigator.userAgent)):()=>!0,gd=Ce.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];w.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),w.isString(s)&&i.push("path="+s),w.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function md(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function yd(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function qc(e,t){return e&&!md(t)?yd(e,t):t}const Uo=e=>e instanceof Ne?{...e}:e;function Wt(e,t){t=t||{};const n={};function s(u,a,f,p){return w.isPlainObject(u)&&w.isPlainObject(a)?w.merge.call({caseless:p},u,a):w.isPlainObject(a)?w.merge({},a):w.isArray(a)?a.slice():a}function r(u,a,f,p){if(w.isUndefined(a)){if(!w.isUndefined(u))return s(void 0,u,f,p)}else return s(u,a,f,p)}function o(u,a){if(!w.isUndefined(a))return s(void 0,a)}function i(u,a){if(w.isUndefined(a)){if(!w.isUndefined(u))return s(void 0,u)}else return s(void 0,a)}function c(u,a,f){if(f in t)return s(u,a);if(f in e)return s(void 0,u)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,a,f)=>r(Uo(u),Uo(a),f,!0)};return w.forEach(Object.keys(Object.assign({},e,t)),function(a){const f=l[a]||r,p=f(e[a],t[a],a);w.isUndefined(p)&&f!==c||(n[a]=p)}),n}const Wc=e=>{const t=Wt({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=Ne.from(i),t.url=Bc(qc(t.baseURL,t.url),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if(w.isFormData(n)){if(Ce.hasStandardBrowserEnv||Ce.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...a]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...a].join("; "))}}if(Ce.hasStandardBrowserEnv&&(s&&w.isFunction(s)&&(s=s(t)),s||s!==!1&&pd(t.url))){const u=r&&o&&gd.read(o);u&&i.set(r,u)}return t},wd=typeof XMLHttpRequest<"u",bd=wd&&function(e){return new Promise(function(n,s){const r=Wc(e);let o=r.data;const i=Ne.from(r.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:u}=r,a,f,p,g,y;function b(){g&&g(),y&&y(),r.cancelToken&&r.cancelToken.unsubscribe(a),r.signal&&r.signal.removeEventListener("abort",a)}let S=new XMLHttpRequest;S.open(r.method.toUpperCase(),r.url,!0),S.timeout=r.timeout;function C(){if(!S)return;const P=Ne.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),j={data:!c||c==="text"||c==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:P,config:e,request:S};Vc(function(J){n(J),b()},function(J){s(J),b()},j),S=null}"onloadend"in S?S.onloadend=C:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(C)},S.onabort=function(){S&&(s(new K("Request aborted",K.ECONNABORTED,e,S)),S=null)},S.onerror=function(){s(new K("Network Error",K.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let L=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const j=r.transitional||Uc;r.timeoutErrorMessage&&(L=r.timeoutErrorMessage),s(new K(L,j.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,S)),S=null},o===void 0&&i.setContentType(null),"setRequestHeader"in S&&w.forEach(i.toJSON(),function(L,j){S.setRequestHeader(j,L)}),w.isUndefined(r.withCredentials)||(S.withCredentials=!!r.withCredentials),c&&c!=="json"&&(S.responseType=r.responseType),u&&([p,y]=as(u,!0),S.addEventListener("progress",p)),l&&S.upload&&([f,g]=as(l),S.upload.addEventListener("progress",f),S.upload.addEventListener("loadend",g)),(r.cancelToken||r.signal)&&(a=P=>{S&&(s(!P||P.type?new un(null,e,S):P),S.abort(),S=null)},r.cancelToken&&r.cancelToken.subscribe(a),r.signal&&(r.signal.aborted?a():r.signal.addEventListener("abort",a)));const T=fd(r.url);if(T&&Ce.protocols.indexOf(T)===-1){s(new K("Unsupported protocol "+T+":",K.ERR_BAD_REQUEST,e));return}S.send(o||null)})},Sd=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(u){if(!r){r=!0,c();const a=u instanceof Error?u:this.reason;s.abort(a instanceof K?a:new un(a instanceof Error?a.message:a))}};let i=t&&setTimeout(()=>{i=null,o(new K(`timeout ${t} of ms exceeded`,K.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:l}=s;return l.unsubscribe=()=>w.asap(c),l}},vd=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},_d=async function*(e,t){for await(const n of Ed(e))yield*vd(n,t)},Ed=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},jo=(e,t,n,s)=>{const r=_d(e,t);let o=0,i,c=l=>{i||(i=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:u,value:a}=await r.next();if(u){c(),l.close();return}let f=a.byteLength;if(n){let p=o+=f;n(p)}l.enqueue(new Uint8Array(a))}catch(u){throw c(u),u}},cancel(l){return c(l),r.return()}},{highWaterMark:2})},Is=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Kc=Is&&typeof ReadableStream=="function",Ad=Is&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),zc=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Cd=Kc&&zc(()=>{let e=!1;const t=new Request(Ce.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ho=64*1024,pr=Kc&&zc(()=>w.isReadableStream(new Response("").body)),us={stream:pr&&(e=>e.body)};Is&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!us[t]&&(us[t]=w.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new K(`Response type '${t}' is not supported`,K.ERR_NOT_SUPPORT,s)})})})(new Response);const Rd=async e=>{if(e==null)return 0;if(w.isBlob(e))return e.size;if(w.isSpecCompliantForm(e))return(await new Request(Ce.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(w.isArrayBufferView(e)||w.isArrayBuffer(e))return e.byteLength;if(w.isURLSearchParams(e)&&(e=e+""),w.isString(e))return(await Ad(e)).byteLength},Td=async(e,t)=>{const n=w.toFiniteNumber(e.getContentLength());return n??Rd(t)},xd=Is&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:a,withCredentials:f="same-origin",fetchOptions:p}=Wc(e);u=u?(u+"").toLowerCase():"text";let g=Sd([r,o&&o.toAbortSignal()],i),y;const b=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let S;try{if(l&&Cd&&n!=="get"&&n!=="head"&&(S=await Td(a,s))!==0){let j=new Request(t,{method:"POST",body:s,duplex:"half"}),z;if(w.isFormData(s)&&(z=j.headers.get("content-type"))&&a.setContentType(z),j.body){const[J,fe]=$o(S,as(Bo(l)));s=jo(j.body,Ho,J,fe)}}w.isString(f)||(f=f?"include":"omit");const C="credentials"in Request.prototype;y=new Request(t,{...p,signal:g,method:n.toUpperCase(),headers:a.normalize().toJSON(),body:s,duplex:"half",credentials:C?f:void 0});let T=await fetch(y);const P=pr&&(u==="stream"||u==="response");if(pr&&(c||P&&b)){const j={};["status","statusText","headers"].forEach(q=>{j[q]=T[q]});const z=w.toFiniteNumber(T.headers.get("content-length")),[J,fe]=c&&$o(z,as(Bo(c),!0))||[];T=new Response(jo(T.body,Ho,J,()=>{fe&&fe(),b&&b()}),j)}u=u||"text";let L=await us[w.findKey(us,u)||"text"](T,e);return!P&&b&&b(),await new Promise((j,z)=>{Vc(j,z,{data:L,headers:Ne.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:y})})}catch(C){throw b&&b(),C&&C.name==="TypeError"&&/fetch/i.test(C.message)?Object.assign(new K("Network Error",K.ERR_NETWORK,e,y),{cause:C.cause||C}):K.from(C,C&&C.code,e,y)}}),gr={http:Vf,xhr:bd,fetch:xd};w.forEach(gr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Vo=e=>`- ${e}`,Id=e=>w.isFunction(e)||e===null||e===!1,Jc={getAdapter:e=>{e=w.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!Id(n)&&(s=gr[(i=String(n)).toLowerCase()],s===void 0))throw new K(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Vo).join(`
`):" "+Vo(o[0]):"as no adapter specified";throw new K("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:gr};function Js(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new un(null,e)}function qo(e){return Js(e),e.headers=Ne.from(e.headers),e.data=zs.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Jc.getAdapter(e.adapter||jn.adapter)(e).then(function(s){return Js(e),s.data=zs.call(e,e.transformResponse,s),s.headers=Ne.from(s.headers),s},function(s){return Hc(s)||(Js(e),s&&s.response&&(s.response.data=zs.call(e,e.transformResponse,s.response),s.response.headers=Ne.from(s.response.headers))),Promise.reject(s)})}const Gc="1.7.9",Ps={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ps[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Wo={};Ps.transitional=function(t,n,s){function r(o,i){return"[Axios v"+Gc+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,c)=>{if(t===!1)throw new K(r(i," has been removed"+(n?" in "+n:"")),K.ERR_DEPRECATED);return n&&!Wo[i]&&(Wo[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};Ps.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Pd(e,t,n){if(typeof e!="object")throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const c=e[o],l=c===void 0||i(c,o,e);if(l!==!0)throw new K("option "+o+" must be "+l,K.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new K("Unknown option "+o,K.ERR_BAD_OPTION)}}const es={assertOptions:Pd,validators:Ps},ot=es.validators;class Ht{constructor(t){this.defaults=t,this.interceptors={request:new Mo,response:new Mo}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Wt(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&es.assertOptions(s,{silentJSONParsing:ot.transitional(ot.boolean),forcedJSONParsing:ot.transitional(ot.boolean),clarifyTimeoutError:ot.transitional(ot.boolean)},!1),r!=null&&(w.isFunction(r)?n.paramsSerializer={serialize:r}:es.assertOptions(r,{encode:ot.function,serialize:ot.function},!0)),es.assertOptions(n,{baseUrl:ot.spelling("baseURL"),withXsrfToken:ot.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&w.merge(o.common,o[n.method]);o&&w.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),n.headers=Ne.concat(i,o);const c=[];let l=!0;this.interceptors.request.forEach(function(b){typeof b.runWhen=="function"&&b.runWhen(n)===!1||(l=l&&b.synchronous,c.unshift(b.fulfilled,b.rejected))});const u=[];this.interceptors.response.forEach(function(b){u.push(b.fulfilled,b.rejected)});let a,f=0,p;if(!l){const y=[qo.bind(this),void 0];for(y.unshift.apply(y,c),y.push.apply(y,u),p=y.length,a=Promise.resolve(n);f<p;)a=a.then(y[f++],y[f++]);return a}p=c.length;let g=n;for(f=0;f<p;){const y=c[f++],b=c[f++];try{g=y(g)}catch(S){b.call(this,S);break}}try{a=qo.call(this,g)}catch(y){return Promise.reject(y)}for(f=0,p=u.length;f<p;)a=a.then(u[f++],u[f++]);return a}getUri(t){t=Wt(this.defaults,t);const n=qc(t.baseURL,t.url);return Bc(n,t.params,t.paramsSerializer)}}w.forEach(["delete","get","head","options"],function(t){Ht.prototype[t]=function(n,s){return this.request(Wt(s||{},{method:t,url:n,data:(s||{}).data}))}});w.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,c){return this.request(Wt(c||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Ht.prototype[t]=n(),Ht.prototype[t+"Form"]=n(!0)});class Ur{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(c=>{s.subscribe(c),o=c}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,c){s.reason||(s.reason=new un(o,i,c),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Ur(function(r){t=r}),cancel:t}}}function Od(e){return function(n){return e.apply(null,n)}}function Ld(e){return w.isObject(e)&&e.isAxiosError===!0}const mr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(mr).forEach(([e,t])=>{mr[t]=e});function Qc(e){const t=new Ht(e),n=Tc(Ht.prototype.request,t);return w.extend(n,Ht.prototype,t,{allOwnKeys:!0}),w.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Qc(Wt(e,r))},n}const ue=Qc(jn);ue.Axios=Ht;ue.CanceledError=un;ue.CancelToken=Ur;ue.isCancel=Hc;ue.VERSION=Gc;ue.toFormData=xs;ue.AxiosError=K;ue.Cancel=ue.CanceledError;ue.all=function(t){return Promise.all(t)};ue.spread=Od;ue.isAxiosError=Ld;ue.mergeConfig=Wt;ue.AxiosHeaders=Ne;ue.formToJSON=e=>jc(w.isHTMLForm(e)?new FormData(e):e);ue.getAdapter=Jc.getAdapter;ue.HttpStatusCode=mr;ue.default=ue;const kd=100;class Nd{constructor(){this.logs=[],this.listeners=[]}log(t,n="info",s=null){const r={timestamp:new Date().toISOString(),message:t,type:n,data:s?JSON.stringify(s):null};console.log(`${n.toUpperCase()}: ${t}`,s||""),this.logs.unshift(r),this.logs.length>kd&&this.logs.pop(),this.notifyListeners()}info(t,n=null){this.log(t,"info",n)}warn(t,n=null){this.log(t,"warning",n)}error(t,n=null){this.log(t,"error",n)}success(t,n=null){this.log(t,"success",n)}clear(){this.logs=[],this.notifyListeners()}getLogs(){return[...this.logs]}addListener(t){this.listeners.push(t)}removeListener(t){this.listeners=this.listeners.filter(n=>n!==t)}notifyListeners(){this.listeners.forEach(t=>t(this.logs))}}const M=new Nd;class Dd{constructor(){this.ws=null,this.isConnected=!1,this.isConnecting=!1,this.reconnectTimer=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.messageQueue=[],this.messageCallbacks=new Map,this.messageId=0,this.connectionSuccessHandler=null,this.eventListeners={watcher:new Set,urlMonitor:new Set,config:new Set,auth:new Set,health:new Set,connection:new Set,error:new Set,version:new Set},this.nonCriticalTypes=new Set(["health","auth.refreshSession","auth.getOwn","logger"]),this.clientId=null;try{this.clientId=localStorage.getItem("wsClientId")}catch(t){M.warn("无法从localStorage加载客户端ID",t)}}getClientId(){return this.clientId}_saveClientId(t){this.clientId=t;try{localStorage.setItem("wsClientId",t)}catch(n){M.warn("无法保存客户端ID到localStorage",n)}}connect(){return new Promise(t=>{if(this.isConnected){t(!0);return}if(this.isConnecting){const s=setInterval(()=>{this.isConnected&&(clearInterval(s),t(!0))},100);return}this.isConnecting=!0;const n="ws://127.0.0.1:3000";try{this.ws=new WebSocket(n),this.ws.onopen=()=>{this.isConnected=!0,this.isConnecting=!1,this.reconnectAttempts=0,M.success("WebSocket连接成功"),this._processQueue(),this.connectionSuccessHandler&&this.connectionSuccessHandler(),this._notifyEventListeners("connection",{status:"connected"}),t(!0)},this.ws.onmessage=s=>{try{const r=JSON.parse(s.data);r.type==="connection"&&r.action==="connected"&&r.clientId&&this._saveClientId(r.clientId),r.messageId&&this.messageCallbacks.has(r.messageId)&&(this.messageCallbacks.get(r.messageId)(r),this.messageCallbacks.delete(r.messageId)),r.type&&this.eventListeners[r.type]&&this._notifyEventListeners(r.type,r)}catch(r){M.error("处理WebSocket消息时出错",r)}},this.ws.onerror=s=>{M.error("WebSocket错误",s),this._notifyEventListeners("error",{error:"WebSocket连接错误"}),this.isConnected||(this.isConnecting=!1,t(!1))},this.ws.onclose=s=>{this.isConnected=!1,this.isConnecting=!1,M.warn("WebSocket连接关闭",{code:s.code,reason:s.reason}),this._notifyEventListeners("connection",{status:"disconnected",code:s.code,reason:s.reason}),this._reconnect()}}catch(s){this.isConnecting=!1,M.error("创建WebSocket连接时出错",s),this._notifyEventListeners("error",{error:"创建WebSocket连接时出错"}),t(!1),this._reconnect()}})}disconnect(){this.ws&&(this.ws.close(),this.ws=null),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.isConnected=!1,this.isConnecting=!1,this.messageCallbacks.clear()}async sendRequest(t,n,s={},r=1e4){const o=this.nonCriticalTypes.has(t)||this.nonCriticalTypes.has(`${t}.${n}`),i=o?Math.min(r,5e3):r;if(!this.isConnected)try{if(!await this.connect()){if(o)return{success:!1,error:"无法连接到WebSocket服务器",data:null};throw new Error("无法连接到WebSocket服务器")}}catch(c){if(o)return{success:!1,error:c.message,data:null};throw c}return new Promise((c,l)=>{const u=this._generateMessageId(),a={messageId:u,type:t,action:n,data:s},f=setTimeout(()=>{this.messageCallbacks.delete(u),o?(M.warn(`非关键请求超时: ${t}.${n}，返回降级响应`),c({success:!1,error:`请求超时: ${t}.${n}`,data:null})):l(new Error(`请求超时: ${t}.${n}`))},i);this.messageCallbacks.set(u,p=>{clearTimeout(f),p.success===!1&&!o?l(new Error(p.message||"请求失败")):c(p)});try{this._sendMessage(a)}catch(p){clearTimeout(f),this.messageCallbacks.delete(u),o?(M.warn(`非关键请求发送失败: ${t}.${n}，返回降级响应`,p),c({success:!1,error:`发送请求失败: ${p.message}`,data:null})):l(p)}})}addEventListener(t,n){return this.eventListeners[t]||(this.eventListeners[t]=new Set),this.eventListeners[t].add(n),()=>{this.eventListeners[t].delete(n)}}_generateMessageId(){return`${Date.now()}-${++this.messageId}`}_sendMessage(t){if(!this.isConnected){this.messageQueue.push(t),this.connect();return}try{const n=JSON.stringify(t);this.ws.send(n),this.nonCriticalTypes.has(t.type)||this.nonCriticalTypes.has(`${t.type}.${t.action}`)||M.info("发送WebSocket消息",{type:t.type,action:t.action,messageId:t.messageId})}catch(n){throw M.error("发送WebSocket消息时出错",n),t.messageId&&this.messageCallbacks.has(t.messageId)&&this.messageQueue.push(t),this.isConnected=!1,this._reconnect(),n}}_processQueue(){if(this.messageQueue.length===0)return;const t=[...this.messageQueue];this.messageQueue=[],t.forEach(n=>{this._sendMessage(n)})}_reconnect(){if(this.reconnectTimer||this.reconnectAttempts>=this.maxReconnectAttempts)return;this.reconnectAttempts++;const t=this.reconnectInterval*Math.min(this.reconnectAttempts,3);this.reconnectTimer=setTimeout(()=>{this.reconnectTimer=null,this.connect()},t)}_notifyEventListeners(t,n){this.eventListeners[t]&&this.eventListeners[t].forEach(s=>{try{s(n)}catch(r){M.error(`执行 ${t} 事件监听器时出错`,r)}})}setConnectionSuccessHandler(t){this.connectionSuccessHandler=t}async getWatcherStatus(){return this.sendRequest("watcher","getStatus")}async setWatchDirectory(t){return this.sendRequest("watcher","setDirectory",{directory:t})}async startWatcher(){return this.sendRequest("watcher","start")}async stopWatcher(){return this.sendRequest("watcher","stop")}async associateFileWithClient(t){return t?this.sendRequest("watcher","associateFile",{fileName:t,clientId:this.clientId}):(M.warn("关联文件时必须提供文件名"),{success:!1,message:"必须提供文件名"})}async getUrlMonitorStatus(){return this.sendRequest("urlMonitor","getStatus",{clientId:this.clientId})}async startUrlMonitoring(t,n,s={}){return this.sendRequest("urlMonitor","startMonitoring",{url:t,interval:n,clientId:this.clientId,...s})}async stopUrlMonitoring(t){return this.sendRequest("urlMonitor","stopMonitoring",{urlId:t,clientId:this.clientId})}async forceUrlCheck(t){return this.sendRequest("urlMonitor","forceCheck",{urlId:t,clientId:this.clientId})}async startUrlChecking(t){return this.sendRequest("urlMonitor","startChecking",{urlId:t,clientId:this.clientId})}async forceUrlDownload(t){return this.sendRequest("urlMonitor","forceDownload",{urlId:t,clientId:this.clientId})}async getDownloadPath(){return this.sendRequest("urlMonitor","getDownloadPath",{clientId:this.clientId})}async setDownloadPath(t){return this.sendRequest("urlMonitor","setDownloadPath",{path:t,clientId:this.clientId})}async updateTaskStatus(t,n,s){return this.sendRequest("urlMonitor","updateTaskStatus",{urlId:t,status:n,taskId:s,clientId:this.clientId})}async getAddonConfigPath(){return this.sendRequest("config","getAddonConfigPath")}async setAddonConfigPath(t){return this.sendRequest("config","setAddonConfigPath",{path:t})}async getSubjectAndStage(){return this.sendRequest("config","getSubjectAndStage")}async setSubjectAndStage(t,n){return this.sendRequest("config","setSubjectAndStage",{subject:t,stage:n})}async getSaveMethod(){return this.sendRequest("config","getSaveMethod")}async setSaveMethod(t){return this.sendRequest("config","setSaveMethod",{saveMethod:t})}async login(t,n){return this.sendRequest("auth","login",{username:t,password:n})}async logout(){return this.sendRequest("auth","logout")}async getAuthUserInfo(){return this.sendRequest("auth","getOwn")}async refreshSession(){return this.sendRequest("auth","refreshSession")}async getHealthStatus(){try{return await this.sendRequest("health","getStatus",{},3e3)}catch(t){return M.warn("健康检查请求失败，但不影响其他功能",t),{success:!1,error:t.message,data:{status:"error"}}}}async syncLog(t,n,s){return this.sendRequest("logger","syncLog",{content:t,timestamp:n,clientId:s})}async getLogStats(){return this.sendRequest("logger","getStats")}async readClientLog(t,n=100){return this.sendRequest("logger","readLog",{clientId:t,lines:n})}}const Se=new Dd;function Xc(){const e=ge(!1),t=ge(""),n=ge("info"),s=ge(null);return{showNotification:e,notificationMessage:t,notificationType:n,showNotify:(i,c="info",l=2e3)=>{s.value&&clearTimeout(s.value),t.value=i,n.value=c,e.value=!0,s.value=setTimeout(()=>{e.value=!1},l)},cleanupNotification:()=>{s.value&&clearTimeout(s.value)}}}const ye="user_info";ue.defaults.withCredentials=!0;ue.interceptors.request.use(e=>(e.url.includes("127.0.0.1:3000"),e),e=>(M.error("API请求错误",e),Promise.reject(e)));ue.interceptors.response.use(e=>(e.config.url.includes("127.0.0.1:3000"),e),e=>{var t,n;return e.config&&e.config.url.includes("127.0.0.1:3000")?e.response?M.error(`API响应错误 (详细): ${e.config.url}`,{status:e.response.status,statusText:e.response.statusText,headers:e.response.headers,data:e.response.data,message:e.message,stack:e.stack,timestamp:new Date().toISOString()}):e.request?M.error(`API网络错误 (详细): ${e.config.url}`,{message:e.message,code:e.code,errno:e.errno,syscall:e.syscall,address:e.address,port:e.port,stack:e.stack,timestamp:new Date().toISOString(),request:{method:e.config.method,url:e.config.url,headers:e.config.headers,withCredentials:e.config.withCredentials,data:e.config.data,timeout:e.config.timeout}}):M.error(`API配置错误 (详细): ${((t=e.config)==null?void 0:t.url)||"unknown"}`,{message:e.message,stack:e.stack,timestamp:new Date().toISOString(),config:e.config}):e.response?M.error(`API响应错误: ${(n=e.config)==null?void 0:n.url}`,{status:e.response.status,statusText:e.response.statusText,data:e.response.data}):(console.trace(e),M.error("API网络错误",e.message)),Promise.reject(e)});async function Yc(){try{try{const n=await Se.getAuthUserInfo();if(!n.success)throw n.code==="AI_EDIT_DISABLED"?new Error(`AI_EDIT_DISABLED:${n.message||"该企业尚未开启 AI 编辑功能，请联系您的企业管理员。"}`):new Error(n.message||"获取用户信息失败");const s=n==null?void 0:n.data;if(s!=null&&s.userId&&window.Application)return window.Application.PluginStorage.setItem(ye,JSON.stringify(s)),!0;if(s!=null&&s.userId&&localStorage)return localStorage.setItem(ye,JSON.stringify(s)),!0}catch(n){M.warn("存储的凭证无效，需要重新登录",n.message),console.log("Stored credentials are no longer valid, will try to login again")}const e=await Se.getAuthUserInfo();if(!e.success){if(e.code==="AI_EDIT_DISABLED")throw new Error(`AI_EDIT_DISABLED:${e.message||"该企业尚未开启 AI 编辑功能，请联系您的企业管理员。"}`);return M.warn("获取用户信息失败",e.message),!1}const t=e.data;return t!=null&&t.userId&&window.Application?(window.Application.PluginStorage.setItem(ye,JSON.stringify(t)),!0):t!=null&&t.userId&&localStorage?(localStorage.setItem(ye,JSON.stringify(t)),!0):!1}catch(e){return M.error("检查登录状态时出错",e),console.error("Error checking login status:",e),!1}}function Md(){var e;try{const t=(e=window.Application)==null?void 0:e.PluginStorage.getItem(ye);if(t)return JSON.parse(t);if(localStorage){const n=localStorage.getItem(ye);if(n)return JSON.parse(n)}return M.warn("未找到用户信息"),null}catch(t){return M.error("获取用户信息时出错",t),null}}async function Ip(e,t){var n,s;try{const r=await Se.login(e,t);if(r.success){if(r.cookies&&Object.entries(r.cookies).forEach(([o,i])=>{document.cookie=`${o}=${i};`}),r.data){const o=r.data;(n=window.Application)!=null&&n.PluginStorage&&window.Application.PluginStorage.setItem(ye,JSON.stringify(o)),localStorage&&localStorage.setItem(ye,JSON.stringify(o))}else try{const o=await Se.getAuthUserInfo();if(o.success&&o.data){const i=o.data;return(s=window.Application)!=null&&s.PluginStorage&&window.Application.PluginStorage.setItem(ye,JSON.stringify(i)),localStorage&&localStorage.setItem(ye,JSON.stringify(i)),{success:!0,user:i}}}catch(o){M.warn("获取详细用户信息失败",o)}return{success:!0,user:r.data}}return M.warn("登录失败",r.message),{success:!1,message:r.message||"登录失败",code:r.code,data:r.data}}catch(r){return M.error("登录请求异常",{message:r.message}),{success:!1,message:r.message||"登录失败，请稍后重试"}}}async function Pp(){var e;try{return await Se.logout(),(e=window.Application)!=null&&e.PluginStorage&&window.Application.PluginStorage.removeItem(ye),localStorage&&localStorage.removeItem(ye),!0}catch(t){return M.error("登出请求异常",t),console.error("Logout error:",t),!1}}function Op(e,t){const n={};if(!e)n.username="请输入用户名";else{const r=/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),o=/^1[3-9]\d{9}$/.test(e);!r&&!o&&(n.username="请输入正确的手机号或邮箱")}t?t.length<6&&(n.password="密码长度不能少于6位"):n.password="请输入密码";const s=Object.keys(n).length===0;return s||M.warn("登录表单验证失败",n),{isValid:s,errors:n}}async function Fd(){var e;try{const t=await Se.refreshSession();return t.success&&t.data?((e=window.Application)!=null&&e.PluginStorage&&window.Application.PluginStorage.setItem(ye,JSON.stringify(t.data)),localStorage&&localStorage.setItem(ye,JSON.stringify(t.data)),t.cookies&&Object.entries(t.cookies).forEach(([n,s])=>{document.cookie=`${n}=${s};`}),!0):(M.warn("会话刷新失败",t),!1)}catch(t){return M.error("会话刷新请求异常",t),console.error("Session refresh error:",t),!1}}async function Ko(){var e,t;try{const n=Date.now();let s=!0,r=!1,o=null,i=null;try{const c=(t=(e=window.Application)==null?void 0:e.Document)==null?void 0:t.DocID;if(await Se.connect(c))try{const u=await Se.getHealthStatus();console.log("WebSocket健康检查响应",u),(u==null?void 0:u.status)==="ok"?r=!0:(M.warn("WebSocket健康检查返回异常状态",{status:u==null?void 0:u.status}),i=new Error("WebSocket健康检查返回异常状态"))}catch(u){M.error("WebSocket健康请求失败",{message:u.message}),i=u}else M.error("WebSocket连接失败"),i=new Error("WebSocket连接失败")}catch(c){M.error("WebSocket连接异常",{message:c.message}),i=c}return{success:s||r,httpCheck:{success:s,error:o?o.message:null},wsCheck:{success:r,error:i?i.message:null},connectionTime:Date.now()-n}}catch(n){return M.error("服务器连接性检查出错",{message:n.message,stack:n.stack}),{success:!1,error:n.message}}}function $d(){const e=Xc();return Se.addEventListener("auth",async t=>{var n,s;if(t.eventType==="login-success"){e.showNotify("登录成功","success");try{const r=await Se.getAuthUserInfo();if(r.success&&r.data){const o=r.data;(n=window.Application)!=null&&n.PluginStorage&&window.Application.PluginStorage.setItem(ye,JSON.stringify(o)),localStorage&&localStorage.setItem(ye,JSON.stringify(o)),window.location.hash="#/taskpane"}else M.warn("登录成功但获取用户信息失败")}catch(r){M.error("获取用户信息时出错",r)}}else t.eventType==="logout"&&(e.showNotify("检测到插件服务已登出","warning"),(s=window.Application)!=null&&s.PluginStorage&&window.Application.PluginStorage.removeItem(ye),localStorage&&localStorage.removeItem(ye),window.location.hash="#/login")})}const Os=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Bd={name:"NotificationContainer",setup(){const e=Qe("notification");return{showNotification:e.showNotification,notificationMessage:e.notificationMessage,notificationType:e.notificationType}}};function Ud(e,t,n,s,r,o){return Ae(),wc(gu,{name:"notification-fade"},{default:ji(()=>[s.showNotification?(Ae(),Oe("div",{key:0,class:ln(["notification",s.notificationType])},bt(s.notificationMessage),3)):yt("",!0)]),_:1})}const jd=Os(Bd,[["render",Ud]]),Hd={name:"LogViewer",props:{visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){const n=ge([]),s=ge({x:20,y:20}),r=ge(!1),o=ge({x:0,y:0}),i=ge(null),c=S=>{n.value=S};Ss(()=>{n.value=M.getLogs(),M.addListener(c),document.addEventListener("mousemove",u),document.addEventListener("mouseup",a)}),vs(()=>{M.removeListener(c),document.removeEventListener("mousemove",u),document.removeEventListener("mouseup",a)});const l=S=>{if(i.value){r.value=!0;const C=i.value.getBoundingClientRect();o.value={x:S.clientX-C.left,y:S.clientY-C.top}}},u=S=>{r.value&&(s.value={x:S.clientX-o.value.x,y:S.clientY-o.value.y})},a=()=>{r.value=!1};return{logs:n,position:s,logPanel:i,startDrag:l,clearLogs:()=>{M.clear()},copyLogs:()=>{const S=n.value.map(C=>`[${C.timestamp}][${C.type}] ${C.message}${C.data?" - Data: "+C.data:""}`).join(`
`);navigator.clipboard.writeText(S).then(()=>{M.success("日志已复制到剪贴板")}).catch(C=>{M.error("复制失败",C)})},closeViewer:()=>{t("close")},testConnection:async()=>{try{const S=Date.now(),C=await fetch("http://localhost:3000"),P=Date.now()-S;C.ok?M.success(`连接成功 (${P}ms)`):M.error(`连接失败: HTTP ${C.status}`)}catch(S){M.error("连接失败",S.message)}},formatTime:S=>{const C=new Date(S);return`${C.getHours().toString().padStart(2,"0")}:${C.getMinutes().toString().padStart(2,"0")}:${C.getSeconds().toString().padStart(2,"0")}.${C.getMilliseconds().toString().padStart(3,"0")}`}}}},Vd={class:"log-controls"},qd={class:"log-body"},Wd={key:0,class:"no-logs"},Kd={key:1,class:"log-list"},zd={class:"log-time"},Jd={class:"log-content"},Gd={class:"log-message"},Qd={key:0,class:"log-data"};function Xd(e,t,n,s,r,o){return n.visible?(Ae(),Oe("div",{key:0,class:"log-viewer",style:gs({top:s.position.y+"px",left:s.position.x+"px"}),ref:"logPanel"},[pe("div",{class:"log-header",onMousedown:t[4]||(t[4]=(...i)=>s.startDrag&&s.startDrag(...i))},[t[5]||(t[5]=pe("h3",null,"开发者日志",-1)),pe("div",Vd,[pe("button",{onClick:t[0]||(t[0]=(...i)=>s.clearLogs&&s.clearLogs(...i)),class:"control-btn"},"清空"),pe("button",{onClick:t[1]||(t[1]=(...i)=>s.copyLogs&&s.copyLogs(...i)),class:"control-btn"},"复制"),pe("button",{onClick:t[2]||(t[2]=(...i)=>s.testConnection&&s.testConnection(...i)),class:"control-btn"},"测试连接"),pe("button",{onClick:t[3]||(t[3]=(...i)=>s.closeViewer&&s.closeViewer(...i)),class:"close-btn"},"×")])],32),pe("div",qd,[s.logs.length===0?(Ae(),Oe("div",Wd," 暂无日志信息 ")):(Ae(),Oe("div",Kd,[(Ae(!0),Oe(Je,null,Sa(s.logs,(i,c)=>(Ae(),Oe("div",{key:c,class:ln(["log-entry",i.type])},[pe("div",zd,bt(s.formatTime(i.timestamp)),1),pe("div",Jd,[pe("div",Gd,bt(i.message),1),i.data?(Ae(),Oe("div",Qd,bt(i.data),1)):yt("",!0)])],2))),128))]))])],4)):yt("",!0)}const Yd=Os(Hd,[["render",Xd],["__scopeId","data-v-098c9c40"]]),Zd={name:"DevToolsButton",components:{LogViewer:Yd},setup(){const e=ge(!1),t=ge(!1),n=qe(()=>{var o;const r=Md();return((o=r==null?void 0:r.orgs[0])==null?void 0:o.orgId)===2});return{isDevMode:e,showLogs:t,toggleDevTools:()=>{e.value=!e.value,t.value=e.value,e.value?M.info("开发者模式已启用"):M.info("开发者模式已关闭")},shouldShowDevTools:n}}},eh={key:0},th={class:"dev-text"};function nh(e,t,n,s,r,o){const i=Yi("LogViewer");return s.shouldShowDevTools?(Ae(),Oe("div",eh,[pe("button",{onClick:t[0]||(t[0]=(...c)=>s.toggleDevTools&&s.toggleDevTools(...c)),class:ln(["dev-tools-btn",{active:s.isDevMode}])},[t[2]||(t[2]=pe("span",{class:"dev-icon"},"⚙",-1)),pe("span",th,bt(s.isDevMode?"关闭开发者模式":"开发者模式"),1)],2),ve(i,{visible:s.showLogs,onClose:t[1]||(t[1]=c=>s.showLogs=!1)},null,8,["visible"])])):yt("",!0)}const sh=Os(Zd,[["render",nh],["__scopeId","data-v-0f4ba3ab"]]);class rh{constructor(){this.versionInfo={edition:"wanwei",appVersion:"1.0.0",buildDate:new Date().toISOString(),environment:"development",bypassEncryption:!1},this.versionCallbacks=new Set,this.isInitialized=!1,this.setupWebSocketListener()}setupWebSocketListener(){Se.addEventListener("version",t=>{t.action==="info"&&t.success&&t.data&&this.updateVersionInfo(t.data)})}updateVersionInfo(t){const n={...this.versionInfo};Object.assign(this.versionInfo,t),this.isInitialized=!0,M.info("版本信息已更新",{old:n,new:this.versionInfo}),this.versionCallbacks.forEach(s=>{try{s(this.versionInfo,n)}catch(r){M.error("版本信息回调执行失败",r)}})}getVersionInfo(){return{...this.versionInfo}}getEdition(){return this.versionInfo.edition}isWanweiEdition(){return this.versionInfo.edition==="wanwei"}isHexinEdition(){return this.versionInfo.edition==="hexin"}isSeniorEdition(){return this.versionInfo.edition==="wwsenior"}getVersionConfig(){const t={edition:this.versionInfo.edition,appVersion:this.versionInfo.appVersion,environment:this.versionInfo.environment};return this.isWanweiEdition()?{...t,appName:"万唯AI编辑WPS插件服务",shortName:"WPS插件",iconName:"ww.png",primaryColor:"#1890ff"}:this.isHexinEdition()?{...t,appName:"合心AI编辑WPS插件服务",shortName:"WPS插件",iconName:"hexin.png",primaryColor:"#1890ff"}:this.isSeniorEdition()?{...t,appName:"万唯AI编辑WPS插件服务",shortName:"WPS插件(高中)",iconName:"ww.png",primaryColor:"#1890ff"}:t}onVersionChange(t){if(this.versionCallbacks.add(t),this.isInitialized)try{t(this.versionInfo,null)}catch(n){M.error("版本信息回调执行失败",n)}return()=>{this.versionCallbacks.delete(t)}}waitForInitialization(t=5e3){return new Promise((n,s)=>{if(this.isInitialized){n(this.versionInfo);return}let r=null;const o=this.onVersionChange(i=>{r&&clearTimeout(r),o(),n(i)});r=setTimeout(()=>{o(),s(new Error("版本信息初始化超时"))},t)})}requestVersionInfo(){Se.isConnected?Se.sendMessage("version","getInfo",{}):M.warn("WebSocket未连接，无法请求版本信息")}setEdition(t){return new Promise((n,s)=>{if(!Se.isConnected){s(new Error("WebSocket未连接"));return}if(!t||t!=="wanwei"&&t!=="hexin"&&t!=="wwsenior"){s(new Error("无效的版本类型，必须是 wanwei, hexin 或 wwsenior"));return}Se.sendMessage("version","setEdition",{edition:t},r=>{r.success?(M.success("版本类型设置成功",r),n(r)):(M.error("版本类型设置失败",r),s(new Error(r.message||"设置失败")))})})}}const Gs=new rh,oh={class:"app"},ih={key:0,class:"ws-warning-overlay"},ch={class:"ws-warning-content"},lh={key:0},ah={key:1,class:"error-details"},uh={key:0},fh=["disabled"],dh={key:0,class:"loading-icon"},hh={__name:"App",setup(e){ge("当你看到这句话的时候，说明你已经成功安装了wps加载项，但是你访问的路径不对。");const t=ge(!1),n=ge(null),s=ge(!1),r=Fn({http:!1,ws:!1,httpMessage:"",wsMessage:""}),o=Qe("notification");let i=null,c=null;ge(""),ge(null);const l=ge("万唯AI编辑WPS插件服务"),u=ge(Gs.getVersionConfig()),a=async()=>{s.value=!0;const f=Date.now();try{const p=await Ko();r.http=!p.httpCheck.success,r.httpMessage=p.httpCheck.error||"",r.ws=!p.wsCheck.success,r.wsMessage=p.wsCheck.error||"",t.value=r.http||r.ws,p.success?M.success("连接测试成功",p):M.error("连接测试失败",p)}catch(p){M.error("连接测试出错",p),t.value=!0}setTimeout(()=>{const p=Date.now()-f,g=Math.max(0,300-p);setTimeout(()=>{s.value=!1},g)},0)};return Ss(async()=>{window.ribbon=nf,c=Gs.onVersionChange(f=>{M.info("版本信息已更新，更新UI显示",f);const p=Gs.getVersionConfig();u.value=p,l.value=p.appName}),i=$d(),Se.addEventListener("connection",f=>{f.status==="connected"?(r.ws=!1,r.wsMessage="",r.http||(t.value=!1)):f.status==="disconnected"&&(r.ws=!0,r.wsMessage=`连接关闭 (代码: ${f.code}${f.reason?", 原因: "+f.reason:""})`,t.value=!0)}),Se.addEventListener("error",f=>{r.ws=!0,r.wsMessage=f.error||"WebSocket错误",t.value=!0});try{const f=await Ko();r.http=!f.httpCheck.success,r.httpMessage=f.httpCheck.error||"",r.ws=!f.wsCheck.success,r.wsMessage=f.wsCheck.error||"",t.value=r.http||r.ws,f.success?M.success("初始连接测试成功",f):M.error("初始连接测试失败",f)}catch(f){M.error("初始连接测试出错",f),r.http=!0,r.httpMessage=f.message||"连接测试出错",r.ws=!0,r.wsMessage=f.message||"连接测试出错",t.value=!0}try{const f=await Yc();!f&&window.location.hash!=="#/login"?window.location.hash="#/login":f&&(M.success("已检测到登录状态",{currentHash:window.location.hash}),window.location.hash!=="#/taskpane"&&(window.location.hash="#/taskpane"),o.showNotify("登录成功","success"))}catch(f){if(f.message&&f.message.startsWith("AI_EDIT_DISABLED:")){const p=f.message.replace("AI_EDIT_DISABLED:","");M.warn("AI编辑功能未开启",p),o.showNotify(p,"warning"),window.location.hash="#/login"}else M.error("初始登录状态检查时出错",f),window.location.hash="#/login"}}),vs(()=>{i&&i(),c&&c(),n.value&&clearTimeout(n.value),o.cleanupNotification(),window._docCheckInterval&&(clearInterval(window._docCheckInterval),window._docCheckInterval=null)}),(f,p)=>{const g=Yi("RouterView");return Ae(),Oe("div",oh,[t.value?(Ae(),Oe("div",ih,[pe("div",ch,[p[0]||(p[0]=pe("h3",null,"连接失败",-1)),r.ws?(Ae(),Oe("p",lh,"WebSocket 连接失败")):yt("",!0),r.http||r.ws?(Ae(),Oe("div",ah,[r.ws?(Ae(),Oe("p",uh,bt(r.wsMessage),1)):yt("",!0)])):yt("",!0),pe("p",null,"请确保 "+bt(l.value)+" 已启动并正常运行。",1),pe("button",{onClick:a,class:"reconnect-btn",disabled:s.value},[s.value?(Ae(),Oe("span",dh)):yt("",!0),Sc(" "+bt(s.value?"连接中...":"重新连接"),1)],8,fh)])])):yt("",!0),ve(jd),ve(g),ve(sh)])}}},ph=Os(hh,[["__scopeId","data-v-aa303dcb"]]),gh="modulepreload",mh=function(e,t){return new URL(e,t).href},zo={},gn=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){const i=document.getElementsByTagName("link"),c=document.querySelector("meta[property=csp-nonce]"),l=(c==null?void 0:c.nonce)||(c==null?void 0:c.getAttribute("nonce"));r=Promise.allSettled(n.map(u=>{if(u=mh(u,s),u in zo)return;zo[u]=!0;const a=u.endsWith(".css"),f=a?'[rel="stylesheet"]':"";if(!!s)for(let y=i.length-1;y>=0;y--){const b=i[y];if(b.href===u&&(!a||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${f}`))return;const g=document.createElement("link");if(g.rel=a?"stylesheet":gh,a||(g.as="script"),g.crossOrigin="",g.href=u,l&&g.setAttribute("nonce",l),document.head.appendChild(g),a)return new Promise((y,b)=>{g.addEventListener("load",y),g.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${u}`)))})}))}function o(i){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i}return r.then(i=>{for(const c of i||[])c.status==="rejected"&&o(c.reason);return t().catch(o)})};/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Qt=typeof document<"u";function Zc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function yh(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Zc(e.default)}const ee=Object.assign;function Qs(e,t){const n={};for(const s in t){const r=t[s];n[s]=Ye(r)?r.map(e):e(r)}return n}const An=()=>{},Ye=Array.isArray,el=/#/g,wh=/&/g,bh=/\//g,Sh=/=/g,vh=/\?/g,tl=/\+/g,_h=/%5B/g,Eh=/%5D/g,nl=/%5E/g,Ah=/%60/g,sl=/%7B/g,Ch=/%7C/g,rl=/%7D/g,Rh=/%20/g;function jr(e){return encodeURI(""+e).replace(Ch,"|").replace(_h,"[").replace(Eh,"]")}function Th(e){return jr(e).replace(sl,"{").replace(rl,"}").replace(nl,"^")}function yr(e){return jr(e).replace(tl,"%2B").replace(Rh,"+").replace(el,"%23").replace(wh,"%26").replace(Ah,"`").replace(sl,"{").replace(rl,"}").replace(nl,"^")}function xh(e){return yr(e).replace(Sh,"%3D")}function Ih(e){return jr(e).replace(el,"%23").replace(vh,"%3F")}function Ph(e){return e==null?"":Ih(e).replace(bh,"%2F")}function Nn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Oh=/\/$/,Lh=e=>e.replace(Oh,"");function Xs(e,t,n="/"){let s,r={},o="",i="";const c=t.indexOf("#");let l=t.indexOf("?");return c<l&&c>=0&&(l=-1),l>-1&&(s=t.slice(0,l),o=t.slice(l+1,c>-1?c:t.length),r=e(o)),c>-1&&(s=s||t.slice(0,c),i=t.slice(c,t.length)),s=Mh(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:Nn(i)}}function kh(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Jo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Nh(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&rn(t.matched[s],n.matched[r])&&ol(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function rn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ol(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Dh(e[n],t[n]))return!1;return!0}function Dh(e,t){return Ye(e)?Go(e,t):Ye(t)?Go(t,e):e===t}function Go(e,t){return Ye(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Mh(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,c;for(i=0;i<s.length;i++)if(c=s[i],c!==".")if(c==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const Et={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Dn;(function(e){e.pop="pop",e.push="push"})(Dn||(Dn={}));var Cn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Cn||(Cn={}));function Fh(e){if(!e)if(Qt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Lh(e)}const $h=/^[^#]+#/;function Bh(e,t){return e.replace($h,"#")+t}function Uh(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Ls=()=>({left:window.scrollX,top:window.scrollY});function jh(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Uh(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Qo(e,t){return(history.state?history.state.position-t:-1)+e}const wr=new Map;function Hh(e,t){wr.set(e,t)}function Vh(e){const t=wr.get(e);return wr.delete(e),t}let qh=()=>location.protocol+"//"+location.host;function il(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let c=r.includes(e.slice(o))?e.slice(o).length:1,l=r.slice(c);return l[0]!=="/"&&(l="/"+l),Jo(l,"")}return Jo(n,e)+s+r}function Wh(e,t,n,s){let r=[],o=[],i=null;const c=({state:p})=>{const g=il(e,location),y=n.value,b=t.value;let S=0;if(p){if(n.value=g,t.value=p,i&&i===y){i=null;return}S=b?p.position-b.position:0}else s(g);r.forEach(C=>{C(n.value,y,{delta:S,type:Dn.pop,direction:S?S>0?Cn.forward:Cn.back:Cn.unknown})})};function l(){i=n.value}function u(p){r.push(p);const g=()=>{const y=r.indexOf(p);y>-1&&r.splice(y,1)};return o.push(g),g}function a(){const{history:p}=window;p.state&&p.replaceState(ee({},p.state,{scroll:Ls()}),"")}function f(){for(const p of o)p();o=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function Xo(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Ls():null}}function Kh(e){const{history:t,location:n}=window,s={value:il(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,u,a){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:qh()+e+l;try{t[a?"replaceState":"pushState"](u,"",p),r.value=u}catch(g){console.error(g),n[a?"replace":"assign"](p)}}function i(l,u){const a=ee({},t.state,Xo(r.value.back,l,r.value.forward,!0),u,{position:r.value.position});o(l,a,!0),s.value=l}function c(l,u){const a=ee({},r.value,t.state,{forward:l,scroll:Ls()});o(a.current,a,!0);const f=ee({},Xo(s.value,l,null),{position:a.position+1},u);o(l,f,!1),s.value=l}return{location:s,state:r,push:c,replace:i}}function zh(e){e=Fh(e);const t=Kh(e),n=Wh(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=ee({location:"",base:e,go:s,createHref:Bh.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Jh(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),zh(e)}function Gh(e){return typeof e=="string"||e&&typeof e=="object"}function cl(e){return typeof e=="string"||typeof e=="symbol"}const ll=Symbol("");var Yo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Yo||(Yo={}));function on(e,t){return ee(new Error,{type:e,[ll]:!0},t)}function ht(e,t){return e instanceof Error&&ll in e&&(t==null||!!(e.type&t))}const Zo="[^/]+?",Qh={sensitive:!1,strict:!1,start:!0,end:!0},Xh=/[.+*?^${}()[\]/\\]/g;function Yh(e,t){const n=ee({},Qh,t),s=[];let r=n.start?"^":"";const o=[];for(const u of e){const a=u.length?[]:[90];n.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const p=u[f];let g=40+(n.sensitive?.25:0);if(p.type===0)f||(r+="/"),r+=p.value.replace(Xh,"\\$&"),g+=40;else if(p.type===1){const{value:y,repeatable:b,optional:S,regexp:C}=p;o.push({name:y,repeatable:b,optional:S});const T=C||Zo;if(T!==Zo){g+=10;try{new RegExp(`(${T})`)}catch(L){throw new Error(`Invalid custom RegExp for param "${y}" (${T}): `+L.message)}}let P=b?`((?:${T})(?:/(?:${T}))*)`:`(${T})`;f||(P=S&&u.length<2?`(?:/${P})`:"/"+P),S&&(P+="?"),r+=P,g+=20,S&&(g+=-8),b&&(g+=-20),T===".*"&&(g+=-50)}a.push(g)}s.push(a)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function c(u){const a=u.match(i),f={};if(!a)return null;for(let p=1;p<a.length;p++){const g=a[p]||"",y=o[p-1];f[y.name]=g&&y.repeatable?g.split("/"):g}return f}function l(u){let a="",f=!1;for(const p of e){(!f||!a.endsWith("/"))&&(a+="/"),f=!1;for(const g of p)if(g.type===0)a+=g.value;else if(g.type===1){const{value:y,repeatable:b,optional:S}=g,C=y in u?u[y]:"";if(Ye(C)&&!b)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const T=Ye(C)?C.join("/"):C;if(!T)if(S)p.length<2&&(a.endsWith("/")?a=a.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);a+=T}}return a||"/"}return{re:i,score:s,keys:o,parse:c,stringify:l}}function Zh(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function al(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Zh(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(ei(s))return 1;if(ei(r))return-1}return r.length-s.length}function ei(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ep={type:0,value:""},tp=/[a-zA-Z0-9_]/;function np(e){if(!e)return[[]];if(e==="/")return[[ep]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let c=0,l,u="",a="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:a,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=l}for(;c<e.length;){if(l=e[c++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(u&&f(),i()):l===":"?(f(),n=1):p();break;case 4:p(),n=s;break;case 1:l==="("?n=2:tp.test(l)?p():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--);break;case 2:l===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+l:n=3:a+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),r}function sp(e,t,n){const s=Yh(np(e.path),n),r=ee(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function rp(e,t){const n=[],s=new Map;t=ri({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,p,g){const y=!g,b=ni(f);b.aliasOf=g&&g.record;const S=ri(t,f),C=[b];if("alias"in f){const L=typeof f.alias=="string"?[f.alias]:f.alias;for(const j of L)C.push(ni(ee({},b,{components:g?g.record.components:b.components,path:j,aliasOf:g?g.record:b})))}let T,P;for(const L of C){const{path:j}=L;if(p&&j[0]!=="/"){const z=p.record.path,J=z[z.length-1]==="/"?"":"/";L.path=p.record.path+(j&&J+j)}if(T=sp(L,p,S),g?g.alias.push(T):(P=P||T,P!==T&&P.alias.push(T),y&&f.name&&!si(T)&&i(f.name)),ul(T)&&l(T),b.children){const z=b.children;for(let J=0;J<z.length;J++)o(z[J],T,g&&g.children[J])}g=g||T}return P?()=>{i(P)}:An}function i(f){if(cl(f)){const p=s.get(f);p&&(s.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function c(){return n}function l(f){const p=cp(f,n);n.splice(p,0,f),f.record.name&&!si(f)&&s.set(f.record.name,f)}function u(f,p){let g,y={},b,S;if("name"in f&&f.name){if(g=s.get(f.name),!g)throw on(1,{location:f});S=g.record.name,y=ee(ti(p.params,g.keys.filter(P=>!P.optional).concat(g.parent?g.parent.keys.filter(P=>P.optional):[]).map(P=>P.name)),f.params&&ti(f.params,g.keys.map(P=>P.name))),b=g.stringify(y)}else if(f.path!=null)b=f.path,g=n.find(P=>P.re.test(b)),g&&(y=g.parse(b),S=g.record.name);else{if(g=p.name?s.get(p.name):n.find(P=>P.re.test(p.path)),!g)throw on(1,{location:f,currentLocation:p});S=g.record.name,y=ee({},p.params,f.params),b=g.stringify(y)}const C=[];let T=g;for(;T;)C.unshift(T.record),T=T.parent;return{name:S,path:b,params:y,matched:C,meta:ip(C)}}e.forEach(f=>o(f));function a(){n.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:a,getRoutes:c,getRecordMatcher:r}}function ti(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function ni(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:op(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function op(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function si(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ip(e){return e.reduce((t,n)=>ee(t,n.meta),{})}function ri(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function cp(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;al(e,t[o])<0?s=o:n=o+1}const r=lp(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function lp(e){let t=e;for(;t=t.parent;)if(ul(t)&&al(e,t)===0)return t}function ul({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function ap(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(tl," "),i=o.indexOf("="),c=Nn(i<0?o:o.slice(0,i)),l=i<0?null:Nn(o.slice(i+1));if(c in t){let u=t[c];Ye(u)||(u=t[c]=[u]),u.push(l)}else t[c]=l}return t}function oi(e){let t="";for(let n in e){const s=e[n];if(n=xh(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ye(s)?s.map(o=>o&&yr(o)):[s&&yr(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function up(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Ye(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const fp=Symbol(""),ii=Symbol(""),Hr=Symbol(""),fl=Symbol(""),br=Symbol("");function mn(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Tt(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((c,l)=>{const u=p=>{p===!1?l(on(4,{from:n,to:t})):p instanceof Error?l(p):Gh(p)?l(on(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),c())},a=o(()=>e.call(s&&s.instances[r],t,n,u));let f=Promise.resolve(a);e.length<3&&(f=f.then(u)),f.catch(p=>l(p))})}function Ys(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const c in i.components){let l=i.components[c];if(!(t!=="beforeRouteEnter"&&!i.instances[c]))if(Zc(l)){const a=(l.__vccOpts||l)[t];a&&o.push(Tt(a,n,s,i,c,r))}else{let u=l();o.push(()=>u.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${c}" at "${i.path}"`);const f=yh(a)?a.default:a;i.mods[c]=a,i.components[c]=f;const g=(f.__vccOpts||f)[t];return g&&Tt(g,n,s,i,c,r)()}))}}return o}function ci(e){const t=Qe(Hr),n=Qe(fl),s=qe(()=>{const l=en(e.to);return t.resolve(l)}),r=qe(()=>{const{matched:l}=s.value,{length:u}=l,a=l[u-1],f=n.matched;if(!a||!f.length)return-1;const p=f.findIndex(rn.bind(null,a));if(p>-1)return p;const g=li(l[u-2]);return u>1&&li(a)===g&&f[f.length-1].path!==g?f.findIndex(rn.bind(null,l[u-2])):p}),o=qe(()=>r.value>-1&&mp(n.params,s.value.params)),i=qe(()=>r.value>-1&&r.value===n.matched.length-1&&ol(n.params,s.value.params));function c(l={}){if(gp(l)){const u=t[en(e.replace)?"replace":"push"](en(e.to)).catch(An);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:qe(()=>s.value.href),isActive:o,isExactActive:i,navigate:c}}function dp(e){return e.length===1?e[0]:e}const hp=Ji({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:ci,setup(e,{slots:t}){const n=Fn(ci(e)),{options:s}=Qe(Hr),r=qe(()=>({[ai(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[ai(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&dp(t.default(n));return e.custom?o:Mr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),pp=hp;function gp(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function mp(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Ye(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function li(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ai=(e,t,n)=>e??t??n,yp=Ji({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Qe(br),r=qe(()=>e.route||s.value),o=Qe(ii,0),i=qe(()=>{let u=en(o);const{matched:a}=r.value;let f;for(;(f=a[u])&&!f.components;)u++;return u}),c=qe(()=>r.value.matched[i.value]);Jn(ii,qe(()=>i.value+1)),Jn(fp,c),Jn(br,r);const l=ge();return Gn(()=>[l.value,c.value,e.name],([u,a,f],[p,g,y])=>{a&&(a.instances[f]=u,g&&g!==a&&u&&u===p&&(a.leaveGuards.size||(a.leaveGuards=g.leaveGuards),a.updateGuards.size||(a.updateGuards=g.updateGuards))),u&&a&&(!g||!rn(a,g)||!p)&&(a.enterCallbacks[f]||[]).forEach(b=>b(u))},{flush:"post"}),()=>{const u=r.value,a=e.name,f=c.value,p=f&&f.components[a];if(!p)return ui(n.default,{Component:p,route:u});const g=f.props[a],y=g?g===!0?u.params:typeof g=="function"?g(u):g:null,S=Mr(p,ee({},y,t,{onVnodeUnmounted:C=>{C.component.isUnmounted&&(f.instances[a]=null)},ref:l}));return ui(n.default,{Component:S,route:u})||S}}});function ui(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const wp=yp;function bp(e){const t=rp(e.routes,e),n=e.parseQuery||ap,s=e.stringifyQuery||oi,r=e.history,o=mn(),i=mn(),c=mn(),l=zl(Et);let u=Et;Qt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=Qs.bind(null,_=>""+_),f=Qs.bind(null,Ph),p=Qs.bind(null,Nn);function g(_,D){let k,$;return cl(_)?(k=t.getRecordMatcher(_),$=D):$=_,t.addRoute($,k)}function y(_){const D=t.getRecordMatcher(_);D&&t.removeRoute(D)}function b(){return t.getRoutes().map(_=>_.record)}function S(_){return!!t.getRecordMatcher(_)}function C(_,D){if(D=ee({},D||l.value),typeof _=="string"){const m=Xs(n,_,D.path),v=t.resolve({path:m.path},D),A=r.createHref(m.fullPath);return ee(m,v,{params:p(v.params),hash:Nn(m.hash),redirectedFrom:void 0,href:A})}let k;if(_.path!=null)k=ee({},_,{path:Xs(n,_.path,D.path).path});else{const m=ee({},_.params);for(const v in m)m[v]==null&&delete m[v];k=ee({},_,{params:f(m)}),D.params=f(D.params)}const $=t.resolve(k,D),re=_.hash||"";$.params=a(p($.params));const d=kh(s,ee({},_,{hash:Th(re),path:$.path})),h=r.createHref(d);return ee({fullPath:d,hash:re,query:s===oi?up(_.query):_.query||{}},$,{redirectedFrom:void 0,href:h})}function T(_){return typeof _=="string"?Xs(n,_,l.value.path):ee({},_)}function P(_,D){if(u!==_)return on(8,{from:D,to:_})}function L(_){return J(_)}function j(_){return L(ee(T(_),{replace:!0}))}function z(_){const D=_.matched[_.matched.length-1];if(D&&D.redirect){const{redirect:k}=D;let $=typeof k=="function"?k(_):k;return typeof $=="string"&&($=$.includes("?")||$.includes("#")?$=T($):{path:$},$.params={}),ee({query:_.query,hash:_.hash,params:$.path!=null?{}:_.params},$)}}function J(_,D){const k=u=C(_),$=l.value,re=_.state,d=_.force,h=_.replace===!0,m=z(k);if(m)return J(ee(T(m),{state:typeof m=="object"?ee({},re,m.state):re,force:d,replace:h}),D||k);const v=k;v.redirectedFrom=D;let A;return!d&&Nh(s,$,k)&&(A=on(16,{to:v,from:$}),et($,$,!0,!1)),(A?Promise.resolve(A):Q(v,$)).catch(E=>ht(E)?ht(E,2)?E:vt(E):Z(E,v,$)).then(E=>{if(E){if(ht(E,2))return J(ee({replace:h},T(E.to),{state:typeof E.to=="object"?ee({},re,E.to.state):re,force:d}),D||v)}else E=F(v,$,!0,h,re);return ce(v,$,E),E})}function fe(_,D){const k=P(_,D);return k?Promise.reject(k):Promise.resolve()}function q(_){const D=zt.values().next().value;return D&&typeof D.runWithContext=="function"?D.runWithContext(_):_()}function Q(_,D){let k;const[$,re,d]=Sp(_,D);k=Ys($.reverse(),"beforeRouteLeave",_,D);for(const m of $)m.leaveGuards.forEach(v=>{k.push(Tt(v,_,D))});const h=fe.bind(null,_,D);return k.push(h),He(k).then(()=>{k=[];for(const m of o.list())k.push(Tt(m,_,D));return k.push(h),He(k)}).then(()=>{k=Ys(re,"beforeRouteUpdate",_,D);for(const m of re)m.updateGuards.forEach(v=>{k.push(Tt(v,_,D))});return k.push(h),He(k)}).then(()=>{k=[];for(const m of d)if(m.beforeEnter)if(Ye(m.beforeEnter))for(const v of m.beforeEnter)k.push(Tt(v,_,D));else k.push(Tt(m.beforeEnter,_,D));return k.push(h),He(k)}).then(()=>(_.matched.forEach(m=>m.enterCallbacks={}),k=Ys(d,"beforeRouteEnter",_,D,q),k.push(h),He(k))).then(()=>{k=[];for(const m of i.list())k.push(Tt(m,_,D));return k.push(h),He(k)}).catch(m=>ht(m,8)?m:Promise.reject(m))}function ce(_,D,k){c.list().forEach($=>q(()=>$(_,D,k)))}function F(_,D,k,$,re){const d=P(_,D);if(d)return d;const h=D===Et,m=Qt?history.state:{};k&&($||h?r.replace(_.fullPath,ee({scroll:h&&m&&m.scroll},re)):r.push(_.fullPath,re)),l.value=_,et(_,D,k,h),vt()}let Y;function me(){Y||(Y=r.listen((_,D,k)=>{if(!Hn.listening)return;const $=C(_),re=z($);if(re){J(ee(re,{replace:!0,force:!0}),$).catch(An);return}u=$;const d=l.value;Qt&&Hh(Qo(d.fullPath,k.delta),Ls()),Q($,d).catch(h=>ht(h,12)?h:ht(h,2)?(J(ee(T(h.to),{force:!0}),$).then(m=>{ht(m,20)&&!k.delta&&k.type===Dn.pop&&r.go(-1,!1)}).catch(An),Promise.reject()):(k.delta&&r.go(-k.delta,!1),Z(h,$,d))).then(h=>{h=h||F($,d,!1),h&&(k.delta&&!ht(h,8)?r.go(-k.delta,!1):k.type===Dn.pop&&ht(h,20)&&r.go(-1,!1)),ce($,d,h)}).catch(An)}))}let De=mn(),de=mn(),se;function Z(_,D,k){vt(_);const $=de.list();return $.length?$.forEach(re=>re(_,D,k)):console.error(_),Promise.reject(_)}function ut(){return se&&l.value!==Et?Promise.resolve():new Promise((_,D)=>{De.add([_,D])})}function vt(_){return se||(se=!_,me(),De.list().forEach(([D,k])=>_?k(_):D()),De.reset()),_}function et(_,D,k,$){const{scrollBehavior:re}=e;if(!Qt||!re)return Promise.resolve();const d=!k&&Vh(Qo(_.fullPath,0))||($||!k)&&history.state&&history.state.scroll||null;return Lr().then(()=>re(_,D,d)).then(h=>h&&jh(h)).catch(h=>Z(h,_,D))}const Ie=_=>r.go(_);let Kt;const zt=new Set,Hn={currentRoute:l,listening:!0,addRoute:g,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:S,getRoutes:b,resolve:C,options:e,push:L,replace:j,go:Ie,back:()=>Ie(-1),forward:()=>Ie(1),beforeEach:o.add,beforeResolve:i.add,afterEach:c.add,onError:de.add,isReady:ut,install(_){const D=this;_.component("RouterLink",pp),_.component("RouterView",wp),_.config.globalProperties.$router=D,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>en(l)}),Qt&&!Kt&&l.value===Et&&(Kt=!0,L(r.location).catch(re=>{}));const k={};for(const re in Et)Object.defineProperty(k,re,{get:()=>l.value[re],enumerable:!0});_.provide(Hr,D),_.provide(fl,Li(k)),_.provide(br,l);const $=_.unmount;zt.add(_),_.unmount=function(){zt.delete(_),zt.size<1&&(u=Et,Y&&Y(),Y=null,l.value=Et,Kt=!1,se=!1),$()}}};function He(_){return _.reduce((D,k)=>D.then(()=>q(k)),Promise.resolve())}return Hn}function Sp(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const c=t.matched[i];c&&(e.matched.find(u=>rn(u,c))?s.push(c):n.push(c));const l=e.matched[i];l&&(t.matched.find(u=>rn(u,l))||r.push(l))}return[n,s,r]}const dl=bp({history:Jh(""),routes:[{path:"/",name:"u9ed8u8ba4u9875",component:()=>gn(()=>import("./Root-DVISQiuB.js"),[],import.meta.url),meta:{requiresAuth:!1}},{path:"/login",name:"u767bu5f55",component:()=>gn(()=>import("./Login-MtSME_hR.js"),__vite__mapDeps([0,1]),import.meta.url),meta:{requiresAuth:!1}},{path:"/dashboard",name:"u4eeau8868u76d8",component:()=>gn(()=>import("./TaskPane-BTyRbBBz.js"),__vite__mapDeps([2,3]),import.meta.url),meta:{requiresAuth:!0}},{path:"/dialog",name:"u5bf9u8bddu6846",component:()=>gn(()=>import("./Dialog-BGVI8nNN.js"),__vite__mapDeps([4,5]),import.meta.url),meta:{requiresAuth:!0}},{path:"/taskpane",name:"u4efbu52a1u7a97u683c",component:()=>gn(()=>import("./TaskPane-BTyRbBBz.js"),__vite__mapDeps([2,3]),import.meta.url),meta:{requiresAuth:!0}}]});dl.beforeEach(async(e,t,n)=>{e.matched.some(r=>r.meta.requiresAuth)?await Yc()?n():n({path:"/login"}):n()});const vp={install:e=>{const t=Xc();e.provide("notification",t),e.config.globalProperties.$notify=t.showNotify}};Fd().then(e=>{}).catch(e=>{console.error("Initial session refresh error:",e)});const Vr=ju(ph);Vr.use(dl);Vr.use(vp);Vr.mount("#app");export{Rp as A,Re as B,Tp as C,Ap as D,ve as E,Je as F,Rc as U,Os as _,pe as a,Op as b,Oe as c,Ip as d,Ep as e,yt as f,xp as g,Fn as h,Se as i,ue as j,Ss as k,M as l,Gn as m,Pp as n,Ae as o,Sc as p,ln as q,ge as r,Cp as s,bt as t,Sa as u,Gs as v,_p as w,qe as x,Xi as y,en as z};
