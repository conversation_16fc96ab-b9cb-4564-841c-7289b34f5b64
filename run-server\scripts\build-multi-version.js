const fs = require('fs');
const path = require('path');
const cp = require('child_process');
const chalk = require('chalk');
const { updateVersionInfo } = require('./update-version-info');

// 构建配置
const buildConfigs = [
    {
        name: '万唯版本',
        config: 'electron-builder.yml',
        outputDir: 'dist',
        envConfig: 'env-config/production.json'
    },
    {
        name: '合心版本',
        config: 'electron-builder-hexin.yml',
        outputDir: 'dist-hexin',
        envConfig: 'env-config/hexin-production.json'
    },
    {
        name: '万唯高中版本',
        config: 'electron-builder-wwsenior.yml',
        outputDir: 'dist-wwsenior',
        envConfig: 'env-config/wwsenior.json'
    }
];

// 执行命令的辅助函数
function execCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
        console.log(chalk.blue(`执行命令: ${command}`));

        const child = cp.exec(command, {
            encoding: 'utf8',
            maxBuffer: 10 * 1024 * 1024, // 10MB buffer
            ...options
        });

        child.stdout.on('data', (data) => {
            process.stdout.write(data);
        });

        child.stderr.on('data', (data) => {
            process.stderr.write(data);
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`命令执行失败，退出代码: ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

// 清理目录
function cleanDirectory(dir) {
    if (fs.existsSync(dir)) {
        console.log(chalk.yellow(`清理目录: ${dir}`));
        fs.rmSync(dir, { recursive: true, force: true });
    }
}

// 验证环境配置文件
function validateConfig(configPath) {
    if (!fs.existsSync(configPath)) {
        throw new Error(`环境配置文件不存在: ${configPath}`);
    }

    try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        console.log(chalk.green(`✓ 配置文件验证通过: ${configPath}`));
        console.log(chalk.gray(`  版本: ${config.EDITION || 'unknown'}`));
        console.log(chalk.gray(`  应用名: ${config.APP_NAME || 'unknown'}`));
        return config;
    } catch (error) {
        throw new Error(`配置文件格式错误: ${configPath} - ${error.message}`);
    }
}

// 构建单个版本
async function buildVersion(buildConfig) {
    console.log(chalk.cyan(`\n=== 开始构建 ${buildConfig.name} ===`));

    try {
        // 验证配置文件
        validateConfig(buildConfig.envConfig);

        // 清理输出目录
        cleanDirectory(buildConfig.outputDir);

        // 执行构建
        const buildCommand = `electron-builder --config ${buildConfig.config} --win`;
        await execCommand(buildCommand);

        // 验证构建结果
        if (fs.existsSync(buildConfig.outputDir)) {
            const files = fs.readdirSync(buildConfig.outputDir);
            console.log(chalk.green(`✓ ${buildConfig.name} 构建完成`));
            console.log(chalk.gray(`  输出目录: ${buildConfig.outputDir}`));
            console.log(chalk.gray(`  生成文件: ${files.length} 个`));
        } else {
            throw new Error(`构建完成但输出目录不存在: ${buildConfig.outputDir}`);
        }

    } catch (error) {
        console.error(chalk.red(`✗ ${buildConfig.name} 构建失败:`), error.message);
        throw error;
    }
}

// 主构建函数
async function buildAllVersions() {
    console.log(chalk.magenta('=== 多版本构建开始 ==='));
    console.log(chalk.blue(`计划构建 ${buildConfigs.length} 个版本`));

    const startTime = Date.now();
    const results = [];

    try {
        // 更新版本信息
        console.log(chalk.blue('\n更新版本信息...'));
        if (!updateVersionInfo()) {
            throw new Error('版本信息更新失败');
        }

        // 验证必要文件
        console.log(chalk.blue('\n检查必要文件...'));
        for (const config of buildConfigs) {
            if (!fs.existsSync(config.config)) {
                throw new Error(`构建配置文件不存在: ${config.config}`);
            }
            if (!fs.existsSync(config.envConfig)) {
                throw new Error(`环境配置文件不存在: ${config.envConfig}`);
            }
        }

        // 逐个构建版本
        for (const config of buildConfigs) {
            try {
                await buildVersion(config);
                results.push({ name: config.name, success: true });
            } catch (error) {
                results.push({
                    name: config.name,
                    success: false,
                    error: error.message
                });

                // 决定是否继续构建其他版本
                console.log(chalk.yellow(`${config.name} 构建失败，继续构建其他版本...`));
            }
        }

        // 显示构建结果
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        console.log(chalk.magenta('\n=== 构建结果汇总 ==='));
        console.log(chalk.blue(`总耗时: ${duration} 秒`));

        let successCount = 0;
        for (const result of results) {
            if (result.success) {
                console.log(chalk.green(`✓ ${result.name}: 构建成功`));
                successCount++;
            } else {
                console.log(chalk.red(`✗ ${result.name}: 构建失败 - ${result.error}`));
            }
        }

        console.log(chalk.blue(`\n成功构建: ${successCount}/${results.length} 个版本`));

        // 为成功构建的版本生成更新日志
        if (successCount > 0) {
            console.log(chalk.cyan('\n📝 正在生成更新日志...'));
            try {
                const generateReleaseNotes = require('./generate-release-notes-advanced');
                // 调用更新日志生成脚本
                const { execSync } = require('child_process');
                execSync('node scripts/generate-release-notes-advanced.js', { cwd: process.cwd(), stdio: 'inherit' });
                console.log(chalk.green('✓ 更新日志生成完成'));
            } catch (error) {
                console.log(chalk.yellow('⚠️  更新日志生成失败:', error.message));
            }
        }

        if (successCount === results.length) {
            console.log(chalk.green('\n🎉 所有版本构建完成！'));
            console.log(chalk.cyan('\n下一步: 运行 npm run upload 上传构建产物'));
        } else if (successCount > 0) {
            console.log(chalk.yellow('\n⚠️  部分版本构建完成，请检查失败的版本'));
        } else {
            console.log(chalk.red('\n❌ 所有版本构建失败'));
            process.exit(1);
        }

    } catch (error) {
        console.error(chalk.red('\n构建过程出错:'), error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    buildAllVersions().catch(error => {
        console.error(chalk.red('构建失败:'), error);
        process.exit(1);
    });
}

module.exports = { buildAllVersions };
