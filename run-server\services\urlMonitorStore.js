// 导入必要的模块
const Database = require('better-sqlite3') // 引入 better-sqlite3
const { v4: uuidv4 } = require('uuid')
const path = require('path')
const { defaultLogger } = require('../utils/logger') // 假设 logger 保持不变
const configStore = require('./configStore') // 仍然用于获取初始路径等
const fs = require('fs')
const { app } = require('electron')

// SQLite 数据库文件名
const DB_NAME = 'url_monitor.sqlite'

class UrlMonitorStore {
  constructor() {
    // RxDB 数据库文件的存储目录，现在是 SQLite 文件的存储目录
    const dbDir = path.join(app.getPath('userData'), 'db') // 使用旧 configStore 的路径作为基础
    this.dbPath = path.join(dbDir, DB_NAME)
    this.db = null // SQLite 数据库实例
    defaultLogger.info(`SQLite 数据库路径设置为: ${this.dbPath}`)
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true }) // 确保目录存在
      defaultLogger.info(`创建 SQLite 数据库目录: ${dbDir}`)
    }
  }

  /**
   * 初始化 SQLite 数据库和表结构
   * 这个方法必须在使用任何其他数据库方法之前调用。
   */
  async init() {
    if (this.db) {
      defaultLogger.info('SQLite 数据库已经初始化。')
      return
    }

    try {
      this.db = new Database(this.dbPath /*, { verbose: console.log }*/) // 初始化数据库连接
      defaultLogger.info('SQLite 数据库实例已创建。')

      // 创建表结构 (如果不存在)
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS url_tasks
        (
          urlId        TEXT PRIMARY KEY,
          url          TEXT NOT NULL,
          label        TEXT,
          taskId       TEXT,
          status       TEXT NOT NULL DEFAULT 'pending',
          lastCheck    TEXT,
          nextCheck    TEXT,
          interval     INTEGER,
          creationDate TEXT NOT NULL,
          checkStarted BOOLEAN       DEFAULT 0,
          localFilePath TEXT,
          appKey       TEXT,
          ticketId     TEXT
        );

        CREATE INDEX IF NOT EXISTS idx_url_tasks_status ON url_tasks (status);
        CREATE INDEX IF NOT EXISTS idx_url_tasks_taskId ON url_tasks (taskId);

        CREATE TABLE IF NOT EXISTS config
        (
          configId             TEXT PRIMARY KEY,
          downloadPath         TEXT,
          nextUrlTaskIdCounter INTEGER DEFAULT 1
        );

        CREATE TABLE IF NOT EXISTS task_associations
        (
          associationId    TEXT PRIMARY KEY,
          businessTaskId   TEXT NOT NULL,
          urlMonitorTaskId TEXT NOT NULL,
          FOREIGN KEY (urlMonitorTaskId) REFERENCES url_tasks (urlId) ON DELETE CASCADE,
          UNIQUE (businessTaskId, urlMonitorTaskId)
        );
        CREATE INDEX IF NOT EXISTS idx_task_associations_businessTaskId ON task_associations (businessTaskId);
        CREATE INDEX IF NOT EXISTS idx_task_associations_urlMonitorTaskId ON task_associations (urlMonitorTaskId);
      `)

      defaultLogger.info('SQLite 表已成功创建/加载。')

      // Check and add 'checkStarted' column to url_tasks if it doesn't exist
      try {
        const columns = this.db.pragma('table_info(url_tasks)')
        const hasCheckStarted = columns.some((col) => col.name === 'checkStarted')
        if (!hasCheckStarted) {
          this.db.exec('ALTER TABLE url_tasks ADD COLUMN checkStarted BOOLEAN DEFAULT 0')
          defaultLogger.info("'checkStarted' column added to 'url_tasks' table.")
        }
        
        // 检查并添加 localFilePath 列（如果不存在）
        const hasLocalFilePath = columns.some((col) => col.name === 'localFilePath')
        if (!hasLocalFilePath) {
          this.db.exec('ALTER TABLE url_tasks ADD COLUMN localFilePath TEXT')
          defaultLogger.info("'localFilePath' column added to 'url_tasks' table.")
        }
        
        // 检查并添加 appKey 列（如果不存在）
        const hasAppKey = columns.some((col) => col.name === 'appKey')
        if (!hasAppKey) {
          this.db.exec('ALTER TABLE url_tasks ADD COLUMN appKey TEXT')
          defaultLogger.info("'appKey' column added to 'url_tasks' table.")
        }
        
        // 检查并添加 ticketId 列（如果不存在）
        const hasTicketId = columns.some((col) => col.name === 'ticketId')
        if (!hasTicketId) {
          this.db.exec('ALTER TABLE url_tasks ADD COLUMN ticketId TEXT')
          defaultLogger.info("'ticketId' column added to 'url_tasks' table.")
        }
      } catch (error) {
        defaultLogger.error(
          `Failed to check/add columns to url_tasks: ${error.message}`
        )
      }

      // 初始化默认配置 (如果尚不存在)
      await this._initializeDefaultConfig()
    } catch (error) {
      defaultLogger.error(`初始化 SQLite 失败: ${error.message} \nStack: ${error.stack}`)
      throw error
    }
  }

  /**
   * 初始化默认配置 (如果 config 表为空或没有对应项)
   * @private
   */
  async _initializeDefaultConfig() {
    const configId = 'singletonConfig'
    try {
      const stmt = this.db.prepare('SELECT * FROM config WHERE configId = ?')
      const existingConfig = stmt.get(configId)

      if (!existingConfig) {
        const insertStmt = this.db.prepare(
          'INSERT INTO config (configId, downloadPath, nextUrlTaskIdCounter) VALUES (?, ?, ?)'
        )
        insertStmt.run(configId, configStore.getUrlDownloadPath(), 1)
        defaultLogger.info('默认 SQLite 配置已初始化。')
      } else {
        defaultLogger.info('SQLite 配置已存在。')
      }
    } catch (error) {
      defaultLogger.error(`初始化/检查默认配置失败: ${error.message}`)
    }
  }

  /**
   * 获取下一个自定义任务ID (例如 "url_abcd1234")
   * @returns {Promise<string>} 新的任务ID，格式为 url_ 前缀加8位UUID
   * @private
   */
  async getNextTaskId() {
    try {
      // 生成一个UUID并取前8位
      const uuid = uuidv4().substring(0, 8)
      return `url_${uuid}`
    } catch (error) {
      defaultLogger.error(`获取下一个自定义任务ID失败: ${error.message}`)
      return `url_uuid_${uuidv4().substring(0, 8)}` // 回退策略
    }
  }

  /**
   * 保存URL任务，如果urlId已存在则更新
   * @param {string} urlId - URL监控ID
   * @param {Object} urlData - URL监控数据
   * @returns {Promise<Object|null>} 保存或更新的任务对象 或 null
   */
  async saveTask(urlId, urlData) {
    if (!this.db) await this.init() // 确保数据库已初始化
    if (!urlData || !urlData.url || !urlId) {
      defaultLogger.error('保存URL任务时缺少必要参数 (url)。')
      return null
    }
    try {
      // 首先检查是否存在该urlId的任务
      const existingTask = await this.getTask(urlId)

      if (existingTask) {
        return await this.updateTask(urlId, urlData)
      }

      // 任务不存在，创建新任务
      const taskToInsert = {
        urlId: urlId,
        url: String(urlData.url || ''), // Ensure string, handle if urlData.url is null/undefined
        label: typeof urlData.label === 'string' ? urlData.label : null,
        taskId: typeof urlData.taskId === 'string' ? urlData.taskId : null,
        status: typeof urlData.status === 'string' ? urlData.status : 'pending',
        lastCheck: typeof urlData.lastCheck === 'string' ? urlData.lastCheck : null,
        nextCheck: typeof urlData.nextCheck === 'string' ? urlData.nextCheck : null,
        interval:
          typeof urlData.interval === 'number' && Number.isFinite(urlData.interval)
            ? Math.floor(urlData.interval)
            : null,
        creationDate: new Date().toISOString(),
        checkStarted: urlData.checkStarted === true || urlData.checkStarted === 1 ? 1 : 0, // Convert boolean to integer 1 or 0
        localFilePath: typeof urlData.localFilePath === 'string' ? urlData.localFilePath : null,
        appKey: typeof urlData.appKey === 'string' ? urlData.appKey : null, // 保存 appKey 字段
        ticketId: typeof urlData.ticketId === 'string' ? urlData.ticketId : null // 保存 ticketId 字段
      }

      // Validate required url field after coercion
      if (!taskToInsert.url) {
        defaultLogger.error('URL任务的URL无效或缺失，无法保存。')
        return null
      }
      const stmt = this.db.prepare(`
        INSERT INTO url_tasks (urlId, url, label, taskId, status, lastCheck, nextCheck, interval, creationDate,
                               checkStarted, localFilePath, appKey, ticketId)
        VALUES (@urlId, @url, @label, @taskId, @status, @lastCheck, @nextCheck, @interval, @creationDate, @checkStarted, @localFilePath, @appKey, @ticketId)
      `)
      stmt.run(taskToInsert)
      defaultLogger.info(`URL任务 ${taskToInsert.urlId} 已保存到 SQLite。`)

      // 如果提供了业务任务ID，尝试关联，但不阻止整个保存过程
      if (urlData.taskId) {
        try {
          await this.associateUrlWithTask(taskToInsert.urlId, urlData.taskId)
        } catch (error) {
          defaultLogger.warn(`保存URL任务成功，但关联业务任务失败: ${error.message}`)
        }
      }
      return taskToInsert // 返回插入的对象
    } catch (error) {
      defaultLogger.error(`保存URL任务失败: ${error.message} \nStack: ${error.stack}`)
      return null
    }
  }

  /**
   * 获取URL任务
   * @param {string} urlId - URL监控ID
   * @returns {Promise<Object|null>} URL任务数据，如果不存在返回null
   */
  async getTask(urlId) {
    if (!this.db) await this.init()
    if (!urlId) return null
    try {
      const stmt = this.db.prepare('SELECT * FROM url_tasks WHERE urlId = ?')
      const task = stmt.get(urlId)
      return task || null
    } catch (error) {
      defaultLogger.error(`获取URL任务 ${urlId} 失败: ${error.message}`)
      return null
    }
  }

  /**
   * 获取所有URL任务
   * @returns {Promise<Array<Object>>} 所有URL任务的数组
   */
  async getAllTasks() {
    if (!this.db) await this.init()
    try {
      const stmt = this.db.prepare('SELECT * FROM url_tasks')
      const tasks = stmt.all()
      return tasks
    } catch (error) {
      defaultLogger.error(`获取所有URL任务失败: ${error.message}`)
      return []
    }
  }

  /**
   * 删除URL任务及其所有关联 (通过外键的 ON DELETE CASCADE 实现部分关联删除)
   * @param {string} urlId - URL监控ID
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteTask(urlId) {
    if (!this.db) await this.init()
    if (!urlId) return false
    try {
      // 外键约束 ON DELETE CASCADE 会自动删除 task_associations 中关联的记录
      const stmt = this.db.prepare('DELETE FROM url_tasks WHERE urlId = ?')
      const result = stmt.run(urlId)

      if (result.changes > 0) {
        defaultLogger.info(`URL任务 ${urlId} 已从 SQLite 中删除 (关联数据通过 CASCADE 删除)。`)
        return true
      } else {
        defaultLogger.warn(`尝试删除不存在的 URL 任务 ${urlId}。`)
        return false
      }
    } catch (error) {
      defaultLogger.error(`删除URL任务 ${urlId} 失败: ${error.message}`)
      return false
    }
  }

  /**
   * 更新URL任务
   * @param {string} urlId - URL监控ID
   * @param {Object} updateData - 要更新的数据
   * @returns {Promise<Object|null>} 更新后的任务对象 或 null
   */
  async updateTask(urlId, updateData) {
    if (!this.db) await this.init()
    if (!urlId || !updateData || Object.keys(updateData).length === 0) return null

    try {
      const currentTask = await this.getTask(urlId)
      if (!currentTask) {
        defaultLogger.warn(`尝试更新不存在的 URL 任务 ${urlId}。`)
        return null
      }

      // 构建 SET 字句
      const setClauses = []
      const params = []
      for (const key in updateData) {
        if (updateData.hasOwnProperty(key) && key !== 'urlId' && currentTask.hasOwnProperty(key)) {
          setClauses.push(`${key} = ?`)
          params.push(updateData[key])
        }
      }
      if (setClauses.length === 0) {
        defaultLogger.info(`没有有效的字段用于更新 URL 任务 ${urlId}。`)
        return currentTask // 返回当前任务，因为没有更新发生
      }
      params.push(urlId) // 添加 urlId 到参数末尾用于 WHERE 子句

      const stmt = this.db.prepare(`UPDATE url_tasks
                                    SET ${setClauses.join(', ')}
                                    WHERE urlId = ?`)
      stmt.run(...params)
      defaultLogger.info(`URL任务 ${urlId} 已更新。`)

      // 处理 taskId 关联的逻辑 (如果 taskId 被更新)
      if (updateData.hasOwnProperty('taskId')) {
        const newBusinessTaskId = updateData.taskId
        const oldBusinessTaskId = currentTask.taskId

        if (newBusinessTaskId !== oldBusinessTaskId) {
          if (oldBusinessTaskId) {
            // 移除旧的关联
            await this.dissociateUrlFromTask(urlId, oldBusinessTaskId)
          }
          if (newBusinessTaskId) {
            // 添加新的关联
            await this.associateUrlWithTask(urlId, newBusinessTaskId)
          }
        }
      }
      return this.getTask(urlId) // 返回更新后的任务
    } catch (error) {
      defaultLogger.error(`更新URL任务 ${urlId} 失败: ${error.message}`)
      return null
    }
  }

  /**
   * 设置全局下载路径
   * @param {string} downloadPath - 下载路径
   * @returns {Promise<boolean>} 是否成功设置
   */
  async setDownloadPath(downloadPath) {
    if (!this.db) await this.init()
    if (typeof downloadPath !== 'string') return false
    const configId = 'singletonConfig'
    try {
      const stmt = this.db.prepare('UPDATE config SET downloadPath = ? WHERE configId = ?')
      const result = stmt.run(downloadPath, configId)
      if (result.changes === 0) {
        // 如果没有更新，可能是因为 configId 不存在
        await this._initializeDefaultConfig() // 确保配置存在
        const retryResult = stmt.run(downloadPath, configId) // 再次尝试
        if (retryResult.changes === 0) throw new Error('无法更新下载路径，即使在初始化配置后。')
      }
      defaultLogger.info(`全局URL下载路径已更新为: ${downloadPath}`)
      return true
    } catch (error) {
      defaultLogger.error(`设置下载路径失败: ${error.message}`)
      return false
    }
  }

  /**
   * 获取全局下载路径
   * @returns {Promise<string|null>} 下载路径或null
   */
  async getDownloadPath() {
    if (!this.db) await this.init()
    const configId = 'singletonConfig'
    try {
      const stmt = this.db.prepare('SELECT downloadPath FROM config WHERE configId = ?')
      const config = stmt.get(configId)
      if (!config) {
        await this._initializeDefaultConfig()
        const newConfig = stmt.get(configId)
        return newConfig ? newConfig.downloadPath : configStore.getUrlDownloadPath() // Fallback
      }
      return config.downloadPath
    } catch (error) {
      defaultLogger.error(`获取下载路径失败: ${error.message}`)
      return configStore.getUrlDownloadPath() // Fallback
    }
  }

  // --- 关联方法 (需要用 SQL 重写) ---
  async associateUrlWithTask(urlMonitorTaskId, businessTaskId) {
    defaultLogger.info(
      `[UrlMonitorStore] Attempting to associate urlMonitorTaskId: ${urlMonitorTaskId} with businessTaskId: ${businessTaskId}`
    ) // Added log
    if (!this.db) await this.init()
    if (!urlMonitorTaskId || !businessTaskId) return null
    try {
      // 首先验证URL任务是否存在
      const urlTask = await this.getTask(urlMonitorTaskId)
      if (!urlTask) {
        defaultLogger.warn(`无法关联：URL任务 ${urlMonitorTaskId} 不存在。`)
        return null
      }

      // 检查是否已存在关联
      const existingStmt = this.db.prepare(
        'SELECT * FROM task_associations WHERE businessTaskId = ? AND urlMonitorTaskId = ?'
      )
      const existingAssociation = existingStmt.get(businessTaskId, urlMonitorTaskId)
      if (existingAssociation) {
        defaultLogger.info(
          `URL任务 ${urlMonitorTaskId} 与业务任务 ${businessTaskId} 的关联已存在。`
        )
        return existingAssociation
      }

      // 创建新关联
      const associationId = uuidv4()
      const stmt = this.db.prepare(`
        INSERT INTO task_associations (associationId, businessTaskId, urlMonitorTaskId)
        VALUES (?, ?, ?)
      `)
      const result = stmt.run(associationId, businessTaskId, urlMonitorTaskId)

      defaultLogger.info(
        `已关联URL任务 ${urlMonitorTaskId} 与业务任务 ${businessTaskId}。 ID: ${associationId}`
      )
      defaultLogger.info(
        `[UrlMonitorStore] Association result for urlMonitorTaskId ${urlMonitorTaskId} and businessTaskId ${businessTaskId}: ${JSON.stringify(result)}`
      ) // Added log
      return { associationId, businessTaskId, urlMonitorTaskId }
    } catch (error) {
      if (error.message.includes('FOREIGN KEY constraint failed')) {
        defaultLogger.warn(
          `关联URL任务 ${urlMonitorTaskId} 与业务任务 ${businessTaskId} 失败: 可能业务任务ID不存在。`
        )
      } else {
        defaultLogger.error(
          `关联URL任务 ${urlMonitorTaskId} 与业务任务 ${businessTaskId} 失败: ${error.message}`
        )
      }
      return null
    }
  }

  async dissociateUrlFromTask(urlMonitorTaskId, businessTaskId) {
    if (!this.db) await this.init()
    if (!urlMonitorTaskId || !businessTaskId) return false
    try {
      const stmt = this.db.prepare(
        'DELETE FROM task_associations WHERE urlMonitorTaskId = ? AND businessTaskId = ?'
      )
      const result = stmt.run(urlMonitorTaskId, businessTaskId)
      if (result.changes > 0) {
        defaultLogger.info(
          `已解除URL任务 ${urlMonitorTaskId} 与业务任务 ${businessTaskId} 的关联。`
        )
      } else {
        defaultLogger.info(
          `未找到URL任务 ${urlMonitorTaskId} 与业务任务 ${businessTaskId} 的关联进行解除。`
        )
      }
      return true
    } catch (error) {
      defaultLogger.error(
        `解除URL任务 ${urlMonitorTaskId} 与业务任务 ${businessTaskId} 关联失败: ${error.message}`
      )
      return false
    }
  }

  async getUrlsForTask(businessTaskId) {
    if (!this.db) await this.init()
    if (!businessTaskId) return []
    try {
      const stmt = this.db.prepare(
        'SELECT urlMonitorTaskId FROM task_associations WHERE businessTaskId = ?'
      )
      const rows = stmt.all(businessTaskId)
      return rows.map((row) => row.urlMonitorTaskId)
    } catch (error) {
      defaultLogger.error(`获取业务任务 ${businessTaskId} 关联的URL任务ID失败: ${error.message}`)
      return []
    }
  }

  async findUrlMonitorsByBusinessTaskId(businessTaskId) {
    if (!this.db) await this.init()
    if (!businessTaskId) {
      return []
    }
    try {
      // Step 1: Check task_associations directly
      const associationSql = `SELECT *
                              FROM task_associations
                              WHERE businessTaskId = ?`
      const associations = this.db.prepare(associationSql).all(businessTaskId)

      if (associations.length === 0) {
        // Original query for comparison, though it will be empty if associations is empty
        const originalSql = `
          SELECT ut.*
          FROM url_tasks ut
                 JOIN task_associations ta ON ut.urlId = ta.urlMonitorTaskId
          WHERE ta.businessTaskId = ?
        `
        const originalTasks = this.db.prepare(originalSql).all(businessTaskId)
        return originalTasks // Return the (likely empty) result of the original query
      }

      const urlMonitorTaskIds = associations.map((a) => a.urlMonitorTaskId)

      if (urlMonitorTaskIds.length === 0) {
        return []
      }

      // Step 2: Fetch url_tasks based on the found urlMonitorTaskIds
      const placeholders = urlMonitorTaskIds.map(() => '?').join(',')
      const tasksSql = `SELECT *
                        FROM url_tasks
                        WHERE urlId IN (${placeholders})`
      const tasksStepByStep = this.db.prepare(tasksSql).all(...urlMonitorTaskIds)
      const originalJoinSql = `
        SELECT ut.*
        FROM url_tasks ut
               JOIN task_associations ta ON ut.urlId = ta.urlMonitorTaskId
        WHERE ta.businessTaskId = ?
      `
      const originalJoinTasks = this.db.prepare(originalJoinSql).all(businessTaskId)

      // We should return the result of the original query's logic, which is the JOIN.
      // The step-by-step was for debugging. If originalJoinTasks is what the app expects, return that.
      // Or, if the step-by-step is believed to be more correct IF the join is faulty, return tasksStepByStep.
      // For now, let's assume the original JOIN logic is what's intended to be authoritative if data exists.
      return originalJoinTasks
    } catch (error) {
      throw error
    }
  }

  /**
   * 更新URL任务的本地文件路径
   * @param {string} urlId - URL监控ID
   * @param {string} localFilePath - 本地文件路径
   * @returns {Promise<boolean>} 是否成功更新
   */
  async updateLocalFilePath(urlId, localFilePath) {
    if (!this.db) await this.init()
    if (!urlId || typeof localFilePath !== 'string') {
      defaultLogger.warn(`更新本地文件路径失败：无效的参数 urlId=${urlId}, localFilePath=${localFilePath}`)
      return false
    }

    try {
      const stmt = this.db.prepare('UPDATE url_tasks SET localFilePath = ? WHERE urlId = ?')
      const result = stmt.run(localFilePath, urlId)
      
      if (result.changes > 0) {
        defaultLogger.info(`URL任务 ${urlId} 的本地文件路径已更新为: ${localFilePath}`)
        return true
      } else {
        defaultLogger.warn(`未能更新URL任务 ${urlId} 的本地文件路径，可能任务不存在。`)
        return false
      }
    } catch (error) {
      defaultLogger.error(`更新URL任务 ${urlId} 的本地文件路径失败: ${error.message}`)
      return false
    }
  }

  /**
   * 获取URL任务的本地文件路径
   * @param {string} urlId - URL监控ID
   * @returns {Promise<string|null>} 本地文件路径或null
   */
  async getLocalFilePath(urlId) {
    if (!this.db) await this.init()
    if (!urlId) return null
    
    try {
      const stmt = this.db.prepare('SELECT localFilePath FROM url_tasks WHERE urlId = ?')
      const result = stmt.get(urlId)
      return result ? result.localFilePath : null
    } catch (error) {
      defaultLogger.error(`获取URL任务 ${urlId} 的本地文件路径失败: ${error.message}`)
      return null
    }
  }

  /**
   * 关闭数据库连接
   */
  async destroy() {
    if (this.db) {
      try {
        this.db.close()
        defaultLogger.info('SQLite 数据库已成功关闭。')
        this.db = null
      } catch (error) {
        defaultLogger.error(`关闭 SQLite 数据库失败: ${error.message}`)
      }
    }
  }
}

// --- 导出和使用 ---
// 注意: better-sqlite3 是同步的，所以 getUrlMonitorStore 的行为会有些不同。
// 如果你想保持异步接口，可以在每个方法内部处理 init() 的调用。
// 或者，在应用启动时就初始化一次。

const urlMonitorStoreInstance = new UrlMonitorStore()

// 异步获取实例并确保初始化
async function getUrlMonitorStore() {
  if (!urlMonitorStoreInstance.db) {
    await urlMonitorStoreInstance.init() // init 现在是 async
  }
  return urlMonitorStoreInstance
}

module.exports = {
  getUrlMonitorStore // 提供异步获取并初始化的函数
  // 如果需要直接访问实例（可能在初始化后），可以导出实例本身
  // 但推荐通过 getUrlMonitorStore 获取，以确保已初始化
  // urlMonitorStoreInstance
}
