<template>
  <div class="file-watcher">
    <!-- 设置弹窗 -->
    <div class="settings-modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>
            {{ versionConfig.shortName }}设置
            <span class="version-tag">{{ versionConfig.appVersion }}</span>
            <span class="user-info"  v-if="userInfo">欢迎您，{{ userInfo.nickname }}</span>
            <span class="user-info inner-tag" v-if="userInfo?.orgs?.[0]?.orgId === 2">内部版本号：1.1.28</span>
          </h3>
          <div class="header-actions">
            <button class="logout-btn" @click="handleLogout" title="退出登录">
              <span class="icon-logout"></span>
            </button>
            <button class="close-btn" @click="() => $emit('close')">&times;</button>
          </div>
        </div>



        <div class="modal-body">
          <div class="status-section">
            <div class="status-item">
              <span class="label">状态：</span>
              <span :class="['status-badge', status.status]">{{
                status.status === 'running' ? '运行中' : '已停止'
                }}</span>
            </div>
            <div class="status-item"  v-if="userInfo?.orgs?.[0]?.orgId === 2">
              <span class="label">本次上传：</span>
              <span>{{ status.processedFiles || 0 }} 个文件</span>
            </div>
            <!-- 保存方式设置区域 - 移到顶部，只在企业ID为2时显示 -->
            <div class="directory-section">
              <h4>保存方式设置</h4>
              <div class="directory-form">
                <div class="radio-group">
                  <label class="radio-item">
                    <input type="radio" v-model="currentSaveMethod" value="method1" @change="updateSaveMethod" />
                    <span class="radio-label">方式一</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="currentSaveMethod" value="method2" @change="updateSaveMethod" />
                    <span class="radio-label">方式二</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="currentSaveMethod" value="method3" @change="updateSaveMethod" />
                    <span class="radio-label">方式三 (默认)</span>
                  </label>
                  <label class="radio-item" v-if="userInfo?.orgs?.[0]?.orgId === 2">
                    <input type="radio" v-model="currentSaveMethod" value="method4" @change="updateSaveMethod" />
                    <span class="radio-label">方式四</span>
                  </label>
                </div>
                <div v-if="saveMethodUpdateMessage" class="update-message"
                  :class="saveMethodUpdateSuccess ? 'success' : 'error'">
                  {{ saveMethodUpdateMessage }}
                </div>
              </div>
            </div>
            <!-- <div class="status-item">
              <span class="label">运行时间：</span>
              <span>{{ formatDuration }}</span>
            </div> -->
          </div>
          <!-- 添加Tooltip配置区域 -->
          <div v-if="false" class="directory-section">
            <h4>题目内容预览</h4>
            <div class="directory-form">
              <div class="form-group">
                <label class="tooltip-label">
                  <input type="checkbox" v-model="showTooltip" @change="updateTooltipSetting" />
                </label>
                <div class="tooltip-desc">启用后，鼠标悬停在题目内容上会显示完整内容</div>
              </div>
              <div v-if="tooltipUpdateMessage" class="update-message"
                :class="tooltipUpdateSuccess ? 'success' : 'error'">
                {{ tooltipUpdateMessage }}
              </div>
            </div>
          </div>

          <!-- 添加目录设置区域 -->
          <div class="directory-section"  v-if="userInfo?.orgs?.[0]?.orgId === 2">
            <h4>上传目录设置</h4>
            <div class="directory-form">
              <div class="form-group">
                <span class="label">路径：</span>
                <input type="text" class="directory-input" v-model="newWatchDir"
                  :placeholder="status.watchDir || 'C:\\Temp'" />
                <button class="action-btn" @click="updateWatchDir" :disabled="isUpdating || !newWatchDir">
                  {{ isUpdating ? '更新中...' : '更新目录' }}
                </button>
              </div>
              <div v-if="updateMessage" class="update-message" :class="updateSuccess ? 'success' : 'error'">
                {{ updateMessage }}
              </div>
            </div>
          </div>

          <!-- 添加文件下载目录设置区域 -->
          <div class="directory-section"  v-if="userInfo?.orgs?.[0]?.orgId === 2">
            <h4>下载目录设置</h4>
            <div class="directory-form">
              <div class="form-group">
                <span class="label">路径：</span>
                <input type="text" class="directory-input" v-model="newDownloadPath"
                  :placeholder="downloadPath || 'C:\\Temp\\Downloads'" />
                <button class="action-btn" @click="updateDownloadPath"
                  :disabled="isUpdatingDownloadPath || !newDownloadPath">
                  {{ isUpdatingDownloadPath ? '更新中...' : '更新路径' }}
                </button>
              </div>
              <div v-if="downloadPathUpdateMessage" class="update-message"
                :class="downloadPathUpdateSuccess ? 'success' : 'error'">
                {{ downloadPathUpdateMessage }}
              </div>
            </div>
          </div>

          <!-- 添加配置目录设置区域 -->
          <div class="directory-section"  v-if="userInfo?.orgs?.[0]?.orgId === 2">
            <h4>配置目录设置</h4>
            <div class="directory-form">
              <div class="form-group">
                <span class="label">路径：</span>
                <input type="text" class="directory-input" v-model="newAddonConfigPath"
                  :placeholder="addonConfigPath || 'C:\\ww-wps-addon\\cfg'" />
                <button class="action-btn" @click="updateAddonConfigPath"
                  :disabled="isUpdatingAddonConfigPath || !newAddonConfigPath">
                  {{ isUpdatingAddonConfigPath ? '更新中...' : '更新路径' }}
                </button>
              </div>
              <div v-if="addonConfigPathUpdateMessage" class="update-message"
                :class="addonConfigPathUpdateSuccess ? 'success' : 'error'">
                {{ addonConfigPathUpdateMessage }}
              </div>
            </div>
          </div>
          <div class="events-section"  v-if="userInfo?.orgs?.[0]?.orgId === 2">
            <h4>最近事件</h4>
            <div class="events-list">
              <div v-for="(event, index) in recentEvents" :key="index" class="event-item">
                <span class="event-time">{{ formatTime(event.timestamp) }}</span>
                <span :class="['event-type', event.eventType]">{{
                  getEventTypeText(event.eventType)
                  }}</span>
                <span class="event-message">{{ getEventMessage(event) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 版本信息调试区域 -->
        <div v-if="0 && userInfo?.orgs?.[0]?.orgId === 2" class="directory-section">
          <h4>版本信息 (调试)</h4>
          <div class="version-info">
            <div class="form-group">
              <span class="label">当前版本：</span>
              <span class="version-display">{{ versionConfig.edition }} ({{ versionConfig.appVersion }})</span>
            </div>
            <div class="form-group">
              <span class="label">切换版本：</span>
              <select v-model="selectedEdition" class="edition-select">
                <option value="wanwei">万唯版本</option>
                <option value="hexin">合心版本</option>
              </select>
              <button class="action-btn" @click="switchEdition"
                :disabled="isSwitchingEdition || selectedEdition === versionConfig.edition">
                {{ isSwitchingEdition ? '切换中...' : '切换版本' }}
              </button>
            </div>
            <div v-if="editionSwitchMessage" class="update-message" :class="editionSwitchSuccess ? 'success' : 'error'">
              {{ editionSwitchMessage }}
            </div>
          </div>
        </div>



        <div class="modal-footer"  v-if="userInfo?.orgs?.[0]?.orgId === 2">
          <button class="control-btn" :class="status.status === 'running' ? 'stop' : 'start'" @click="controlService">
            {{ status.status === 'running' ? '停止服务' : '启动服务' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { logout } from './js/auth'
import wsClient from './js/wsClient'
import versionManager from './js/versionManager'

export default {
  name: 'FileWatcher',
  data() {
    return {
      showSettings: false,
      status: {
        status: 'stopped',
        startTime: null,
        watchDir: 'C:\\Temp',
        processedFiles: 0,
        lastError: null
      },
      recentEvents: [],
      // 新增目录设置相关状态
      newWatchDir: '',
      isUpdating: false,
      updateMessage: '',
      updateSuccess: false,
      wasRunningBeforeUpdate: false,
      // 下载路径设置相关状态
      downloadPath: 'C:\\Temp\\Downloads',
      newDownloadPath: '',
      isUpdatingDownloadPath: false,
      downloadPathUpdateMessage: '',
      downloadPathUpdateSuccess: false,
      // 配置路径设置相关状态
      addonConfigPath: 'C:\\ww-wps-addon\\cfg',
      newAddonConfigPath: '',
      isUpdatingAddonConfigPath: false,
      addonConfigPathUpdateMessage: '',
      addonConfigPathUpdateSuccess: false,
      // 版本配置
      versionConfig: versionManager.getVersionConfig(),
      // 版本切换相关
      selectedEdition: versionManager.getEdition(),
      isSwitchingEdition: false,
      editionSwitchMessage: '',
      editionSwitchSuccess: false,
      showTooltip: false,
      tooltipUpdateMessage: '',
      tooltipUpdateSuccess: false,

      currentSaveMethod: 'method3', // 默认方式三
      saveMethodUpdateMessage: '',
      saveMethodUpdateSuccess: false
    }
  },
  computed: {
    formatDuration() {
      if (!this.status.startTime) return '未启动'
      const start = new Date(this.status.startTime)
      const now = new Date()
      const diff = now - start
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)
      return `${hours}小时 ${minutes}分 ${seconds}秒`
    },
    userInfo() {
      const userInfoStr = window.Application?.PluginStorage?.getItem('user_info')
      return userInfoStr ? JSON.parse(userInfoStr) : null
    }
  },
  methods: {
    async fetchStatus() {
      try {
        const response = await wsClient.getWatcherStatus()
        if (response.success) {
          this.status = response.data
          // 如果状态响应中包含 addonConfigPath，则更新本地状态
          if (response.data.addonConfigPath) {
            this.addonConfigPath = response.data.addonConfigPath
          }
        }
      } catch (error) {
        console.error('获取状态失败:', error)
      }
    },

    async controlService() {
      try {
        const action = this.status.status === 'running' ? 'stopWatcher' : 'startWatcher'
        await wsClient[action]()
        await this.fetchStatus()
      } catch (error) {
        console.error('控制服务失败:', error)
      }
    },

    // 更新上传目录
    async updateWatchDir() {
      if (!this.newWatchDir || this.isUpdating) return

      this.isUpdating = true
      this.updateMessage = ''

      try {
        // 检查服务是否在运行，如果是，则先停止
        this.wasRunningBeforeUpdate = this.status.status === 'running'

        if (this.wasRunningBeforeUpdate) {
          this.updateMessage = '正在停止服务以更新目录...'
          await wsClient.stopWatcher()

          // 等待服务停止
          await new Promise((resolve) => setTimeout(resolve, 1000))
          await this.fetchStatus()

          // 二次检查确保服务已停止
          if (this.status.status === 'running') {
            throw new Error('无法停止服务，目录更新失败')
          }
        }

        // 更新目录
        const result = await wsClient.setWatchDirectory(this.newWatchDir)

        if (result.success) {
          this.updateSuccess = true
          this.updateMessage = `上传目录已更新为: ${this.newWatchDir}`

          // 如果之前在运行，则重新启动服务
          if (this.wasRunningBeforeUpdate) {
            this.updateMessage += '，正在重新启动服务...'
            await wsClient.startWatcher()
          }

          // 刷新状态
          await this.fetchStatus()
          this.newWatchDir = '' // 清空输入框
        } else {
          this.updateSuccess = false
          this.updateMessage = `目录更新失败: ${result.message || '未知错误'}`
        }
      } catch (error) {
        this.updateSuccess = false
        this.updateMessage = `发生错误: ${error.message}`
        console.error('更新上传目录失败:', error)
      } finally {
        this.isUpdating = false
      }
    },

    // 获取下载路径
    async fetchDownloadPath() {
      try {
        const response = await wsClient.getDownloadPath()
        if (response.success && response.downloadPath) {
          this.downloadPath = response.downloadPath
        }
      } catch (error) {
        console.error('获取下载路径失败:', error)
      }
    },

    // 更新下载路径
    async updateDownloadPath() {
      if (!this.newDownloadPath || this.isUpdatingDownloadPath) return

      this.isUpdatingDownloadPath = true
      this.downloadPathUpdateMessage = ''

      try {
        const result = await wsClient.setDownloadPath(this.newDownloadPath)

        if (result.success) {
          this.downloadPathUpdateSuccess = true
          this.downloadPathUpdateMessage = `下载路径已更新为: ${this.newDownloadPath}`
          this.downloadPath = this.newDownloadPath
          this.newDownloadPath = '' // 清空输入框
        } else {
          this.downloadPathUpdateSuccess = false
          this.downloadPathUpdateMessage = `下载路径更新失败: ${result.message || '未知错误'}`
        }
      } catch (error) {
        this.downloadPathUpdateSuccess = false
        this.downloadPathUpdateMessage = `发生错误: ${error.message}`
        console.error('更新下载路径失败:', error)
      } finally {
        this.isUpdatingDownloadPath = false
      }
    },

    // 获取配置路径
    async fetchAddonConfigPath() {
      try {
        const response = await wsClient.getAddonConfigPath()
        if (response.success && response.addonConfigPath) {
          this.addonConfigPath = response.addonConfigPath
        }
      } catch (error) {
        console.error('获取配置路径失败:', error)
      }
    },

    // 更新配置路径
    async updateAddonConfigPath() {
      if (!this.newAddonConfigPath || this.isUpdatingAddonConfigPath) return

      this.isUpdatingAddonConfigPath = true
      this.addonConfigPathUpdateMessage = ''

      try {
        const result = await wsClient.setAddonConfigPath(this.newAddonConfigPath)

        if (result.success) {
          this.addonConfigPathUpdateSuccess = true
          this.addonConfigPathUpdateMessage = `配置路径已更新为: ${this.newAddonConfigPath}`
          this.addonConfigPath = this.newAddonConfigPath
          this.newAddonConfigPath = '' // 清空输入框
        } else {
          this.addonConfigPathUpdateSuccess = false
          this.addonConfigPathUpdateMessage = `配置路径更新失败: ${result.message || '未知错误'}`
        }
      } catch (error) {
        this.addonConfigPathUpdateSuccess = false
        this.addonConfigPathUpdateMessage = `发生错误: ${error.message}`
        console.error('更新配置路径失败:', error)
      } finally {
        this.isUpdatingAddonConfigPath = false
      }
    },

    handleWatcherEvent(event) {
      // 更新状态
      if (event.eventType === 'start') {
        this.status.status = 'running'
        this.status.startTime = event.data.startTime
      } else if (event.eventType === 'stop') {
        this.status.status = 'stopped'
        this.status.processedFiles = event.data.processedFiles
      } else if (event.eventType === 'uploadSuccess') {
        this.status.processedFiles = event.data.totalProcessed
      } else if (event.eventType === 'urlDownloadPathChanged') {
        // 处理下载路径变更事件
        this.downloadPath = event.data.path
        this.downloadPathUpdateSuccess = true
        this.downloadPathUpdateMessage = `下载路径已变更为: ${event.data.path}`
      } else if (event.eventType === 'addonConfigPathChanged') {
        // 处理配置路径变更事件
        this.addonConfigPath = event.data.path
        this.addonConfigPathUpdateSuccess = true
        this.addonConfigPathUpdateMessage = `配置路径已变更为: ${event.data.path}`
      } else if (event.eventType === 'urlFileDownloaded') {
        // 文件下载成功事件
        this.recentEvents.unshift({
          ...event,
          timestamp: new Date().toISOString()
        })
      }

      // 添加到最近事件
      this.recentEvents.unshift(event)
      if (this.recentEvents.length > 50) {
        this.recentEvents.pop()
      }
    },

    handleUrlMonitorEvent(event) {
      // 处理URL监控相关事件
      console.log('URL监控事件:', event)
    },

    // 版本切换方法
    async switchEdition() {
      if (this.isSwitchingEdition || this.selectedEdition === this.versionConfig.edition) return

      this.isSwitchingEdition = true
      this.editionSwitchMessage = ''
      this.editionSwitchSuccess = false

      try {
        await versionManager.setEdition(this.selectedEdition)
        this.editionSwitchSuccess = true
        this.editionSwitchMessage = `版本已成功切换到: ${this.selectedEdition === 'wanwei' ? '万唯版本' : '合心版本'}`
      } catch (error) {
        this.editionSwitchSuccess = false
        this.editionSwitchMessage = `版本切换失败: ${error.message}`
        console.error('版本切换失败:', error)
      } finally {
        this.isSwitchingEdition = false
      }
    },

    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleTimeString()
    },

    async handleLogout() {
      try {
        const success = await logout()
        if (success) {
          // Redirect to login page
          window.location.hash = '#/login'
        } else {
          alert('退出登录失败，请稍后重试')
        }
      } catch (error) {
        console.error('Logout error:', error)
        alert('退出登录失败，请稍后重试')
      }
    },

    getEventTypeText(type) {
      const typeMap = {
        start: '启动',
        stop: '停止',
        filesFound: '发现文件',
        uploadStart: '开始上传',
        uploadSuccess: '上传成功',
        uploadError: '上传失败',
        deleteError: '删除失败',
        error: '错误',
        urlMonitorUpdate: 'URL状态',
        urlFileDownloaded: '文件下载',
        urlFileDownloadError: '下载失败',
        urlDownloadPathChanged: '下载路径更新',
        addonConfigPathChanged: '配置路径更新'
      }
      return typeMap[type] || type
    },

    getEventMessage(event) {
      switch (event.eventType) {
        case 'start':
          return `服务已启动，上传目录: ${event.data.watchDir}`
        case 'stop':
          return `服务已停止，处理了 ${event.data.processedFiles} 个文件`
        case 'filesFound':
          return `发现 ${event.data.count} 个新文件`
        case 'uploadStart':
          return `正在上传: ${event.data.file}`
        case 'uploadSuccess':
          return `文件 ${event.data.file} 上传成功`
        case 'uploadError':
          return `文件 ${event.data.file} 上传失败`
        case 'deleteError':
          return `无法删除文件: ${event.data.file}`
        case 'dirCreated':
          return `创建目录: ${event.data.dir}`
        case 'urlMonitorUpdate':
          const status =
            event.data.status === 'accessible'
              ? '可访问'
              : event.data.status === 'inaccessible'
                ? '不可访问'
                : event.data.status === 'error'
                  ? '检查出错'
                  : '未知状态'
          return `URL "${event.data.url.substring(0, 40)}..." ${status}`
        case 'urlFileDownloaded':
          return `文件已下载: ${event.data.filePath}`
        case 'urlFileDownloadError':
          return `下载失败: ${event.data.error}`
        case 'urlDownloadPathChanged':
          return `下载路径已更新: ${event.data.path}`
        case 'addonConfigPathChanged':
          return `配置路径已更新: ${event.data.path}`
        case 'error':
          return `${event.data.error || '错误'}: ${event.data.message}`
        default:
          // 避免显示原始JSON
          if (event.data) {
            const key = Object.keys(event.data)[0]
            return key ? `${key}: ${event.data[key]}` : '事件通知'
          }
          return '系统事件'
      }
    },

    async fetchTooltipSetting() {
      try {
        const response = await wsClient.sendRequest('config', 'getShowTooltip')
        if (response.success && response.showTooltip !== undefined) {
          this.showTooltip = response.showTooltip
        }
      } catch (error) {
        console.error('获取Tooltip设置失败:', error)
      }
    },

    // 更新tooltip设置
    async updateTooltipSetting() {
      this.tooltipUpdateMessage = ''
      this.tooltipUpdateSuccess = false

      try {
        console.log('发送tooltip设置:', this.showTooltip)
        // 确保showTooltip是一个明确的布尔值
        const tooltipValue = Boolean(this.showTooltip)

        const result = await wsClient.sendRequest('config', 'setShowTooltip', {
          showTooltip: tooltipValue
        })

        if (result.success) {
          this.tooltipUpdateSuccess = true
          this.tooltipUpdateMessage = 'Tooltip设置已更新'
          console.log('Tooltip设置更新成功')
        } else {
          this.tooltipUpdateSuccess = false
          this.tooltipUpdateMessage = `Tooltip设置更新失败: ${result.message || '未知错误'}`
          console.error('Tooltip设置更新失败:', result.message)
        }
      } catch (error) {
        this.tooltipUpdateSuccess = false
        this.tooltipUpdateMessage = `发生错误: ${error.message}`
        console.error('更新Tooltip设置失败:', error)
      }
    },

    // 获取保存方式设置
    async fetchSaveMethod() {
      try {
        const response = await wsClient.sendRequest('config', 'getSaveMethod')
        if (response.success && response.saveMethod) {
          this.currentSaveMethod = response.saveMethod

          // 如果当前保存方式不是method3，自动设置为method3
          if (response.saveMethod !== 'method3') {
            console.log(`当前保存方式为${response.saveMethod}，自动切换到方式三`)
            await this.setSaveMethodToThree()
          }
        } else {
          // 如果没有设置，默认设置为method3
          console.log('未检测到保存方式设置，自动设置为方式三')
          await this.setSaveMethodToThree()
        }
      } catch (error) {
        console.error('获取保存方式设置失败:', error)
        // 发生错误时也尝试设置为method3
        await this.setSaveMethodToThree()
      }
    },

    // 自动设置保存方式为方式三
    async setSaveMethodToThree() {
      try {
        const result = await wsClient.sendRequest('config', 'setSaveMethod', {
          saveMethod: 'method3'
        })

        if (result.success) {
          this.currentSaveMethod = 'method3'
          console.log('已自动设置保存方式为方式三')
        } else {
          console.error('自动设置保存方式为方式三失败:', result.message)
        }
      } catch (error) {
        console.error('自动设置保存方式失败:', error)
      }
    },

    // 更新保存方式设置
    async updateSaveMethod() {
      this.saveMethodUpdateMessage = ''
      this.saveMethodUpdateSuccess = false

      try {
        const result = await wsClient.sendRequest('config', 'setSaveMethod', {
          saveMethod: this.currentSaveMethod
        })

        if (result.success) {
          this.saveMethodUpdateSuccess = true
          this.saveMethodUpdateMessage = '保存方式设置已更新'
          console.log('保存方式设置更新成功:', this.currentSaveMethod)
        } else {
          this.saveMethodUpdateSuccess = false
          this.saveMethodUpdateMessage = `保存方式设置更新失败: ${result.message || '未知错误'}`
          console.error('保存方式设置更新失败:', result.message)
        }
      } catch (error) {
        this.saveMethodUpdateSuccess = false
        this.saveMethodUpdateMessage = `发生错误: ${error.message}`
        console.error('更新保存方式设置失败:', error)
      }
    },

    handleConfigEvent(event) {
      // 处理配置相关事件
      console.log('配置事件:', event)

      // 处理tooltip设置变更事件
      if (event.eventType === 'tooltipSettingChanged') {
        this.showTooltip = event.data.showTooltip
        this.tooltipUpdateSuccess = true
        this.tooltipUpdateMessage = `Tooltip设置已变更为: ${event.data.showTooltip ? '显示' : '隐藏'}`
      }

      // 处理保存方式设置变更事件
      if (event.eventType === 'saveMethodChanged') {
        this.currentSaveMethod = event.data.saveMethod
        this.saveMethodUpdateSuccess = true
        this.saveMethodUpdateMessage = `保存方式已变更为: ${event.data.saveMethod === 'method1'
            ? '方式一'
            : event.data.saveMethod === 'method2'
              ? '方式二'
              : event.data.saveMethod === 'method3'
                ? '方式三'
                : '方式四'
          }`
      }
    },

    // 注册事件监听器
    setupEventListeners() {
      // 监听文件观察器事件
      this.removeWatcherListener = wsClient.addEventListener('watcher', this.handleWatcherEvent)

      // 监听URL监控事件
      this.removeUrlMonitorListener = wsClient.addEventListener(
        'urlMonitor',
        this.handleUrlMonitorEvent
      )

      // 监听配置事件
      this.removeConfigListener = wsClient.addEventListener('config', this.handleConfigEvent)
    }
  },
  async mounted() {
    // 设置版本信息监听
    this.removeVersionListener = versionManager.onVersionChange((versionInfo) => {
      this.versionConfig = versionManager.getVersionConfig()
      this.selectedEdition = versionManager.getEdition()
    })

    // 初始化连接并获取初始数据
    await wsClient.connect()

    // 设置事件监听器
    this.setupEventListeners()

    // 获取初始数据
    await this.fetchStatus()
    await this.fetchDownloadPath()
    await this.fetchAddonConfigPath()
    await this.fetchTooltipSetting()
    await this.fetchSaveMethod()
  },
  beforeDestroy() {
    // 移除事件监听器
    if (this.removeWatcherListener) this.removeWatcherListener()
    if (this.removeUrlMonitorListener) this.removeUrlMonitorListener()
    if (this.removeConfigListener) this.removeConfigListener()
    if (this.removeVersionListener) this.removeVersionListener()
  }
}
</script>

<style scoped>
.file-watcher {
  position: relative;
  padding: 16px;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.settings-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #666;
  transition: color 0.3s;
}

.settings-btn:hover {
  color: #333;
}

.icon-settings {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M12 15.5A3.5 3.5 0 0 1 8.5 12 3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.65.07-.97 0-.32-.03-.65-.07-.97l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65c-.04-.24-.25-.42-.5-.42h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.63c-.04.32-.07.65-.07.97 0 .32.03.65.07.97l-2.11 1.63c-.19.15-.24.42-.12.64l2 3.46c.12.22.39.31.61.22l2.49-1c.52.39 1.06.73 1.69.98l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.25 1.17-.59 1.69-.98l2.49 1c.22.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.63z"/></svg>') no-repeat center;
}

.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 600px;
  max-width: 90%;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.logout-btn {
  background: none;
  border: none;
  margin-right: 15px;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-logout {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z" /></svg>') no-repeat center;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 16px;
}

.status-section,
.events-section,
.directory-section {
  margin-bottom: 24px;
}

.status-item {
  margin: 8px 0;
  display: flex;
  align-items: center;
}

.label {
  width: 80px;
  text-align: right;
  color: #666;
}

.status-badge {
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.status-badge.running {
  background-color: #4caf50;
}

.status-badge.stopped {
  background-color: #f44336;
}

.events-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
}

.event-item {
  padding: 6px 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 14px;
}

.event-item:last-child {
  border-bottom: none;
}

.event-time {
  color: #999;
  margin-right: 8px;
}

.event-type {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 8px;
  color: white;
  font-size: 12px;
}

.event-type.start,
.event-type.uploadSuccess {
  background-color: #4caf50;
}

.event-type.stop,
.event-type.error,
.event-type.uploadError,
.event-type.deleteError {
  background-color: #f44336;
}

.event-type.filesFound,
.event-type.uploadStart {
  background-color: #2196f3;
}

.modal-footer {
  padding: 16px;
  border-top: 1px solid #eee;
  text-align: center;
}

.control-btn {
  padding: 8px 24px;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.control-btn.start {
  background-color: #4caf50;
}

.control-btn.start:hover {
  background-color: #3d8b40;
}

.control-btn.stop {
  background-color: #f44336;
}

.control-btn.stop:hover {
  background-color: #d32f2f;
}

/* 新增样式 */
.directory-form {
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #f9f9f9;
}

.form-group {
  display: flex;
  align-items: center;
}

.directory-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin: 0 10px;
}

.form-actions {
  text-align: right;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #2196f3;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
}

.action-btn:hover {
  background-color: #0b7dda;
}

.action-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.update-message {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
}

.update-message.success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.update-message.error {
  background-color: #ffebee;
  color: #c62828;
}

.user-info {
  font-size: 14px;
  color: #666;
  margin-left: 15px;
  border-left: 1px solid #ddd;
  padding-left: 15px;
  cursor: pointer;
  transition: color 0.2s;
}

.user-info:hover {
  color: #1f1e1e;
}

.version-tag {
  background-color: #2196f3;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 12px;
}

.inner-tag {
  background-color: #ababab;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 12px;
}

.tooltip-label {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.tooltip-label input {
  margin-right: 8px;
}

.tooltip-desc {
  font-size: 12px;
  color: #666;
}

/* 版本信息样式 */
.version-info {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.version-display {
  font-weight: bold;
  color: #495057;
  background-color: #e9ecef;
  padding: 4px 8px;
  border-radius: 3px;
  font-family: monospace;
}

.edition-select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 8px;
  font-size: 14px;
  background-color: white;
}

.edition-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 保存方式设置区域样式 */
.save-method-section {
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
  padding-bottom: 20px;
}

.save-method-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.save-method-form {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  background-color: white;
  min-width: 80px;
}

.radio-item:hover {
  background-color: #f1f3f4;
  border-color: #007bff;
}

.radio-item input[type='radio'] {
  margin-right: 8px;
  accent-color: #007bff;
}

.radio-item input[type='radio']:checked+.radio-label {
  font-weight: 600;
  color: #007bff;
}

.radio-label {
  font-size: 14px;
  color: #495057;
  user-select: none;
}

.update-message {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
}

.update-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.update-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
</style>
